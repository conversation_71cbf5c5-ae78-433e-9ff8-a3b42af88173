export const featureItems = [
  {
    id: 1,
    className: "col-md-4 col-lg-3 d-flex align-items-stretch mb-sm-30",
    path: "M21.62 20.196c1.055-.922 1.737-2.262 1.737-3.772 0-1.321-.521-2.515-1.357-3.412v-6.946l-11.001-6.066-11 6v12.131l11 5.869 5.468-2.917c.578.231 1.205.367 1.865.367.903 0 1.739-.258 2.471-.676l2.394 3.226.803-.596-2.38-3.208zm-11.121 2.404l-9.5-5.069v-10.447l9.5 4.946v10.57zm1-.001v-10.567l5.067-2.608.029.015.021-.04 4.384-2.256v5.039c-.774-.488-1.686-.782-2.668-.782-2.773 0-5.024 2.252-5.024 5.024 0 1.686.838 3.171 2.113 4.083l-3.922 2.092zm6.833-2.149c-2.219 0-4.024-1.808-4.024-4.026s1.805-4.025 4.024-4.025c2.22 0 4.025 1.807 4.025 4.025 0 2.218-1.805 4.026-4.025 4.026zm-.364-3.333l-1.306-1.147-.66.751 2.029 1.782 2.966-3.12-.725-.689-2.304 2.423zm-16.371-10.85l4.349-2.372 9.534 4.964-4.479 2.305-9.404-4.897zm9.4-5.127l9.404 5.186-3.832 1.972-9.565-4.98 3.993-2.178z",
    title: "Unique Design",
    description:
      "Fusce aliquet quam eget neque ultrices elementum felis id arcu blandit sagittis.",
  },
  {
    id: 2,
    className: "col-md-4 col-lg-3 d-flex align-items-stretch mb-sm-30",
    path: "M12 0c-3.371 2.866-5.484 3-9 3v11.535c0 4.603 3.203 5.804 9 9.465 5.797-3.661 9-4.862 9-9.465v-11.535c-3.516 0-5.629-.134-9-3zm0 1.292c2.942 2.31 5.12 2.655 8 2.701v10.542c0 3.891-2.638 4.943-8 8.284-5.375-3.35-8-4.414-8-8.284v-10.542c2.88-.046 5.058-.391 8-2.701zm5 7.739l-5.992 6.623-3.672-3.931.701-.683 3.008 3.184 5.227-5.878.728.685z",
    title: "Quality Code",
    description:
      "Lorem ipsum dolor sit amet rembe adipiscing elite Inwege maximus ligula imsum.",
  },
  {
    id: 3,
    className: "col-md-4 col-lg-3 d-flex align-items-stretch",
    path: "M6.514 24.015h-3v-3.39c-2.08-.638-3.5-2.652-3.5-5.04 0-1.19.202-1.693 1.774-5.603.521-1.294 1.195-2.97 2.068-5.179.204-.518.67-.806 1.17-.802.482.004.941.284 1.146.802.718 1.817 1.302 3.274 1.777 4.454.26-.596.567-1.288.928-2.103.694-1.565 1.591-3.592 2.754-6.265.258-.592.881-.906 1.397-.888.572.015 1.126.329 1.369.888 1.163 2.673 2.06 4.7 2.754 6.265 2.094 4.727 2.363 5.334 2.363 6.764 0 2.927-2.078 5.422-5 6.082v4.015h-3v-4.015c-.943-.213-1.797-.617-2.523-1.165-.612.845-1.466 1.48-2.477 1.79v3.39zm14.493-6c1.652 0 2.993 1.341 2.993 2.993s-1.341 2.993-2.993 2.993-2.993-1.341-2.993-2.993 1.341-2.993 2.993-2.993zm.007.993c1.104 0 2 .896 2 2s-.896 2-2 2-2-.896-2-2 .896-2 2-2zm-7.5 3.993v-3.839c4.906-.786 5-4.751 5-5.244 0-1.218-.216-1.705-2.277-6.359-2.134-4.82-2.721-6.198-2.755-6.261-.079-.145-.193-.292-.455-.297-.238 0-.37.092-.481.297-.034.063-.621 1.441-2.755 6.261-2.061 4.654-2.277 5.141-2.277 6.359 0 .493.094 4.458 5 5.244v3.839h1zm-6.123-12.448l-.08-.198c-1.589-3.957-2.04-5.116-2.067-5.171-.072-.151-.15-.226-.226-.228-.109 0-.188.13-.235.228-.028.05-.316.818-2.066 5.171-1.542 3.833-1.703 4.233-1.703 5.23 0 1.988 1.076 3.728 3.5 4.25v3.166h1v-3.166c1.266-.273 2.159-.876 2.725-1.666-1.078-1.12-1.725-2.619-1.725-4.251 0-.979.126-1.572.877-3.365z",
    title: "Clean and Minimal",
    description:
      "Maecenas volutpat, diam enime volutpa cramas luctus interdum sodales.",
  },
];

export const featureItems2 = [
  {
    id: 1,
    className: "col-lg-6",
    path: "M21.62 20.196c1.055-.922 1.737-2.262 1.737-3.772 0-1.321-.521-2.515-1.357-3.412v-6.946l-11.001-6.066-11 6v12.131l11 5.869 5.468-2.917c.578.231 1.205.367 1.865.367.903 0 1.739-.258 2.471-.676l2.394 3.226.803-.596-2.38-3.208zm-11.121 2.404l-9.5-5.069v-10.447l9.5 4.946v10.57zm1-.001v-10.567l5.067-2.608.029.015.021-.04 4.384-2.256v5.039c-.774-.488-1.686-.782-2.668-.782-2.773 0-5.024 2.252-5.024 5.024 0 1.686.838 3.171 2.113 4.083l-3.922 2.092zm6.833-2.149c-2.219 0-4.024-1.808-4.024-4.026s1.805-4.025 4.024-4.025c2.22 0 4.025 1.807 4.025 4.025 0 2.218-1.805 4.026-4.025 4.026zm-.364-3.333l-1.306-1.147-.66.751 2.029 1.782 2.966-3.12-.725-.689-2.304 2.423zm-16.371-10.85l4.349-2.372 9.534 4.964-4.479 2.305-9.404-4.897zm9.4-5.127l9.404 5.186-3.832 1.972-9.565-4.98 3.993-2.178z",
    title: "Unique Design",
    description:
      "Fusce aliquet quam eget neque ultrices elementum felis id arcu blandit sagittis.",
  },
  {
    id: 2,
    className: "col-lg-6",
    path: "M12 0c-3.371 2.866-5.484 3-9 3v11.535c0 4.603 3.203 5.804 9 9.465 5.797-3.661 9-4.862 9-9.465v-11.535c-3.516 0-5.629-.134-9-3zm0 1.292c2.942 2.31 5.12 2.655 8 2.701v10.542c0 3.891-2.638 4.943-8 8.284-5.375-3.35-8-4.414-8-8.284v-10.542c2.88-.046 5.058-.391 8-2.701zm5 7.739l-5.992 6.623-3.672-3.931.701-.683 3.008 3.184 5.227-5.878.728.685z",
    title: "Quality Code",
    description:
      "Lorem ipsum dolor sit amet rembe adipiscing elite Inwege maximus ligula imsum.",
  },
  {
    id: 3,
    className: "col-lg-6",
    path: "M6.514 24.015h-3v-3.39c-2.08-.638-3.5-2.652-3.5-5.04 0-1.19.202-1.693 1.774-5.603.521-1.294 1.195-2.97 2.068-5.179.204-.518.67-.806 1.17-.802.482.004.941.284 1.146.802.718 1.817 1.302 3.274 1.777 4.454.26-.596.567-1.288.928-2.103.694-1.565 1.591-3.592 2.754-6.265.258-.592.881-.906 1.397-.888.572.015 1.126.329 1.369.888 1.163 2.673 2.06 4.7 2.754 6.265 2.094 4.727 2.363 5.334 2.363 6.764 0 2.927-2.078 5.422-5 6.082v4.015h-3v-4.015c-.943-.213-1.797-.617-2.523-1.165-.612.845-1.466 1.48-2.477 1.79v3.39zm14.493-6c1.652 0 2.993 1.341 2.993 2.993s-1.341 2.993-2.993 2.993-2.993-1.341-2.993-2.993 1.341-2.993 2.993-2.993zm.007.993c1.104 0 2 .896 2 2s-.896 2-2 2-2-.896-2-2 .896-2 2-2zm-7.5 3.993v-3.839c4.906-.786 5-4.751 5-5.244 0-1.218-.216-1.705-2.277-6.359-2.134-4.82-2.721-6.198-2.755-6.261-.079-.145-.193-.292-.455-.297-.238 0-.37.092-.481.297-.034.063-.621 1.441-2.755 6.261-2.061 4.654-2.277 5.141-2.277 6.359 0 .493.094 4.458 5 5.244v3.839h1zm-6.123-12.448l-.08-.198c-1.589-3.957-2.04-5.116-2.067-5.171-.072-.151-.15-.226-.226-.228-.109 0-.188.13-.235.228-.028.05-.316.818-2.066 5.171-1.542 3.833-1.703 4.233-1.703 5.23 0 1.988 1.076 3.728 3.5 4.25v3.166h1v-3.166c1.266-.273 2.159-.876 2.725-1.666-1.078-1.12-1.725-2.619-1.725-4.251 0-.979.126-1.572.877-3.365z",
    title: "Clean and Minimal",
    description:
      "Maecenas volutpat, diam enime volutpa cramas luctus interdum sodales.",
  },
  {
    id: 4,
    className: "col-lg-6",
    path: "M16 3.383l-.924-.383-7.297 17.617.924.383 7.297-17.617zm.287 3.617l6.153 4.825-6.173 5.175.678.737 7.055-5.912-7.048-5.578-.665.753zm-8.478 0l-6.249 4.825 6.003 5.175-.679.737-6.884-5.912 7.144-5.578.665.753z",
    title: "Easy Customization",
    description:
      "Praesent sed nisi eleifend lorem ember fermete acome ante lorem ipsum.",
  },
];

export const qualities = [
  "We're Creative",
  "We're Punctual",
  "We're Friendly",
  "We're Professional",
  "We're Witty",
  "We're Honest",
  "We're Smart",
  "We're Resonance",
];

export const features = [
  { id: 1, text: "No credit card required" },
  { id: 2, text: "Free 30 minute consultation" },
  { id: 3, text: "Free marketing report" },
];

export const features2 = [
  {
    id: 1,
    text: "Business to business",
  },
  {
    id: 2,
    text: "Non-profit business",
  },
  {
    id: 3,
    text: "Business to customer",
  },
  {
    id: 4,
    text: "Ecommerce",
  },
];

export const features3 = [
  { text: "We're professional" },
  { text: "We're creative" },
  { text: "We're honest" },
  { text: "We're friendly" },
];

export const features4 = [
  {
    text: "Lorem ipsum dolor sit amet, consectetur adipiscing.",
  },
  {
    text: "Vivamus hendrerit eros vitae tincidunt vulputate.",
  },
  {
    text: "Aenean at bibendum enim. In auctor consectetur urna.",
  },
  {
    text: "Proin ut gravida lorem, quis scelerisque metus.",
  },
];

export const features5 = [
  {
    titleKey: "services.results.title",
    descriptionKey: "services.results.text",
    svgPath:
      "M21.62 20.196c1.055-.922 1.737-2.262 1.737-3.772 0-1.321-.521-2.515-1.357-3.412v-6.946l-11.001-6.066-11 6v12.131l11 5.869 5.468-2.917c.578.231 1.205.367 1.865.367.903 0 1.739-.258 2.471-.676l2.394 3.226.803-.596-2.38-3.208zm-11.121 2.404l-9.5-5.069v-10.447l9.5 4.946v10.57zm1-.001v-10.567l5.067-2.608.029.015.021-.04 4.384-2.256v5.039c-.774-.488-1.686-.782-2.668-.782-2.773 0-5.024 2.252-5.024 5.024 0 1.686.838 3.171 2.113 4.083l-3.922 2.092zm6.833-2.149c-2.219 0-4.024-1.808-4.024-4.026s1.805-4.025 4.024-4.025c2.22 0 4.025 1.807 4.025 4.025 0 2.218-1.805 4.026-4.025 4.026zm-.364-3.333l-1.306-1.147-.66.751 2.029 1.782 2.966-3.12-.725-.689-2.304 2.423zm-16.371-10.85l4.349-2.372 9.534 4.964-4.479 2.305-9.404-4.897zm9.4-5.127l9.404 5.186-3.832 1.972-9.565-4.98 3.993-2.178z",
  },
  {
    titleKey: "services.innovation.title",
    descriptionKey: "services.innovation.text",
    svgPath:
      "M12 0c-3.371 2.866-5.484 3-9 3v11.535c0 4.603 3.203 5.804 9 9.465 5.797-3.661 9-4.862 9-9.465v-11.535c-3.516 0-5.629-.134-9-3zm0 1.292c2.942 2.31 5.12 2.655 8 2.701v10.542c0 3.891-2.638 4.943-8 8.284-5.375-3.35-8-4.414-8-8.284v-10.542c2.88-.046 5.058-.391 8-2.701zm5 7.739l-5.992 6.623-3.672-3.931.701-.683 3.008 3.184 5.227-5.878.728.685z",
  },
  {
    titleKey: "services.business.title",
    descriptionKey: "services.business.text",
    svgPath:
      "M6.514 24.015h-3v-3.39c-2.08-.638-3.5-2.652-3.5-5.04 0-1.19.202-1.693 1.774-5.603.521-1.294 1.195-2.97 2.068-5.179.204-.518.67-.806 1.17-.802.482.004.941.284 1.146.802.718 1.817 1.302 3.274 1.777 4.454.26-.596.567-1.288.928-2.103.694-1.565 1.591-3.592 2.754-6.265.258-.592.881-.906 1.397-.888.572.015 1.126.329 1.369.888 1.163 2.673 2.06 4.7 2.754 6.265 2.094 4.727 2.363 5.334 2.363 6.764 0 2.927-2.078 5.422-5 6.082v4.015h-3v-4.015c-.943-.213-1.797-.617-2.523-1.165-.612.845-1.466 1.48-2.477 1.79v3.39zm14.493-6c1.652 0 2.993 1.341 2.993 2.993s-1.341 2.993-2.993 2.993-2.993-1.341-2.993-2.993 1.341-2.993 2.993-2.993zm.007.993c1.104 0 2 .896 2 2s-.896 2-2 2-2-.896-2-2 .896-2 2-2zm-7.5 3.993v-3.839c4.906-.786 5-4.751 5-5.244 0-1.218-.216-1.705-2.277-6.359-2.134-4.82-2.721-6.198-2.755-6.261-.079-.145-.193-.292-.455-.297-.238 0-.37.092-.481.297-.034.063-.621 1.441-2.755 6.261-2.061 4.654-2.277 5.141-2.277 6.359 0 .493.094 4.458 5 5.244v3.839h1zm-6.123-12.448l-.08-.198c-1.589-3.957-2.04-5.116-2.067-5.171-.072-.151-.15-.226-.226-.228-.109 0-.188.13-.235.228-.028.05-.316.818-2.066 5.171-1.542 3.833-1.703 4.233-1.703 5.23 0 1.988 1.076 3.728 3.5 4.25v3.166h1v-3.166c1.266-.273 2.159-.876 2.725-1.666-1.078-1.12-1.725-2.619-1.725-4.251 0-.979.126-1.572.877-3.365z",
  },
  {
    titleKey: "services.support.title",
    descriptionKey: "services.support.text",
    svgPath:
      "M4.57 18.55l2.43.335-1.769 1.7.432 2.415-2.163-1.157-2.163 1.157.432-2.415-1.769-1.7 2.43-.335 1.07-2.207 1.07 2.207zm8.5 0l2.43.335-1.769 1.7.432 2.415-2.163-1.157-2.163 1.157.432-2.415-1.769-1.7 2.43-.335 1.07-2.207 1.07 2.207zm8.5 0l2.43.335-1.769 1.7.432 2.415-2.163-1.157-2.163 1.157.432-2.415-1.769-1.7 2.43-.335 1.07-2.207 1.07 2.207zm-18.473.918l-.917.126.667.641-.163.91.816-.436.816.436-.163-.91.667-.641-.917-.126-.403-.832-.403.832zm8.5 0l-.917.126.667.641-.163.91.816-.436.816.436-.163-.91.667-.641-.917-.126-.403-.832-.403.832zm8.5 0l-.917.126.667.641-.163.91.816-.436.816.436-.163-.91.667-.641-.917-.126-.403-.832-.403.832zm-14.497-5.293l-4-8h5.381c-.3-1.42-1.573-2.5-3.066-2.5-1.838 0-3.315 1.662-3.315 3.5s1.477 3.5 3.315 3.5h2.684l.002.01c.283-.397.618-.747.995-1.041zm.915-8h-1.464c.691-.592 1.579-1 2.564-1 1.719 0 3.125 1.266 3.125 3.5s-1.406 3.5-3.125 3.5c-1.718 0-3.125-1.266-3.125-3.5 0-.39.065-.764.179-1.115-.293.381-.49.841-.553 1.354h3.662c.32-.662.866-1.211 1.559-1.538zm9.932 8.5h-1.25c-.427-.59-.994-1.082-1.661-1.431-.153-.446-.411-.838-.742-1.141h2.203c.3-1.42 1.573-2.5 3.066-2.5 1.838 0 3.315 1.662 3.315 3.5s-1.477 3.5-3.315 3.5h-2.684zm-10.848 0h-1.715c-.09-.465-.365-.869-.752-1.155.337-.312.596-.709.762-1.162h3.183c-.158.418-.278.87-.348 1.349zm8.348 8h-1.715c-.09-.465-.365-.869-.752-1.155.337-.312.596-.709.762-1.162h3.183c-.158.418-.278.87-.348 1.349zm-6.5 0h-1.25c-.427-.59-.994-1.082-1.661-1.431-.153-.446-.411-.838-.742-1.141h2.203c.3-1.42 1.573-2.5 3.066-2.5 1.838 0 3.315 1.662 3.315 3.5s-1.477 3.5-3.315 3.5h-2.684z",
  },
];

// Helper function to get translated features
export const getTranslatedFeatures = (t) => {
  return features5.map((feature) => ({
    ...feature,
    title: t(feature.titleKey),
    description: t(feature.descriptionKey),
  }));
};

export const featuresData = [
  {
    parentClass: "mb-100 mb-md-60",
    number: "01",
    title1: "Unique",
    title2: "Design",
    desc: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sem dolor, rhoncus eleifend fermentum vel, bibendum vel neque. Cras eu elementum enim. Curabitur elit leo, fringilla in velit at, fermentum semper augue. Vestibulum sit amet venenatis metus. Aenean a arcu sed augue consequat sagittis.`,
    iconPath:
      "M24 26.5c0 0 1-0.625 2.5-0.875 2.625-0.5 4.875-0.625 5.875 1.125 1.25 2.125 2.375 6.625 2.125 10.625-0.125 2.125-0.625 2.75-0.625 2.75s-1.875 1.25-5.875 0.875c-4.375-0.375-5-1.875-5-1.875s0.625-1.5 1-5.375 0-7.25 0-7.25zM19 35.625c-2.125 0.5-4.75 1.25-4.75 1.25s5.625-0.5 8-1.125c-0.5 2.625-1.25 4.25-1.25 4.25s-10.375 10-14.5 10c-3.5 0-6.5-8.5-6.5-18s3.25-18 6.5-18c4.375 0 13.25 7.125 16 12.125 0 0 0.375 2 0.25 4-1.25-0.625-3-1.25-5.5-1.75-3.5-0.75-5.25-0.375-5.25-0.375s2.25 0.5 7.125 2.125c1.875 0.625 2.75 1 3.625 1.25 0 0.375-0.125 0.875-0.125 1.25 0 0.625 0 1.25-0.125 1.875-0.625 0.25-1.875 0.75-3.5 1.125zM49.5 14c3.25 0 6.5 8.5 6.5 18s-3 18-6.5 18c-4.625 0-13-8.5-14-9.875 0 0 0.5-0.625 0.625-2.75v-1.125c2 0.875 8.75 1.625 8.75 1.625s-2.625-0.75-5-1.375c-1.625-0.5-3.125-1-3.75-1.25-0.125-1.375-0.25-2.875-0.5-4.125 2.125-1.375 8.625-4.25 8.625-4.25s-1.875 0.25-4.375 1.125c-1.75 0.625-3.625 1.375-4.625 1.75-0.375-1.25-0.875-2.25-1.25-3 0-0.125-0.125-0.5-0.125-0.5v-0.125c2.75-5 11.125-12.125 15.625-12.125z",
  },
  {
    parentClass: "mt-n140 mt-sm-0 mb-100 mb-md-60",
    number: "02",
    title1: "Quality",
    title2: "Code",
    desc: `Praesent nec tempus dui. Curabitur molestie, sapien semper dictum porttitor, odio ipsum hendrerit urna, quis rutrum mi tortor quis velit. Nulla scelerisque eu nisi id volutpat. Nullam fringilla, eros ut semper tincidunt, massa ante congue erat, in vestibulum nibh urna vitae lorem aliquet ornare.`,
    iconPath:
      "M0 46l29.5-29.5 10 10-29.5 29.5h-10v-10zM47.25 18.75l-5 5-10-10 5-5c1.125-1.125 2.5-1.125 3.625 0l6.375 6.375c1.125 1.125 1.125 2.5 0 3.625z",
  },
  {
    parentClass: "mb-md-60",
    number: "03",
    title1: "Clean and",
    title2: "Minimal",
    desc: `Integer sit amet nunc sem. Quisque scelerisque dui id fermentum venenatis. Cras non tempor nisi. Aliquam lobortis ligula id efficitur iaculis ipum. Curabitur elit leo, fringilla in velit at, fermentum semper augue. Vestibulum sit amet venenatis metus. Aenean a arcu sed augue consequat sagittis.`,
    iconPath:
      "M24 8c13.25 0 24 9.625 24 21.375 0 7.375-6 13.25-13.375 13.25h-4.75c-2.25 0-4 1.75-4 4 0 1.125 0.375 2.125 1 2.75s1.125 1.5 1.125 2.625c0 2.25-1.75 4-4 4-13.25 0-24-10.75-24-24s10.75-24 24-24zM9.375 32c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4zM17.375 21.375c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4zM30.625 21.375c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4zM38.625 32c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4z",
  },
  {
    parentClass: "mt-n140 mt-sm-0 mb-0",
    number: "04",
    title1: "Premium",
    title2: "Suport",
    desc: `Aliquam nec erat eleifend, volutpat dolor eu, ultrices erat. Etiam et arcu nec quam laoreet hendrerit eget sed velit. Nullam dapibus magna odio. Fusce convallis elit at ex sodales, quis maximus augue hendrerit. Phasellus eget turpis vel nibh convallis vulputate. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,
    iconPath:
      "M25.25 46.625l-15.625 9.375 4.125-17.75-13.75-12 18.125-1.5 7.125-16.75 7.125 16.75 18.125 1.5-13.75 12 4.125 17.75z",
  },
];

export const featuresListData = [
  { text: "Agency Website" },
  { text: "Personal Website" },
  { text: "Landing Page" },
  { text: "Portfolio and Gallery" },
];
