import fs from "fs";

try {
  const data = JSON.parse(
    fs.readFileSync("build/bundle-analysis.json", "utf8")
  );

  console.log("=== BUNDLE ANALYSIS SUMMARY ===\n");

  // Extract bundle sizes
  const bundles = {};
  data.tree.children.forEach((bundle) => {
    if (bundle.name.includes(".js") || bundle.name.includes(".css")) {
      bundles[bundle.name] = bundle;
    }
  });

  // Sort by size
  const sortedBundles = Object.entries(bundles).sort((a, b) => {
    const sizeA = data.nodeMetas[a[1].uid]?.renderedLength || 0;
    const sizeB = data.nodeMetas[b[1].uid]?.renderedLength || 0;
    return sizeB - sizeA;
  });

  console.log("📊 BUNDLE SIZES (largest first):");
  sortedBundles.forEach(([name, bundle]) => {
    const meta = data.nodeMetas[bundle.uid];
    if (meta) {
      const size = (meta.renderedLength / 1024).toFixed(1);
      const gzip = meta.gzipLength
        ? (meta.gzipLength / 1024).toFixed(1)
        : "N/A";
      console.log(`  ${name}: ${size} KB (gzip: ${gzip} KB)`);
    }
  });

  console.log("\n🔍 HIGHLIGHT.JS ANALYSIS:");
  let highlightCount = 0;
  let highlightSize = 0;

  function analyzeNode(node, path = "") {
    if (node.name && node.name.includes("highlight.js")) {
      const meta = data.nodeMetas[node.uid];
      if (meta && meta.renderedLength) {
        highlightCount++;
        highlightSize += meta.renderedLength;
        const size = (meta.renderedLength / 1024).toFixed(1);
        console.log(`  ${path}/${node.name}: ${size} KB`);
      }
    }

    if (node.children) {
      node.children.forEach((child) => {
        analyzeNode(child, path ? `${path}/${node.name}` : node.name);
      });
    }
  }

  analyzeNode(data.tree);

  console.log(`\nTotal highlight.js files: ${highlightCount}`);
  console.log(
    `Total highlight.js size: ${(highlightSize / 1024).toFixed(1)} KB`
  );

  console.log("\n🎯 TOP 10 LARGEST FILES IN VENDOR-MISC:");
  const vendorMisc = data.tree.children.find((c) =>
    c.name.includes("vendor-misc")
  );
  if (vendorMisc) {
    const files = [];

    function collectFiles(node) {
      if (
        node.uid &&
        data.nodeMetas[node.uid] &&
        data.nodeMetas[node.uid].renderedLength
      ) {
        files.push({
          name: node.name,
          size: data.nodeMetas[node.uid].renderedLength,
        });
      }
      if (node.children) {
        node.children.forEach(collectFiles);
      }
    }

    collectFiles(vendorMisc);

    files
      .sort((a, b) => b.size - a.size)
      .slice(0, 10)
      .forEach((file) => {
        console.log(`  ${file.name}: ${(file.size / 1024).toFixed(1)} KB`);
      });
  }
} catch (error) {
  console.error("Error analyzing bundle:", error.message);
}
