import{u,j as e,a as x,r as y}from"./vendor-react-BE9lZbv0.js";import{P as i}from"./vendor-misc-BUjjPnRU.js";import{L as f,a as b}from"./components-common-DSJ_Yu9e.js";import{t as g}from"./components-home-WXC5F58I.js";const j=[{href:"#",title:"Facebook",iconClass:"fa-facebook-f",name:"Facebook"},{href:"#",title:"X",iconClass:"fa-x-twitter",name:"X"},{href:"#",title:"Instagram",iconClass:"fa-instagram",name:"Instagram"},{href:"https://www.youtube.com/@DevSkillsStudio",title:"YouTube",iconClass:"fa-youtube",name:"YouTube"},{href:"https://www.linkedin.com/company/devskills-development-studio",title:"LinkedIn",iconClass:"fa-linkedin-in",name:"LinkedIn"},{href:"#",title:"WhatsApp",iconClass:"fa-whatsapp",name:"WhatsApp"}];function E(){const{t:s}=u(),t=a=>{a.preventDefault(),window.scrollTo({top:0,behavior:"smooth"})};return e.jsxs("div",{className:"container position-relative text-center pt-140 pb-80 pb-sm-50",children:[e.jsx("div",{className:"local-scroll link-to-top-2-wrap",children:e.jsx("a",{href:"#top",className:"link-to-top-2",onClick:t,children:s("footer.backToTop")})}),e.jsx("div",{className:"footer-social-links mb-60",children:j.map((a,o)=>e.jsxs("a",{href:a.href,title:a.title,rel:"noreferrer nofollow",target:"_blank",children:[e.jsx("span",{className:"visually-hidden",children:a.name}),e.jsx("i",{className:`fa ${a.iconClass}`})]},o))}),e.jsxs("div",{className:"footer-text",children:[e.jsxs("div",{children:["© Devskills OÜ ",new Date().getFullYear()," -"," ",s("footer.allRightsReserved")]}),e.jsx("div",{className:"footer-made",children:"Making great things that people actually need"})]})]})}const w=()=>{var s=document.querySelector(".mobile-nav"),t=document.querySelector(".desktop-nav");t.classList.contains("js-opened")?(t.style.maxHeight="0px",t.classList.remove("js-opened"),s.classList.remove("active")):(t.style.maxHeight="calc(100vh - 60px)",t.classList.add("js-opened"),s.classList.add("active"))},L=()=>{var s=document.querySelector(".mobile-nav"),t=document.querySelector(".desktop-nav");t.classList.contains("js-opened")&&(t.style.maxHeight="0px",t.classList.remove("js-opened"),s.classList.remove("active"))},h=(s="",t=".scrollspy-link",a="active")=>{var c;var o=document.querySelectorAll(".scrollSpysection"),r={},l=0;Array.prototype.forEach.call(o,function(n){r[n.id]=n.offsetTop});var d=document.documentElement.scrollTop||document.body.scrollTop;for(l in r)if(r[l]<=d){(c=document.querySelector(t+` .${a}`))==null||c.setAttribute("class",s);const n=document.querySelector(t+" a[href*="+l+"]");n==null||n.setAttribute("class",`${s} ${a}`)}};function p(){var s=document.querySelector(".mobile-nav"),t=document.querySelector(".desktop-nav");s.setAttribute("aria-expanded","false"),document.querySelector(".main-nav")&&(document.querySelector(".desktop-nav > ul").style.maxHeight=window.innerHeight-document.querySelector(".main-nav").offsetHeight-20+"px"),window.innerWidth<=1024?(document.querySelector(".main-nav").classList.add("mobile-on"),s.classList.contains("active")||(t.style.display="none")):window.innerWidth>1024&&(document.querySelector(".main-nav").classList.remove("mobile-on"),t.style.display="block")}const N=()=>{document.querySelectorAll('a[href^="#"]').forEach(s=>{s.href&&s.href!="#"&&!s.hasAttribute("data-bs-toggle")&&s.addEventListener("click",t=>{t.preventDefault(),console.log("clicle");const a=s.href.split("#")[1],o=document.getElementById(a);if(o){const l=o.getBoundingClientRect().top+window.pageYOffset+70;window.scrollTo({top:l,behavior:"smooth"})}})})};function v({links:s,animateY:t=!1}){var r,l,d,c;const{t:a}=u(),{pathname:o}=x();return y.useEffect(()=>(setTimeout(()=>{N()},1e3),p(),window.addEventListener("scroll",h),window.addEventListener("resize",p),()=>{window.removeEventListener("scroll",h),window.removeEventListener("resize",p)}),[]),e.jsxs(e.Fragment,{children:[((l=(r=s[0])==null?void 0:r.href)==null?void 0:l.includes("/"))&&s.map((n,m)=>e.jsx("li",{children:e.jsx(f,{className:o.split("/")[2]===n.href.split("/")[1]?"active":"",to:n.href,children:t?e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:a(`menu.${n.text.toLowerCase()}`)}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:a(`menu.${n.text.toLowerCase()}`)})]}):a(`menu.${n.text.toLowerCase()}`)})},m)),!((c=(d=s[0])==null?void 0:d.href)!=null&&c.includes("/"))&&s.map((n,m)=>e.jsx("li",{className:"scrollspy-link",children:e.jsx("a",{onClick:()=>L(),className:"",href:n.href,children:t?e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:a(`menu.${n.text.toLowerCase()}`)}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:a(`menu.${n.text.toLowerCase()}`)})]}):a(`menu.${n.text.toLowerCase()}`)})},m))]})}v.propTypes={links:i.arrayOf(i.shape({href:i.string.isRequired,text:i.string.isRequired})).isRequired,animateY:i.bool};function k({links:s}){const{t}=u();t||console.warn("Language store not initialized, using fallback");const a=o=>{o.preventDefault(),g("navbar",{cta_type:"navbar_button",section:"navigation"}),window.open("https://comanager.ee","_blank")};return e.jsxs("div",{className:"main-nav-sub full-wrapper",children:[e.jsx("div",{className:"nav-logo-wrap local-scroll",children:e.jsxs(f,{to:"/",className:"logo font-alt d-flex align-items-center gap-2 pointer-event",children:[e.jsx("img",{src:"/assets/img/power-128.png",alt:"Your Company Logo",width:26,height:24}),e.jsx("span",{className:"mt-1",children:"DEVSKILLS"})]})}),e.jsxs("div",{onClick:w,className:"mobile-nav",role:"button",tabIndex:0,children:[e.jsx("i",{className:"mobile-nav-icon"}),e.jsx("span",{className:"visually-hidden",children:"Menu"})]}),e.jsx("div",{className:"inner-nav desktop-nav",children:e.jsxs("ul",{className:"clearlist scroll-nav local-scroll justify-content-end scrollspyLinks",children:[e.jsx(v,{links:s}),e.jsx("li",{className:"ms-3 me-2 d-flex align-items-center",children:e.jsx(b,{})}),e.jsx("li",{children:e.jsx("a",{href:"https://comanager.ee",onClick:a,className:"opacity-1 no-hover",style:{cursor:"pointer"},children:e.jsx("span",{className:"btn btn-mod btn-small btn-border-w btn-circle","data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:"Business Comanager"}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:"Business Comanager"})]})})})})]})})]})}k.propTypes={links:i.arrayOf(i.shape({href:i.string.isRequired,text:i.string.isRequired})).isRequired};export{E as F,k as H};
//# sourceMappingURL=components-layout-Btds6-N8.js.map
