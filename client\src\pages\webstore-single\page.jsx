import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import {
  generateProductSchema,
  generateBreadcrumbSchema,
} from "@/utils/seoHelpers";

import Header from "@/components/headers/Header";
import Footer from "@/components/footers/Footer";
import { trackEvent } from "@/utils/analytics";
import { menuItems } from "@/data/menu";
import ProductGallery from "@/components/ProductGallery";

export default function ElegantWebstoreSinglePageDark() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t: translate, i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";

  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    loadProduct();
  }, [id, currentLanguage]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/products/${id}?language=${currentLanguage}`
      );
      const data = await response.json();

      if (data.success) {
        setProduct(data.product);

        // Track product view
        trackEvent("product_detail_view", {
          product_id: data.product.id,
          product_title: data.product.title,
          language: currentLanguage,
          source: "direct_link",
        });
      } else {
        setError("Product not found");
      }
    } catch (err) {
      console.error("Error loading product:", err);
      setError("Error loading product");
    } finally {
      setLoading(false);
    }
  };

  const handleDemoClick = () => {
    trackEvent("demo_click", {
      product_id: product.id,
      product_title: product.title,
      language: currentLanguage,
      source: "product_detail",
    });
  };

  const handlePurchaseClick = (type) => {
    trackEvent("purchase_intent", {
      product_id: product.id,
      product_title: product.title,
      purchase_type: type, // 'whitelabel' or 'subscription'
      language: currentLanguage,
      source: "product_detail",
    });
  };

  const formatPrice = (price) => {
    if (!price) return null;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "EUR",
    }).format(price);
  };

  if (loading) {
    return (
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-1 light-content">
                <div className="container text-center py-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              </section>
            </main>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-1 light-content">
                <div className="container text-center py-5">
                  <h1>Product Not Found</h1>
                  <p>The product you're looking for doesn't exist.</p>
                  <button
                    onClick={() => navigate(`/${currentLanguage}/webstore`)}
                    className="btn btn-mod btn-round"
                  >
                    Back to Webstore
                  </button>
                </div>
              </section>
            </main>
          </div>
        </div>
      </div>
    );
  }

  // Generate product schema
  const productSchema = generateProductSchema({
    title: product.title,
    description: product.excerpt,
    price: product.whitelabelPrice || product.subscriptionPrice,
    featuredImage: product.featuredImage,
    url: `https://devskills.ee/${currentLanguage}/webstore-single/${product.slug}`,
  });

  // Generate breadcrumb schema
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: "Home", url: `https://devskills.ee/${currentLanguage}` },
    {
      name: "Webstore",
      url: `https://devskills.ee/${currentLanguage}/webstore`,
    },
    {
      name: product.title,
      url: `https://devskills.ee/${currentLanguage}/webstore-single/${product.slug}`,
    },
  ]);

  const productImage = product.featuredImage
    ? `${
        import.meta.env.VITE_API_BASE_URL || "http://localhost:4004"
      }/uploads/product-images/${product.featuredImage}`
    : "https://devskills.ee/webstore.jpg";

  return (
    <>
      <UnifiedSEO
        title={product.metaTitle || product.title}
        description={product.metaDescription || product.excerpt}
        slug={`webstore-single/${product.slug}`}
        type="product"
        image={productImage}
        imageAlt={product.featuredImageAlt || product.title}
        schema={[productSchema, breadcrumbSchema]}
        keywords={
          product.metaKeywords
            ? product.metaKeywords.split(",").map((k) => k.trim())
            : ["software", "business", "devskills"]
        }
        publishedAt={product.publishedAt}
        modifiedAt={product.updatedAt}
      />

      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>

            <main id="main">
              {/* Hero Section with Background */}
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/3.jpg)",
                  paddingBottom: "40px",
                }}
              >
                <div className="container position-relative pt-30 pt-sm-50">
                  <div className="text-center">
                    <div className="row">
                      <div className="col-md-8 offset-md-2">
                        <h1 className="hs-title-1 mb-20">
                          <span
                            className="wow charsAnimIn"
                            data-splitting="chars"
                          >
                            {product.title}
                          </span>
                        </h1>
                        <div className="row">
                          <div className="col-lg-8 offset-lg-2">
                            <p
                              className="section-descr mb-20 wow fadeIn"
                              data-wow-delay="0.2s"
                            >
                              {product.excerpt}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Product Content Section */}
              <section
                className="page-section bg-dark-1 light-content"
                style={{ paddingTop: "40px" }}
              >
                <div className="container position-relative">
                  {/* Product Content */}
                  <div className="row">
                    <div className="col-lg-8">
                      {/* Product Gallery */}
                      <ProductGallery
                        images={product.images}
                        productTitle={product.title}
                      />

                      {/* Product Content */}
                      <div className="blog-item-body">
                        <div
                          className="blog-item-content"
                          dangerouslySetInnerHTML={{ __html: product.content }}
                        />
                      </div>

                      {/* Categories and Tags */}
                      <div className="blog-item-footer pt-4 mt-4 border-top">
                        {product.categories &&
                          product.categories.length > 0 && (
                            <div className="mb-3">
                              <strong>Categories: </strong>
                              {product.categories.map((cat, index) => (
                                <span key={cat.category.id}>
                                  <span className="badge bg-primary me-2">
                                    {cat.category.name}
                                  </span>
                                </span>
                              ))}
                            </div>
                          )}

                        {product.tags && product.tags.length > 0 && (
                          <div>
                            <strong>Tags: </strong>
                            {product.tags.map((tag, index) => (
                              <span key={tag.tag.id}>
                                <span className="badge bg-secondary me-2">
                                  {tag.tag.name}
                                </span>
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Sidebar */}
                    <div className="col-lg-4">
                      <div className="blog-sidebar ps-lg-4">
                        {/* Pricing Options */}
                        {product.whitelabelPrice && (
                          <div className="widget mb-4">
                            <div
                              className="pricing-option mb-4 p-4 rounded-3"
                              style={{
                                background: "rgba(255, 255, 255, 0.02)",
                                border: "1px solid rgba(255, 255, 255, 0.15)",
                                transition: "all 0.3s ease",
                              }}
                            >
                              <h6 className="text-white mb-3 text-center">
                                Whitelabel License
                              </h6>
                              <div className="text-center mb-3">
                                <div
                                  className="price text-primary"
                                  style={{
                                    fontSize: "2.5rem",
                                    fontWeight: "700",
                                  }}
                                >
                                  {formatPrice(product.whitelabelPrice)}
                                </div>
                                <small className="text-gray">
                                  One-time payment
                                </small>
                              </div>
                              <p className="text-gray small mb-4 text-center">
                                Get the complete source code with commercial
                                license
                              </p>
                              <div className="text-center">
                                <button
                                  className="btn btn-mod btn-medium btn-circle btn-hover-anim btn-color"
                                  onClick={() =>
                                    handlePurchaseClick("whitelabel")
                                  }
                                  style={{ minWidth: "180px" }}
                                >
                                  <span>Buy Whitelabel</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        )}

                        {product.subscriptionPrice && (
                          <div className="widget mb-4">
                            <div
                              className="pricing-option mb-4 p-4 rounded-3"
                              style={{
                                background: "rgba(255, 255, 255, 0.02)",
                                border: "1px solid rgba(255, 255, 255, 0.15)",
                                transition: "all 0.3s ease",
                              }}
                            >
                              <h6 className="text-white mb-3 text-center">
                                Subscription
                              </h6>
                              <div className="text-center mb-3">
                                <div
                                  className="price text-success"
                                  style={{
                                    fontSize: "2.5rem",
                                    fontWeight: "700",
                                  }}
                                >
                                  {formatPrice(product.subscriptionPrice)}
                                  <small
                                    style={{
                                      fontSize: "1rem",
                                      fontWeight: "400",
                                    }}
                                  >
                                    /mo
                                  </small>
                                </div>
                                <small className="text-gray">
                                  Monthly billing
                                </small>
                              </div>
                              <p className="text-gray small mb-4 text-center">
                                Use the software as a service without source
                                code
                              </p>
                              <div className="text-center">
                                <button
                                  className="btn btn-mod btn-medium btn-circle btn-hover-anim"
                                  onClick={() =>
                                    handlePurchaseClick("subscription")
                                  }
                                  style={{
                                    minWidth: "180px",
                                    background: "#22c55e",
                                    borderColor: "#22c55e",
                                    color: "#fff",
                                  }}
                                >
                                  <span>Start Subscription</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        )}

                        {product.demoUrl && (
                          <div className="widget mb-4">
                            <div className="text-center mt-4">
                              <a
                                href={product.demoUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="opacity-1 no-hover"
                                onClick={handleDemoClick}
                                style={{ cursor: "pointer" }}
                              >
                                <span
                                  className="btn btn-mod btn-small btn-border-w btn-circle"
                                  data-btn-animate="y"
                                >
                                  <span className="btn-animate-y">
                                    <span className="btn-animate-y-1">
                                      View Live Demo
                                    </span>
                                    <span
                                      className="btn-animate-y-2"
                                      aria-hidden="true"
                                    >
                                      View Live Demo
                                    </span>
                                  </span>
                                </span>
                              </a>
                            </div>
                          </div>
                        )}

                        {/* Product Info */}
                        <div className="widget">
                          <h5 className="widget-title">Product Information</h5>
                          <ul className="list-unstyled">
                            <li className="mb-2">
                              <strong>Published:</strong>{" "}
                              {new Date(
                                product.publishedAt
                              ).toLocaleDateString()}
                            </li>
                            <li className="mb-2">
                              <strong>Last Updated:</strong>{" "}
                              {new Date(product.updatedAt).toLocaleDateString()}
                            </li>
                            <li className="mb-2">
                              <strong>Views:</strong> {product.viewCount}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </main>

            <footer className="footer-1 bg-dark-2 light-content">
              <Footer />
            </footer>
          </div>
        </div>
      </div>
    </>
  );
}
