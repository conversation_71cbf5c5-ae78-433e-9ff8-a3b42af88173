<!DOCTYPE html>
<html lang="en" class="no-mobile no-touch">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon-theme.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#06B6D4" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta
      name="description"
      content="DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager."
    />
    <meta name="robots" content="index, follow" />
    <link rel="manifest" href="/manifest.json" />
    <script type="application/ld+json" src="/structured-data.json"></script>
    <title>DevSkills - Professional Software Development Services</title>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://devskills.ee/" />
    <meta
      property="og:title"
      content="DevSkills - Professional Software Development Services"
    />
    <meta
      property="og:description"
      content="DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager."
    />
    <meta property="og:image" content="https://devskills.ee/home.jpg" />
    <meta property="og:image:width" content="1162" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="DevSkills" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://devskills.ee/" />
    <meta
      property="twitter:title"
      content="DevSkills - Professional Software Development Services"
    />
    <meta
      property="twitter:description"
      content="DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager."
    />
    <meta property="twitter:image" content="https://devskills.ee/home.jpg" />
    <meta property="twitter:site" content="@DevSkillsEE" />
    <meta property="twitter:creator" content="@DevSkillsEE" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://devskills.ee/" />

    <!-- Force clear old service worker cache -->
    <script>
      if ("serviceWorker" in navigator) {
        navigator.serviceWorker
          .getRegistrations()
          .then(function (registrations) {
            for (let registration of registrations) {
              registration.unregister();
            }
          });
      }
      // Clear all caches
      if ("caches" in window) {
        caches.keys().then(function (names) {
          for (let name of names) {
            caches.delete(name);
          }
        });
      }
    </script>

    <!-- Google Analytics 4 with Consent Mode -->
    <script>
      // Initialize dataLayer and gtag function
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }

      // Set default consent mode
      gtag("consent", "default", {
        analytics_storage: "denied",
        ad_storage: "denied",
        ad_user_data: "denied",
        ad_personalization: "denied",
        wait_for_update: 500,
      });

      gtag("js", new Date());
      gtag("config", "G-8NEGL4LL8Q", {
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false,
      });
    </script>
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-8NEGL4LL8Q"
    ></script>

    <!-- Critical font preloading for LCP optimization -->
    <link
      rel="preload"
      href="/assets/webfonts/dm-sans/DMSans-Regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/assets/webfonts/poppins/Poppins-Regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/assets/webfonts/poppins/Poppins-SemiBold.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />

    <!-- Critical CSS - Inlined for instant rendering -->
    <style id="critical-css">
      /* Critical CSS for Above-the-fold Content - DevSkills */
      :root {
        --font-global: "DM Sans", sans-serif;
        --font-alt: "DM Sans", sans-serif;
        --container-width: 1350px;
        --section-padding-y: 120px;
        --menu-bar-height: 85px;
        --color-dark-1: #010101;
        --color-dark-2: #171717;
        --color-gray-1: #757575;
        --color-dark-mode-gray-1: rgba(255, 255, 255, 0.7);
      }

      .theme-elegant {
        --font-global: "Poppins", sans-serif;
        --container-width: 1230px;
        --section-padding-y: 120px; /* Reduced from 160px to fix oversized headers */
        --color-dark-1: #111;
        --color-gray-1: #777;
      }

      .theme-elegant .dark-mode {
        --color-dark-1: #171717;
        --color-dark-2: #222;
      }

      * {
        box-sizing: border-box;
      }

      html,
      body {
        margin: 0;
        padding: 0;
        background-color: #000000 !important;
        background: #000000 !important;
      }

      body {
        font-family: var(--font-global);
        font-size: 16px;
        line-height: 1.6;
        color: var(--color-dark-1);
      }

      .theme-elegant body {
        color: var(--color-dark-1);
        font-family: var(--font-global);
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0.01em;
        line-height: 2;
      }

      .container {
        width: 100%;
        max-width: var(--container-width);
        margin: 0 auto;
        padding: 0 15px;
      }

      .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
      }

      .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
      }
      .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%;
      }

      .page {
        background-color: var(--color-dark-1);
        min-height: 100vh;
      }

      .bg-dark-1 {
        background-color: var(--color-dark-1) !important;
      }

      .main-nav {
        display: flex;
        width: 100%;
        height: var(--menu-bar-height);
        position: relative;
        background: rgba(255, 255, 255, 0.98);
        z-index: 1030;
        transition: all 0.2s ease;
      }

      .main-nav.dark {
        background-color: rgba(10, 10, 10, 0.905);
        box-shadow: none;
      }

      .main-nav.transparent {
        background: transparent;
        box-shadow: none;
      }

      .main-nav-sub {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0 30px;
      }

      .nav-logo-wrap {
        display: flex;
        align-items: center;
      }

      .logo {
        font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif !important;
        font-size: 18px;
        font-weight: 600 !important;
        text-decoration: none;
        color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .logo .mt-1 {
        font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif !important;
        font-weight: 600 !important;
      }

      .home-section {
        width: 100%;
        display: block;
        position: relative;
        background-repeat: no-repeat;
        background-attachment: fixed;
        background-position: center center;
        background-size: cover;
      }

      .home-content {
        width: 100%;
        position: relative;
        text-align: center;
      }

      .min-height-100vh {
        min-height: 100vh !important;
        /* Override the problematic 100svh */
        min-height: 100vh !important;
      }

      .d-flex {
        display: flex;
      }

      .align-items-center {
        align-items: center;
      }

      .pt-100 {
        padding-top: 100px !important;
      }

      .pb-100 {
        padding-bottom: 100px !important;
      }

      .pt-sm-120 {
        padding-top: 120px !important;
      }

      .pb-sm-120 {
        padding-bottom: 120px !important;
      }

      /* Fix hero container to be exactly 100vh total height */
      .min-height-100vh.pt-100.pb-100 {
        min-height: calc(100vh - 200px) !important;
        box-sizing: border-box !important;
      }

      /* Ensure home section is exactly 100vh */
      .home-section {
        min-height: 100vh !important;
        max-height: 100vh !important;
        height: 100vh !important;
      }

      .page-section,
      .small-section,
      .bg-image {
        width: 100%;
        display: block;
        position: relative;
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
        padding-top: var(--section-padding-y);
        padding-bottom: var(--section-padding-y);
      }

      .small-section {
        padding: 100px 0;
      }

      .parallax-5 {
        background-attachment: fixed;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
      }

      .bg-dark-alpha-30::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: 1;
      }

      .light-content {
        color: #ffffff;
      }

      .section-title-tiny {
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 2px;
        text-transform: uppercase;
        margin-bottom: 50px;
        opacity: 0.8;
      }

      .hs-title-3 {
        margin-top: 0;
        font-size: 38px !important;
        font-weight: 400 !important;
        line-height: 1.3 !important;
        letter-spacing: 0.3em !important;
        text-transform: uppercase !important;
      }

      .mb-120 {
        margin-bottom: 120px;
      }

      .mb-sm-80 {
        margin-bottom: 80px;
      }

      .mb-xs-140 {
        margin-bottom: 140px;
      }

      .mb-50 {
        margin-bottom: 50px;
      }

      .mb-sm-30 {
        margin-bottom: 30px;
      }

      .wow {
        opacity: 0.001;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
      }

      .animated {
        -webkit-animation-duration: 1s;
        animation-duration: 1s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
        opacity: 1;
        transform: scale(1);
      }

      .appear-animate .wow.animated {
        opacity: 1;
        transform: scale(1);
      }

      @keyframes fadeInDown {
        0% {
          opacity: 0;
          transform: translate3d(0, -37px, 0);
        }
        to {
          opacity: 1;
          transform: translate3d(0, 0, 0);
        }
      }

      @keyframes fadeInUp {
        0% {
          opacity: 0;
          transform: translate3d(0, 37px, 0);
        }
        to {
          opacity: 1;
          transform: translate3d(0, 0, 0);
        }
      }

      .fadeInDown {
        animation-name: fadeInDown;
      }

      .fadeInUp {
        animation-name: fadeInUp;
      }

      .d-flex {
        display: flex;
      }
      .align-items-center {
        align-items: center;
      }
      .justify-content-center {
        justify-content: center;
      }
      .text-center {
        text-align: center;
      }
      .position-relative {
        position: relative;
      }

      @media (max-width: 1366px) {
        .theme-elegant {
          --section-padding-y: 100px; /* Reduced from 140px */
        }

        .hs-title-3 {
          font-size: calc(1.559rem + 0.96vw) !important;
        }
      }

      @media (max-width: 768px) {
        .hs-title-3 {
          font-size: calc(1.559rem + 0.96vw) !important;
        }

        .section-title-tiny {
          margin-bottom: 30px;
        }

        .main-nav-sub {
          padding: 0 15px;
        }

        .pt-sm-120 {
          padding-top: 120px;
        }

        .pb-sm-120 {
          padding-bottom: 120px;
        }

        .mb-sm-80 {
          margin-bottom: 80px;
        }

        .mb-sm-30 {
          margin-bottom: 30px;
        }

        .mb-xs-140 {
          margin-bottom: 140px;
        }
      }
    </style>

    <!-- Self-hosted fonts - Deferred for performance -->
    <link
      rel="preload"
      href="/assets/css/fonts.css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript><link rel="stylesheet" href="/assets/css/fonts.css" /></noscript>

    <!-- Hero section height fixes - loaded after main CSS -->
    <link rel="stylesheet" href="/assets/css/hero-fixes.css">

    <!-- Note: Main CSS bundle still loads via Vite build process -->

      /* Critical GDPR Banner Styles - Prevent render blocking */
      .gdpr-banner {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(40, 40, 40, 0.95);
        backdrop-filter: blur(20px);
        color: #ffffff;
        padding: 20px 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        transform: translateZ(0);
        will-change: transform;
        contain: layout style paint;
        /* Initially hidden, shown by React when needed */
        display: none;
      }
      .gdpr-banner.gdpr-show {
        display: block;
      }

      /* Instant GDPR banner with slide-up animation */
      .gdpr-banner.gdpr-instant {
        transform: translateY(100%);
        animation: gdprSlideUp 0.5s ease-out 0.1s forwards;
      }

      @keyframes gdprSlideUp {
        to {
          transform: translateY(0);
        }
      }

      /* Instant hero section animations */
      .hero-instant-welcome {
        opacity: 0;
        visibility: hidden;
        transform: translateY(30px);
        animation-fill-mode: both;
      }

      .hero-instant-welcome.animated {
        opacity: 1 !important;
        visibility: visible !important;
        animation-name: fadeInDownShort !important;
      }

      .hero-instant-studio {
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
        animation-fill-mode: both;
      }

      .hero-instant-studio.animated {
        opacity: 1 !important;
        visibility: visible !important;
        animation-name: fadeInUpShort !important;
      }

      /* Ensure WOW animations work with instant content */
      @keyframes fadeInDownShort {
        from {
          opacity: 0;
          transform: translateY(-30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes fadeInUpShort {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .gdpr-content {
        text-align: left;
        padding-right: 20px;
      }
      .gdpr-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
      }
      .gdpr-description {
        font-size: 0.9rem;
        line-height: 1.4;
        color: #e0e0e0;
        margin-bottom: 0;
      }
      .gdpr-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        justify-content: flex-end;
        align-items: center;
      }
      .gdpr-buttons .btn {
        white-space: nowrap;
      }
      /* Mobile responsive */
      @media (max-width: 768px) {
        .gdpr-banner {
          padding: 15px 0;
        }
        .gdpr-content {
          padding-right: 10px;
          margin-bottom: 15px;
        }
        .gdpr-title {
          font-size: 1rem;
        }
        .gdpr-description {
          font-size: 0.8rem;
        }
        .gdpr-buttons {
          gap: 6px;
          justify-content: center;
          flex-wrap: wrap;
        }
        .gdpr-buttons .btn {
          font-size: 0.75rem;
          padding: 6px 10px;
          min-width: auto;
        }
      }
    </style>

    <!-- Iconify Web Component for Solar Icons - Deferred for LCP performance -->
    <script
      defer
      src="https://code.iconify.design/iconify-icon/2.1.0/iconify-icon.min.js"
    ></script>
    <script type="module" crossorigin src="/assets/index-CU_BpMaE.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-animations-Dl3DQHMd.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-misc-j6k8kvFA.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-gallery-BKyWYjF6.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-admin-DvrlCxcB.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-react-EBZQFYZ5.js">
    <link rel="modulepreload" crossorigin href="/assets/admin-routes-D7V7jRaZ.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-i18n-DxzbetI3.js">
    <link rel="modulepreload" crossorigin href="/assets/components-common-DDbdC8oB.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-utils-t--hEgTQ.js">
    <link rel="modulepreload" crossorigin href="/assets/components-home-B-IXSbjU.js">
    <link rel="modulepreload" crossorigin href="/assets/components-layout-DGce1jMY.js">
    <link rel="modulepreload" crossorigin href="/assets/pages-other-BrCzhuD6.js">
    <link rel="stylesheet" crossorigin href="/assets/vendor-misc-Cg1sXqY3.css">
    <link rel="stylesheet" crossorigin href="/assets/components-layout-LOVgDiEq.css">
    <link rel="stylesheet" crossorigin href="/assets/critical-css-CUEwOf80.css">
  </head>
  <body class="appear-animate body">
    <div id="root"></div>

    <!-- Immediate GDPR display for LCP optimization -->
    <script>
      // Critical texts for instant display (LCP optimization)
      window.heroTexts = {
        en: {
          welcome: "Turning Your Ideas Into Reality",
          studio: "DEVSKILLS DEVELOPMENT STUDIO",
          discover: "Discover Now",
          scrollDown: "Scroll Down",
        },
        et: {
          welcome: "Arendame teie ideed tegelikkuseks",
          studio: "DEVSKILLS ARENDUS STUDIO",
          discover: "Avasta kohe",
          scrollDown: "Keri alla",
        },
        fi: {
          welcome: "Muuttamme ideasi todellisuudeksi",
          studio: "DEVSKILLS KEHITYSSTUDIO",
          discover: "Tutustu nyt",
          scrollDown: "Vieritä alas",
        },
        de: {
          welcome: "Ihre Ideen in die Realität umsetzen",
          studio: "DEVSKILLS ENTWICKLUNGSSTUDIO",
          discover: "Jetzt entdecken",
          scrollDown: "Nach unten scrollen",
        },
        sv: {
          welcome: "Förvandlar dina idéer till verklighet",
          studio: "DEVSKILLS UTVECKLINGSSTUDIO",
          discover: "Upptäck nu",
          scrollDown: "Scrolla ner",
        },
      };

      // Critical GDPR texts for instant display (LCP optimization)
      window.gdprTexts = {
        en: {
          title: "We value your privacy",
          description:
            "We use cookies and similar technologies to enhance your browsing experience, analyze site traffic, and provide personalized content. By clicking 'Accept All', you consent to our use of cookies.",
          acceptAll: "Accept All",
          rejectAll: "Reject All",
          customize: "Customize",
        },
        et: {
          title: "Hindame teie privaatsust",
          description:
            "Kasutame küpsiseid ja sarnaseid tehnoloogiaid, et parandada teie sirvimiskogemust, analüüsida saidi liiklust ja pakkuda isikupärastatud sisu. Klõpsates 'Nõustu kõigiga', nõustute meie küpsiste kasutamisega.",
          acceptAll: "Nõustu kõigiga",
          rejectAll: "Keeldu kõigist",
          customize: "Kohanda",
        },
        fi: {
          title: "Arvostamme yksityisyyttäsi",
          description:
            "Käytämme evästeitä ja vastaavia teknologioita parantaaksemme selailukokemustasi, analysoidaksemme sivuston liikennettä ja tarjotaksemme henkilökohtaista sisältöä. Klikkaamalla 'Hyväksy kaikki' suostut evästeiden käyttöömme.",
          acceptAll: "Hyväksy kaikki",
          rejectAll: "Hylkää kaikki",
          customize: "Mukauta",
        },
        de: {
          title: "Wir schätzen Ihre Privatsphäre",
          description:
            "Wir verwenden Cookies und ähnliche Technologien, um Ihr Browsing-Erlebnis zu verbessern, Website-Traffic zu analysieren und personalisierte Inhalte bereitzustellen. Durch Klicken auf 'Alle akzeptieren' stimmen Sie unserer Verwendung von Cookies zu.",
          acceptAll: "Alle akzeptieren",
          rejectAll: "Alle ablehnen",
          customize: "Anpassen",
        },
        sv: {
          title: "Vi värdesätter din integritet",
          description:
            "Vi använder cookies och liknande teknologier för att förbättra din surfupplevelse, analysera webbplatstrafik och tillhandahålla personligt innehåll. Genom att klicka på 'Acceptera alla' samtycker du till vår användning av cookies.",
          acceptAll: "Acceptera alla",
          rejectAll: "Avvisa alla",
          customize: "Anpassa",
        },
      };

      // Immediate GDPR banner display for LCP optimization
      (function () {
        const consent = localStorage.getItem("gdpr-consent");
        if (!consent) {
          // Detect language
          const savedLang = localStorage.getItem("i18nextLng");
          const browserLang = navigator.language.split("-")[0];
          const detectedLang = savedLang || browserLang || "et";
          const lang = window.gdprTexts[detectedLang] ? detectedLang : "en";
          const texts = window.gdprTexts[lang];

          // Create and show GDPR banner immediately
          const banner = document.createElement("div");
          banner.className = "gdpr-banner gdpr-show gdpr-instant";
          banner.innerHTML = `
            <div class="container">
              <div class="row align-items-center">
                <div class="col-lg-8 col-md-7">
                  <div class="gdpr-content">
                    <h6 class="gdpr-title mb-2">${texts.title}</h6>
                    <p class="gdpr-description mb-0">${texts.description}</p>
                  </div>
                </div>
                <div class="col-lg-4 col-md-5">
                  <div class="gdpr-buttons">
                    <button class="btn btn-mod btn-small btn-w btn-circle gdpr-btn-accept" data-action="accept">
                      <span class="btn-animate-y">
                        <span class="btn-animate-y-1">${texts.acceptAll}</span>
                        <span class="btn-animate-y-2" aria-hidden="true">${texts.acceptAll}</span>
                      </span>
                    </button>
                    <button class="btn btn-mod btn-small btn-border-w btn-circle" data-action="reject">
                      <span class="btn-animate-y">
                        <span class="btn-animate-y-1">${texts.rejectAll}</span>
                        <span class="btn-animate-y-2" aria-hidden="true">${texts.rejectAll}</span>
                      </span>
                    </button>
                    <button class="btn btn-mod btn-small btn-border-w btn-circle" data-action="customize">
                      <span class="btn-animate-y">
                        <span class="btn-animate-y-1">${texts.customize}</span>
                        <span class="btn-animate-y-2" aria-hidden="true">${texts.customize}</span>
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Add to page
          document.body.appendChild(banner);

          // Add event listeners for immediate functionality
          banner.addEventListener("click", function (e) {
            const action = e.target.closest("[data-action]")?.dataset.action;
            if (!action) return;

            if (action === "accept") {
              const consent = {
                necessary: true,
                analytics: true,
                marketing: true,
                timestamp: new Date().toISOString(),
              };
              localStorage.setItem("gdpr-consent", JSON.stringify(consent));
              banner.remove();

              // Initialize Google Analytics
              if (typeof window !== "undefined" && window.gtag) {
                window.gtag("consent", "update", {
                  analytics_storage: "granted",
                  ad_storage: "granted",
                  ad_user_data: "granted",
                  ad_personalization: "granted",
                });
              }
            } else if (action === "reject") {
              const consent = {
                necessary: true,
                analytics: false,
                marketing: false,
                timestamp: new Date().toISOString(),
              };
              localStorage.setItem("gdpr-consent", JSON.stringify(consent));
              banner.remove();

              // Initialize Google Analytics with denied consent
              if (typeof window !== "undefined" && window.gtag) {
                window.gtag("consent", "update", {
                  analytics_storage: "denied",
                  ad_storage: "denied",
                  ad_user_data: "denied",
                  ad_personalization: "denied",
                });
              }
            } else if (action === "customize") {
              // Mark for React to handle detailed modal
              document.body.classList.add("gdpr-customize-requested");
              banner.remove();
            }
          });

          // Mark that instant banner is shown
          document.body.classList.add("gdpr-instant-shown");
        }
      })();

      // Immediate hero section display for LCP optimization
      (function () {
        // Reset state on fresh page load
        const isPageRefresh =
          performance.navigation && performance.navigation.type === 1;
        const isPageReload =
          performance.getEntriesByType("navigation")[0]?.type === "reload";

        if (isPageRefresh || isPageReload) {
          // Clear previous state on refresh
          document.body.classList.remove(
            "hero-instant-shown",
            "hero-instant-loading"
          );
          delete window.instantHeroTexts;
        }

        // Only skip if we've already processed this page load
        if (document.body.classList.contains("hero-instant-shown")) {
          return;
        }

        // Mark that we're about to show instant hero
        document.body.classList.add("hero-instant-loading");

        // Detect language
        const savedLang = localStorage.getItem("i18nextLng");
        const browserLang = navigator.language.split("-")[0];
        const detectedLang = savedLang || browserLang || "et";
        const lang = window.heroTexts[detectedLang] ? detectedLang : "en";
        const texts = window.heroTexts[lang];

        // Store texts globally for React to use
        window.instantHeroTexts = texts;

        // Make instant hero function available globally for React Router navigation
        window.showInstantHero = function (forceShow = false) {
          const heroContainer = document.querySelector("#home .home-content");
          if (!heroContainer) return false;

          // Check if we should show instant hero
          if (
            !forceShow &&
            (heroContainer.children.length > 0 ||
              document.body.classList.contains("hero-instant-shown"))
          ) {
            return false;
          }

          // Clear existing content
          heroContainer.innerHTML = "";

          // Create instant hero content
          heroContainer.innerHTML = `
            <h2 class="section-title-tiny mb-50 mb-sm-30 wow fadeInDownShort hero-instant-welcome">
              ${texts.welcome}
            </h2>
            <h1 class="hs-title-3 mb-120 mb-sm-80 mb-xs-140">
              <span class="wow charsAnimInLong hero-instant-studio" data-splitting="chars">
                ${texts.studio}
              </span>
            </h1>
            <div class="local-scroll wow fadeInUpShort" data-wow-delay="0.57s">
              <a
                href="#about"
                class="link-hover-anim link-circle-1 align-middle"
                data-link-animate="y"
              >
                <span class="link-strong link-strong-unhovered">
                  ${texts.discover}
                  <i class="mi-arrow-right size-18 align-middle" aria-hidden="true"></i>
                </span>
                <span class="link-strong link-strong-hovered" aria-hidden="true">
                  ${texts.discover}
                  <i class="mi-arrow-right size-18 align-middle" aria-hidden="true"></i>
                </span>
              </a>
            </div>
          `;

          // Update scroll down text
          const scrollDown = document.querySelector(".scroll-down-3");
          if (scrollDown) {
            scrollDown.textContent = texts.scrollDown;
          }

          // Mark that instant hero is shown
          document.body.classList.add("hero-instant-shown");

          // Trigger animations
          setTimeout(() => {
            const welcomeEl = heroContainer.querySelector(
              ".hero-instant-welcome"
            );
            const studioEl = heroContainer.querySelector(
              ".hero-instant-studio"
            );
            const buttonEl = heroContainer.querySelector(".local-scroll");

            if (welcomeEl) {
              welcomeEl.classList.add("animated", "fadeInDownShort");
              welcomeEl.style.opacity = "1";
              welcomeEl.style.visibility = "visible";
              welcomeEl.style.animationDelay = "0s";
              welcomeEl.style.animationDuration = "0.6s";
            }

            if (studioEl) {
              studioEl.classList.add("animated", "charsAnimInLong");
              studioEl.style.opacity = "1";
              studioEl.style.visibility = "visible";
              studioEl.style.animationDelay = "0.2s";
              studioEl.style.animationDuration = "0.8s";
            }

            if (buttonEl) {
              buttonEl.classList.add("animated", "fadeInUpShort");
              buttonEl.style.opacity = "1";
              buttonEl.style.visibility = "visible";
              buttonEl.style.animationDelay = "0.4s";
              buttonEl.style.animationDuration = "0.6s";
            }
          }, 100);

          return true;
        };

        // Wait for DOM to be ready
        function initHeroSection() {
          const heroContainer = document.querySelector("#home .home-content");
          if (!heroContainer) {
            // Hero container not ready yet, try again
            setTimeout(initHeroSection, 10);
            return;
          }

          // Check if container already has content (React hydration case)
          if (heroContainer.children.length > 0) {
            // React is handling this, just mark as shown and exit
            document.body.classList.add("hero-instant-shown");
            return;
          }

          // Create instant hero content only if container is empty
          heroContainer.innerHTML = `
            <h2 class="section-title-tiny mb-50 mb-sm-30 wow fadeInDownShort hero-instant-welcome">
              ${texts.welcome}
            </h2>
            <h1 class="hs-title-3 mb-120 mb-sm-80 mb-xs-140">
              <span class="wow charsAnimInLong hero-instant-studio" data-splitting="chars">
                ${texts.studio}
              </span>
            </h1>
            <div class="local-scroll wow fadeInUpShort" data-wow-delay="0.57s">
              <a
                href="#about"
                class="link-hover-anim link-circle-1 align-middle"
                data-link-animate="y"
              >
                <span class="link-strong link-strong-unhovered">
                  ${texts.discover}
                  <i class="mi-arrow-right size-18 align-middle" aria-hidden="true"></i>
                </span>
                <span class="link-strong link-strong-hovered" aria-hidden="true">
                  ${texts.discover}
                  <i class="mi-arrow-right size-18 align-middle" aria-hidden="true"></i>
                </span>
              </a>
            </div>
          `;

          // Update scroll down text
          const scrollDown = document.querySelector(".scroll-down-3");
          if (scrollDown) {
            scrollDown.textContent = texts.scrollDown;
          }

          // Mark that instant hero is shown
          document.body.classList.add("hero-instant-shown");

          // Trigger animations immediately
          setTimeout(() => {
            const welcomeEl = heroContainer.querySelector(
              ".hero-instant-welcome"
            );
            const studioEl = heroContainer.querySelector(
              ".hero-instant-studio"
            );

            if (welcomeEl) {
              welcomeEl.classList.add("animated", "fadeInDownShort");
              welcomeEl.style.opacity = "1";
              welcomeEl.style.visibility = "visible";
              welcomeEl.style.animationDelay = "0s";
              welcomeEl.style.animationDuration = "0.6s";
            }

            if (studioEl) {
              studioEl.classList.add("animated", "charsAnimInLong");
              studioEl.style.opacity = "1";
              studioEl.style.visibility = "visible";
              studioEl.style.animationDelay = "0.2s";
              studioEl.style.animationDuration = "0.8s";
            }

            // Trigger button animation
            const buttonEl = heroContainer.querySelector(".local-scroll");
            if (buttonEl) {
              buttonEl.classList.add("animated", "fadeInUpShort");
              buttonEl.style.opacity = "1";
              buttonEl.style.visibility = "visible";
              buttonEl.style.animationDelay = "0.4s";
              buttonEl.style.animationDuration = "0.6s";
            }
          }, 100);
        }

        // Only run on true first load, not on React hydration
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", initHeroSection);
        } else {
          // Small delay to let React initialize first
          setTimeout(initHeroSection, 50);
        }
      })();
    </script>

    <script src="/scripts/simple-slider.js"></script>

    <!-- LinkedIn Insight Tag -->
    <script type="text/javascript">
      _linkedin_partner_id = "8530641";
      window._linkedin_data_partner_ids =
        window._linkedin_data_partner_ids || [];
      window._linkedin_data_partner_ids.push(_linkedin_partner_id);
    </script>
    <script type="text/javascript">
      (function (l) {
        if (!l) {
          window.lintrk = function (a, b) {
            window.lintrk.q.push([a, b]);
          };
          window.lintrk.q = [];
        }
        var s = document.getElementsByTagName("script")[0];
        var b = document.createElement("script");
        b.type = "text/javascript";
        b.async = true;
        b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
        s.parentNode.insertBefore(b, s);
      })(window.lintrk);
    </script>
    <noscript>
      <img
        height="1"
        width="1"
        style="display: none"
        alt=""
        src="https://px.ads.linkedin.com/collect/?pid=8530641&fmt=gif"
      />
    </noscript>
  </body>
</html>
