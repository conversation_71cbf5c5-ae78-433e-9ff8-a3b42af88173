#!/bin/bash

# Script to fix production upload directory permissions
# Run this on the production server if needed

echo "🔧 Fixing production upload directory permissions..."

# Check if uploads directory exists
if [ -d "uploads" ]; then
    echo "📁 Found uploads directory"
    
    # Fix ownership and permissions
    sudo chown -R $USER:$USER uploads/
    chmod -R 755 uploads/
    
    echo "✅ Fixed permissions for uploads directory"
else
    echo "📁 Creating uploads directory structure..."
    mkdir -p uploads/blog-images
    mkdir -p uploads/temp
    chmod -R 755 uploads/
    echo "✅ Created uploads directory with proper permissions"
fi

echo "📋 Current uploads directory structure:"
ls -la uploads/ 2>/dev/null || echo "No uploads directory found"

echo ""
echo "✅ Production permissions fix complete!"
echo "ℹ️  You can now run git pull without permission issues."
