import express from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import rateLimit from "express-rate-limit";
import dotenv from "dotenv";
import path from "path";

// Import routes
import authRoutes from "./routes/auth";
import blogRoutes from "./routes/blog";
import productRoutes from "./routes/products";
import categoriesRoutes from "./routes/categories";
import tagsRoutes from "./routes/tags";
import contactRoutes from "./routes/contact";
import adminRoutes from "./routes/admin";
import sitemapRoutes from "./routes/sitemap";
import analyticsRoutes from "./routes/analytics";

// Import middleware
import { errorHandler } from "./middleware/errorHandler";
import { notFound } from "./middleware/notFound";
import { trackPageView } from "./middleware/analytics";

// Load environment variables
dotenv.config();

// Initialize Redis connection
import "./config/redis";

const app = express();
const PORT = process.env.PORT || 5000;

// Trust proxy for rate limiting behind nginx
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  })
);

// CORS configuration
const getAllowedOrigins = () => {
  const isProduction = process.env.NODE_ENV === "production";

  if (isProduction) {
    return [
      "https://devskills.ee",
      "https://www.devskills.ee",
      // Add any other production domains if needed
    ];
  } else {
    return [
      "http://localhost:3000", // React dev server
      "http://localhost:5173", // Vite dev server
      "http://localhost:5174", // Vite dev server (alternative port)
      "http://127.0.0.1:3000", // Alternative localhost
      "http://127.0.0.1:5173", // Alternative localhost
      "http://127.0.0.1:5174", // Alternative localhost
    ];
  }
};

const corsOptions = {
  origin: (
    origin: string | undefined,
    callback: (err: Error | null, allow?: boolean) => void
  ) => {
    const allowedOrigins = getAllowedOrigins();

    // Allow requests with no origin (like mobile apps or curl requests) in development
    if (!origin && process.env.NODE_ENV !== "production") {
      return callback(null, true);
    }

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Origin",
    "X-Requested-With",
    "Content-Type",
    "Accept",
    "Authorization",
    "X-API-Key",
  ],
};

app.use(cors(corsOptions));

// Rate limiting - disabled in development
if (process.env.NODE_ENV === "production") {
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"),
    message: {
      error: "Too many requests from this IP, please try again later.",
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use(limiter);
  console.log("🛡️  Rate limiting enabled for production");
} else {
  console.log("🚀 Rate limiting disabled for development");
}

// Body parsing middleware
app.use(compression());

// Conditional JSON parsing - only parse JSON content types
app.use((req, res, next) => {
  if (req.is("application/json")) {
    express.json({ limit: "10mb" })(req, res, next);
  } else {
    next();
  }
});

// URL-encoded parsing for form submissions (not multipart)
app.use((req, res, next) => {
  if (req.is("application/x-www-form-urlencoded")) {
    express.urlencoded({ extended: true, limit: "10mb" })(req, res, next);
  } else {
    next();
  }
});

// Logging
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
} else {
  app.use(morgan("combined"));
}

// Static files - serve from uploads directory
const uploadsPath =
  process.env.NODE_ENV === "production"
    ? "/app/uploads"
    : path.join(process.cwd(), "uploads");

console.log(`📁 Serving static files from: ${uploadsPath}`);
app.use("/uploads", express.static(uploadsPath));

// Analytics tracking middleware (before routes)
app.use(trackPageView);

// Health check
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Debug endpoint to check uploads directory (remove in production later)
app.get("/debug/uploads", (req, res) => {
  try {
    const fs = require("fs");
    const uploadsPath =
      process.env.NODE_ENV === "production"
        ? "/app/uploads"
        : path.join(process.cwd(), "uploads");

    const blogImagesPath = path.join(uploadsPath, "blog-images");

    const uploadsExists = fs.existsSync(uploadsPath);
    const blogImagesExists = fs.existsSync(blogImagesPath);

    let files = [];
    if (blogImagesExists) {
      files = fs.readdirSync(blogImagesPath);
    }

    res.json({
      uploadsPath,
      blogImagesPath,
      uploadsExists,
      blogImagesExists,
      files: files.slice(0, 10), // Show first 10 files
      totalFiles: files.length,
    });
  } catch (error) {
    res.status(500).json({
      error: error.message,
      uploadsPath:
        process.env.NODE_ENV === "production"
          ? "/app/uploads"
          : path.join(process.cwd(), "uploads"),
    });
  }
});

// API Routes
app.use("/api/auth", authRoutes);
app.use("/api/blog", blogRoutes);
app.use("/api/products", productRoutes);
app.use("/api/categories", categoriesRoutes);
app.use("/api/tags", tagsRoutes);
app.use("/api/contact", contactRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/analytics", analyticsRoutes);

// Sitemap Routes (no /api prefix for SEO)
app.use("/", sitemapRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📝 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 CORS Origin: ${process.env.CORS_ORIGIN}`);
});

export default app;
