/* Self-hosted fonts for DevSkills - Optimized for performance */

/* D<PERSON> Sans - Used as fallback and admin font */
@font-face {
    font-family: 'DM Sans';
    src: url('/assets/webfonts/dm-sans/DMSans-Regular.woff2') format('woff2'),
         url('/assets/webfonts/dm-sans/DMSans-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: url('/assets/webfonts/dm-sans/DMSans-Medium.woff2') format('woff2'),
         url('/assets/webfonts/dm-sans/DMSans-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: url('/assets/webfonts/dm-sans/DMSans-Bold.woff2') format('woff2'),
         url('/assets/webfonts/dm-sans/DMSans-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Poppins - Main theme font for theme-elegant */
@font-face {
    font-family: 'Poppins';
    src: url('/assets/webfonts/poppins/Poppins-Regular.woff2') format('woff2'),
         url('/assets/webfonts/poppins/Poppins-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('/assets/webfonts/poppins/Poppins-Medium.woff2') format('woff2'),
         url('/assets/webfonts/poppins/Poppins-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('/assets/webfonts/poppins/Poppins-SemiBold.woff2') format('woff2'),
         url('/assets/webfonts/poppins/Poppins-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('/assets/webfonts/poppins/Poppins-Bold.woff2') format('woff2'),
         url('/assets/webfonts/poppins/Poppins-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}
