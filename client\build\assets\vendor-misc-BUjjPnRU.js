import{g as _e,c as je}from"./vendor-animations-Dl3DQHMd.js";function Cr(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const i=Object.getOwnPropertyDescriptor(n,a);i&&Object.defineProperty(e,a,i.get?i:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var zt={exports:{}},Xt={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(u,b){var w=u.length;u.push(b);e:for(;0<w;){var F=w-1>>>1,I=u[F];if(0<a(I,b))u[F]=b,u[w]=I,w=F;else break e}}function r(u){return u.length===0?null:u[0]}function n(u){if(u.length===0)return null;var b=u[0],w=u.pop();if(w!==b){u[0]=w;e:for(var F=0,I=u.length,j=I>>>1;F<j;){var C=2*(F+1)-1,$=u[C],D=C+1,B=u[D];if(0>a($,w))D<I&&0>a(B,$)?(u[F]=B,u[D]=w,F=D):(u[F]=$,u[C]=w,F=C);else if(D<I&&0>a(B,w))u[F]=B,u[D]=w,F=D;else break e}}return b}function a(u,b){var w=u.sortIndex-b.sortIndex;return w!==0?w:u.id-b.id}if(e.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var o=[],v=[],m=1,T=null,O=3,R=!1,k=!1,y=!1,E=typeof setTimeout=="function"?setTimeout:null,x=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;function p(u){for(var b=r(v);b!==null;){if(b.callback===null)n(v);else if(b.startTime<=u)n(v),b.sortIndex=b.expirationTime,t(o,b);else break;b=r(v)}}function d(u){if(y=!1,p(u),!k)if(r(o)!==null)k=!0,U();else{var b=r(v);b!==null&&c(d,b.startTime-u)}}var h=!1,g=-1,S=5,P=-1;function A(){return!(e.unstable_now()-P<S)}function _(){if(h){var u=e.unstable_now();P=u;var b=!0;try{e:{k=!1,y&&(y=!1,x(g),g=-1),R=!0;var w=O;try{t:{for(p(u),T=r(o);T!==null&&!(T.expirationTime>u&&A());){var F=T.callback;if(typeof F=="function"){T.callback=null,O=T.priorityLevel;var I=F(T.expirationTime<=u);if(u=e.unstable_now(),typeof I=="function"){T.callback=I,p(u),b=!0;break t}T===r(o)&&n(o),p(u)}else n(o);T=r(o)}if(T!==null)b=!0;else{var j=r(v);j!==null&&c(d,j.startTime-u),b=!1}}break e}finally{T=null,O=w,R=!1}b=void 0}}finally{b?L():h=!1}}}var L;if(typeof f=="function")L=function(){f(_)};else if(typeof MessageChannel<"u"){var N=new MessageChannel,M=N.port2;N.port1.onmessage=_,L=function(){M.postMessage(null)}}else L=function(){E(_,0)};function U(){h||(h=!0,L())}function c(u,b){g=E(function(){u(e.unstable_now())},b)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(u){u.callback=null},e.unstable_continueExecution=function(){k||R||(k=!0,U())},e.unstable_forceFrameRate=function(u){0>u||125<u?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):S=0<u?Math.floor(1e3/u):5},e.unstable_getCurrentPriorityLevel=function(){return O},e.unstable_getFirstCallbackNode=function(){return r(o)},e.unstable_next=function(u){switch(O){case 1:case 2:case 3:var b=3;break;default:b=O}var w=O;O=b;try{return u()}finally{O=w}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(u,b){switch(u){case 1:case 2:case 3:case 4:case 5:break;default:u=3}var w=O;O=u;try{return b()}finally{O=w}},e.unstable_scheduleCallback=function(u,b,w){var F=e.unstable_now();switch(typeof w=="object"&&w!==null?(w=w.delay,w=typeof w=="number"&&0<w?F+w:F):w=F,u){case 1:var I=-1;break;case 2:I=250;break;case 5:I=1073741823;break;case 4:I=1e4;break;default:I=5e3}return I=w+I,u={id:m++,callback:b,priorityLevel:u,startTime:w,expirationTime:I,sortIndex:-1},w>F?(u.sortIndex=w,t(v,u),r(o)===null&&u===r(v)&&(y?(x(g),g=-1):y=!0,c(d,w-F))):(u.sortIndex=I,t(o,u),k||R||(k=!0,U())),u},e.unstable_shouldYield=A,e.unstable_wrapCallback=function(u){var b=O;return function(){var w=O;O=b;try{return u.apply(this,arguments)}finally{O=w}}}})(Xt);zt.exports=Xt;var ka=zt.exports,Vt={exports:{}},$r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Mr=$r,Br=Mr;function qt(){}function Zt(){}Zt.resetWarningCache=qt;var Ur=function(){function e(n,a,i,l,s,o){if(o!==Br){var v=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw v.name="Invariant Violation",v}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Zt,resetWarningCache:qt};return r.PropTypes=r,r};Vt.exports=Ur();var jr=Vt.exports;const La=_e(jr);/**
 * @remix-run/router v1.22.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Pe(){return Pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pe.apply(this,arguments)}var be;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(be||(be={}));const Pt="popstate";function Na(e){e===void 0&&(e={});function t(n,a){let{pathname:i,search:l,hash:s}=n.location;return et("",{pathname:i,search:l,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Qt(a)}return Gr(t,r,null,e)}function ye(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Kt(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Hr(){return Math.random().toString(36).substr(2,8)}function _t(e,t){return{usr:e.state,key:e.key,idx:t}}function et(e,t,r,n){return r===void 0&&(r=null),Pe({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?We(t):t,{state:r,key:t&&t.key||n||Hr()})}function Qt(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function We(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Gr(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:i=!1}=n,l=a.history,s=be.Pop,o=null,v=m();v==null&&(v=0,l.replaceState(Pe({},l.state,{idx:v}),""));function m(){return(l.state||{idx:null}).idx}function T(){s=be.Pop;let E=m(),x=E==null?null:E-v;v=E,o&&o({action:s,location:y.location,delta:x})}function O(E,x){s=be.Push;let f=et(y.location,E,x);v=m()+1;let p=_t(f,v),d=y.createHref(f);try{l.pushState(p,"",d)}catch(h){if(h instanceof DOMException&&h.name==="DataCloneError")throw h;a.location.assign(d)}i&&o&&o({action:s,location:y.location,delta:1})}function R(E,x){s=be.Replace;let f=et(y.location,E,x);v=m();let p=_t(f,v),d=y.createHref(f);l.replaceState(p,"",d),i&&o&&o({action:s,location:y.location,delta:0})}function k(E){let x=a.location.origin!=="null"?a.location.origin:a.location.href,f=typeof E=="string"?E:Qt(E);return f=f.replace(/ $/,"%20"),ye(x,"No window.location.(origin|href) available to create URL for href: "+f),new URL(f,x)}let y={get action(){return s},get location(){return e(a,l)},listen(E){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(Pt,T),o=E,()=>{a.removeEventListener(Pt,T),o=null}},createHref(E){return t(a,E)},createURL:k,encodeLocation(E){let x=k(E);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:O,replace:R,go(E){return l.go(E)}};return y}var kt;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(kt||(kt={}));function Da(e,t,r){return r===void 0&&(r="/"),Yr(e,t,r)}function Yr(e,t,r,n){let a=typeof t=="string"?We(t):t,i=an(a.pathname||"/",r);if(i==null)return null;let l=Jt(e);Wr(l);let s=null;for(let o=0;s==null&&o<l.length;++o){let v=nn(i);s=en(l[o],v)}return s}function Jt(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(i,l,s)=>{let o={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};o.relativePath.startsWith("/")&&(ye(o.relativePath.startsWith(n),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(n.length));let v=Be([n,o.relativePath]),m=r.concat(o);i.children&&i.children.length>0&&(ye(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+v+'".')),Jt(i.children,t,m,v)),!(i.path==null&&!i.index)&&t.push({path:v,score:Qr(v,i.index),routesMeta:m})};return e.forEach((i,l)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))a(i,l);else for(let o of er(i.path))a(i,l,o)}),t}function er(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),i=r.replace(/\?$/,"");if(n.length===0)return a?[i,""]:[i];let l=er(n.join("/")),s=[];return s.push(...l.map(o=>o===""?i:[i,o].join("/"))),a&&s.push(...l),s.map(o=>e.startsWith("/")&&o===""?"/":o)}function Wr(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Jr(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const zr=/^:[\w-]+$/,Xr=3,Vr=2,qr=1,Zr=10,Kr=-2,Lt=e=>e==="*";function Qr(e,t){let r=e.split("/"),n=r.length;return r.some(Lt)&&(n+=Kr),t&&(n+=Vr),r.filter(a=>!Lt(a)).reduce((a,i)=>a+(zr.test(i)?Xr:i===""?qr:Zr),n)}function Jr(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function en(e,t,r){let{routesMeta:n}=e,a={},i="/",l=[];for(let s=0;s<n.length;++s){let o=n[s],v=s===n.length-1,m=i==="/"?t:t.slice(i.length)||"/",T=tn({path:o.relativePath,caseSensitive:o.caseSensitive,end:v},m),O=o.route;if(!T)return null;Object.assign(a,T.params),l.push({params:a,pathname:Be([i,T.pathname]),pathnameBase:un(Be([i,T.pathnameBase])),route:O}),T.pathnameBase!=="/"&&(i=Be([i,T.pathnameBase]))}return l}function tn(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=rn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let i=a[0],l=i.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:n.reduce((v,m,T)=>{let{paramName:O,isOptional:R}=m;if(O==="*"){let y=s[T]||"";l=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const k=s[T];return R&&!k?v[O]=void 0:v[O]=(k||"").replace(/%2F/g,"/"),v},{}),pathname:i,pathnameBase:l,pattern:e}}function rn(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Kt(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,o)=>(n.push({paramName:s,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function nn(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Kt(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function an(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function sn(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?We(e):e;return{pathname:r?r.startsWith("/")?r:on(r,t):t,search:cn(n),hash:fn(a)}}function on(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Ke(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ln(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Ca(e,t){let r=ln(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function $a(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=We(e):(a=Pe({},e),ye(!a.pathname||!a.pathname.includes("?"),Ke("?","pathname","search",a)),ye(!a.pathname||!a.pathname.includes("#"),Ke("#","pathname","hash",a)),ye(!a.search||!a.search.includes("#"),Ke("#","search","hash",a)));let i=e===""||a.pathname==="",l=i?"/":a.pathname,s;if(l==null)s=r;else{let T=t.length-1;if(!n&&l.startsWith("..")){let O=l.split("/");for(;O[0]==="..";)O.shift(),T-=1;a.pathname=O.join("/")}s=T>=0?t[T]:"/"}let o=sn(a,s),v=l&&l!=="/"&&l.endsWith("/"),m=(i||l===".")&&r.endsWith("/");return!o.pathname.endsWith("/")&&(v||m)&&(o.pathname+="/"),o}const Be=e=>e.join("/").replace(/\/\/+/g,"/"),un=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),cn=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,fn=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ma(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const tr=["post","put","patch","delete"];new Set(tr);const pn=["get",...tr];new Set(pn);var dn=function(e,t,r,n,a,i,l,s){if(!e){var o;if(t===void 0)o=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var v=[r,n,a,i,l,s],m=0;o=new Error(t.replace(/%s/g,function(){return v[m++]})),o.name="Invariant Violation"}throw o.framesToPop=1,o}},hn=dn;const Ba=_e(hn);var gn=function(t,r,n,a){var i=n?n.call(a,t,r):void 0;if(i!==void 0)return!!i;if(t===r)return!0;if(typeof t!="object"||!t||typeof r!="object"||!r)return!1;var l=Object.keys(t),s=Object.keys(r);if(l.length!==s.length)return!1;for(var o=Object.prototype.hasOwnProperty.bind(r),v=0;v<l.length;v++){var m=l[v];if(!o(m))return!1;var T=t[m],O=r[m];if(i=n?n.call(a,T,O,m):void 0,i===!1||i===void 0&&T!==O)return!1}return!0};const Ua=_e(gn);var tt={exports:{}};(function(e,t){var r=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof je<"u"&&je,n=function(){function i(){this.fetch=!1,this.DOMException=r.DOMException}return i.prototype=r,new i}();(function(i){(function(l){var s=typeof i<"u"&&i||typeof self<"u"&&self||typeof s<"u"&&s,o={searchParams:"URLSearchParams"in s,iterable:"Symbol"in s&&"iterator"in Symbol,blob:"FileReader"in s&&"Blob"in s&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in s,arrayBuffer:"ArrayBuffer"in s};function v(c){return c&&DataView.prototype.isPrototypeOf(c)}if(o.arrayBuffer)var m=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],T=ArrayBuffer.isView||function(c){return c&&m.indexOf(Object.prototype.toString.call(c))>-1};function O(c){if(typeof c!="string"&&(c=String(c)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(c)||c==="")throw new TypeError('Invalid character in header field name: "'+c+'"');return c.toLowerCase()}function R(c){return typeof c!="string"&&(c=String(c)),c}function k(c){var u={next:function(){var b=c.shift();return{done:b===void 0,value:b}}};return o.iterable&&(u[Symbol.iterator]=function(){return u}),u}function y(c){this.map={},c instanceof y?c.forEach(function(u,b){this.append(b,u)},this):Array.isArray(c)?c.forEach(function(u){this.append(u[0],u[1])},this):c&&Object.getOwnPropertyNames(c).forEach(function(u){this.append(u,c[u])},this)}y.prototype.append=function(c,u){c=O(c),u=R(u);var b=this.map[c];this.map[c]=b?b+", "+u:u},y.prototype.delete=function(c){delete this.map[O(c)]},y.prototype.get=function(c){return c=O(c),this.has(c)?this.map[c]:null},y.prototype.has=function(c){return this.map.hasOwnProperty(O(c))},y.prototype.set=function(c,u){this.map[O(c)]=R(u)},y.prototype.forEach=function(c,u){for(var b in this.map)this.map.hasOwnProperty(b)&&c.call(u,this.map[b],b,this)},y.prototype.keys=function(){var c=[];return this.forEach(function(u,b){c.push(b)}),k(c)},y.prototype.values=function(){var c=[];return this.forEach(function(u){c.push(u)}),k(c)},y.prototype.entries=function(){var c=[];return this.forEach(function(u,b){c.push([b,u])}),k(c)},o.iterable&&(y.prototype[Symbol.iterator]=y.prototype.entries);function E(c){if(c.bodyUsed)return Promise.reject(new TypeError("Already read"));c.bodyUsed=!0}function x(c){return new Promise(function(u,b){c.onload=function(){u(c.result)},c.onerror=function(){b(c.error)}})}function f(c){var u=new FileReader,b=x(u);return u.readAsArrayBuffer(c),b}function p(c){var u=new FileReader,b=x(u);return u.readAsText(c),b}function d(c){for(var u=new Uint8Array(c),b=new Array(u.length),w=0;w<u.length;w++)b[w]=String.fromCharCode(u[w]);return b.join("")}function h(c){if(c.slice)return c.slice(0);var u=new Uint8Array(c.byteLength);return u.set(new Uint8Array(c)),u.buffer}function g(){return this.bodyUsed=!1,this._initBody=function(c){this.bodyUsed=this.bodyUsed,this._bodyInit=c,c?typeof c=="string"?this._bodyText=c:o.blob&&Blob.prototype.isPrototypeOf(c)?this._bodyBlob=c:o.formData&&FormData.prototype.isPrototypeOf(c)?this._bodyFormData=c:o.searchParams&&URLSearchParams.prototype.isPrototypeOf(c)?this._bodyText=c.toString():o.arrayBuffer&&o.blob&&v(c)?(this._bodyArrayBuffer=h(c.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):o.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(c)||T(c))?this._bodyArrayBuffer=h(c):this._bodyText=c=Object.prototype.toString.call(c):this._bodyText="",this.headers.get("content-type")||(typeof c=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):o.searchParams&&URLSearchParams.prototype.isPrototypeOf(c)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},o.blob&&(this.blob=function(){var c=E(this);if(c)return c;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var c=E(this);return c||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else return this.blob().then(f)}),this.text=function(){var c=E(this);if(c)return c;if(this._bodyBlob)return p(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(d(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},o.formData&&(this.formData=function(){return this.text().then(_)}),this.json=function(){return this.text().then(JSON.parse)},this}var S=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function P(c){var u=c.toUpperCase();return S.indexOf(u)>-1?u:c}function A(c,u){if(!(this instanceof A))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');u=u||{};var b=u.body;if(c instanceof A){if(c.bodyUsed)throw new TypeError("Already read");this.url=c.url,this.credentials=c.credentials,u.headers||(this.headers=new y(c.headers)),this.method=c.method,this.mode=c.mode,this.signal=c.signal,!b&&c._bodyInit!=null&&(b=c._bodyInit,c.bodyUsed=!0)}else this.url=String(c);if(this.credentials=u.credentials||this.credentials||"same-origin",(u.headers||!this.headers)&&(this.headers=new y(u.headers)),this.method=P(u.method||this.method||"GET"),this.mode=u.mode||this.mode||null,this.signal=u.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&b)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(b),(this.method==="GET"||this.method==="HEAD")&&(u.cache==="no-store"||u.cache==="no-cache")){var w=/([?&])_=[^&]*/;if(w.test(this.url))this.url=this.url.replace(w,"$1_="+new Date().getTime());else{var F=/\?/;this.url+=(F.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}A.prototype.clone=function(){return new A(this,{body:this._bodyInit})};function _(c){var u=new FormData;return c.trim().split("&").forEach(function(b){if(b){var w=b.split("="),F=w.shift().replace(/\+/g," "),I=w.join("=").replace(/\+/g," ");u.append(decodeURIComponent(F),decodeURIComponent(I))}}),u}function L(c){var u=new y,b=c.replace(/\r?\n[\t ]+/g," ");return b.split("\r").map(function(w){return w.indexOf(`
`)===0?w.substr(1,w.length):w}).forEach(function(w){var F=w.split(":"),I=F.shift().trim();if(I){var j=F.join(":").trim();u.append(I,j)}}),u}g.call(A.prototype);function N(c,u){if(!(this instanceof N))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');u||(u={}),this.type="default",this.status=u.status===void 0?200:u.status,this.ok=this.status>=200&&this.status<300,this.statusText=u.statusText===void 0?"":""+u.statusText,this.headers=new y(u.headers),this.url=u.url||"",this._initBody(c)}g.call(N.prototype),N.prototype.clone=function(){return new N(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new y(this.headers),url:this.url})},N.error=function(){var c=new N(null,{status:0,statusText:""});return c.type="error",c};var M=[301,302,303,307,308];N.redirect=function(c,u){if(M.indexOf(u)===-1)throw new RangeError("Invalid status code");return new N(null,{status:u,headers:{location:c}})},l.DOMException=s.DOMException;try{new l.DOMException}catch{l.DOMException=function(u,b){this.message=u,this.name=b;var w=Error(u);this.stack=w.stack},l.DOMException.prototype=Object.create(Error.prototype),l.DOMException.prototype.constructor=l.DOMException}function U(c,u){return new Promise(function(b,w){var F=new A(c,u);if(F.signal&&F.signal.aborted)return w(new l.DOMException("Aborted","AbortError"));var I=new XMLHttpRequest;function j(){I.abort()}I.onload=function(){var $={status:I.status,statusText:I.statusText,headers:L(I.getAllResponseHeaders()||"")};$.url="responseURL"in I?I.responseURL:$.headers.get("X-Request-URL");var D="response"in I?I.response:I.responseText;setTimeout(function(){b(new N(D,$))},0)},I.onerror=function(){setTimeout(function(){w(new TypeError("Network request failed"))},0)},I.ontimeout=function(){setTimeout(function(){w(new TypeError("Network request failed"))},0)},I.onabort=function(){setTimeout(function(){w(new l.DOMException("Aborted","AbortError"))},0)};function C($){try{return $===""&&s.location.href?s.location.href:$}catch{return $}}I.open(F.method,C(F.url),!0),F.credentials==="include"?I.withCredentials=!0:F.credentials==="omit"&&(I.withCredentials=!1),"responseType"in I&&(o.blob?I.responseType="blob":o.arrayBuffer&&F.headers.get("Content-Type")&&F.headers.get("Content-Type").indexOf("application/octet-stream")!==-1&&(I.responseType="arraybuffer")),u&&typeof u.headers=="object"&&!(u.headers instanceof y)?Object.getOwnPropertyNames(u.headers).forEach(function($){I.setRequestHeader($,R(u.headers[$]))}):F.headers.forEach(function($,D){I.setRequestHeader(D,$)}),F.signal&&(F.signal.addEventListener("abort",j),I.onreadystatechange=function(){I.readyState===4&&F.signal.removeEventListener("abort",j)}),I.send(typeof F._bodyInit>"u"?null:F._bodyInit)})}return U.polyfill=!0,s.fetch||(s.fetch=U,s.Headers=y,s.Request=A,s.Response=N),l.Headers=y,l.Request=A,l.Response=N,l.fetch=U,l})({})})(n),n.fetch.ponyfill=!0,delete n.fetch.polyfill;var a=r.fetch?r:n;t=a.fetch,t.default=a.fetch,t.fetch=a.fetch,t.Headers=a.Headers,t.Request=a.Request,t.Response=a.Response,e.exports=t})(tt,tt.exports);var rr=tt.exports;const vn=_e(rr),ja=Cr({__proto__:null,default:vn},[rr]);function Y(e){this.content=e}Y.prototype={constructor:Y,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return-1},get:function(e){var t=this.find(e);return t==-1?void 0:this.content[t+1]},update:function(e,t,r){var n=r&&r!=e?this.remove(r):this,a=n.find(e),i=n.content.slice();return a==-1?i.push(r||e,t):(i[a+1]=t,r&&(i[a]=r)),new Y(i)},remove:function(e){var t=this.find(e);if(t==-1)return this;var r=this.content.slice();return r.splice(t,2),new Y(r)},addToStart:function(e,t){return new Y([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var r=this.remove(e).content.slice();return r.push(e,t),new Y(r)},addBefore:function(e,t,r){var n=this.remove(t),a=n.content.slice(),i=n.find(e);return a.splice(i==-1?a.length:i,0,t,r),new Y(a)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return e=Y.from(e),e.size?new Y(e.content.concat(this.subtract(e).content)):this},append:function(e){return e=Y.from(e),e.size?new Y(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=Y.from(e);for(var r=0;r<e.content.length;r+=2)t=t.remove(e.content[r]);return t},toObject:function(){var e={};return this.forEach(function(t,r){e[t]=r}),e},get size(){return this.content.length>>1}};Y.from=function(e){if(e instanceof Y)return e;var t=[];if(e)for(var r in e)t.push(r,e[r]);return new Y(t)};var fe={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},He={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},mn=typeof navigator<"u"&&/Mac/.test(navigator.platform),bn=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var H=0;H<10;H++)fe[48+H]=fe[96+H]=String(H);for(var H=1;H<=24;H++)fe[H+111]="F"+H;for(var H=65;H<=90;H++)fe[H]=String.fromCharCode(H+32),He[H]=String.fromCharCode(H);for(var Qe in fe)He.hasOwnProperty(Qe)||(He[Qe]=fe[Qe]);function Ha(e){var t=mn&&e.metaKey&&e.shiftKey&&!e.ctrlKey&&!e.altKey||bn&&e.shiftKey&&e.key&&e.key.length==1||e.key=="Unidentified",r=!t&&e.key||(e.shiftKey?He:fe)[e.keyCode]||e.key||"Unidentified";return r=="Esc"&&(r="Escape"),r=="Del"&&(r="Delete"),r=="Left"&&(r="ArrowLeft"),r=="Up"&&(r="ArrowUp"),r=="Right"&&(r="ArrowRight"),r=="Down"&&(r="ArrowDown"),r}var W="top",X="bottom",V="right",z="left",ze="auto",Te=[W,X,V,z],pe="start",Ee="end",nr="clippingParents",ot="viewport",me="popper",ar="reference",rt=Te.reduce(function(e,t){return e.concat([t+"-"+pe,t+"-"+Ee])},[]),lt=[].concat(Te,[ze]).reduce(function(e,t){return e.concat([t,t+"-"+pe,t+"-"+Ee])},[]),ir="beforeRead",sr="read",or="afterRead",lr="beforeMain",ur="main",cr="afterMain",fr="beforeWrite",pr="write",dr="afterWrite",hr=[ir,sr,or,lr,ur,cr,fr,pr,dr];function te(e){return e?(e.nodeName||"").toLowerCase():null}function q(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function de(e){var t=q(e).Element;return e instanceof t||e instanceof Element}function K(e){var t=q(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function ut(e){if(typeof ShadowRoot>"u")return!1;var t=q(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function yn(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},a=t.attributes[r]||{},i=t.elements[r];!K(i)||!te(i)||(Object.assign(i.style,n),Object.keys(a).forEach(function(l){var s=a[l];s===!1?i.removeAttribute(l):i.setAttribute(l,s===!0?"":s)}))})}function En(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var a=t.elements[n],i=t.attributes[n]||{},l=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),s=l.reduce(function(o,v){return o[v]="",o},{});!K(a)||!te(a)||(Object.assign(a.style,s),Object.keys(i).forEach(function(o){a.removeAttribute(o)}))})}}const ct={name:"applyStyles",enabled:!0,phase:"write",fn:yn,effect:En,requires:["computeStyles"]};function ee(e){return e.split("-")[0]}var ce=Math.max,Ge=Math.min,we=Math.round;function nt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function gr(){return!/^((?!chrome|android).)*safari/i.test(nt())}function Ae(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var n=e.getBoundingClientRect(),a=1,i=1;t&&K(e)&&(a=e.offsetWidth>0&&we(n.width)/e.offsetWidth||1,i=e.offsetHeight>0&&we(n.height)/e.offsetHeight||1);var l=de(e)?q(e):window,s=l.visualViewport,o=!gr()&&r,v=(n.left+(o&&s?s.offsetLeft:0))/a,m=(n.top+(o&&s?s.offsetTop:0))/i,T=n.width/a,O=n.height/i;return{width:T,height:O,top:m,right:v+T,bottom:m+O,left:v,x:v,y:m}}function ft(e){var t=Ae(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function vr(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&ut(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function ae(e){return q(e).getComputedStyle(e)}function wn(e){return["table","td","th"].indexOf(te(e))>=0}function le(e){return((de(e)?e.ownerDocument:e.document)||window.document).documentElement}function Xe(e){return te(e)==="html"?e:e.assignedSlot||e.parentNode||(ut(e)?e.host:null)||le(e)}function Nt(e){return!K(e)||ae(e).position==="fixed"?null:e.offsetParent}function An(e){var t=/firefox/i.test(nt()),r=/Trident/i.test(nt());if(r&&K(e)){var n=ae(e);if(n.position==="fixed")return null}var a=Xe(e);for(ut(a)&&(a=a.host);K(a)&&["html","body"].indexOf(te(a))<0;){var i=ae(a);if(i.transform!=="none"||i.perspective!=="none"||i.contain==="paint"||["transform","perspective"].indexOf(i.willChange)!==-1||t&&i.willChange==="filter"||t&&i.filter&&i.filter!=="none")return a;a=a.parentNode}return null}function ke(e){for(var t=q(e),r=Nt(e);r&&wn(r)&&ae(r).position==="static";)r=Nt(r);return r&&(te(r)==="html"||te(r)==="body"&&ae(r).position==="static")?t:r||An(e)||t}function pt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ie(e,t,r){return ce(e,Ge(t,r))}function Sn(e,t,r){var n=Ie(e,t,r);return n>r?r:n}function mr(){return{top:0,right:0,bottom:0,left:0}}function br(e){return Object.assign({},mr(),e)}function yr(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var On=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,br(typeof t!="number"?t:yr(t,Te))};function Tn(e){var t,r=e.state,n=e.name,a=e.options,i=r.elements.arrow,l=r.modifiersData.popperOffsets,s=ee(r.placement),o=pt(s),v=[z,V].indexOf(s)>=0,m=v?"height":"width";if(!(!i||!l)){var T=On(a.padding,r),O=ft(i),R=o==="y"?W:z,k=o==="y"?X:V,y=r.rects.reference[m]+r.rects.reference[o]-l[o]-r.rects.popper[m],E=l[o]-r.rects.reference[o],x=ke(i),f=x?o==="y"?x.clientHeight||0:x.clientWidth||0:0,p=y/2-E/2,d=T[R],h=f-O[m]-T[k],g=f/2-O[m]/2+p,S=Ie(d,g,h),P=o;r.modifiersData[n]=(t={},t[P]=S,t.centerOffset=S-g,t)}}function xn(e){var t=e.state,r=e.options,n=r.element,a=n===void 0?"[data-popper-arrow]":n;a!=null&&(typeof a=="string"&&(a=t.elements.popper.querySelector(a),!a)||vr(t.elements.popper,a)&&(t.elements.arrow=a))}const Er={name:"arrow",enabled:!0,phase:"main",fn:Tn,effect:xn,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Se(e){return e.split("-")[1]}var Fn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function In(e,t){var r=e.x,n=e.y,a=t.devicePixelRatio||1;return{x:we(r*a)/a||0,y:we(n*a)/a||0}}function Dt(e){var t,r=e.popper,n=e.popperRect,a=e.placement,i=e.variation,l=e.offsets,s=e.position,o=e.gpuAcceleration,v=e.adaptive,m=e.roundOffsets,T=e.isFixed,O=l.x,R=O===void 0?0:O,k=l.y,y=k===void 0?0:k,E=typeof m=="function"?m({x:R,y}):{x:R,y};R=E.x,y=E.y;var x=l.hasOwnProperty("x"),f=l.hasOwnProperty("y"),p=z,d=W,h=window;if(v){var g=ke(r),S="clientHeight",P="clientWidth";if(g===q(r)&&(g=le(r),ae(g).position!=="static"&&s==="absolute"&&(S="scrollHeight",P="scrollWidth")),g=g,a===W||(a===z||a===V)&&i===Ee){d=X;var A=T&&g===h&&h.visualViewport?h.visualViewport.height:g[S];y-=A-n.height,y*=o?1:-1}if(a===z||(a===W||a===X)&&i===Ee){p=V;var _=T&&g===h&&h.visualViewport?h.visualViewport.width:g[P];R-=_-n.width,R*=o?1:-1}}var L=Object.assign({position:s},v&&Fn),N=m===!0?In({x:R,y},q(r)):{x:R,y};if(R=N.x,y=N.y,o){var M;return Object.assign({},L,(M={},M[d]=f?"0":"",M[p]=x?"0":"",M.transform=(h.devicePixelRatio||1)<=1?"translate("+R+"px, "+y+"px)":"translate3d("+R+"px, "+y+"px, 0)",M))}return Object.assign({},L,(t={},t[d]=f?y+"px":"",t[p]=x?R+"px":"",t.transform="",t))}function Rn(e){var t=e.state,r=e.options,n=r.gpuAcceleration,a=n===void 0?!0:n,i=r.adaptive,l=i===void 0?!0:i,s=r.roundOffsets,o=s===void 0?!0:s,v={placement:ee(t.placement),variation:Se(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Dt(Object.assign({},v,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:l,roundOffsets:o})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Dt(Object.assign({},v,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:o})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const dt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Rn,data:{}};var De={passive:!0};function Pn(e){var t=e.state,r=e.instance,n=e.options,a=n.scroll,i=a===void 0?!0:a,l=n.resize,s=l===void 0?!0:l,o=q(t.elements.popper),v=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&v.forEach(function(m){m.addEventListener("scroll",r.update,De)}),s&&o.addEventListener("resize",r.update,De),function(){i&&v.forEach(function(m){m.removeEventListener("scroll",r.update,De)}),s&&o.removeEventListener("resize",r.update,De)}}const ht={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Pn,data:{}};var _n={left:"right",right:"left",bottom:"top",top:"bottom"};function Ue(e){return e.replace(/left|right|bottom|top/g,function(t){return _n[t]})}var kn={start:"end",end:"start"};function Ct(e){return e.replace(/start|end/g,function(t){return kn[t]})}function gt(e){var t=q(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function vt(e){return Ae(le(e)).left+gt(e).scrollLeft}function Ln(e,t){var r=q(e),n=le(e),a=r.visualViewport,i=n.clientWidth,l=n.clientHeight,s=0,o=0;if(a){i=a.width,l=a.height;var v=gr();(v||!v&&t==="fixed")&&(s=a.offsetLeft,o=a.offsetTop)}return{width:i,height:l,x:s+vt(e),y:o}}function Nn(e){var t,r=le(e),n=gt(e),a=(t=e.ownerDocument)==null?void 0:t.body,i=ce(r.scrollWidth,r.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),l=ce(r.scrollHeight,r.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),s=-n.scrollLeft+vt(e),o=-n.scrollTop;return ae(a||r).direction==="rtl"&&(s+=ce(r.clientWidth,a?a.clientWidth:0)-i),{width:i,height:l,x:s,y:o}}function mt(e){var t=ae(e),r=t.overflow,n=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+a+n)}function wr(e){return["html","body","#document"].indexOf(te(e))>=0?e.ownerDocument.body:K(e)&&mt(e)?e:wr(Xe(e))}function Re(e,t){var r;t===void 0&&(t=[]);var n=wr(e),a=n===((r=e.ownerDocument)==null?void 0:r.body),i=q(n),l=a?[i].concat(i.visualViewport||[],mt(n)?n:[]):n,s=t.concat(l);return a?s:s.concat(Re(Xe(l)))}function at(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Dn(e,t){var r=Ae(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function $t(e,t,r){return t===ot?at(Ln(e,r)):de(t)?Dn(t,r):at(Nn(le(e)))}function Cn(e){var t=Re(Xe(e)),r=["absolute","fixed"].indexOf(ae(e).position)>=0,n=r&&K(e)?ke(e):e;return de(n)?t.filter(function(a){return de(a)&&vr(a,n)&&te(a)!=="body"}):[]}function $n(e,t,r,n){var a=t==="clippingParents"?Cn(e):[].concat(t),i=[].concat(a,[r]),l=i[0],s=i.reduce(function(o,v){var m=$t(e,v,n);return o.top=ce(m.top,o.top),o.right=Ge(m.right,o.right),o.bottom=Ge(m.bottom,o.bottom),o.left=ce(m.left,o.left),o},$t(e,l,n));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function Ar(e){var t=e.reference,r=e.element,n=e.placement,a=n?ee(n):null,i=n?Se(n):null,l=t.x+t.width/2-r.width/2,s=t.y+t.height/2-r.height/2,o;switch(a){case W:o={x:l,y:t.y-r.height};break;case X:o={x:l,y:t.y+t.height};break;case V:o={x:t.x+t.width,y:s};break;case z:o={x:t.x-r.width,y:s};break;default:o={x:t.x,y:t.y}}var v=a?pt(a):null;if(v!=null){var m=v==="y"?"height":"width";switch(i){case pe:o[v]=o[v]-(t[m]/2-r[m]/2);break;case Ee:o[v]=o[v]+(t[m]/2-r[m]/2);break}}return o}function Oe(e,t){t===void 0&&(t={});var r=t,n=r.placement,a=n===void 0?e.placement:n,i=r.strategy,l=i===void 0?e.strategy:i,s=r.boundary,o=s===void 0?nr:s,v=r.rootBoundary,m=v===void 0?ot:v,T=r.elementContext,O=T===void 0?me:T,R=r.altBoundary,k=R===void 0?!1:R,y=r.padding,E=y===void 0?0:y,x=br(typeof E!="number"?E:yr(E,Te)),f=O===me?ar:me,p=e.rects.popper,d=e.elements[k?f:O],h=$n(de(d)?d:d.contextElement||le(e.elements.popper),o,m,l),g=Ae(e.elements.reference),S=Ar({reference:g,element:p,placement:a}),P=at(Object.assign({},p,S)),A=O===me?P:g,_={top:h.top-A.top+x.top,bottom:A.bottom-h.bottom+x.bottom,left:h.left-A.left+x.left,right:A.right-h.right+x.right},L=e.modifiersData.offset;if(O===me&&L){var N=L[a];Object.keys(_).forEach(function(M){var U=[V,X].indexOf(M)>=0?1:-1,c=[W,X].indexOf(M)>=0?"y":"x";_[M]+=N[c]*U})}return _}function Mn(e,t){t===void 0&&(t={});var r=t,n=r.placement,a=r.boundary,i=r.rootBoundary,l=r.padding,s=r.flipVariations,o=r.allowedAutoPlacements,v=o===void 0?lt:o,m=Se(n),T=m?s?rt:rt.filter(function(k){return Se(k)===m}):Te,O=T.filter(function(k){return v.indexOf(k)>=0});O.length===0&&(O=T);var R=O.reduce(function(k,y){return k[y]=Oe(e,{placement:y,boundary:a,rootBoundary:i,padding:l})[ee(y)],k},{});return Object.keys(R).sort(function(k,y){return R[k]-R[y]})}function Bn(e){if(ee(e)===ze)return[];var t=Ue(e);return[Ct(e),t,Ct(t)]}function Un(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var a=r.mainAxis,i=a===void 0?!0:a,l=r.altAxis,s=l===void 0?!0:l,o=r.fallbackPlacements,v=r.padding,m=r.boundary,T=r.rootBoundary,O=r.altBoundary,R=r.flipVariations,k=R===void 0?!0:R,y=r.allowedAutoPlacements,E=t.options.placement,x=ee(E),f=x===E,p=o||(f||!k?[Ue(E)]:Bn(E)),d=[E].concat(p).reduce(function(D,B){return D.concat(ee(B)===ze?Mn(t,{placement:B,boundary:m,rootBoundary:T,padding:v,flipVariations:k,allowedAutoPlacements:y}):B)},[]),h=t.rects.reference,g=t.rects.popper,S=new Map,P=!0,A=d[0],_=0;_<d.length;_++){var L=d[_],N=ee(L),M=Se(L)===pe,U=[W,X].indexOf(N)>=0,c=U?"width":"height",u=Oe(t,{placement:L,boundary:m,rootBoundary:T,altBoundary:O,padding:v}),b=U?M?V:z:M?X:W;h[c]>g[c]&&(b=Ue(b));var w=Ue(b),F=[];if(i&&F.push(u[N]<=0),s&&F.push(u[b]<=0,u[w]<=0),F.every(function(D){return D})){A=L,P=!1;break}S.set(L,F)}if(P)for(var I=k?3:1,j=function(B){var J=d.find(function(re){var Q=S.get(re);if(Q)return Q.slice(0,B).every(function(he){return he})});if(J)return A=J,"break"},C=I;C>0;C--){var $=j(C);if($==="break")break}t.placement!==A&&(t.modifiersData[n]._skip=!0,t.placement=A,t.reset=!0)}}const Sr={name:"flip",enabled:!0,phase:"main",fn:Un,requiresIfExists:["offset"],data:{_skip:!1}};function Mt(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function Bt(e){return[W,V,X,z].some(function(t){return e[t]>=0})}function jn(e){var t=e.state,r=e.name,n=t.rects.reference,a=t.rects.popper,i=t.modifiersData.preventOverflow,l=Oe(t,{elementContext:"reference"}),s=Oe(t,{altBoundary:!0}),o=Mt(l,n),v=Mt(s,a,i),m=Bt(o),T=Bt(v);t.modifiersData[r]={referenceClippingOffsets:o,popperEscapeOffsets:v,isReferenceHidden:m,hasPopperEscaped:T},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":m,"data-popper-escaped":T})}const Or={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:jn};function Hn(e,t,r){var n=ee(e),a=[z,W].indexOf(n)>=0?-1:1,i=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,l=i[0],s=i[1];return l=l||0,s=(s||0)*a,[z,V].indexOf(n)>=0?{x:s,y:l}:{x:l,y:s}}function Gn(e){var t=e.state,r=e.options,n=e.name,a=r.offset,i=a===void 0?[0,0]:a,l=lt.reduce(function(m,T){return m[T]=Hn(T,t.rects,i),m},{}),s=l[t.placement],o=s.x,v=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=o,t.modifiersData.popperOffsets.y+=v),t.modifiersData[n]=l}const Tr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Gn};function Yn(e){var t=e.state,r=e.name;t.modifiersData[r]=Ar({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const bt={name:"popperOffsets",enabled:!0,phase:"read",fn:Yn,data:{}};function Wn(e){return e==="x"?"y":"x"}function zn(e){var t=e.state,r=e.options,n=e.name,a=r.mainAxis,i=a===void 0?!0:a,l=r.altAxis,s=l===void 0?!1:l,o=r.boundary,v=r.rootBoundary,m=r.altBoundary,T=r.padding,O=r.tether,R=O===void 0?!0:O,k=r.tetherOffset,y=k===void 0?0:k,E=Oe(t,{boundary:o,rootBoundary:v,padding:T,altBoundary:m}),x=ee(t.placement),f=Se(t.placement),p=!f,d=pt(x),h=Wn(d),g=t.modifiersData.popperOffsets,S=t.rects.reference,P=t.rects.popper,A=typeof y=="function"?y(Object.assign({},t.rects,{placement:t.placement})):y,_=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),L=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,N={x:0,y:0};if(g){if(i){var M,U=d==="y"?W:z,c=d==="y"?X:V,u=d==="y"?"height":"width",b=g[d],w=b+E[U],F=b-E[c],I=R?-P[u]/2:0,j=f===pe?S[u]:P[u],C=f===pe?-P[u]:-S[u],$=t.elements.arrow,D=R&&$?ft($):{width:0,height:0},B=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:mr(),J=B[U],re=B[c],Q=Ie(0,S[u],D[u]),he=p?S[u]/2-I-Q-J-_.mainAxis:j-Q-J-_.mainAxis,xe=p?-S[u]/2+I+Q+re+_.mainAxis:C+Q+re+_.mainAxis,ie=t.elements.arrow&&ke(t.elements.arrow),qe=ie?d==="y"?ie.clientTop||0:ie.clientLeft||0:0,ge=(M=L==null?void 0:L[d])!=null?M:0,kr=b+he-ge-qe,Lr=b+xe-ge,At=Ie(R?Ge(w,kr):w,b,R?ce(F,Lr):F);g[d]=At,N[d]=At-b}if(s){var St,Nr=d==="x"?W:z,Dr=d==="x"?X:V,ue=g[h],Ne=h==="y"?"height":"width",Ot=ue+E[Nr],Tt=ue-E[Dr],Ze=[W,z].indexOf(x)!==-1,xt=(St=L==null?void 0:L[h])!=null?St:0,Ft=Ze?Ot:ue-S[Ne]-P[Ne]-xt+_.altAxis,It=Ze?ue+S[Ne]+P[Ne]-xt-_.altAxis:Tt,Rt=R&&Ze?Sn(Ft,ue,It):Ie(R?Ft:Ot,ue,R?It:Tt);g[h]=Rt,N[h]=Rt-ue}t.modifiersData[n]=N}}const xr={name:"preventOverflow",enabled:!0,phase:"main",fn:zn,requiresIfExists:["offset"]};function Xn(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Vn(e){return e===q(e)||!K(e)?gt(e):Xn(e)}function qn(e){var t=e.getBoundingClientRect(),r=we(t.width)/e.offsetWidth||1,n=we(t.height)/e.offsetHeight||1;return r!==1||n!==1}function Zn(e,t,r){r===void 0&&(r=!1);var n=K(t),a=K(t)&&qn(t),i=le(t),l=Ae(e,a,r),s={scrollLeft:0,scrollTop:0},o={x:0,y:0};return(n||!n&&!r)&&((te(t)!=="body"||mt(i))&&(s=Vn(t)),K(t)?(o=Ae(t,!0),o.x+=t.clientLeft,o.y+=t.clientTop):i&&(o.x=vt(i))),{x:l.left+s.scrollLeft-o.x,y:l.top+s.scrollTop-o.y,width:l.width,height:l.height}}function Kn(e){var t=new Map,r=new Set,n=[];e.forEach(function(i){t.set(i.name,i)});function a(i){r.add(i.name);var l=[].concat(i.requires||[],i.requiresIfExists||[]);l.forEach(function(s){if(!r.has(s)){var o=t.get(s);o&&a(o)}}),n.push(i)}return e.forEach(function(i){r.has(i.name)||a(i)}),n}function Qn(e){var t=Kn(e);return hr.reduce(function(r,n){return r.concat(t.filter(function(a){return a.phase===n}))},[])}function Jn(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function ea(e){var t=e.reduce(function(r,n){var a=r[n.name];return r[n.name]=a?Object.assign({},a,n,{options:Object.assign({},a.options,n.options),data:Object.assign({},a.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var Ut={placement:"bottom",modifiers:[],strategy:"absolute"};function jt(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Ve(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,a=t.defaultOptions,i=a===void 0?Ut:a;return function(s,o,v){v===void 0&&(v=i);var m={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ut,i),modifiersData:{},elements:{reference:s,popper:o},attributes:{},styles:{}},T=[],O=!1,R={state:m,setOptions:function(x){var f=typeof x=="function"?x(m.options):x;y(),m.options=Object.assign({},i,m.options,f),m.scrollParents={reference:de(s)?Re(s):s.contextElement?Re(s.contextElement):[],popper:Re(o)};var p=Qn(ea([].concat(n,m.options.modifiers)));return m.orderedModifiers=p.filter(function(d){return d.enabled}),k(),R.update()},forceUpdate:function(){if(!O){var x=m.elements,f=x.reference,p=x.popper;if(jt(f,p)){m.rects={reference:Zn(f,ke(p),m.options.strategy==="fixed"),popper:ft(p)},m.reset=!1,m.placement=m.options.placement,m.orderedModifiers.forEach(function(_){return m.modifiersData[_.name]=Object.assign({},_.data)});for(var d=0;d<m.orderedModifiers.length;d++){if(m.reset===!0){m.reset=!1,d=-1;continue}var h=m.orderedModifiers[d],g=h.fn,S=h.options,P=S===void 0?{}:S,A=h.name;typeof g=="function"&&(m=g({state:m,options:P,name:A,instance:R})||m)}}}},update:Jn(function(){return new Promise(function(E){R.forceUpdate(),E(m)})}),destroy:function(){y(),O=!0}};if(!jt(s,o))return R;R.setOptions(v).then(function(E){!O&&v.onFirstUpdate&&v.onFirstUpdate(E)});function k(){m.orderedModifiers.forEach(function(E){var x=E.name,f=E.options,p=f===void 0?{}:f,d=E.effect;if(typeof d=="function"){var h=d({state:m,name:x,instance:R,options:p}),g=function(){};T.push(h||g)}})}function y(){T.forEach(function(E){return E()}),T=[]}return R}}var ta=Ve(),ra=[ht,bt,dt,ct],na=Ve({defaultModifiers:ra}),aa=[ht,bt,dt,ct,Tr,Sr,xr,Er,Or],ia=Ve({defaultModifiers:aa});const Ga=Object.freeze(Object.defineProperty({__proto__:null,afterMain:cr,afterRead:or,afterWrite:dr,applyStyles:ct,arrow:Er,auto:ze,basePlacements:Te,beforeMain:lr,beforeRead:ir,beforeWrite:fr,bottom:X,clippingParents:nr,computeStyles:dt,createPopper:ia,createPopperBase:ta,createPopperLite:na,detectOverflow:Oe,end:Ee,eventListeners:ht,flip:Sr,hide:Or,left:z,main:ur,modifierPhases:hr,offset:Tr,placements:lt,popper:me,popperGenerator:Ve,popperOffsets:bt,preventOverflow:xr,read:sr,reference:ar,right:V,start:pe,top:W,variationPlacements:rt,viewport:ot,write:pr},Symbol.toStringTag,{value:"Module"}));var Ye=200,G=function(){};G.prototype.append=function(t){return t.length?(t=G.from(t),!this.length&&t||t.length<Ye&&this.leafAppend(t)||this.length<Ye&&t.leafPrepend(this)||this.appendInner(t)):this};G.prototype.prepend=function(t){return t.length?G.from(t).append(this):this};G.prototype.appendInner=function(t){return new sa(this,t)};G.prototype.slice=function(t,r){return t===void 0&&(t=0),r===void 0&&(r=this.length),t>=r?G.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,r))};G.prototype.get=function(t){if(!(t<0||t>=this.length))return this.getInner(t)};G.prototype.forEach=function(t,r,n){r===void 0&&(r=0),n===void 0&&(n=this.length),r<=n?this.forEachInner(t,r,n,0):this.forEachInvertedInner(t,r,n,0)};G.prototype.map=function(t,r,n){r===void 0&&(r=0),n===void 0&&(n=this.length);var a=[];return this.forEach(function(i,l){return a.push(t(i,l))},r,n),a};G.from=function(t){return t instanceof G?t:t&&t.length?new Fr(t):G.empty};var Fr=function(e){function t(n){e.call(this),this.values=n}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(a,i){return a==0&&i==this.length?this:new t(this.values.slice(a,i))},t.prototype.getInner=function(a){return this.values[a]},t.prototype.forEachInner=function(a,i,l,s){for(var o=i;o<l;o++)if(a(this.values[o],s+o)===!1)return!1},t.prototype.forEachInvertedInner=function(a,i,l,s){for(var o=i-1;o>=l;o--)if(a(this.values[o],s+o)===!1)return!1},t.prototype.leafAppend=function(a){if(this.length+a.length<=Ye)return new t(this.values.concat(a.flatten()))},t.prototype.leafPrepend=function(a){if(this.length+a.length<=Ye)return new t(a.flatten().concat(this.values))},r.length.get=function(){return this.values.length},r.depth.get=function(){return 0},Object.defineProperties(t.prototype,r),t}(G);G.empty=new Fr([]);var sa=function(e){function t(r,n){e.call(this),this.left=r,this.right=n,this.length=r.length+n.length,this.depth=Math.max(r.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(n){return n<this.left.length?this.left.get(n):this.right.get(n-this.left.length)},t.prototype.forEachInner=function(n,a,i,l){var s=this.left.length;if(a<s&&this.left.forEachInner(n,a,Math.min(i,s),l)===!1||i>s&&this.right.forEachInner(n,Math.max(a-s,0),Math.min(this.length,i)-s,l+s)===!1)return!1},t.prototype.forEachInvertedInner=function(n,a,i,l){var s=this.left.length;if(a>s&&this.right.forEachInvertedInner(n,a-s,Math.max(i,s)-s,l+s)===!1||i<s&&this.left.forEachInvertedInner(n,Math.min(a,s),i,l)===!1)return!1},t.prototype.sliceInner=function(n,a){if(n==0&&a==this.length)return this;var i=this.left.length;return a<=i?this.left.slice(n,a):n>=i?this.right.slice(n-i,a-i):this.left.slice(n,i).append(this.right.slice(0,a-i))},t.prototype.leafAppend=function(n){var a=this.right.leafAppend(n);if(a)return new t(this.left,a)},t.prototype.leafPrepend=function(n){var a=this.left.leafPrepend(n);if(a)return new t(a,this.right)},t.prototype.appendInner=function(n){return this.left.depth>=Math.max(this.right.depth,n.depth)+1?new t(this.left,new t(this.right,n)):new t(this,n)},t}(G);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function Le(e){return e+.5|0}const se=(e,t,r)=>Math.max(Math.min(e,r),t);function Fe(e){return se(Le(e*2.55),0,255)}function oe(e){return se(Le(e*255),0,255)}function ne(e){return se(Le(e/2.55)/100,0,1)}function Ht(e){return se(Le(e*100),0,100)}const Z={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},it=[..."0123456789ABCDEF"],oa=e=>it[e&15],la=e=>it[(e&240)>>4]+it[e&15],Ce=e=>(e&240)>>4===(e&15),ua=e=>Ce(e.r)&&Ce(e.g)&&Ce(e.b)&&Ce(e.a);function ca(e){var t=e.length,r;return e[0]==="#"&&(t===4||t===5?r={r:255&Z[e[1]]*17,g:255&Z[e[2]]*17,b:255&Z[e[3]]*17,a:t===5?Z[e[4]]*17:255}:(t===7||t===9)&&(r={r:Z[e[1]]<<4|Z[e[2]],g:Z[e[3]]<<4|Z[e[4]],b:Z[e[5]]<<4|Z[e[6]],a:t===9?Z[e[7]]<<4|Z[e[8]]:255})),r}const fa=(e,t)=>e<255?t(e):"";function pa(e){var t=ua(e)?oa:la;return e?"#"+t(e.r)+t(e.g)+t(e.b)+fa(e.a,t):void 0}const da=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Ir(e,t,r){const n=t*Math.min(r,1-r),a=(i,l=(i+e/30)%12)=>r-n*Math.max(Math.min(l-3,9-l,1),-1);return[a(0),a(8),a(4)]}function ha(e,t,r){const n=(a,i=(a+e/60)%6)=>r-r*t*Math.max(Math.min(i,4-i,1),0);return[n(5),n(3),n(1)]}function ga(e,t,r){const n=Ir(e,1,.5);let a;for(t+r>1&&(a=1/(t+r),t*=a,r*=a),a=0;a<3;a++)n[a]*=1-t-r,n[a]+=t;return n}function va(e,t,r,n,a){return e===a?(t-r)/n+(t<r?6:0):t===a?(r-e)/n+2:(e-t)/n+4}function yt(e){const r=e.r/255,n=e.g/255,a=e.b/255,i=Math.max(r,n,a),l=Math.min(r,n,a),s=(i+l)/2;let o,v,m;return i!==l&&(m=i-l,v=s>.5?m/(2-i-l):m/(i+l),o=va(r,n,a,m,i),o=o*60+.5),[o|0,v||0,s]}function Et(e,t,r,n){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,r,n)).map(oe)}function wt(e,t,r){return Et(Ir,e,t,r)}function ma(e,t,r){return Et(ga,e,t,r)}function ba(e,t,r){return Et(ha,e,t,r)}function Rr(e){return(e%360+360)%360}function ya(e){const t=da.exec(e);let r=255,n;if(!t)return;t[5]!==n&&(r=t[6]?Fe(+t[5]):oe(+t[5]));const a=Rr(+t[2]),i=+t[3]/100,l=+t[4]/100;return t[1]==="hwb"?n=ma(a,i,l):t[1]==="hsv"?n=ba(a,i,l):n=wt(a,i,l),{r:n[0],g:n[1],b:n[2],a:r}}function Ea(e,t){var r=yt(e);r[0]=Rr(r[0]+t),r=wt(r),e.r=r[0],e.g=r[1],e.b=r[2]}function wa(e){if(!e)return;const t=yt(e),r=t[0],n=Ht(t[1]),a=Ht(t[2]);return e.a<255?`hsla(${r}, ${n}%, ${a}%, ${ne(e.a)})`:`hsl(${r}, ${n}%, ${a}%)`}const Gt={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Yt={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Aa(){const e={},t=Object.keys(Yt),r=Object.keys(Gt);let n,a,i,l,s;for(n=0;n<t.length;n++){for(l=s=t[n],a=0;a<r.length;a++)i=r[a],s=s.replace(i,Gt[i]);i=parseInt(Yt[l],16),e[s]=[i>>16&255,i>>8&255,i&255]}return e}let $e;function Sa(e){$e||($e=Aa(),$e.transparent=[0,0,0,0]);const t=$e[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Oa=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Ta(e){const t=Oa.exec(e);let r=255,n,a,i;if(t){if(t[7]!==n){const l=+t[7];r=t[8]?Fe(l):se(l*255,0,255)}return n=+t[1],a=+t[3],i=+t[5],n=255&(t[2]?Fe(n):se(n,0,255)),a=255&(t[4]?Fe(a):se(a,0,255)),i=255&(t[6]?Fe(i):se(i,0,255)),{r:n,g:a,b:i,a:r}}}function xa(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${ne(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const Je=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,ve=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Fa(e,t,r){const n=ve(ne(e.r)),a=ve(ne(e.g)),i=ve(ne(e.b));return{r:oe(Je(n+r*(ve(ne(t.r))-n))),g:oe(Je(a+r*(ve(ne(t.g))-a))),b:oe(Je(i+r*(ve(ne(t.b))-i))),a:e.a+r*(t.a-e.a)}}function Me(e,t,r){if(e){let n=yt(e);n[t]=Math.max(0,Math.min(n[t]+n[t]*r,t===0?360:1)),n=wt(n),e.r=n[0],e.g=n[1],e.b=n[2]}}function Pr(e,t){return e&&Object.assign(t||{},e)}function Wt(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=oe(e[3]))):(t=Pr(e,{r:0,g:0,b:0,a:1}),t.a=oe(t.a)),t}function Ia(e){return e.charAt(0)==="r"?Ta(e):ya(e)}class st{constructor(t){if(t instanceof st)return t;const r=typeof t;let n;r==="object"?n=Wt(t):r==="string"&&(n=ca(t)||Sa(t)||Ia(t)),this._rgb=n,this._valid=!!n}get valid(){return this._valid}get rgb(){var t=Pr(this._rgb);return t&&(t.a=ne(t.a)),t}set rgb(t){this._rgb=Wt(t)}rgbString(){return this._valid?xa(this._rgb):void 0}hexString(){return this._valid?pa(this._rgb):void 0}hslString(){return this._valid?wa(this._rgb):void 0}mix(t,r){if(t){const n=this.rgb,a=t.rgb;let i;const l=r===i?.5:r,s=2*l-1,o=n.a-a.a,v=((s*o===-1?s:(s+o)/(1+s*o))+1)/2;i=1-v,n.r=255&v*n.r+i*a.r+.5,n.g=255&v*n.g+i*a.g+.5,n.b=255&v*n.b+i*a.b+.5,n.a=l*n.a+(1-l)*a.a,this.rgb=n}return this}interpolate(t,r){return t&&(this._rgb=Fa(this._rgb,t._rgb,r)),this}clone(){return new st(this.rgb)}alpha(t){return this._rgb.a=oe(t),this}clearer(t){const r=this._rgb;return r.a*=1-t,this}greyscale(){const t=this._rgb,r=Le(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=r,this}opaquer(t){const r=this._rgb;return r.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Me(this._rgb,2,t),this}darken(t){return Me(this._rgb,2,-t),this}saturate(t){return Me(this._rgb,1,t),this}desaturate(t){return Me(this._rgb,1,-t),this}rotate(t){return Ea(this._rgb,t),this}}var _r={exports:{}};(function(e){var t=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var r=function(n){var a=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,i=0,l={},s={manual:n.Prism&&n.Prism.manual,disableWorkerMessageHandler:n.Prism&&n.Prism.disableWorkerMessageHandler,util:{encode:function f(p){return p instanceof o?new o(p.type,f(p.content),p.alias):Array.isArray(p)?p.map(f):p.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(f){return Object.prototype.toString.call(f).slice(8,-1)},objId:function(f){return f.__id||Object.defineProperty(f,"__id",{value:++i}),f.__id},clone:function f(p,d){d=d||{};var h,g;switch(s.util.type(p)){case"Object":if(g=s.util.objId(p),d[g])return d[g];h={},d[g]=h;for(var S in p)p.hasOwnProperty(S)&&(h[S]=f(p[S],d));return h;case"Array":return g=s.util.objId(p),d[g]?d[g]:(h=[],d[g]=h,p.forEach(function(P,A){h[A]=f(P,d)}),h);default:return p}},getLanguage:function(f){for(;f;){var p=a.exec(f.className);if(p)return p[1].toLowerCase();f=f.parentElement}return"none"},setLanguage:function(f,p){f.className=f.className.replace(RegExp(a,"gi"),""),f.classList.add("language-"+p)},currentScript:function(){if(typeof document>"u")return null;if(document.currentScript&&document.currentScript.tagName==="SCRIPT")return document.currentScript;try{throw new Error}catch(h){var f=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(h.stack)||[])[1];if(f){var p=document.getElementsByTagName("script");for(var d in p)if(p[d].src==f)return p[d]}return null}},isActive:function(f,p,d){for(var h="no-"+p;f;){var g=f.classList;if(g.contains(p))return!0;if(g.contains(h))return!1;f=f.parentElement}return!!d}},languages:{plain:l,plaintext:l,text:l,txt:l,extend:function(f,p){var d=s.util.clone(s.languages[f]);for(var h in p)d[h]=p[h];return d},insertBefore:function(f,p,d,h){h=h||s.languages;var g=h[f],S={};for(var P in g)if(g.hasOwnProperty(P)){if(P==p)for(var A in d)d.hasOwnProperty(A)&&(S[A]=d[A]);d.hasOwnProperty(P)||(S[P]=g[P])}var _=h[f];return h[f]=S,s.languages.DFS(s.languages,function(L,N){N===_&&L!=f&&(this[L]=S)}),S},DFS:function f(p,d,h,g){g=g||{};var S=s.util.objId;for(var P in p)if(p.hasOwnProperty(P)){d.call(p,P,p[P],h||P);var A=p[P],_=s.util.type(A);_==="Object"&&!g[S(A)]?(g[S(A)]=!0,f(A,d,null,g)):_==="Array"&&!g[S(A)]&&(g[S(A)]=!0,f(A,d,P,g))}}},plugins:{},highlightAll:function(f,p){s.highlightAllUnder(document,f,p)},highlightAllUnder:function(f,p,d){var h={callback:d,container:f,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};s.hooks.run("before-highlightall",h),h.elements=Array.prototype.slice.apply(h.container.querySelectorAll(h.selector)),s.hooks.run("before-all-elements-highlight",h);for(var g=0,S;S=h.elements[g++];)s.highlightElement(S,p===!0,h.callback)},highlightElement:function(f,p,d){var h=s.util.getLanguage(f),g=s.languages[h];s.util.setLanguage(f,h);var S=f.parentElement;S&&S.nodeName.toLowerCase()==="pre"&&s.util.setLanguage(S,h);var P=f.textContent,A={element:f,language:h,grammar:g,code:P};function _(N){A.highlightedCode=N,s.hooks.run("before-insert",A),A.element.innerHTML=A.highlightedCode,s.hooks.run("after-highlight",A),s.hooks.run("complete",A),d&&d.call(A.element)}if(s.hooks.run("before-sanity-check",A),S=A.element.parentElement,S&&S.nodeName.toLowerCase()==="pre"&&!S.hasAttribute("tabindex")&&S.setAttribute("tabindex","0"),!A.code){s.hooks.run("complete",A),d&&d.call(A.element);return}if(s.hooks.run("before-highlight",A),!A.grammar){_(s.util.encode(A.code));return}if(p&&n.Worker){var L=new Worker(s.filename);L.onmessage=function(N){_(N.data)},L.postMessage(JSON.stringify({language:A.language,code:A.code,immediateClose:!0}))}else _(s.highlight(A.code,A.grammar,A.language))},highlight:function(f,p,d){var h={code:f,grammar:p,language:d};if(s.hooks.run("before-tokenize",h),!h.grammar)throw new Error('The language "'+h.language+'" has no grammar.');return h.tokens=s.tokenize(h.code,h.grammar),s.hooks.run("after-tokenize",h),o.stringify(s.util.encode(h.tokens),h.language)},tokenize:function(f,p){var d=p.rest;if(d){for(var h in d)p[h]=d[h];delete p.rest}var g=new T;return O(g,g.head,f),m(f,g,p,g.head,0),k(g)},hooks:{all:{},add:function(f,p){var d=s.hooks.all;d[f]=d[f]||[],d[f].push(p)},run:function(f,p){var d=s.hooks.all[f];if(!(!d||!d.length))for(var h=0,g;g=d[h++];)g(p)}},Token:o};n.Prism=s;function o(f,p,d,h){this.type=f,this.content=p,this.alias=d,this.length=(h||"").length|0}o.stringify=function f(p,d){if(typeof p=="string")return p;if(Array.isArray(p)){var h="";return p.forEach(function(_){h+=f(_,d)}),h}var g={type:p.type,content:f(p.content,d),tag:"span",classes:["token",p.type],attributes:{},language:d},S=p.alias;S&&(Array.isArray(S)?Array.prototype.push.apply(g.classes,S):g.classes.push(S)),s.hooks.run("wrap",g);var P="";for(var A in g.attributes)P+=" "+A+'="'+(g.attributes[A]||"").replace(/"/g,"&quot;")+'"';return"<"+g.tag+' class="'+g.classes.join(" ")+'"'+P+">"+g.content+"</"+g.tag+">"};function v(f,p,d,h){f.lastIndex=p;var g=f.exec(d);if(g&&h&&g[1]){var S=g[1].length;g.index+=S,g[0]=g[0].slice(S)}return g}function m(f,p,d,h,g,S){for(var P in d)if(!(!d.hasOwnProperty(P)||!d[P])){var A=d[P];A=Array.isArray(A)?A:[A];for(var _=0;_<A.length;++_){if(S&&S.cause==P+","+_)return;var L=A[_],N=L.inside,M=!!L.lookbehind,U=!!L.greedy,c=L.alias;if(U&&!L.pattern.global){var u=L.pattern.toString().match(/[imsuy]*$/)[0];L.pattern=RegExp(L.pattern.source,u+"g")}for(var b=L.pattern||L,w=h.next,F=g;w!==p.tail&&!(S&&F>=S.reach);F+=w.value.length,w=w.next){var I=w.value;if(p.length>f.length)return;if(!(I instanceof o)){var j=1,C;if(U){if(C=v(b,F,f,M),!C||C.index>=f.length)break;var J=C.index,$=C.index+C[0].length,D=F;for(D+=w.value.length;J>=D;)w=w.next,D+=w.value.length;if(D-=w.value.length,F=D,w.value instanceof o)continue;for(var B=w;B!==p.tail&&(D<$||typeof B.value=="string");B=B.next)j++,D+=B.value.length;j--,I=f.slice(F,D),C.index-=F}else if(C=v(b,0,I,M),!C)continue;var J=C.index,re=C[0],Q=I.slice(0,J),he=I.slice(J+re.length),xe=F+I.length;S&&xe>S.reach&&(S.reach=xe);var ie=w.prev;Q&&(ie=O(p,ie,Q),F+=Q.length),R(p,ie,j);var qe=new o(P,N?s.tokenize(re,N):re,c,re);if(w=O(p,ie,qe),he&&O(p,w,he),j>1){var ge={cause:P+","+_,reach:xe};m(f,p,d,w.prev,F,ge),S&&ge.reach>S.reach&&(S.reach=ge.reach)}}}}}}function T(){var f={value:null,prev:null,next:null},p={value:null,prev:f,next:null};f.next=p,this.head=f,this.tail=p,this.length=0}function O(f,p,d){var h=p.next,g={value:d,prev:p,next:h};return p.next=g,h.prev=g,f.length++,g}function R(f,p,d){for(var h=p.next,g=0;g<d&&h!==f.tail;g++)h=h.next;p.next=h,h.prev=p,f.length-=g}function k(f){for(var p=[],d=f.head.next;d!==f.tail;)p.push(d.value),d=d.next;return p}if(!n.document)return n.addEventListener&&(s.disableWorkerMessageHandler||n.addEventListener("message",function(f){var p=JSON.parse(f.data),d=p.language,h=p.code,g=p.immediateClose;n.postMessage(s.highlight(h,s.languages[d],d)),g&&n.close()},!1)),s;var y=s.util.currentScript();y&&(s.filename=y.src,y.hasAttribute("data-manual")&&(s.manual=!0));function E(){s.manual||s.highlightAll()}if(!s.manual){var x=document.readyState;x==="loading"||x==="interactive"&&y&&y.defer?document.addEventListener("DOMContentLoaded",E):window.requestAnimationFrame?window.requestAnimationFrame(E):window.setTimeout(E,16)}return s}(t);e.exports&&(e.exports=r),typeof je<"u"&&(je.Prism=r),r.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},r.languages.markup.tag.inside["attr-value"].inside.entity=r.languages.markup.entity,r.languages.markup.doctype.inside["internal-subset"].inside=r.languages.markup,r.hooks.add("wrap",function(n){n.type==="entity"&&(n.attributes.title=n.content.replace(/&amp;/,"&"))}),Object.defineProperty(r.languages.markup.tag,"addInlined",{value:function(a,i){var l={};l["language-"+i]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:r.languages[i]},l.cdata=/^<!\[CDATA\[|\]\]>$/i;var s={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:l}};s["language-"+i]={pattern:/[\s\S]+/,inside:r.languages[i]};var o={};o[a]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return a}),"i"),lookbehind:!0,greedy:!0,inside:s},r.languages.insertBefore("markup","cdata",o)}}),Object.defineProperty(r.languages.markup.tag,"addAttribute",{value:function(n,a){r.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+n+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[a,"language-"+a],inside:r.languages[a]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),r.languages.html=r.languages.markup,r.languages.mathml=r.languages.markup,r.languages.svg=r.languages.markup,r.languages.xml=r.languages.extend("markup",{}),r.languages.ssml=r.languages.xml,r.languages.atom=r.languages.xml,r.languages.rss=r.languages.xml,function(n){var a=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;n.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+a.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+a.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+a.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+a.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:a,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},n.languages.css.atrule.inside.rest=n.languages.css;var i=n.languages.markup;i&&(i.tag.addInlined("style","css"),i.tag.addAttribute("style","css"))}(r),r.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},r.languages.javascript=r.languages.extend("clike",{"class-name":[r.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),r.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,r.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:r.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:r.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:r.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:r.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:r.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),r.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:r.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),r.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),r.languages.markup&&(r.languages.markup.tag.addInlined("script","javascript"),r.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),r.languages.js=r.languages.javascript,function(){if(typeof r>"u"||typeof document>"u")return;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var n="Loading…",a=function(y,E){return"✖ Error "+y+" while fetching file: "+E},i="✖ Error: File does not exist or is empty",l={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},s="data-src-status",o="loading",v="loaded",m="failed",T="pre[data-src]:not(["+s+'="'+v+'"]):not(['+s+'="'+o+'"])';function O(y,E,x){var f=new XMLHttpRequest;f.open("GET",y,!0),f.onreadystatechange=function(){f.readyState==4&&(f.status<400&&f.responseText?E(f.responseText):f.status>=400?x(a(f.status,f.statusText)):x(i))},f.send(null)}function R(y){var E=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(y||"");if(E){var x=Number(E[1]),f=E[2],p=E[3];return f?p?[x,Number(p)]:[x,void 0]:[x,x]}}r.hooks.add("before-highlightall",function(y){y.selector+=", "+T}),r.hooks.add("before-sanity-check",function(y){var E=y.element;if(E.matches(T)){y.code="",E.setAttribute(s,o);var x=E.appendChild(document.createElement("CODE"));x.textContent=n;var f=E.getAttribute("data-src"),p=y.language;if(p==="none"){var d=(/\.(\w+)$/.exec(f)||[,"none"])[1];p=l[d]||d}r.util.setLanguage(x,p),r.util.setLanguage(E,p);var h=r.plugins.autoloader;h&&h.loadLanguages(p),O(f,function(g){E.setAttribute(s,v);var S=R(E.getAttribute("data-range"));if(S){var P=g.split(/\r\n?|\n/g),A=S[0],_=S[1]==null?P.length:S[1];A<0&&(A+=P.length),A=Math.max(0,Math.min(A-1,P.length)),_<0&&(_+=P.length),_=Math.max(0,Math.min(_,P.length)),g=P.slice(A,_).join(`
`),E.hasAttribute("data-start")||E.setAttribute("data-start",String(A+1))}x.textContent=g,r.highlightElement(x)},function(g){E.setAttribute(s,m),x.textContent=g})}}),r.plugins.fileHighlight={highlight:function(E){for(var x=(E||document).querySelectorAll(T),f=0,p;p=x[f++];)r.highlightElement(p)}};var k=!1;r.fileHighlight=function(){k||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),k=!0),r.plugins.fileHighlight.highlight.apply(this,arguments)}}()})(_r);var Ra=_r.exports;const Pa=_e(Ra),Ya=Object.freeze(Object.defineProperty({__proto__:null,default:Pa},Symbol.toStringTag,{value:"Module"})),Wa=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/});Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/;Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}});Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}});Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript"));Prism.languages.js=Prism.languages.javascript;const za=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));(function(e){e.languages.typescript=e.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),e.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript["literal-property"];var t=e.languages.extend("typescript",{});delete t["class-name"],e.languages.typescript["class-name"].inside=t,e.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:t}}}}),e.languages.ts=e.languages.typescript})(Prism);const Xa=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));(function(e){var t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+t.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css;var r=e.languages.markup;r&&(r.tag.addInlined("style","css"),r.tag.addAttribute("style","css"))})(Prism);const Va=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));Prism.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/};Prism.languages.python["string-interpolation"].inside.interpolation.inside.rest=Prism.languages.python;Prism.languages.py=Prism.languages.python;const qa=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));Prism.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}};Prism.languages.webmanifest=Prism.languages.json;const Za=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));(function(e){var t="\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\b",r={pattern:/(^(["']?)\w+\2)[ \t]+\S.*/,lookbehind:!0,alias:"punctuation",inside:null},n={bash:r,environment:{pattern:RegExp("\\$"+t),alias:"constant"},variable:[{pattern:/\$?\(\([\s\S]+?\)\)/,greedy:!0,inside:{variable:[{pattern:/(^\$\(\([\s\S]+)\)\)/,lookbehind:!0},/^\$\(\(/],number:/\b0x[\dA-Fa-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee]-?\d+)?/,operator:/--|\+\+|\*\*=?|<<=?|>>=?|&&|\|\||[=!+\-*/%<>^&|]=?|[?~:]/,punctuation:/\(\(?|\)\)?|,|;/}},{pattern:/\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,greedy:!0,inside:{variable:/^\$\(|^`|\)$|`$/}},{pattern:/\$\{[^}]+\}/,greedy:!0,inside:{operator:/:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,punctuation:/[\[\]]/,environment:{pattern:RegExp("(\\{)"+t),lookbehind:!0,alias:"constant"}}},/\$(?:\w+|[#?*!@$])/],entity:/\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/};e.languages.bash={shebang:{pattern:/^#!\s*\/.*/,alias:"important"},comment:{pattern:/(^|[^"{\\$])#.*/,lookbehind:!0},"function-name":[{pattern:/(\bfunction\s+)[\w-]+(?=(?:\s*\(?:\s*\))?\s*\{)/,lookbehind:!0,alias:"function"},{pattern:/\b[\w-]+(?=\s*\(\s*\)\s*\{)/,alias:"function"}],"for-or-select":{pattern:/(\b(?:for|select)\s+)\w+(?=\s+in\s)/,alias:"variable",lookbehind:!0},"assign-left":{pattern:/(^|[\s;|&]|[<>]\()\w+(?:\.\w+)*(?=\+?=)/,inside:{environment:{pattern:RegExp("(^|[\\s;|&]|[<>]\\()"+t),lookbehind:!0,alias:"constant"}},alias:"variable",lookbehind:!0},parameter:{pattern:/(^|\s)-{1,2}(?:\w+:[+-]?)?\w+(?:\.\w+)*(?=[=\s]|$)/,alias:"variable",lookbehind:!0},string:[{pattern:/((?:^|[^<])<<-?\s*)(\w+)\s[\s\S]*?(?:\r?\n|\r)\2/,lookbehind:!0,greedy:!0,inside:n},{pattern:/((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s[\s\S]*?(?:\r?\n|\r)\3/,lookbehind:!0,greedy:!0,inside:{bash:r}},{pattern:/(^|[^\\](?:\\\\)*)"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/,lookbehind:!0,greedy:!0,inside:n},{pattern:/(^|[^$\\])'[^']*'/,lookbehind:!0,greedy:!0},{pattern:/\$'(?:[^'\\]|\\[\s\S])*'/,greedy:!0,inside:{entity:n.entity}}],environment:{pattern:RegExp("\\$?"+t),alias:"constant"},variable:n.variable,function:{pattern:/(^|[\s;|&]|[<>]\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\s;|&])/,lookbehind:!0},keyword:{pattern:/(^|[\s;|&]|[<>]\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\s;|&])/,lookbehind:!0},builtin:{pattern:/(^|[\s;|&]|[<>]\()(?:\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\s;|&])/,lookbehind:!0,alias:"class-name"},boolean:{pattern:/(^|[\s;|&]|[<>]\()(?:false|true)(?=$|[)\s;|&])/,lookbehind:!0},"file-descriptor":{pattern:/\B&\d\b/,alias:"important"},operator:{pattern:/\d?<>|>\||\+=|=[=~]?|!=?|<<[<-]?|[&\d]?>>|\d[<>]&?|[<>][&=]?|&[>&]?|\|[&|]?/,inside:{"file-descriptor":{pattern:/^\d/,alias:"important"}}},punctuation:/\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,number:{pattern:/(^|\s)(?:[1-9]\d*|0)(?:[.,]\d+)?\b/,lookbehind:!0}},r.inside=e.languages.bash;for(var a=["comment","function-name","for-or-select","assign-left","parameter","string","environment","function","keyword","builtin","boolean","file-descriptor","operator","punctuation","number"],i=n.variable[1].inside,l=0;l<a.length;l++)i[a[l]]=e.languages.bash[a[l]];e.languages.sh=e.languages.bash,e.languages.shell=e.languages.bash})(Prism);const Ka=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));Prism.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};const Qa=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{be as A,Qa as B,st as C,Y as O,La as P,G as R,fe as a,ja as b,We as c,an as d,Ma as e,Qt as f,Ca as g,Na as h,ye as i,Be as j,Ha as k,Ba as l,Da as m,Ua as n,Ga as o,Ya as p,ia as q,$a as r,ka as s,Wa as t,za as u,Xa as v,Va as w,qa as x,Za as y,Ka as z};
//# sourceMappingURL=vendor-misc-BUjjPnRU.js.map
