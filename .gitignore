# Root level gitignore for DevSkills project

# Dependencies (in case they're installed at root)
node_modules/
.pnpm-store/
.pnpm-debug.log*

# Environment variables
.env
.env.local
.env.*.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# Docker
.docker/

# Temporary files
tmp/
temp/

# Backup files
*.backup
*.bak

# Archive files
*.zip
*.tar.gz
*.rar

# IDE files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Upload files - ignore all uploaded content
# Directories are created by Docker entrypoint script
uploads/
