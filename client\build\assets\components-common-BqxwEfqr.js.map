{"version": 3, "file": "components-common-BqxwEfqr.js", "sources": ["../../src/i18n/index.js", "../../src/components/common/GDPRConsent.jsx", "../../src/components/common/ScrollTopBehaviour.jsx", "../../src/components/routing/LanguageRedirect.jsx", "../../src/components/common/LanguageAwareLink.jsx", "../../src/components/common/LanguageSelector.jsx", "../../src/utils/api.jsx", "../../src/components/common/AnimatedText.jsx", "../../src/components/common/ParallaxContainer.jsx", "../../src/components/common/UnifiedSEO.jsx", "../../src/components/common/ErrorBoundary.jsx", "../../src/data/portfolio.js", "../../src/components/common/MetaComponent.jsx", "../../src/components/common/Pagination.jsx", "../../src/components/portfolio/RelatedProjects.jsx", "../../src/components/ProductGallery.jsx", "../../src/components/blog/Comments.jsx", "../../src/components/blog/commentForm/Form.jsx", "../../src/components/blog/widgets/Widget1.jsx", "../../src/components/common/Map.jsx", "../../src/components/common/SEO.jsx", "../../src/components/admin/AdminLayout.jsx", "../../src/components/editor/TipTapEditor.jsx", "../../src/components/analytics/TimeRangeSelector.jsx", "../../src/components/analytics/LanguageSelector.jsx", "../../src/components/analytics/AnalyticsOverview.jsx", "../../src/components/analytics/AnalyticsChart.jsx", "../../src/components/analytics/HeatmapChart.jsx", "../../src/components/analytics/PostsTable.jsx", "../../src/components/analytics/ConversionAnalytics.jsx", "../../src/components/analytics/StaticPagesAnalytics.jsx"], "sourcesContent": ["import i18n from \"i18next\";\nimport { initReactI18next } from \"react-i18next\";\nimport LanguageDetector from \"i18next-browser-languagedetector\";\nimport Backend from \"i18next-http-backend\";\n\n// Language configuration\nconst languages = {\n  en: { name: \"English\", flag: \"🇬🇧\" },\n  et: { name: \"<PERSON><PERSON><PERSON>\", flag: \"🇪🇪\" },\n  fi: { name: \"<PERSON><PERSON>\", flag: \"🇫🇮\" },\n  de: { name: \"<PERSON><PERSON><PERSON>\", flag: \"🇩🇪\" },\n  sv: { name: \"<PERSON><PERSON>\", flag: \"🇸🇪\" },\n};\n\n// Initialize i18next\ni18n\n  .use(Backend) // Load translations from files\n  .use(LanguageDetector) // Detect user language\n  .use(initReactI18next) // Pass i18n instance to react-i18next\n  .init({\n    // Language settings\n    lng: \"et\", // Default language (Estonian as requested)\n    fallbackLng: \"en\", // Fallback language\n    supportedLngs: Object.keys(languages),\n\n    // Namespace settings\n    ns: [\"translation\"],\n    defaultNS: \"translation\",\n\n    // Backend settings for loading translations\n    backend: {\n      loadPath: \"/locales/{{lng}}/{{ns}}.json\",\n    },\n\n    // Language detection settings\n    detection: {\n      // Order of language detection methods\n      order: [\n        \"path\", // Check URL path first (/en/, /et/, etc.)\n        \"localStorage\", // Then localStorage\n        \"navigator\", // Then browser language\n        \"htmlTag\", // Then HTML lang attribute\n      ],\n\n      // Cache user language preference\n      caches: [\"localStorage\"],\n\n      // localStorage key\n      lookupLocalStorage: \"i18nextLng\",\n\n      // Path detection settings\n      lookupFromPathIndex: 0, // Language is the first segment: /en/about\n      checkWhitelist: true, // Only allow supported languages\n\n      // Don't lookup from path by default (we'll handle this manually)\n      lookupFromPathIndex: 0,\n\n      // Check all fallbacks\n      checkWhitelist: true,\n    },\n\n    // Interpolation settings\n    interpolation: {\n      escapeValue: false, // React already escapes values\n      formatSeparator: \",\",\n      format: (value, format, lng) => {\n        if (format === \"uppercase\") return value.toUpperCase();\n        if (format === \"lowercase\") return value.toLowerCase();\n        if (format === \"capitalize\")\n          return value.charAt(0).toUpperCase() + value.slice(1);\n        return value;\n      },\n    },\n\n    // React settings\n    react: {\n      useSuspense: false, // Disable suspense for SSR compatibility\n      bindI18n: \"languageChanged\",\n      bindI18nStore: \"\",\n      transEmptyNodeValue: \"\",\n      transSupportBasicHtmlNodes: true,\n      transKeepBasicHtmlNodesFor: [\"br\", \"strong\", \"i\", \"em\", \"span\"],\n    },\n\n    // Debug settings (disable in production)\n    debug: process.env.NODE_ENV === \"development\",\n\n    // Load settings\n    load: \"languageOnly\", // Load only language codes (en, not en-US)\n    preload: Object.keys(languages), // Preload all supported languages\n\n    // Cleanup settings\n    cleanCode: true,\n\n    // Key separator (use dots for nested keys)\n    keySeparator: \".\",\n    nsSeparator: \":\",\n\n    // Pluralization\n    pluralSeparator: \"_\",\n    contextSeparator: \"_\",\n\n    // Return objects for missing keys\n    returnObjects: false,\n    returnEmptyString: false,\n    returnNull: false,\n\n    // Join arrays\n    joinArrays: false,\n\n    // Post processing\n    postProcess: false,\n\n    // Save missing keys\n    saveMissing: process.env.NODE_ENV === \"development\",\n    saveMissingTo: \"current\",\n\n    // Missing key handler\n    missingKeyHandler: (lng, ns, key, fallbackValue) => {\n      if (process.env.NODE_ENV === \"development\") {\n        console.warn(`Missing translation key: ${key} for language: ${lng}`);\n      }\n    },\n\n    // Update missing keys\n    updateMissing: false,\n\n    // Ignore JSON structure\n    ignoreJSONStructure: true,\n  });\n\n// Export language configuration\nexport { languages };\n\n// Export i18n instance\nexport default i18n;\n", "import React from \"react\";\nimport { useState, useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { useTranslation } from \"react-i18next\";\n\nconst GDPRConsent = () => {\n  const { t } = useTranslation();\n  const [showBanner, setShowBanner] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n\n  useEffect(() => {\n    // Check if instant banner was shown and customize was requested\n    const customizeRequested = document.body.classList.contains(\n      \"gdpr-customize-requested\"\n    );\n    const instantShown = document.body.classList.contains(\"gdpr-instant-shown\");\n\n    if (customizeRequested) {\n      // User clicked customize on instant banner, show details modal\n      setShowDetails(true);\n      document.body.classList.remove(\"gdpr-customize-requested\");\n    } else if (!instantShown) {\n      // No instant banner was shown (consent exists), check if we need to initialize GA\n      const consent = localStorage.getItem(\"gdpr-consent\");\n      if (consent) {\n        initializeGoogleAnalytics(JSON.parse(consent));\n      } else {\n        // Fallback: show React banner if instant banner failed\n        setShowBanner(true);\n      }\n    }\n    // If instant banner was shown, React doesn't need to show anything\n  }, []);\n\n  const initializeGoogleAnalytics = (consentSettings) => {\n    // Defer Google Analytics initialization to avoid blocking render\n    setTimeout(() => {\n      if (typeof window !== \"undefined\" && window.gtag) {\n        window.gtag(\"consent\", \"update\", {\n          analytics_storage: consentSettings.analytics ? \"granted\" : \"denied\",\n          ad_storage: consentSettings.marketing ? \"granted\" : \"denied\",\n          ad_user_data: consentSettings.marketing ? \"granted\" : \"denied\",\n          ad_personalization: consentSettings.marketing ? \"granted\" : \"denied\",\n        });\n      }\n    }, 0);\n  };\n\n  const handleAcceptAll = () => {\n    const consent = {\n      necessary: true,\n      analytics: true,\n      marketing: true,\n      timestamp: new Date().toISOString(),\n    };\n\n    localStorage.setItem(\"gdpr-consent\", JSON.stringify(consent));\n    initializeGoogleAnalytics(consent);\n    setShowBanner(false);\n\n    // Track consent acceptance (deferred)\n    setTimeout(() => {\n      if (typeof window !== \"undefined\" && window.gtag) {\n        window.gtag(\"event\", \"consent_granted\", {\n          event_category: \"GDPR\",\n          event_label: \"Accept All\",\n        });\n      }\n    }, 100);\n  };\n\n  const handleRejectAll = () => {\n    const consent = {\n      necessary: true,\n      analytics: false,\n      marketing: false,\n      timestamp: new Date().toISOString(),\n    };\n\n    localStorage.setItem(\"gdpr-consent\", JSON.stringify(consent));\n    initializeGoogleAnalytics(consent);\n    setShowBanner(false);\n\n    // Track consent rejection (deferred)\n    setTimeout(() => {\n      if (typeof window !== \"undefined\" && window.gtag) {\n        window.gtag(\"event\", \"consent_denied\", {\n          event_category: \"GDPR\",\n          event_label: \"Reject All\",\n        });\n      }\n    }, 100);\n  };\n\n  const handleCustomize = (settings) => {\n    const consent = {\n      necessary: true,\n      analytics: settings.analytics,\n      marketing: settings.marketing,\n      timestamp: new Date().toISOString(),\n    };\n\n    localStorage.setItem(\"gdpr-consent\", JSON.stringify(consent));\n    initializeGoogleAnalytics(consent);\n    setShowBanner(false);\n    setShowDetails(false);\n\n    // Track custom consent (deferred)\n    setTimeout(() => {\n      if (typeof window !== \"undefined\" && window.gtag) {\n        window.gtag(\"event\", \"consent_customized\", {\n          event_category: \"GDPR\",\n          event_label: `Analytics: ${settings.analytics}, Marketing: ${settings.marketing}`,\n        });\n      }\n    }, 100);\n  };\n\n  if (!showBanner) return null;\n\n  return (\n    <>\n      {/* Slim Bottom Banner */}\n      <div className=\"gdpr-banner gdpr-show\">\n        <div className=\"container\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-lg-8 col-md-7\">\n              <div className=\"gdpr-content\">\n                <h6 className=\"gdpr-title mb-2\">{t(\"gdpr.title\")}</h6>\n                <p className=\"gdpr-description mb-0\">{t(\"gdpr.description\")}</p>\n              </div>\n            </div>\n            <div className=\"col-lg-4 col-md-5\">\n              <div className=\"gdpr-buttons\">\n                <button\n                  className=\"btn btn-mod btn-small btn-w btn-circle gdpr-btn-accept\"\n                  onClick={handleAcceptAll}\n                  data-btn-animate=\"y\"\n                >\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {t(\"gdpr.acceptAll\")}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {t(\"gdpr.acceptAll\")}\n                    </span>\n                  </span>\n                </button>\n                <button\n                  className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                  onClick={handleRejectAll}\n                  data-btn-animate=\"y\"\n                >\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {t(\"gdpr.rejectAll\")}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {t(\"gdpr.rejectAll\")}\n                    </span>\n                  </span>\n                </button>\n                <button\n                  className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                  onClick={() => setShowDetails(true)}\n                  data-btn-animate=\"y\"\n                >\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {t(\"gdpr.customize\")}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {t(\"gdpr.customize\")}\n                    </span>\n                  </span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detailed Settings Modal */}\n      {showDetails && (\n        <GDPRDetailsModal\n          onClose={() => setShowDetails(false)}\n          onSave={handleCustomize}\n          t={t}\n        />\n      )}\n    </>\n  );\n};\n\nconst GDPRDetailsModal = ({ onClose, onSave, t }) => {\n  const [settings, setSettings] = useState({\n    analytics: false,\n    marketing: false,\n  });\n\n  const handleSave = () => {\n    onSave(settings);\n  };\n\n  return (\n    <div className=\"gdpr-modal\">\n      <div className=\"gdpr-modal-content\">\n        <div className=\"container\">\n          <div className=\"row\">\n            <div className=\"col-lg-6 offset-lg-3\">\n              <div className=\"gdpr-modal-inner\">\n                <div className=\"gdpr-modal-header mb-30\">\n                  <div className=\"d-flex justify-content-between align-items-center\">\n                    <h5 className=\"gdpr-modal-title mb-0\">\n                      {t(\"gdpr.customizeTitle\")}\n                    </h5>\n                    <button className=\"gdpr-close\" onClick={onClose}>\n                      <i className=\"mi-close size-18\"></i>\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"gdpr-modal-body\">\n                  <div className=\"gdpr-category mb-25\">\n                    <div className=\"gdpr-category-header mb-10\">\n                      <div className=\"d-flex justify-content-between align-items-center\">\n                        <h6 className=\"gdpr-category-title mb-0 mr-2\">\n                          {t(\"gdpr.necessary\")}\n                        </h6>\n                        <span className=\"gdpr-required-badge\">\n                          {t(\"gdpr.required\")}\n                        </span>\n                      </div>\n                    </div>\n                    <p className=\"gdpr-category-desc\">\n                      {t(\"gdpr.necessaryDesc\")}\n                    </p>\n                  </div>\n\n                  <div className=\"gdpr-category mb-25\">\n                    <div className=\"gdpr-category-header mb-10\">\n                      <div className=\"d-flex justify-content-between align-items-center\">\n                        <h6 className=\"gdpr-category-title mb-0\">\n                          {t(\"gdpr.analytics\")}\n                        </h6>\n                        <label className=\"gdpr-switch\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.analytics}\n                            onChange={(e) =>\n                              setSettings((prev) => ({\n                                ...prev,\n                                analytics: e.target.checked,\n                              }))\n                            }\n                          />\n                          <span className=\"gdpr-slider\"></span>\n                        </label>\n                      </div>\n                    </div>\n                    <p className=\"gdpr-category-desc\">\n                      {t(\"gdpr.analyticsDesc\")}\n                    </p>\n                  </div>\n\n                  <div className=\"gdpr-category mb-30\">\n                    <div className=\"gdpr-category-header mb-10\">\n                      <div className=\"d-flex justify-content-between align-items-center\">\n                        <h6 className=\"gdpr-category-title mb-0\">\n                          {t(\"gdpr.marketing\")}\n                        </h6>\n                        <label className=\"gdpr-switch\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.marketing}\n                            onChange={(e) =>\n                              setSettings((prev) => ({\n                                ...prev,\n                                marketing: e.target.checked,\n                              }))\n                            }\n                          />\n                          <span className=\"gdpr-slider\"></span>\n                        </label>\n                      </div>\n                    </div>\n                    <p className=\"gdpr-category-desc\">\n                      {t(\"gdpr.marketingDesc\")}\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"gdpr-modal-footer\">\n                  <div className=\"d-flex justify-content-end gap-2\">\n                    <button\n                      className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                      onClick={onClose}\n                      data-btn-animate=\"y\"\n                    >\n                      <span className=\"btn-animate-y\">\n                        <span className=\"btn-animate-y-1\">\n                          {t(\"gdpr.cancel\")}\n                        </span>\n                        <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                          {t(\"gdpr.cancel\")}\n                        </span>\n                      </span>\n                    </button>\n                    <button\n                      className=\"btn btn-mod btn-small btn-w btn-circle\"\n                      onClick={handleSave}\n                      data-btn-animate=\"y\"\n                    >\n                      <span className=\"btn-animate-y\">\n                        <span className=\"btn-animate-y-1\">\n                          {t(\"gdpr.savePreferences\")}\n                        </span>\n                        <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                          {t(\"gdpr.savePreferences\")}\n                        </span>\n                      </span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nGDPRDetailsModal.propTypes = {\n  onClose: PropTypes.func.isRequired,\n  onSave: PropTypes.func.isRequired,\n  t: PropTypes.func.isRequired,\n};\n\nexport default GDPRConsent;\n", "import React from \"react\";\nimport { useEffect } from \"react\";\nimport { useLocation } from \"react-router-dom\";\n\nexport default function ScrollTopBehaviour() {\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    window.scrollTo(0, 0);\n  }, [pathname]);\n\n  return <></>;\n}\n", "import React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\n\nconst SUPPORTED_LANGUAGES = [\"en\", \"et\", \"fi\", \"de\", \"sv\"];\nconst DEFAULT_LANGUAGE = \"et\";\n\nconst LanguageRedirect = ({ children }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { i18n } = useTranslation();\n\n  useEffect(() => {\n    // Get user's preferred language\n    const browserLang = navigator.language.split(\"-\")[0];\n    const storedLang = localStorage.getItem(\"i18nextLng\");\n\n    // Determine the best language to use\n    const preferredLang = SUPPORTED_LANGUAGES.includes(storedLang)\n      ? storedLang\n      : SUPPORTED_LANGUAGES.includes(browserLang)\n      ? browserLang\n      : DEFAULT_LANGUAGE;\n\n    // Set the language in i18next\n    if (i18n.language !== preferredLang) {\n      i18n.changeLanguage(preferredLang);\n    }\n\n    // Get current path and add language prefix\n    const currentPath = location.pathname;\n    const searchParams = location.search;\n\n    // If we're on root, redirect to language home\n    if (currentPath === \"/\") {\n      navigate(`/${preferredLang}${searchParams}`, { replace: true });\n    } else {\n      // For other paths, add language prefix\n      navigate(`/${preferredLang}${currentPath}${searchParams}`, {\n        replace: true,\n      });\n    }\n  }, [navigate, location, i18n]);\n\n  // Don't render children during redirect\n  return null;\n};\n\nexport default LanguageRedirect;\n", "import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\n// Supported languages\nconst SUPPORTED_LANGUAGES = ['en', 'et', 'fi', 'de', 'sv'];\n\n// Get current language from URL\nconst getCurrentLanguage = (pathname) => {\n  const match = pathname.match(/^\\/([a-z]{2})\\//);\n  return match ? match[1] : 'et'; // Default to Estonian\n};\n\n// Create language-aware URL\nconst createLanguageUrl = (to, currentLanguage) => {\n  // If the 'to' already has a language prefix, use it as is\n  if (to.match(/^\\/[a-z]{2}\\//)) {\n    return to;\n  }\n  \n  // If it's a root path, just add language\n  if (to === '/') {\n    return `/${currentLanguage}`;\n  }\n  \n  // Add language prefix to the path\n  const cleanPath = to.startsWith('/') ? to : `/${to}`;\n  return `/${currentLanguage}${cleanPath}`;\n};\n\n// Language-aware Link component\nconst LanguageAwareLink = ({ to, children, className, ...props }) => {\n  const location = useLocation();\n  const currentLanguage = getCurrentLanguage(location.pathname);\n  const languageAwareUrl = createLanguageUrl(to, currentLanguage);\n\n  return (\n    <Link to={languageAwareUrl} className={className} {...props}>\n      {children}\n    </Link>\n  );\n};\n\nexport default LanguageAwareLink;\nexport { getCurrentLanguage, createLanguageUrl, SUPPORTED_LANGUAGES };\n", "import React, { useState, useRef, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { languages } from \"@/i18n\";\nimport { createLanguageUrl } from \"./LanguageAwareLink\";\n\nexport default function LanguageSelector() {\n  const { i18n } = useTranslation();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef(null);\n  const [isMobile, setIsMobile] = useState(\n    typeof window !== \"undefined\" ? window.innerWidth <= 1024 : false\n  );\n\n  // Update isMobile state on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 1024);\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => {\n      window.removeEventListener(\"resize\", handleResize);\n    };\n  }, []);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    }\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  const toggleDropdown = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const handleLanguageChange = (langCode) => {\n    // Change the language in i18next\n    i18n.changeLanguage(langCode);\n    setIsOpen(false);\n\n    // Update the URL to reflect the new language\n    const currentPath = location.pathname;\n    const searchParams = location.search;\n\n    // Replace the current language in the URL with the new one\n    let newPath;\n    if (currentPath.match(/^\\/[a-z]{2}(\\/|$)/)) {\n      // Replace existing language prefix\n      newPath = currentPath.replace(/^\\/[a-z]{2}/, `/${langCode}`);\n      // Handle case where we're on language root (e.g., /et -> /en)\n      if (newPath === `/${langCode}`) {\n        newPath = `/${langCode}/`;\n      }\n    } else {\n      // Add language prefix to current path\n      newPath = createLanguageUrl(currentPath, langCode);\n    }\n\n    // Navigate to the new URL (preserve search params)\n    navigate(`${newPath}${searchParams}`, { replace: true });\n\n    // Track language change in analytics\n    if (typeof window !== \"undefined\" && window.gtag) {\n      window.gtag(\"event\", \"language_change\", {\n        event_category: \"User Interaction\",\n        event_label: `${i18n.language} to ${langCode}`,\n        from_language: i18n.language,\n        to_language: langCode,\n        send_to: \"G-8NEGL4LL8Q\",\n      });\n    }\n  };\n\n  const currentLanguage = i18n.language || \"et\";\n\n  // Mobile view - show languages side by side\n  if (isMobile) {\n    return (\n      <div className=\"language-selector-mobile d-flex align-items-center\">\n        {Object.entries(languages).map(([langCode, langData]) => (\n          <button\n            key={langCode}\n            className={`btn btn-link p-0 mx-2 ${\n              langCode === currentLanguage\n                ? \"text-white\"\n                : \"text-muted opacity-50\"\n            }`}\n            onClick={() => handleLanguageChange(langCode)}\n            aria-label={`Switch to ${langData.name}`}\n          >\n            <span className=\"language-code text-uppercase font-weight-bold\">\n              {langCode}\n            </span>\n          </button>\n        ))}\n      </div>\n    );\n  }\n\n  // Desktop view - dropdown\n  return (\n    <div className=\"language-selector position-relative\" ref={dropdownRef}>\n      <button\n        className=\"language-toggle btn btn-link p-0 d-flex align-items-center\"\n        onClick={toggleDropdown}\n        aria-expanded={isOpen}\n        aria-haspopup=\"true\"\n      >\n        <span className=\"language-code text-uppercase\">{currentLanguage}</span>\n      </button>\n\n      {isOpen && (\n        <div className=\"language-dropdown position-absolute bg-dark-1 py-2 rounded shadow-sm\">\n          {Object.entries(languages).map(([langCode, langData]) => (\n            <button\n              key={langCode}\n              className={`dropdown-item d-flex align-items-center px-3 py-1 ${\n                langCode === currentLanguage ? \"active\" : \"\"\n              }`}\n              onClick={() => handleLanguageChange(langCode)}\n            >\n              {/* <span className=\"me-2\">{langData.flag}</span> */}\n              <span className=\"language-name\">{langData.name}</span>\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n", "// client/src/utils/api.jsx\n// API configuration for both development and production\n\nconst getApiBaseUrl = () => {\n  // In production, both frontend and backend are served from the same domain\n  if (import.meta.env.PROD) {\n    return \"https://devskills.ee/api\";\n  }\n\n  // In development, backend runs on port 4004\n  return \"http://localhost:4004/api\";\n};\n\nexport const API_BASE_URL = getApiBaseUrl();\n\n// Helper function to make API calls with proper headers\nexport const apiCall = async (endpoint, options = {}) => {\n  const url = `${API_BASE_URL}${endpoint}`;\n\n  // Check if this is a FormData request (file upload)\n  const isFormData = options.body instanceof FormData;\n\n  const defaultHeaders = {};\n\n  // Only set Content-Type for non-FormData requests\n  // FormData requests need the browser to set the Content-Type with boundary\n  if (!isFormData) {\n    defaultHeaders[\"Content-Type\"] = \"application/json\";\n  }\n\n  // Add API key for contact form\n  if (endpoint.includes(\"/contact\")) {\n    defaultHeaders[\"X-API-Key\"] = \"9afe34d2134b43e19163c50924df6714\";\n  }\n\n  // Add auth token if available\n  const token = localStorage.getItem(\"adminToken\");\n  if (token) {\n    defaultHeaders[\"Authorization\"] = `Bearer ${token}`;\n  }\n\n  const config = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  try {\n    const response = await fetch(url, config);\n\n    // Handle non-JSON responses (like file uploads)\n    const contentType = response.headers.get(\"content-type\");\n    if (contentType && contentType.includes(\"application/json\")) {\n      const data = await response.json();\n      return { response, data };\n    } else {\n      return { response, data: null };\n    }\n  } catch (error) {\n    console.error(\"API call failed:\", error);\n    throw error;\n  }\n};\n\n// Specific API functions\nexport const authAPI = {\n  login: (credentials) =>\n    apiCall(\"/auth/login\", {\n      method: \"POST\",\n      body: JSON.stringify(credentials),\n    }),\n\n  getMe: () => apiCall(\"/auth/me\"),\n\n  logout: () => apiCall(\"/auth/logout\", { method: \"POST\" }),\n};\n\nexport const blogAPI = {\n  getPosts: (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    return apiCall(`/blog${queryString ? `?${queryString}` : \"\"}`);\n  },\n\n  getPost: (slug) => apiCall(`/blog/${slug}`),\n\n  createPost: (formData) =>\n    apiCall(\"/blog\", {\n      method: \"POST\",\n      body: formData, // FormData for file upload\n      headers: {}, // Let browser set Content-Type for FormData\n    }),\n\n  updatePost: (id, formData) =>\n    apiCall(`/blog/${id}`, {\n      method: \"PUT\",\n      body: formData,\n      headers: {},\n    }),\n\n  deletePost: (id) => apiCall(`/blog/${id}`, { method: \"DELETE\" }),\n\n  toggleVisibility: (id) =>\n    apiCall(`/blog/${id}/toggle-visibility`, {\n      method: \"PATCH\",\n    }),\n\n  // Helper function to get posts for homepage (featured posts)\n  getFeaturedPosts: (language = \"en\", limit = 3) => {\n    return apiCall(\n      `/blog?featured=true&limit=${limit}&published=true&language=${language}`\n    );\n  },\n\n  // Helper function to get posts for blog listing page\n  getBlogPosts: (params = {}) => {\n    // Set default values\n    const defaultParams = {\n      language: \"en\",\n      page: 1,\n      limit: 9,\n      published: \"true\",\n    };\n\n    // Merge with provided params\n    const finalParams = { ...defaultParams, ...params };\n    const queryString = new URLSearchParams(finalParams).toString();\n\n    return apiCall(`/blog?${queryString}`);\n  },\n\n  // Create a comment on a blog post\n  createComment: (slug, commentData) =>\n    apiCall(`/blog/${slug}/comments`, {\n      method: \"POST\",\n      body: JSON.stringify(commentData),\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n    }),\n};\n\nexport const adminAPI = {\n  getDashboard: () => apiCall(\"/admin/dashboard\"),\n\n  // Analytics endpoints\n  getBlogAnalytics: (timeRange = \"last30days\", language = \"all\") =>\n    apiCall(\n      `/admin/analytics/blog?timeRange=${timeRange}&language=${language}`\n    ),\n\n  getBlogPostsAnalytics: (timeRange = \"last30days\", language = \"all\") =>\n    apiCall(\n      `/admin/analytics/posts?timeRange=${timeRange}&language=${language}`\n    ),\n\n  getConversionAnalytics: (startDate, endDate, language = \"all\") =>\n    apiCall(\n      `/admin/analytics/conversions?startDate=${startDate}&endDate=${endDate}&language=${language}`\n    ),\n\n  getStaticPagesAnalytics: (timeRange = \"last30days\", language = \"all\") =>\n    apiCall(\n      `/admin/analytics/pages?timeRange=${timeRange}&language=${language}`\n    ),\n\n  getPosts: (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    return apiCall(`/admin/posts${queryString ? `?${queryString}` : \"\"}`);\n  },\n\n  getPost: (id) => apiCall(`/admin/posts/${id}`),\n\n  uploadImage: (file) => {\n    const formData = new FormData();\n    formData.append(\"image\", file);\n    return apiCall(\"/admin/upload-image\", {\n      method: \"POST\",\n      body: formData,\n      headers: {},\n    });\n  },\n\n  getCategories: () => apiCall(\"/admin/categories\"),\n\n  createCategory: (category) =>\n    apiCall(\"/admin/categories\", {\n      method: \"POST\",\n      body: JSON.stringify(category),\n    }),\n\n  updateCategory: (id, category) =>\n    apiCall(`/admin/categories/${id}`, {\n      method: \"PUT\",\n      body: JSON.stringify(category),\n    }),\n\n  deleteCategory: (id) =>\n    apiCall(`/admin/categories/${id}`, {\n      method: \"DELETE\",\n    }),\n\n  getTags: () => apiCall(\"/admin/tags\"),\n\n  createTag: (tag) =>\n    apiCall(\"/admin/tags\", {\n      method: \"POST\",\n      body: JSON.stringify(tag),\n    }),\n\n  updateTag: (id, tag) =>\n    apiCall(`/admin/tags/${id}`, {\n      method: \"PUT\",\n      body: JSON.stringify(tag),\n    }),\n\n  deleteTag: (id) =>\n    apiCall(`/admin/tags/${id}`, {\n      method: \"DELETE\",\n    }),\n\n  // Product endpoints\n  getProducts: (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    return apiCall(`/admin/products${queryString ? `?${queryString}` : \"\"}`);\n  },\n\n  getProduct: (id) => apiCall(`/admin/products/${id}`),\n\n  createProduct: (productData) =>\n    apiCall(\"/admin/products\", {\n      method: \"POST\",\n      body: productData, // FormData for file uploads\n      headers: {}, // Let browser set Content-Type for FormData\n    }),\n\n  updateProduct: (id, productData) =>\n    apiCall(`/admin/products/${id}`, {\n      method: \"PUT\",\n      body: productData, // FormData for file uploads\n      headers: {}, // Let browser set Content-Type for FormData\n    }),\n\n  deleteProduct: (id) =>\n    apiCall(`/admin/products/${id}`, {\n      method: \"DELETE\",\n    }),\n};\n\n// Public API for products\nexport const productsAPI = {\n  getProducts: (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    return apiCall(`/products${queryString ? `?${queryString}` : \"\"}`);\n  },\n\n  getProduct: (slug, language = \"en\") =>\n    apiCall(`/products/${slug}?language=${language}`),\n};\n\n// Public API for categories\nexport const categoriesAPI = {\n  getCategories: () => apiCall(\"/categories\"),\n};\n\n// Public API for tags\nexport const tagsAPI = {\n  getTags: () => apiCall(\"/tags\"),\n};\n\n// Public API for blog archive\nexport const archiveAPI = {\n  getArchive: () => apiCall(\"/blog/archive\"),\n};\n\nexport const contactAPI = {\n  sendMessage: (message) =>\n    apiCall(\"/contact\", {\n      method: \"POST\",\n      body: JSON.stringify(message),\n    }),\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nexport default function AnimatedText({\n  text = \"Grow your business with a new website.\",\n}) {\n  return (\n    <>\n      <span\n        className=\"wow charsAnimIn words chars splitting\"\n        data-splitting=\"chars\"\n        aria-hidden=\"true\"\n        style={{\n          \"--word-total\": text.split(\" \").length,\n          \"--char-total\": text.split(\"\").length,\n          visibility: \"visible\",\n        }}\n      >\n        {text\n          .trim()\n          .split(\" \")\n          .map((elm, i) => (\n            <React.Fragment key={i}>\n              <span\n                className=\"word\"\n                data-word=\"Grow\"\n                style={{ \"--word-index\": i }}\n              >\n                {elm.split(\"\").map((elm2, i2) => (\n                  <span\n                    key={i2}\n                    className=\"char\"\n                    data-char=\"G\"\n                    style={{ \"--char-index\": i + i2 }}\n                  >\n                    {elm2}\n                  </span>\n                ))}\n              </span>\n              <span className=\"whitespace\"> </span>\n            </React.Fragment>\n          ))}\n      </span>\n    </>\n  );\n}\n\nAnimatedText.propTypes = {\n  text: PropTypes.string,\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { jarallax } from \"jarallax\";\nimport { useEffect } from \"react\";\n\nexport default function ParallaxContainer(props) {\n  useEffect(() => {\n    jarallax(document.querySelectorAll(\".parallax-5\"), {\n      speed: 0.5,\n    });\n  }, []);\n  return (\n    <div\n      //   ref={parallax.ref}\n      {...props}\n    >\n      {props.children}\n    </div>\n  );\n}\n\nParallaxContainer.propTypes = {\n  children: PropTypes.node,\n};\n", "// client/src/components/common/UnifiedSEO.jsx\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Helmet } from \"react-helmet-async\";\nimport { useTranslation } from \"react-i18next\";\n\n/**\n * Unified SEO component following 2025 best practices\n * Combines functionality from SEO.jsx, MultilingualSEO.jsx, and MetaComponent.jsx\n *\n * Features:\n * - Multilingual support with hreflang tags\n * - Local business schema markup\n * - AI Overview optimization\n * - Core Web Vitals meta tags\n * - Enhanced social media support\n * - 2025 SEO best practices\n *\n * @param {Object} props - Component props\n * @param {string} props.title - Page title (without brand suffix)\n * @param {string} props.description - Page description\n * @param {string} props.slug - Page slug for generating language URLs\n * @param {string} props.type - Page type (website, article, product, etc.)\n * @param {Object|Array} props.schema - JSON-LD structured data\n * @param {string[]} props.keywords - Keywords for SEO\n * @param {string} props.image - OG image URL\n * @param {string} props.imageAlt - Alt text for the OG image\n * @param {string} props.author - Content author\n * @param {string} props.publishedAt - Published date (ISO format)\n * @param {string} props.modifiedAt - Modified date (ISO format)\n * @param {Object} props.alternateUrls - Custom alternate URLs for languages\n * @param {boolean} props.noIndex - Whether to prevent indexing\n * @param {string} props.canonicalUrl - Custom canonical URL\n */\nconst UnifiedSEO = ({\n  title,\n  description,\n  slug = \"\",\n  type = \"website\",\n  schema = null,\n  keywords = [],\n  image = \"https://devskills.ee/home.jpg\",\n  imageAlt = null,\n  author = \"DevSkills\",\n  publishedAt = \"\",\n  modifiedAt = \"\",\n  alternateUrls = null,\n  noIndex = false,\n  canonicalUrl = null,\n}) => {\n  const { i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"en\";\n\n  // Base URL and business information\n  const baseUrl = \"https://devskills.ee\";\n  const businessName = \"DevSkills\";\n\n  // Generate language-specific URLs\n  const generateLanguageUrls = () => {\n    if (alternateUrls) {\n      return alternateUrls;\n    }\n\n    const supportedLanguages = [\"en\", \"et\", \"fi\", \"de\", \"sv\"];\n    const urls = {};\n\n    supportedLanguages.forEach((lang) => {\n      if (slug) {\n        urls[lang] = `${baseUrl}/${lang}/${slug}`;\n      } else {\n        urls[lang] = `${baseUrl}/${lang}`;\n      }\n    });\n\n    return urls;\n  };\n\n  const languageUrls = generateLanguageUrls();\n  const currentUrl =\n    canonicalUrl ||\n    languageUrls[currentLanguage] ||\n    `${baseUrl}/${currentLanguage}`;\n\n  // Language-specific meta data\n  const getLanguageSpecificData = () => {\n    const localeMap = {\n      en: \"en_US\",\n      et: \"et_EE\",\n      fi: \"fi_FI\",\n      de: \"de_DE\",\n      sv: \"sv_SE\",\n    };\n\n    const languageMap = {\n      en: \"English\",\n      et: \"Estonian\",\n      fi: \"Finnish\",\n      de: \"German\",\n      sv: \"Swedish\",\n    };\n\n    return {\n      locale: localeMap[currentLanguage] || \"en_US\",\n      language: languageMap[currentLanguage] || \"English\",\n    };\n  };\n\n  const { locale, language } = getLanguageSpecificData();\n\n  // Format the title with brand suffix\n  const formattedTitle = title\n    ? `${title} | ${businessName}`\n    : `${businessName} - Professional Software Development Services`;\n\n  // Default image alt text\n  const defaultImageAlt =\n    imageAlt ||\n    `${businessName} - ${\n      title || \"Professional Software Development Services\"\n    }`;\n\n  // Robots directive\n  const robotsContent = noIndex\n    ? \"noindex, nofollow\"\n    : \"index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1\";\n\n  return (\n    <Helmet>\n      {/* Basic meta tags */}\n      <title>{formattedTitle}</title>\n      <meta name=\"description\" content={description} />\n      <link rel=\"canonical\" href={currentUrl} />\n      {keywords.length > 0 && (\n        <meta name=\"keywords\" content={keywords.join(\", \")} />\n      )}\n      <meta name=\"language\" content={language} />\n      <meta httpEquiv=\"Content-Language\" content={currentLanguage} />\n\n      {/* Hreflang tags for multilingual SEO */}\n      {Object.entries(languageUrls).map(([lang, url]) => (\n        <link key={lang} rel=\"alternate\" hrefLang={lang} href={url} />\n      ))}\n\n      {/* x-default for international targeting */}\n      <link\n        rel=\"alternate\"\n        hrefLang=\"x-default\"\n        href={languageUrls.en || currentUrl}\n      />\n\n      {/* Open Graph meta tags */}\n      <meta property=\"og:title\" content={formattedTitle} />\n      <meta property=\"og:description\" content={description} />\n      <meta property=\"og:type\" content={type} />\n      <meta property=\"og:url\" content={currentUrl} />\n      <meta property=\"og:image\" content={image} />\n      <meta property=\"og:image:alt\" content={defaultImageAlt} />\n      <meta property=\"og:image:width\" content=\"1200\" />\n      <meta property=\"og:image:height\" content=\"630\" />\n      <meta property=\"og:site_name\" content={businessName} />\n      <meta property=\"og:locale\" content={locale} />\n\n      {/* Facebook App ID for Facebook Debugger - Replace with your actual Facebook App ID */}\n      <meta property=\"fb:app_id\" content=\"YOUR_FACEBOOK_APP_ID\" />\n\n      {/* Alternate locales for Facebook */}\n      {Object.keys(languageUrls)\n        .filter((lang) => lang !== currentLanguage)\n        .map((lang) => {\n          const altLocale = {\n            en: \"en_US\",\n            et: \"et_EE\",\n            fi: \"fi_FI\",\n            de: \"de_DE\",\n            sv: \"sv_SE\",\n          }[lang];\n          return (\n            <meta\n              key={lang}\n              property=\"og:locale:alternate\"\n              content={altLocale}\n            />\n          );\n        })}\n\n      {/* Article specific meta tags */}\n      {type === \"article\" && publishedAt && (\n        <meta property=\"article:published_time\" content={publishedAt} />\n      )}\n      {type === \"article\" && modifiedAt && (\n        <meta property=\"article:modified_time\" content={modifiedAt} />\n      )}\n      {type === \"article\" && author && (\n        <meta property=\"article:author\" content={author} />\n      )}\n\n      {/* LinkedIn Author and Publication Date */}\n      <meta name=\"author\" content={author || businessName} />\n      {publishedAt && <meta name=\"publish_date\" content={publishedAt} />}\n      {!publishedAt && (\n        <meta name=\"publish_date\" content={new Date().toISOString()} />\n      )}\n\n      {/* Twitter Card meta tags */}\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\n      <meta name=\"twitter:site\" content=\"@DevSkillsEE\" />\n      <meta name=\"twitter:creator\" content=\"@DevSkillsEE\" />\n      <meta name=\"twitter:title\" content={formattedTitle} />\n      <meta name=\"twitter:description\" content={description} />\n      <meta name=\"twitter:image\" content={image} />\n      <meta name=\"twitter:image:alt\" content={defaultImageAlt} />\n\n      {/* SEO and crawler directives */}\n      <meta name=\"robots\" content={robotsContent} />\n      <meta name=\"googlebot\" content={robotsContent} />\n      <meta httpEquiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\n      {/* Core Web Vitals and Performance hints */}\n      <meta\n        name=\"viewport\"\n        content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\"\n      />\n      <meta name=\"theme-color\" content=\"#06B6D4\" />\n      <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n      <link\n        rel=\"preconnect\"\n        href=\"https://fonts.gstatic.com\"\n        crossOrigin=\"anonymous\"\n      />\n      <link rel=\"dns-prefetch\" href=\"//www.google-analytics.com\" />\n\n      {/* Mobile and PWA meta tags */}\n      <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n      <meta\n        name=\"apple-mobile-web-app-status-bar-style\"\n        content=\"black-translucent\"\n      />\n      <meta name=\"apple-mobile-web-app-title\" content={businessName} />\n      <meta name=\"application-name\" content={businessName} />\n      <meta name=\"msapplication-TileColor\" content=\"#06B6D4\" />\n\n      {/* AI Overview optimization */}\n      <meta name=\"AI-generated\" content=\"false\" />\n      <meta name=\"content-type\" content=\"original\" />\n\n      {/* JSON-LD structured data */}\n      {schema && Array.isArray(schema) ? (\n        // Handle array of schema objects\n        schema.map((schemaItem, index) => (\n          <script key={index} type=\"application/ld+json\">\n            {JSON.stringify({\n              ...schemaItem,\n              \"@context\": \"https://schema.org\",\n              inLanguage: currentLanguage,\n              url: currentUrl,\n            })}\n          </script>\n        ))\n      ) : schema ? (\n        // Handle single schema object\n        <script type=\"application/ld+json\">\n          {JSON.stringify({\n            ...schema,\n            \"@context\": \"https://schema.org\",\n            inLanguage: currentLanguage,\n            url: currentUrl,\n          })}\n        </script>\n      ) : null}\n    </Helmet>\n  );\n};\n\nUnifiedSEO.propTypes = {\n  title: PropTypes.string.isRequired,\n  description: PropTypes.string.isRequired,\n  slug: PropTypes.string,\n  type: PropTypes.string,\n  schema: PropTypes.oneOfType([\n    PropTypes.object,\n    PropTypes.arrayOf(PropTypes.object),\n  ]),\n  keywords: PropTypes.arrayOf(PropTypes.string),\n  image: PropTypes.string,\n  imageAlt: PropTypes.string,\n  author: PropTypes.string,\n  publishedAt: PropTypes.string,\n  modifiedAt: PropTypes.string,\n  alternateUrls: PropTypes.object,\n  noIndex: PropTypes.bool,\n  canonicalUrl: PropTypes.string,\n};\n\nexport default UnifiedSEO;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error for debugging\n    console.error(\"Error caught by boundary:\", error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Render fallback UI that doesn't crash\n      return (\n        <div\n          style={{\n            padding: \"20px\",\n            textAlign: \"center\",\n            color: \"#fff\",\n            backgroundColor: \"#1a1a1a\",\n            minHeight: \"100vh\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            flexDirection: \"column\",\n          }}\n        >\n          <h2>Something went wrong</h2>\n          <p>The page is recovering...</p>\n          <button\n            onClick={() => window.location.reload()}\n            style={{\n              padding: \"10px 20px\",\n              backgroundColor: \"#06B6D4\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"4px\",\n              cursor: \"pointer\",\n              marginTop: \"20px\",\n            }}\n          >\n            Reload Page\n          </button>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nErrorBoundary.propTypes = {\n  children: PropTypes.node.isRequired,\n};\n\nexport default ErrorBoundary;\n", "export const portfolioItems = [\n  {\n    id: 1,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/1.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Rise of Design\",\n    descr: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 2,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/2.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Amplitude\",\n    descr: \"UI/UX Design, Development\",\n  },\n  {\n    id: 3,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/3.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Medium Scene\",\n    descr: \"Branding, Design\",\n  },\n  {\n    id: 4,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/4.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Rise of Design\",\n    descr: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 5,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/5.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Amplitude\",\n    descr: \"UI/UX Design, Development\",\n  },\n  {\n    id: 6,\n    href: \"strong-portfolio-single.html\",\n    imgSrc: \"/assets/images/demo-strong/portfolio/6.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Medium Scene\",\n    descr: \"Branding, Design\",\n  },\n];\n\nexport const portfolios1 = [\n  {\n    id: 7,\n    className: \"work-item mt-90 mt-md-0 mix development\",\n    href: \"/assets/images/portfolio/masonry/full-project-1.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-1.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 8,\n    className: \"work-item mix branding design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-2.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n  },\n  {\n    id: 9,\n    className: \"work-item mt-90 mt-md-0 mix branding\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-3.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n  },\n  {\n    id: 10,\n    className: \"work-item mix design development\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-4.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 11,\n    className: \"work-item mix design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-5.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n  {\n    id: 12,\n    className: \"work-item mix design branding\",\n    href: \"/assets/images/portfolio/masonry/full-project-6.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 13,\n    className: \"work-item mix mix design\",\n    href: \"/assets/images/portfolio/masonry/full-project-7.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 14,\n    className: \"work-item mix design development\",\n    href: \"/assets/images/portfolio/masonry/full-project-8.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n];\n\nexport const portfolios2 = [\n  {\n    id: 15,\n    imageUrl: \"/assets/images/demo-bold/portfolio/1.jpg\",\n    title: \"Medium Scene\",\n    description:\n      \"Lorem ipsum dolor siter amet consectetur adipiscing elit sed do eiusmod tempor incididunt labore dolore magna aliqua.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"development\"],\n  },\n  {\n    id: 16,\n    imageUrl: \"/assets/images/demo-bold/portfolio/2.jpg\",\n    title: \"Rise of Design\",\n    description:\n      \"Proin elementum ipsum vel mauris pellentesque accumsan. Nulla in erat ligula vivamus sed egestas elit, sit amet convallis metus.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"branding\"],\n  },\n  {\n    id: 17,\n    imageUrl: \"/assets/images/demo-bold/portfolio/3.jpg\",\n    title: \"Visual Stranger\",\n    description:\n      \"Suspendisse scelerisque convallis nibh. Maecenas bibendum porta mattis. Donec quis nibh porta dolor ultrices bibendum vel quis leo.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"design\", \"development\"],\n  },\n  {\n    id: 18,\n    imageUrl: \"/assets/images/demo-bold/portfolio/4.jpg\",\n    title: \"Amplitude\",\n    description:\n      \"Aliquam tempus nunc nec rutrum malesuada. Proin pulvinar augue quis pharetra vulputate. Sed lacinia convallis orci vitae condimentum.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"branding\", \"design\"],\n  },\n  {\n    id: 19,\n    imageUrl: \"/assets/images/demo-bold/portfolio/5.jpg\",\n    title: \"Super Awards\",\n    description:\n      \"Praesent est lacus, fringilla et justo vel, scelerisque aliquet elit. Mauris malesuada eleifend sapien irere semper a orci ac turpis luctus.\",\n    link: \"bold-portfolio-single.html\",\n    categories: [\"design\", \"development\"],\n  },\n];\n\nexport const portfolios3 = [\n  {\n    id: 20,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/1.jpg\",\n    imgWidth: 700,\n    imgHeight: 848,\n    title: \"Medium Scene\",\n    description:\n      \"Take maximus ligula semper metus pellente mattis. Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet adipiscing elit.\",\n  },\n  {\n    id: 21,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/2.jpg\",\n    imgWidth: 848,\n    imgHeight: 700,\n    title: \"Rise of Design\",\n    description:\n      \"Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet, cetere adipiscing elit. Maximus ligula semper metus pellentesque mattis.\",\n  },\n  {\n    id: 22,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/3.jpg\",\n    imgWidth: 700,\n    imgHeight: 848,\n    title: \"Visual Stranger\",\n    description:\n      \"Curabitur iaculis accumsan augue, finibus mauris pretium eu. Duis placerat ex gravida nibh tristique porta nulla facilisi.\",\n  },\n  {\n    id: 23,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/4.jpg\",\n    imgWidth: 848,\n    imgHeight: 700,\n    title: \"Rise of Design\",\n    description:\n      \"Take maximus ligula semper metus pellente mattis. Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet adipiscing elit.\",\n  },\n  {\n    id: 24,\n    imgSrc: \"/assets/images/demo-brutalist/portfolio/5.jpg\",\n    imgWidth: 700,\n    imgHeight: 848,\n    title: \"Amplitude\",\n    description:\n      \"Posuere felis id arcu blandit sagittis. Eleifeni vestibulum purus, sit amet vulputate risusece fusce aliquet quam eget neque.\",\n  },\n];\n\nexport const portfolios4 = [\n  {\n    id: 25,\n    imageSrc: \"/assets/images/demo-corporate/portfolio/project-1.jpg\",\n    title: \"How Marketing Wire Support Increased Data Accuracy by 70%\",\n    number: \"70%\",\n    description: \"growth with Resonance\",\n  },\n  {\n    id: 26,\n    imageSrc: \"/assets/images/demo-corporate/portfolio/project-2.jpg\",\n    title:\n      \"How Surface Mobility Increased Sales 3X During the Latest Six Months\",\n    number: \"3x\",\n    description: \"sales increased with Resonance\",\n  },\n  {\n    id: 27,\n    imageSrc: \"/assets/images/demo-corporate/portfolio/project-3.jpg\",\n    title: \"How Gen Machine Uses Automations to Grow Their Subscriber Base\",\n    number: \"Zero\",\n    description: \"negative reviews with Resonance\",\n  },\n];\n\nexport const portfolios5 = [\n  {\n    id: 28,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/1.jpg\",\n    title: \"Medium Scene\",\n    type: \"Lightbox\",\n    categories: [\"development\"],\n  },\n  {\n    id: 29,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/2.jpg\",\n    title: \"Rise of Design\",\n    type: \"External Page\",\n    categories: [\"branding\", \"design\"],\n  },\n  {\n    id: 30,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/3.jpg\",\n    title: \"Visual Stranger\",\n    type: \"External Page\",\n    categories: [\"branding\"],\n  },\n  {\n    id: 31,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/4.jpg\",\n    title: \"Amplitude\",\n    type: \"External Page\",\n    categories: [\"design\", \"development\"],\n  },\n  {\n    id: 32,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/5.jpg\",\n    title: \"Super Awards\",\n    type: \"External Page\",\n    categories: [\"design\"],\n  },\n  {\n    id: 33,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/6.jpg\",\n    title: \"Design System\",\n    type: \"Lightbox\",\n    categories: [\"design\", \"branding\"],\n  },\n  {\n    id: 34,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/7.jpg\",\n    title: \"Rise of Design\",\n    type: \"External Page\",\n    categories: [\"branding\", \"design\"],\n  },\n  {\n    id: 35,\n    imageSrc: \"/assets/images/demo-elegant/portfolio/8.jpg\",\n    title: \"Medium Scene\",\n    type: \"Lightbox\",\n    categories: [\"development\"],\n  },\n];\n\nexport const portfolios6 = [\n  {\n    id: 36,\n    categories: [\"development\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-1.jpg\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n    lightbox: true,\n    lightboxLink: \"/assets/images/demo-fancy/portfolio/project-1-large.jpg\",\n  },\n  {\n    id: 37,\n    categories: [\"branding\", \"design\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-2.jpg\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 38,\n    categories: [\"branding\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-3.jpg\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 39,\n    categories: [\"design\", \"development\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-4.jpg\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 40,\n    categories: [\"design\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-5.jpg\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n    lightbox: false,\n    externalLink: \"fancy-portfolio-single.html\",\n  },\n  {\n    id: 41,\n    categories: [\"design\", \"branding\"],\n    imgSrc: \"/assets/images/demo-fancy/portfolio/project-6.jpg\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n    lightbox: true,\n    lightboxLink: \"/assets/images/demo-fancy/portfolio/project-6-large.jpg\",\n  },\n];\n\nexport const portfolios7 = [\n  {\n    id: 42,\n    categories: [\"development\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-1.jpg\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 43,\n    categories: [\"branding\", \"design\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-2.jpg\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 44,\n    categories: [\"branding\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-3.jpg\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 45,\n    categories: [\"design\", \"development\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-4.jpg\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 46,\n    categories: [\"design\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-5.jpg\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n    dataWowDelay: \"1s\",\n  },\n  {\n    id: 47,\n    categories: [\"design\", \"branding\"],\n    imgSrc: \"/assets/images/demo-gradient/portfolio/project-6.jpg\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n    dataWowDelay: \"1s\",\n  },\n];\n\nexport const portfolios8 = [\n  {\n    id: 48,\n    imageSrc: \"/assets/images/demo-modern/portfolio/1.jpg\",\n    title: \"Medium Scene\",\n    categories: \"Branding, Design\",\n    align: \"text-center\",\n  },\n  {\n    id: 49,\n    imageSrc: \"/assets/images/demo-modern/portfolio/2.jpg\",\n    title: \"The Rise of Design\",\n    categories: \"Branding, Design\",\n    align: \"text-end\",\n  },\n  {\n    id: 50,\n    imageSrc: \"/assets/images/demo-modern/portfolio/3.jpg\",\n    title: \"Visual Stranger\",\n    categories: \"Branding, Design, Development\",\n    align: \"text-start\",\n  },\n  {\n    id: 51,\n    imageSrc: \"/assets/images/demo-modern/portfolio/4.jpg\",\n    title: \"Amplitude Studios\",\n    categories: \"Branding, Design\",\n    align: \"text-end\",\n  },\n  {\n    id: 52,\n    imageSrc: \"/assets/images/demo-modern/portfolio/5.jpg\",\n    title: \"Super Awards\",\n    categories: \"Design, Development\",\n    align: \"text-center\",\n  },\n];\n\nexport const portfolios9 = [\n  {\n    id: 53,\n    className: \"work-item\",\n    categories: [\"mix\", \"development\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-1.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n    isLightbox: true,\n  },\n  {\n    id: 54,\n    className: \"work-item\",\n    categories: [\"mt-80\", \"mt-sm-0\", \"mix\", \"branding\", \"design\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-2.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 55,\n    className: \"work-item\",\n    categories: [\"mix\", \"branding\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-3.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 56,\n    className: \"work-item\",\n    categories: [\"mix\", \"design\", \"development\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-4.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 57,\n    className: \"work-item\",\n    categories: [\"mix\", \"design\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-5.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n    isLightbox: false,\n  },\n  {\n    id: 58,\n    className: \"work-item\",\n    categories: [\"mix\", \"design\", \"branding\"],\n    imgSrc: \"/assets/images/demo-slick/portfolio/project-6.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n    isLightbox: true,\n  },\n];\nexport const portfolios10 = [\n  {\n    id: 59,\n    imgSrc: \"/assets/images/demo-strong/portfolio/1.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Rise of Design\",\n    description: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 60,\n    imgSrc: \"/assets/images/demo-strong/portfolio/2.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Amplitude\",\n    description: \"UI/UX Design, Development\",\n  },\n  {\n    id: 61,\n    imgSrc: \"/assets/images/demo-strong/portfolio/3.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Medium Scene\",\n    description: \"Branding, Design\",\n  },\n  {\n    id: 62,\n    imgSrc: \"/assets/images/demo-strong/portfolio/4.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Visual Stranger\",\n    description: \"Branding, UI/UX Design\",\n  },\n  {\n    id: 63,\n    imgSrc: \"/assets/images/demo-strong/portfolio/5.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Super Awards\",\n    description: \"UI/UX Design, Development\",\n  },\n  {\n    id: 64,\n    imgSrc: \"/assets/images/demo-strong/portfolio/6.jpg\",\n    imgAlt: \"Image Description\",\n    title: \"Design System\",\n    description: \"Branding, Design\",\n  },\n];\n\nexport const portfolios11 = [\n  {\n    id: 65,\n    title: \"How Marketing Wire Support Increased Data Accuracy by 70%\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-1.jpg\",\n    number: \"70%\",\n    description: \"growth with Resonance\",\n  },\n  {\n    id: 66,\n    title:\n      \"How Surface Mobility Increased Sales 3X During the Latest Six Months\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-2.jpg\",\n    number: \"3x\",\n    description: \"sales increased with Resonance\",\n  },\n  {\n    id: 67,\n    title: \"How Gen Machine Uses Automations to Grow Their Subscriber Base\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-3.jpg\",\n    number: \"Zero\",\n    description: \"negative reviews with Resonance\",\n  },\n  {\n    id: 68,\n    title:\n      \"How Surface Mobility Increased Sales 3X During the Latest Six Months\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-4.jpg\",\n    number: \"2x\",\n    description: \"sales increased with Resonance\",\n  },\n  {\n    id: 69,\n    title: \"How Gen Machine Uses Automations to Grow Their Subscriber Base\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-5.jpg\",\n    number: \"Zero\",\n    description: \"negative reviews with Resonance\",\n  },\n  {\n    id: 70,\n    title: \"How Marketing Wire Support Increased Data Accuracy by 70%\",\n    imageUrl: \"/assets/images/demo-corporate/portfolio/project-6.jpg\",\n    number: \"80%\",\n    description: \"growth with Resonance\",\n  },\n];\n\nexport const portfolios12 = [\n  {\n    id: 71,\n    className: \"work-item mix development\",\n    href: \"/assets/images/portfolio/masonry/full-project-1.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-1.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Medium Scene\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 72,\n    className: \"work-item mix branding design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-2.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Rise of Design\",\n    description: \"External Page\",\n  },\n  {\n    id: 73,\n    className: \"work-item  mix branding\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-3.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Visual Stranger\",\n    description: \"External Page\",\n  },\n  {\n    id: 74,\n    className: \"work-item mix design development\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-4.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 75,\n    className: \"work-item mix design\",\n    href: \"main-portfolio-single-1.html\",\n    linkClassName: \"work-ext-link\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-5.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n  {\n    id: 76,\n    className: \"work-item mix design branding\",\n    href: \"/assets/images/portfolio/masonry/full-project-6.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Design System\",\n    description: \"Lightbox\",\n  },\n  {\n    id: 77,\n    className: \"work-item mix mix design\",\n    href: \"/assets/images/portfolio/masonry/full-project-7.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-7.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Amplitude\",\n    description: \"External Page\",\n  },\n  {\n    id: 78,\n    className: \"work-item mix design development\",\n    href: \"/assets/images/portfolio/masonry/full-project-8.jpg\",\n    linkClassName: \"work-lightbox-link mfp-image\",\n    imgSrc: \"/assets/images/portfolio/masonry/projects-8.jpg\",\n    imgAlt: \"Work Description\",\n    delay: \"1s\",\n    title: \"Super Awards\",\n    description: \"External Page\",\n  },\n];\n\nexport const portfolios13 = [\n  {\n    id: 79,\n    type: \"lightbox\",\n    mix: \"development\",\n    href: \"/assets/images/portfolio/full-project-1.jpg\",\n    imgSrc: \"/assets/images/portfolio/projects-1.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Green Leaf\",\n    descr: \"Lightbox\",\n  },\n  {\n    id: 80,\n    type: \"external\",\n    mix: \"branding design\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-2.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Photo Lighting\",\n    descr: \"External Page\",\n  },\n  {\n    id: 81,\n    type: \"external\",\n    mix: \"branding\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-3.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Green Branch\",\n    descr: \"External Page\",\n  },\n  {\n    id: 82,\n    type: \"external\",\n    mix: \"design development\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-4.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"White Chair\",\n    descr: \"External Page\",\n  },\n  {\n    id: 83,\n    type: \"external\",\n    mix: \"design\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-5.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"White Table\",\n    descr: \"External Page\",\n  },\n  {\n    id: 84,\n    type: \"lightbox\",\n    mix: \"design branding\",\n    href: \"/assets/images/portfolio/full-project-6.jpg\",\n    imgSrc: \"/assets/images/portfolio/projects-6.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"The Book\",\n    descr: \"Lightbox\",\n  },\n  {\n    id: 85,\n    type: \"external\",\n    mix: \"branding\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-7.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"Green Branch\",\n    descr: \"External Page\",\n  },\n  {\n    id: 86,\n    type: \"external\",\n    mix: \"design development\",\n    href: \"main-portfolio-single-1.html\",\n    imgSrc: \"/assets/images/portfolio/projects-8.jpg\",\n    imgAlt: \"Work Description\",\n    title: \"White Chair\",\n    descr: \"External Page\",\n  },\n];\n\nexport const allPortfolios = [\n  ...portfolioItems,\n  ...portfolios1,\n  ...portfolios2,\n  ...portfolios3,\n  ...portfolios4,\n  ...portfolios5,\n  ...portfolios6,\n  ...portfolios7,\n  ...portfolios8,\n  ...portfolios9,\n  ...portfolios10,\n  ...portfolios11,\n  ...portfolios12,\n  ...portfolios13,\n];\n", "// client\\src\\components\\common\\MetaComponent.jsx\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Helmet, HelmetProvider } from \"react-helmet-async\";\n\nexport default function MetaComponent({ meta }) {\n  return (\n    <HelmetProvider>\n      <Helmet>\n        <title>{meta?.title}</title>\n        <meta name=\"description\" content={meta?.description} />\n      </Helmet>\n    </HelmetProvider>\n  );\n}\n\nMetaComponent.propTypes = {\n  meta: PropTypes.shape({\n    title: PropTypes.string,\n    description: PropTypes.string,\n  }),\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nexport default function Pagination({\n  className,\n  currentPage = 1,\n  totalPages = 1,\n  onPageChange = () => {},\n}) {\n  // Function to handle page change\n  const handlePageChange = (page) => {\n    if (page >= 1 && page <= totalPages && page !== currentPage) {\n      onPageChange(page);\n    }\n  };\n\n  // Don't render pagination if there's only one page or no pages\n  if (totalPages <= 1) {\n    return null;\n  }\n\n  // Generate page numbers array\n  const getPageNumbers = () => {\n    const pages = [];\n    const maxVisiblePages = 5;\n\n    if (totalPages <= maxVisiblePages) {\n      // Show all pages if total is small\n      for (let i = 1; i <= totalPages; i++) {\n        pages.push(i);\n      }\n    } else {\n      // Show smart pagination with ellipsis\n      if (currentPage <= 3) {\n        // Show first pages\n        for (let i = 1; i <= 4; i++) {\n          pages.push(i);\n        }\n        pages.push(\"...\");\n        pages.push(totalPages);\n      } else if (currentPage >= totalPages - 2) {\n        // Show last pages\n        pages.push(1);\n        pages.push(\"...\");\n        for (let i = totalPages - 3; i <= totalPages; i++) {\n          pages.push(i);\n        }\n      } else {\n        // Show middle pages\n        pages.push(1);\n        pages.push(\"...\");\n        for (let i = currentPage - 1; i <= currentPage + 1; i++) {\n          pages.push(i);\n        }\n        pages.push(\"...\");\n        pages.push(totalPages);\n      }\n    }\n\n    return pages;\n  };\n\n  const pageNumbers = getPageNumbers();\n\n  return (\n    <div\n      className={className ? className : \"pagination justify-content-center\"}\n    >\n      {/* Previous Page Button */}\n      <a\n        onClick={() => handlePageChange(currentPage - 1)}\n        className={currentPage === 1 ? \"disabled\" : \"\"}\n        style={{ cursor: currentPage === 1 ? \"not-allowed\" : \"pointer\" }}\n      >\n        <i className=\"mi-chevron-left\" />\n        <span className=\"visually-hidden\">Previous page</span>\n      </a>\n\n      {/* Dynamic Page Numbers */}\n      {pageNumbers.map((page, index) => {\n        if (page === \"...\") {\n          return (\n            <span key={`ellipsis-${index}`} className=\"no-active\">\n              ...\n            </span>\n          );\n        }\n\n        return (\n          <a\n            key={page}\n            onClick={() => handlePageChange(page)}\n            className={currentPage === page ? \"active\" : \"\"}\n            style={{ cursor: \"pointer\" }}\n          >\n            {page}\n          </a>\n        );\n      })}\n\n      {/* Next Page Button */}\n      <a\n        onClick={() => handlePageChange(currentPage + 1)}\n        className={currentPage === totalPages ? \"disabled\" : \"\"}\n        style={{\n          cursor: currentPage === totalPages ? \"not-allowed\" : \"pointer\",\n        }}\n      >\n        <i className=\"mi-chevron-right\" />\n        <span className=\"visually-hidden\">Next page</span>\n      </a>\n    </div>\n  );\n}\n\nPagination.propTypes = {\n  className: PropTypes.string,\n  currentPage: PropTypes.number,\n  totalPages: PropTypes.number,\n  onPageChange: PropTypes.func,\n};\n", "import { portfolios5 } from \"@/data/portfolio\";\nimport React from \"react\";\n\nimport { Link } from \"react-router-dom\";\nimport { Gallery, Item } from \"react-photoswipe-gallery\";\n\nexport default function RelatedProjects() {\n  return (\n    <div className=\"container relative\">\n      <div className=\"text-center mb-60 mb-sm-40\">\n        <h2 className=\"section-title-small\">Related Projects</h2>\n      </div>\n      {/* Works Grid */}\n      <ul\n        className=\"works-grid work-grid-4 work-grid-gut-sm hide-titles\"\n        id=\"work-grid\"\n      >\n        {/* Work Item (External Page) */}\n        <Gallery>\n          {/* Work Item (Lightbox) */}\n          {portfolios5.slice(1, 5).map((item, index) => (\n            <li\n              key={index}\n              className={`work-item mix ${item.categories.join(\" \")}`}\n            >\n              {item.type === \"Lightbox\" ? (\n                <Item\n                  original={item.imageSrc}\n                  thumbnail={item.imageSrc}\n                  width={650}\n                  height={773}\n                >\n                  {({ ref, open }) => (\n                    <a onClick={open} className=\"work-lightbox-link mfp-image\">\n                      <div className=\"work-img\">\n                        <div className=\"work-img-bg wow-p scalexIn\" />\n\n                        <img\n                          src={item.imageSrc}\n                          ref={ref}\n                          width={650}\n                          height={761}\n                          alt=\"Work Description\"\n                        />\n                      </div>\n                      <div className=\"work-intro\">\n                        <h3 className=\"work-title\">{item.title}</h3>\n                        <div className=\"work-descr\">{item.type}</div>\n                      </div>\n                    </a>\n                  )}\n                </Item>\n              ) : (\n                <Link\n                  to={`/elegant-portfolio-single/${item.id}`}\n                  className=\"work-ext-link\"\n                >\n                  <div className=\"work-img\">\n                    <div className=\"work-img-bg\" />\n                    <img\n                      src={item.imageSrc}\n                      width={650}\n                      height={761}\n                      alt=\"Work Description\"\n                    />\n                  </div>\n                  <div className=\"work-intro\">\n                    <h3 className=\"work-title\">{item.title}</h3>\n                    <div className=\"work-descr\">{item.type}</div>\n                  </div>\n                </Link>\n              )}\n            </li>\n          ))}{\" \"}\n        </Gallery>\n        {/* End Work Item */}\n      </ul>\n      {/* End Works Grid */}\n    </div>\n  );\n}\n", "import { useState } from \"react\";\n\nconst ProductGallery = ({ images, productTitle }) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n\n  if (!images || images.length === 0) {\n    return null;\n  }\n\n  const currentImage = images[currentImageIndex];\n  const hasMultipleImages = images.length > 1;\n\n  const nextImage = () => {\n    setCurrentImageIndex((prev) => (prev + 1) % images.length);\n  };\n\n  const prevImage = () => {\n    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);\n  };\n\n  const goToImage = (index) => {\n    setCurrentImageIndex(index);\n  };\n\n  const openFullscreen = () => {\n    setIsFullscreen(true);\n  };\n\n  const closeFullscreen = () => {\n    setIsFullscreen(false);\n  };\n\n  const getImageUrl = (filename) => {\n    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4004';\n    return `${baseUrl}/uploads/product-images/${filename}`;\n  };\n\n  return (\n    <>\n      {/* Main Gallery */}\n      <div className=\"product-gallery mb-60 mb-xs-40\">\n        <div className=\"position-relative\">\n          <img\n            src={getImageUrl(currentImage.filename)}\n            alt={currentImage.alt || productTitle}\n            className=\"w-100 rounded cursor-pointer\"\n            style={{ maxHeight: \"500px\", objectFit: \"cover\" }}\n            onClick={openFullscreen}\n          />\n          \n          {/* Navigation arrows - only show if multiple images */}\n          {hasMultipleImages && (\n            <>\n              <button\n                className=\"btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3\"\n                onClick={prevImage}\n                style={{ zIndex: 2 }}\n              >\n                <i className=\"mi-arrow-left\" />\n              </button>\n              <button\n                className=\"btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3\"\n                onClick={nextImage}\n                style={{ zIndex: 2 }}\n              >\n                <i className=\"mi-arrow-right\" />\n              </button>\n            </>\n          )}\n        </div>\n\n        {/* Thumbnails - only show if multiple images */}\n        {hasMultipleImages && (\n          <div className=\"gallery-thumbnails mt-3\">\n            <div className=\"d-flex gap-2 flex-wrap\">\n              {images.map((image, index) => (\n                <div\n                  key={image.id}\n                  className={`thumbnail-item cursor-pointer ${\n                    index === currentImageIndex ? 'active' : ''\n                  }`}\n                  onClick={() => goToImage(index)}\n                >\n                  <img\n                    src={getImageUrl(image.filename)}\n                    alt={image.alt || `${productTitle} ${index + 1}`}\n                    className=\"rounded\"\n                    style={{\n                      width: \"80px\",\n                      height: \"60px\",\n                      objectFit: \"cover\",\n                      border: index === currentImageIndex ? \"2px solid #007bff\" : \"2px solid transparent\",\n                      opacity: index === currentImageIndex ? 1 : 0.7\n                    }}\n                  />\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Fullscreen Modal */}\n      {isFullscreen && (\n        <div \n          className=\"fullscreen-gallery position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\n          style={{ \n            backgroundColor: \"rgba(0, 0, 0, 0.95)\", \n            zIndex: 9999 \n          }}\n          onClick={closeFullscreen}\n        >\n          <div className=\"position-relative\" onClick={(e) => e.stopPropagation()}>\n            <img\n              src={getImageUrl(currentImage.filename)}\n              alt={currentImage.alt || productTitle}\n              className=\"img-fluid\"\n              style={{ maxHeight: \"90vh\", maxWidth: \"90vw\" }}\n            />\n            \n            {/* Close button */}\n            <button\n              className=\"btn btn-light btn-lg position-absolute top-0 end-0 m-3\"\n              onClick={closeFullscreen}\n              style={{ zIndex: 10000 }}\n            >\n              ×\n            </button>\n\n            {/* Navigation arrows in fullscreen - only show if multiple images */}\n            {hasMultipleImages && (\n              <>\n                <button\n                  className=\"btn btn-light btn-lg position-absolute top-50 start-0 translate-middle-y ms-3\"\n                  onClick={prevImage}\n                  style={{ zIndex: 10000 }}\n                >\n                  <i className=\"mi-arrow-left\" />\n                </button>\n                <button\n                  className=\"btn btn-light btn-lg position-absolute top-50 end-0 translate-middle-y me-3\"\n                  onClick={nextImage}\n                  style={{ zIndex: 10000 }}\n                >\n                  <i className=\"mi-arrow-right\" />\n                </button>\n              </>\n            )}\n          </div>\n        </div>\n      )}\n\n      <style jsx>{`\n        .cursor-pointer {\n          cursor: pointer;\n        }\n        .thumbnail-item:hover img {\n          opacity: 1 !important;\n        }\n        .fullscreen-gallery {\n          backdrop-filter: blur(5px);\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default ProductGallery;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Comments({ comments = [] }) {\n  const { t } = useTranslation();\n  // Format date for display\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  // Show empty state if no comments\n  if (!comments || comments.length === 0) {\n    return (\n      <div className=\"text-center py-4\">\n        <p className=\"text-muted\">{t(\"blog.comments.no_comments\")}</p>\n      </div>\n    );\n  }\n  return (\n    <>\n      {comments.map((comment, i) => (\n        <li key={i} className=\"media comment-item\">\n          {/* <a className=\"float-start\" href=\"#\">\n            <img\n              className=\"media-object comment-avatar\"\n              src={comment.avatar}\n              alt=\"\"\n              width={50}\n              height={50}\n            />\n          </a> */}\n          <div className=\"media-body\">\n            <div className=\"comment-item-data\">\n              <div className=\"comment-author\">\n                <a href=\"#\">{comment.author}</a>\n              </div>\n              {formatDate(comment.createdAt)}{\" \"}\n              <span className=\"separator\">—</span>\n              <a href=\"#\">\n                <i className=\"fa fa-comment\" />\n                &nbsp;{t(\"blog.comments.reply\")}\n              </a>\n            </div>\n            <p>{comment.content}</p>\n            {comment.replies &&\n              comment.replies.length > 0 &&\n              comment.replies.map((reply) => (\n                <div key={reply.id} className=\"media comment-item\">\n                  <a className=\"float-start\" href=\"#\">\n                    <img\n                      className=\"media-object comment-avatar\"\n                      src=\"/assets/images/user-avatar.png\"\n                      alt=\"\"\n                      width={100}\n                      height={100}\n                    />\n                  </a>\n                  <div className=\"media-body\">\n                    <div className=\"comment-item-data\">\n                      <div className=\"comment-author\">\n                        <a href=\"#\">{reply.author}</a>\n                      </div>\n                      {formatDate(reply.createdAt)}{\" \"}\n                      <span className=\"separator\">—</span>\n                      <a href=\"#\">\n                        <i className=\"fa fa-comment\" />\n                        &nbsp;{t(\"blog.comments.reply\")}\n                      </a>\n                    </div>\n                    <p>{reply.content}</p>\n                  </div>\n                </div>\n              ))}\n          </div>\n        </li>\n      ))}\n    </>\n  );\n}\n\nComments.propTypes = {\n  comments: PropTypes.arrayOf(\n    PropTypes.shape({\n      id: PropTypes.string.isRequired,\n      author: PropTypes.string.isRequired,\n      content: PropTypes.string.isRequired,\n      createdAt: PropTypes.string.isRequired,\n      replies: PropTypes.arrayOf(\n        PropTypes.shape({\n          id: PropTypes.string.isRequired,\n          author: PropTypes.string.isRequired,\n          content: PropTypes.string.isRequired,\n          createdAt: PropTypes.string.isRequired,\n        })\n      ),\n    })\n  ),\n};\n", "import React, { useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Form({ blogSlug, onCommentSubmitted }) {\n  const { t } = useTranslation();\n  const [formData, setFormData] = useState({\n    author: \"\",\n    email: \"\",\n    website: \"\",\n    content: \"\",\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [message, setMessage] = useState({ type: \"\", text: \"\" });\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setMessage({ type: \"\", text: \"\" });\n\n    try {\n      const result = await blogAPI.createComment(blogSlug, formData);\n\n      if (result.response.ok) {\n        setMessage({\n          type: \"success\",\n          text:\n            t(\"blog.comment.success\") ||\n            \"Comment submitted successfully! It will be reviewed before being published.\",\n        });\n        setFormData({ author: \"\", email: \"\", website: \"\", content: \"\" });\n        if (onCommentSubmitted) {\n          onCommentSubmitted();\n        }\n      } else {\n        setMessage({\n          type: \"error\",\n          text:\n            result.data?.message ||\n            t(\"blog.comment.error\") ||\n            \"Failed to submit comment. Please try again.\",\n        });\n      }\n    } catch (error) {\n      console.error(\"Comment submission error:\", error);\n      setMessage({\n        type: \"error\",\n        text:\n          t(\"blog.comment.error\") ||\n          \"Failed to submit comment. Please try again.\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <form className=\"form\" onSubmit={handleSubmit}>\n      <div className=\"row mb-30 mb-md-20\">\n        <div className=\"col-md-6 mb-md-20\">\n          {/* Name */}\n          <label htmlFor=\"name\">{t(\"blog.comments.name\")} *</label>\n          <input\n            type=\"text\"\n            name=\"author\"\n            id=\"name\"\n            className=\"input-lg round form-control\"\n            placeholder={t(\"blog.comments.name_placeholder\")}\n            maxLength={100}\n            value={formData.author}\n            onChange={handleChange}\n            required\n            aria-required=\"true\"\n          />\n        </div>\n        <div className=\"col-md-6\">\n          {/* Email */}\n          <label htmlFor=\"email\">{t(\"blog.comments.email\")} *</label>\n          <input\n            type=\"email\"\n            name=\"email\"\n            id=\"email\"\n            className=\"input-lg round form-control\"\n            placeholder={t(\"blog.comments.email_placeholder\")}\n            maxLength={100}\n            value={formData.email}\n            onChange={handleChange}\n            required\n            aria-required=\"true\"\n          />\n        </div>\n      </div>\n      <div className=\"mb-30 mb-md-20\">\n        {/* Website */}\n        <label htmlFor=\"website\">{t(\"blog.comments.website\")}</label>\n        <input\n          type=\"text\"\n          name=\"website\"\n          id=\"website\"\n          className=\"input-lg round form-control\"\n          placeholder={t(\"blog.comments.website_placeholder\")}\n          maxLength={100}\n          value={formData.website}\n          onChange={handleChange}\n        />\n      </div>\n      {/* Comment */}\n      <div className=\"mb-30 mb-md-20\">\n        <label htmlFor=\"comment\">{t(\"blog.comments.message\")}</label>\n        <textarea\n          name=\"content\"\n          id=\"comment\"\n          className=\"input-lg round form-control\"\n          rows={6}\n          placeholder={t(\"blog.comments.message_placeholder\")}\n          maxLength={1000}\n          value={formData.content}\n          onChange={handleChange}\n          required\n        />\n      </div>\n      {/* Message Display */}\n      {message.text && (\n        <div\n          className={`alert ${\n            message.type === \"success\" ? \"alert-success\" : \"alert-danger\"\n          } mb-30`}\n        >\n          {message.text}\n        </div>\n      )}\n\n      {/* Send Button */}\n      <button\n        type=\"submit\"\n        className=\"submit_btn link-hover-anim link-circle-1 align-middle\"\n        data-link-animate=\"y\"\n        disabled={isSubmitting}\n      >\n        <span className=\"link-strong link-strong-unhovered\">\n          {isSubmitting\n            ? t(\"blog.comments.submitting\")\n            : t(\"blog.comments.submit\")}{\" \"}\n          <i\n            className=\"mi-arrow-right size-18 align-middle\"\n            aria-hidden=\"true\"\n          ></i>\n        </span>\n        <span className=\"link-strong link-strong-hovered\" aria-hidden=\"true\">\n          {isSubmitting\n            ? t(\"blog.comments.submitting\")\n            : t(\"blog.comments.submit\")}{\" \"}\n          <i\n            className=\"mi-arrow-right size-18 align-middle\"\n            aria-hidden=\"true\"\n          ></i>\n        </span>\n      </button>\n      {/* Inform Tip */}\n      <div className=\"form-tip form-tip-2   bg-gray-light-1 round mt-30 p-3\">\n        * - these fields are required. By sending the form you agree to the{\" \"}\n        <a href=\"#\">Terms &amp; Conditions</a> and{\" \"}\n        <a href=\"#\">Privacy Policy</a>.\n      </div>\n    </form>\n  );\n}\n\nForm.propTypes = {\n  blogSlug: PropTypes.string.isRequired,\n  onCommentSubmitted: PropTypes.func,\n};\n", "import React, { useState, useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { categoriesAPI, tagsAPI, archiveAPI, blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Widget1({\n  searchInputClass = \"form-control input-md search-field input-circle\",\n}) {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [archiveData, setArchiveData] = useState([]);\n  const [latestPosts, setLatestPosts] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch categories\n        const categoriesResult = await categoriesAPI.getCategories();\n        if (categoriesResult.response.ok && categoriesResult.data) {\n          setCategories(\n            categoriesResult.data.data || categoriesResult.data.categories || []\n          );\n        }\n\n        // Fetch tags\n        const tagsResult = await tagsAPI.getTags();\n        if (tagsResult.response.ok && tagsResult.data) {\n          setTags(tagsResult.data.data || tagsResult.data.tags || []);\n        }\n\n        // Fetch archive data\n        const archiveResult = await archiveAPI.getArchive();\n        if (archiveResult.response.ok && archiveResult.data) {\n          setArchiveData(archiveResult.data.archive || []);\n        }\n\n        // Fetch latest posts\n        const postsResult = await blogAPI.getBlogPosts(currentLanguage, 1, 5);\n        if (postsResult.response.ok && postsResult.data) {\n          const posts =\n            postsResult.data.data?.data || postsResult.data.data || [];\n          setLatestPosts(Array.isArray(posts) ? posts : []);\n        }\n      } catch (error) {\n        console.error(\"Error fetching widget data:\", error);\n      }\n    };\n\n    fetchData();\n  }, [currentLanguage]);\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Redirect to blog page with search query\n      window.location.href = `/blog?search=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    const translation = post.translations?.find(\n      (t) => t.language === currentLanguage\n    );\n    return translation?.[field] || \"\";\n  };\n\n  return (\n    <>\n      {/* Search Widget */}\n      <div className=\"widget\">\n        <form onSubmit={handleSearch} className=\"form\">\n          <div className=\"search-wrap\">\n            <button\n              className=\"search-button animate\"\n              type=\"submit\"\n              title=\"Start Search\"\n            >\n              <i className=\"mi-search size-18\" />\n              <span className=\"visually-hidden\">Start search</span>\n            </button>\n            <input\n              type=\"text\"\n              className={searchInputClass}\n              placeholder={t(\"blog.search_placeholder\") || \"Search...\"}\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              required\n            />\n          </div>\n        </form>\n      </div>\n      {/* End Search Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">{t(\"blog.categories\")}</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-menu\">\n            {categories.length > 0 ? (\n              categories.map((category) => (\n                <li key={category.id}>\n                  <a\n                    href=\"#\"\n                    title=\"\"\n                    onClick={(e) => {\n                      e.preventDefault();\n                      window.location.href = `/${currentLanguage}/blog?category=${category.slug}`;\n                    }}\n                  >\n                    {category.name}\n                  </a>\n                  <small> - {category._count?.blogPosts || 0} </small>\n                </li>\n              ))\n            ) : (\n              <li>{t(\"blog.no_categories\")}</li>\n            )}\n          </ul>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">{t(\"blog.tags\")}</h3>\n        <div className=\"widget-body\">\n          <div className=\"tags\">\n            {tags.length > 0 ? (\n              tags.map((tag) => (\n                <a\n                  href=\"#\"\n                  key={tag.id}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    window.location.href = `/${currentLanguage}/blog?tag=${tag.slug}`;\n                  }}\n                >\n                  {tag.name}\n                </a>\n              ))\n            ) : (\n              <span>{t(\"blog.no_tags\")}</span>\n            )}\n          </div>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">{t(\"blog.latest_posts\")}</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-posts\">\n            {latestPosts.length > 0 ? (\n              latestPosts.map((post, index) => (\n                <li key={post.id || index} className=\"clearfix\">\n                  <a href={`/${currentLanguage}/blog-single/${post.slug}`}>\n                    <img\n                      src={\n                        post.featuredImage ||\n                        \"/assets/images/demo-elegant/blog/1.jpg\"\n                      }\n                      height={140}\n                      style={{ height: \"fit-content\" }}\n                      alt={getTranslation(post, \"title\")}\n                      width={100}\n                      className=\"widget-posts-img\"\n                    />\n                  </a>\n                  <div className=\"widget-posts-descr\">\n                    <a\n                      href={`/${currentLanguage}/blog-single/${post.slug}`}\n                      title=\"\"\n                    >\n                      {getTranslation(post, \"title\")}\n                    </a>\n                    <span>\n                      {t(\"blog.posted_by\")}{\" \"}\n                      {post.author?.name || \"DevSkills Team\"}\n                    </span>\n                  </div>\n                </li>\n              ))\n            ) : (\n              <li>{t(\"blog.no_recent_posts\")}</li>\n            )}\n          </ul>\n        </div>\n      </div>\n      {/* End Widget */}\n      {/* Widget */}\n      <div className=\"widget\">\n        <h3 className=\"widget-title\">{t(\"blog.archive\")}</h3>\n        <div className=\"widget-body\">\n          <ul className=\"clearlist widget-menu\">\n            {archiveData.length > 0 ? (\n              archiveData.map((archive, index) => (\n                <li key={index}>\n                  <a href=\"#\" title=\"\">\n                    {archive.monthName} {archive.year}\n                  </a>\n                  <small> - {archive.count} </small>\n                </li>\n              ))\n            ) : (\n              <li>{t(\"blog.no_archive\")}</li>\n            )}\n          </ul>\n        </div>\n      </div>\n    </>\n  );\n}\n\nWidget1.propTypes = {\n  searchInputClass: PropTypes.string,\n};\n", "import React from \"react\";\nimport { useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Map() {\n  const [mapOpen, setMapOpen] = useState(false);\n  const { t } = useTranslation();\n  return (\n    <>\n      <a href=\"#\" className={`map-section ${mapOpen ? \"js-active\" : \"\"}`}>\n        <div className=\"map-toggle wow fadeInUpShort\" aria-hidden=\"true\">\n          <div className=\"mt-icon\">\n            <i className=\"mi-location\"></i>\n          </div>\n          <div className=\"mt-text\">\n            <div onClick={() => setMapOpen((pre) => !pre)} className=\"mt-open\">\n              {t(\"contact.map.open\")} <i className=\"mt-open-icon\"></i>\n            </div>\n            <div onClick={() => setMapOpen((pre) => !pre)} className=\"mt-close\">\n              {t(\"contact.map.close\")} <i className=\"mt-close-icon\"></i>\n            </div>\n          </div>\n        </div>\n      </a>\n\n      <iframe\n        src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2028.7573128553793!2d24.7553!3d59.4372!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4692935c7d5dfa3b%3A0x4b0f5f5c5d9c1f0!2sTornim%C3%A4e%20tn%207%2C%2010145%20Tallinn!5e0!3m2!1sen!2see!4v1684450429598!5m2!1sen!2see\"\n        width={600}\n        height={450}\n        loading=\"lazy\"\n        style={{ border: 0 }}\n        allowFullScreen=\"\"\n        aria-hidden=\"false\"\n        tabIndex={0}\n      />\n    </>\n  );\n}\n", "// client\\src\\components\\common\\SEO.jsx\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Helmet } from \"react-helmet-async\";\n\n/**\n * Enhanced SEO component for managing all meta tags with advanced social media support\n *\n * @param {Object} props - Component props\n * @param {string} props.title - Page title\n * @param {string} props.description - Page description\n * @param {string} props.canonical - Canonical URLs\n * @param {string} props.image - OG image URL\n * @param {string} props.type - OG type (website, article, etc.)\n * @param {Object} props.schema - JSON-LD structured data\n * @param {string} props.imageAlt - Alt text for the OG image\n * @param {string} props.imageWidth - Width of the OG image\n * @param {string} props.imageHeight - Height of the OG image\n * @param {string} props.twitterHandle - Twitter handle\n * @param {string} props.publishedAt - Article published date (ISO format)\n * @param {string} props.modifiedAt - Article modified date (ISO format)\n * @param {string} props.author - Article author\n * @param {string[]} props.keywords - Keywords for SEO\n * @param {string} props.locale - Content locale\n */\nconst SEO = ({\n  title = \"DevSkills - Professional Software Development Services\",\n  description = \"DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.\",\n  canonical = \"https://devskills.ee\",\n  image = \"https://devskills.ee/home.jpg\",\n  imageAlt = \"DevSkills Development Studio\",\n  imageWidth = \"1162\",\n  imageHeight = \"630\",\n  type = \"website\",\n  schema = null,\n  twitterHandle = \"@DevSkillsEE\",\n  publishedAt = \"\",\n  modifiedAt = \"\",\n  author = \"DevSkills\",\n  keywords = [\n    \"software development\",\n    \"custom software\",\n    \"web development\",\n    \"AI solutions\",\n    \"white label software\",\n    \"business comanager\",\n  ],\n  locale = \"en_US\",\n}) => {\n  // Format the title\n  const formattedTitle = `${title} | DevSkills`;\n\n  return (\n    <Helmet>\n      {/* Basic meta tags */}\n      <title>{formattedTitle}</title>\n      <meta name=\"description\" content={description} />\n      <link rel=\"canonical\" href={canonical} />\n      <meta name=\"keywords\" content={keywords.join(\", \")} />\n\n      {/* Open Graph meta tags for social sharing */}\n      <meta property=\"og:title\" content={formattedTitle} />\n      <meta property=\"og:description\" content={description} />\n      <meta property=\"og:type\" content={type} />\n      <meta property=\"og:url\" content={canonical} />\n      <meta property=\"og:image\" content={image} />\n      <meta property=\"og:image:alt\" content={imageAlt} />\n      <meta property=\"og:image:width\" content={imageWidth} />\n      <meta property=\"og:image:height\" content={imageHeight} />\n      <meta property=\"og:site_name\" content=\"DevSkills\" />\n      <meta property=\"og:locale\" content={locale} />\n\n      {/* Facebook App ID for Facebook Debugger - Replace with your actual Facebook App ID */}\n      <meta property=\"fb:app_id\" content=\"YOUR_FACEBOOK_APP_ID\" />\n\n      {/* Article specific meta tags */}\n      {type === \"article\" && publishedAt && (\n        <meta property=\"article:published_time\" content={publishedAt} />\n      )}\n      {type === \"article\" && modifiedAt && (\n        <meta property=\"article:modified_time\" content={modifiedAt} />\n      )}\n      {type === \"article\" && author && (\n        <meta property=\"article:author\" content={author} />\n      )}\n\n      {/* LinkedIn Author and Publication Date */}\n      <meta name=\"author\" content={author || \"DevSkills\"} />\n      {publishedAt && <meta name=\"publish_date\" content={publishedAt} />}\n      {!publishedAt && (\n        <meta name=\"publish_date\" content={new Date().toISOString()} />\n      )}\n\n      {/* Twitter Card meta tags */}\n      <meta name=\"twitter:card\" content=\"summary_large_image\" />\n      <meta name=\"twitter:site\" content={twitterHandle} />\n      <meta name=\"twitter:creator\" content={twitterHandle} />\n      <meta name=\"twitter:title\" content={formattedTitle} />\n      <meta name=\"twitter:description\" content={description} />\n      <meta name=\"twitter:image\" content={image} />\n      <meta name=\"twitter:image:alt\" content={imageAlt} />\n\n      {/* Additional meta tags for SEO */}\n      <meta name=\"robots\" content=\"index, follow, max-image-preview:large\" />\n      <meta name=\"googlebot\" content=\"index, follow\" />\n      <meta httpEquiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n      <meta name=\"language\" content=\"English\" />\n\n      {/* Mobile specific meta tags */}\n      <meta\n        name=\"viewport\"\n        content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\"\n      />\n      <meta name=\"theme-color\" content=\"#06B6D4\" />\n      <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n      <meta\n        name=\"apple-mobile-web-app-status-bar-style\"\n        content=\"black-translucent\"\n      />\n\n      {/* JSON-LD structured data */}\n      {schema && Array.isArray(schema) ? (\n        // Handle array of schema objects\n        schema.map((schemaItem, index) => (\n          <script key={index} type=\"application/ld+json\">\n            {JSON.stringify(schemaItem)}\n          </script>\n        ))\n      ) : schema ? (\n        // Handle single schema object\n        <script type=\"application/ld+json\">{JSON.stringify(schema)}</script>\n      ) : null}\n    </Helmet>\n  );\n};\n\nSEO.propTypes = {\n  title: PropTypes.string,\n  description: PropTypes.string,\n  canonical: PropTypes.string,\n  image: PropTypes.string,\n  imageAlt: PropTypes.string,\n  imageWidth: PropTypes.string,\n  imageHeight: PropTypes.string,\n  type: PropTypes.string,\n  schema: PropTypes.oneOfType([\n    PropTypes.object,\n    PropTypes.arrayOf(PropTypes.object),\n  ]),\n  twitterHandle: PropTypes.string,\n  publishedAt: PropTypes.string,\n  modifiedAt: PropTypes.string,\n  author: PropTypes.string,\n  keywords: PropTypes.arrayOf(PropTypes.string),\n  locale: PropTypes.string,\n};\n\nexport default SEO;\n", "import React, { useState, useRef, useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport PropTypes from \"prop-types\";\n\nconst AdminLayout = ({ children, title }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);\n  const [userDropdownOpen, setUserDropdownOpen] = useState(false);\n  const dropdownRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setUserDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"adminToken\");\n    localStorage.removeItem(\"adminUser\");\n    navigate(\"/admin\");\n    setUserDropdownOpen(false);\n  };\n\n  const toggleUserDropdown = () => {\n    setUserDropdownOpen(!userDropdownOpen);\n  };\n\n  const isActive = (path) => {\n    return (\n      location.pathname === path || location.pathname.startsWith(path + \"/\")\n    );\n  };\n\n  const user = JSON.parse(localStorage.getItem(\"adminUser\") || \"{}\");\n\n  const menuItems = [\n    {\n      path: \"/admin/dashboard\",\n      icon: \"solar:widget-2-bold\",\n      label: \"Dashboard\",\n      exact: true,\n    },\n    {\n      path: \"/admin/posts\",\n      icon: \"solar:document-text-bold\",\n      label: \"Blog Posts\",\n      exact: false,\n    },\n    {\n      path: \"/admin/blog/new\",\n      icon: \"solar:add-circle-bold\",\n      label: \"New Post\",\n      exact: true,\n    },\n    {\n      path: \"/admin/products\",\n      icon: \"solar:shop-bold\",\n      label: \"Products\",\n      exact: false,\n    },\n    {\n      path: \"/admin/products/new\",\n      icon: \"solar:add-square-bold\",\n      label: \"New Product\",\n      exact: true,\n    },\n    {\n      path: \"/admin/analytics\",\n      icon: \"solar:chart-2-bold\",\n      label: \"Analytics\",\n      exact: true,\n    },\n    {\n      path: \"/admin/categories\",\n      icon: \"solar:folder-bold\",\n      label: \"Categories\",\n      exact: true,\n    },\n    {\n      path: \"/admin/tags\",\n      icon: \"solar:tag-bold\",\n      label: \"Tags\",\n      exact: true,\n    },\n    {\n      path: \"/admin/comments\",\n      icon: \"solar:chat-round-bold\",\n      label: \"Comments\",\n      exact: true,\n    },\n  ];\n\n  return (\n    <div className=\"admin-layout-wrapper\">\n      {/* Mobile Overlay */}\n      {mobileSidebarOpen && (\n        <div\n          className=\"admin-mobile-overlay\"\n          onClick={() => setMobileSidebarOpen(false)}\n        ></div>\n      )}\n\n      {/* Sidebar */}\n      <aside\n        className={`admin-sidebar ${sidebarCollapsed ? \"collapsed\" : \"\"} ${\n          mobileSidebarOpen ? \"mobile-open\" : \"\"\n        }`}\n      >\n        {/* Logo */}\n        <div className=\"admin-sidebar-header\">\n          <div className=\"admin-logo\">\n            {!sidebarCollapsed ? (\n              <span>\n                <span className=\"color-primary-1\">DevSkills</span> Admin\n              </span>\n            ) : (\n              <span className=\"color-primary-1\">DS</span>\n            )}\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"admin-sidebar-nav\">\n          <ul className=\"admin-nav-list\">\n            {menuItems.map((item) => (\n              <li\n                key={item.path}\n                className={`admin-nav-item ${\n                  isActive(item.path) ? \"active\" : \"\"\n                }`}\n              >\n                <a\n                  href=\"#\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    navigate(item.path);\n                    setMobileSidebarOpen(false);\n                  }}\n                  className=\"admin-nav-link\"\n                  title={item.label}\n                >\n                  <iconify-icon\n                    icon={item.icon}\n                    className=\"admin-nav-icon\"\n                  ></iconify-icon>\n                  <span className=\"admin-nav-text\">{item.label}</span>\n                </a>\n              </li>\n            ))}\n\n            {/* Divider */}\n            <li className=\"admin-nav-divider\"></li>\n\n            {/* View Site */}\n            <li className=\"admin-nav-item\">\n              <a\n                href=\"/\"\n                target=\"_blank\"\n                className=\"admin-nav-link\"\n                title=\"View Site\"\n              >\n                <iconify-icon\n                  icon=\"solar:link-bold\"\n                  className=\"admin-nav-icon\"\n                ></iconify-icon>\n                <span className=\"admin-nav-text\">View Site</span>\n              </a>\n            </li>\n          </ul>\n        </nav>\n\n        {/* User Menu */}\n        <div className=\"admin-sidebar-footer\">\n          <div className=\"admin-user-menu\" ref={dropdownRef}>\n            <div\n              className=\"admin-user-info clickable\"\n              onClick={toggleUserDropdown}\n              title=\"User menu\"\n            >\n              <div className=\"admin-user-avatar\">\n                <iconify-icon icon=\"solar:user-bold\"></iconify-icon>\n              </div>\n              {!sidebarCollapsed && (\n                <div className=\"admin-user-details\">\n                  <div className=\"admin-user-name\">{user.name || \"Admin\"}</div>\n                  <div className=\"admin-user-email\">{user.email}</div>\n                </div>\n              )}\n              <div className=\"admin-user-dropdown-arrow\">\n                <iconify-icon\n                  icon={`solar:alt-arrow-${\n                    userDropdownOpen ? \"up\" : \"down\"\n                  }-bold`}\n                ></iconify-icon>\n              </div>\n            </div>\n\n            {/* Dropdown Menu */}\n            {userDropdownOpen && (\n              <div className=\"admin-user-dropdown\">\n                <div\n                  className=\"admin-dropdown-item\"\n                  onClick={() => {\n                    navigate(\"/admin/dashboard\");\n                    setUserDropdownOpen(false);\n                  }}\n                >\n                  <iconify-icon\n                    icon=\"solar:widget-2-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Dashboard\n                </div>\n                <div\n                  className=\"admin-dropdown-item\"\n                  onClick={() => {\n                    window.open(\"/\", \"_blank\");\n                    setUserDropdownOpen(false);\n                  }}\n                >\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  View Site\n                </div>\n                <div className=\"admin-dropdown-divider\"></div>\n                <div\n                  className=\"admin-dropdown-item logout\"\n                  onClick={handleLogout}\n                >\n                  <iconify-icon\n                    icon=\"solar:power-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Logout\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </aside>\n\n      {/* Main Content */}\n      <main\n        className={`admin-main-content ${\n          sidebarCollapsed ? \"sidebar-collapsed\" : \"\"\n        }`}\n      >\n        {/* Top Bar */}\n        <header className=\"admin-topbar\">\n          <div className=\"admin-topbar-left\">\n            <button\n              className=\"admin-sidebar-toggle\"\n              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n            >\n              <iconify-icon icon=\"solar:hamburger-menu-bold\"></iconify-icon>\n            </button>\n            <button\n              className=\"admin-mobile-toggle\"\n              onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}\n            >\n              <iconify-icon icon=\"solar:hamburger-menu-bold\"></iconify-icon>\n            </button>\n            {title && <h1 className=\"admin-page-title\">{title}</h1>}\n          </div>\n          <div className=\"admin-topbar-right\">\n            <span className=\"admin-welcome\">\n              Welcome, {user.name || user.email}\n            </span>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <div className=\"admin-content\">{children}</div>\n      </main>\n    </div>\n  );\n};\n\nAdminLayout.propTypes = {\n  children: PropTypes.node.isRequired,\n  title: PropTypes.string,\n};\n\nexport default AdminLayout;\n", "import React, { useEffect } from \"react\";\nimport { useEditor, EditorContent } from \"@tiptap/react\";\nimport StarterK<PERSON> from \"@tiptap/starter-kit\";\nimport CodeBlock from \"@tiptap/extension-code-block\";\nimport Code from \"@tiptap/extension-code\";\n\nconst TipTapEditor = ({\n  content,\n  onChange,\n  placeholder = \"Start writing...\",\n}) => {\n  // No need for syntax highlighting in editor - will be applied on blog pages\n\n  // Create editor with basic CodeBlock (no syntax highlighting in editor)\n  const editor = useEditor({\n    extensions: [\n      StarterKit.configure({\n        code: false, // Disable default code extension\n        codeBlock: false, // Disable default codeBlock extension\n      }),\n      Code.configure({\n        HTMLAttributes: {\n          class: \"inline-code\",\n        },\n      }),\n      // Use basic CodeBlock (syntax highlighting will be applied on blog pages)\n      CodeBlock.configure({\n        defaultLanguage: \"javascript\",\n        HTMLAttributes: {\n          class: \"code-block\",\n        },\n      }),\n    ],\n    content: content,\n    onUpdate: ({ editor }) => {\n      const html = editor.getHTML();\n      onChange(html);\n    },\n    editorProps: {\n      attributes: {\n        class: \"tiptap-editor\",\n      },\n    },\n  });\n\n  // Update editor content when the content prop changes (but not during user typing)\n  useEffect(() => {\n    if (editor && content !== undefined) {\n      const currentContent = editor.getHTML();\n      // Only update if content is significantly different (not just formatting differences)\n      if (content !== currentContent && !editor.isFocused) {\n        editor.commands.setContent(content);\n      }\n    }\n  }, [editor, content]);\n\n  if (!editor) {\n    return (\n      <div className=\"tiptap-wrapper\">\n        <div className=\"tiptap-loading\">\n          <p>Loading editor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"tiptap-wrapper\">\n      {/* Toolbar */}\n      <div className=\"tiptap-toolbar\">\n        <div className=\"toolbar-group\">\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleBold().run()}\n            className={`toolbar-btn ${editor.isActive(\"bold\") ? \"active\" : \"\"}`}\n            title=\"Bold\"\n          >\n            <strong>B</strong>\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleItalic().run()}\n            className={`toolbar-btn ${\n              editor.isActive(\"italic\") ? \"active\" : \"\"\n            }`}\n            title=\"Italic\"\n          >\n            <em>I</em>\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleCode().run()}\n            className={`toolbar-btn ${editor.isActive(\"code\") ? \"active\" : \"\"}`}\n            title=\"Inline Code\"\n          >\n            {\"</>\"}\n          </button>\n        </div>\n\n        <div className=\"toolbar-group\">\n          <button\n            type=\"button\"\n            onClick={() =>\n              editor.chain().focus().toggleHeading({ level: 1 }).run()\n            }\n            className={`toolbar-btn ${\n              editor.isActive(\"heading\", { level: 1 }) ? \"active\" : \"\"\n            }`}\n            title=\"Heading 1\"\n          >\n            H1\n          </button>\n          <button\n            type=\"button\"\n            onClick={() =>\n              editor.chain().focus().toggleHeading({ level: 2 }).run()\n            }\n            className={`toolbar-btn ${\n              editor.isActive(\"heading\", { level: 2 }) ? \"active\" : \"\"\n            }`}\n            title=\"Heading 2\"\n          >\n            H2\n          </button>\n          <button\n            type=\"button\"\n            onClick={() =>\n              editor.chain().focus().toggleHeading({ level: 3 }).run()\n            }\n            className={`toolbar-btn ${\n              editor.isActive(\"heading\", { level: 3 }) ? \"active\" : \"\"\n            }`}\n            title=\"Heading 3\"\n          >\n            H3\n          </button>\n        </div>\n\n        <div className=\"toolbar-group\">\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleBulletList().run()}\n            className={`toolbar-btn ${\n              editor.isActive(\"bulletList\") ? \"active\" : \"\"\n            }`}\n            title=\"Bullet List\"\n          >\n            • List\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleOrderedList().run()}\n            className={`toolbar-btn ${\n              editor.isActive(\"orderedList\") ? \"active\" : \"\"\n            }`}\n            title=\"Numbered List\"\n          >\n            1. List\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleCodeBlock().run()}\n            className={`toolbar-btn ${\n              editor.isActive(\"codeBlock\") ? \"active\" : \"\"\n            }`}\n            title=\"Code Block\"\n          >\n            {\"{ }\"}\n          </button>\n        </div>\n\n        <div className=\"toolbar-group\">\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().toggleBlockquote().run()}\n            className={`toolbar-btn ${\n              editor.isActive(\"blockquote\") ? \"active\" : \"\"\n            }`}\n            title=\"Quote\"\n          ></button>\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().setHorizontalRule().run()}\n            className=\"toolbar-btn\"\n            title=\"Horizontal Rule\"\n          >\n            ―\n          </button>\n        </div>\n\n        <div className=\"toolbar-group\">\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().undo().run()}\n            disabled={!editor.can().undo()}\n            className=\"toolbar-btn\"\n            title=\"Undo\"\n          >\n            ↶\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => editor.chain().focus().redo().run()}\n            disabled={!editor.can().redo()}\n            className=\"toolbar-btn\"\n            title=\"Redo\"\n          >\n            ↷\n          </button>\n        </div>\n      </div>\n\n      {/* Editor */}\n      <EditorContent\n        editor={editor}\n        className=\"tiptap-content\"\n        placeholder={placeholder}\n      />\n    </div>\n  );\n};\n\nexport default TipTapEditor;\n", "import React, { useState, useRef, useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\n\nconst TimeRangeSelector = ({ options, value, onChange, comparedPeriod }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  const selectedOption = options.find((option) => option.value === value);\n\n  const handleOptionClick = (optionValue) => {\n    onChange(optionValue);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"time-range-selector-wrapper d-flex align-items-center gap-3\">\n      {/* Time Range Dropdown */}\n      <div className=\"position-relative\" ref={dropdownRef}>\n        <button\n          className=\"btn btn-outline-secondary d-flex align-items-center gap-2 text-nowrap\"\n          onClick={() => setIsOpen(!isOpen)}\n          aria-expanded={isOpen}\n        >\n          <iconify-icon\n            icon=\"solar:calendar-bold\"\n            className=\"text-primary\"\n          ></iconify-icon>\n          <span className=\"text-nowrap\">\n            {selectedOption?.label || \"Select time range\"}\n          </span>\n          <iconify-icon\n            icon=\"solar:alt-arrow-down-bold\"\n            className={`transition-transform ${isOpen ? \"rotate-180\" : \"\"}`}\n          ></iconify-icon>\n        </button>\n\n        {isOpen && (\n          <div className=\"dropdown-menu show position-absolute mt-1 shadow-lg border-0 bg-white rounded-3\">\n            {options.map((option) => (\n              <button\n                key={option.value}\n                className={`dropdown-item px-3 py-2 text-nowrap ${\n                  option.value === value\n                    ? \"active bg-primary text-white\"\n                    : \"text-dark\"\n                }`}\n                onClick={() => handleOptionClick(option.value)}\n              >\n                {option.label}\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nTimeRangeSelector.propTypes = {\n  options: PropTypes.arrayOf(\n    PropTypes.shape({\n      value: PropTypes.string.isRequired,\n      label: PropTypes.string.isRequired,\n    })\n  ).isRequired,\n  value: PropTypes.string.isRequired,\n  onChange: PropTypes.func.isRequired,\n  comparedPeriod: PropTypes.string,\n};\n\nexport default TimeRangeSelector;\n", "import React, { useState, useRef, useEffect } from \"react\";\n\nconst LanguageSelector = ({ options, value, onChange }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  const selectedOption = options.find((option) => option.value === value);\n\n  const handleOptionClick = (optionValue) => {\n    onChange(optionValue);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"language-selector-wrapper d-flex justify-content-end\">\n      {/* Language Dropdown */}\n      <div className=\"position-relative\" ref={dropdownRef}>\n        <button\n          className=\"btn btn-outline-secondary d-flex align-items-center gap-2\"\n          onClick={() => setIsOpen(!isOpen)}\n          aria-expanded={isOpen}\n        >\n          <span className=\"fs-5\">{selectedOption?.flag || \"🌐\"}</span>\n          <span>{selectedOption?.label || \"Select language\"}</span>\n          <iconify-icon\n            icon=\"solar:alt-arrow-down-bold\"\n            className={`transition-transform ${isOpen ? \"rotate-180\" : \"\"}`}\n          ></iconify-icon>\n        </button>\n\n        {isOpen && (\n          <div\n            className=\"dropdown-menu show position-absolute mt-1 shadow-lg border-0 bg-white rounded-3\"\n            style={{ right: 0, minWidth: \"200px\" }}\n          >\n            {options.map((option) => (\n              <button\n                key={option.value}\n                className={`dropdown-item px-3 py-2 d-flex align-items-center gap-2 text-nowrap ${\n                  option.value === value\n                    ? \"active bg-primary text-white\"\n                    : \"text-dark\"\n                }`}\n                onClick={() => handleOptionClick(option.value)}\n              >\n                <span className=\"fs-6\">{option.flag}</span>\n                <span>{option.label}</span>\n                {option.value === value && (\n                  <iconify-icon\n                    icon=\"solar:check-circle-bold\"\n                    className=\"ms-auto\"\n                  ></iconify-icon>\n                )}\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default LanguageSelector;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nconst AnalyticsOverview = ({ data }) => {\n  if (!data) {\n    return (\n      <div className=\"analytics-overview-skeleton\">\n        <div className=\"row\">\n          {[1, 2, 3].map((i) => (\n            <div key={i} className=\"col-md-4 mb-3\">\n              <div className=\"card border-0 shadow-sm\">\n                <div className=\"card-body p-4\">\n                  <div className=\"placeholder-glow\">\n                    <div className=\"placeholder col-6 mb-2\"></div>\n                    <div\n                      className=\"placeholder col-8 mb-1\"\n                      style={{ height: \"2rem\" }}\n                    ></div>\n                    <div className=\"placeholder col-4\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  const formatNumber = (num) => {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + \"M\";\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + \"K\";\n    }\n    return num?.toLocaleString() || \"0\";\n  };\n\n  const formatPercentage = (current, previous) => {\n    if (!previous || previous === 0) return null;\n    const change = ((current - previous) / previous) * 100;\n    return {\n      value: Math.abs(change).toFixed(1),\n      isPositive: change >= 0,\n      isSignificant: Math.abs(change) >= 1,\n    };\n  };\n\n  const MetricCard = ({\n    title,\n    value,\n    previousValue,\n    icon,\n    color = \"primary\",\n  }) => {\n    const percentage = formatPercentage(value, previousValue);\n\n    return (\n      <div className=\"col-md-4 mb-3\">\n        <div className=\"card border-0 shadow-sm h-100 bg-white\">\n          <div className=\"card-body p-4\">\n            <div className=\"d-flex align-items-center justify-content-between mb-3\">\n              <h6 className=\"card-title text-muted mb-0 fw-normal\">{title}</h6>\n              <iconify-icon\n                icon={icon}\n                className={`text-${color} fs-4`}\n              ></iconify-icon>\n            </div>\n\n            <div className=\"mb-2\">\n              <h2 className=\"mb-0 fw-bold text-dark\">{formatNumber(value)}</h2>\n            </div>\n\n            {percentage && percentage.isSignificant && (\n              <div className=\"d-flex align-items-center\">\n                <iconify-icon\n                  icon={\n                    percentage.isPositive\n                      ? \"solar:arrow-up-bold\"\n                      : \"solar:arrow-down-bold\"\n                  }\n                  className={`me-1 ${\n                    percentage.isPositive ? \"text-success\" : \"text-danger\"\n                  }`}\n                ></iconify-icon>\n                <span\n                  className={`small fw-medium ${\n                    percentage.isPositive ? \"text-success\" : \"text-danger\"\n                  }`}\n                >\n                  {percentage.value}%\n                </span>\n                <span className=\"text-muted small ms-1\">\n                  vs previous period\n                </span>\n              </div>\n            )}\n\n            {(!percentage || !percentage.isSignificant) && (\n              <div className=\"text-muted small\">No significant change</div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"analytics-overview\">\n      <div className=\"row\">\n        <MetricCard\n          title=\"Post views\"\n          value={data.pageViews?.current || 0}\n          previousValue={data.pageViews?.previous || 0}\n          icon=\"solar:eye-bold\"\n          color=\"primary\"\n        />\n\n        <MetricCard\n          title=\"Visitors\"\n          value={data.visitors?.current || 0}\n          previousValue={data.visitors?.previous || 0}\n          icon=\"solar:users-group-rounded-bold\"\n          color=\"info\"\n        />\n\n        <MetricCard\n          title=\"Engagement\"\n          value={data.engagement?.current || 0}\n          previousValue={data.engagement?.previous || 0}\n          icon=\"solar:heart-bold\"\n          color=\"success\"\n        />\n      </div>\n    </div>\n  );\n};\n\nAnalyticsOverview.propTypes = {\n  data: PropTypes.shape({\n    totalPageViews: PropTypes.number,\n    totalVisitors: PropTypes.number,\n    avgEngagement: PropTypes.number,\n    pageViewsChange: PropTypes.number,\n    visitorsChange: PropTypes.number,\n    engagementChange: PropTypes.number,\n  }),\n};\n\nexport default AnalyticsOverview;\n", "import React, { useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler,\n} from \"chart.js\";\nimport { Line } from \"react-chartjs-2\";\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n);\n\nconst AnalyticsChart = ({ data }) => {\n  const chartRef = useRef(null);\n\n  if (!data || !data.labels || data.labels.length === 0) {\n    return (\n      <div className=\"card border-0 shadow-sm h-100\">\n        <div className=\"card-body p-4\">\n          <div className=\"d-flex align-items-center justify-content-between mb-4\">\n            <h5 className=\"card-title mb-0\">Analytics Overview</h5>\n            <a\n              href=\"https://analytics.google.com\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-primary small text-decoration-none\"\n            >\n              View Report\n            </a>\n          </div>\n          <div\n            className=\"d-flex align-items-center justify-content-center\"\n            style={{ height: \"300px\" }}\n          >\n            <div className=\"text-center text-muted\">\n              <iconify-icon\n                icon=\"solar:chart-2-bold\"\n                className=\"fs-1 mb-3 d-block\"\n              ></iconify-icon>\n              <p className=\"mb-0\">No data available for the selected period</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const chartData = {\n    labels: data.labels,\n    datasets: [\n      {\n        label: \"Page Views\",\n        data: data.pageViews || [],\n        borderColor: \"#3b82f6\",\n        backgroundColor: \"rgba(59, 130, 246, 0.1)\",\n        borderWidth: 2,\n        fill: true,\n        tension: 0.4,\n        pointRadius: 4,\n        pointHoverRadius: 6,\n        pointBackgroundColor: \"#3b82f6\",\n        pointBorderColor: \"#ffffff\",\n        pointBorderWidth: 2,\n      },\n      {\n        label: \"Visitors\",\n        data: data.visitors || [],\n        borderColor: \"#06b6d4\",\n        backgroundColor: \"rgba(6, 182, 212, 0.1)\",\n        borderWidth: 2,\n        fill: false,\n        tension: 0.4,\n        pointRadius: 4,\n        pointHoverRadius: 6,\n        pointBackgroundColor: \"#06b6d4\",\n        pointBorderColor: \"#ffffff\",\n        pointBorderWidth: 2,\n      },\n      {\n        label: \"Engagement\",\n        data: data.engagement || [],\n        borderColor: \"#10b981\",\n        backgroundColor: \"rgba(16, 185, 129, 0.1)\",\n        borderWidth: 2,\n        fill: false,\n        tension: 0.4,\n        pointRadius: 4,\n        pointHoverRadius: 6,\n        pointBackgroundColor: \"#10b981\",\n        pointBorderColor: \"#ffffff\",\n        pointBorderWidth: 2,\n      },\n    ],\n  };\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: \"bottom\",\n        labels: {\n          usePointStyle: true,\n          padding: 20,\n          font: {\n            size: 12,\n          },\n        },\n      },\n      tooltip: {\n        mode: \"index\",\n        intersect: false,\n        backgroundColor: \"rgba(0, 0, 0, 0.8)\",\n        titleColor: \"#ffffff\",\n        bodyColor: \"#ffffff\",\n        borderColor: \"rgba(255, 255, 255, 0.1)\",\n        borderWidth: 1,\n        cornerRadius: 8,\n        padding: 12,\n        displayColors: true,\n        callbacks: {\n          label: function (context) {\n            return `${\n              context.dataset.label\n            }: ${context.parsed.y.toLocaleString()}`;\n          },\n        },\n      },\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false,\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: \"#6b7280\",\n          font: {\n            size: 11,\n          },\n        },\n      },\n      y: {\n        beginAtZero: true,\n        grid: {\n          color: \"rgba(107, 114, 128, 0.1)\",\n          borderDash: [2, 2],\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: \"#6b7280\",\n          font: {\n            size: 11,\n          },\n          callback: function (value) {\n            if (value >= 1000000) {\n              return (value / 1000000).toFixed(1) + \"M\";\n            } else if (value >= 1000) {\n              return (value / 1000).toFixed(1) + \"K\";\n            }\n            return value;\n          },\n        },\n      },\n    },\n    interaction: {\n      mode: \"nearest\",\n      axis: \"x\",\n      intersect: false,\n    },\n    elements: {\n      point: {\n        hoverRadius: 8,\n      },\n    },\n  };\n\n  return (\n    <div className=\"card border-0 shadow-sm h-100\">\n      <div className=\"card-body p-4\">\n        <div className=\"d-flex align-items-center justify-content-between mb-4\">\n          <h5 className=\"card-title mb-0\">Analytics Overview</h5>\n          <a\n            href=\"https://analytics.google.com\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-primary small text-decoration-none\"\n          >\n            View Report\n          </a>\n        </div>\n\n        <div style={{ height: \"300px\" }}>\n          <Line ref={chartRef} data={chartData} options={options} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAnalyticsChart.propTypes = {\n  data: PropTypes.shape({\n    labels: PropTypes.arrayOf(PropTypes.string),\n    pageViews: PropTypes.arrayOf(PropTypes.number),\n    visitors: PropTypes.arrayOf(PropTypes.number),\n    engagement: PropTypes.arrayOf(PropTypes.number),\n  }),\n  timeRange: PropTypes.string,\n};\n\nexport default AnalyticsChart;\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\n\nconst HeatmapChart = ({ data, title }) => {\n  if (!data || !data.heatmapData) {\n    return (\n      <div className=\"card border-0 shadow-sm h-100\">\n        <div className=\"card-body p-4\">\n          <div className=\"d-flex align-items-center justify-content-between mb-4\">\n            <h6 className=\"card-title mb-0\">{title}</h6>\n            <a\n              href=\"https://analytics.google.com\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-primary small text-decoration-none\"\n            >\n              View Report\n            </a>\n          </div>\n          <div\n            className=\"d-flex align-items-center justify-content-center\"\n            style={{ height: \"250px\" }}\n          >\n            <div className=\"text-center text-muted\">\n              <iconify-icon\n                icon=\"solar:chart-square-bold\"\n                className=\"fs-1 mb-3 d-block\"\n              ></iconify-icon>\n              <p className=\"mb-0 small\">No heatmap data available</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const days = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"];\n  const hours = Array.from({ length: 24 }, (_, i) => {\n    const hour = i === 0 ? 12 : i > 12 ? i - 12 : i;\n    const period = i < 12 ? \"AM\" : \"PM\";\n    return `${hour} ${period}`;\n  });\n\n  // Get the maximum value for normalization\n  const maxValue = Math.max(...data.heatmapData.flat());\n\n  // Generate intensity levels (0-4 scale like GitHub)\n  const getIntensity = (value) => {\n    if (value === 0) return 0;\n    const percentage = value / maxValue;\n    if (percentage <= 0.25) return 1;\n    if (percentage <= 0.5) return 2;\n    if (percentage <= 0.75) return 3;\n    return 4;\n  };\n\n  const getIntensityColor = (intensity) => {\n    const colors = {\n      0: \"#f3f4f6\", // gray-100\n      1: \"#dbeafe\", // blue-100\n      2: \"#93c5fd\", // blue-300\n      3: \"#3b82f6\", // blue-500\n      4: \"#1d4ed8\", // blue-700\n    };\n    return colors[intensity] || colors[0];\n  };\n\n  return (\n    <div className=\"card border-0 shadow-sm h-100\">\n      <div className=\"card-body p-4\">\n        <div className=\"d-flex align-items-center justify-content-between mb-4\">\n          <h6 className=\"card-title mb-0\">{title}</h6>\n          <a href=\"#\" className=\"text-primary small text-decoration-none\">\n            View Report\n          </a>\n        </div>\n\n        <div className=\"heatmap-container\">\n          {/* Time labels */}\n          <div className=\"heatmap-time-labels mb-2\">\n            <div></div> {/* Empty space for day labels column */}\n            <div className=\"heatmap-time-row\">\n              <div className=\"heatmap-time-label\">12 AM</div>\n              <div className=\"heatmap-time-label\">6 AM</div>\n              <div className=\"heatmap-time-label\">12 PM</div>\n              <div className=\"heatmap-time-label\">6 PM</div>\n              <div className=\"heatmap-time-label\">12 AM</div>\n            </div>\n          </div>\n\n          {/* Heatmap grid */}\n          <div className=\"heatmap-grid-container\">\n            {days.map((day, dayIndex) => (\n              <div key={day} className=\"heatmap-row\">\n                {/* Day label */}\n                <div className=\"heatmap-day-label\">{day}</div>\n\n                {/* Hour cells */}\n                <div className=\"heatmap-cells\">\n                  {Array.from({ length: 24 }, (_, hourIndex) => {\n                    const value = data.heatmapData[dayIndex]?.[hourIndex] || 0;\n                    const intensity = getIntensity(value);\n\n                    return (\n                      <div\n                        key={hourIndex}\n                        className=\"heatmap-cell\"\n                        style={{\n                          backgroundColor: getIntensityColor(intensity),\n                        }}\n                        title={`${day} ${hours[hourIndex]}: ${value} views`}\n                        onMouseEnter={(e) => {\n                          e.target.style.transform = \"scale(1.2)\";\n                          e.target.style.zIndex = \"10\";\n                        }}\n                        onMouseLeave={(e) => {\n                          e.target.style.transform = \"scale(1)\";\n                          e.target.style.zIndex = \"1\";\n                        }}\n                      />\n                    );\n                  })}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Legend */}\n          <div className=\"d-flex align-items-center justify-content-between mt-3\">\n            <span className=\"text-muted small\">Less</span>\n            <div className=\"d-flex gap-1\">\n              {[0, 1, 2, 3, 4].map((intensity) => (\n                <div\n                  key={intensity}\n                  style={{\n                    width: \"12px\",\n                    height: \"12px\",\n                    backgroundColor: getIntensityColor(intensity),\n                    borderRadius: \"2px\",\n                  }}\n                />\n              ))}\n            </div>\n            <span className=\"text-muted small\">More</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nHeatmapChart.propTypes = {\n  data: PropTypes.shape({\n    heatmapData: PropTypes.arrayOf(\n      PropTypes.shape({\n        hour: PropTypes.number,\n        day: PropTypes.number,\n        value: PropTypes.number,\n      })\n    ),\n  }),\n  title: PropTypes.string,\n};\n\nexport default HeatmapChart;\n", "// client/src/components/analytics/PostsTable.jsx\n\nimport React, { useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport PropTypes from \"prop-types\";\nimport { API_BASE_URL } from \"../../utils/api\";\n\nconst PostsTable = ({ data, loading, timeRange: _timeRange }) => {\n  const [sortBy, setSortBy] = useState(\"publishDate\");\n  const [sortOrder, setSortOrder] = useState(\"desc\");\n\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/blog-images/${filename}`;\n  };\n  const [visibleColumns, setVisibleColumns] = useState({\n    publishDate: true,\n    views: true,\n    clicks: true,\n    readingTime: true,\n    engagement: true,\n    categories: false,\n  });\n\n  const columnOptions = [\n    { key: \"publishDate\", label: \"Publish date\", icon: \"solar:calendar-bold\" },\n    { key: \"views\", label: \"Views\", icon: \"solar:eye-bold\" },\n    { key: \"clicks\", label: \"Clicks\", icon: \"solar:cursor-bold\" },\n    {\n      key: \"readingTime\",\n      label: \"Reading time\",\n      icon: \"solar:clock-circle-bold\",\n    },\n    { key: \"engagement\", label: \"Engagement\", icon: \"solar:heart-bold\" },\n    { key: \"categories\", label: \"Categories\", icon: \"solar:folder-bold\" },\n  ];\n\n  const handleSort = (column) => {\n    if (sortBy === column) {\n      setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\");\n    } else {\n      setSortBy(column);\n      setSortOrder(\"desc\");\n    }\n  };\n\n  const toggleColumn = (columnKey) => {\n    setVisibleColumns((prev) => ({\n      ...prev,\n      [columnKey]: !prev[columnKey],\n    }));\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      month: \"short\",\n      day: \"numeric\",\n      year: \"numeric\",\n    });\n  };\n\n  const formatReadingTime = (minutes) => {\n    return minutes ? `${minutes}m` : \"0m\";\n  };\n\n  const formatViewTime = (seconds) => {\n    if (!seconds || seconds === 0) return \"0s\";\n    if (seconds < 60) return `${seconds}s`;\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return remainingSeconds > 0\n      ? `${minutes}m ${remainingSeconds}s`\n      : `${minutes}m`;\n  };\n\n  const sortedData = React.useMemo(() => {\n    if (!data || !Array.isArray(data)) return [];\n\n    return [...data].sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === \"publishDate\") {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      }\n\n      if (sortOrder === \"asc\") {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n  }, [data, sortBy, sortOrder]);\n\n  if (loading) {\n    return (\n      <div className=\"card border-0 shadow-sm\">\n        <div className=\"card-body p-4\">\n          <h5 className=\"card-title mb-4\">Posts Analytics</h5>\n          <div className=\"d-flex justify-content-center py-5\">\n            <div className=\"spinner-border text-primary\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"card border-0 shadow-sm\">\n      <div className=\"card-body p-4\">\n        <div className=\"d-flex align-items-center justify-content-between mb-4\">\n          <h5 className=\"card-title mb-0\">Posts by</h5>\n          <a href=\"#\" className=\"text-primary small text-decoration-none\">\n            View Report\n          </a>\n        </div>\n\n        {/* Column Selector */}\n        <div className=\"row mb-4\">\n          <div className=\"col-md-6\">\n            <div className=\"d-flex flex-wrap gap-2\">\n              {columnOptions.map((column) => (\n                <button\n                  key={column.key}\n                  className={`btn btn-sm d-flex align-items-center gap-1 ${\n                    visibleColumns[column.key]\n                      ? \"btn-primary\"\n                      : \"btn-outline-secondary\"\n                  }`}\n                  onClick={() => toggleColumn(column.key)}\n                >\n                  <iconify-icon\n                    icon={column.icon}\n                    className=\"fs-6\"\n                  ></iconify-icon>\n                  <span>{column.label}</span>\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Table */}\n        <div className=\"table-responsive\">\n          <table className=\"table table-hover align-middle\">\n            <thead className=\"table-light\">\n              <tr>\n                <th scope=\"col\" className=\"border-0 fw-semibold text-muted\">\n                  Post title\n                </th>\n                {visibleColumns.publishDate && (\n                  <th\n                    scope=\"col\"\n                    className=\"border-0 fw-semibold text-muted cursor-pointer\"\n                    onClick={() => handleSort(\"publishDate\")}\n                  >\n                    <div className=\"d-flex align-items-center gap-1\">\n                      <iconify-icon icon=\"solar:calendar-bold\"></iconify-icon>\n                      <span>Publish date</span>\n                      {sortBy === \"publishDate\" && (\n                        <iconify-icon\n                          icon={\n                            sortOrder === \"asc\"\n                              ? \"solar:arrow-up-bold\"\n                              : \"solar:arrow-down-bold\"\n                          }\n                          className=\"text-primary\"\n                        ></iconify-icon>\n                      )}\n                    </div>\n                  </th>\n                )}\n                {visibleColumns.views && (\n                  <th\n                    scope=\"col\"\n                    className=\"border-0 fw-semibold text-muted cursor-pointer text-center\"\n                    onClick={() => handleSort(\"views\")}\n                  >\n                    <div className=\"d-flex align-items-center justify-content-center gap-1\">\n                      <iconify-icon icon=\"solar:eye-bold\"></iconify-icon>\n                      <span>Views</span>\n                      {sortBy === \"views\" && (\n                        <iconify-icon\n                          icon={\n                            sortOrder === \"asc\"\n                              ? \"solar:arrow-up-bold\"\n                              : \"solar:arrow-down-bold\"\n                          }\n                          className=\"text-primary\"\n                        ></iconify-icon>\n                      )}\n                    </div>\n                  </th>\n                )}\n                {visibleColumns.clicks && (\n                  <th\n                    scope=\"col\"\n                    className=\"border-0 fw-semibold text-muted cursor-pointer text-center\"\n                    onClick={() => handleSort(\"clicks\")}\n                  >\n                    <div className=\"d-flex align-items-center justify-content-center gap-1\">\n                      <iconify-icon icon=\"solar:cursor-bold\"></iconify-icon>\n                      <span>Clicks</span>\n                      {sortBy === \"clicks\" && (\n                        <iconify-icon\n                          icon={\n                            sortOrder === \"asc\"\n                              ? \"solar:arrow-up-bold\"\n                              : \"solar:arrow-down-bold\"\n                          }\n                          className=\"text-primary\"\n                        ></iconify-icon>\n                      )}\n                    </div>\n                  </th>\n                )}\n                {visibleColumns.readingTime && (\n                  <th\n                    scope=\"col\"\n                    className=\"border-0 fw-semibold text-muted cursor-pointer text-center\"\n                    onClick={() => handleSort(\"readingTime\")}\n                  >\n                    <div className=\"d-flex align-items-center justify-content-center gap-1\">\n                      <iconify-icon icon=\"solar:clock-circle-bold\"></iconify-icon>\n                      <span>Time</span>\n                      {sortBy === \"readingTime\" && (\n                        <iconify-icon\n                          icon={\n                            sortOrder === \"asc\"\n                              ? \"solar:arrow-up-bold\"\n                              : \"solar:arrow-down-bold\"\n                          }\n                          className=\"text-primary\"\n                        ></iconify-icon>\n                      )}\n                    </div>\n                  </th>\n                )}\n                {visibleColumns.engagement && (\n                  <th\n                    scope=\"col\"\n                    className=\"border-0 fw-semibold text-muted cursor-pointer text-center\"\n                    onClick={() => handleSort(\"engagement\")}\n                  >\n                    <div className=\"d-flex align-items-center justify-content-center gap-1\">\n                      <iconify-icon icon=\"solar:heart-bold\"></iconify-icon>\n                      <span>Engagement</span>\n                      {sortBy === \"engagement\" && (\n                        <iconify-icon\n                          icon={\n                            sortOrder === \"asc\"\n                              ? \"solar:arrow-up-bold\"\n                              : \"solar:arrow-down-bold\"\n                          }\n                          className=\"text-primary\"\n                        ></iconify-icon>\n                      )}\n                    </div>\n                  </th>\n                )}\n                {visibleColumns.categories && (\n                  <th scope=\"col\" className=\"border-0 fw-semibold text-muted\">\n                    <div className=\"d-flex align-items-center gap-1\">\n                      <iconify-icon icon=\"solar:folder-bold\"></iconify-icon>\n                      <span>Categories</span>\n                    </div>\n                  </th>\n                )}\n              </tr>\n            </thead>\n            <tbody>\n              {sortedData.length === 0 ? (\n                <tr>\n                  <td colSpan=\"7\" className=\"text-center py-5 text-muted\">\n                    <iconify-icon\n                      icon=\"solar:document-text-bold\"\n                      className=\"fs-1 mb-3 d-block\"\n                    ></iconify-icon>\n                    No posts data available for the selected period\n                  </td>\n                </tr>\n              ) : (\n                sortedData.map((post) => (\n                  <tr key={post.id}>\n                    <td>\n                      <div className=\"d-flex align-items-center gap-3\">\n                        {post.featuredImage && (\n                          <img\n                            src={getImageUrl(post.featuredImage)}\n                            alt=\"\"\n                            className=\"rounded\"\n                            style={{\n                              width: \"40px\",\n                              height: \"40px\",\n                              objectFit: \"cover\",\n                            }}\n                          />\n                        )}\n                        <div>\n                          <Link\n                            to={`/admin/blog/edit/${post.id}`}\n                            className=\"text-decoration-none fw-medium text-dark\"\n                          >\n                            {post.title}\n                          </Link>\n                          <div className=\"text-muted small\">\n                            Published on {formatDate(post.publishedAt)}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    {visibleColumns.publishDate && (\n                      <td className=\"text-muted small\">\n                        {formatDate(post.publishedAt)}\n                      </td>\n                    )}\n                    {visibleColumns.views && (\n                      <td className=\"text-center fw-medium\">\n                        {post.views?.toLocaleString() || \"0\"}\n                      </td>\n                    )}\n                    {visibleColumns.clicks && (\n                      <td className=\"text-center fw-medium\">\n                        {post.clicks?.toLocaleString() || \"0\"}\n                      </td>\n                    )}\n                    {visibleColumns.readingTime && (\n                      <td className=\"text-center text-muted\">\n                        <div className=\"small\">\n                          <div>Est: {formatReadingTime(post.readingTime)}</div>\n                          <div className=\"text-primary\">\n                            Avg: {formatViewTime(post.avgViewTime)}\n                          </div>\n                        </div>\n                      </td>\n                    )}\n                    {visibleColumns.engagement && (\n                      <td className=\"text-center fw-medium\">\n                        {post.engagement?.toLocaleString() || \"0\"}\n                      </td>\n                    )}\n                    {visibleColumns.categories && (\n                      <td>\n                        <div className=\"d-flex flex-wrap gap-1\">\n                          {post.categories?.map((category) => (\n                            <span\n                              key={category.id}\n                              className=\"badge bg-light text-dark\"\n                            >\n                              {category.name}\n                            </span>\n                          ))}\n                        </div>\n                      </td>\n                    )}\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nPostsTable.propTypes = {\n  data: PropTypes.arrayOf(\n    PropTypes.shape({\n      id: PropTypes.string,\n      title: PropTypes.string,\n      publishedAt: PropTypes.string,\n      featuredImage: PropTypes.string,\n      readingTime: PropTypes.number,\n      avgViewTime: PropTypes.number,\n      categories: PropTypes.arrayOf(\n        PropTypes.shape({\n          id: PropTypes.string,\n          name: PropTypes.string,\n        })\n      ),\n      views: PropTypes.number,\n      clicks: PropTypes.number,\n      engagement: PropTypes.number,\n    })\n  ),\n  loading: PropTypes.bool,\n  timeRange: PropTypes.string,\n};\n\nexport default PostsTable;\n", "import React, { useState, useEffect } from \"react\";\nimport { adminAPI } from \"../../utils/api\";\n\nexport default function ConversionAnalytics({ timeRange, selectedLanguage }) {\n  const [conversionData, setConversionData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadConversionData();\n  }, [timeRange, selectedLanguage]);\n\n  const loadConversionData = async () => {\n    try {\n      setLoading(true);\n      setError(\"\");\n\n      // Check if user is authenticated\n      const token = localStorage.getItem(\"adminToken\");\n      if (!token) {\n        setError(\"Authentication required. Please log in to access this page.\");\n        setLoading(false);\n        return;\n      }\n\n      const end = new Date();\n      let start = new Date();\n\n      switch (timeRange) {\n        case \"last7days\":\n          start.setDate(end.getDate() - 7);\n          break;\n        case \"last30days\":\n          start.setDate(end.getDate() - 30);\n          break;\n        case \"last90days\":\n          start.setDate(end.getDate() - 90);\n          break;\n        default:\n          start.setDate(end.getDate() - 30);\n      }\n\n      const result = await adminAPI.getConversionAnalytics(\n        start.toISOString().split(\"T\")[0],\n        end.toISOString().split(\"T\")[0],\n        selectedLanguage\n      );\n\n      // Handle response like blog analytics\n      if (result.response.ok && result.data) {\n        setConversionData(result.data.data || result.data);\n      } else {\n        console.error(\n          \"Conversion API failed:\",\n          result.response.status,\n          result.response.statusText\n        );\n        if (result.response.status === 401 || result.response.status === 403) {\n          setError(\"Authentication failed. Please log in again.\");\n          localStorage.removeItem(\"adminToken\");\n          return;\n        }\n        setError(\"Failed to load conversion data\");\n      }\n    } catch (err) {\n      console.error(\"Error loading conversion analytics:\", err);\n      setError(\"Error loading conversion analytics\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"card border-0 shadow-sm\">\n        <div className=\"card-body p-4\">\n          <h5 className=\"card-title mb-4\">Conversion Analytics</h5>\n          <div className=\"d-flex justify-content-center py-5\">\n            <div className=\"spinner-border text-primary\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"card border-0 shadow-sm\">\n        <div className=\"card-body p-4\">\n          <h5 className=\"card-title mb-4\">Conversion Analytics</h5>\n          <div className=\"alert alert-danger\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!conversionData) {\n    return (\n      <div className=\"card border-0 shadow-sm\">\n        <div className=\"card-body p-4\">\n          <h5 className=\"card-title mb-4\">Conversion Analytics</h5>\n          <div className=\"text-center py-5 text-muted\">\n            <iconify-icon\n              icon=\"solar:target-bold\"\n              className=\"fs-1 mb-3 d-block\"\n            ></iconify-icon>\n            No conversion data available for the selected time range.\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const { summary, conversions_by_source, recent_events } = conversionData;\n\n  console.log(\"🎨 Rendering ConversionAnalytics with data:\", {\n    summary,\n    conversions_by_source,\n    recent_events,\n  });\n\n  return (\n    <div className=\"card border-0 shadow-sm\">\n      <div className=\"card-body p-4\">\n        <div className=\"d-flex align-items-center justify-content-between mb-4\">\n          <h5 className=\"card-title mb-0\">Conversion Analytics</h5>\n          <span className=\"text-primary small\">\n            {summary.total_conversions} conversions • €{summary.total_value}{\" \"}\n            total value\n          </span>\n        </div>\n\n        {/* Summary Cards */}\n        <div className=\"row mb-4\">\n          <div className=\"col-sm-6 col-lg-3 mb-3\">\n            <div className=\"card border-0 shadow-sm\">\n              <div className=\"card-body\">\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"flex-shrink-0 me-3\">\n                    <div\n                      className=\"bg-primary rounded-circle d-flex align-items-center justify-content-center\"\n                      style={{ width: \"40px\", height: \"40px\" }}\n                    >\n                      <iconify-icon\n                        icon=\"solar:target-bold\"\n                        className=\"text-white\"\n                      ></iconify-icon>\n                    </div>\n                  </div>\n                  <div>\n                    <p className=\"text-muted mb-1 small\">Total Conversions</p>\n                    <h4 className=\"mb-0\">{summary.total_conversions}</h4>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"col-sm-6 col-lg-3 mb-3\">\n            <div className=\"card border-0 shadow-sm\">\n              <div className=\"card-body\">\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"flex-shrink-0 me-3\">\n                    <div\n                      className=\"bg-success rounded-circle d-flex align-items-center justify-content-center\"\n                      style={{ width: \"40px\", height: \"40px\" }}\n                    >\n                      <iconify-icon\n                        icon=\"solar:euro-bold\"\n                        className=\"text-white\"\n                      ></iconify-icon>\n                    </div>\n                  </div>\n                  <div>\n                    <p className=\"text-muted mb-1 small\">Total Value</p>\n                    <h4 className=\"mb-0\">€{summary.total_value}</h4>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"col-sm-6 col-lg-3 mb-3\">\n            <div className=\"card border-0 shadow-sm\">\n              <div className=\"card-body\">\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"flex-shrink-0 me-3\">\n                    <div\n                      className=\"bg-info rounded-circle d-flex align-items-center justify-content-center\"\n                      style={{ width: \"40px\", height: \"40px\" }}\n                    >\n                      <iconify-icon\n                        icon=\"solar:chart-bold\"\n                        className=\"text-white\"\n                      ></iconify-icon>\n                    </div>\n                  </div>\n                  <div>\n                    <p className=\"text-muted mb-1 small\">Average Value</p>\n                    <h4 className=\"mb-0\">€{summary.average_value}</h4>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"col-sm-6 col-lg-3 mb-3\">\n            <div className=\"card border-0 shadow-sm\">\n              <div className=\"card-body\">\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"flex-shrink-0 me-3\">\n                    <div\n                      className=\"bg-warning rounded-circle d-flex align-items-center justify-content-center\"\n                      style={{ width: \"40px\", height: \"40px\" }}\n                    >\n                      <iconify-icon\n                        icon=\"solar:layers-bold\"\n                        className=\"text-white\"\n                      ></iconify-icon>\n                    </div>\n                  </div>\n                  <div>\n                    <p className=\"text-muted mb-1 small\">Sources</p>\n                    <h4 className=\"mb-0\">{conversions_by_source.length}</h4>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Conversions by Source Table */}\n        <div className=\"card mb-4\">\n          <div className=\"card-header\">\n            <h5 className=\"card-title mb-0\">Conversions by Source</h5>\n          </div>\n          <div className=\"card-body p-0\">\n            <div className=\"table-responsive\">\n              <table className=\"table table-hover mb-0\">\n                <thead className=\"table-light\">\n                  <tr>\n                    <th>Source</th>\n                    <th>Conversions</th>\n                    <th>Value</th>\n                    <th>Top CTA Type</th>\n                    <th>Languages</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {conversions_by_source.map((source, index) => {\n                    const topCtaType = Object.entries(source.cta_types).sort(\n                      (a, b) => b[1] - a[1]\n                    )[0];\n                    const topLanguages = Object.entries(source.languages)\n                      .sort((a, b) => b[1] - a[1])\n                      .slice(0, 2);\n\n                    return (\n                      <tr key={index}>\n                        <td>\n                          <strong>\n                            {source.source\n                              .replace(/_/g, \" \")\n                              .replace(/\\b\\w/g, (l) => l.toUpperCase())}\n                          </strong>\n                        </td>\n                        <td>{source.total_conversions}</td>\n                        <td>€{source.total_value}</td>\n                        <td>\n                          {topCtaType\n                            ? `${topCtaType[0]} (${topCtaType[1]})`\n                            : \"-\"}\n                        </td>\n                        <td>\n                          {topLanguages\n                            .map(\n                              ([lang, count]) =>\n                                `${lang.toUpperCase()} (${count})`\n                            )\n                            .join(\", \")}\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Conversions Table */}\n        <div className=\"card mb-4\">\n          <div className=\"card-header\">\n            <h5 className=\"card-title mb-0\">Recent Conversions</h5>\n          </div>\n          <div className=\"card-body p-0\">\n            <div className=\"table-responsive\">\n              <table className=\"table table-hover mb-0\">\n                <thead className=\"table-light\">\n                  <tr>\n                    <th>Timestamp</th>\n                    <th>Source</th>\n                    <th>CTA Type</th>\n                    <th>Language</th>\n                    <th>Value</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {recent_events.slice(0, 10).map((event, index) => (\n                    <tr key={index}>\n                      <td>\n                        <small>\n                          {new Date(event.timestamp).toLocaleString()}\n                        </small>\n                      </td>\n                      <td>\n                        {event.source\n                          .replace(/_/g, \" \")\n                          .replace(/\\b\\w/g, (l) => l.toUpperCase())}\n                      </td>\n                      <td>\n                        <span className=\"badge bg-secondary\">\n                          {event.cta_type\n                            .replace(/_/g, \" \")\n                            .replace(/\\b\\w/g, (l) => l.toUpperCase())}\n                        </span>\n                      </td>\n                      <td>\n                        <span className=\"badge bg-primary\">\n                          {event.language.toUpperCase()}\n                        </span>\n                      </td>\n                      <td>€{event.value}</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import React, { useState, useEffect } from \"react\";\nimport { adminAPI } from \"../../utils/api\";\n\nexport default function StaticPagesAnalytics({ timeRange, selectedLanguage }) {\n  const [pagesData, setPagesData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadPagesData();\n  }, [timeRange, selectedLanguage]);\n\n  const loadPagesData = async () => {\n    try {\n      setLoading(true);\n      setError(\"\");\n\n      // Check if user is authenticated\n      const token = localStorage.getItem(\"adminToken\");\n      if (!token) {\n        setError(\"Authentication required. Please log in to access this page.\");\n        setLoading(false);\n        return;\n      }\n\n      const result = await adminAPI.getStaticPagesAnalytics(\n        timeRange,\n        selectedLanguage\n      );\n\n      // Handle response like blog analytics\n      if (result.response.ok && result.data) {\n        setPagesData(result.data.data || result.data);\n      } else {\n        console.error(\n          \"Static pages API failed:\",\n          result.response.status,\n          result.response.statusText\n        );\n        if (result.response.status === 401 || result.response.status === 403) {\n          setError(\"Authentication failed. Please log in again.\");\n          localStorage.removeItem(\"adminToken\");\n          return;\n        }\n        setError(\"Failed to load static pages data\");\n      }\n    } catch (err) {\n      console.error(\"Error loading static pages analytics:\", err);\n      setError(\"Error loading static pages analytics\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getPageTypeColor = (pageType) => {\n    const colors = {\n      home: \"bg-primary\",\n      about: \"bg-success\",\n      services: \"bg-info\",\n      products: \"bg-warning\",\n      contact: \"bg-danger\",\n      blog: \"bg-secondary\",\n      other: \"bg-dark\",\n    };\n    return colors[pageType] || colors.other;\n  };\n\n  const formatPageTitle = (path) => {\n    const segments = path.split(\"/\").filter(Boolean);\n    if (segments.length === 0) return \"Home\";\n    if (segments.length === 1) return segments[0];\n\n    const pagePath = segments.slice(1).join(\" / \");\n    return pagePath.replace(/-/g, \" \").replace(/\\b\\w/g, function (l) {\n      return l.toUpperCase();\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"card border-0 shadow-sm\">\n        <div className=\"card-body p-4\">\n          <h5 className=\"card-title mb-4\">Static Pages Analytics</h5>\n          <div className=\"d-flex justify-content-center py-5\">\n            <div className=\"spinner-border text-primary\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"card border-0 shadow-sm\">\n        <div className=\"card-body p-4\">\n          <h5 className=\"card-title mb-4\">Static Pages Analytics</h5>\n          <div className=\"alert alert-danger\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"card border-0 shadow-sm\">\n      <div className=\"card-body p-4\">\n        <div className=\"d-flex align-items-center justify-content-between mb-4\">\n          <h5 className=\"card-title mb-0\">Static Pages Analytics</h5>\n          <span className=\"text-primary small\">\n            {pagesData.length} pages tracked\n          </span>\n        </div>\n\n        <div className=\"table-responsive\">\n          <table className=\"table table-hover align-middle\">\n            <thead className=\"table-light\">\n              <tr>\n                <th>Page</th>\n                <th>Type</th>\n                <th>Views</th>\n                <th>Unique Visitors</th>\n                <th>Languages</th>\n              </tr>\n            </thead>\n            <tbody>\n              {pagesData.map((page, index) => (\n                <tr key={index}>\n                  <td>\n                    <div>\n                      <div className=\"fw-bold\">\n                        {page.page_title || formatPageTitle(page.path)}\n                      </div>\n                      <small className=\"text-muted\">{page.path}</small>\n                    </div>\n                  </td>\n                  <td>\n                    <span\n                      className={`badge ${getPageTypeColor(\n                        page.page_type\n                      )} text-white`}\n                    >\n                      {page.page_type}\n                    </span>\n                  </td>\n                  <td>{page.views.toLocaleString()}</td>\n                  <td>{page.unique_visitors.toLocaleString()}</td>\n                  <td>\n                    <small>\n                      {page.languages\n                        ? Object.entries(page.languages)\n                            .map(\n                              ([lang, count]) =>\n                                `${lang.toUpperCase()} (${count})`\n                            )\n                            .join(\", \")\n                        : \"-\"}\n                    </small>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {pagesData.length === 0 && (\n          <div className=\"text-center py-4\">\n            <p className=\"text-muted mb-0\">\n              No page data available for the selected time range.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": ["languages", "i18n", "Backend", "LanguageDetector", "initReactI18next", "value", "format", "lng", "ns", "key", "fallback<PERSON><PERSON><PERSON>", "GDPRConsent", "t", "useTranslation", "showBanner", "setShowBanner", "useState", "showDetails", "setShowDetails", "useEffect", "customizeRequested", "instantShown", "consent", "initializeGoogleAnalytics", "consentSettings", "handleAcceptAll", "handleRejectAll", "handleCustomize", "settings", "jsxs", "Fragment", "jsx", "GDPRDetailsModal", "onClose", "onSave", "setSettings", "handleSave", "e", "prev", "PropTypes", "ScrollTopBehaviour", "pathname", "useLocation", "SUPPORTED_LANGUAGES", "DEFAULT_LANGUAGE", "LanguageRedirect", "children", "navigate", "useNavigate", "location", "browserLang", "storedLang", "preferredLang", "currentPath", "searchParams", "getCurrentLanguage", "match", "createLanguageUrl", "to", "currentLanguage", "cleanPath", "LanguageAwareLink", "className", "props", "languageAwareUrl", "Link", "LanguageSelector", "isOpen", "setIsOpen", "dropdownRef", "useRef", "isMobile", "setIsMobile", "handleResize", "handleClickOutside", "event", "toggleDropdown", "handleLanguageChange", "langCode", "newPath", "langData", "getApiBaseUrl", "API_BASE_URL", "apiCall", "endpoint", "options", "url", "isFormData", "defaultHeaders", "token", "config", "response", "contentType", "data", "error", "authAPI", "credentials", "blogAPI", "params", "queryString", "slug", "formData", "id", "language", "limit", "finalParams", "commentData", "adminAPI", "timeRange", "startDate", "endDate", "file", "category", "tag", "productData", "productsAPI", "categoriesAPI", "tagsAPI", "archiveAPI", "AnimatedText", "text", "elm", "React", "elm2", "i2", "ParallaxContainer", "jarall<PERSON>", "UnifiedSEO", "title", "description", "type", "schema", "keywords", "image", "imageAlt", "author", "publishedAt", "modifiedAt", "alternateUrls", "noIndex", "canonicalUrl", "baseUrl", "businessName", "languageUrls", "supportedLanguages", "urls", "lang", "currentUrl", "getLanguageSpecificData", "localeMap", "languageMap", "locale", "formattedTitle", "defaultImageAlt", "robotsContent", "<PERSON><PERSON><PERSON>", "altLocale", "schemaItem", "index", "Error<PERSON>ou<PERSON><PERSON>", "errorInfo", "portfolioItems", "portfolios1", "portfolios2", "portfolios3", "portfolios4", "portfolios5", "portfolios6", "portfolios7", "portfolios8", "portfolios9", "portfolios10", "portfolios11", "portfolios12", "portfolios13", "allPortfolios", "MetaComponent", "meta", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pagination", "currentPage", "totalPages", "onPageChange", "handlePageChange", "page", "pageNumbers", "pages", "i", "RelatedProjects", "Gallery", "item", "<PERSON><PERSON>", "ref", "open", "ProductGallery", "images", "productTitle", "currentImageIndex", "setCurrentImageIndex", "isFullscreen", "setIsFullscreen", "currentImage", "hasMultipleImages", "nextImage", "prevImage", "goToImage", "openFullscreen", "closeFullscreen", "getImageUrl", "filename", "Comments", "comments", "formatDate", "dateString", "comment", "reply", "Form", "blogSlug", "onCommentSubmitted", "setFormData", "isSubmitting", "setIsSubmitting", "message", "setMessage", "handleChange", "name", "handleSubmit", "result", "_a", "Widget1", "searchInputClass", "categories", "setCategories", "tags", "setTags", "archiveData", "setArchiveData", "latestPosts", "setLatestPosts", "searchQuery", "setSearch<PERSON>uery", "categoriesResult", "tagsResult", "archiveResult", "postsResult", "posts", "handleSearch", "getTranslation", "post", "field", "translation", "archive", "Map", "mapOpen", "setMapOpen", "pre", "SEO", "canonical", "imageWidth", "imageHeight", "twitter<PERSON><PERSON>le", "AdminLayout", "sidebarCollapsed", "setSidebarCollapsed", "mobileSidebarOpen", "setMobileSidebarOpen", "userDropdownOpen", "setUserDropdownOpen", "handleLogout", "toggleUserDropdown", "isActive", "path", "user", "menuItems", "TipTapEditor", "content", "onChange", "placeholder", "editor", "useEditor", "StarterKit", "Code", "CodeBlock", "html", "currentC<PERSON>nt", "Editor<PERSON><PERSON><PERSON>", "TimeRangeSelector", "comparedPeriod", "selectedOption", "option", "handleOptionClick", "optionValue", "AnalyticsOverview", "formatNumber", "num", "formatPercentage", "current", "previous", "change", "MetricCard", "previousValue", "icon", "color", "percentage", "_b", "_c", "_d", "_e", "_f", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "AnalyticsChart", "chartRef", "chartData", "context", "Line", "HeatmapChart", "days", "hours", "_", "hour", "period", "maxValue", "getIntensity", "getIntensityColor", "intensity", "colors", "day", "dayIndex", "hourIndex", "PostsTable", "loading", "_timeRange", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "visibleColumns", "setVisibleColumns", "columnOptions", "handleSort", "column", "toggleColumn", "column<PERSON>ey", "formatReadingTime", "minutes", "formatViewTime", "seconds", "remainingSeconds", "sortedData", "a", "b", "aValue", "bValue", "ConversionAnalytics", "selectedLanguage", "conversionData", "setConversionData", "setLoading", "setError", "loadConversionData", "end", "start", "err", "summary", "conversions_by_source", "recent_events", "source", "topCtaType", "topLanguages", "l", "count", "StaticPagesAnalytics", "pagesData", "setPagesData", "loadPagesData", "getPageTypeColor", "pageType", "formatPageTitle", "segments"], "mappings": "4bAMA,MAAMA,EAAY,CAChB,GAAI,CAAE,KAAM,UAAW,KAAM,MAAO,EACpC,GAAI,CAAE,KAAM,QAAS,KAAM,MAAO,EAClC,GAAI,CAAE,KAAM,QAAS,KAAM,MAAO,EAClC,GAAI,CAAE,KAAM,UAAW,KAAM,MAAO,EACpC,GAAI,CAAE,KAAM,UAAW,KAAM,MAAO,CACtC,EAGAC,GACG,IAAIC,EAAO,EACX,IAAIC,EAAgB,EACpB,IAAIC,CAAgB,EACpB,KAAK,CAEJ,IAAK,KACL,YAAa,KACb,cAAe,OAAO,KAAKJ,CAAS,EAGpC,GAAI,CAAC,aAAa,EAClB,UAAW,cAGX,QAAS,CACP,SAAU,8BACZ,EAGA,UAAW,CAET,MAAO,CACL,OACA,eACA,YACA,SACF,EAGA,OAAQ,CAAC,cAAc,EAGvB,mBAAoB,aAGpB,oBAAqB,EACrB,eAAgB,GAGhB,oBAAqB,EAGrB,eAAgB,EAClB,EAGA,cAAe,CACb,YAAa,GACb,gBAAiB,IACjB,OAAQ,CAACK,EAAOC,EAAQC,IAClBD,IAAW,YAAoBD,EAAM,YAAY,EACjDC,IAAW,YAAoBD,EAAM,YAAY,EACjDC,IAAW,aACND,EAAM,OAAO,CAAC,EAAE,cAAgBA,EAAM,MAAM,CAAC,EAC/CA,CAEX,EAGA,MAAO,CACL,YAAa,GACb,SAAU,kBACV,cAAe,GACf,oBAAqB,GACrB,2BAA4B,GAC5B,2BAA4B,CAAC,KAAM,SAAU,IAAK,KAAM,MAAM,CAChE,EAGA,MAAO,GAGP,KAAM,eACN,QAAS,OAAO,KAAKL,CAAS,EAG9B,UAAW,GAGX,aAAc,IACd,YAAa,IAGb,gBAAiB,IACjB,iBAAkB,IAGlB,cAAe,GACf,kBAAmB,GACnB,WAAY,GAGZ,WAAY,GAGZ,YAAa,GAGb,YAAa,GACb,cAAe,UAGf,kBAAmB,CAACO,EAAKC,EAAIC,EAAKC,IAAkB,CAIpD,EAGA,cAAe,GAGf,oBAAqB,EACvB,CAAC,EC5HH,MAAMC,GAAc,IAAM,CAClB,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAS,EAAK,EAC5C,CAACC,EAAaC,CAAc,EAAIF,EAAAA,SAAS,EAAK,EAEpDG,EAAAA,UAAU,IAAM,CAER,MAAAC,EAAqB,SAAS,KAAK,UAAU,SACjD,0BACF,EACMC,EAAe,SAAS,KAAK,UAAU,SAAS,oBAAoB,EAE1E,GAAID,EAEFF,EAAe,EAAI,EACV,SAAA,KAAK,UAAU,OAAO,0BAA0B,UAChD,CAACG,EAAc,CAElB,MAAAC,EAAU,aAAa,QAAQ,cAAc,EAC/CA,EACwBC,EAAA,KAAK,MAAMD,CAAO,CAAC,EAG7CP,EAAc,EAAI,CACpB,CAGJ,EAAG,EAAE,EAEC,MAAAQ,EAA6BC,GAAoB,CAErD,WAAW,IAAM,CACX,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,UAAW,SAAU,CAC/B,kBAAmBA,EAAgB,UAAY,UAAY,SAC3D,WAAYA,EAAgB,UAAY,UAAY,SACpD,aAAcA,EAAgB,UAAY,UAAY,SACtD,mBAAoBA,EAAgB,UAAY,UAAY,QAAA,CAC7D,GAEF,CAAC,CACN,EAEMC,EAAkB,IAAM,CAC5B,MAAMH,EAAU,CACd,UAAW,GACX,UAAW,GACX,UAAW,GACX,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEA,aAAa,QAAQ,eAAgB,KAAK,UAAUA,CAAO,CAAC,EAC5DC,EAA0BD,CAAO,EACjCP,EAAc,EAAK,EAGnB,WAAW,IAAM,CACX,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,kBAAmB,CACtC,eAAgB,OAChB,YAAa,YAAA,CACd,GAEF,GAAG,CACR,EAEMW,EAAkB,IAAM,CAC5B,MAAMJ,EAAU,CACd,UAAW,GACX,UAAW,GACX,UAAW,GACX,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEA,aAAa,QAAQ,eAAgB,KAAK,UAAUA,CAAO,CAAC,EAC5DC,EAA0BD,CAAO,EACjCP,EAAc,EAAK,EAGnB,WAAW,IAAM,CACX,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,iBAAkB,CACrC,eAAgB,OAChB,YAAa,YAAA,CACd,GAEF,GAAG,CACR,EAEMY,EAAmBC,GAAa,CACpC,MAAMN,EAAU,CACd,UAAW,GACX,UAAWM,EAAS,UACpB,UAAWA,EAAS,UACpB,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EAEA,aAAa,QAAQ,eAAgB,KAAK,UAAUN,CAAO,CAAC,EAC5DC,EAA0BD,CAAO,EACjCP,EAAc,EAAK,EACnBG,EAAe,EAAK,EAGpB,WAAW,IAAM,CACX,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,qBAAsB,CACzC,eAAgB,OAChB,YAAa,cAAcU,EAAS,SAAS,gBAAgBA,EAAS,SAAS,EAAA,CAChF,GAEF,GAAG,CACR,EAEI,OAACd,EAKDe,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,wBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAF,OAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,kBAAmB,SAAAnB,EAAE,YAAY,EAAE,QAChD,IAAE,CAAA,UAAU,wBAAyB,SAAAA,EAAE,kBAAkB,CAAE,CAAA,CAAA,CAAA,CAC9D,CACF,CAAA,QACC,MAAI,CAAA,UAAU,oBACb,SAACiB,EAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,UAAU,yDACV,QAASN,EACT,mBAAiB,IAEjB,SAAAI,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAnB,EAAE,gBAAgB,EACrB,EACAmB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAnB,EAAE,gBAAgB,CACrB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAmB,EAAA,IAAC,SAAA,CACC,UAAU,gDACV,QAASL,EACT,mBAAiB,IAEjB,SAAAG,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAnB,EAAE,gBAAgB,EACrB,EACAmB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAnB,EAAE,gBAAgB,CACrB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAmB,EAAA,IAAC,SAAA,CACC,UAAU,gDACV,QAAS,IAAMb,EAAe,EAAI,EAClC,mBAAiB,IAEjB,SAAAW,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAnB,EAAE,gBAAgB,EACrB,EACAmB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAnB,EAAE,gBAAgB,CACrB,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGCK,GACCc,EAAA,IAACC,EAAA,CACC,QAAS,IAAMd,EAAe,EAAK,EACnC,OAAQS,EACR,EAAAf,CAAA,CAAA,CACF,EAEJ,EAxEsB,IA0E1B,EAEMoB,EAAmB,CAAC,CAAE,QAAAC,EAAS,OAAAC,EAAQ,EAAAtB,KAAQ,CACnD,KAAM,CAACgB,EAAUO,CAAW,EAAInB,WAAS,CACvC,UAAW,GACX,UAAW,EAAA,CACZ,EAEKoB,EAAa,IAAM,CACvBF,EAAON,CAAQ,CACjB,EAGE,OAAAG,EAAAA,IAAC,OAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,uBACb,SAAAF,OAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,0BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,wBACX,SAAAnB,EAAE,qBAAqB,EAC1B,EACAmB,EAAAA,IAAC,SAAO,CAAA,UAAU,aAAa,QAASE,EACtC,SAACF,EAAA,IAAA,IAAA,CAAE,UAAU,kBAAmB,CAAA,CAClC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,gCACX,SAAAnB,EAAE,gBAAgB,EACrB,QACC,OAAK,CAAA,UAAU,sBACb,SAAAA,EAAE,eAAe,CACpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,IAAE,CAAA,UAAU,qBACV,SAAAA,EAAE,oBAAoB,CACzB,CAAA,CAAA,EACF,EAEAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,2BACX,SAAAnB,EAAE,gBAAgB,EACrB,EACAiB,EAAAA,KAAC,QAAM,CAAA,UAAU,cACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASH,EAAS,UAClB,SAAWS,GACTF,EAAaG,IAAU,CACrB,GAAGA,EACH,UAAWD,EAAE,OAAO,OAAA,EACpB,CAAA,CAEN,EACAN,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAc,CAAA,CAAA,CAChC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,IAAE,CAAA,UAAU,qBACV,SAAAnB,EAAE,oBAAoB,CACzB,CAAA,CAAA,EACF,EAEAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,2BACX,SAAAnB,EAAE,gBAAgB,EACrB,EACAiB,EAAAA,KAAC,QAAM,CAAA,UAAU,cACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASH,EAAS,UAClB,SAAWS,GACTF,EAAaG,IAAU,CACrB,GAAGA,EACH,UAAWD,EAAE,OAAO,OAAA,EACpB,CAAA,CAEN,EACAN,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAc,CAAA,CAAA,CAChC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,IAAE,CAAA,UAAU,qBACV,SAAAnB,EAAE,oBAAoB,CACzB,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,oBACb,SAACiB,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,UAAU,gDACV,QAASE,EACT,mBAAiB,IAEjB,SAAAJ,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAnB,EAAE,aAAa,EAClB,EACAmB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAnB,EAAE,aAAa,CAClB,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAmB,EAAA,IAAC,SAAA,CACC,UAAU,yCACV,QAASK,EACT,mBAAiB,IAEjB,SAAAP,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAAAE,MAAC,OAAK,CAAA,UAAU,kBACb,SAAAnB,EAAE,sBAAsB,EAC3B,EACAmB,MAAC,QAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAAnB,EAAE,sBAAsB,CAC3B,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EAEAoB,EAAiB,UAAY,CAC3B,QAASO,EAAU,KAAK,WACxB,OAAQA,EAAU,KAAK,WACvB,EAAGA,EAAU,KAAK,UACpB,EC7UA,SAAwBC,IAAqB,CACrC,KAAA,CAAE,SAAAC,CAAS,EAAIC,EAAY,EAEjCvB,OAAAA,EAAAA,UAAU,IAAM,CACP,OAAA,SAAS,EAAG,CAAC,CAAA,EACnB,CAACsB,CAAQ,CAAC,EAEJV,EAAA,IAAAD,EAAA,SAAA,EAAA,CACX,CCRA,MAAMa,EAAsB,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,EACnDC,GAAmB,KAEnBC,GAAmB,CAAC,CAAE,SAAAC,KAAe,CACzC,MAAMC,EAAWC,EAAY,EACvBC,EAAWP,EAAY,EACvB,CAAE,KAAAzC,CAAK,EAAIY,EAAe,EAEhCM,OAAAA,EAAAA,UAAU,IAAM,CAEd,MAAM+B,EAAc,UAAU,SAAS,MAAM,GAAG,EAAE,CAAC,EAC7CC,EAAa,aAAa,QAAQ,YAAY,EAG9CC,EAAgBT,EAAoB,SAASQ,CAAU,EACzDA,EACAR,EAAoB,SAASO,CAAW,EACxCA,EACAN,GAGA3C,EAAK,WAAamD,GACpBnD,EAAK,eAAemD,CAAa,EAInC,MAAMC,EAAcJ,EAAS,SACvBK,EAAeL,EAAS,OAG1BI,IAAgB,IACTN,EAAA,IAAIK,CAAa,GAAGE,CAAY,GAAI,CAAE,QAAS,GAAM,EAG9DP,EAAS,IAAIK,CAAa,GAAGC,CAAW,GAAGC,CAAY,GAAI,CACzD,QAAS,EAAA,CACV,CAEF,EAAA,CAACP,EAAUE,EAAUhD,CAAI,CAAC,EAGtB,IACT,ECtCMsD,GAAsBd,GAAa,CACjC,MAAAe,EAAQf,EAAS,MAAM,iBAAiB,EACvC,OAAAe,EAAQA,EAAM,CAAC,EAAI,IAC5B,EAGMC,EAAoB,CAACC,EAAIC,IAAoB,CAE7C,GAAAD,EAAG,MAAM,eAAe,EACnB,OAAAA,EAIT,GAAIA,IAAO,IACT,MAAO,IAAIC,CAAe,GAI5B,MAAMC,EAAYF,EAAG,WAAW,GAAG,EAAIA,EAAK,IAAIA,CAAE,GAC3C,MAAA,IAAIC,CAAe,GAAGC,CAAS,EACxC,EAGMC,GAAoB,CAAC,CAAE,GAAAH,EAAI,SAAAZ,EAAU,UAAAgB,EAAW,GAAGC,KAAY,CACnE,MAAMd,EAAWP,EAAY,EACvBiB,EAAkBJ,GAAmBN,EAAS,QAAQ,EACtDe,EAAmBP,EAAkBC,EAAIC,CAAe,EAE9D,aACGM,EAAK,CAAA,GAAID,EAAkB,UAAAF,EAAuB,GAAGC,EACnD,SAAAjB,EACH,CAEJ,ECnCA,SAAwBoB,IAAmB,CACnC,KAAA,CAAE,KAAAjE,CAAK,EAAIY,EAAe,EAC1BkC,EAAWC,EAAY,EACvBC,EAAWP,EAAY,EACvB,CAACyB,EAAQC,CAAS,EAAIpD,EAAAA,SAAS,EAAK,EACpCqD,EAAcC,SAAO,IAAI,EACzB,CAACC,EAAUC,CAAW,EAAIxD,EAAA,SAC9B,OAAO,OAAW,IAAc,OAAO,YAAc,KAAO,EAC9D,EAGAG,EAAAA,UAAU,IAAM,CACd,MAAMsD,EAAe,IAAM,CACbD,EAAA,OAAO,YAAc,IAAI,CACvC,EAEO,cAAA,iBAAiB,SAAUC,CAAY,EACvC,IAAM,CACJ,OAAA,oBAAoB,SAAUA,CAAY,CACnD,CACF,EAAG,EAAE,EAGLtD,EAAAA,UAAU,IAAM,CACd,SAASuD,EAAmBC,EAAO,CAC7BN,EAAY,SAAW,CAACA,EAAY,QAAQ,SAASM,EAAM,MAAM,GACnEP,EAAU,EAAK,CACjB,CAGO,gBAAA,iBAAiB,YAAaM,CAAkB,EAClD,IAAM,CACF,SAAA,oBAAoB,YAAaA,CAAkB,CAC9D,CACF,EAAG,EAAE,EAEL,MAAME,EAAiB,IAAM,CAC3BR,EAAU,CAACD,CAAM,CACnB,EAEMU,EAAwBC,GAAa,CAEzC7E,EAAK,eAAe6E,CAAQ,EAC5BV,EAAU,EAAK,EAGf,MAAMf,EAAcJ,EAAS,SACvBK,EAAeL,EAAS,OAG1B,IAAA8B,EACA1B,EAAY,MAAM,mBAAmB,GAEvC0B,EAAU1B,EAAY,QAAQ,cAAe,IAAIyB,CAAQ,EAAE,EAEvDC,IAAY,IAAID,CAAQ,KAC1BC,EAAU,IAAID,CAAQ,MAIdC,EAAAtB,EAAkBJ,EAAayB,CAAQ,EAI1C/B,EAAA,GAAGgC,CAAO,GAAGzB,CAAY,GAAI,CAAE,QAAS,GAAM,EAGnD,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,kBAAmB,CACtC,eAAgB,mBAChB,YAAa,GAAGrD,EAAK,QAAQ,OAAO6E,CAAQ,GAC5C,cAAe7E,EAAK,SACpB,YAAa6E,EACb,QAAS,cAAA,CACV,CAEL,EAEMnB,EAAkB1D,EAAK,UAAY,KAGzC,OAAIsE,EAECxC,EAAAA,IAAA,MAAA,CAAI,UAAU,qDACZ,SAAO,OAAA,QAAQ/B,CAAS,EAAE,IAAI,CAAC,CAAC8E,EAAUE,CAAQ,IACjDjD,EAAA,IAAC,SAAA,CAEC,UAAW,yBACT+C,IAAanB,EACT,aACA,uBACN,GACA,QAAS,IAAMkB,EAAqBC,CAAQ,EAC5C,aAAY,aAAaE,EAAS,IAAI,GAEtC,SAACjD,EAAA,IAAA,OAAA,CAAK,UAAU,gDACb,SACH+C,CAAA,CAAA,CAAA,EAXKA,CAaR,CAAA,EACH,EAMDjD,EAAAA,KAAA,MAAA,CAAI,UAAU,sCAAsC,IAAKwC,EACxD,SAAA,CAAAtC,EAAA,IAAC,SAAA,CACC,UAAU,6DACV,QAAS6C,EACT,gBAAeT,EACf,gBAAc,OAEd,SAACpC,EAAA,IAAA,OAAA,CAAK,UAAU,+BAAgC,SAAgB4B,CAAA,CAAA,CAAA,CAClE,EAECQ,GACCpC,EAAA,IAAC,MAAI,CAAA,UAAU,uEACZ,SAAO,OAAA,QAAQ/B,CAAS,EAAE,IAAI,CAAC,CAAC8E,EAAUE,CAAQ,IACjDjD,EAAA,IAAC,SAAA,CAEC,UAAW,qDACT+C,IAAanB,EAAkB,SAAW,EAC5C,GACA,QAAS,IAAMkB,EAAqBC,CAAQ,EAG5C,SAAC/C,EAAAA,IAAA,OAAA,CAAK,UAAU,gBAAiB,WAAS,IAAK,CAAA,CAAA,EAP1C+C,CAAA,CASR,CACH,CAAA,CAAA,EAEJ,CAEJ,CCzIA,MAAMG,GAAgB,IAGX,2BAOEC,EAAeD,GAAc,EAG7BE,EAAU,MAAOC,EAAUC,EAAU,KAAO,CACvD,MAAMC,EAAM,GAAGJ,CAAY,GAAGE,CAAQ,GAGhCG,EAAaF,EAAQ,gBAAgB,SAErCG,EAAiB,CAAC,EAInBD,IACHC,EAAe,cAAc,EAAI,oBAI/BJ,EAAS,SAAS,UAAU,IAC9BI,EAAe,WAAW,EAAI,oCAI1B,MAAAC,EAAQ,aAAa,QAAQ,YAAY,EAC3CA,IACaD,EAAA,cAAmB,UAAUC,CAAK,IAGnD,MAAMC,EAAS,CACb,GAAGL,EACH,QAAS,CACP,GAAGG,EACH,GAAGH,EAAQ,OAAA,CAEf,EAEI,GAAA,CACF,MAAMM,EAAW,MAAM,MAAML,EAAKI,CAAM,EAGlCE,EAAcD,EAAS,QAAQ,IAAI,cAAc,EACvD,GAAIC,GAAeA,EAAY,SAAS,kBAAkB,EAAG,CACrD,MAAAC,EAAO,MAAMF,EAAS,KAAK,EAC1B,MAAA,CAAE,SAAAA,EAAU,KAAAE,CAAK,CAAA,KAEjB,OAAA,CAAE,SAAAF,EAAU,KAAM,IAAK,QAEzBG,EAAO,CACN,cAAA,MAAM,mBAAoBA,CAAK,EACjCA,CAAA,CAEV,EAGaC,GAAU,CACrB,MAAQC,GACNb,EAAQ,cAAe,CACrB,OAAQ,OACR,KAAM,KAAK,UAAUa,CAAW,CAAA,CACjC,EAEH,MAAO,IAAMb,EAAQ,UAAU,EAE/B,OAAQ,IAAMA,EAAQ,eAAgB,CAAE,OAAQ,MAAQ,CAAA,CAC1D,EAEac,EAAU,CACrB,SAAU,CAACC,EAAS,KAAO,CACzB,MAAMC,EAAc,IAAI,gBAAgBD,CAAM,EAAE,SAAS,EACzD,OAAOf,EAAQ,QAAQgB,EAAc,IAAIA,CAAW,GAAK,EAAE,EAAE,CAC/D,EAEA,QAAUC,GAASjB,EAAQ,SAASiB,CAAI,EAAE,EAE1C,WAAaC,GACXlB,EAAQ,QAAS,CACf,OAAQ,OACR,KAAMkB,EACN,QAAS,CAAA,CAAC,CACX,EAEH,WAAY,CAACC,EAAID,IACflB,EAAQ,SAASmB,CAAE,GAAI,CACrB,OAAQ,MACR,KAAMD,EACN,QAAS,CAAA,CAAC,CACX,EAEH,WAAaC,GAAOnB,EAAQ,SAASmB,CAAE,GAAI,CAAE,OAAQ,SAAU,EAE/D,iBAAmBA,GACjBnB,EAAQ,SAASmB,CAAE,qBAAsB,CACvC,OAAQ,OAAA,CACT,EAGH,iBAAkB,CAACC,EAAW,KAAMC,EAAQ,IACnCrB,EACL,6BAA6BqB,CAAK,4BAA4BD,CAAQ,EACxE,EAIF,aAAc,CAACL,EAAS,KAAO,CAU7B,MAAMO,EAAc,CAAE,GARA,CACpB,SAAU,KACV,KAAM,EACN,MAAO,EACP,UAAW,MACb,EAGwC,GAAGP,CAAO,EAC5CC,EAAc,IAAI,gBAAgBM,CAAW,EAAE,SAAS,EAEvD,OAAAtB,EAAQ,SAASgB,CAAW,EAAE,CACvC,EAGA,cAAe,CAACC,EAAMM,IACpBvB,EAAQ,SAASiB,CAAI,YAAa,CAChC,OAAQ,OACR,KAAM,KAAK,UAAUM,CAAW,EAChC,QAAS,CACP,eAAgB,kBAAA,CAEnB,CAAA,CACL,EAEaC,EAAW,CACtB,aAAc,IAAMxB,EAAQ,kBAAkB,EAG9C,iBAAkB,CAACyB,EAAY,aAAcL,EAAW,QACtDpB,EACE,mCAAmCyB,CAAS,aAAaL,CAAQ,EACnE,EAEF,sBAAuB,CAACK,EAAY,aAAcL,EAAW,QAC3DpB,EACE,oCAAoCyB,CAAS,aAAaL,CAAQ,EACpE,EAEF,uBAAwB,CAACM,EAAWC,EAASP,EAAW,QACtDpB,EACE,0CAA0C0B,CAAS,YAAYC,CAAO,aAAaP,CAAQ,EAC7F,EAEF,wBAAyB,CAACK,EAAY,aAAcL,EAAW,QAC7DpB,EACE,oCAAoCyB,CAAS,aAAaL,CAAQ,EACpE,EAEF,SAAU,CAACL,EAAS,KAAO,CACzB,MAAMC,EAAc,IAAI,gBAAgBD,CAAM,EAAE,SAAS,EACzD,OAAOf,EAAQ,eAAegB,EAAc,IAAIA,CAAW,GAAK,EAAE,EAAE,CACtE,EAEA,QAAUG,GAAOnB,EAAQ,gBAAgBmB,CAAE,EAAE,EAE7C,YAAcS,GAAS,CACf,MAAAV,EAAW,IAAI,SACZ,OAAAA,EAAA,OAAO,QAASU,CAAI,EACtB5B,EAAQ,sBAAuB,CACpC,OAAQ,OACR,KAAMkB,EACN,QAAS,CAAA,CAAC,CACX,CACH,EAEA,cAAe,IAAMlB,EAAQ,mBAAmB,EAEhD,eAAiB6B,GACf7B,EAAQ,oBAAqB,CAC3B,OAAQ,OACR,KAAM,KAAK,UAAU6B,CAAQ,CAAA,CAC9B,EAEH,eAAgB,CAACV,EAAIU,IACnB7B,EAAQ,qBAAqBmB,CAAE,GAAI,CACjC,OAAQ,MACR,KAAM,KAAK,UAAUU,CAAQ,CAAA,CAC9B,EAEH,eAAiBV,GACfnB,EAAQ,qBAAqBmB,CAAE,GAAI,CACjC,OAAQ,QAAA,CACT,EAEH,QAAS,IAAMnB,EAAQ,aAAa,EAEpC,UAAY8B,GACV9B,EAAQ,cAAe,CACrB,OAAQ,OACR,KAAM,KAAK,UAAU8B,CAAG,CAAA,CACzB,EAEH,UAAW,CAACX,EAAIW,IACd9B,EAAQ,eAAemB,CAAE,GAAI,CAC3B,OAAQ,MACR,KAAM,KAAK,UAAUW,CAAG,CAAA,CACzB,EAEH,UAAYX,GACVnB,EAAQ,eAAemB,CAAE,GAAI,CAC3B,OAAQ,QAAA,CACT,EAGH,YAAa,CAACJ,EAAS,KAAO,CAC5B,MAAMC,EAAc,IAAI,gBAAgBD,CAAM,EAAE,SAAS,EACzD,OAAOf,EAAQ,kBAAkBgB,EAAc,IAAIA,CAAW,GAAK,EAAE,EAAE,CACzE,EAEA,WAAaG,GAAOnB,EAAQ,mBAAmBmB,CAAE,EAAE,EAEnD,cAAgBY,GACd/B,EAAQ,kBAAmB,CACzB,OAAQ,OACR,KAAM+B,EACN,QAAS,CAAA,CAAC,CACX,EAEH,cAAe,CAACZ,EAAIY,IAClB/B,EAAQ,mBAAmBmB,CAAE,GAAI,CAC/B,OAAQ,MACR,KAAMY,EACN,QAAS,CAAA,CAAC,CACX,EAEH,cAAgBZ,GACdnB,EAAQ,mBAAmBmB,CAAE,GAAI,CAC/B,OAAQ,QACT,CAAA,CACL,EAGaa,GAAc,CACzB,YAAa,CAACjB,EAAS,KAAO,CAC5B,MAAMC,EAAc,IAAI,gBAAgBD,CAAM,EAAE,SAAS,EACzD,OAAOf,EAAQ,YAAYgB,EAAc,IAAIA,CAAW,GAAK,EAAE,EAAE,CACnE,EAEA,WAAY,CAACC,EAAMG,EAAW,OAC5BpB,EAAQ,aAAaiB,CAAI,aAAaG,CAAQ,EAAE,CACpD,EAGaa,GAAgB,CAC3B,cAAe,IAAMjC,EAAQ,aAAa,CAC5C,EAGakC,GAAU,CACrB,QAAS,IAAMlC,EAAQ,OAAO,CAChC,EAGamC,GAAa,CACxB,WAAY,IAAMnC,EAAQ,eAAe,CAC3C,EC/QA,SAAwBoC,GAAa,CACnC,KAAAC,EAAO,wCACT,EAAG,CACD,OAEIzF,MAAAD,EAAAA,SAAA,CAAA,SAAAC,EAAA,IAAC,OAAA,CACC,UAAU,wCACV,iBAAe,QACf,cAAY,OACZ,MAAO,CACL,eAAgByF,EAAK,MAAM,GAAG,EAAE,OAChC,eAAgBA,EAAK,MAAM,EAAE,EAAE,OAC/B,WAAY,SACd,EAEC,SACEA,EAAA,KACA,EAAA,MAAM,GAAG,EACT,IAAI,CAACC,EAAK,IACR5F,EAAA,KAAA6F,EAAM,SAAN,CACC,SAAA,CAAA3F,EAAA,IAAC,OAAA,CACC,UAAU,OACV,YAAU,OACV,MAAO,CAAE,eAAgB,CAAE,EAE1B,WAAI,MAAM,EAAE,EAAE,IAAI,CAAC4F,EAAMC,IACxB7F,EAAA,IAAC,OAAA,CAEC,UAAU,OACV,YAAU,IACV,MAAO,CAAE,eAAgB,EAAI6F,CAAG,EAE/B,SAAAD,CAAA,EALIC,CAOR,CAAA,CAAA,CACH,EACC7F,EAAA,IAAA,OAAA,CAAK,UAAU,aAAa,SAAC,GAAA,CAAA,CAAA,CAAA,EAjBX,CAkBrB,CACD,CAAA,CAAA,EAEP,CAEJ,CAEAwF,GAAa,UAAY,CACvB,KAAMhF,EAAU,MAClB,EC5CA,SAAwBsF,GAAkB9D,EAAO,CAC/C5C,OAAAA,EAAAA,UAAU,IAAM,CACL2G,GAAA,SAAS,iBAAiB,aAAa,EAAG,CACjD,MAAO,EAAA,CACR,CACH,EAAG,EAAE,EAEH/F,EAAA,IAAC,MAAA,CAEE,GAAGgC,EAEH,SAAMA,EAAA,QAAA,CACT,CAEJ,CAEA8D,GAAkB,UAAY,CAC5B,SAAUtF,EAAU,IACtB,ECWA,MAAMwF,GAAa,CAAC,CAClB,MAAAC,EACA,YAAAC,EACA,KAAA7B,EAAO,GACP,KAAA8B,EAAO,UACP,OAAAC,EAAS,KACT,SAAAC,EAAW,CAAC,EACZ,MAAAC,EAAQ,gCACR,SAAAC,EAAW,KACX,OAAAC,EAAS,YACT,YAAAC,EAAc,GACd,WAAAC,EAAa,GACb,cAAAC,EAAgB,KAChB,QAAAC,EAAU,GACV,aAAAC,EAAe,IACjB,IAAM,CACE,KAAA,CAAE,KAAA3I,CAAK,EAAIY,EAAe,EAC1B8C,EAAkB1D,EAAK,UAAY,KAGnC4I,EAAU,uBACVC,EAAe,YAsBfC,GAnBuB,IAAM,CACjC,GAAIL,EACK,OAAAA,EAGT,MAAMM,EAAqB,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,EAClDC,EAAO,CAAC,EAEK,OAAAD,EAAA,QAASE,GAAS,CAC/B9C,EACF6C,EAAKC,CAAI,EAAI,GAAGL,CAAO,IAAIK,CAAI,IAAI9C,CAAI,GAEvC6C,EAAKC,CAAI,EAAI,GAAGL,CAAO,IAAIK,CAAI,EACjC,CACD,EAEMD,CACT,GAE0C,EACpCE,EACJP,GACAG,EAAapF,CAAe,GAC5B,GAAGkF,CAAO,IAAIlF,CAAe,GAGzByF,EAA0B,IAAM,CACpC,MAAMC,EAAY,CAChB,GAAI,QACJ,GAAI,QACJ,GAAI,QACJ,GAAI,QACJ,GAAI,OACN,EAEMC,EAAc,CAClB,GAAI,UACJ,GAAI,WACJ,GAAI,UACJ,GAAI,SACJ,GAAI,SACN,EAEO,MAAA,CACL,OAAQD,EAAU1F,CAAe,GAAK,QACtC,SAAU2F,EAAY3F,CAAe,GAAK,SAC5C,CACF,EAEM,CAAE,OAAA4F,EAAQ,SAAAhD,CAAS,EAAI6C,EAAwB,EAG/CI,EAAiBxB,EACnB,GAAGA,CAAK,MAAMc,CAAY,GAC1B,GAAGA,CAAY,gDAGbW,EACJnB,GACA,GAAGQ,CAAY,MACbd,GAAS,4CACX,GAGI0B,EAAgBf,EAClB,oBACA,+EAEJ,cACGgB,EAEC,CAAA,SAAA,CAAA5H,EAAAA,IAAC,SAAO,SAAeyH,CAAA,CAAA,EACtBzH,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAASkG,EAAa,EAC9ClG,EAAA,IAAA,OAAA,CAAK,IAAI,YAAY,KAAMoH,EAAY,EACvCf,EAAS,OAAS,GAChBrG,EAAA,IAAA,OAAA,CAAK,KAAK,WAAW,QAASqG,EAAS,KAAK,IAAI,CAAG,CAAA,EAErDrG,EAAA,IAAA,OAAA,CAAK,KAAK,WAAW,QAASwE,EAAU,EACxCxE,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,QAAS4B,EAAiB,EAG5D,OAAO,QAAQoF,CAAY,EAAE,IAAI,CAAC,CAACG,EAAM5D,CAAG,IAC1CvD,EAAA,IAAA,OAAA,CAAgB,IAAI,YAAY,SAAUmH,EAAM,KAAM5D,CAAA,EAA5C4D,CAAiD,CAC7D,EAGDnH,EAAA,IAAC,OAAA,CACC,IAAI,YACJ,SAAS,YACT,KAAMgH,EAAa,IAAMI,CAAA,CAC3B,EAGCpH,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAASyH,EAAgB,EAClDzH,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAASkG,EAAa,EACrDlG,EAAA,IAAA,OAAA,CAAK,SAAS,UAAU,QAASmG,EAAM,EACvCnG,EAAA,IAAA,OAAA,CAAK,SAAS,SAAS,QAASoH,EAAY,EAC5CpH,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAASsG,EAAO,EACzCtG,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAAS0H,EAAiB,EACvD1H,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAAQ,OAAO,EAC9CA,EAAA,IAAA,OAAA,CAAK,SAAS,kBAAkB,QAAQ,MAAM,EAC9CA,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAAS+G,EAAc,EACpD/G,EAAA,IAAA,OAAA,CAAK,SAAS,YAAY,QAASwH,EAAQ,EAG3CxH,EAAA,IAAA,OAAA,CAAK,SAAS,YAAY,QAAQ,uBAAuB,EAGzD,OAAO,KAAKgH,CAAY,EACtB,OAAQG,GAASA,IAASvF,CAAe,EACzC,IAAKuF,GAAS,CACb,MAAMU,EAAY,CAChB,GAAI,QACJ,GAAI,QACJ,GAAI,QACJ,GAAI,QACJ,GAAI,SACJV,CAAI,EAEJ,OAAAnH,EAAA,IAAC,OAAA,CAEC,SAAS,sBACT,QAAS6H,CAAA,EAFJV,CAGP,CAAA,CAEH,EAGFhB,IAAS,WAAaM,GACrBzG,MAAC,QAAK,SAAS,yBAAyB,QAASyG,EAAa,EAE/DN,IAAS,WAAaO,GACrB1G,MAAC,QAAK,SAAS,wBAAwB,QAAS0G,EAAY,EAE7DP,IAAS,WAAaK,GACrBxG,MAAC,QAAK,SAAS,iBAAiB,QAASwG,EAAQ,QAIlD,OAAK,CAAA,KAAK,SAAS,QAASA,GAAUO,EAAc,EACpDN,GAAgBzG,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAASyG,EAAa,EAC/D,CAACA,GACAzG,EAAAA,IAAC,OAAK,CAAA,KAAK,eAAe,QAAa,IAAA,OAAO,YAAe,CAAA,CAAA,EAI9DA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,sBAAsB,EACvDA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,eAAe,EAChDA,EAAA,IAAA,OAAA,CAAK,KAAK,kBAAkB,QAAQ,eAAe,EACnDA,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAASyH,EAAgB,EACnDzH,EAAA,IAAA,OAAA,CAAK,KAAK,sBAAsB,QAASkG,EAAa,EACtDlG,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAASsG,EAAO,EAC1CtG,EAAA,IAAA,OAAA,CAAK,KAAK,oBAAoB,QAAS0H,EAAiB,EAGxD1H,EAAA,IAAA,OAAA,CAAK,KAAK,SAAS,QAAS2H,EAAe,EAC3C3H,EAAA,IAAA,OAAA,CAAK,KAAK,YAAY,QAAS2H,EAAe,EAC9C3H,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,QAAQ,2BAA2B,EAGlEA,EAAA,IAAC,OAAA,CACC,KAAK,WACL,QAAQ,0DAAA,CACV,EACCA,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAAQ,UAAU,EAC1CA,EAAA,IAAA,OAAA,CAAK,IAAI,aAAa,KAAK,+BAA+B,EAC3DA,EAAA,IAAC,OAAA,CACC,IAAI,aACJ,KAAK,4BACL,YAAY,WAAA,CACd,EACCA,EAAA,IAAA,OAAA,CAAK,IAAI,eAAe,KAAK,6BAA6B,EAG1DA,EAAA,IAAA,OAAA,CAAK,KAAK,+BAA+B,QAAQ,MAAM,EACxDA,EAAA,IAAC,OAAA,CACC,KAAK,wCACL,QAAQ,mBAAA,CACV,EACCA,EAAA,IAAA,OAAA,CAAK,KAAK,6BAA6B,QAAS+G,EAAc,EAC9D/G,EAAA,IAAA,OAAA,CAAK,KAAK,mBAAmB,QAAS+G,EAAc,EACpD/G,EAAA,IAAA,OAAA,CAAK,KAAK,0BAA0B,QAAQ,UAAU,EAGtDA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,QAAQ,EACzCA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,WAAW,EAG5CoG,GAAU,MAAM,QAAQA,CAAM,EAE7BA,EAAO,IAAI,CAAC0B,EAAYC,UACrB,SAAmB,CAAA,KAAK,sBACtB,SAAA,KAAK,UAAU,CACd,GAAGD,EACH,WAAY,qBACZ,WAAYlG,EACZ,IAAKwF,CAAA,CACN,CANU,EAAAW,CAOb,CACD,EACC3B,EAEDpG,EAAA,IAAA,SAAA,CAAO,KAAK,sBACV,cAAK,UAAU,CACd,GAAGoG,EACH,WAAY,qBACZ,WAAYxE,EACZ,IAAKwF,CAAA,CACN,CACH,CAAA,EACE,IAAA,EACN,CAEJ,EAEApB,GAAW,UAAY,CACrB,MAAOxF,EAAU,OAAO,WACxB,YAAaA,EAAU,OAAO,WAC9B,KAAMA,EAAU,OAChB,KAAMA,EAAU,OAChB,OAAQA,EAAU,UAAU,CAC1BA,EAAU,OACVA,EAAU,QAAQA,EAAU,MAAM,CAAA,CACnC,EACD,SAAUA,EAAU,QAAQA,EAAU,MAAM,EAC5C,MAAOA,EAAU,OACjB,SAAUA,EAAU,OACpB,OAAQA,EAAU,OAClB,YAAaA,EAAU,OACvB,WAAYA,EAAU,OACtB,cAAeA,EAAU,OACzB,QAASA,EAAU,KACnB,aAAcA,EAAU,MAC1B,EChSA,MAAMwH,WAAsBrC,EAAM,SAAU,CAC1C,YAAY3D,EAAO,CACjB,MAAMA,CAAK,EACX,KAAK,MAAQ,CAAE,SAAU,GAAO,MAAO,IAAK,CAAA,CAG9C,OAAO,yBAAyB+B,EAAO,CAE9B,MAAA,CAAE,SAAU,GAAM,MAAAA,CAAM,CAAA,CAGjC,kBAAkBA,EAAOkE,EAAW,CAE1B,QAAA,MAAM,4BAA6BlE,EAAOkE,CAAS,CAAA,CAG7D,QAAS,CACH,OAAA,KAAK,MAAM,SAGXnI,EAAA,KAAC,MAAA,CACC,MAAO,CACL,QAAS,OACT,UAAW,SACX,MAAO,OACP,gBAAiB,UACjB,UAAW,QACX,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,cAAe,QACjB,EAEA,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAoB,sBAAA,CAAA,EACxBA,EAAAA,IAAC,KAAE,SAAyB,2BAAA,CAAA,EAC5BA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,OAAO,SAAS,OAAO,EACtC,MAAO,CACL,QAAS,YACT,gBAAiB,UACjB,MAAO,QACP,OAAQ,OACR,aAAc,MACd,OAAQ,UACR,UAAW,MACb,EACD,SAAA,aAAA,CAAA,CAED,CAAA,CACF,EAIG,KAAK,MAAM,QAAA,CAEtB,CAEAgI,GAAc,UAAY,CACxB,SAAUxH,EAAU,KAAK,UAC3B,EC9DO,MAAM0H,GAAiB,CAC5B,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,iBACP,MAAO,wBACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,YACP,MAAO,2BACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,MAAO,kBACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,iBACP,MAAO,wBACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,YACP,MAAO,2BACR,EACD,CACE,GAAI,EACJ,KAAM,+BACN,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,MAAO,kBACR,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,EACJ,UAAW,0CACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,UACd,EACD,CACE,GAAI,EACJ,UAAW,gCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,iBACP,YAAa,eACd,EACD,CACE,GAAI,EACJ,UAAW,uCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,kBACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,uBACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,gCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,gBACP,YAAa,UACd,EACD,CACE,GAAI,GACJ,UAAW,2BACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,eACP,YACE,wHACF,KAAM,6BACN,WAAY,CAAC,aAAa,CAC3B,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,iBACP,YACE,mIACF,KAAM,6BACN,WAAY,CAAC,UAAU,CACxB,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,kBACP,YACE,sIACF,KAAM,6BACN,WAAY,CAAC,SAAU,aAAa,CACrC,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,YACP,YACE,wIACF,KAAM,6BACN,WAAY,CAAC,WAAY,QAAQ,CAClC,EACD,CACE,GAAI,GACJ,SAAU,2CACV,MAAO,eACP,YACE,+IACF,KAAM,6BACN,WAAY,CAAC,SAAU,aAAa,CACrC,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,eACP,YACE,6HACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,iBACP,YACE,oIACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,kBACP,YACE,4HACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,iBACP,YACE,6HACH,EACD,CACE,GAAI,GACJ,OAAQ,gDACR,SAAU,IACV,UAAW,IACX,MAAO,YACP,YACE,+HACH,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,SAAU,wDACV,MAAO,4DACP,OAAQ,MACR,YAAa,uBACd,EACD,CACE,GAAI,GACJ,SAAU,wDACV,MACE,uEACF,OAAQ,KACR,YAAa,gCACd,EACD,CACE,GAAI,GACJ,SAAU,wDACV,MAAO,iEACP,OAAQ,OACR,YAAa,iCACd,CACH,EAEaC,EAAc,CACzB,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,eACP,KAAM,WACN,WAAY,CAAC,aAAa,CAC3B,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,iBACP,KAAM,gBACN,WAAY,CAAC,WAAY,QAAQ,CAClC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,kBACP,KAAM,gBACN,WAAY,CAAC,UAAU,CACxB,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,YACP,KAAM,gBACN,WAAY,CAAC,SAAU,aAAa,CACrC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,eACP,KAAM,gBACN,WAAY,CAAC,QAAQ,CACtB,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,gBACP,KAAM,WACN,WAAY,CAAC,SAAU,UAAU,CAClC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,iBACP,KAAM,gBACN,WAAY,CAAC,WAAY,QAAQ,CAClC,EACD,CACE,GAAI,GACJ,SAAU,8CACV,MAAO,eACP,KAAM,WACN,WAAY,CAAC,aAAa,CAC3B,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,WAAY,CAAC,aAAa,EAC1B,OAAQ,oDACR,MAAO,eACP,YAAa,WACb,SAAU,GACV,aAAc,yDACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,WAAY,QAAQ,EACjC,OAAQ,oDACR,MAAO,iBACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,UAAU,EACvB,OAAQ,oDACR,MAAO,kBACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,aAAa,EACpC,OAAQ,oDACR,MAAO,YACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,QAAQ,EACrB,OAAQ,oDACR,MAAO,eACP,YAAa,gBACb,SAAU,GACV,aAAc,6BACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,UAAU,EACjC,OAAQ,oDACR,MAAO,gBACP,YAAa,WACb,SAAU,GACV,aAAc,yDACf,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,WAAY,CAAC,aAAa,EAC1B,OAAQ,uDACR,MAAO,eACP,YAAa,WACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,WAAY,QAAQ,EACjC,OAAQ,uDACR,MAAO,iBACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,UAAU,EACvB,OAAQ,uDACR,MAAO,kBACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,aAAa,EACpC,OAAQ,uDACR,MAAO,YACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,QAAQ,EACrB,OAAQ,uDACR,MAAO,eACP,YAAa,gBACb,aAAc,IACf,EACD,CACE,GAAI,GACJ,WAAY,CAAC,SAAU,UAAU,EACjC,OAAQ,uDACR,MAAO,gBACP,YAAa,WACb,aAAc,IACf,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,eACP,WAAY,mBACZ,MAAO,aACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,qBACP,WAAY,mBACZ,MAAO,UACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,kBACP,WAAY,gCACZ,MAAO,YACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,oBACP,WAAY,mBACZ,MAAO,UACR,EACD,CACE,GAAI,GACJ,SAAU,6CACV,MAAO,eACP,WAAY,sBACZ,MAAO,aACR,CACH,EAEaC,GAAc,CACzB,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,aAAa,EACjC,OAAQ,oDACR,OAAQ,mBACR,MAAO,eACP,YAAa,WACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,QAAS,UAAW,MAAO,WAAY,QAAQ,EAC5D,OAAQ,oDACR,OAAQ,mBACR,MAAO,iBACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,UAAU,EAC9B,OAAQ,oDACR,OAAQ,mBACR,MAAO,kBACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,SAAU,aAAa,EAC3C,OAAQ,oDACR,OAAQ,mBACR,MAAO,YACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,QAAQ,EAC5B,OAAQ,oDACR,OAAQ,mBACR,MAAO,eACP,YAAa,gBACb,WAAY,EACb,EACD,CACE,GAAI,GACJ,UAAW,YACX,WAAY,CAAC,MAAO,SAAU,UAAU,EACxC,OAAQ,oDACR,OAAQ,mBACR,MAAO,gBACP,YAAa,WACb,WAAY,EACb,CACH,EACaC,GAAe,CAC1B,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,iBACP,YAAa,wBACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,YACP,YAAa,2BACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,YAAa,kBACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,kBACP,YAAa,wBACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,eACP,YAAa,2BACd,EACD,CACE,GAAI,GACJ,OAAQ,6CACR,OAAQ,oBACR,MAAO,gBACP,YAAa,kBACd,CACH,EAEaC,GAAe,CAC1B,CACE,GAAI,GACJ,MAAO,4DACP,SAAU,wDACV,OAAQ,MACR,YAAa,uBACd,EACD,CACE,GAAI,GACJ,MACE,uEACF,SAAU,wDACV,OAAQ,KACR,YAAa,gCACd,EACD,CACE,GAAI,GACJ,MAAO,iEACP,SAAU,wDACV,OAAQ,OACR,YAAa,iCACd,EACD,CACE,GAAI,GACJ,MACE,uEACF,SAAU,wDACV,OAAQ,KACR,YAAa,gCACd,EACD,CACE,GAAI,GACJ,MAAO,iEACP,SAAU,wDACV,OAAQ,OACR,YAAa,iCACd,EACD,CACE,GAAI,GACJ,MAAO,4DACP,SAAU,wDACV,OAAQ,MACR,YAAa,uBACd,CACH,EAEaC,GAAe,CAC1B,CACE,GAAI,GACJ,UAAW,4BACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,UACd,EACD,CACE,GAAI,GACJ,UAAW,gCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,iBACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,0BACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,kBACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,uBACX,KAAM,+BACN,cAAe,gBACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,gCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,gBACP,YAAa,UACd,EACD,CACE,GAAI,GACJ,UAAW,2BACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,YACP,YAAa,eACd,EACD,CACE,GAAI,GACJ,UAAW,mCACX,KAAM,sDACN,cAAe,+BACf,OAAQ,kDACR,OAAQ,mBACR,MAAO,KACP,MAAO,eACP,YAAa,eACd,CACH,EAEaC,GAAe,CAC1B,CACE,GAAI,GACJ,KAAM,WACN,IAAK,cACL,KAAM,8CACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,aACP,MAAO,UACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,kBACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,iBACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,WACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,eACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,qBACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,cACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,SACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,cACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,kBACL,KAAM,8CACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,WACP,MAAO,UACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,WACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,eACP,MAAO,eACR,EACD,CACE,GAAI,GACJ,KAAM,WACN,IAAK,qBACL,KAAM,+BACN,OAAQ,0CACR,OAAQ,mBACR,MAAO,cACP,MAAO,eACR,CACH,EAEaC,GAAgB,CAC3B,GAAGd,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,EACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,GACH,GAAGC,EACL,ECtyBwB,SAAAE,GAAc,CAAE,KAAAC,GAAQ,CAE5C,OAAAlJ,EAAAA,IAACmJ,EACC,CAAA,SAAArJ,EAAAA,KAAC8H,EACC,CAAA,SAAA,CAAC5H,EAAAA,IAAA,QAAA,CAAO,0BAAM,KAAM,CAAA,QACnB,OAAK,CAAA,KAAK,cAAc,QAASkJ,GAAA,YAAAA,EAAM,WAAa,CAAA,CAAA,CAAA,CACvD,CACF,CAAA,CAEJ,CAEAD,GAAc,UAAY,CACxB,KAAMzI,EAAU,MAAM,CACpB,MAAOA,EAAU,OACjB,YAAaA,EAAU,MACxB,CAAA,CACH,EClBA,SAAwB4I,GAAW,CACjC,UAAArH,EACA,YAAAsH,EAAc,EACd,WAAAC,EAAa,EACb,aAAAC,EAAe,IAAM,CAAA,CACvB,EAAG,CAEK,MAAAC,EAAoBC,GAAS,CAC7BA,GAAQ,GAAKA,GAAQH,GAAcG,IAASJ,GAC9CE,EAAaE,CAAI,CAErB,EAGA,GAAIH,GAAc,EACT,OAAA,KA4CT,MAAMI,GAxCiB,IAAM,CAC3B,MAAMC,EAAQ,CAAC,EAGf,GAAIL,GAAc,EAEhB,QAASM,EAAI,EAAGA,GAAKN,EAAYM,IAC/BD,EAAM,KAAKC,CAAC,UAIVP,GAAe,EAAG,CAEpB,QAASO,EAAI,EAAGA,GAAK,EAAGA,IACtBD,EAAM,KAAKC,CAAC,EAEdD,EAAM,KAAK,KAAK,EAChBA,EAAM,KAAKL,CAAU,CAAA,SACZD,GAAeC,EAAa,EAAG,CAExCK,EAAM,KAAK,CAAC,EACZA,EAAM,KAAK,KAAK,EAChB,QAASC,EAAIN,EAAa,EAAGM,GAAKN,EAAYM,IAC5CD,EAAM,KAAKC,CAAC,CACd,KACK,CAELD,EAAM,KAAK,CAAC,EACZA,EAAM,KAAK,KAAK,EAChB,QAASC,EAAIP,EAAc,EAAGO,GAAKP,EAAc,EAAGO,IAClDD,EAAM,KAAKC,CAAC,EAEdD,EAAM,KAAK,KAAK,EAChBA,EAAM,KAAKL,CAAU,CAAA,CAIlB,OAAAK,CACT,GAEmC,EAGjC,OAAA7J,EAAA,KAAC,MAAA,CACC,UAAWiC,GAAwB,oCAGnC,SAAA,CAAAjC,EAAA,KAAC,IAAA,CACC,QAAS,IAAM0J,EAAiBH,EAAc,CAAC,EAC/C,UAAWA,IAAgB,EAAI,WAAa,GAC5C,MAAO,CAAE,OAAQA,IAAgB,EAAI,cAAgB,SAAU,EAE/D,SAAA,CAACrJ,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAa,eAAA,CAAA,CAAA,CAAA,CACjD,EAGC0J,EAAY,IAAI,CAACD,EAAM1B,IAClB0B,IAAS,YAER,OAA+B,CAAA,UAAU,YAAY,SAA3C,OAAA,YAAY1B,CAAK,EAE5B,EAKF/H,EAAA,IAAC,IAAA,CAEC,QAAS,IAAMwJ,EAAiBC,CAAI,EACpC,UAAWJ,IAAgBI,EAAO,SAAW,GAC7C,MAAO,CAAE,OAAQ,SAAU,EAE1B,SAAAA,CAAA,EALIA,CAMP,CAEH,EAGD3J,EAAA,KAAC,IAAA,CACC,QAAS,IAAM0J,EAAiBH,EAAc,CAAC,EAC/C,UAAWA,IAAgBC,EAAa,WAAa,GACrD,MAAO,CACL,OAAQD,IAAgBC,EAAa,cAAgB,SACvD,EAEA,SAAA,CAACtJ,EAAAA,IAAA,IAAA,CAAE,UAAU,kBAAmB,CAAA,EAC/BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,CAC7C,CAAA,CACF,CAEJ,CAEAoJ,GAAW,UAAY,CACrB,UAAW5I,EAAU,OACrB,YAAaA,EAAU,OACvB,WAAYA,EAAU,OACtB,aAAcA,EAAU,IAC1B,EClHA,SAAwBqJ,IAAkB,CAEtC,OAAA/J,EAAA,KAAC,MAAI,CAAA,UAAU,qBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,6BACb,SAAAA,EAAA,IAAC,MAAG,UAAU,sBAAsB,4BAAgB,CACtD,CAAA,EAEAA,EAAA,IAAC,KAAA,CACC,UAAU,sDACV,GAAG,YAGH,gBAAC8J,EAEE,CAAA,SAAA,CAAAvB,EAAY,MAAM,EAAG,CAAC,EAAE,IAAI,CAACwB,EAAMhC,IAClC/H,EAAA,IAAC,KAAA,CAEC,UAAW,iBAAiB+J,EAAK,WAAW,KAAK,GAAG,CAAC,GAEpD,SAAAA,EAAK,OAAS,WACb/J,EAAA,IAACgK,EAAA,CACC,SAAUD,EAAK,SACf,UAAWA,EAAK,SAChB,MAAO,IACP,OAAQ,IAEP,SAAA,CAAC,CAAE,IAAAE,EAAK,KAAAC,CAAK,WACX,IAAE,CAAA,QAASA,EAAM,UAAU,+BAC1B,SAAA,CAACpK,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,CAAA,EAE5CA,EAAA,IAAC,MAAA,CACC,IAAK+J,EAAK,SACV,IAAAE,EACA,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACAnK,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAA+J,EAAK,MAAM,EACtC/J,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,EAIJF,EAAA,KAACoC,EAAA,CACC,GAAI,6BAA6B6H,EAAK,EAAE,GACxC,UAAU,gBAEV,SAAA,CAACjK,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,aAAc,CAAA,EAC7BA,EAAA,IAAC,MAAA,CACC,IAAK+J,EAAK,SACV,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACAjK,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAA+J,EAAK,MAAM,EACtC/J,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,EAhDG+H,CAAA,CAmDR,EAAG,GAAA,CACN,CAAA,CAAA,CAAA,CAEF,EAEF,CAEJ,CC9EA,MAAMoC,GAAiB,CAAC,CAAE,OAAAC,EAAQ,aAAAC,KAAmB,CACnD,KAAM,CAACC,EAAmBC,CAAoB,EAAItL,EAAAA,SAAS,CAAC,EACtD,CAACuL,EAAcC,CAAe,EAAIxL,EAAAA,SAAS,EAAK,EAEtD,GAAI,CAACmL,GAAUA,EAAO,SAAW,EACxB,OAAA,KAGH,MAAAM,EAAeN,EAAOE,CAAiB,EACvCK,EAAoBP,EAAO,OAAS,EAEpCQ,EAAY,IAAM,CACtBL,EAAsBhK,IAAUA,EAAO,GAAK6J,EAAO,MAAM,CAC3D,EAEMS,EAAY,IAAM,CACtBN,EAAsBhK,IAAUA,EAAO,EAAI6J,EAAO,QAAUA,EAAO,MAAM,CAC3E,EAEMU,EAAa/C,GAAU,CAC3BwC,EAAqBxC,CAAK,CAC5B,EAEMgD,EAAiB,IAAM,CAC3BN,EAAgB,EAAI,CACtB,EAEMO,EAAkB,IAAM,CAC5BP,EAAgB,EAAK,CACvB,EAEMQ,EAAeC,GAEZ,gDAAqCA,CAAQ,GAMlD,OAAApL,EAAA,KAAAC,WAAA,CAAA,SAAA,CAACD,EAAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,IAAKiL,EAAYP,EAAa,QAAQ,EACtC,IAAKA,EAAa,KAAOL,EACzB,UAAU,+BACV,MAAO,CAAE,UAAW,QAAS,UAAW,OAAQ,EAChD,QAASU,CAAA,CACX,EAGCJ,GAEG7K,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,UAAU,+EACV,QAAS6K,EACT,MAAO,CAAE,OAAQ,CAAE,EAEnB,SAAC7K,EAAAA,IAAA,IAAE,CAAA,UAAU,eAAgB,CAAA,CAAA,CAC/B,EACAA,EAAA,IAAC,SAAA,CACC,UAAU,6EACV,QAAS4K,EACT,MAAO,CAAE,OAAQ,CAAE,EAEnB,SAAC5K,EAAAA,IAAA,IAAE,CAAA,UAAU,gBAAiB,CAAA,CAAA,CAAA,CAChC,CACF,CAAA,CAAA,EAEJ,EAGC2K,GACE3K,EAAA,IAAA,MAAI,CAAA,UAAU,0BACb,SAAAA,MAAC,MAAI,CAAA,UAAU,yBACZ,SAAAoK,EAAO,IAAI,CAAC9D,EAAOyB,IAClB/H,EAAA,IAAC,MAAA,CAEC,UAAW,iCACT+H,IAAUuC,EAAoB,SAAW,EAC3C,GACA,QAAS,IAAMQ,EAAU/C,CAAK,EAE9B,SAAA/H,EAAA,IAAC,MAAA,CACC,IAAKiL,EAAY3E,EAAM,QAAQ,EAC/B,IAAKA,EAAM,KAAO,GAAG+D,CAAY,IAAItC,EAAQ,CAAC,GAC9C,UAAU,UACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,QACX,OAAQA,IAAUuC,EAAoB,oBAAsB,wBAC5D,QAASvC,IAAUuC,EAAoB,EAAI,EAAA,CAC7C,CAAA,CACF,EAjBKhE,EAAM,EAmBd,CAAA,CACH,CAAA,CACF,CAAA,CAAA,EAEJ,EAGCkE,GACCxK,EAAA,IAAC,MAAA,CACC,UAAU,+GACV,MAAO,CACL,gBAAiB,sBACjB,OAAQ,IACV,EACA,QAASgL,EAET,SAAAlL,EAAA,KAAC,OAAI,UAAU,oBAAoB,QAAUQ,GAAMA,EAAE,gBAAgB,EACnE,SAAA,CAAAN,EAAA,IAAC,MAAA,CACC,IAAKiL,EAAYP,EAAa,QAAQ,EACtC,IAAKA,EAAa,KAAOL,EACzB,UAAU,YACV,MAAO,CAAE,UAAW,OAAQ,SAAU,MAAO,CAAA,CAC/C,EAGArK,EAAA,IAAC,SAAA,CACC,UAAU,yDACV,QAASgL,EACT,MAAO,CAAE,OAAQ,GAAM,EACxB,SAAA,GAAA,CAED,EAGCL,GAEG7K,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAAC,SAAA,CACC,UAAU,gFACV,QAAS6K,EACT,MAAO,CAAE,OAAQ,GAAM,EAEvB,SAAC7K,EAAAA,IAAA,IAAE,CAAA,UAAU,eAAgB,CAAA,CAAA,CAC/B,EACAA,EAAA,IAAC,SAAA,CACC,UAAU,8EACV,QAAS4K,EACT,MAAO,CAAE,OAAQ,GAAM,EAEvB,SAAC5K,EAAAA,IAAA,IAAE,CAAA,UAAU,gBAAiB,CAAA,CAAA,CAAA,CAChC,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,EAGDA,EAAAA,IAAA,QAAM,CAAA,IAAG,GAAE,SAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAUV,CAAA,CAAA,EACJ,CAEJ,EClKA,SAAwBmL,GAAS,CAAE,SAAAC,EAAW,CAAA,GAAM,CAC5C,KAAA,CAAE,CAAE,EAAItM,EAAe,EAEvBuM,EAAcC,GACL,IAAI,KAAKA,CAAU,EACpB,mBAAmB,QAAS,CACtC,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,SAAA,CACT,EAIH,MAAI,CAACF,GAAYA,EAAS,SAAW,EAEjCpL,EAAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAA,MAAC,IAAE,CAAA,UAAU,aAAc,SAAA,EAAE,2BAA2B,CAAE,CAAA,EAC5D,EAKCA,EAAA,IAAAD,EAAA,SAAA,CAAA,SAAAqL,EAAS,IAAI,CAACG,EAAS3B,IACrB5J,EAAA,IAAA,KAAA,CAAW,UAAU,qBAUpB,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC,KAAE,KAAK,IAAK,SAAQuL,EAAA,MAAA,CAAO,CAC9B,CAAA,EACCF,EAAWE,EAAQ,SAAS,EAAG,IAC/BvL,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,IAAA,EAC7BF,EAAAA,KAAC,IAAE,CAAA,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAAE,IACxB,EAAE,qBAAqB,CAAA,CAChC,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,IAAG,CAAA,SAAAuL,EAAQ,OAAQ,CAAA,EACnBA,EAAQ,SACPA,EAAQ,QAAQ,OAAS,GACzBA,EAAQ,QAAQ,IAAKC,GAClB1L,OAAA,MAAA,CAAmB,UAAU,qBAC5B,SAAA,CAAAE,EAAA,IAAC,IAAE,CAAA,UAAU,cAAc,KAAK,IAC9B,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,IAAI,iCACJ,IAAI,GACJ,MAAO,IACP,OAAQ,GAAA,CAAA,EAEZ,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC,KAAE,KAAK,IAAK,SAAMwL,EAAA,MAAA,CAAO,CAC5B,CAAA,EACCH,EAAWG,EAAM,SAAS,EAAG,IAC7BxL,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,IAAA,EAC7BF,EAAAA,KAAC,IAAE,CAAA,KAAK,IACN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAAE,IACxB,EAAE,qBAAqB,CAAA,CAChC,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,IAAG,CAAA,SAAAwL,EAAM,OAAQ,CAAA,CAAA,CACpB,CAAA,CAAA,CAvBQ,EAAAA,EAAM,EAwBhB,CACD,CACL,CAAA,CAAA,CAAA,EApDO5B,CAqDT,CACD,EACH,CAEJ,CAEAuB,GAAS,UAAY,CACnB,SAAU3K,EAAU,QAClBA,EAAU,MAAM,CACd,GAAIA,EAAU,OAAO,WACrB,OAAQA,EAAU,OAAO,WACzB,QAASA,EAAU,OAAO,WAC1B,UAAWA,EAAU,OAAO,WAC5B,QAASA,EAAU,QACjBA,EAAU,MAAM,CACd,GAAIA,EAAU,OAAO,WACrB,OAAQA,EAAU,OAAO,WACzB,QAASA,EAAU,OAAO,WAC1B,UAAWA,EAAU,OAAO,UAC7B,CAAA,CAAA,CAEJ,CAAA,CAAA,CAEL,ECpGA,SAAwBiL,GAAK,CAAE,SAAAC,EAAU,mBAAAC,GAAsB,CACvD,KAAA,CAAE,EAAA9M,CAAE,EAAIC,EAAe,EACvB,CAACwF,EAAUsH,CAAW,EAAI3M,WAAS,CACvC,OAAQ,GACR,MAAO,GACP,QAAS,GACT,QAAS,EAAA,CACV,EACK,CAAC4M,EAAcC,CAAe,EAAI7M,EAAAA,SAAS,EAAK,EAChD,CAAC8M,EAASC,CAAU,EAAI/M,EAAA,SAAS,CAAE,KAAM,GAAI,KAAM,GAAI,EAEvDgN,EAAgB3L,GAAM,CAC1B,KAAM,CAAE,KAAA4L,EAAM,MAAA5N,CAAM,EAAIgC,EAAE,OACdsL,EAACrL,IAAU,CAAE,GAAGA,EAAM,CAAC2L,CAAI,EAAG5N,CAAA,EAAQ,CACpD,EAEM6N,EAAe,MAAO7L,GAAM,OAChCA,EAAE,eAAe,EACjBwL,EAAgB,EAAI,EACpBE,EAAW,CAAE,KAAM,GAAI,KAAM,GAAI,EAE7B,GAAA,CACF,MAAMI,EAAS,MAAMlI,EAAQ,cAAcwH,EAAUpH,CAAQ,EAEzD8H,EAAO,SAAS,IACPJ,EAAA,CACT,KAAM,UACN,KACEnN,EAAE,sBAAsB,GACxB,6EAAA,CACH,EACW+M,EAAA,CAAE,OAAQ,GAAI,MAAO,GAAI,QAAS,GAAI,QAAS,GAAI,EAC3DD,GACiBA,EAAA,GAGVK,EAAA,CACT,KAAM,QACN,OACEK,EAAAD,EAAO,OAAP,YAAAC,EAAa,UACbxN,EAAE,oBAAoB,GACtB,6CAAA,CACH,QAEIkF,EAAO,CACN,QAAA,MAAM,4BAA6BA,CAAK,EACrCiI,EAAA,CACT,KAAM,QACN,KACEnN,EAAE,oBAAoB,GACtB,6CAAA,CACH,CAAA,QACD,CACAiN,EAAgB,EAAK,CAAA,CAEzB,EAEA,OACGhM,EAAAA,KAAA,OAAA,CAAK,UAAU,OAAO,SAAUqM,EAC/B,SAAA,CAACrM,EAAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBAEb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,QAAQ,OAAQ,SAAA,CAAAjB,EAAE,oBAAoB,EAAE,IAAA,EAAE,EACjDmB,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,SACL,GAAG,OACH,UAAU,8BACV,YAAanB,EAAE,gCAAgC,EAC/C,UAAW,IACX,MAAOyF,EAAS,OAChB,SAAU2H,EACV,SAAQ,GACR,gBAAc,MAAA,CAAA,CAChB,EACF,EACAnM,EAAAA,KAAC,MAAI,CAAA,UAAU,WAEb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,QAAQ,QAAS,SAAA,CAAAjB,EAAE,qBAAqB,EAAE,IAAA,EAAE,EACnDmB,EAAA,IAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAanB,EAAE,iCAAiC,EAChD,UAAW,IACX,MAAOyF,EAAS,MAChB,SAAU2H,EACV,SAAQ,GACR,gBAAc,MAAA,CAAA,CAChB,CACF,CAAA,CAAA,EACF,EACAnM,EAAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAAAE,MAAC,QAAM,CAAA,QAAQ,UAAW,SAAAnB,EAAE,uBAAuB,EAAE,EACrDmB,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,UACL,GAAG,UACH,UAAU,8BACV,YAAanB,EAAE,mCAAmC,EAClD,UAAW,IACX,MAAOyF,EAAS,QAChB,SAAU2H,CAAA,CAAA,CACZ,EACF,EAEAnM,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAE,MAAC,QAAM,CAAA,QAAQ,UAAW,SAAAnB,EAAE,uBAAuB,EAAE,EACrDmB,EAAA,IAAC,WAAA,CACC,KAAK,UACL,GAAG,UACH,UAAU,8BACV,KAAM,EACN,YAAanB,EAAE,mCAAmC,EAClD,UAAW,IACX,MAAOyF,EAAS,QAChB,SAAU2H,EACV,SAAQ,EAAA,CAAA,CACV,EACF,EAECF,EAAQ,MACP/L,EAAA,IAAC,MAAA,CACC,UAAW,SACT+L,EAAQ,OAAS,UAAY,gBAAkB,cACjD,SAEC,SAAQA,EAAA,IAAA,CACX,EAIFjM,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAU,wDACV,oBAAkB,IAClB,SAAU+L,EAEV,SAAA,CAAC/L,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CACGjB,EADHgN,EACK,2BACA,sBAD0B,EACD,IAC/B7L,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACCF,EAAA,KAAA,OAAA,CAAK,UAAU,kCAAkC,cAAY,OAC3D,SAAA,CACGjB,EADHgN,EACK,2BACA,sBAD0B,EACD,IAC/B7L,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CACH,CAAA,CAAA,CAAA,CACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,wDAAwD,SAAA,CAAA,sEACD,IACnEE,EAAA,IAAA,IAAA,CAAE,KAAK,IAAI,SAAsB,qBAAA,EAAI,OAAK,IAC1CA,EAAA,IAAA,IAAA,CAAE,KAAK,IAAI,SAAc,iBAAA,EAAI,GAAA,CAChC,CAAA,CAAA,EACF,CAEJ,CAEAyL,GAAK,UAAY,CACf,SAAUjL,EAAU,OAAO,WAC3B,mBAAoBA,EAAU,IAChC,EC5KA,SAAwB8L,GAAQ,CAC9B,iBAAAC,EAAmB,iDACrB,EAAG,CACD,KAAM,CAAE,EAAG,KAAArO,CAAK,EAAIY,EAAe,EAC7B8C,EAAkB1D,EAAK,UAAY,KACnC,CAACsO,EAAYC,CAAa,EAAIxN,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACyN,EAAMC,CAAO,EAAI1N,EAAAA,SAAS,CAAA,CAAE,EAC7B,CAAC2N,EAAaC,CAAc,EAAI5N,EAAAA,SAAS,CAAA,CAAE,EAC3C,CAAC6N,EAAaC,CAAc,EAAI9N,EAAAA,SAAS,CAAA,CAAE,EAC3C,CAAC+N,EAAaC,CAAc,EAAIhO,EAAAA,SAAS,EAAE,EAEjDG,EAAAA,UAAU,IAAM,EACI,SAAY,OACxB,GAAA,CAEI,MAAA8N,EAAmB,MAAM7H,GAAc,cAAc,EACvD6H,EAAiB,SAAS,IAAMA,EAAiB,MACnDT,EACES,EAAiB,KAAK,MAAQA,EAAiB,KAAK,YAAc,CAAA,CACpE,EAII,MAAAC,EAAa,MAAM7H,GAAQ,QAAQ,EACrC6H,EAAW,SAAS,IAAMA,EAAW,MACvCR,EAAQQ,EAAW,KAAK,MAAQA,EAAW,KAAK,MAAQ,EAAE,EAItD,MAAAC,EAAgB,MAAM7H,GAAW,WAAW,EAC9C6H,EAAc,SAAS,IAAMA,EAAc,MAC7CP,EAAeO,EAAc,KAAK,SAAW,CAAA,CAAE,EAIjD,MAAMC,EAAc,MAAMnJ,EAAQ,aAAatC,EAAiB,EAAG,CAAC,EACpE,GAAIyL,EAAY,SAAS,IAAMA,EAAY,KAAM,CACzC,MAAAC,IACJjB,EAAAgB,EAAY,KAAK,OAAjB,YAAAhB,EAAuB,OAAQgB,EAAY,KAAK,MAAQ,CAAC,EAC3DN,EAAe,MAAM,QAAQO,CAAK,EAAIA,EAAQ,CAAA,CAAE,CAAA,QAE3CvJ,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,CAAA,CAEtD,GAEU,CAAA,EACT,CAACnC,CAAe,CAAC,EAEd,MAAA2L,EAAgBjN,GAAM,CAC1BA,EAAE,eAAe,EACb0M,EAAY,SAEd,OAAO,SAAS,KAAO,gBAAgB,mBAAmBA,CAAW,CAAC,GAE1E,EAGMQ,EAAiB,CAACC,EAAMC,IAAU,OAChC,MAAAC,GAActB,EAAAoB,EAAK,eAAL,YAAApB,EAAmB,KACpCxN,GAAMA,EAAE,WAAa+C,GAEjB,OAAA+L,GAAA,YAAAA,EAAcD,KAAU,EACjC,EAEA,OAGI5N,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,SACb,SAAAA,EAAA,IAAC,OAAK,CAAA,SAAUuN,EAAc,UAAU,OACtC,SAAAzN,EAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,UAAU,wBACV,KAAK,SACL,MAAM,eAEN,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,mBAAoB,CAAA,EAChCA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAY,cAAA,CAAA,CAAA,CAAA,CAChD,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAWuM,EACX,YAAa,EAAE,yBAAyB,GAAK,YAC7C,MAAOS,EACP,SAAW1M,GAAM2M,EAAe3M,EAAE,OAAO,KAAK,EAC9C,SAAQ,EAAA,CAAA,CACV,CACF,CAAA,CACF,CAAA,EACF,EAGAR,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,eAAgB,SAAA,EAAE,iBAAiB,EAAE,QAClD,MAAI,CAAA,UAAU,cACb,SAAAA,MAAC,MAAG,UAAU,wBACX,SAAWwM,EAAA,OAAS,EACnBA,EAAW,IAAKvH,wBACb,KACC,CAAA,SAAA,CAAAjF,EAAA,IAAC,IAAA,CACC,KAAK,IACL,MAAM,GACN,QAAUM,GAAM,CACdA,EAAE,eAAe,EACjB,OAAO,SAAS,KAAO,IAAIsB,CAAe,kBAAkBqD,EAAS,IAAI,EAC3E,EAEC,SAASA,EAAA,IAAA,CACZ,SACC,QAAM,CAAA,SAAA,CAAA,QAAIoH,EAAApH,EAAS,SAAT,YAAAoH,EAAiB,YAAa,EAAE,GAAA,CAAC,CAAA,CAXrC,CAAA,EAAApH,EAAS,EAYlB,EACD,EAEDjF,EAAAA,IAAC,MAAI,SAAE,EAAA,oBAAoB,CAAE,CAAA,CAEjC,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,eAAgB,SAAA,EAAE,WAAW,EAAE,EAC5CA,EAAA,IAAA,MAAA,CAAI,UAAU,cACb,eAAC,MAAI,CAAA,UAAU,OACZ,SAAA0M,EAAK,OAAS,EACbA,EAAK,IAAKxH,GACRlF,EAAA,IAAC,IAAA,CACC,KAAK,IAEL,QAAUM,GAAM,CACdA,EAAE,eAAe,EACjB,OAAO,SAAS,KAAO,IAAIsB,CAAe,aAAasD,EAAI,IAAI,EACjE,EAEC,SAAIA,EAAA,IAAA,EANAA,EAAI,EAAA,CAQZ,EAEAlF,EAAAA,IAAA,OAAA,CAAM,WAAE,cAAc,EAAE,EAE7B,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,eAAgB,SAAA,EAAE,mBAAmB,EAAE,QACpD,MAAI,CAAA,UAAU,cACb,SAACA,EAAA,IAAA,KAAA,CAAG,UAAU,yBACX,SAAA8M,EAAY,OAAS,EACpBA,EAAY,IAAI,CAACW,EAAM1F,WACpBjI,OAAAA,EAAA,KAAA,KAAA,CAA0B,UAAU,WACnC,SAAA,CAAAE,EAAAA,IAAC,KAAE,KAAM,IAAI4B,CAAe,gBAAgB6L,EAAK,IAAI,GACnD,SAAAzN,EAAA,IAAC,MAAA,CACC,IACEyN,EAAK,eACL,yCAEF,OAAQ,IACR,MAAO,CAAE,OAAQ,aAAc,EAC/B,IAAKD,EAAeC,EAAM,OAAO,EACjC,MAAO,IACP,UAAU,kBAAA,CAAA,EAEd,EACA3N,EAAAA,KAAC,MAAI,CAAA,UAAU,qBACb,SAAA,CAAAE,EAAA,IAAC,IAAA,CACC,KAAM,IAAI4B,CAAe,gBAAgB6L,EAAK,IAAI,GAClD,MAAM,GAEL,SAAAD,EAAeC,EAAM,OAAO,CAAA,CAC/B,SACC,OACE,CAAA,SAAA,CAAA,EAAE,gBAAgB,EAAG,MACrBpB,EAAAoB,EAAK,SAAL,YAAApB,EAAa,OAAQ,gBAAA,CACxB,CAAA,CAAA,CACF,CAAA,CAzBO,CAAA,EAAAoB,EAAK,IAAM1F,CA0BpB,EACD,EAEA/H,EAAA,IAAA,KAAA,CAAI,SAAE,EAAA,sBAAsB,CAAE,CAAA,CAEnC,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,eAAgB,SAAA,EAAE,cAAc,EAAE,QAC/C,MAAI,CAAA,UAAU,cACb,SAAAA,EAAAA,IAAC,MAAG,UAAU,wBACX,SAAY4M,EAAA,OAAS,EACpBA,EAAY,IAAI,CAACgB,EAAS7F,WACvB,KACC,CAAA,SAAA,CAAAjI,EAAA,KAAC,IAAE,CAAA,KAAK,IAAI,MAAM,GACf,SAAA,CAAQ8N,EAAA,UAAU,IAAEA,EAAQ,IAAA,EAC/B,SACC,QAAM,CAAA,SAAA,CAAA,MAAIA,EAAQ,MAAM,GAAA,CAAC,CAAA,CAJnB,CAAA,EAAA7F,CAKT,CACD,EAED/H,EAAAA,IAAC,MAAI,SAAE,EAAA,iBAAiB,CAAE,CAAA,CAE9B,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,CAEAsM,GAAQ,UAAY,CAClB,iBAAkB9L,EAAU,MAC9B,ECrNA,SAAwBqN,IAAM,CAC5B,KAAM,CAACC,EAASC,CAAU,EAAI9O,EAAAA,SAAS,EAAK,EACtC,CAAE,EAAAJ,CAAE,EAAIC,EAAe,EAC7B,OAEIgB,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,MAAC,IAAE,CAAA,KAAK,IAAI,UAAW,eAAe8N,EAAU,YAAc,EAAE,GAC9D,SAAChO,EAAA,KAAA,MAAA,CAAI,UAAU,+BAA+B,cAAY,OACxD,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,UACb,eAAC,IAAE,CAAA,UAAU,cAAc,CAC7B,CAAA,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,UACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,QAAS,IAAMiO,EAAYC,GAAQ,CAACA,CAAG,EAAG,UAAU,UACtD,SAAA,CAAAnP,EAAE,kBAAkB,EAAE,IAACmB,EAAAA,IAAC,IAAE,CAAA,UAAU,cAAe,CAAA,CAAA,EACtD,EACAF,EAAAA,KAAC,MAAI,CAAA,QAAS,IAAMiO,EAAYC,GAAQ,CAACA,CAAG,EAAG,UAAU,WACtD,SAAA,CAAAnP,EAAE,mBAAmB,EAAE,IAACmB,EAAAA,IAAC,IAAE,CAAA,UAAU,eAAgB,CAAA,CAAA,CACxD,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAA,EAAA,IAAC,SAAA,CACC,IAAI,sRACJ,MAAO,IACP,OAAQ,IACR,QAAQ,OACR,MAAO,CAAE,OAAQ,CAAE,EACnB,gBAAgB,GAChB,cAAY,QACZ,SAAU,CAAA,CAAA,CACZ,EACF,CAEJ,CCZA,MAAMiO,GAAM,CAAC,CACX,MAAAhI,EAAQ,yDACR,YAAAC,EAAc,wJACd,UAAAgI,EAAY,uBACZ,MAAA5H,EAAQ,gCACR,SAAAC,EAAW,+BACX,WAAA4H,EAAa,OACb,YAAAC,EAAc,MACd,KAAAjI,EAAO,UACP,OAAAC,EAAS,KACT,cAAAiI,EAAgB,eAChB,YAAA5H,EAAc,GACd,WAAAC,EAAa,GACb,OAAAF,EAAS,YACT,SAAAH,EAAW,CACT,uBACA,kBACA,kBACA,eACA,uBACA,oBACF,EACA,OAAAmB,EAAS,OACX,IAAM,CAEE,MAAAC,EAAiB,GAAGxB,CAAK,eAE/B,cACG2B,EAEC,CAAA,SAAA,CAAA5H,EAAAA,IAAC,SAAO,SAAeyH,CAAA,CAAA,EACtBzH,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAASkG,EAAa,EAC9ClG,EAAA,IAAA,OAAA,CAAK,IAAI,YAAY,KAAMkO,EAAW,EACvClO,MAAC,QAAK,KAAK,WAAW,QAASqG,EAAS,KAAK,IAAI,EAAG,EAGnDrG,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAASyH,EAAgB,EAClDzH,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAASkG,EAAa,EACrDlG,EAAA,IAAA,OAAA,CAAK,SAAS,UAAU,QAASmG,EAAM,EACvCnG,EAAA,IAAA,OAAA,CAAK,SAAS,SAAS,QAASkO,EAAW,EAC3ClO,EAAA,IAAA,OAAA,CAAK,SAAS,WAAW,QAASsG,EAAO,EACzCtG,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAASuG,EAAU,EAChDvG,EAAA,IAAA,OAAA,CAAK,SAAS,iBAAiB,QAASmO,EAAY,EACpDnO,EAAA,IAAA,OAAA,CAAK,SAAS,kBAAkB,QAASoO,EAAa,EACtDpO,EAAA,IAAA,OAAA,CAAK,SAAS,eAAe,QAAQ,YAAY,EACjDA,EAAA,IAAA,OAAA,CAAK,SAAS,YAAY,QAASwH,EAAQ,EAG3CxH,EAAA,IAAA,OAAA,CAAK,SAAS,YAAY,QAAQ,uBAAuB,EAGzDmG,IAAS,WAAaM,GACrBzG,MAAC,QAAK,SAAS,yBAAyB,QAASyG,EAAa,EAE/DN,IAAS,WAAaO,GACrB1G,MAAC,QAAK,SAAS,wBAAwB,QAAS0G,EAAY,EAE7DP,IAAS,WAAaK,GACrBxG,MAAC,QAAK,SAAS,iBAAiB,QAASwG,EAAQ,QAIlD,OAAK,CAAA,KAAK,SAAS,QAASA,GAAU,YAAa,EACnDC,GAAgBzG,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAASyG,EAAa,EAC/D,CAACA,GACAzG,EAAAA,IAAC,OAAK,CAAA,KAAK,eAAe,QAAa,IAAA,OAAO,YAAe,CAAA,CAAA,EAI9DA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAAQ,sBAAsB,EACvDA,EAAA,IAAA,OAAA,CAAK,KAAK,eAAe,QAASqO,EAAe,EACjDrO,EAAA,IAAA,OAAA,CAAK,KAAK,kBAAkB,QAASqO,EAAe,EACpDrO,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAASyH,EAAgB,EACnDzH,EAAA,IAAA,OAAA,CAAK,KAAK,sBAAsB,QAASkG,EAAa,EACtDlG,EAAA,IAAA,OAAA,CAAK,KAAK,gBAAgB,QAASsG,EAAO,EAC1CtG,EAAA,IAAA,OAAA,CAAK,KAAK,oBAAoB,QAASuG,EAAU,EAGjDvG,EAAA,IAAA,OAAA,CAAK,KAAK,SAAS,QAAQ,yCAAyC,EACpEA,EAAA,IAAA,OAAA,CAAK,KAAK,YAAY,QAAQ,gBAAgB,EAC9CA,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,QAAQ,2BAA2B,EACjEA,EAAA,IAAA,OAAA,CAAK,KAAK,WAAW,QAAQ,UAAU,EAGxCA,EAAA,IAAC,OAAA,CACC,KAAK,WACL,QAAQ,0DAAA,CACV,EACCA,EAAA,IAAA,OAAA,CAAK,KAAK,cAAc,QAAQ,UAAU,EAC1CA,EAAA,IAAA,OAAA,CAAK,KAAK,+BAA+B,QAAQ,MAAM,EACxDA,EAAA,IAAC,OAAA,CACC,KAAK,wCACL,QAAQ,mBAAA,CACV,EAGCoG,GAAU,MAAM,QAAQA,CAAM,EAE7BA,EAAO,IAAI,CAAC0B,EAAYC,IACrB/H,EAAAA,IAAA,SAAA,CAAmB,KAAK,sBACtB,SAAK,KAAA,UAAU8H,CAAU,CAAA,EADfC,CAEb,CACD,EACC3B,QAED,SAAO,CAAA,KAAK,sBAAuB,SAAK,KAAA,UAAUA,CAAM,CAAE,CAAA,EACzD,IAAA,EACN,CAEJ,EAEA6H,GAAI,UAAY,CACd,MAAOzN,EAAU,OACjB,YAAaA,EAAU,OACvB,UAAWA,EAAU,OACrB,MAAOA,EAAU,OACjB,SAAUA,EAAU,OACpB,WAAYA,EAAU,OACtB,YAAaA,EAAU,OACvB,KAAMA,EAAU,OAChB,OAAQA,EAAU,UAAU,CAC1BA,EAAU,OACVA,EAAU,QAAQA,EAAU,MAAM,CAAA,CACnC,EACD,cAAeA,EAAU,OACzB,YAAaA,EAAU,OACvB,WAAYA,EAAU,OACtB,OAAQA,EAAU,OAClB,SAAUA,EAAU,QAAQA,EAAU,MAAM,EAC5C,OAAQA,EAAU,MACpB,ECvJA,MAAM8N,GAAc,CAAC,CAAE,SAAAvN,EAAU,MAAAkF,KAAY,CAC3C,MAAMjF,EAAWC,EAAY,EACvBC,EAAWP,EAAY,EACvB,CAAC4N,EAAkBC,CAAmB,EAAIvP,EAAAA,SAAS,EAAK,EACxD,CAACwP,EAAmBC,CAAoB,EAAIzP,EAAAA,SAAS,EAAK,EAC1D,CAAC0P,EAAkBC,CAAmB,EAAI3P,EAAAA,SAAS,EAAK,EACxDqD,EAAcC,SAAO,IAAI,EAG/BnD,EAAAA,UAAU,IAAM,CACR,MAAAuD,EAAsBC,GAAU,CAChCN,EAAY,SAAW,CAACA,EAAY,QAAQ,SAASM,EAAM,MAAM,GACnEgM,EAAoB,EAAK,CAE7B,EAES,gBAAA,iBAAiB,YAAajM,CAAkB,EAClD,IAAM,CACF,SAAA,oBAAoB,YAAaA,CAAkB,CAC9D,CACF,EAAG,EAAE,EAEL,MAAMkM,EAAe,IAAM,CACzB,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnC7N,EAAS,QAAQ,EACjB4N,EAAoB,EAAK,CAC3B,EAEME,EAAqB,IAAM,CAC/BF,EAAoB,CAACD,CAAgB,CACvC,EAEMI,EAAYC,GAEd9N,EAAS,WAAa8N,GAAQ9N,EAAS,SAAS,WAAW8N,EAAO,GAAG,EAInEC,EAAO,KAAK,MAAM,aAAa,QAAQ,WAAW,GAAK,IAAI,EAE3DC,EAAY,CAChB,CACE,KAAM,mBACN,KAAM,sBACN,MAAO,YACP,MAAO,EACT,EACA,CACE,KAAM,eACN,KAAM,2BACN,MAAO,aACP,MAAO,EACT,EACA,CACE,KAAM,kBACN,KAAM,wBACN,MAAO,WACP,MAAO,EACT,EACA,CACE,KAAM,kBACN,KAAM,kBACN,MAAO,WACP,MAAO,EACT,EACA,CACE,KAAM,sBACN,KAAM,wBACN,MAAO,cACP,MAAO,EACT,EACA,CACE,KAAM,mBACN,KAAM,qBACN,MAAO,YACP,MAAO,EACT,EACA,CACE,KAAM,oBACN,KAAM,oBACN,MAAO,aACP,MAAO,EACT,EACA,CACE,KAAM,cACN,KAAM,iBACN,MAAO,OACP,MAAO,EACT,EACA,CACE,KAAM,kBACN,KAAM,wBACN,MAAO,WACP,MAAO,EAAA,CAEX,EAGE,OAAApP,EAAA,KAAC,MAAI,CAAA,UAAU,uBAEZ,SAAA,CACC2O,GAAAzO,EAAA,IAAC,MAAA,CACC,UAAU,uBACV,QAAS,IAAM0O,EAAqB,EAAK,CAAA,CAC1C,EAIH5O,EAAA,KAAC,QAAA,CACC,UAAW,iBAAiByO,EAAmB,YAAc,EAAE,IAC7DE,EAAoB,cAAgB,EACtC,GAGA,SAAA,CAACzO,EAAA,IAAA,MAAA,CAAI,UAAU,uBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,aACZ,SAACuO,EAKCvO,EAAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAA,IAAA,CAAE,EAJpCF,EAAAA,KAAC,OACC,CAAA,SAAA,CAACE,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,YAAA,EAAO,QAAA,CAAA,CACpD,CAIJ,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,oBACb,SAACF,EAAA,KAAA,KAAA,CAAG,UAAU,iBACX,SAAA,CAAUoP,EAAA,IAAKnF,GACd/J,EAAA,IAAC,KAAA,CAEC,UAAW,kBACT+O,EAAShF,EAAK,IAAI,EAAI,SAAW,EACnC,GAEA,SAAAjK,EAAA,KAAC,IAAA,CACC,KAAK,IACL,QAAUQ,GAAM,CACdA,EAAE,eAAe,EACjBU,EAAS+I,EAAK,IAAI,EAClB2E,EAAqB,EAAK,CAC5B,EACA,UAAU,iBACV,MAAO3E,EAAK,MAEZ,SAAA,CAAA/J,EAAA,IAAC,eAAA,CACC,KAAM+J,EAAK,KACX,UAAU,gBAAA,CACX,EACA/J,EAAA,IAAA,OAAA,CAAK,UAAU,iBAAkB,WAAK,KAAM,CAAA,CAAA,CAAA,CAAA,CAC/C,EApBK+J,EAAK,IAAA,CAsBb,EAGD/J,EAAAA,IAAC,KAAG,CAAA,UAAU,mBAAoB,CAAA,EAGlCA,EAAAA,IAAC,KAAG,CAAA,UAAU,iBACZ,SAAAF,EAAA,KAAC,IAAA,CACC,KAAK,IACL,OAAO,SACP,UAAU,iBACV,MAAM,YAEN,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,gBAAA,CACX,EACAA,EAAA,IAAA,OAAA,CAAK,UAAU,iBAAiB,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,CAE9C,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAA,EAAAA,IAAC,OAAI,UAAU,uBACb,gBAAC,MAAI,CAAA,UAAU,kBAAkB,IAAKsC,EACpC,SAAA,CAAAxC,EAAA,KAAC,MAAA,CACC,UAAU,4BACV,QAASgP,EACT,MAAM,YAEN,SAAA,CAAA9O,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,kBAAkB,CACvC,CAAA,EACC,CAACuO,GACCzO,EAAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,kBAAmB,SAAAiP,EAAK,MAAQ,QAAQ,EACtDjP,EAAA,IAAA,MAAA,CAAI,UAAU,mBAAoB,WAAK,KAAM,CAAA,CAAA,EAChD,EAEFA,EAAAA,IAAC,MAAI,CAAA,UAAU,4BACb,SAAAA,EAAA,IAAC,eAAA,CACC,KAAM,mBACJ2O,EAAmB,KAAO,MAC5B,OAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,EAGCA,GACC7O,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAU,sBACV,QAAS,IAAM,CACbkB,EAAS,kBAAkB,EAC3B4N,EAAoB,EAAK,CAC3B,EAEA,SAAA,CAAA5O,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,MAAA,CACX,EAAe,WAAA,CAAA,CAElB,EACAF,EAAA,KAAC,MAAA,CACC,UAAU,sBACV,QAAS,IAAM,CACN,OAAA,KAAK,IAAK,QAAQ,EACzB8O,EAAoB,EAAK,CAC3B,EAEA,SAAA,CAAA5O,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,WAAA,CAAA,CAElB,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,wBAAyB,CAAA,EACxCF,EAAA,KAAC,MAAA,CACC,UAAU,6BACV,QAAS+O,EAET,SAAA,CAAA7O,EAAA,IAAC,eAAA,CACC,KAAK,mBACL,UAAU,MAAA,CACX,EAAe,QAAA,CAAA,CAAA,CAElB,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,CAAA,CACF,EAGAF,EAAA,KAAC,OAAA,CACC,UAAW,sBACTyO,EAAmB,oBAAsB,EAC3C,GAGA,SAAA,CAACzO,EAAAA,KAAA,SAAA,CAAO,UAAU,eAChB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,UAAU,uBACV,QAAS,IAAMwO,EAAoB,CAACD,CAAgB,EAEpD,SAAAvO,EAAAA,IAAC,eAAa,CAAA,KAAK,2BAA4B,CAAA,CAAA,CACjD,EACAA,EAAA,IAAC,SAAA,CACC,UAAU,sBACV,QAAS,IAAM0O,EAAqB,CAACD,CAAiB,EAEtD,SAAAzO,EAAAA,IAAC,eAAa,CAAA,KAAK,2BAA4B,CAAA,CAAA,CACjD,EACCiG,GAASjG,EAAA,IAAC,KAAG,CAAA,UAAU,mBAAoB,SAAMiG,CAAA,CAAA,CAAA,EACpD,QACC,MAAI,CAAA,UAAU,qBACb,SAACnG,EAAA,KAAA,OAAA,CAAK,UAAU,gBAAgB,SAAA,CAAA,YACpBmP,EAAK,MAAQA,EAAK,KAAA,CAAA,CAC9B,CACF,CAAA,CAAA,EACF,EAGCjP,EAAAA,IAAA,MAAA,CAAI,UAAU,gBAAiB,SAAAe,CAAS,CAAA,CAAA,CAAA,CAAA,CAC3C,EACF,CAEJ,EAEAuN,GAAY,UAAY,CACtB,SAAU9N,EAAU,KAAK,WACzB,MAAOA,EAAU,MACnB,EC/RA,MAAM2O,GAAe,CAAC,CACpB,QAAAC,EACA,SAAAC,EACA,YAAAC,EAAc,kBAChB,IAAM,CAIJ,MAAMC,EAASC,EAAU,CACvB,WAAY,CACVC,GAAW,UAAU,CACnB,KAAM,GACN,UAAW,EAAA,CACZ,EACDC,GAAK,UAAU,CACb,eAAgB,CACd,MAAO,aAAA,CACT,CACD,EAEDC,GAAU,UAAU,CAClB,gBAAiB,aACjB,eAAgB,CACd,MAAO,YAAA,CAEV,CAAA,CACH,EACA,QAAAP,EACA,SAAU,CAAC,CAAE,OAAAG,KAAa,CAClB,MAAAK,EAAOL,EAAO,QAAQ,EAC5BF,EAASO,CAAI,CACf,EACA,YAAa,CACX,WAAY,CACV,MAAO,eAAA,CACT,CACF,CACD,EAaD,OAVAxQ,EAAAA,UAAU,IAAM,CACV,GAAAmQ,GAAUH,IAAY,OAAW,CAC7B,MAAAS,EAAiBN,EAAO,QAAQ,EAElCH,IAAYS,GAAkB,CAACN,EAAO,WACjCA,EAAA,SAAS,WAAWH,CAAO,CACpC,CACF,EACC,CAACG,EAAQH,CAAO,CAAC,EAEfG,EAWHzP,EAAA,KAAC,MAAI,CAAA,UAAU,iBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,WAAW,EAAE,IAAI,EACvD,UAAW,eAAeA,EAAO,SAAS,MAAM,EAAI,SAAW,EAAE,GACjE,MAAM,OAEN,SAAAvP,EAAAA,IAAC,UAAO,SAAC,GAAA,CAAA,CAAA,CACX,EACAA,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,aAAa,EAAE,IAAI,EACzD,UAAW,eACTA,EAAO,SAAS,QAAQ,EAAI,SAAW,EACzC,GACA,MAAM,SAEN,SAAAvP,EAAAA,IAAC,MAAG,SAAC,GAAA,CAAA,CAAA,CACP,EACAA,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,WAAW,EAAE,IAAI,EACvD,UAAW,eAAeA,EAAO,SAAS,MAAM,EAAI,SAAW,EAAE,GACjE,MAAM,cAEL,SAAA,KAAA,CAAA,CACH,EACF,EAEAzP,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IACPuP,EAAO,MAAA,EAAQ,MAAA,EAAQ,cAAc,CAAE,MAAO,CAAG,CAAA,EAAE,IAAI,EAEzD,UAAW,eACTA,EAAO,SAAS,UAAW,CAAE,MAAO,CAAG,CAAA,EAAI,SAAW,EACxD,GACA,MAAM,YACP,SAAA,IAAA,CAED,EACAvP,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IACPuP,EAAO,MAAA,EAAQ,MAAA,EAAQ,cAAc,CAAE,MAAO,CAAG,CAAA,EAAE,IAAI,EAEzD,UAAW,eACTA,EAAO,SAAS,UAAW,CAAE,MAAO,CAAG,CAAA,EAAI,SAAW,EACxD,GACA,MAAM,YACP,SAAA,IAAA,CAED,EACAvP,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IACPuP,EAAO,MAAA,EAAQ,MAAA,EAAQ,cAAc,CAAE,MAAO,CAAG,CAAA,EAAE,IAAI,EAEzD,UAAW,eACTA,EAAO,SAAS,UAAW,CAAE,MAAO,CAAG,CAAA,EAAI,SAAW,EACxD,GACA,MAAM,YACP,SAAA,IAAA,CAAA,CAED,EACF,EAEAzP,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,iBAAiB,EAAE,IAAI,EAC7D,UAAW,eACTA,EAAO,SAAS,YAAY,EAAI,SAAW,EAC7C,GACA,MAAM,cACP,SAAA,QAAA,CAED,EACAvP,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,kBAAkB,EAAE,IAAI,EAC9D,UAAW,eACTA,EAAO,SAAS,aAAa,EAAI,SAAW,EAC9C,GACA,MAAM,gBACP,SAAA,SAAA,CAED,EACAvP,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,gBAAgB,EAAE,IAAI,EAC5D,UAAW,eACTA,EAAO,SAAS,WAAW,EAAI,SAAW,EAC5C,GACA,MAAM,aAEL,SAAA,KAAA,CAAA,CACH,EACF,EAEAzP,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,iBAAiB,EAAE,IAAI,EAC7D,UAAW,eACTA,EAAO,SAAS,YAAY,EAAI,SAAW,EAC7C,GACA,MAAM,OAAA,CACP,EACDvP,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,kBAAkB,EAAE,IAAI,EAC9D,UAAU,cACV,MAAM,kBACP,SAAA,GAAA,CAAA,CAED,EACF,EAEAzP,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,KAAK,EAAE,IAAI,EACjD,SAAU,CAACA,EAAO,IAAA,EAAM,KAAK,EAC7B,UAAU,cACV,MAAM,OACP,SAAA,GAAA,CAED,EACAvP,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMuP,EAAO,MAAA,EAAQ,QAAQ,KAAK,EAAE,IAAI,EACjD,SAAU,CAACA,EAAO,IAAA,EAAM,KAAK,EAC7B,UAAU,cACV,MAAM,OACP,SAAA,GAAA,CAAA,CAED,CACF,CAAA,CAAA,EACF,EAGAvP,EAAA,IAAC8P,EAAA,CACC,OAAAP,EACA,UAAU,iBACV,YAAAD,CAAA,CAAA,CACF,EACF,EAhKGtP,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAACA,EAAA,IAAA,IAAA,CAAE,SAAiB,mBAAA,CAAA,CACtB,CAAA,EACF,CA8JN,ECzNM+P,GAAoB,CAAC,CAAE,QAAAzM,EAAS,MAAAhF,EAAO,SAAA+Q,EAAU,eAAAW,KAAqB,CAC1E,KAAM,CAAC5N,EAAQC,CAAS,EAAIpD,EAAAA,SAAS,EAAK,EACpCqD,EAAcC,SAAO,IAAI,EAG/BnD,EAAAA,UAAU,IAAM,CACR,MAAAuD,EAAsBC,GAAU,CAChCN,EAAY,SAAW,CAACA,EAAY,QAAQ,SAASM,EAAM,MAAM,GACnEP,EAAU,EAAK,CAEnB,EAES,gBAAA,iBAAiB,YAAaM,CAAkB,EAClD,IAAM,CACF,SAAA,oBAAoB,YAAaA,CAAkB,CAC9D,CACF,EAAG,EAAE,EAEL,MAAMsN,EAAiB3M,EAAQ,KAAM4M,GAAWA,EAAO,QAAU5R,CAAK,EAEhE6R,EAAqBC,GAAgB,CACzCf,EAASe,CAAW,EACpB/N,EAAU,EAAK,CACjB,EAGE,OAAArC,EAAA,IAAC,OAAI,UAAU,8DAEb,gBAAC,MAAI,CAAA,UAAU,oBAAoB,IAAKsC,EACtC,SAAA,CAAAxC,EAAA,KAAC,SAAA,CACC,UAAU,wEACV,QAAS,IAAMuC,EAAU,CAACD,CAAM,EAChC,gBAAeA,EAEf,SAAA,CAAApC,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,cAAA,CACX,QACA,OAAK,CAAA,UAAU,cACb,UAAAiQ,GAAA,YAAAA,EAAgB,QAAS,oBAC5B,EACAjQ,EAAA,IAAC,eAAA,CACC,KAAK,4BACL,UAAW,wBAAwBoC,EAAS,aAAe,EAAE,EAAA,CAAA,CAC9D,CAAA,CACH,EAECA,SACE,MAAI,CAAA,UAAU,kFACZ,SAAQkB,EAAA,IAAK4M,GACZlQ,EAAA,IAAC,SAAA,CAEC,UAAW,uCACTkQ,EAAO,QAAU5R,EACb,+BACA,WACN,GACA,QAAS,IAAM6R,EAAkBD,EAAO,KAAK,EAE5C,SAAOA,EAAA,KAAA,EARHA,EAAO,KAAA,CAUf,CACH,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAEJ,EAEAH,GAAkB,UAAY,CAC5B,QAASvP,EAAU,QACjBA,EAAU,MAAM,CACd,MAAOA,EAAU,OAAO,WACxB,MAAOA,EAAU,OAAO,UACzB,CAAA,CAAA,EACD,WACF,MAAOA,EAAU,OAAO,WACxB,SAAUA,EAAU,KAAK,WACzB,eAAgBA,EAAU,MAC5B,EChFA,MAAM2B,GAAmB,CAAC,CAAE,QAAAmB,EAAS,MAAAhF,EAAO,SAAA+Q,KAAe,CACzD,KAAM,CAACjN,EAAQC,CAAS,EAAIpD,EAAAA,SAAS,EAAK,EACpCqD,EAAcC,SAAO,IAAI,EAG/BnD,EAAAA,UAAU,IAAM,CACR,MAAAuD,EAAsBC,GAAU,CAChCN,EAAY,SAAW,CAACA,EAAY,QAAQ,SAASM,EAAM,MAAM,GACnEP,EAAU,EAAK,CAEnB,EAES,gBAAA,iBAAiB,YAAaM,CAAkB,EAClD,IAAM,CACF,SAAA,oBAAoB,YAAaA,CAAkB,CAC9D,CACF,EAAG,EAAE,EAEL,MAAMsN,EAAiB3M,EAAQ,KAAM4M,GAAWA,EAAO,QAAU5R,CAAK,EAEhE6R,EAAqBC,GAAgB,CACzCf,EAASe,CAAW,EACpB/N,EAAU,EAAK,CACjB,EAGE,OAAArC,EAAA,IAAC,OAAI,UAAU,uDAEb,gBAAC,MAAI,CAAA,UAAU,oBAAoB,IAAKsC,EACtC,SAAA,CAAAxC,EAAA,KAAC,SAAA,CACC,UAAU,4DACV,QAAS,IAAMuC,EAAU,CAACD,CAAM,EAChC,gBAAeA,EAEf,SAAA,CAAApC,MAAC,OAAK,CAAA,UAAU,OAAQ,UAAAiQ,GAAA,YAAAA,EAAgB,OAAQ,KAAK,EACpDjQ,EAAA,IAAA,OAAA,CAAM,UAAgBiQ,GAAA,YAAAA,EAAA,QAAS,kBAAkB,EAClDjQ,EAAA,IAAC,eAAA,CACC,KAAK,4BACL,UAAW,wBAAwBoC,EAAS,aAAe,EAAE,EAAA,CAAA,CAC9D,CAAA,CACH,EAECA,GACCpC,EAAA,IAAC,MAAA,CACC,UAAU,kFACV,MAAO,CAAE,MAAO,EAAG,SAAU,OAAQ,EAEpC,SAAAsD,EAAQ,IAAK4M,GACZpQ,EAAA,KAAC,SAAA,CAEC,UAAW,uEACToQ,EAAO,QAAU5R,EACb,+BACA,WACN,GACA,QAAS,IAAM6R,EAAkBD,EAAO,KAAK,EAE7C,SAAA,CAAAlQ,EAAA,IAAC,OAAK,CAAA,UAAU,OAAQ,SAAAkQ,EAAO,KAAK,EACpClQ,EAAAA,IAAC,OAAM,CAAA,SAAAkQ,EAAO,KAAM,CAAA,EACnBA,EAAO,QAAU5R,GAChB0B,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,SAAA,CAAA,CACX,CAAA,EAdEkQ,EAAO,KAiBf,CAAA,CAAA,CAAA,CACH,CAAA,CAEJ,CACF,CAAA,CAEJ,ECvEMG,GAAoB,CAAC,CAAE,KAAAvM,KAAW,iBACtC,GAAI,CAACA,EACH,OACG9D,EAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MACZ,SAAC,CAAA,EAAG,EAAG,CAAC,EAAE,IAAK4J,GACd5J,MAAC,MAAY,CAAA,UAAU,gBACrB,SAAAA,EAAA,IAAC,OAAI,UAAU,0BACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,mBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,wBAAyB,CAAA,EACxCA,EAAA,IAAC,MAAA,CACC,UAAU,yBACV,MAAO,CAAE,OAAQ,MAAO,CAAA,CACzB,EACDA,EAAAA,IAAC,MAAI,CAAA,UAAU,mBAAoB,CAAA,CAAA,CACrC,CAAA,CACF,CAAA,EACF,CAAA,EAZQ4J,CAaV,CACD,CACH,CAAA,EACF,EAIE,MAAA0G,EAAgBC,GAChBA,GAAO,KACDA,EAAM,KAAS,QAAQ,CAAC,EAAI,IAC3BA,GAAO,KACRA,EAAM,KAAM,QAAQ,CAAC,EAAI,KAE5BA,GAAA,YAAAA,EAAK,mBAAoB,IAG5BC,EAAmB,CAACC,EAASC,IAAa,CAC9C,GAAI,CAACA,GAAYA,IAAa,EAAU,OAAA,KAClC,MAAAC,GAAWF,EAAUC,GAAYA,EAAY,IAC5C,MAAA,CACL,MAAO,KAAK,IAAIC,CAAM,EAAE,QAAQ,CAAC,EACjC,WAAYA,GAAU,EACtB,cAAe,KAAK,IAAIA,CAAM,GAAK,CACrC,CACF,EAEMC,EAAa,CAAC,CAClB,MAAA3K,EACA,MAAA3H,EACA,cAAAuS,EACA,KAAAC,EACA,MAAAC,EAAQ,SAAA,IACJ,CACE,MAAAC,EAAaR,EAAiBlS,EAAOuS,CAAa,EAGtD,OAAA7Q,EAAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,yCACb,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,uCAAwC,SAAMiG,EAAA,EAC5DjG,EAAA,IAAC,eAAA,CACC,KAAA8Q,EACA,UAAW,QAAQC,CAAK,OAAA,CAAA,CACzB,EACH,EAEA/Q,EAAA,IAAC,MAAI,CAAA,UAAU,OACb,SAAAA,EAAAA,IAAC,KAAG,CAAA,UAAU,yBAA0B,SAAAsQ,EAAahS,CAAK,CAAE,CAAA,EAC9D,EAEC0S,GAAcA,EAAW,eACvBlR,EAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KACEgR,EAAW,WACP,sBACA,wBAEN,UAAW,QACTA,EAAW,WAAa,eAAiB,aAC3C,EAAA,CACD,EACDlR,EAAA,KAAC,OAAA,CACC,UAAW,mBACTkR,EAAW,WAAa,eAAiB,aAC3C,GAEC,SAAA,CAAWA,EAAA,MAAM,GAAA,CAAA,CACpB,EACChR,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAExC,oBAAA,CAAA,CAAA,EACF,GAGA,CAACgR,GAAc,CAACA,EAAW,gBAC1BhR,EAAAA,IAAA,MAAA,CAAI,UAAU,mBAAmB,SAAqB,uBAAA,CAAA,CAAA,CAE3D,CAAA,CACF,CAAA,EACF,CAEJ,EAEA,aACG,MAAI,CAAA,UAAU,qBACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAE,EAAA,IAAC4Q,EAAA,CACC,MAAM,aACN,QAAOvE,EAAAvI,EAAK,YAAL,YAAAuI,EAAgB,UAAW,EAClC,gBAAe4E,EAAAnN,EAAK,YAAL,YAAAmN,EAAgB,WAAY,EAC3C,KAAK,iBACL,MAAM,SAAA,CACR,EAEAjR,EAAA,IAAC4Q,EAAA,CACC,MAAM,WACN,QAAOM,EAAApN,EAAK,WAAL,YAAAoN,EAAe,UAAW,EACjC,gBAAeC,EAAArN,EAAK,WAAL,YAAAqN,EAAe,WAAY,EAC1C,KAAK,iCACL,MAAM,MAAA,CACR,EAEAnR,EAAA,IAAC4Q,EAAA,CACC,MAAM,aACN,QAAOQ,EAAAtN,EAAK,aAAL,YAAAsN,EAAiB,UAAW,EACnC,gBAAeC,EAAAvN,EAAK,aAAL,YAAAuN,EAAiB,WAAY,EAC5C,KAAK,mBACL,MAAM,SAAA,CAAA,CACR,CAAA,CACF,CACF,CAAA,CAEJ,EAEAhB,GAAkB,UAAY,CAC5B,KAAM7P,EAAU,MAAM,CACpB,eAAgBA,EAAU,OAC1B,cAAeA,EAAU,OACzB,cAAeA,EAAU,OACzB,gBAAiBA,EAAU,OAC3B,eAAgBA,EAAU,OAC1B,iBAAkBA,EAAU,MAC7B,CAAA,CACH,ECnIA8Q,GAAQ,SACNC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,EACF,EAEA,MAAMC,GAAiB,CAAC,CAAE,KAAAjO,KAAW,CAC7B,MAAAkO,EAAWzP,SAAO,IAAI,EAExB,GAAA,CAACuB,GAAQ,CAACA,EAAK,QAAUA,EAAK,OAAO,SAAW,EAClD,aACG,MAAI,CAAA,UAAU,gCACb,SAAChE,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAkB,qBAAA,EAClDA,EAAA,IAAC,IAAA,CACC,KAAK,+BACL,OAAO,SACP,IAAI,sBACJ,UAAU,0CACX,SAAA,aAAA,CAAA,CAED,EACF,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,mDACV,MAAO,CAAE,OAAQ,OAAQ,EAEzB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,mBAAA,CACX,EACAA,EAAA,IAAA,IAAA,CAAE,UAAU,OAAO,SAAyC,2CAAA,CAAA,CAAA,CAC/D,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,EAIJ,MAAMiS,EAAY,CAChB,OAAQnO,EAAK,OACb,SAAU,CACR,CACE,MAAO,aACP,KAAMA,EAAK,WAAa,CAAC,EACzB,YAAa,UACb,gBAAiB,0BACjB,YAAa,EACb,KAAM,GACN,QAAS,GACT,YAAa,EACb,iBAAkB,EAClB,qBAAsB,UACtB,iBAAkB,UAClB,iBAAkB,CACpB,EACA,CACE,MAAO,WACP,KAAMA,EAAK,UAAY,CAAC,EACxB,YAAa,UACb,gBAAiB,yBACjB,YAAa,EACb,KAAM,GACN,QAAS,GACT,YAAa,EACb,iBAAkB,EAClB,qBAAsB,UACtB,iBAAkB,UAClB,iBAAkB,CACpB,EACA,CACE,MAAO,aACP,KAAMA,EAAK,YAAc,CAAC,EAC1B,YAAa,UACb,gBAAiB,0BACjB,YAAa,EACb,KAAM,GACN,QAAS,GACT,YAAa,EACb,iBAAkB,EAClB,qBAAsB,UACtB,iBAAkB,UAClB,iBAAkB,CAAA,CACpB,CAEJ,EAEMR,EAAU,CACd,WAAY,GACZ,oBAAqB,GACrB,QAAS,CACP,OAAQ,CACN,SAAU,SACV,OAAQ,CACN,cAAe,GACf,QAAS,GACT,KAAM,CACJ,KAAM,EAAA,CACR,CAEJ,EACA,QAAS,CACP,KAAM,QACN,UAAW,GACX,gBAAiB,qBACjB,WAAY,UACZ,UAAW,UACX,YAAa,2BACb,YAAa,EACb,aAAc,EACd,QAAS,GACT,cAAe,GACf,UAAW,CACT,MAAO,SAAU4O,EAAS,CACjB,MAAA,GACLA,EAAQ,QAAQ,KAClB,KAAKA,EAAQ,OAAO,EAAE,eAAA,CAAgB,EAAA,CACxC,CACF,CAEJ,EACA,OAAQ,CACN,EAAG,CACD,KAAM,CACJ,QAAS,EACX,EACA,OAAQ,CACN,QAAS,EACX,EACA,MAAO,CACL,MAAO,UACP,KAAM,CACJ,KAAM,EAAA,CACR,CAEJ,EACA,EAAG,CACD,YAAa,GACb,KAAM,CACJ,MAAO,2BACP,WAAY,CAAC,EAAG,CAAC,CACnB,EACA,OAAQ,CACN,QAAS,EACX,EACA,MAAO,CACL,MAAO,UACP,KAAM,CACJ,KAAM,EACR,EACA,SAAU,SAAU5T,EAAO,CACzB,OAAIA,GAAS,KACHA,EAAQ,KAAS,QAAQ,CAAC,EAAI,IAC7BA,GAAS,KACVA,EAAQ,KAAM,QAAQ,CAAC,EAAI,IAE9BA,CAAA,CACT,CACF,CAEJ,EACA,YAAa,CACX,KAAM,UACN,KAAM,IACN,UAAW,EACb,EACA,SAAU,CACR,MAAO,CACL,YAAa,CAAA,CACf,CAEJ,EAEA,aACG,MAAI,CAAA,UAAU,gCACb,SAACwB,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAkB,qBAAA,EAClDA,EAAA,IAAC,IAAA,CACC,KAAK,+BACL,OAAO,SACP,IAAI,sBACJ,UAAU,0CACX,SAAA,aAAA,CAAA,CAED,EACF,EAECA,EAAA,IAAA,MAAA,CAAI,MAAO,CAAE,OAAQ,OAAQ,EAC5B,SAACA,EAAA,IAAAmS,GAAA,CAAK,IAAKH,EAAU,KAAMC,EAAW,QAAA3O,EAAkB,CAC1D,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EAEAyO,GAAe,UAAY,CACzB,KAAMvR,EAAU,MAAM,CACpB,OAAQA,EAAU,QAAQA,EAAU,MAAM,EAC1C,UAAWA,EAAU,QAAQA,EAAU,MAAM,EAC7C,SAAUA,EAAU,QAAQA,EAAU,MAAM,EAC5C,WAAYA,EAAU,QAAQA,EAAU,MAAM,CAAA,CAC/C,EACD,UAAWA,EAAU,MACvB,EChOA,MAAM4R,GAAe,CAAC,CAAE,KAAAtO,EAAM,MAAAmC,KAAY,CACxC,GAAI,CAACnC,GAAQ,CAACA,EAAK,YACjB,aACG,MAAI,CAAA,UAAU,gCACb,SAAChE,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAmB,SAAMiG,EAAA,EACvCjG,EAAA,IAAC,IAAA,CACC,KAAK,+BACL,OAAO,SACP,IAAI,sBACJ,UAAU,0CACX,SAAA,aAAA,CAAA,CAED,EACF,EACAA,EAAA,IAAC,MAAA,CACC,UAAU,mDACV,MAAO,CAAE,OAAQ,OAAQ,EAEzB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,mBAAA,CACX,EACAA,EAAA,IAAA,IAAA,CAAE,UAAU,aAAa,SAAyB,2BAAA,CAAA,CAAA,CACrD,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,EAIE,MAAAqS,EAAO,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EACvDC,EAAQ,MAAM,KAAK,CAAE,OAAQ,EAAG,EAAG,CAACC,EAAG3I,IAAM,CACjD,MAAM4I,EAAO5I,IAAM,EAAI,GAAKA,EAAI,GAAKA,EAAI,GAAKA,EACxC6I,EAAS7I,EAAI,GAAK,KAAO,KACxB,MAAA,GAAG4I,CAAI,IAAIC,CAAM,EAAA,CACzB,EAGKC,EAAW,KAAK,IAAI,GAAG5O,EAAK,YAAY,MAAM,EAG9C6O,EAAgBrU,GAAU,CAC1B,GAAAA,IAAU,EAAU,MAAA,GACxB,MAAM0S,EAAa1S,EAAQoU,EACvB,OAAA1B,GAAc,IAAa,EAC3BA,GAAc,GAAY,EAC1BA,GAAc,IAAa,EACxB,CACT,EAEM4B,EAAqBC,GAAc,CACvC,MAAMC,EAAS,CACb,EAAG,UACH,EAAG,UACH,EAAG,UACH,EAAG,UACH,EAAG,SACL,EACA,OAAOA,EAAOD,CAAS,GAAKC,EAAO,CAAC,CACtC,EAEA,aACG,MAAI,CAAA,UAAU,gCACb,SAAChT,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAmB,SAAMiG,EAAA,QACtC,IAAE,CAAA,KAAK,IAAI,UAAU,0CAA0C,SAEhE,aAAA,CAAA,CAAA,EACF,EAEAnG,EAAAA,KAAC,MAAI,CAAA,UAAU,oBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAAAE,EAAA,IAAC,MAAI,EAAA,EAAM,IACXF,EAAAA,KAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAK,QAAA,EACxCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAI,OAAA,EACvCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAK,QAAA,EACxCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAI,OAAA,EACvCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAK,OAAA,CAAA,CAAA,CAC3C,CAAA,CAAA,EACF,EAGCA,EAAA,IAAA,MAAA,CAAI,UAAU,yBACZ,SAAKqS,EAAA,IAAI,CAACU,EAAKC,IACdlT,EAAAA,KAAC,MAAc,CAAA,UAAU,cAEvB,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,oBAAqB,SAAI+S,EAAA,EAGvC/S,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACZ,SAAM,MAAA,KAAK,CAAE,OAAQ,EAAG,EAAG,CAACuS,EAAGU,IAAc,OAC5C,MAAM3U,IAAQ+N,EAAAvI,EAAK,YAAYkP,CAAQ,IAAzB,YAAA3G,EAA6B4G,KAAc,EACnDJ,EAAYF,EAAarU,CAAK,EAGlC,OAAA0B,EAAA,IAAC,MAAA,CAEC,UAAU,eACV,MAAO,CACL,gBAAiB4S,EAAkBC,CAAS,CAC9C,EACA,MAAO,GAAGE,CAAG,IAAIT,EAAMW,CAAS,CAAC,KAAK3U,CAAK,SAC3C,aAAegC,GAAM,CACjBA,EAAA,OAAO,MAAM,UAAY,aACzBA,EAAA,OAAO,MAAM,OAAS,IAC1B,EACA,aAAeA,GAAM,CACjBA,EAAA,OAAO,MAAM,UAAY,WACzBA,EAAA,OAAO,MAAM,OAAS,GAAA,CAC1B,EAbK2S,CAcP,CAAA,CAEH,CACH,CAAA,CAAA,GA7BQF,CA8BV,CACD,EACH,EAGAjT,EAAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,SAAI,OAAA,EACtCA,EAAA,IAAA,MAAA,CAAI,UAAU,eACZ,SAAC,CAAA,EAAG,EAAG,EAAG,EAAG,CAAC,EAAE,IAAK6S,GACpB7S,EAAA,IAAC,MAAA,CAEC,MAAO,CACL,MAAO,OACP,OAAQ,OACR,gBAAiB4S,EAAkBC,CAAS,EAC5C,aAAc,KAAA,CAChB,EANKA,CAQR,CAAA,EACH,EACC7S,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,SAAI,MAAA,CAAA,CAAA,CACzC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EAEAoS,GAAa,UAAY,CACvB,KAAM5R,EAAU,MAAM,CACpB,YAAaA,EAAU,QACrBA,EAAU,MAAM,CACd,KAAMA,EAAU,OAChB,IAAKA,EAAU,OACf,MAAOA,EAAU,MAClB,CAAA,CAAA,CACH,CACD,EACD,MAAOA,EAAU,MACnB,EC3JA,MAAM0S,GAAa,CAAC,CAAE,KAAApP,EAAM,QAAAqP,EAAS,UAAWC,KAAiB,CAC/D,KAAM,CAACC,EAAQC,CAAS,EAAIrU,EAAAA,SAAS,aAAa,EAC5C,CAACsU,EAAWC,CAAY,EAAIvU,EAAAA,SAAS,MAAM,EAE3CgM,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADS/H,EAAa,QAAQ,OAAQ,EAAE,CAC9B,wBAAwB+H,CAAQ,GAT3B,KAWlB,CAACuI,EAAgBC,CAAiB,EAAIzU,WAAS,CACnD,YAAa,GACb,MAAO,GACP,OAAQ,GACR,YAAa,GACb,WAAY,GACZ,WAAY,EAAA,CACb,EAEK0U,EAAgB,CACpB,CAAE,IAAK,cAAe,MAAO,eAAgB,KAAM,qBAAsB,EACzE,CAAE,IAAK,QAAS,MAAO,QAAS,KAAM,gBAAiB,EACvD,CAAE,IAAK,SAAU,MAAO,SAAU,KAAM,mBAAoB,EAC5D,CACE,IAAK,cACL,MAAO,eACP,KAAM,yBACR,EACA,CAAE,IAAK,aAAc,MAAO,aAAc,KAAM,kBAAmB,EACnE,CAAE,IAAK,aAAc,MAAO,aAAc,KAAM,mBAAoB,CACtE,EAEMC,EAAcC,GAAW,CACzBR,IAAWQ,EACAL,EAAAD,IAAc,MAAQ,OAAS,KAAK,GAEjDD,EAAUO,CAAM,EAChBL,EAAa,MAAM,EAEvB,EAEMM,EAAgBC,GAAc,CAClCL,EAAmBnT,IAAU,CAC3B,GAAGA,EACH,CAACwT,CAAS,EAAG,CAACxT,EAAKwT,CAAS,CAAA,EAC5B,CACJ,EAEM1I,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,MAAO,QACP,IAAK,UACL,KAAM,SAAA,CACP,EAGG0I,EAAqBC,GAClBA,EAAU,GAAGA,CAAO,IAAM,KAG7BC,EAAkBC,GAAY,CAClC,GAAI,CAACA,GAAWA,IAAY,EAAU,MAAA,KACtC,GAAIA,EAAU,GAAW,MAAA,GAAGA,CAAO,IACnC,MAAMF,EAAU,KAAK,MAAME,EAAU,EAAE,EACjCC,EAAmBD,EAAU,GAC5B,OAAAC,EAAmB,EACtB,GAAGH,CAAO,KAAKG,CAAgB,IAC/B,GAAGH,CAAO,GAChB,EAEMI,EAAa1O,EAAM,QAAQ,IAC3B,CAAC7B,GAAQ,CAAC,MAAM,QAAQA,CAAI,EAAU,CAAC,EAEpC,CAAC,GAAGA,CAAI,EAAE,KAAK,CAACwQ,EAAGC,IAAM,CAC1B,IAAAC,EAASF,EAAEjB,CAAM,EACjBoB,EAASF,EAAElB,CAAM,EAOrB,OALIA,IAAW,gBACJmB,EAAA,IAAI,KAAKA,CAAM,EACfC,EAAA,IAAI,KAAKA,CAAM,GAGtBlB,IAAc,MACTiB,EAASC,EAAS,EAAI,GAEtBD,EAASC,EAAS,EAAI,EAC/B,CACD,EACA,CAAC3Q,EAAMuP,EAAQE,CAAS,CAAC,EAE5B,OAAIJ,QAEC,MAAI,CAAA,UAAU,0BACb,SAACrT,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAe,kBAAA,QAC9C,MAAI,CAAA,UAAU,qCACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,8BAA8B,KAAK,SAChD,eAAC,OAAK,CAAA,UAAU,kBAAkB,SAAA,aAAU,EAC9C,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAKD,MAAI,CAAA,UAAU,0BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAQ,WAAA,QACvC,IAAE,CAAA,KAAK,IAAI,UAAU,0CAA0C,SAEhE,aAAA,CAAA,CAAA,EACF,EAGCA,MAAA,MAAA,CAAI,UAAU,WACb,eAAC,MAAI,CAAA,UAAU,WACb,SAAAA,MAAC,OAAI,UAAU,yBACZ,SAAc2T,EAAA,IAAKE,GAClB/T,EAAA,KAAC,SAAA,CAEC,UAAW,8CACT2T,EAAeI,EAAO,GAAG,EACrB,cACA,uBACN,GACA,QAAS,IAAMC,EAAaD,EAAO,GAAG,EAEtC,SAAA,CAAA7T,EAAA,IAAC,eAAA,CACC,KAAM6T,EAAO,KACb,UAAU,MAAA,CACX,EACD7T,EAAAA,IAAC,OAAM,CAAA,SAAA6T,EAAO,KAAM,CAAA,CAAA,CAAA,EAZfA,EAAO,GAAA,CAcf,CACH,CAAA,CACF,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,mBACb,SAAC/T,EAAA,KAAA,QAAA,CAAM,UAAU,iCACf,SAAA,CAAAE,MAAC,QAAM,CAAA,UAAU,cACf,SAAAF,EAAA,KAAC,KACC,CAAA,SAAA,CAAAE,MAAC,KAAG,CAAA,MAAM,MAAM,UAAU,kCAAkC,SAE5D,aAAA,EACCyT,EAAe,aACdzT,EAAA,IAAC,KAAA,CACC,MAAM,MACN,UAAU,iDACV,QAAS,IAAM4T,EAAW,aAAa,EAEvC,SAAA9T,EAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAACE,EAAAA,IAAA,eAAA,CAAa,KAAK,qBAAsB,CAAA,EACzCA,EAAAA,IAAC,QAAK,SAAY,cAAA,CAAA,EACjBqT,IAAW,eACVrT,EAAA,IAAC,eAAA,CACC,KACEuT,IAAc,MACV,sBACA,wBAEN,UAAU,cAAA,CAAA,CACX,CAEL,CAAA,CAAA,CACF,EAEDE,EAAe,OACdzT,EAAA,IAAC,KAAA,CACC,MAAM,MACN,UAAU,6DACV,QAAS,IAAM4T,EAAW,OAAO,EAEjC,SAAA9T,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAACE,EAAAA,IAAA,eAAA,CAAa,KAAK,gBAAiB,CAAA,EACpCA,EAAAA,IAAC,QAAK,SAAK,OAAA,CAAA,EACVqT,IAAW,SACVrT,EAAA,IAAC,eAAA,CACC,KACEuT,IAAc,MACV,sBACA,wBAEN,UAAU,cAAA,CAAA,CACX,CAEL,CAAA,CAAA,CACF,EAEDE,EAAe,QACdzT,EAAA,IAAC,KAAA,CACC,MAAM,MACN,UAAU,6DACV,QAAS,IAAM4T,EAAW,QAAQ,EAElC,SAAA9T,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAACE,EAAAA,IAAA,eAAA,CAAa,KAAK,mBAAoB,CAAA,EACvCA,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,EACXqT,IAAW,UACVrT,EAAA,IAAC,eAAA,CACC,KACEuT,IAAc,MACV,sBACA,wBAEN,UAAU,cAAA,CAAA,CACX,CAEL,CAAA,CAAA,CACF,EAEDE,EAAe,aACdzT,EAAA,IAAC,KAAA,CACC,MAAM,MACN,UAAU,6DACV,QAAS,IAAM4T,EAAW,aAAa,EAEvC,SAAA9T,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAACE,EAAAA,IAAA,eAAA,CAAa,KAAK,yBAA0B,CAAA,EAC7CA,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,EACTqT,IAAW,eACVrT,EAAA,IAAC,eAAA,CACC,KACEuT,IAAc,MACV,sBACA,wBAEN,UAAU,cAAA,CAAA,CACX,CAEL,CAAA,CAAA,CACF,EAEDE,EAAe,YACdzT,EAAA,IAAC,KAAA,CACC,MAAM,MACN,UAAU,6DACV,QAAS,IAAM4T,EAAW,YAAY,EAEtC,SAAA9T,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAACE,EAAAA,IAAA,eAAA,CAAa,KAAK,kBAAmB,CAAA,EACtCA,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,EACfqT,IAAW,cACVrT,EAAA,IAAC,eAAA,CACC,KACEuT,IAAc,MACV,sBACA,wBAEN,UAAU,cAAA,CAAA,CACX,CAEL,CAAA,CAAA,CACF,EAEDE,EAAe,YACdzT,EAAA,IAAC,KAAG,CAAA,MAAM,MAAM,UAAU,kCACxB,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAACE,EAAAA,IAAA,eAAA,CAAa,KAAK,mBAAoB,CAAA,EACvCA,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAWqU,EAAA,SAAW,EACrBrU,EAAAA,IAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,KAAG,CAAA,QAAQ,IAAI,UAAU,8BACxB,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,2BACL,UAAU,mBAAA,CACX,EAAe,iDAAA,EAElB,EACF,EAEAqU,EAAW,IAAK5G,8BACb,KACC,CAAA,SAAA,CAAAzN,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACZ,SAAA,CAAA2N,EAAK,eACJzN,EAAA,IAAC,MAAA,CACC,IAAKiL,EAAYwC,EAAK,aAAa,EACnC,IAAI,GACJ,UAAU,UACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OAAA,CACb,CACF,SAED,MACC,CAAA,SAAA,CAAAzN,EAAA,IAACkC,EAAA,CACC,GAAI,oBAAoBuL,EAAK,EAAE,GAC/B,UAAU,2CAET,SAAKA,EAAA,KAAA,CACR,EACA3N,EAAAA,KAAC,MAAI,CAAA,UAAU,mBAAmB,SAAA,CAAA,gBAClBuL,EAAWoC,EAAK,WAAW,CAAA,CAC3C,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACCgG,EAAe,aACbzT,MAAA,KAAA,CAAG,UAAU,mBACX,SAAAqL,EAAWoC,EAAK,WAAW,CAC9B,CAAA,EAEDgG,EAAe,OACdzT,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACX,WAAKqM,EAAAoB,EAAA,QAAA,YAAApB,EAAO,mBAAoB,GACnC,CAAA,EAEDoH,EAAe,QACdzT,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACX,WAAKiR,EAAAxD,EAAA,SAAA,YAAAwD,EAAQ,mBAAoB,GACpC,CAAA,EAEDwC,EAAe,aACbzT,EAAAA,IAAA,KAAA,CAAG,UAAU,yBACZ,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,QACb,SAAA,CAAAA,OAAC,MAAI,CAAA,SAAA,CAAA,QAAMkU,EAAkBvG,EAAK,WAAW,CAAA,EAAE,EAC/C3N,EAAAA,KAAC,MAAI,CAAA,UAAU,eAAe,SAAA,CAAA,QACtBoU,EAAezG,EAAK,WAAW,CAAA,CACvC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEDgG,EAAe,YACdzT,EAAAA,IAAC,KAAG,CAAA,UAAU,wBACX,WAAKkR,EAAAzD,EAAA,aAAA,YAAAyD,EAAY,mBAAoB,GACxC,CAAA,EAEDuC,EAAe,YACbzT,EAAA,IAAA,KAAA,CACC,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,yBACZ,UAAKmR,EAAA1D,EAAA,aAAA,YAAA0D,EAAY,IAAKlM,GACrBjF,EAAA,IAAC,OAAA,CAEC,UAAU,2BAET,SAASiF,EAAA,IAAA,EAHLA,EAAS,EAKjB,EACH,CAAA,CACF,CAAA,CAAA,GAtEKwI,EAAK,EAwEd,EACD,CAEL,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EAEAyF,GAAW,UAAY,CACrB,KAAM1S,EAAU,QACdA,EAAU,MAAM,CACd,GAAIA,EAAU,OACd,MAAOA,EAAU,OACjB,YAAaA,EAAU,OACvB,cAAeA,EAAU,OACzB,YAAaA,EAAU,OACvB,YAAaA,EAAU,OACvB,WAAYA,EAAU,QACpBA,EAAU,MAAM,CACd,GAAIA,EAAU,OACd,KAAMA,EAAU,MACjB,CAAA,CACH,EACA,MAAOA,EAAU,OACjB,OAAQA,EAAU,OAClB,WAAYA,EAAU,MACvB,CAAA,CACH,EACA,QAASA,EAAU,KACnB,UAAWA,EAAU,MACvB,EC5YA,SAAwBkU,GAAoB,CAAE,UAAA7P,EAAW,iBAAA8P,GAAoB,CAC3E,KAAM,CAACC,EAAgBC,CAAiB,EAAI5V,EAAAA,SAAS,IAAI,EACnD,CAACkU,EAAS2B,CAAU,EAAI7V,EAAAA,SAAS,EAAI,EACrC,CAAC8E,EAAOgR,CAAQ,EAAI9V,EAAAA,SAAS,EAAE,EAErCG,EAAAA,UAAU,IAAM,CACK4V,EAAA,CAAA,EAClB,CAACnQ,EAAW8P,CAAgB,CAAC,EAEhC,MAAMK,EAAqB,SAAY,CACjC,GAAA,CAMF,GALAF,EAAW,EAAI,EACfC,EAAS,EAAE,EAIP,CADU,aAAa,QAAQ,YAAY,EACnC,CACVA,EAAS,6DAA6D,EACtED,EAAW,EAAK,EAChB,MAAA,CAGI,MAAAG,MAAU,KACZ,IAAAC,MAAY,KAEhB,OAAQrQ,EAAW,CACjB,IAAK,YACHqQ,EAAM,QAAQD,EAAI,QAAQ,EAAI,CAAC,EAC/B,MACF,IAAK,aACHC,EAAM,QAAQD,EAAI,QAAQ,EAAI,EAAE,EAChC,MACF,IAAK,aACHC,EAAM,QAAQD,EAAI,QAAQ,EAAI,EAAE,EAChC,MACF,QACEC,EAAM,QAAQD,EAAI,QAAQ,EAAI,EAAE,CAAA,CAG9B,MAAA7I,EAAS,MAAMxH,EAAS,uBAC5BsQ,EAAM,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC,EAChCD,EAAI,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC,EAC9BN,CACF,EAGA,GAAIvI,EAAO,SAAS,IAAMA,EAAO,KAC/ByI,EAAkBzI,EAAO,KAAK,MAAQA,EAAO,IAAI,MAC5C,CAML,GALQ,QAAA,MACN,yBACAA,EAAO,SAAS,OAChBA,EAAO,SAAS,UAClB,EACIA,EAAO,SAAS,SAAW,KAAOA,EAAO,SAAS,SAAW,IAAK,CACpE2I,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEFA,EAAS,gCAAgC,CAAA,QAEpCI,EAAK,CACJ,QAAA,MAAM,sCAAuCA,CAAG,EACxDJ,EAAS,oCAAoC,CAAA,QAC7C,CACAD,EAAW,EAAK,CAAA,CAEpB,EAEA,GAAI3B,EACF,aACG,MAAI,CAAA,UAAU,0BACb,SAACrT,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAoB,uBAAA,QACnD,MAAI,CAAA,UAAU,qCACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,8BAA8B,KAAK,SAChD,eAAC,OAAK,CAAA,UAAU,kBAAkB,SAAA,aAAU,EAC9C,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIJ,GAAI+D,EACF,aACG,MAAI,CAAA,UAAU,0BACb,SAACjE,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAoB,uBAAA,EACnDF,EAAA,KAAA,MAAA,CAAI,UAAU,qBAAqB,KAAK,QACvC,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EACA+D,CAAA,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIJ,GAAI,CAAC6Q,EACH,aACG,MAAI,CAAA,UAAU,0BACb,SAAC9U,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAoB,uBAAA,EACpDF,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,mBAAA,CACX,EAAe,2DAAA,CAElB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIJ,KAAM,CAAE,QAAAoV,EAAS,sBAAAC,EAAuB,cAAAC,CAAkB,EAAAV,EAE1D,eAAQ,IAAI,8CAA+C,CACzD,QAAAQ,EACA,sBAAAC,EACA,cAAAC,CAAA,CACD,QAGE,MAAI,CAAA,UAAU,0BACb,SAACxV,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAoB,uBAAA,EACpDF,EAAAA,KAAC,OAAK,CAAA,UAAU,qBACb,SAAA,CAAQsV,EAAA,kBAAkB,mBAAiBA,EAAQ,YAAa,IAAI,aAAA,CAEvE,CAAA,CAAA,EACF,EAGAtV,EAAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAAAE,MAAC,MAAI,CAAA,UAAU,yBACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,0BACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAACF,OAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,6EACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,EAEvC,SAAAA,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,YAAA,CAAA,CACX,CAAA,EAEL,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAiB,oBAAA,EACrDA,EAAA,IAAA,KAAA,CAAG,UAAU,OAAQ,WAAQ,iBAAkB,CAAA,CAAA,CAClD,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,EAECA,MAAA,MAAA,CAAI,UAAU,yBACb,eAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,YACb,SAACF,OAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,6EACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,EAEvC,SAAAA,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,YAAA,CAAA,CACX,CAAA,EAEL,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAW,cAAA,EAChDF,EAAAA,KAAC,KAAG,CAAA,UAAU,OAAO,SAAA,CAAA,IAAEsV,EAAQ,WAAA,CAAY,CAAA,CAAA,CAC7C,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,EAECpV,MAAA,MAAA,CAAI,UAAU,yBACb,eAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,YACb,SAACF,OAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,0EACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,EAEvC,SAAAA,EAAA,IAAC,eAAA,CACC,KAAK,mBACL,UAAU,YAAA,CAAA,CACX,CAAA,EAEL,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAa,gBAAA,EAClDF,EAAAA,KAAC,KAAG,CAAA,UAAU,OAAO,SAAA,CAAA,IAAEsV,EAAQ,aAAA,CAAc,CAAA,CAAA,CAC/C,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,EAECpV,MAAA,MAAA,CAAI,UAAU,yBACb,eAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,YACb,SAACF,OAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,6EACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,EAEvC,SAAAA,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,YAAA,CAAA,CACX,CAAA,EAEL,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAO,UAAA,EAC3CA,EAAA,IAAA,KAAA,CAAG,UAAU,OAAQ,WAAsB,MAAO,CAAA,CAAA,CACrD,CAAA,CAAA,EACF,CAAA,CACF,EACF,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,cACb,SAAAA,EAAA,IAAC,MAAG,UAAU,kBAAkB,iCAAqB,CACvD,CAAA,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAF,OAAC,QAAM,CAAA,UAAU,yBACf,SAAA,CAAAE,MAAC,QAAM,CAAA,UAAU,cACf,SAAAF,EAAA,KAAC,KACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAW,aAAA,CAAA,EACfA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAY,cAAA,CAAA,EAChBA,EAAAA,IAAC,MAAG,SAAS,WAAA,CAAA,CAAA,CAAA,CACf,CACF,CAAA,QACC,QACE,CAAA,SAAAqV,EAAsB,IAAI,CAACE,EAAQxN,IAAU,CAC5C,MAAMyN,EAAa,OAAO,QAAQD,EAAO,SAAS,EAAE,KAClD,CAACjB,EAAGC,IAAMA,EAAE,CAAC,EAAID,EAAE,CAAC,GACpB,CAAC,EACGmB,EAAe,OAAO,QAAQF,EAAO,SAAS,EACjD,KAAK,CAACjB,EAAGC,IAAMA,EAAE,CAAC,EAAID,EAAE,CAAC,CAAC,EAC1B,MAAM,EAAG,CAAC,EAEb,cACG,KACC,CAAA,SAAA,CAAAtU,MAAC,MACC,SAACA,EAAA,IAAA,SAAA,CACE,SAAOuV,EAAA,OACL,QAAQ,KAAM,GAAG,EACjB,QAAQ,QAAUG,GAAMA,EAAE,YAAY,CAAC,CAC5C,CAAA,EACF,EACA1V,EAAAA,IAAC,KAAI,CAAA,SAAAuV,EAAO,iBAAkB,CAAA,SAC7B,KAAG,CAAA,SAAA,CAAA,IAAEA,EAAO,WAAA,EAAY,EACxBvV,EAAA,IAAA,KAAA,CACE,SACGwV,EAAA,GAAGA,EAAW,CAAC,CAAC,KAAKA,EAAW,CAAC,CAAC,IAClC,GACN,CAAA,EACAxV,EAAAA,IAAC,MACE,SACEyV,EAAA,IACC,CAAC,CAACtO,EAAMwO,CAAK,IACX,GAAGxO,EAAK,YAAa,CAAA,KAAKwO,CAAK,GAAA,EAElC,KAAK,IAAI,CACd,CAAA,CAAA,CAAA,EAtBO5N,CAuBT,CAAA,CAEH,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EACF,EAGAjI,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,cACb,SAAAA,EAAA,IAAC,MAAG,UAAU,kBAAkB,8BAAkB,CACpD,CAAA,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAF,OAAC,QAAM,CAAA,UAAU,yBACf,SAAA,CAAAE,MAAC,QAAM,CAAA,UAAU,cACf,SAAAF,EAAA,KAAC,KACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAS,WAAA,CAAA,EACbA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAQ,UAAA,CAAA,EACZA,EAAAA,IAAC,MAAG,SAAQ,UAAA,CAAA,EACZA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,CAAA,CAAA,CACX,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAcsV,EAAA,MAAM,EAAG,EAAE,EAAE,IAAI,CAAC1S,EAAOmF,IACtCjI,EAAAA,KAAC,KACC,CAAA,SAAA,CAACE,EAAAA,IAAA,KAAA,CACC,SAACA,EAAA,IAAA,QAAA,CACE,SAAI,IAAA,KAAK4C,EAAM,SAAS,EAAE,eAAe,CAAA,CAC5C,CACF,CAAA,EACC5C,EAAA,IAAA,KAAA,CACE,SAAM4C,EAAA,OACJ,QAAQ,KAAM,GAAG,EACjB,QAAQ,QAAU8S,GAAMA,EAAE,YAAa,CAAA,EAC5C,EACA1V,EAAAA,IAAC,MACC,SAACA,MAAA,OAAA,CAAK,UAAU,qBACb,SAAA4C,EAAM,SACJ,QAAQ,KAAM,GAAG,EACjB,QAAQ,QAAU8S,GAAMA,EAAE,YAAY,CAAC,CAC5C,CAAA,EACF,EACA1V,EAAAA,IAAC,KACC,CAAA,SAAAA,EAAA,IAAC,OAAK,CAAA,UAAU,mBACb,SAAM4C,EAAA,SAAS,YAAY,CAAA,CAC9B,CACF,CAAA,SACC,KAAG,CAAA,SAAA,CAAA,IAAEA,EAAM,KAAA,CAAM,CAAA,CAAA,CAvBX,EAAAmF,CAwBT,CACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,CC5VA,SAAwB6N,GAAqB,CAAE,UAAA/Q,EAAW,iBAAA8P,GAAoB,CAC5E,KAAM,CAACkB,EAAWC,CAAY,EAAI7W,EAAAA,SAAS,CAAA,CAAE,EACvC,CAACkU,EAAS2B,CAAU,EAAI7V,EAAAA,SAAS,EAAI,EACrC,CAAC8E,EAAOgR,CAAQ,EAAI9V,EAAAA,SAAS,EAAE,EAErCG,EAAAA,UAAU,IAAM,CACA2W,EAAA,CAAA,EACb,CAAClR,EAAW8P,CAAgB,CAAC,EAEhC,MAAMoB,EAAgB,SAAY,CAC5B,GAAA,CAMF,GALAjB,EAAW,EAAI,EACfC,EAAS,EAAE,EAIP,CADU,aAAa,QAAQ,YAAY,EACnC,CACVA,EAAS,6DAA6D,EACtED,EAAW,EAAK,EAChB,MAAA,CAGI,MAAA1I,EAAS,MAAMxH,EAAS,wBAC5BC,EACA8P,CACF,EAGA,GAAIvI,EAAO,SAAS,IAAMA,EAAO,KAC/B0J,EAAa1J,EAAO,KAAK,MAAQA,EAAO,IAAI,MACvC,CAML,GALQ,QAAA,MACN,2BACAA,EAAO,SAAS,OAChBA,EAAO,SAAS,UAClB,EACIA,EAAO,SAAS,SAAW,KAAOA,EAAO,SAAS,SAAW,IAAK,CACpE2I,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEFA,EAAS,kCAAkC,CAAA,QAEtCI,EAAK,CACJ,QAAA,MAAM,wCAAyCA,CAAG,EAC1DJ,EAAS,sCAAsC,CAAA,QAC/C,CACAD,EAAW,EAAK,CAAA,CAEpB,EAEMkB,EAAoBC,GAAa,CACrC,MAAMnD,EAAS,CACb,KAAM,aACN,MAAO,aACP,SAAU,UACV,SAAU,aACV,QAAS,YACT,KAAM,eACN,MAAO,SACT,EACO,OAAAA,EAAOmD,CAAQ,GAAKnD,EAAO,KACpC,EAEMoD,EAAmBlH,GAAS,CAChC,MAAMmH,EAAWnH,EAAK,MAAM,GAAG,EAAE,OAAO,OAAO,EAC3C,OAAAmH,EAAS,SAAW,EAAU,OAC9BA,EAAS,SAAW,EAAUA,EAAS,CAAC,EAE3BA,EAAS,MAAM,CAAC,EAAE,KAAK,KAAK,EAC7B,QAAQ,KAAM,GAAG,EAAE,QAAQ,QAAS,SAAUT,EAAG,CAC/D,OAAOA,EAAE,YAAY,CAAA,CACtB,CACH,EAEA,OAAIvC,QAEC,MAAI,CAAA,UAAU,0BACb,SAACrT,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAsB,yBAAA,QACrD,MAAI,CAAA,UAAU,qCACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,8BAA8B,KAAK,SAChD,eAAC,OAAK,CAAA,UAAU,kBAAkB,SAAA,aAAU,EAC9C,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIA+D,QAEC,MAAI,CAAA,UAAU,0BACb,SAACjE,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAsB,yBAAA,EACrDF,EAAA,KAAA,MAAA,CAAI,UAAU,qBAAqB,KAAK,QACvC,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EACA+D,CAAA,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAKD,MAAI,CAAA,UAAU,0BACb,SAACjE,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kBAAkB,SAAsB,yBAAA,EACtDF,EAAAA,KAAC,OAAK,CAAA,UAAU,qBACb,SAAA,CAAU+V,EAAA,OAAO,gBAAA,CACpB,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,mBACb,SAAC/V,EAAA,KAAA,QAAA,CAAM,UAAU,iCACf,SAAA,CAAAE,MAAC,QAAM,CAAA,UAAU,cACf,SAAAF,EAAA,KAAC,KACC,CAAA,SAAA,CAAAE,EAAAA,IAAC,MAAG,SAAI,MAAA,CAAA,EACRA,EAAAA,IAAC,MAAG,SAAI,MAAA,CAAA,EACRA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAe,iBAAA,CAAA,EACnBA,EAAAA,IAAC,MAAG,SAAS,WAAA,CAAA,CAAA,CAAA,CACf,CACF,CAAA,EACAA,EAAAA,IAAC,SACE,SAAU6V,EAAA,IAAI,CAACpM,EAAM1B,WACnB,KACC,CAAA,SAAA,CAAC/H,EAAA,IAAA,KAAA,CACC,gBAAC,MACC,CAAA,SAAA,CAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,UACZ,SAAAyJ,EAAK,YAAcyM,EAAgBzM,EAAK,IAAI,CAC/C,CAAA,EACCzJ,EAAA,IAAA,QAAA,CAAM,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CAAA,CAC3C,CACF,CAAA,QACC,KACC,CAAA,SAAAA,EAAA,IAAC,OAAA,CACC,UAAW,SAASgW,EAClBvM,EAAK,SACN,CAAA,cAEA,SAAKA,EAAA,SAAA,CAAA,EAEV,EACCzJ,EAAA,IAAA,KAAA,CAAI,SAAKyJ,EAAA,MAAM,iBAAiB,EAChCzJ,EAAA,IAAA,KAAA,CAAI,SAAKyJ,EAAA,gBAAgB,iBAAiB,EAC3CzJ,EAAAA,IAAC,KACC,CAAA,SAAAA,EAAA,IAAC,QACE,CAAA,SAAAyJ,EAAK,UACF,OAAO,QAAQA,EAAK,SAAS,EAC1B,IACC,CAAC,CAACtC,EAAMwO,CAAK,IACX,GAAGxO,EAAK,YAAa,CAAA,KAAKwO,CAAK,GAElC,EAAA,KAAK,IAAI,EACZ,GACN,CAAA,CACF,CAAA,CAAA,CA/BO,EAAA5N,CAgCT,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEC8N,EAAU,SAAW,GACpB7V,EAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAA,EAAA,IAAC,IAAE,CAAA,UAAU,kBAAkB,SAAA,qDAAA,CAE/B,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAEJ"}