// client/src/pages/services/page.jsx

import React from "react";
import Footer from "@/components/footers/Footer";
import Header from "@/components/headers/Header";
import { Link } from "react-router-dom";
import { menuItems } from "@/data/menu";
import MarqueeDark from "@/components/home/<USER>";
import { getTranslatedFeatures } from "@/data/features";
import { services6 } from "@/data/services";
import { useTranslation } from "react-i18next";

import UnifiedSEO from "@/components/common/UnifiedSEO";
import { getPageSEOData } from "@/utils/seoHelpers";
import "@/styles/benefits-cards.css";
export default function ElegantServicesPageDark() {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";
  // Get translated features directly using the t function
  const translatedFeatures = getTranslatedFeatures(t);

  const seoData = getPageSEOData("services");

  const title =
    currentLanguage === "en"
      ? seoData.title
      : "Teenused | DevSkills - Äri Transformatsioon Tehnoloogia Abil";

  const description =
    currentLanguage === "en"
      ? seoData.description
      : "Avasta, kuidas DevSkills saab transformeerida sinu ettevõtet tipptasemel tehnoloogialahenduste, tehisintellekti rakendamise ja äriprotsesside optimeerimise abil.";

  return (
    <>
      <UnifiedSEO
        title={title}
        description={description}
        slug="services"
        type="website"
        image="https://devskills.ee/services.jpg"
        schema={seoData.schema}
        keywords={seoData.keywords}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("services.page.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("services.page.subtitle")}
                      </p>
                    </div>
                  </div>
                </div>
              </section>
              <>
                {/* Services Section */}
                <section className="page-section bg-dark-1 light-content pb-0">
                  <div className="container">
                    <div className="row mb-n30">
                      {services6.map((elm, i) => (
                        <div
                          key={i}
                          className="col-md-6 col-lg-4 d-flex align-items-stretch mb-30"
                        >
                          <div className="service-card-container">
                            <div className="service-card-img">
                              <img
                                src={elm.image}
                                width={400}
                                height={250}
                                alt={t(`services.${elm.key}.title`)}
                                className="service-card-image"
                              />
                            </div>
                            <div className="service-card-content">
                              <h3 className="service-card-title">
                                {t(`services.${elm.key}.title`)}
                              </h3>
                              <div className="service-card-text">
                                {t(`services.${elm.key}.text`)}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </section>
                {/* End Services Section */}
                {/* Marquee Section */}
                <div className="page-section overflow-hidden">
                  <MarqueeDark />
                </div>
                {/* End Marquee Section */}
                {/* Benefits Section */}
                <section className="page-section bg-dark-1 light-content pt-0 z-index-1">
                  <div className="container position-relative">
                    {/* Grid */}
                    <div className="row">
                      {/* Text */}
                      <div className="col-md-12 col-lg-3 mb-md-50">
                        <h2 className="section-caption mb-xs-10">
                          {t("services.approach.title")}
                        </h2>
                        <h3 className="section-title-small mb-40">
                          {t("services.approach.subtitle")}
                        </h3>
                        <div className="section-line" />
                      </div>
                      {/* End Text */}
                      {/* Feature Item */}
                      {translatedFeatures.slice(0, 3).map((elm, i) => (
                        <div
                          key={i}
                          className="col-md-4 col-lg-3 d-flex align-items-stretch mb-sm-30"
                        >
                          <div className="alt-features-item border-left mt-0 benefits-card">
                            <div className="alt-features-icon icon-hover-anim">
                              <span className="icon-strong icon-strong-unhovered">
                                <svg
                                  width={24}
                                  height={24}
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  aria-hidden="true"
                                  focusable="false"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fillRule="evenodd"
                                  clipRule="evenodd"
                                >
                                  <path d={elm.svgPath} />
                                </svg>
                              </span>
                              <span
                                className="icon-strong icon-strong-hovered"
                                aria-hidden="true"
                              >
                                <svg
                                  width={24}
                                  height={24}
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  aria-hidden="true"
                                  focusable="false"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fillRule="evenodd"
                                  clipRule="evenodd"
                                >
                                  <path d={elm.svgPath} />
                                </svg>
                              </span>
                            </div>
                            <h4 className="alt-features-title">{elm.title}</h4>
                            <div className="alt-features-descr">
                              {elm.description}
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* End Feature Item */}
                    </div>
                    {/* End Grid */}
                  </div>
                </section>
                {/* End Benefits Section */}
                {/* Divider */}
                <hr className="mt-0 mb-0 white" />
                {/* End Divider */}
                {/* Call Action Section */}
                <section
                  className="page-section light-content"
                  style={{ backgroundColor: "#000000" }}
                >
                  <div className="container position-relative">
                    {/* Decorative Waves */}
                    <div className="position-relative">
                      <div
                        className="decoration-21 d-none d-lg-block"
                        data-rellax-y=""
                        data-rellax-speed="0.7"
                        data-rellax-percentage="0.35"
                      >
                        <img
                          src="/assets/images/decoration-3.svg"
                          className="svg-shape"
                          width={148}
                          height={148}
                          alt=""
                        />
                      </div>
                    </div>
                    {/* End Decorative Waves */}
                    <div className="row text-center wow fadeInUp">
                      <div className="col-md-10 offset-md-1 col-lg-6 offset-lg-3">
                        <p className="section-descr mb-50 mb-sm-30">
                          {t("services.cta.text")}
                        </p>
                        <div className="local-scroll">
                          <Link
                            to="/contact"
                            className="btn btn-mod btn-large btn-circle btn-hover-anim btn-w"
                          >
                            <span>{t("services.cta.button")}</span>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
