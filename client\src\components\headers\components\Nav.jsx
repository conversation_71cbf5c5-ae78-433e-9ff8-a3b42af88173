import React from "react";
import PropTypes from "prop-types";
import addScrollspy from "@/utils/addScrollSpy";
import { init_classic_menu_resize } from "@/utils/menuToggle";
import { scrollToElement } from "@/utils/scrollToElement";
import { closeMobileMenu } from "@/utils/toggleMobileMenu";
import { Link, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import LanguageAwareLink from "@/components/common/LanguageAwareLink";

export default function Nav({ links, animateY = false }) {
  const { t: translate } = useTranslation();

  const { pathname } = useLocation();

  useEffect(() => {
    setTimeout(() => {
      scrollToElement();
    }, 1000);
    init_classic_menu_resize();
    window.addEventListener("scroll", addScrollspy);
    window.addEventListener("resize", init_classic_menu_resize);

    return () => {
      window.removeEventListener("scroll", addScrollspy);
      window.removeEventListener("resize", init_classic_menu_resize);
    };
  }, []);

  return (
    <>
      {links[0]?.href?.includes("/") &&
        links.map((link, index) => {
          // Regular menu items
          return (
            <li key={index}>
              <LanguageAwareLink
                className={
                  pathname.split("/")[2] === link.href.split("/")[1]
                    ? "active"
                    : ""
                }
                to={link.href}
              >
                {animateY ? (
                  <span className="btn-animate-y">
                    <span className="btn-animate-y-1">
                      {translate(`menu.${link.text.toLowerCase()}`)}
                    </span>
                    <span className="btn-animate-y-2" aria-hidden="true">
                      {translate(`menu.${link.text.toLowerCase()}`)}
                    </span>
                  </span>
                ) : (
                  translate(`menu.${link.text.toLowerCase()}`)
                )}
              </LanguageAwareLink>
            </li>
          );
        })}
      {!links[0]?.href?.includes("/") &&
        links.map((link, index) => (
          <li className="scrollspy-link" key={index}>
            <a onClick={() => closeMobileMenu()} className="" href={link.href}>
              {animateY ? (
                <span className="btn-animate-y">
                  <span className="btn-animate-y-1">
                    {translate(`menu.${link.text.toLowerCase()}`)}
                  </span>
                  <span className="btn-animate-y-2" aria-hidden="true">
                    {translate(`menu.${link.text.toLowerCase()}`)}
                  </span>
                </span>
              ) : (
                translate(`menu.${link.text.toLowerCase()}`)
              )}
            </a>
          </li>
        ))}
    </>
  );
}

Nav.propTypes = {
  links: PropTypes.arrayOf(
    PropTypes.shape({
      href: PropTypes.string.isRequired,
      text: PropTypes.string.isRequired,
    })
  ).isRequired,
  animateY: PropTypes.bool,
};
