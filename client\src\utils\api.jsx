// client/src/utils/api.jsx
// API configuration for both development and production

const getApiBaseUrl = () => {
  // In production, both frontend and backend are served from the same domain
  if (import.meta.env.PROD) {
    return "https://devskills.ee/api";
  }

  // In development, backend runs on port 4004
  return "http://localhost:4004/api";
};

export const API_BASE_URL = getApiBaseUrl();

// Helper function to make API calls with proper headers
export const apiCall = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;

  // Check if this is a FormData request (file upload)
  const isFormData = options.body instanceof FormData;

  const defaultHeaders = {};

  // Only set Content-Type for non-FormData requests
  // FormData requests need the browser to set the Content-Type with boundary
  if (!isFormData) {
    defaultHeaders["Content-Type"] = "application/json";
  }

  // Add API key for contact form
  if (endpoint.includes("/contact")) {
    defaultHeaders["X-API-Key"] = "9afe34d2134b43e19163c50924df6714";
  }

  // Add auth token if available
  const token = localStorage.getItem("adminToken");
  if (token) {
    defaultHeaders["Authorization"] = `Bearer ${token}`;
  }

  const config = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);

    // Handle non-JSON responses (like file uploads)
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();
      return { response, data };
    } else {
      return { response, data: null };
    }
  } catch (error) {
    console.error("API call failed:", error);
    throw error;
  }
};

// Specific API functions
export const authAPI = {
  login: (credentials) =>
    apiCall("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    }),

  getMe: () => apiCall("/auth/me"),

  logout: () => apiCall("/auth/logout", { method: "POST" }),
};

export const blogAPI = {
  getPosts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/blog${queryString ? `?${queryString}` : ""}`);
  },

  getPost: (slug) => apiCall(`/blog/${slug}`),

  createPost: (formData) =>
    apiCall("/blog", {
      method: "POST",
      body: formData, // FormData for file upload
      headers: {}, // Let browser set Content-Type for FormData
    }),

  updatePost: (id, formData) =>
    apiCall(`/blog/${id}`, {
      method: "PUT",
      body: formData,
      headers: {},
    }),

  deletePost: (id) => apiCall(`/blog/${id}`, { method: "DELETE" }),

  toggleVisibility: (id) =>
    apiCall(`/blog/${id}/toggle-visibility`, {
      method: "PATCH",
    }),

  // Helper function to get posts for homepage (featured posts)
  getFeaturedPosts: (language = "en", limit = 3) => {
    return apiCall(
      `/blog?featured=true&limit=${limit}&published=true&language=${language}`
    );
  },

  // Helper function to get posts for blog listing page
  getBlogPosts: (params = {}) => {
    // Set default values
    const defaultParams = {
      language: "en",
      page: 1,
      limit: 9,
      published: "true",
    };

    // Merge with provided params
    const finalParams = { ...defaultParams, ...params };
    const queryString = new URLSearchParams(finalParams).toString();

    return apiCall(`/blog?${queryString}`);
  },

  // Create a comment on a blog post
  createComment: (slug, commentData) =>
    apiCall(`/blog/${slug}/comments`, {
      method: "POST",
      body: JSON.stringify(commentData),
      headers: {
        "Content-Type": "application/json",
      },
    }),
};

export const adminAPI = {
  getDashboard: () => apiCall("/admin/dashboard"),

  // Analytics endpoints
  getBlogAnalytics: (timeRange = "last30days", language = "all") =>
    apiCall(
      `/admin/analytics/blog?timeRange=${timeRange}&language=${language}`
    ),

  getBlogPostsAnalytics: (timeRange = "last30days", language = "all") =>
    apiCall(
      `/admin/analytics/posts?timeRange=${timeRange}&language=${language}`
    ),

  getConversionAnalytics: (startDate, endDate, language = "all") =>
    apiCall(
      `/admin/analytics/conversions?startDate=${startDate}&endDate=${endDate}&language=${language}`
    ),

  getStaticPagesAnalytics: (timeRange = "last30days", language = "all") =>
    apiCall(
      `/admin/analytics/pages?timeRange=${timeRange}&language=${language}`
    ),

  getPosts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/admin/posts${queryString ? `?${queryString}` : ""}`);
  },

  getPost: (id) => apiCall(`/admin/posts/${id}`),

  uploadImage: (file) => {
    const formData = new FormData();
    formData.append("image", file);
    return apiCall("/admin/upload-image", {
      method: "POST",
      body: formData,
      headers: {},
    });
  },

  getCategories: () => apiCall("/admin/categories"),

  createCategory: (category) =>
    apiCall("/admin/categories", {
      method: "POST",
      body: JSON.stringify(category),
    }),

  updateCategory: (id, category) =>
    apiCall(`/admin/categories/${id}`, {
      method: "PUT",
      body: JSON.stringify(category),
    }),

  deleteCategory: (id) =>
    apiCall(`/admin/categories/${id}`, {
      method: "DELETE",
    }),

  getTags: () => apiCall("/admin/tags"),

  createTag: (tag) =>
    apiCall("/admin/tags", {
      method: "POST",
      body: JSON.stringify(tag),
    }),

  updateTag: (id, tag) =>
    apiCall(`/admin/tags/${id}`, {
      method: "PUT",
      body: JSON.stringify(tag),
    }),

  deleteTag: (id) =>
    apiCall(`/admin/tags/${id}`, {
      method: "DELETE",
    }),

  // Product endpoints
  getProducts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/admin/products${queryString ? `?${queryString}` : ""}`);
  },

  getProduct: (id) => apiCall(`/admin/products/${id}`),

  createProduct: (productData) =>
    apiCall("/admin/products", {
      method: "POST",
      body: productData, // FormData for file uploads
      headers: {}, // Let browser set Content-Type for FormData
    }),

  updateProduct: (id, productData) =>
    apiCall(`/admin/products/${id}`, {
      method: "PUT",
      body: productData, // FormData for file uploads
      headers: {}, // Let browser set Content-Type for FormData
    }),

  deleteProduct: (id) =>
    apiCall(`/admin/products/${id}`, {
      method: "DELETE",
    }),
};

// Public API for products
export const productsAPI = {
  getProducts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/products${queryString ? `?${queryString}` : ""}`);
  },

  getProduct: (slug, language = "en") =>
    apiCall(`/products/${slug}?language=${language}`),
};

// Public API for categories
export const categoriesAPI = {
  getCategories: () => apiCall("/categories"),
};

// Public API for tags
export const tagsAPI = {
  getTags: () => apiCall("/tags"),
};

// Public API for blog archive
export const archiveAPI = {
  getArchive: () => apiCall("/blog/archive"),
};

export const contactAPI = {
  sendMessage: (message) =>
    apiCall("/contact", {
      method: "POST",
      body: JSON.stringify(message),
    }),
};
