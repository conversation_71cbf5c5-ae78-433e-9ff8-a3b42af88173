{"version": 3, "mappings": ";8ZAMA,IAAIA,EAAgB,KAChBC,EAAe,KAMnB,eAAeC,GAAkB,CAC/B,OAAIF,GAIAC,IAIJA,GAAgB,SAAY,CAC1B,GAAI,CAEF,MAAME,GAAS,MAAKC,EAAA,wBAAAC,CAAA,OAAC,QAAO,2BAAS,OAAAC,KAAA,GAAC,eAAAD,CAAA,8BAAE,QAGxC,aAAKD,EAAA,IAAC,OAAO,2BAAmC,OAAAE,KAAA,6BAGhD,YAAM,OAAO,2BAAqC,OAAAA,KAAA,6BAClD,YAAM,OAAO,2BAAqC,OAAAA,KAAA,6BAClD,YAAM,OAAO,2BAA8B,OAAAA,KAAA,6BAC3C,YAAM,OAAO,2BAAiC,OAAAA,KAAA,6BAC9C,YAAM,OAAO,2BAA+B,OAAAA,KAAA,6BAC5C,YAAM,OAAO,2BAA+B,OAAAA,KAAA,6BAC5C,YAAM,OAAO,2BAA8B,OAAAA,KAAA,6BAE3CN,EAAgBG,EAChB,QAAQ,IAAI,4DAA4D,EACjEH,CACR,OAAQO,EAAO,CACd,cAAQ,MAAM,iCAAkCA,CAAK,EAC/CA,CACZ,CACA,GAAM,EAEGN,EACT,CAMO,eAAeO,EAAoBC,EAAW,yBAA0B,CAC7E,GAAI,CACF,MAAMN,EAAQ,MAAMD,EAAiB,EAClB,SAAS,iBAAiBO,CAAQ,EAE1C,QAASC,GAAU,CAC5B,MAAMC,EAAOD,EAAM,aAAe,GAC5BE,EAAWC,EAAeH,CAAK,GAAK,aAE1C,GAAI,CAEF,MAAMI,EAAUX,EAAM,UAAUS,CAAQ,EAExC,GAAIE,EAAS,CACX,MAAMC,EAAOZ,EAAM,UAAUQ,EAAMG,EAASF,CAAQ,EACpDF,EAAM,UAAYK,EAClBL,EAAM,UAAU,IAAI,mBAAmB,EACvCA,EAAM,UAAU,IAAI,YAAYE,CAAQ,EAAE,CACpD,MAEUF,EAAM,UAAU,IAAI,gBAAgB,EACpC,QAAQ,KAAK,aAAaE,CAAQ,6BAA6B,CAElE,OAAQI,EAAW,CAClB,QAAQ,KACN,iDAAiDJ,CAAQ,KACzDI,CACD,EAEDN,EAAM,UAAU,IAAI,gBAAgB,CAC5C,CACA,CAAK,CACF,OAAQH,EAAO,CACd,QAAQ,MAAM,mCAAoCA,CAAK,CAE3D,CACA,CAeA,SAASM,EAAeI,EAAa,CAEnC,MAAMC,EAAcD,EAAY,UAAU,MAAM,gBAAgB,EAChE,GAAIC,EACF,OAAOA,EAAY,CAAC,EAItB,MAAMC,EAAaF,EAAY,QAAQ,KAAK,EAC5C,GAAIE,EAAY,CACd,MAAMC,EAAaD,EAAW,UAAU,MAAM,gBAAgB,EAC9D,GAAIC,EACF,OAAOA,EAAW,CAAC,CAEzB,CAGE,MAAMT,EAAOM,EAAY,aAAe,GAExC,OAAIN,EAAK,SAAS,UAAU,GAAKA,EAAK,SAAS,GAAG,EACzC,aAELA,EAAK,SAAS,MAAM,GAAKA,EAAK,SAAS,GAAG,EACrC,SAELA,EAAK,SAAS,OAAO,EAChB,MAELA,EAAK,SAAS,OAAO,GAAKA,EAAK,SAAS,WAAW,EAC9C,OAELA,EAAK,SAAS,QAAQ,GAAKA,EAAK,SAAS,MAAM,EAC1C,MAGF,MACT", "names": ["prismInstance", "prismPromise", "initializePrism", "Prism", "__vitePreload", "__vite_default__", "n", "error", "highlightCodeBlocks", "selector", "block", "code", "language", "detectLanguage", "grammar", "html", "langError", "codeElement", "codeClasses", "preElement", "preClasses"], "ignoreList": [], "sources": ["../../src/utils/syntaxHighlighting.js"], "sourcesContent": ["/**\n * Centralized syntax highlighting service using Prism.js\n * This service ensures only essential languages are loaded and shared\n * between blog-single page and TipTapEditor\n */\n\nlet prismInstance = null;\nlet prismPromise = null;\n\n/**\n * Initialize Prism.js with essential languages only\n * Returns a promise that resolves to the Prism instance\n */\nasync function initializePrism() {\n  if (prismInstance) {\n    return prismInstance;\n  }\n\n  if (prismPromise) {\n    return prismPromise;\n  }\n\n  prismPromise = (async () => {\n    try {\n      // Import Prism core\n      const Prism = (await import(\"prismjs\")).default;\n\n      // Import Prism CSS theme (dark theme for better visual appeal)\n      await import(\"prismjs/themes/prism-tomorrow.css\");\n\n      // Import only essential languages (saves massive bundle size)\n      await import(\"prismjs/components/prism-javascript\");\n      await import(\"prismjs/components/prism-typescript\");\n      await import(\"prismjs/components/prism-css\");\n      await import(\"prismjs/components/prism-python\");\n      await import(\"prismjs/components/prism-json\");\n      await import(\"prismjs/components/prism-bash\");\n      await import(\"prismjs/components/prism-sql\");\n\n      prismInstance = Prism;\n      console.log(\"Prism.js initialized successfully with essential languages\");\n      return prismInstance;\n    } catch (error) {\n      console.error(\"Failed to initialize Prism.js:\", error);\n      throw error;\n    }\n  })();\n\n  return prismPromise;\n}\n\n/**\n * Highlight code blocks in the DOM using Prism.js\n * @param {string} selector - CSS selector for code blocks (default: '.blog-content pre code')\n */\nexport async function highlightCodeBlocks(selector = \".blog-content pre code\") {\n  try {\n    const Prism = await initializePrism();\n    const codeBlocks = document.querySelectorAll(selector);\n\n    codeBlocks.forEach((block) => {\n      const code = block.textContent || \"\";\n      const language = detectLanguage(block) || \"javascript\";\n\n      try {\n        // Use Prism to highlight the code\n        const grammar = Prism.languages[language];\n\n        if (grammar) {\n          const html = Prism.highlight(code, grammar, language);\n          block.innerHTML = html;\n          block.classList.add(\"prism-highlighted\");\n          block.classList.add(`language-${language}`);\n        } else {\n          // Fallback: just add basic styling without syntax highlighting\n          block.classList.add(\"prism-fallback\");\n          console.warn(`Language \"${language}\" not supported by Prism.js`);\n        }\n      } catch (langError) {\n        console.warn(\n          `Failed to highlight code block with language \"${language}\":`,\n          langError\n        );\n        // Fallback: just add basic styling without syntax highlighting\n        block.classList.add(\"prism-fallback\");\n      }\n    });\n  } catch (error) {\n    console.error(\"Failed to highlight code blocks:\", error);\n    // Graceful degradation - code blocks will still be visible\n  }\n}\n\n/**\n * Get Prism instance for custom usage (e.g., in TipTapEditor)\n * @returns {Promise<Object>} Prism highlighter instance\n */\nexport async function getPrismInstance() {\n  return initializePrism();\n}\n\n/**\n * Detect language from code block class or parent element\n * @param {Element} codeElement - The code element\n * @returns {string|null} Detected language or null\n */\nfunction detectLanguage(codeElement) {\n  // Check for language class on code element\n  const codeClasses = codeElement.className.match(/language-(\\w+)/);\n  if (codeClasses) {\n    return codeClasses[1];\n  }\n\n  // Check for language class on parent pre element\n  const preElement = codeElement.closest(\"pre\");\n  if (preElement) {\n    const preClasses = preElement.className.match(/language-(\\w+)/);\n    if (preClasses) {\n      return preClasses[1];\n    }\n  }\n\n  // Try to detect from content (basic heuristics)\n  const code = codeElement.textContent || \"\";\n\n  if (code.includes(\"function\") && code.includes(\"{\")) {\n    return \"javascript\";\n  }\n  if (code.includes(\"def \") && code.includes(\":\")) {\n    return \"python\";\n  }\n  if (code.includes(\"<?php\")) {\n    return \"php\";\n  }\n  if (code.includes(\"<html\") || code.includes(\"<!DOCTYPE\")) {\n    return \"html\";\n  }\n  if (code.includes(\"SELECT\") || code.includes(\"FROM\")) {\n    return \"sql\";\n  }\n\n  return \"text\";\n}\n\n// Removed old Lowlight-specific helper functions\n// Prism.js handles HTML generation internally\n"], "file": "assets/syntaxHighlighting-DKW5YAVq.js"}