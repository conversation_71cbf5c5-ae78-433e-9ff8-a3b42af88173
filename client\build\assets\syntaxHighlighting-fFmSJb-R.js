const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/vendor-misc-BUjjPnRU.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css"])))=>i.map(i=>d[i]);
import{_ as r}from"./pages-other-Brska3YB.js";import"./vendor-react-BE9lZbv0.js";import"./vendor-misc-BUjjPnRU.js";import"./vendor-animations-Dl3DQHMd.js";import"./vendor-gallery-BKyWYjF6.js";import"./vendor-admin-DSFDn6-z.js";import"./components-layout-DatcrN6Q.js";import"./components-common-BqxwEfqr.js";import"./vendor-i18n-Bi47f5qT.js";import"./components-home-F0XSl50W.js";import"./vendor-utils-t--hEgTQ.js";let l=null,c=null;async function _(){return l||c||(c=(async()=>{try{const e=(await r(async()=>{const{default:t}=await import("./vendor-misc-BUjjPnRU.js").then(a=>a.p);return{default:t}},__vite__mapDeps([0,1,2]))).default;return await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.t),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.u),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.v),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.w),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.x),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.y),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.z),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-BUjjPnRU.js").then(t=>t.B),__vite__mapDeps([0,1,2])),l=e,console.log("Prism.js initialized successfully with essential languages"),l}catch(e){throw console.error("Failed to initialize Prism.js:",e),e}})(),c)}async function T(e=".blog-content pre code"){try{const t=await _();document.querySelectorAll(e).forEach(i=>{const s=i.textContent||"",n=d(i)||"javascript";try{const o=t.languages[n];if(o){const u=t.highlight(s,o,n);i.innerHTML=u,i.classList.add("prism-highlighted"),i.classList.add(`language-${n}`)}else i.classList.add("prism-fallback"),console.warn(`Language "${n}" not supported by Prism.js`)}catch(o){console.warn(`Failed to highlight code block with language "${n}":`,o),i.classList.add("prism-fallback")}})}catch(t){console.error("Failed to highlight code blocks:",t)}}function d(e){const t=e.className.match(/language-(\w+)/);if(t)return t[1];const a=e.closest("pre");if(a){const s=a.className.match(/language-(\w+)/);if(s)return s[1]}const i=e.textContent||"";return i.includes("function")&&i.includes("{")?"javascript":i.includes("def ")&&i.includes(":")?"python":i.includes("<?php")?"php":i.includes("<html")||i.includes("<!DOCTYPE")?"html":i.includes("SELECT")||i.includes("FROM")?"sql":"text"}export{T as highlightCodeBlocks};
//# sourceMappingURL=syntaxHighlighting-fFmSJb-R.js.map
