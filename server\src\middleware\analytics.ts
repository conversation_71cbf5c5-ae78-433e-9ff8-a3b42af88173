import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';

const prisma = new PrismaClient();

// Extract language from path
const extractLanguageFromPath = (path: string): string => {
  const match = path.match(/^\/([a-z]{2})\//);
  return match ? match[1] : 'en'; // Default to English
};

// Generate session ID from IP and User Agent
const generateSessionId = (ip: string, userAgent: string): string => {
  return crypto.createHash('md5').update(`${ip}-${userAgent}`).digest('hex');
};

// Get client IP address
const getClientIP = (req: Request): string => {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  );
};

// Middleware to track page views
export const trackPageView = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Only track GET requests for HTML pages
    if (req.method !== 'GET' || req.path.includes('/api/') || req.path.includes('.')) {
      return next();
    }

    const path = req.path;
    const language = extractLanguageFromPath(path);
    const userAgent = req.headers['user-agent'] || '';
    const ipAddress = getClientIP(req);
    const referrer = req.headers.referer || '';
    const sessionId = generateSessionId(ipAddress, userAgent);

    // Check if this is a blog post
    let blogPostId = null;
    const blogPostMatch = path.match(/\/blog-single\/([^\/]+)/);
    if (blogPostMatch) {
      const slug = blogPostMatch[1];
      const blogPost = await prisma.blogPost.findUnique({
        where: { slug },
        select: { id: true }
      });
      if (blogPost) {
        blogPostId = blogPost.id;
        
        // Increment view count
        await prisma.blogPost.update({
          where: { id: blogPost.id },
          data: { viewCount: { increment: 1 } }
        });
      }
    }

    // Record page view (async, don't wait)
    prisma.pageView.create({
      data: {
        path,
        language,
        userAgent,
        ipAddress,
        referrer,
        sessionId,
        blogPostId
      }
    }).catch(error => {
      console.error('Error recording page view:', error);
    });

    next();
  } catch (error) {
    console.error('Error in trackPageView middleware:', error);
    next(); // Continue even if tracking fails
  }
};

// Function to track custom events
export const trackEvent = async (
  eventName: string,
  eventCategory: string,
  eventLabel: string,
  eventValue: number | null,
  path: string,
  language: string,
  userAgent: string,
  ipAddress: string,
  blogPostId: string | null = null,
  metadata: any = null
) => {
  try {
    const sessionId = generateSessionId(ipAddress, userAgent);

    await prisma.analyticsEvent.create({
      data: {
        eventName,
        eventCategory,
        eventLabel,
        eventValue,
        path,
        language,
        userAgent,
        ipAddress,
        sessionId,
        blogPostId,
        metadata
      }
    });
  } catch (error) {
    console.error('Error tracking event:', error);
  }
};
