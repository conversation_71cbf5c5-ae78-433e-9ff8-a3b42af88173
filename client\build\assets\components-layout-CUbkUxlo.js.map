{"version": 3, "file": "components-layout-CUbkUxlo.js", "sources": ["../../src/data/footer.js", "../../src/components/footers/Footer.jsx", "../../src/utils/toggleMobileMenu.js", "../../src/utils/addScrollSpy.js", "../../src/utils/menuToggle.js", "../../src/utils/scrollToElement.js", "../../src/components/headers/components/Nav.jsx", "../../src/components/headers/Header.jsx"], "sourcesContent": ["export const socialMediaLinks = [\n  {\n    href: \"#\",\n    title: \"Facebook\",\n    iconClass: \"fa-facebook-f\",\n    name: \"Facebook\",\n  },\n  {\n    href: \"#\",\n    title: \"X\",\n    iconClass: \"fa-x-twitter\",\n    name: \"X\",\n  },\n  {\n    href: \"#\",\n    title: \"Instagram\",\n    iconClass: \"fa-instagram\",\n    name: \"Instagram\",\n  },\n  {\n    href: \"https://www.youtube.com/@DevSkillsStudio\",\n    title: \"YouTube\",\n    iconClass: \"fa-youtube\",\n    name: \"YouTube\",\n  },\n  {\n    href: \"https://www.linkedin.com/company/devskills-development-studio\",\n    title: \"LinkedIn\",\n    iconClass: \"fa-linkedin-in\",\n    name: \"LinkedIn\",\n  },\n  {\n    href: \"#\",\n    title: \"WhatsApp\",\n    iconClass: \"fa-whatsapp\",\n    name: \"WhatsApp\",\n  },\n];\n\nexport const navigationLinks = [\n  { href: \"#about\", text: \"About\" },\n  { href: \"#services\", text: \"Services\" },\n  { href: \"#portfolio\", text: \"Portfolio\" },\n  { href: \"#blog\", text: \"Blog\" },\n  { href: \"#contact\", text: \"Contact\" },\n];\n", "import { socialMediaLinks } from \"@/data/footer\";\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Footer() {\n  const { t } = useTranslation();\n\n  const scrollToTop = (event) => {\n    event.preventDefault();\n    window.scrollTo({\n      top: 0,\n      behavior: \"smooth\",\n    });\n  };\n\n  return (\n    <div className=\"container position-relative text-center pt-140 pb-80 pb-sm-50\">\n      {/* Scroll Up */}\n      <div className=\"local-scroll link-to-top-2-wrap\">\n        <a href=\"#top\" className=\"link-to-top-2\" onClick={scrollToTop}>\n          {t(\"footer.backToTop\")}\n        </a>\n      </div>\n      {/* End Scroll Up */}\n\n      <div className=\"footer-social-links mb-60\">\n        {socialMediaLinks.map((elm, i) => (\n          <a\n            href={elm.href}\n            key={i}\n            title={elm.title}\n            rel=\"noreferrer nofollow\"\n            target=\"_blank\"\n          >\n            <span className=\"visually-hidden\">{elm.name}</span>\n            <i className={`fa ${elm.iconClass}`} />\n          </a>\n        ))}\n      </div>\n\n      {/* Footer Text */}\n      <div className=\"footer-text\">\n        {/* Copyright */}\n        <div>\n          © Devskills OÜ {new Date().getFullYear()} -{\" \"}\n          {t(\"footer.allRightsReserved\")}\n        </div>\n        {/* End Copyright */}\n        <div className=\"footer-made\">\n          Making great things that people actually need\n        </div>\n      </div>\n      {/* End Footer Text */}\n    </div>\n  );\n}\n", "export const toggleMobileMenu = () => {\n  var mobile_nav = document.querySelector(\".mobile-nav\");\n  var desktop_nav = document.querySelector(\".desktop-nav\");\n\n  if (desktop_nav.classList.contains(\"js-opened\")) {\n    desktop_nav.style.maxHeight = \"0px\";\n    desktop_nav.classList.remove(\"js-opened\");\n    mobile_nav.classList.remove(\"active\");\n  } else {\n    desktop_nav.style.maxHeight = \"calc(100vh - 60px)\";\n    desktop_nav.classList.add(\"js-opened\");\n    mobile_nav.classList.add(\"active\");\n  }\n};\nexport const closeMobileMenu = () => {\n  var mobile_nav = document.querySelector(\".mobile-nav\");\n  var desktop_nav = document.querySelector(\".desktop-nav\");\n\n  if (desktop_nav.classList.contains(\"js-opened\")) {\n    desktop_nav.style.maxHeight = \"0px\";\n    desktop_nav.classList.remove(\"js-opened\");\n    mobile_nav.classList.remove(\"active\");\n  }\n};\n", "const addScrollspy = (\n  defalutClass = \"\",\n  linksContainer = \".scrollspy-link\",\n  activeClass = \"active\"\n) => {\n  var section = document.querySelectorAll(\".scrollSpysection\");\n  // console.log(section);\n\n  var sections = {};\n  var i = 0;\n\n  Array.prototype.forEach.call(section, function (e) {\n    sections[e.id] = e.offsetTop;\n  });\n  var scrollPosition =\n    document.documentElement.scrollTop || document.body.scrollTop;\n\n  for (i in sections) {\n    if (sections[i] <= scrollPosition) {\n      document\n        .querySelector(linksContainer + ` .${activeClass}`)\n        ?.setAttribute(\"class\", defalutClass);\n      const navLink = document.querySelector(\n        linksContainer + \" a[href*=\" + i + \"]\"\n      );\n\n      navLink?.setAttribute(\"class\", `${defalutClass} ${activeClass}`);\n    }\n  }\n};\nexport default addScrollspy;\n", "export function init_classic_menu_resize() {\n  var mobile_nav = document.querySelector(\".mobile-nav\");\n  var desktop_nav = document.querySelector(\".desktop-nav\");\n\n  mobile_nav.setAttribute(\"aria-expanded\", \"false\");\n\n  // Mobile menu max height\n  if (document.querySelector(\".main-nav\")) {\n    document.querySelector(\".desktop-nav > ul\").style.maxHeight =\n      window.innerHeight -\n      document.querySelector(\".main-nav\").offsetHeight -\n      20 +\n      \"px\";\n  }\n\n  // Mobile menu style toggle\n  if (window.innerWidth <= 1024) {\n    document.querySelector(\".main-nav\").classList.add(\"mobile-on\");\n    if (!mobile_nav.classList.contains(\"active\")) {\n      desktop_nav.style.display = \"none\";\n    }\n  } else if (window.innerWidth > 1024) {\n    document.querySelector(\".main-nav\").classList.remove(\"mobile-on\");\n    desktop_nav.style.display = \"block\";\n  }\n}\n", "export const scrollToElement = () => {\n  document.querySelectorAll('a[href^=\"#\"]').forEach((anchor) => {\n    if (\n      anchor.href &&\n      anchor.href != \"#\" &&\n      !anchor.hasAttribute(\"data-bs-toggle\")\n    ) {\n      //   console.log(anchor.href);\n      anchor.addEventListener(\"click\", (e) => {\n        e.preventDefault();\n        console.log(\"clicle\");\n        const id = anchor.href.split(\"#\")[1];\n        const element = document.getElementById(id);\n        if (element) {\n          const yOffset = +70; // Adjust this offset as per your layout\n          const y =\n            element.getBoundingClientRect().top + window.pageYOffset + yOffset;\n          window.scrollTo({ top: y, behavior: \"smooth\" });\n        }\n      });\n    }\n  });\n};\n", "import React from \"react\";\nimport PropTypes from \"prop-types\";\nimport addScrollspy from \"@/utils/addScrollSpy\";\nimport { init_classic_menu_resize } from \"@/utils/menuToggle\";\nimport { scrollToElement } from \"@/utils/scrollToElement\";\nimport { closeMobileMenu } from \"@/utils/toggleMobileMenu\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nimport LanguageAwareLink from \"@/components/common/LanguageAwareLink\";\n\nexport default function Nav({ links, animateY = false }) {\n  const { t: translate } = useTranslation();\n\n  const { pathname } = useLocation();\n\n  useEffect(() => {\n    setTimeout(() => {\n      scrollToElement();\n    }, 1000);\n    init_classic_menu_resize();\n    window.addEventListener(\"scroll\", addScrollspy);\n    window.addEventListener(\"resize\", init_classic_menu_resize);\n\n    return () => {\n      window.removeEventListener(\"scroll\", addScrollspy);\n      window.removeEventListener(\"resize\", init_classic_menu_resize);\n    };\n  }, []);\n\n  return (\n    <>\n      {links[0]?.href?.includes(\"/\") &&\n        links.map((link, index) => {\n          // Regular menu items\n          return (\n            <li key={index}>\n              <LanguageAwareLink\n                className={\n                  pathname.split(\"/\")[2] === link.href.split(\"/\")[1]\n                    ? \"active\"\n                    : \"\"\n                }\n                to={link.href}\n              >\n                {animateY ? (\n                  <span className=\"btn-animate-y\">\n                    <span className=\"btn-animate-y-1\">\n                      {translate(`menu.${link.text.toLowerCase()}`)}\n                    </span>\n                    <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                      {translate(`menu.${link.text.toLowerCase()}`)}\n                    </span>\n                  </span>\n                ) : (\n                  translate(`menu.${link.text.toLowerCase()}`)\n                )}\n              </LanguageAwareLink>\n            </li>\n          );\n        })}\n      {!links[0]?.href?.includes(\"/\") &&\n        links.map((link, index) => (\n          <li className=\"scrollspy-link\" key={index}>\n            <a onClick={() => closeMobileMenu()} className=\"\" href={link.href}>\n              {animateY ? (\n                <span className=\"btn-animate-y\">\n                  <span className=\"btn-animate-y-1\">\n                    {translate(`menu.${link.text.toLowerCase()}`)}\n                  </span>\n                  <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                    {translate(`menu.${link.text.toLowerCase()}`)}\n                  </span>\n                </span>\n              ) : (\n                translate(`menu.${link.text.toLowerCase()}`)\n              )}\n            </a>\n          </li>\n        ))}\n    </>\n  );\n}\n\nNav.propTypes = {\n  links: PropTypes.arrayOf(\n    PropTypes.shape({\n      href: PropTypes.string.isRequired,\n      text: PropTypes.string.isRequired,\n    })\n  ).isRequired,\n  animateY: PropTypes.bool,\n};\n", "// client\\src\\components\\headers\\Header.jsx\n\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { toggleMobileMenu } from \"@/utils/toggleMobileMenu\";\nimport Nav from \"./components/Nav\";\nimport LanguageSelector from \"@/components/common/LanguageSelector\";\nimport LanguageAwareLink from \"@/components/common/LanguageAwareLink\";\nimport { useTranslation } from \"react-i18next\";\nimport { trackComanagerConversion } from \"@/utils/analytics\";\nimport \"@/styles/languageSelector.css\";\n\nexport default function Header({ links }) {\n  const { t } = useTranslation();\n\n  // Add error handling for language store\n  if (!t) {\n    console.warn(\"Language store not initialized, using fallback\");\n  }\n\n  // Function to handle navbar Business Comanager click\n  const handleNavbarComanagerClick = (e) => {\n    e.preventDefault();\n    trackComanagerConversion(\"navbar\", {\n      cta_type: \"navbar_button\",\n      section: \"navigation\",\n    });\n    window.open(\"https://comanager.ee\", \"_blank\");\n  };\n\n  return (\n    <div className=\"main-nav-sub full-wrapper\">\n      {/* Logo  (* Add your text or image to the link tag. Use SVG or PNG image format.\n              If you use a PNG logo image, the image resolution must be equal 200% of the visible logo\n              image size for support of retina screens. See details in the template documentation. *) */}\n      <div className=\"nav-logo-wrap local-scroll\">\n        <LanguageAwareLink\n          to=\"/\"\n          className=\"logo font-alt d-flex align-items-center gap-2 pointer-event\"\n        >\n          <img\n            src=\"/assets/img/power-128.png\"\n            alt=\"Your Company Logo\"\n            width={26}\n            height={24}\n          />\n          <span className=\"mt-1\">DEVSKILLS</span>\n        </LanguageAwareLink>\n      </div>\n\n      {/* Mobile Menu Button */}\n      <div\n        onClick={toggleMobileMenu}\n        className=\"mobile-nav\"\n        role=\"button\"\n        tabIndex={0}\n      >\n        <i className=\"mobile-nav-icon\" />\n        <span className=\"visually-hidden\">Menu</span>\n      </div>\n      {/* Main Menu */}\n      <div className=\"inner-nav desktop-nav\">\n        <ul className=\"clearlist scroll-nav local-scroll justify-content-end scrollspyLinks\">\n          <Nav links={links} />\n          <li className=\"ms-3 me-2 d-flex align-items-center\">\n            <LanguageSelector />\n          </li>\n          <li>\n            <a\n              href=\"https://comanager.ee\"\n              onClick={handleNavbarComanagerClick}\n              className=\"opacity-1 no-hover\"\n              style={{ cursor: \"pointer\" }}\n            >\n              <span\n                className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                data-btn-animate=\"y\"\n              >\n                <span className=\"btn-animate-y\">\n                  <span className=\"btn-animate-y-1\">Business Comanager</span>\n                  <span className=\"btn-animate-y-2\" aria-hidden=\"true\">\n                    Business Comanager\n                  </span>\n                </span>\n              </span>\n            </a>\n          </li>\n        </ul>\n      </div>\n      {/* End Main Menu */}\n    </div>\n  );\n}\n\nHeader.propTypes = {\n  links: PropTypes.arrayOf(\n    PropTypes.shape({\n      href: PropTypes.string.isRequired,\n      text: PropTypes.string.isRequired,\n    })\n  ).isRequired,\n};\n"], "names": ["socialMediaLinks", "Footer", "t", "useTranslation", "scrollToTop", "event", "jsxs", "jsx", "elm", "i", "toggleMobileMenu", "mobile_nav", "desktop_nav", "closeMobileMenu", "addScrollspy", "defalutClass", "linksContainer", "activeClass", "section", "sections", "e", "scrollPosition", "_a", "navLink", "init_classic_menu_resize", "scrollToElement", "anchor", "id", "element", "y", "Nav", "links", "animateY", "translate", "pathname", "useLocation", "useEffect", "Fragment", "_b", "link", "index", "LanguageAwareLink", "_d", "_c", "PropTypes", "Header", "handleNavbarComanagerClick", "trackComanagerConversion", "LanguageSelector"], "mappings": "0NAAO,MAAMA,EAAmB,CAC9B,CACE,KAAM,IACN,MAAO,WACP,UAAW,gBACX,KAAM,UACP,EACD,CACE,KAAM,IACN,MAAO,IACP,UAAW,eACX,KAAM,GACP,EACD,CACE,KAAM,IACN,MAAO,YACP,UAAW,eACX,KAAM,WACP,EACD,CACE,KAAM,2CACN,MAAO,UACP,UAAW,aACX,KAAM,SACP,EACD,CACE,KAAM,gEACN,MAAO,WACP,UAAW,iBACX,KAAM,UACP,EACD,CACE,KAAM,IACN,MAAO,WACP,UAAW,cACX,KAAM,UACP,CACH,ECjCA,SAAwBC,GAAS,CACzB,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EAEvBC,EAAeC,GAAU,CAC7BA,EAAM,eAAe,EACrB,OAAO,SAAS,CACd,IAAK,EACL,SAAU,QAAA,CACX,CACH,EAGE,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,gEAEb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,kCACb,SAAAA,EAAA,IAAC,KAAE,KAAK,OAAO,UAAU,gBAAgB,QAASH,EAC/C,SAAEF,EAAA,kBAAkB,CACvB,CAAA,EACF,EAGAK,EAAAA,IAAC,OAAI,UAAU,4BACZ,WAAiB,IAAI,CAACC,EAAKC,IAC1BH,EAAA,KAAC,IAAA,CACC,KAAME,EAAI,KAEV,MAAOA,EAAI,MACX,IAAI,sBACJ,OAAO,SAEP,SAAA,CAAAD,EAAA,IAAC,OAAK,CAAA,UAAU,kBAAmB,SAAAC,EAAI,KAAK,QAC3C,IAAE,CAAA,UAAW,MAAMA,EAAI,SAAS,EAAI,CAAA,CAAA,CAAA,EANhCC,CAQR,CAAA,EACH,EAGAH,EAAAA,KAAC,MAAI,CAAA,UAAU,cAEb,SAAA,CAAAA,OAAC,MAAI,CAAA,SAAA,CAAA,kBACa,IAAI,KAAK,EAAE,YAAY,EAAE,KAAG,IAC3CJ,EAAE,0BAA0B,CAAA,EAC/B,EAECK,EAAA,IAAA,MAAA,CAAI,UAAU,cAAc,SAE7B,+CAAA,CAAA,CAAA,CACF,CAAA,CAAA,EAEF,CAEJ,CCvDO,MAAMG,EAAmB,IAAM,CACpC,IAAIC,EAAa,SAAS,cAAc,aAAa,EACjDC,EAAc,SAAS,cAAc,cAAc,EAEnDA,EAAY,UAAU,SAAS,WAAW,GAC5CA,EAAY,MAAM,UAAY,MAC9BA,EAAY,UAAU,OAAO,WAAW,EACxCD,EAAW,UAAU,OAAO,QAAQ,IAEpCC,EAAY,MAAM,UAAY,qBAC9BA,EAAY,UAAU,IAAI,WAAW,EACrCD,EAAW,UAAU,IAAI,QAAQ,EAErC,EACaE,EAAkB,IAAM,CACnC,IAAIF,EAAa,SAAS,cAAc,aAAa,EACjDC,EAAc,SAAS,cAAc,cAAc,EAEnDA,EAAY,UAAU,SAAS,WAAW,IAC5CA,EAAY,MAAM,UAAY,MAC9BA,EAAY,UAAU,OAAO,WAAW,EACxCD,EAAW,UAAU,OAAO,QAAQ,EAExC,ECvBMG,EAAe,CACnBC,EAAe,GACfC,EAAiB,kBACjBC,EAAc,WACX,OACH,IAAIC,EAAU,SAAS,iBAAiB,mBAAmB,EAGvDC,EAAW,CAAE,EACbV,EAAI,EAER,MAAM,UAAU,QAAQ,KAAKS,EAAS,SAAUE,EAAG,CACjDD,EAASC,EAAE,EAAE,EAAIA,EAAE,SACvB,CAAG,EACD,IAAIC,EACF,SAAS,gBAAgB,WAAa,SAAS,KAAK,UAEtD,IAAKZ,KAAKU,EACR,GAAIA,EAASV,CAAC,GAAKY,EAAgB,EACjCC,EAAA,SACG,cAAcN,EAAiB,KAAKC,CAAW,EAAE,IADpD,MAAAK,EAEI,aAAa,QAASP,GAC1B,MAAMQ,EAAU,SAAS,cACvBP,EAAiB,YAAcP,EAAI,GACpC,EAEDc,GAAA,MAAAA,EAAS,aAAa,QAAS,GAAGR,CAAY,IAAIE,CAAW,GACnE,CAEA,EC7BO,SAASO,GAA2B,CACzC,IAAIb,EAAa,SAAS,cAAc,aAAa,EACjDC,EAAc,SAAS,cAAc,cAAc,EAEvDD,EAAW,aAAa,gBAAiB,OAAO,EAG5C,SAAS,cAAc,WAAW,IACpC,SAAS,cAAc,mBAAmB,EAAE,MAAM,UAChD,OAAO,YACP,SAAS,cAAc,WAAW,EAAE,aACpC,GACA,MAIA,OAAO,YAAc,MACvB,SAAS,cAAc,WAAW,EAAE,UAAU,IAAI,WAAW,EACxDA,EAAW,UAAU,SAAS,QAAQ,IACzCC,EAAY,MAAM,QAAU,SAErB,OAAO,WAAa,OAC7B,SAAS,cAAc,WAAW,EAAE,UAAU,OAAO,WAAW,EAChEA,EAAY,MAAM,QAAU,QAEhC,CCzBO,MAAMa,EAAkB,IAAM,CACnC,SAAS,iBAAiB,cAAc,EAAE,QAASC,GAAW,CAE1DA,EAAO,MACPA,EAAO,MAAQ,KACf,CAACA,EAAO,aAAa,gBAAgB,GAGrCA,EAAO,iBAAiB,QAAUN,GAAM,CACtCA,EAAE,eAAgB,EAClB,QAAQ,IAAI,QAAQ,EACpB,MAAMO,EAAKD,EAAO,KAAK,MAAM,GAAG,EAAE,CAAC,EAC7BE,EAAU,SAAS,eAAeD,CAAE,EAC1C,GAAIC,EAAS,CAEX,MAAMC,EACJD,EAAQ,sBAAuB,EAAC,IAAM,OAAO,YAAc,GAC7D,OAAO,SAAS,CAAE,IAAKC,EAAG,SAAU,SAAU,CACxD,CACA,CAAO,CAEP,CAAG,CACH,ECVA,SAAwBC,EAAI,CAAE,MAAAC,EAAO,SAAAC,EAAW,IAAS,aACvD,KAAM,CAAE,EAAGC,CAAU,EAAI9B,EAAe,EAElC,CAAE,SAAA+B,CAAS,EAAIC,EAAY,EAEjCC,OAAAA,EAAAA,UAAU,KACR,WAAW,IAAM,CACCX,EAAA,GACf,GAAI,EACkBD,EAAA,EAClB,OAAA,iBAAiB,SAAUV,CAAY,EACvC,OAAA,iBAAiB,SAAUU,CAAwB,EAEnD,IAAM,CACJ,OAAA,oBAAoB,SAAUV,CAAY,EAC1C,OAAA,oBAAoB,SAAUU,CAAwB,CAC/D,GACC,EAAE,EAIAlB,EAAA,KAAA+B,WAAA,CAAA,SAAA,GAAMC,GAAAhB,EAAAS,EAAA,CAAC,IAAD,YAAAT,EAAI,OAAJ,YAAAgB,EAAU,SAAS,OACxBP,EAAM,IAAI,CAACQ,EAAMC,UAGZ,KACC,CAAA,SAAAjC,EAAA,IAACkC,EAAA,CACC,UACEP,EAAS,MAAM,GAAG,EAAE,CAAC,IAAMK,EAAK,KAAK,MAAM,GAAG,EAAE,CAAC,EAC7C,SACA,GAEN,GAAIA,EAAK,KAER,SACCP,EAAA1B,OAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,UAAU,kBACb,SAAU0B,EAAA,QAAQM,EAAK,KAAK,YAAA,CAAa,EAAE,CAC9C,CAAA,EACChC,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAU0B,EAAA,QAAQM,EAAK,KAAK,YAAY,CAAC,EAAE,CAC9C,CAAA,CAAA,CACF,CAAA,EAEAN,EAAU,QAAQM,EAAK,KAAK,YAAA,CAAa,EAAE,CAAA,IAnBxCC,CAsBT,CAEH,EACF,GAACE,GAAAC,EAAAZ,EAAM,CAAC,IAAP,YAAAY,EAAU,OAAV,MAAAD,EAAgB,SAAS,OACzBX,EAAM,IAAI,CAACQ,EAAMC,UACd,KAAG,CAAA,UAAU,iBACZ,SAAAjC,EAAAA,IAAC,IAAE,CAAA,QAAS,IAAMM,EAAA,EAAmB,UAAU,GAAG,KAAM0B,EAAK,KAC1D,SAAAP,EACE1B,OAAA,OAAA,CAAK,UAAU,gBACd,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,UAAU,kBACb,SAAU0B,EAAA,QAAQM,EAAK,KAAK,YAAA,CAAa,EAAE,CAC9C,CAAA,EACChC,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,cAAY,OAC3C,SAAU0B,EAAA,QAAQM,EAAK,KAAK,YAAY,CAAC,EAAE,CAC9C,CAAA,CACF,CAAA,CAAA,EAEAN,EAAU,QAAQM,EAAK,KAAK,YAAY,CAAC,EAAE,EAE/C,CAdkC,EAAAC,CAepC,CACD,CAAA,EACL,CAEJ,CAEAV,EAAI,UAAY,CACd,MAAOc,EAAU,QACfA,EAAU,MAAM,CACd,KAAMA,EAAU,OAAO,WACvB,KAAMA,EAAU,OAAO,UACxB,CAAA,CAAA,EACD,WACF,SAAUA,EAAU,IACtB,ECjFwB,SAAAC,EAAO,CAAE,MAAAd,GAAS,CAClC,KAAA,CAAE,CAAE,EAAI5B,EAAe,EAGxB,GACH,QAAQ,KAAK,gDAAgD,EAIzD,MAAA2C,EAA8B1B,GAAM,CACxCA,EAAE,eAAe,EACjB2B,EAAyB,SAAU,CACjC,SAAU,gBACV,QAAS,YAAA,CACV,EACM,OAAA,KAAK,uBAAwB,QAAQ,CAC9C,EAGE,OAAAzC,EAAA,KAAC,MAAI,CAAA,UAAU,4BAIb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,6BACb,SAAAD,EAAA,KAACmC,EAAA,CACC,GAAG,IACH,UAAU,8DAEV,SAAA,CAAAlC,EAAA,IAAC,MAAA,CACC,IAAI,4BACJ,IAAI,oBACJ,MAAO,GACP,OAAQ,EAAA,CACV,EACCA,EAAA,IAAA,OAAA,CAAK,UAAU,OAAO,SAAS,WAAA,CAAA,CAAA,CAAA,CAAA,EAEpC,EAGAD,EAAA,KAAC,MAAA,CACC,QAASI,EACT,UAAU,aACV,KAAK,SACL,SAAU,EAEV,SAAA,CAACH,EAAAA,IAAA,IAAA,CAAE,UAAU,iBAAkB,CAAA,EAC9BA,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAI,MAAA,CAAA,CAAA,CAAA,CACxC,QAEC,MAAI,CAAA,UAAU,wBACb,SAACD,EAAA,KAAA,KAAA,CAAG,UAAU,uEACZ,SAAA,CAAAC,MAACuB,GAAI,MAAAC,EAAc,QAClB,KAAG,CAAA,UAAU,sCACZ,SAAAxB,MAACyC,GAAiB,CAAA,EACpB,QACC,KACC,CAAA,SAAAzC,EAAA,IAAC,IAAA,CACC,KAAK,uBACL,QAASuC,EACT,UAAU,qBACV,MAAO,CAAE,OAAQ,SAAU,EAE3B,SAAAvC,EAAA,IAAC,OAAA,CACC,UAAU,gDACV,mBAAiB,IAEjB,SAAAD,EAAA,KAAC,OAAK,CAAA,UAAU,gBACd,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAkB,qBAAA,QACnD,OAAK,CAAA,UAAU,kBAAkB,cAAY,OAAO,SAErD,oBAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EAEF,CAEJ,CAEAsC,EAAO,UAAY,CACjB,MAAOD,EAAU,QACfA,EAAU,MAAM,CACd,KAAMA,EAAU,OAAO,WACvB,KAAMA,EAAU,OAAO,UACxB,CAAA,CAAA,EACD,UACJ"}