const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestPosts() {
  try {
    console.log('Creating test blog posts...');

    // First, let's check if we have any users
    let user = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!user) {
      console.log('Creating admin user...');
      user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'ADMIN',
          password: 'hashed_password_here' // In real app, this would be properly hashed
        }
      });
    }

    // Create test blog posts
    const posts = [
      {
        slug: 'complete-guide-app-development-2024',
        published: true,
        publishedAt: new Date('2024-01-15'),
        featuredImage: '/uploads/blog-images/app-development.jpg',
        readTime: 8,
        viewCount: 1250,
        authorId: user.id,
        translations: [
          {
            language: 'en',
            title: 'Complete Guide to App Development in 2024',
            excerpt: 'Everything you need to know about modern app development, from planning to deployment.',
            content: `# Complete Guide to App Development in 2024

App development has evolved significantly in recent years. This comprehensive guide covers everything you need to know about building modern applications.

## Planning Your App

Before writing any code, proper planning is essential...

## Technology Stack

Choose the right technologies for your project...

## Development Process

Follow best practices for efficient development...`
          },
          {
            language: 'et',
            title: 'Täielik juhend rakenduste arendamiseks 2024',
            excerpt: 'Kõik, mida peate teadma kaasaegse rakenduste arendamise kohta, planeerimisest juurutamiseni.',
            content: `# Täielik juhend rakenduste arendamiseks 2024

Rakenduste arendamine on viimastel aastatel oluliselt arenenud...`
          }
        ]
      },
      {
        slug: 'software-development-company-vs-agency-2024',
        published: true,
        publishedAt: new Date('2024-02-10'),
        featuredImage: '/uploads/blog-images/company-vs-agency.jpg',
        readTime: 6,
        viewCount: 890,
        authorId: user.id,
        translations: [
          {
            language: 'en',
            title: 'Software Development Company vs Agency: What\'s the Difference?',
            excerpt: 'Understanding the key differences between software development companies and agencies to make the right choice.',
            content: `# Software Development Company vs Agency

When looking for software development services, you'll encounter both companies and agencies...`
          },
          {
            language: 'et',
            title: 'Tarkvaraarenduse ettevõte vs agentuur: Mis on erinevus?',
            excerpt: 'Tarkvaraarenduse ettevõtete ja agentuuri vaheliste peamiste erinevuste mõistmine õige valiku tegemiseks.',
            content: `# Tarkvaraarenduse ettevõte vs agentuur

Tarkvaraarenduse teenuseid otsides kohtate nii ettevõtteid kui ka agentuure...`
          }
        ]
      },
      {
        slug: 'mobile-app-development-business-guide-2024',
        published: true,
        publishedAt: new Date('2024-03-05'),
        featuredImage: '/uploads/blog-images/mobile-development.jpg',
        readTime: 10,
        viewCount: 1580,
        authorId: user.id,
        translations: [
          {
            language: 'en',
            title: 'Mobile App Development: Complete Business Guide 2024',
            excerpt: 'A comprehensive guide for businesses looking to develop mobile applications in 2024.',
            content: `# Mobile App Development: Complete Business Guide 2024

Mobile applications have become essential for modern businesses...`
          },
          {
            language: 'et',
            title: 'Mobiilirakenduste arendamine: Täielik ärijuhend 2024',
            excerpt: 'Põhjalik juhend ettevõtetele, kes soovivad arendada mobiilirakendusi 2024. aastal.',
            content: `# Mobiilirakenduste arendamine: Täielik ärijuhend 2024

Mobiilirakendused on saanud kaasaegsete ettevõtete jaoks hädavajalikuks...`
          }
        ]
      }
    ];

    for (const postData of posts) {
      const { translations, ...postFields } = postData;
      
      console.log(`Creating post: ${postFields.slug}`);
      
      const post = await prisma.blogPost.create({
        data: {
          ...postFields,
          translations: {
            create: translations
          }
        },
        include: {
          translations: true
        }
      });
      
      console.log(`Created post: ${post.slug} with ${post.translations.length} translations`);
    }

    console.log('✅ Test blog posts created successfully!');
  } catch (error) {
    console.error('Error creating test posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestPosts();
