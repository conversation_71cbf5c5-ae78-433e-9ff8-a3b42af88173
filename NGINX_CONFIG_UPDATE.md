# Nginx Configuration Update for Dynamic Sitemaps

## Add these rules to your existing nginx configuration:

```nginx
########################################################
# Frontend upstream (blue/green deployment)
########################################################
upstream frontend_upstream {
    server localhost:8082;  # blue
    server localhost:8083 backup;  # green (backup)
}

########################################################
# HTTPS server block for devskills.ee
########################################################
server {
    listen 443 ssl http2;
    server_name devskills.ee www.devskills.ee;

    # SSL certificates (managed by Certbot)
    ssl_certificate /etc/letsencrypt/live/devskills.ee/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/devskills.ee/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    ####################################################
    # Dynamic Sitemaps (NEW - ADD THIS SECTION)
    ####################################################
    location ~ ^/sitemap.*\.xml$ {
        proxy_pass http://localhost:4005;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache sitemaps for 1 hour
        add_header Cache-Control "public, max-age=3600";

        # CORS headers for sitemaps
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
    }

    ####################################################
    # Static Files (Uploads) - CRITICAL FOR IMAGE SERVING
    ####################################################
    location /uploads/ {
        proxy_pass http://localhost:4005;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache static files for 1 year
        add_header Cache-Control "public, max-age=31536000, immutable";

        # CORS headers for images
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
    }

    ####################################################
    # Backend API (Contact Form & Admin)
    ####################################################
    location /api/ {
        proxy_pass http://localhost:4005;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # CORS headers for API
        add_header Access-Control-Allow-Origin "https://devskills.ee" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, X-API-Key, Authorization" always;
    }

    ####################################################
    # Language-prefixed routes (NEW - ADD THIS SECTION)
    ####################################################
    location ~ ^/(en|et|fi|de|sv)/ {
        proxy_pass http://frontend_upstream;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache control for SPA
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    ####################################################
    # Frontend (React/Vite) App
    ####################################################
    location / {
        proxy_pass http://frontend_upstream;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Cache control for SPA
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}

########################################################
# HTTP -> HTTPS redirect
########################################################
server {
    listen 80;
    server_name devskills.ee www.devskills.ee;
    return 301 https://$host$request_uri;
}
```

## What this adds:

1. **Dynamic Sitemaps**: Routes `/sitemap*.xml` to backend (port 4005)
2. **Static Files (CRITICAL)**: Routes `/uploads/` to backend for image serving
3. **Language Routes**: Handles `/en/`, `/et/`, etc. properly
4. **Caching**: Sitemaps cached for 1 hour, static files for 1 year
5. **CORS**: Proper headers for sitemaps and images
6. **Enhanced API**: Support for all HTTP methods (PUT, DELETE for admin)

## After updating nginx:

1. Test the configuration: `sudo nginx -t`
2. Reload nginx: `sudo systemctl reload nginx`
3. Test sitemaps: `curl https://devskills.ee/sitemap.xml`

## Submit to Google Search Console:

- https://devskills.ee/sitemap.xml (main index)
- https://devskills.ee/sitemap-en.xml
- https://devskills.ee/sitemap-et.xml
- https://devskills.ee/sitemap-fi.xml
- https://devskills.ee/sitemap-de.xml
- https://devskills.ee/sitemap-sv.xml
