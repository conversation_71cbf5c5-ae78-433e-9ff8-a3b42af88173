var we=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function xe(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var Ae={exports:{}};(function(c){(function(e,o){c.exports?c.exports=o():e.Rellax=o()})(typeof window<"u"?window:we,function(){var e=function(o,i){var t=Object.create(e.prototype),m=0,v=0,f=0,g=0,u=[],T=!0,z=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||function(a){return setTimeout(a,1e3/60)},$=null,S=!1;try{var O=Object.defineProperty({},"passive",{get:function(){S=!0}});window.addEventListener("testPassive",null,O),window.removeEventListener("testPassive",null,O)}catch{}var C=window.cancelAnimationFrame||window.mozCancelAnimationFrame||clearTimeout,I=window.transformProp||function(){var a=document.createElement("div");if(a.style.transform===null){var s=["Webkit","Moz","ms"];for(var w in s)if(a.style[s[w]+"Transform"]!==void 0)return s[w]+"Transform"}return"transform"}();t.options={speed:-2,verticalSpeed:null,horizontalSpeed:null,breakpoints:[576,768,1201],center:!1,wrapper:null,relativeToWrapper:!1,round:!0,vertical:!0,horizontal:!1,verticalScrollAxis:"y",horizontalScrollAxis:"x",callback:function(){}},i&&Object.keys(i).forEach(function(a){t.options[a]=i[a]});function q(){if(t.options.breakpoints.length===3&&Array.isArray(t.options.breakpoints)){var a=!0,s=!0,w;if(t.options.breakpoints.forEach(function(k){typeof k!="number"&&(s=!1),w!==null&&k<w&&(a=!1),w=k}),a&&s)return}t.options.breakpoints=[576,768,1201],console.warn("Rellax: You must pass an array of 3 numbers in ascending order to the breakpoints option. Defaults reverted")}i&&i.breakpoints&&q(),o||(o=".rellax");var N=typeof o=="string"?document.querySelectorAll(o):[o];if(N.length>0)t.elems=N;else{console.warn("Rellax: The elements you're trying to select don't exist.");return}if(t.options.wrapper&&!t.options.wrapper.nodeType){var F=document.querySelector(t.options.wrapper);if(F)t.options.wrapper=F;else{console.warn("Rellax: The wrapper you're trying to use doesn't exist.");return}}var p,l=function(a){var s=t.options.breakpoints;return a<s[0]?"xs":a>=s[0]&&a<s[1]?"sm":a>=s[1]&&a<s[2]?"md":"lg"},n=function(){for(var a=0;a<t.elems.length;a++){var s=d(t.elems[a]);u.push(s)}},r=function(){for(var a=0;a<u.length;a++)t.elems[a].style.cssText=u[a].style;u=[],v=window.innerHeight,g=window.innerWidth,p=l(g),h(),n(),R(),T&&(window.addEventListener("resize",r),T=!1,V())},d=function(a){var s=a.getAttribute("data-rellax-percentage"),w=a.getAttribute("data-rellax-speed"),k=a.getAttribute("data-rellax-xs-speed"),M=a.getAttribute("data-rellax-mobile-speed"),P=a.getAttribute("data-rellax-tablet-speed"),j=a.getAttribute("data-rellax-desktop-speed"),H=a.getAttribute("data-rellax-vertical-speed"),Y=a.getAttribute("data-rellax-horizontal-speed"),_=a.getAttribute("data-rellax-vertical-scroll-axis"),x=a.getAttribute("data-rellax-horizontal-scroll-axis"),A=a.getAttribute("data-rellax-zindex")||0,U=a.getAttribute("data-rellax-min"),G=a.getAttribute("data-rellax-max"),Te=a.getAttribute("data-rellax-min-x"),Ie=a.getAttribute("data-rellax-max-x"),Oe=a.getAttribute("data-rellax-min-y"),Pe=a.getAttribute("data-rellax-max-y"),J,ne=!0;!k&&!M&&!P&&!j?ne=!1:J={xs:k,sm:M,md:P,lg:j};var ie=t.options.wrapper?t.options.wrapper.scrollTop:window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;if(t.options.relativeToWrapper){var ze=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;ie=ze-t.options.wrapper.offsetTop}var oe=t.options.vertical&&(s||t.options.center)?ie:0,ae=t.options.horizontal&&(s||t.options.center)?t.options.wrapper?t.options.wrapper.scrollLeft:window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft:0,re=oe+a.getBoundingClientRect().top,se=a.clientHeight||a.offsetHeight||a.scrollHeight,le=ae+a.getBoundingClientRect().left,ce=a.clientWidth||a.offsetWidth||a.scrollWidth,de=s||(oe-re+v)/(se+v),ue=s||(ae-le+g)/(ce+g);t.options.center&&(ue=.5,de=.5);var fe=ne&&J[p]!==null?Number(J[p]):w||t.options.speed,pe=H||t.options.verticalSpeed,me=Y||t.options.horizontalSpeed,Me=_||t.options.verticalScrollAxis,je=x||t.options.horizontalScrollAxis,ve=y(ue,de,fe,pe,me),Q=a.style.cssText,Z="",he=/transform\s*:/i.exec(Q);if(he){var He=he.index,K=Q.slice(He),ge=K.indexOf(";");ge?Z=" "+K.slice(11,ge).replace(/\s/g,""):Z=" "+K.slice(11).replace(/\s/g,"")}return{baseX:ve.x,baseY:ve.y,top:re,left:le,height:se,width:ce,speed:fe,verticalSpeed:pe,horizontalSpeed:me,verticalScrollAxis:Me,horizontalScrollAxis:je,style:Q,transform:Z,zindex:A,min:U,max:G,minX:Te,maxX:Ie,minY:Oe,maxY:Pe}},h=function(){var a=m,s=f;if(m=t.options.wrapper?t.options.wrapper.scrollTop:(document.documentElement||document.body.parentNode||document.body).scrollTop||window.pageYOffset,f=t.options.wrapper?t.options.wrapper.scrollLeft:(document.documentElement||document.body.parentNode||document.body).scrollLeft||window.pageXOffset,t.options.relativeToWrapper){var w=(document.documentElement||document.body.parentNode||document.body).scrollTop||window.pageYOffset;m=w-t.options.wrapper.offsetTop}return!!(a!=m&&t.options.vertical||s!=f&&t.options.horizontal)},y=function(a,s,w,k,M){var P={},j=(M||w)*(100*(1-a)),H=(k||w)*(100*(1-s));return P.x=t.options.round?Math.round(j):Math.round(j*100)/100,P.y=t.options.round?Math.round(H):Math.round(H*100)/100,P},b=function(){window.removeEventListener("resize",b),window.removeEventListener("orientationchange",b),(t.options.wrapper?t.options.wrapper:window).removeEventListener("scroll",b),(t.options.wrapper?t.options.wrapper:document).removeEventListener("touchmove",b),$=z(V)},V=function(){h()&&T===!1?(R(),$=z(V)):($=null,window.addEventListener("resize",b),window.addEventListener("orientationchange",b),(t.options.wrapper?t.options.wrapper:window).addEventListener("scroll",b,S?{passive:!0}:!1),(t.options.wrapper?t.options.wrapper:document).addEventListener("touchmove",b,S?{passive:!0}:!1))},R=function(){for(var a,s=0;s<t.elems.length;s++){var w=u[s].verticalScrollAxis.toLowerCase(),k=u[s].horizontalScrollAxis.toLowerCase(),M=w.indexOf("x")!=-1?m:0,P=w.indexOf("y")!=-1?m:0,j=k.indexOf("x")!=-1?f:0,H=k.indexOf("y")!=-1?f:0,Y=(P+H-u[s].top+v)/(u[s].height+v),_=(M+j-u[s].left+g)/(u[s].width+g);a=y(_,Y,u[s].speed,u[s].verticalSpeed,u[s].horizontalSpeed);var x=a.y-u[s].baseY,A=a.x-u[s].baseX;u[s].min!==null&&(t.options.vertical&&!t.options.horizontal&&(x=x<=u[s].min?u[s].min:x),t.options.horizontal&&!t.options.vertical&&(A=A<=u[s].min?u[s].min:A)),u[s].minY!=null&&(x=x<=u[s].minY?u[s].minY:x),u[s].minX!=null&&(A=A<=u[s].minX?u[s].minX:A),u[s].max!==null&&(t.options.vertical&&!t.options.horizontal&&(x=x>=u[s].max?u[s].max:x),t.options.horizontal&&!t.options.vertical&&(A=A>=u[s].max?u[s].max:A)),u[s].maxY!=null&&(x=x>=u[s].maxY?u[s].maxY:x),u[s].maxX!=null&&(A=A>=u[s].maxX?u[s].maxX:A);var U=u[s].zindex,G="translate3d("+(t.options.horizontal?A:"0")+"px,"+(t.options.vertical?x:"0")+"px,"+U+"px) "+u[s].transform;t.elems[s].style[I]=G}t.options.callback(a)};return t.destroy=function(){for(var a=0;a<t.elems.length;a++)t.elems[a].style.cssText=u[a].style;T||(window.removeEventListener("resize",r),T=!0),C($),$=null},r(),t.refresh=r,t};return e})})(Ae);var Le=Ae.exports;const Ge=xe(Le);var ee={exports:{}};(function(c,e){(function(o,i){i(c,e)})(we,function(o,i){Object.defineProperty(i,"__esModule",{value:!0});var t,m;function v(p,l){if(!(p instanceof l))throw new TypeError("Cannot call a class as a function")}var f=function(){function p(l,n){for(var r=0;r<n.length;r++){var d=n[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(l,d.key,d)}}return function(l,n,r){return n&&p(l.prototype,n),r&&p(l,r),l}}();function g(p,l){return l.indexOf(p)>=0}function u(p,l){for(var n in l)if(p[n]==null){var r=l[n];p[n]=r}return p}function T(p){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(p)}function z(p){var l=arguments.length<=1||arguments[1]===void 0?!1:arguments[1],n=arguments.length<=2||arguments[2]===void 0?!1:arguments[2],r=arguments.length<=3||arguments[3]===void 0?null:arguments[3],d=void 0;return document.createEvent!=null?(d=document.createEvent("CustomEvent"),d.initCustomEvent(p,l,n,r)):document.createEventObject!=null?(d=document.createEventObject(),d.eventType=p):d.eventName=p,d}function $(p,l){p.dispatchEvent!=null?p.dispatchEvent(l):l in(p!=null)?p[l]():"on"+l in(p!=null)&&p["on"+l]()}function S(p,l,n){p.addEventListener!=null?p.addEventListener(l,n,!1):p.attachEvent!=null?p.attachEvent("on"+l,n):p[l]=n}function O(p,l,n){p.removeEventListener!=null?p.removeEventListener(l,n,!1):p.detachEvent!=null?p.detachEvent("on"+l,n):delete p[l]}function C(){return"innerHeight"in window?window.innerHeight:document.documentElement.clientHeight}var I=window.WeakMap||window.MozWeakMap||function(){function p(){v(this,p),this.keys=[],this.values=[]}return f(p,[{key:"get",value:function(n){for(var r=0;r<this.keys.length;r++){var d=this.keys[r];if(d===n)return this.values[r]}}},{key:"set",value:function(n,r){for(var d=0;d<this.keys.length;d++){var h=this.keys[d];if(h===n)return this.values[d]=r,this}return this.keys.push(n),this.values.push(r),this}}]),p}(),q=window.MutationObserver||window.WebkitMutationObserver||window.MozMutationObserver||(m=t=function(){function p(){v(this,p),typeof console<"u"&&console!==null&&(console.warn("MutationObserver is not supported by your browser."),console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content."))}return f(p,[{key:"observe",value:function(){}}]),p}(),t.notSupported=!0,m),N=window.getComputedStyle||function(l){var n=/(\-([a-z]){1})/g;return{getPropertyValue:function(d){d==="float"&&(d="styleFloat"),n.test(d)&&d.replace(n,function(y,b){return b.toUpperCase()});var h=l.currentStyle;return(h!=null?h[d]:void 0)||null}}},F=function(){function p(){var l=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];v(this,p),this.defaults={boxClass:"wow",animateClass:"animated",offset:0,mobile:!0,live:!0,callback:null,scrollContainer:null},this.animate=function(){return"requestAnimationFrame"in window?function(r){return window.requestAnimationFrame(r)}:function(r){return r()}}(),this.vendors=["moz","webkit"],this.start=this.start.bind(this),this.resetAnimation=this.resetAnimation.bind(this),this.scrollHandler=this.scrollHandler.bind(this),this.scrollCallback=this.scrollCallback.bind(this),this.scrolled=!0,this.config=u(l,this.defaults),l.scrollContainer!=null&&(this.config.scrollContainer=document.querySelector(l.scrollContainer)),this.animationNameCache=new I,this.wowEvent=z(this.config.boxClass)}return f(p,[{key:"init",value:function(){this.element=window.document.documentElement,g(document.readyState,["interactive","complete"])?this.start():S(document,"DOMContentLoaded",this.start),this.finished=[]}},{key:"start",value:function(){var n=this;if(this.stopped=!1,this.boxes=[].slice.call(this.element.querySelectorAll("."+this.config.boxClass)),this.all=this.boxes.slice(0),this.boxes.length)if(this.disabled())this.resetStyle();else for(var r=0;r<this.boxes.length;r++){var d=this.boxes[r];this.applyStyle(d,!0)}if(this.disabled()||(S(this.config.scrollContainer||window,"scroll",this.scrollHandler),S(window,"resize",this.scrollHandler),this.interval=setInterval(this.scrollCallback,50)),this.config.live){var h=new q(function(y){for(var b=0;b<y.length;b++)for(var V=y[b],R=0;R<V.addedNodes.length;R++){var a=V.addedNodes[R];n.doSync(a)}});h.observe(document.body,{childList:!0,subtree:!0})}}},{key:"stop",value:function(){this.stopped=!0,O(this.config.scrollContainer||window,"scroll",this.scrollHandler),O(window,"resize",this.scrollHandler),this.interval!=null&&clearInterval(this.interval)}},{key:"sync",value:function(){q.notSupported&&this.doSync(this.element)}},{key:"doSync",value:function(n){if((typeof n>"u"||n===null)&&(n=this.element),n.nodeType===1){n=n.parentNode||n;for(var r=n.querySelectorAll("."+this.config.boxClass),d=0;d<r.length;d++){var h=r[d];g(h,this.all)||(this.boxes.push(h),this.all.push(h),this.stopped||this.disabled()?this.resetStyle():this.applyStyle(h,!0),this.scrolled=!0)}}}},{key:"show",value:function(n){return this.applyStyle(n),n.className=n.className+" "+this.config.animateClass,this.config.callback!=null&&this.config.callback(n),$(n,this.wowEvent),S(n,"animationend",this.resetAnimation),S(n,"oanimationend",this.resetAnimation),S(n,"webkitAnimationEnd",this.resetAnimation),S(n,"MSAnimationEnd",this.resetAnimation),n}},{key:"applyStyle",value:function(n,r){var d=this,h=n.getAttribute("data-wow-duration"),y=n.getAttribute("data-wow-delay"),b=n.getAttribute("data-wow-iteration");return this.animate(function(){return d.customStyle(n,r,h,y,b)})}},{key:"resetStyle",value:function(){for(var n=0;n<this.boxes.length;n++){var r=this.boxes[n];r.style.visibility="visible"}}},{key:"resetAnimation",value:function(n){if(n.type.toLowerCase().indexOf("animationend")>=0){var r=n.target||n.srcElement;r.className=r.className.replace(this.config.animateClass,"").trim()}}},{key:"customStyle",value:function(n,r,d,h,y){return r&&this.cacheAnimationName(n),n.style.visibility=r?"hidden":"visible",d&&this.vendorSet(n.style,{animationDuration:d}),h&&this.vendorSet(n.style,{animationDelay:h}),y&&this.vendorSet(n.style,{animationIterationCount:y}),this.vendorSet(n.style,{animationName:r?"none":this.cachedAnimationName(n)}),n}},{key:"vendorSet",value:function(n,r){for(var d in r)if(r.hasOwnProperty(d)){var h=r[d];n[""+d]=h;for(var y=0;y<this.vendors.length;y++){var b=this.vendors[y];n[""+b+d.charAt(0).toUpperCase()+d.substr(1)]=h}}}},{key:"vendorCSS",value:function(n,r){for(var d=N(n),h=d.getPropertyCSSValue(r),y=0;y<this.vendors.length;y++){var b=this.vendors[y];h=h||d.getPropertyCSSValue("-"+b+"-"+r)}return h}},{key:"animationName",value:function(n){var r=void 0;try{r=this.vendorCSS(n,"animation-name").cssText}catch{r=N(n).getPropertyValue("animation-name")}return r==="none"?"":r}},{key:"cacheAnimationName",value:function(n){return this.animationNameCache.set(n,this.animationName(n))}},{key:"cachedAnimationName",value:function(n){return this.animationNameCache.get(n)}},{key:"scrollHandler",value:function(){this.scrolled=!0}},{key:"scrollCallback",value:function(){if(this.scrolled){this.scrolled=!1;for(var n=[],r=0;r<this.boxes.length;r++){var d=this.boxes[r];if(d){if(this.isVisible(d)){this.show(d);continue}n.push(d)}}this.boxes=n,!this.boxes.length&&!this.config.live&&this.stop()}}},{key:"offsetTop",value:function(n){for(;n.offsetTop===void 0;)n=n.parentNode;for(var r=n.offsetTop;n.offsetParent;)n=n.offsetParent,r+=n.offsetTop;return r}},{key:"isVisible",value:function(n){var r=n.getAttribute("data-wow-offset")||this.config.offset,d=this.config.scrollContainer&&this.config.scrollContainer.scrollTop||window.pageYOffset,h=d+Math.min(this.element.clientHeight,C())-r,y=this.offsetTop(n),b=y+n.clientHeight;return y<=h&&b>=d}},{key:"disabled",value:function(){return!this.config.mobile&&T(navigator.userAgent)}}]),p}();i.default=F,o.exports=i.default})})(ee,ee.exports);var Ve=ee.exports;const Je=xe(Ve);/*!
 * Jarallax v2.2.1 (https://github.com/nk-o/jarallax)
 * Copyright 2024 nK <https://nkdev.info>
 * Licensed under MIT (https://github.com/nk-o/jarallax/blob/master/LICENSE)
 */var Re={type:"scroll",speed:.5,containerClass:"jarallax-container",imgSrc:null,imgElement:".jarallax-img",imgSize:"cover",imgPosition:"50% 50%",imgRepeat:"no-repeat",keepImg:!1,elementInViewport:null,zIndex:-100,disableParallax:!1,onScroll:null,onInit:null,onDestroy:null,onCoverImage:null,videoClass:"jarallax-video",videoSrc:null,videoStartTime:0,videoEndTime:0,videoVolume:0,videoLoop:!0,videoPlayOnlyVisible:!0,videoLazyLoading:!0,disableVideo:!1,onVideoInsert:null,onVideoWorkerInit:null};let D;typeof window<"u"?D=window:typeof global<"u"?D=global:typeof self<"u"?D=self:D={};var E=D;function We(c,e){return typeof e=="string"?E.getComputedStyle(c).getPropertyValue(e):(Object.keys(e).forEach(o=>{c.style[o]=e[o]}),c)}function Ne(c,...e){return c=c||{},Object.keys(e).forEach(o=>{e[o]&&Object.keys(e[o]).forEach(i=>{c[i]=e[o][i]})}),c}function De(c){const e=[];for(;c.parentElement!==null;)c=c.parentElement,c.nodeType===1&&e.push(c);return e}function Be(c){document.readyState==="complete"||document.readyState==="interactive"?c():document.addEventListener("DOMContentLoaded",c,{capture:!0,once:!0,passive:!0})}const{navigator:qe}=E,Fe=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(qe.userAgent);function Ye(){return Fe}let Se,te,W;function _e(){return!W&&document.body&&(W=document.createElement("div"),W.style.cssText="position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;",document.body.appendChild(W)),(W?W.clientHeight:0)||E.innerHeight||document.documentElement.clientHeight}function B(){Se=E.innerWidth||document.documentElement.clientWidth,Ye()?te=_e():te=E.innerHeight||document.documentElement.clientHeight}B();E.addEventListener("resize",B);E.addEventListener("orientationchange",B);E.addEventListener("load",B);Be(()=>{B()});function X(){return{width:Se,height:te}}const L=[];function Ee(){if(!L.length)return;const{width:c,height:e}=X();L.forEach((o,i)=>{const{instance:t,oldData:m}=o;if(!t.isVisible())return;const v=t.$item.getBoundingClientRect(),f={width:v.width,height:v.height,top:v.top,bottom:v.bottom,wndW:c,wndH:e},g=!m||m.wndW!==f.wndW||m.wndH!==f.wndH||m.width!==f.width||m.height!==f.height,u=g||!m||m.top!==f.top||m.bottom!==f.bottom;L[i].oldData=f,g&&t.onResize(),u&&t.onScroll()}),E.requestAnimationFrame(Ee)}const ke=new E.IntersectionObserver(c=>{c.forEach(e=>{e.target.jarallax.isElementInViewport=e.isIntersecting})},{rootMargin:"50px"});function Xe(c){L.push({instance:c}),L.length===1&&E.requestAnimationFrame(Ee),ke.observe(c.options.elementInViewport||c.$item)}function Ue(c){L.forEach((e,o)=>{e.instance.instanceID===c.instanceID&&L.splice(o,1)}),ke.unobserve(c.options.elementInViewport||c.$item)}const{navigator:ye}=E;let be=0;class $e{constructor(e,o){const i=this;i.instanceID=be,be+=1,i.$item=e,i.defaults={...Re};const t=i.$item.dataset||{},m={};if(Object.keys(t).forEach(f=>{const g=f.substr(0,1).toLowerCase()+f.substr(1);g&&typeof i.defaults[g]<"u"&&(m[g]=t[f])}),i.options=i.extend({},i.defaults,m,o),i.pureOptions=i.extend({},i.options),Object.keys(i.options).forEach(f=>{i.options[f]==="true"?i.options[f]=!0:i.options[f]==="false"&&(i.options[f]=!1)}),i.options.speed=Math.min(2,Math.max(-1,parseFloat(i.options.speed))),typeof i.options.disableParallax=="string"&&(i.options.disableParallax=new RegExp(i.options.disableParallax)),i.options.disableParallax instanceof RegExp){const f=i.options.disableParallax;i.options.disableParallax=()=>f.test(ye.userAgent)}if(typeof i.options.disableParallax!="function"){const f=i.options.disableParallax;i.options.disableParallax=()=>f===!0}if(typeof i.options.disableVideo=="string"&&(i.options.disableVideo=new RegExp(i.options.disableVideo)),i.options.disableVideo instanceof RegExp){const f=i.options.disableVideo;i.options.disableVideo=()=>f.test(ye.userAgent)}if(typeof i.options.disableVideo!="function"){const f=i.options.disableVideo;i.options.disableVideo=()=>f===!0}let v=i.options.elementInViewport;v&&typeof v=="object"&&typeof v.length<"u"&&([v]=v),v instanceof Element||(v=null),i.options.elementInViewport=v,i.image={src:i.options.imgSrc||null,$container:null,useImgTag:!1,position:"fixed"},i.initImg()&&i.canInitParallax()&&i.init()}css(e,o){return We(e,o)}extend(e,...o){return Ne(e,...o)}getWindowData(){const{width:e,height:o}=X();return{width:e,height:o,y:document.documentElement.scrollTop}}initImg(){const e=this;let o=e.options.imgElement;return o&&typeof o=="string"&&(o=e.$item.querySelector(o)),o instanceof Element||(e.options.imgSrc?(o=new Image,o.src=e.options.imgSrc):o=null),o&&(e.options.keepImg?e.image.$item=o.cloneNode(!0):(e.image.$item=o,e.image.$itemParent=o.parentNode),e.image.useImgTag=!0),e.image.$item?!0:(e.image.src===null&&(e.image.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",e.image.bgImage=e.css(e.$item,"background-image")),!(!e.image.bgImage||e.image.bgImage==="none"))}canInitParallax(){return!this.options.disableParallax()}init(){const e=this,o={position:"absolute",top:0,left:0,width:"100%",height:"100%",overflow:"hidden"};let i={pointerEvents:"none",transformStyle:"preserve-3d",backfaceVisibility:"hidden"};if(!e.options.keepImg){const t=e.$item.getAttribute("style");if(t&&e.$item.setAttribute("data-jarallax-original-styles",t),e.image.useImgTag){const m=e.image.$item.getAttribute("style");m&&e.image.$item.setAttribute("data-jarallax-original-styles",m)}}if(e.css(e.$item,"position")==="static"&&e.css(e.$item,{position:"relative"}),e.css(e.$item,"z-index")==="auto"&&e.css(e.$item,{zIndex:0}),e.image.$container=document.createElement("div"),e.css(e.image.$container,o),e.css(e.image.$container,{"z-index":e.options.zIndex}),this.image.position==="fixed"&&e.css(e.image.$container,{"-webkit-clip-path":"polygon(0 0, 100% 0, 100% 100%, 0 100%)","clip-path":"polygon(0 0, 100% 0, 100% 100%, 0 100%)"}),e.image.$container.setAttribute("id",`jarallax-container-${e.instanceID}`),e.options.containerClass&&e.image.$container.setAttribute("class",e.options.containerClass),e.$item.appendChild(e.image.$container),e.image.useImgTag?i=e.extend({"object-fit":e.options.imgSize,"object-position":e.options.imgPosition,"max-width":"none"},o,i):(e.image.$item=document.createElement("div"),e.image.src&&(i=e.extend({"background-position":e.options.imgPosition,"background-size":e.options.imgSize,"background-repeat":e.options.imgRepeat,"background-image":e.image.bgImage||`url("${e.image.src}")`},o,i))),(e.options.type==="opacity"||e.options.type==="scale"||e.options.type==="scale-opacity"||e.options.speed===1)&&(e.image.position="absolute"),e.image.position==="fixed"){const t=De(e.$item).filter(m=>{const v=E.getComputedStyle(m),f=v["-webkit-transform"]||v["-moz-transform"]||v.transform;return f&&f!=="none"||/(auto|scroll)/.test(v.overflow+v["overflow-y"]+v["overflow-x"])});e.image.position=t.length?"absolute":"fixed"}i.position=e.image.position,e.css(e.image.$item,i),e.image.$container.appendChild(e.image.$item),e.onResize(),e.onScroll(!0),e.options.onInit&&e.options.onInit.call(e),e.css(e.$item,"background-image")!=="none"&&e.css(e.$item,{"background-image":"none"}),Xe(e)}destroy(){const e=this;Ue(e);const o=e.$item.getAttribute("data-jarallax-original-styles");if(e.$item.removeAttribute("data-jarallax-original-styles"),o?e.$item.setAttribute("style",o):e.$item.removeAttribute("style"),e.image.useImgTag){const i=e.image.$item.getAttribute("data-jarallax-original-styles");e.image.$item.removeAttribute("data-jarallax-original-styles"),i?e.image.$item.setAttribute("style",o):e.image.$item.removeAttribute("style"),e.image.$itemParent&&e.image.$itemParent.appendChild(e.image.$item)}e.image.$container&&e.image.$container.parentNode.removeChild(e.image.$container),e.options.onDestroy&&e.options.onDestroy.call(e),delete e.$item.jarallax}coverImage(){const e=this,{height:o}=X(),i=e.image.$container.getBoundingClientRect(),t=i.height,{speed:m}=e.options,v=e.options.type==="scroll"||e.options.type==="scroll-opacity";let f=0,g=t,u=0;return v&&(m<0?(f=m*Math.max(t,o),o<t&&(f-=m*(t-o))):f=m*(t+o),m>1?g=Math.abs(f-o):m<0?g=f/m+Math.abs(f):g+=(o-t)*(1-m),f/=2),e.parallaxScrollDistance=f,v?u=(o-g)/2:u=(t-g)/2,e.css(e.image.$item,{height:`${g}px`,marginTop:`${u}px`,left:e.image.position==="fixed"?`${i.left}px`:"0",width:`${i.width}px`}),e.options.onCoverImage&&e.options.onCoverImage.call(e),{image:{height:g,marginTop:u},container:i}}isVisible(){return this.isElementInViewport||!1}onScroll(e){const o=this;if(!e&&!o.isVisible())return;const{height:i}=X(),t=o.$item.getBoundingClientRect(),m=t.top,v=t.height,f={},g=Math.max(0,m),u=Math.max(0,v+m),T=Math.max(0,-m),z=Math.max(0,m+v-i),$=Math.max(0,v-(m+v-i)),S=Math.max(0,-m+i-v),O=1-2*((i-m)/(i+v));let C=1;if(v<i?C=1-(T||z)/v:u<=i?C=u/i:$<=i&&(C=$/i),(o.options.type==="opacity"||o.options.type==="scale-opacity"||o.options.type==="scroll-opacity")&&(f.transform="translate3d(0,0,0)",f.opacity=C),o.options.type==="scale"||o.options.type==="scale-opacity"){let I=1;o.options.speed<0?I-=o.options.speed*C:I+=o.options.speed*(1-C),f.transform=`scale(${I}) translate3d(0,0,0)`}if(o.options.type==="scroll"||o.options.type==="scroll-opacity"){let I=o.parallaxScrollDistance*O;o.image.position==="absolute"&&(I-=m),f.transform=`translate3d(0,${I}px,0)`}o.css(o.image.$item,f),o.options.onScroll&&o.options.onScroll.call(o,{section:t,beforeTop:g,beforeTopEnd:u,afterTop:T,beforeBottom:z,beforeBottomEnd:$,afterBottom:S,visiblePercent:C,fromViewportCenter:O})}onResize(){this.coverImage()}}const Ce=function(c,e,...o){(typeof HTMLElement=="object"?c instanceof HTMLElement:c&&typeof c=="object"&&c!==null&&c.nodeType===1&&typeof c.nodeName=="string")&&(c=[c]);const i=c.length;let t=0,m;for(t;t<i;t+=1)if(typeof e=="object"||typeof e>"u"?c[t].jarallax||(c[t].jarallax=new $e(c[t],e)):c[t].jarallax&&(m=c[t].jarallax[e].apply(c[t].jarallax,o)),typeof m<"u")return m;return c};Ce.constructor=$e;const Qe=Ce;export{Ge as R,Je as W,we as c,xe as g,Qe as j};
//# sourceMappingURL=vendor-animations-Dl3DQHMd.js.map
