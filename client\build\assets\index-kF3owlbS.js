const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/critical-css-XHLTH4Oj.css","assets/pages-static-BIE16m2Q.js","assets/vendor-react-EBZQFYZ5.js","assets/vendor-misc-j6k8kvFA.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css","assets/vendor-gallery-BKyWYjF6.js","assets/vendor-admin-DvrlCxcB.js","assets/components-layout-DGce1jMY.js","assets/components-common-DDbdC8oB.js","assets/vendor-i18n-DxzbetI3.js","assets/admin-routes-D7V7jRaZ.js","assets/components-home-B-IXSbjU.js","assets/vendor-utils-t--hEgTQ.js","assets/components-layout-LOVgDiEq.css","assets/pages-other-BrCzhuD6.js","assets/pages-static-D4YCAEbJ.css","assets/vendor-ui-DQIoTyJ0.js"])))=>i.map(i=>d[i]);
import{r,c as E,j as t,R as j,a,b,e as P,B as S,l as h}from"./vendor-react-EBZQFYZ5.js";import{_ as l}from"./admin-routes-D7V7jRaZ.js";/* empty css                     */import{x as d,y as A,G as R,E as T}from"./components-common-DDbdC8oB.js";import{R as v,W as w}from"./vendor-animations-Dl3DQHMd.js";import{c as D}from"./components-home-B-IXSbjU.js";import{H as C}from"./pages-other-BrCzhuD6.js";import"./vendor-misc-j6k8kvFA.js";import"./vendor-gallery-BKyWYjF6.js";import"./vendor-admin-DvrlCxcB.js";import"./vendor-i18n-DxzbetI3.js";import"./vendor-utils-t--hEgTQ.js";import"./components-layout-DGce1jMY.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const c of i)if(c.type==="childList")for(const m of c.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&n(m)}).observe(document,{childList:!0,subtree:!0});function e(i){const c={};return i.integrity&&(c.integrity=i.integrity),i.referrerPolicy&&(c.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?c.credentials="include":i.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function n(i){if(i.ep)return;i.ep=!0;const c=e(i);fetch(i.href,c)}})();let _=!1;function O(s,o=null){return new Promise((e,n)=>{if(o&&document.getElementById(o)){e();return}const i=document.createElement("link");i.rel="stylesheet",i.href=s,o&&(i.id=o),i.onload=()=>e(),i.onerror=()=>n(new Error(`Failed to load CSS: ${s}`)),document.head.appendChild(i)})}function y(){if(_)return Promise.resolve();_=!0;const o=["/assets/css/bootstrap.min.css","/assets/css/style.css","/assets/css/vertical-rhythm.min.css","/assets/css/magnific-popup.css","/assets/css/owl.carousel.css","/assets/css/splitting.css","/assets/css/YTPlayer.css","/assets/css/demo-main/demo-main.css","/assets/css/demo-elegant/demo-elegant.css","/assets/css/custom.css","/assets/css/style-responsive.css"].map((e,n)=>O(e,`non-critical-${n}`));return Promise.all(o).then(()=>(console.log("Non-critical CSS loaded"),l(()=>Promise.resolve({}),__vite__mapDeps([0])))).catch(e=>{console.error("Failed to load non-critical CSS:",e)})}function k(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{setTimeout(y,100)}):setTimeout(y,100),window.addEventListener("load",y)}const I=()=>{document.querySelectorAll(".parallax-mousemove-scene").forEach(s=>{s.addEventListener("mousemove",function(o){const e=window.innerWidth,n=window.innerHeight,i=.5-(o.pageX-this.offsetLeft)/e,c=.5-(o.pageY-this.offsetTop)/n;this.querySelectorAll(".parallax-mousemove").forEach(u=>{const x=parseInt(u.getAttribute("data-offset")),L=`translate3d(${Math.round(i*x)}px, ${Math.round(c*x)}px, 0px)`;u.style.transform=L});let m=o.pageX-this.offsetLeft;o.pageY-this.offsetTop,this.querySelectorAll(".parallax-mousemove-follow").forEach(u=>{u.style.left=`${m}px`,u.style.top="31px"})}),s.addEventListener("mouseenter",function(o){this.querySelectorAll(".parallax-mousemove-follow").forEach(e=>{setTimeout(()=>{e.style.transition="all .27s var(--ease-out-short)",e.style.willChange="transform"},27)})}),s.addEventListener("mouseout",function(o){this.querySelectorAll(".parallax-mousemove-follow").forEach(e=>{e.style.transition="none"})})})};function q(){if(document.querySelectorAll("[data-rellax-y]").length&&window.innerWidth>=1280){let o=function(){document.querySelectorAll("[data-rellax-y]").forEach(e=>{e.getBoundingClientRect().top<window.innerHeight&&e.getBoundingClientRect().bottom>0?e.classList.contains("js-in-viewport")||(e.classList.add("js-in-viewport"),s.refresh()):e.classList.contains("js-in-viewport")&&e.classList.remove("js-in-viewport")})};const s=new v("[data-rellax-y]",{vertical:!0,horizontal:!1});window.addEventListener("scroll",o)}if(document.querySelectorAll("[data-rellax-x]").length&&window.innerWidth>=1280){let o=function(){document.querySelectorAll("[data-rellax-x]").forEach(e=>{e.getBoundingClientRect().top<window.innerHeight&&e.getBoundingClientRect().bottom>0?e.classList.contains("js-in-viewport")||(e.classList.add("js-in-viewport"),s.refresh()):e.classList.contains("js-in-viewport")&&e.classList.remove("js-in-viewport")})};const s=new v("[data-rellax-x]",{horizontal:!0});window.addEventListener("scroll",o)}}function z(){setTimeout(()=>{try{document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow").forEach(e=>e.classList.add("no-animate"));var s=new w({boxClass:"wow",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(e){e.classList.add("animated"),e.style.opacity="1",e.style.visibility="visible"}});document.body.classList.contains("appear-animate")||document.body.classList.add("appear-animate"),s.init(),setTimeout(()=>{document.querySelectorAll(".wow").forEach(e=>{e.classList.contains("animated")||(e.style.opacity="1",e.style.visibility="visible",e.classList.add("animated"))})},2e3),document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow-p").forEach(e=>e.classList.add("no-animate"));var o=new w({boxClass:"wow-p",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(e){e.classList.add("animated"),e.style.opacity="1",e.style.visibility="visible"}});document.body.classList.contains("appear-animate")?o.init():document.querySelectorAll(".wow-p").forEach(e=>e.style.opacity="1"),document.body.classList.contains("appear-animate")&&window.innerWidth>=1024&&document.documentElement.classList.contains("no-mobile")?document.querySelectorAll(".wow-menubar").forEach(e=>{e.classList.add("no-animate","fadeInDown","animated"),setInterval(()=>{e.classList.remove("no-animate")},1500)}):document.querySelectorAll(".wow-menubar").forEach(e=>e.style.opacity="1")}catch(e){console.error("Error initializing WOW.js:",e),document.querySelectorAll(".wow, .wow-p, .wow-menubar").forEach(n=>{n.style.opacity="1",n.classList.add("animated")})}},100)}const f=()=>{var s=document.querySelector(".main-nav"),o=document.querySelector(".nav-logo-wrap .logo"),e=document.querySelector(".light-after-scroll");if(!s)return;const n=s.classList.contains("dark-mode")||window.location.pathname==="/"||window.location.pathname.match(/^\/[a-z]{2}\/?$/);window.scrollY>0?(s.classList.remove("transparent"),s.classList.add("small-height","body-scrolled"),o&&o.classList.add("small-height"),e&&!n&&e.classList.remove("dark"),n&&!s.classList.contains("dark")&&s.classList.add("dark")):window.scrollY===0&&(s.classList.add("transparent"),s.classList.remove("small-height","body-scrolled"),o&&o.classList.remove("small-height"),e&&!n&&e.classList.add("dark"),n&&!s.classList.contains("dark")&&s.classList.add("dark"))},V=r.lazy(()=>l(()=>import("./pages-static-BIE16m2Q.js").then(s=>s.p),__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]))),B=r.lazy(()=>l(()=>import("./pages-static-BIE16m2Q.js").then(s=>s.a),__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]))),W=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.p),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),M=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.a),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),N=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.b),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),F=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.c),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),$=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.d),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),H=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.e),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),Y=r.lazy(()=>l(()=>import("./pages-static-BIE16m2Q.js").then(s=>s.b),__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]))),X=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.f),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),G=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.h),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),K=r.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(s=>s.i),__vite__mapDeps([15,2,3,4,5,6,7,8,9,10,11,12,13,14]))),J=r.lazy(()=>l(()=>import("./admin-routes-D7V7jRaZ.js").then(s=>s.A),__vite__mapDeps([11,2,3,4,5,6,7]))),Q=()=>t.jsxs("div",{className:"page-loader",style:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:"#1a1a1a",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:[t.jsx("div",{className:"loader",style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #fff",borderRadius:"50%",animation:"spin 1s linear infinite"}}),t.jsx("style",{children:`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `})]});function U(){const{pathname:s}=E();return r.useEffect(()=>{k(),z(),I();const o=()=>{var n;var e=document.querySelector(".main-nav");if(!e){setTimeout(o,100);return}e.style.display="",e!=null&&e.classList.contains("transparent")?e.classList.add("js-transparent"):(n=e==null?void 0:e.classList)!=null&&n.contains("dark")||e==null||e.classList.add("js-no-transparent-white"),f()};return o(),setTimeout(o,50),window.addEventListener("scroll",f),q(),setTimeout(()=>{const e=document.title||"DevSkills",n=window.location.href;D(e,n)},100),()=>{window.removeEventListener("scroll",f)}},[s]),r.useEffect(()=>{(()=>{const e=document.querySelector(".main-nav");e&&(e.style.display="",e.style.visibility="visible",e.style.opacity="1",setTimeout(()=>{f()},100))})()},[s]),r.useEffect(()=>{typeof window<"u"&&l(()=>import("./vendor-ui-DQIoTyJ0.js"),__vite__mapDeps([17,3,4,5])).then(()=>{console.log("Bootstrap loaded")})},[]),t.jsxs(t.Fragment,{children:[t.jsx(r.Suspense,{fallback:t.jsx(Q,{}),children:t.jsxs(j,{children:[t.jsxs(a,{path:"/:lang",children:[t.jsx(a,{index:!0,element:t.jsx(C,{})}),t.jsx(a,{path:"about",element:t.jsx(V,{})}),t.jsx(a,{path:"services",element:t.jsx(B,{})}),t.jsx(a,{path:"portfolio",element:t.jsx(M,{})}),t.jsx(a,{path:"portfolio-single/:id",element:t.jsx(F,{})}),t.jsx(a,{path:"webstore",element:t.jsx(W,{})}),t.jsx(a,{path:"webstore-single/:id",element:t.jsx($,{})}),t.jsx(a,{path:"blog",element:t.jsx(N,{})}),t.jsx(a,{path:"blog-single/:id",element:t.jsx(H,{})}),t.jsx(a,{path:"contact",element:t.jsx(Y,{})}),t.jsx(a,{path:"privacy-policy",element:t.jsx(X,{})}),t.jsx(a,{path:"terms-conditions",element:t.jsx(G,{})})]}),t.jsx(a,{path:"/admin/*",element:t.jsx(J,{})}),t.jsx(a,{path:"/",element:t.jsx(d,{})}),t.jsx(a,{path:"/about",element:t.jsx(d,{})}),t.jsx(a,{path:"/services",element:t.jsx(d,{})}),t.jsx(a,{path:"/portfolio",element:t.jsx(d,{})}),t.jsx(a,{path:"/portfolio-single/*",element:t.jsx(d,{})}),t.jsx(a,{path:"/blog",element:t.jsx(d,{})}),t.jsx(a,{path:"/blog-single/*",element:t.jsx(d,{})}),t.jsx(a,{path:"/contact",element:t.jsx(d,{})}),t.jsx(a,{path:"/privacy-policy",element:t.jsx(d,{})}),t.jsx(a,{path:"/terms-conditions",element:t.jsx(d,{})}),t.jsx(a,{path:"*",element:t.jsx(K,{})})]})}),t.jsx(A,{}),t.jsx(R,{})]})}const p=document.getElementById("root"),g=t.jsx(b.StrictMode,{children:t.jsx(T,{children:t.jsx(P,{children:t.jsx(S,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:t.jsx(U,{})})})})}),Z=p&&p.hasChildNodes();try{Z?h.hydrateRoot(p,g):h.createRoot(p).render(g)}catch(s){console.error("Error rendering app:",s),h.createRoot(p).render(g)}
//# sourceMappingURL=index-kF3owlbS.js.map
