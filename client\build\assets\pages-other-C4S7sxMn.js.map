{"version": 3, "mappings": ";igDAAaA,EAAY,CACvB,CAAE,KAAM,IAAK,KAAM,MAAQ,EAC3B,CAAE,KAAM,SAAU,KAAM,OAAS,EACjC,CAAE,KAAM,YAAa,KAAM,UAAY,EACvC,CAAE,KAAM,YAAa,KAAM,UAAY,EAEvC,CAAE,KAAM,QAAS,KAAM,MAAQ,EAC/B,CAAE,KAAM,WAAY,KAAM,SAAW,CACvC,ECAaC,EAAgB,CAC3B,KAAM,YACN,SAAU,eACV,cAAe,+BACf,YACE,kEACF,IAAK,uBACL,QAAS,CACP,cAAe,gBACf,gBAAiB,UACjB,WAAY,QACZ,eAAgB,IACjB,EACD,IAAK,CACH,SAAU,OACV,UAAW,OACZ,EACD,aAAc,CACZ,UAAW,iBACX,YAAa,mBACb,kBAAmB,CAAC,UAAW,WAAY,UAAW,SAAU,SAAS,CAC1E,EACD,aAAc,CACZ,UAAW,CAAC,SAAU,UAAW,YAAa,WAAY,QAAQ,EAClE,MAAO,QACP,OAAQ,OACT,EACD,SAAU,CACR,8BACA,kBACA,eACA,uBACA,yBACA,kCACA,sBACA,6BACD,EACD,YAAa,CACX,uCACA,gEACA,iCACD,CACH,EAKaC,GAA8B,KAAO,CAChD,QAAS,gBACT,MAAO,GAAGD,EAAc,GAAG,iBAC3B,KAAMA,EAAc,SACpB,cAAeA,EAAc,cAC7B,IAAKA,EAAc,IACnB,YAAaA,EAAc,YAC3B,QAAS,CACP,QAAS,gBACT,GAAGA,EAAc,OAClB,EACD,IAAK,CACH,QAAS,iBACT,GAAGA,EAAc,GAClB,EACD,aAAc,CACZ,QAAS,eACT,GAAGA,EAAc,YAClB,EACD,0BAA2B,CACzB,QAAS,4BACT,GAAGA,EAAc,YAClB,EACD,YAAa,CACX,QAAS,UACT,KAAM,SACP,EACD,gBAAiB,CACf,QAAS,eACT,KAAM,gCACN,gBAAiBA,EAAc,SAAS,IAAKE,IAAa,CACxD,QAAS,QACT,YAAa,CACX,QAAS,UACT,KAAMA,CACP,CACP,EAAM,CACH,EACD,KAAM,CACJ,QAAS,cACT,IAAK,GAAGF,EAAc,GAAG,YACzB,MAAO,MACP,OAAQ,IACT,EACD,OAAQA,EAAc,WACxB,GAKaG,GAAwB,KAAO,CAC1C,QAAS,UACT,MAAO,GAAGH,EAAc,GAAG,YAC3B,KAAMA,EAAc,KACpB,cAAeA,EAAc,cAC7B,IAAKA,EAAc,IACnB,YAAaA,EAAc,YAC3B,UAAW,CACT,MAAO,GAAGA,EAAc,GAAG,gBAC5B,EACD,gBAAiB,CACf,QAAS,eACT,OAAQ,GAAGA,EAAc,GAAG,iCAC5B,cAAe,kCAChB,EACD,WAAY,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,CAC3C,GAKaI,GAAyBC,IAAa,CACjD,QAAS,UACT,SAAUA,EAAQ,MAClB,YAAaA,EAAQ,SAAWA,EAAQ,YACxC,MAAOA,EAAQ,eAAiB,GAAGL,EAAc,GAAG,YACpD,OAAQ,CACN,QAAS,SACT,KAAMK,EAAQ,QAAUL,EAAc,IACvC,EACD,UAAW,CACT,QAAS,eACT,KAAMA,EAAc,KACpB,KAAM,CACJ,QAAS,cACT,IAAK,GAAGA,EAAc,GAAG,WAC1B,CACF,EACD,cAAeK,EAAQ,YACvB,aAAcA,EAAQ,YAAcA,EAAQ,YAC5C,iBAAkB,CAChB,QAAS,UACT,MAAOA,EAAQ,GAChB,CACH,GAKaC,GAAyBC,IAAa,CACjD,QAAS,sBACT,KAAMA,EAAQ,MACd,YAAaA,EAAQ,YACrB,oBAAqB,sBACrB,gBAAiB,MACjB,UAAW,CACT,MAAO,GAAGP,EAAc,GAAG,gBAC5B,EACD,OAAQ,CACN,QAAS,QACT,MAAOO,EAAQ,OAAS,sBACxB,cAAe,MACf,aAAc,4BACf,EACD,gBAAiBA,EAAQ,OACrB,CACE,QAAS,kBACT,YAAaA,EAAQ,OAAO,MAC5B,YAAaA,EAAQ,OAAO,KACpC,EACM,MACN,GAKaC,GAA4BC,IAAiB,CACxD,QAAS,iBACT,gBAAiBA,EAAY,IAAI,CAACC,EAAOC,KAAW,CAClD,QAAS,WACT,SAAUA,EAAQ,EAClB,KAAM,CACJ,MAAOD,EAAM,IACb,KAAMA,EAAM,IACb,CACL,EAAI,CACJ,GAKaE,GAAiB,CAACC,EAAUC,EAAU,GAAIC,EAAW,OAAS,CACzE,MAAMC,EAAe,CACnB,uBACA,kBACA,kBACA,eACA,UACA,SACD,EAEKC,EAAU,CACd,SAAU,CACR,MAAO,6CACP,YACE,wJACF,SAAU,CAAC,GAAGD,EAAc,uBAAwB,oBAAoB,EACxE,OAAQ,CAACf,KAA+BE,IAAuB,CAChE,EACD,MAAO,CACL,MAAO,kDACP,YACE,kJACF,SAAU,CAAC,GAAGa,EAAc,QAAS,OAAQ,SAAS,EACtD,OAAQ,CAACf,IAA6B,CACvC,EACD,SAAU,CACR,MAAO,mDACP,YACE,yJACF,SAAU,CACR,GAAGe,EACH,WACA,qBACA,aACA,aACD,EACD,OAAQ,CAACf,IAA6B,CACvC,EACD,SAAU,CACR,MAAO,sDACP,YACE,4HACF,SAAU,CACR,GAAGe,EACH,cACA,oBACA,UACD,EACD,OAAQ,CAACf,IAA6B,CACvC,EACD,KAAM,CACJ,MAAO,iDACP,YACE,wIACF,SAAU,CACR,GAAGe,EACH,OACA,YACA,WACA,iBACD,EACD,OAAQ,CAACf,IAA6B,CACvC,EACD,QAAS,CACP,MAAO,qDACP,YACE,mJACF,SAAU,CAAC,GAAGe,EAAc,UAAW,QAAS,cAAc,EAC9D,OAAQ,CAACf,IAA6B,CACvC,CACF,EAGD,OAAIa,EAAQ,OAASA,EAAQ,YACpB,CACL,MAAOA,EAAQ,MACf,YAAaA,EAAQ,YACrB,SAAUA,EAAQ,UAAYE,EAC9B,OAAQF,EAAQ,QAAU,CAACb,GAA2B,CAAE,CACzD,EAGIgB,EAAQJ,CAAQ,GAAKI,EAAQ,QACtC,ECpKA,SAAwBC,IAA6B,CAC7C,MAAAD,EAAUL,GAAe,UAAU,EAEzC,OAEIO,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAOL,EAAQ,MACf,YAAaA,EAAQ,YACrB,KAAK,GACL,KAAK,UACL,MAAM,gCACN,SAAS,mEACT,OAAQA,EAAQ,OAChB,SAAUA,EAAQ,SACpB,QACC,MAAI,WAAU,gBACb,SAACE,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,8DACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAACG,GAAA,CACC,UAAU,oFACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,eAACC,GAAK,IACR,EAEAJ,MAACK,GAAK,MAAI,EAAC,IACb,QACC,SAAO,WAAU,6DAChB,SAAAL,EAAA,IAACM,KAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,CChJA,SAAwBC,IAA0B,CAChD,KAAM,CAAE,EAAGC,EAAW,KAAAC,CAAA,EAASC,GAAe,EACxCC,EAAkBF,EAAK,UAAY,KAEnC,CAACG,EAAUC,CAAW,EAAIC,WAAS,EAAE,EACrC,CAACC,EAAkBC,CAAmB,EAAIF,WAAS,EAAE,EACrD,CAACG,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAACK,EAAkBC,CAAmB,EAAIN,WAAS,KAAK,EACxD,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAErCW,YAAU,IAAM,CACDC,EAAA,EACEC,EAAA,GACd,CAAChB,CAAe,CAAC,EAEpB,MAAMe,EAAe,SAAY,CAC3B,IACFJ,EAAW,EAAI,EACf,KAAM,CAAE,SAAAM,EAAU,KAAAC,CAAS,QAAMC,GAAY,YAAY,CACvD,SAAUnB,EACV,OAAQ,YACT,EAEGiB,EAAS,IAAMC,EAAK,SACd,YAAI,mBAAoBA,EAAK,QAAQ,EAC7ChB,EAAYgB,EAAK,QAAQ,EACzBb,EAAoBa,EAAK,QAAQ,GAEjCL,EAAS,yBAAyB,QAE7BO,EAAK,CACJ,cAAM,0BAA2BA,CAAG,EAC5CP,EAAS,wBAAwB,SACjC,CACAF,EAAW,EAAK,EAEpB,EAEMK,EAAiB,SAAY,CAC7B,IACF,KAAM,CAAE,SAAAC,EAAU,KAAAC,CAAS,QAAMG,GAAc,cAAc,EAEzDJ,EAAS,IAAMC,EAAK,SACRX,EAAAW,EAAK,MAAQA,EAAK,UAAU,QAErCE,EAAK,CACJ,cAAM,4BAA6BA,CAAG,EAElD,EAEME,EAAwBC,GAAiB,CAG7C,GAFAd,EAAoBc,CAAY,EAE5BA,IAAiB,MACnBlB,EAAoBJ,CAAQ,MACvB,CACL,MAAMuB,EAAWvB,EAAS,OAAQ1B,GAChCA,EAAQ,WAAW,KAAMkD,GAAQA,EAAI,SAAS,OAASF,CAAY,CACrE,EACAlB,EAAoBmB,CAAQ,EAEhC,EAEME,EAAsBnD,GAAY,CACtCoD,GAAW,eAAgB,CACzB,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,SAAUyB,EACV,OAAQ,mBACT,CACH,EAUMf,EAAUL,GAAe,UAAU,EAIrC,OAAAO,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAOO,EAAU,qBAAqB,GAAKZ,EAAQ,MACnD,YACEY,EAAU,2BAA2B,GAAKZ,EAAQ,YAEpD,KAAK,WACL,KAAK,UACL,MAAM,oCACN,OAAQA,EAAQ,OAChB,SACEY,EAAU,wBAAwB,EAC9BA,EAAU,wBAAwB,EAC/B,MAAM,GAAG,EACT,IAAK+B,GAAMA,EAAE,KAAK,CAAC,EACtB3C,EAAQ,SAEhB,QACC,QACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAaH,QAEC,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACI,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,EAECoB,OAAA,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,SAACA,EAAA,WAAI,UAAU,6CACb,eAAC,MAAI,WAAU,cACb,SAACA,MAAA,OAAI,UAAU,MACb,gBAAC,OAAI,UAAU,uBACb,UAACA,MAAA,MAAG,UAAU,mBACZ,SAAAA,EAAA,IAAC,QACC,UAAU,kBACV,iBAAe,QAEf,eAACwC,GAAA,CAAa,KAAMhC,EAAU,gBAAgB,CAAG,KAErD,QACC,MAAI,WAAU,MACb,SAACR,MAAA,OAAI,UAAU,uBACb,SAAAA,EAAA,IAAC,KACC,UAAU,gCACV,iBAAe,OAEd,WAAU,sBAAsB,EACnC,EACF,CACF,IACF,EACF,EACF,CACF,GACF,QAEC,UAAQ,WAAU,uCACjB,SAACF,EAAA,YAAI,UAAU,8BAEb,gBAAC,OAAI,UAAU,qBACb,SAACE,EAAA,WAAI,UAAU,YACb,SAAAF,OAAC,MAAI,WAAU,gDACb,UAAAE,EAAA,IAAC,KACC,QAAS,IAAMiC,EAAqB,KAAK,EACzC,UAAW,UACTd,IAAqB,MAAQ,SAAW,EAC1C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,qBAAqB,EAClC,EACCF,EAAW,IAAKwB,GACfzC,EAAA,IAAC,KAEC,QAAS,IAAMiC,EAAqBQ,EAAS,IAAI,EACjD,UAAW,UACTtB,IAAqBsB,EAAS,KAAO,SAAW,EAClD,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,SAASA,EAAA,MAPLA,EAAS,EASjB,GACH,EACF,GACF,EAGCpB,EACErB,EAAA,UAAI,WAAU,mBACb,SAAAA,EAAA,IAAC,OACC,UAAU,8BACV,KAAK,SAEL,eAAC,QAAK,UAAU,kBAAkB,SAAU,gBAEhD,GACEuB,EACFvB,EAAA,IAAC,OACC,UAAU,iCACV,KAAK,QAEJ,SAAAuB,CAAA,CAGH,EAAAvB,EAAA,IAAC,MAAI,WAAU,aACZ,SAAAe,EAAiB,OAAS,EACzBA,EAAiB,IAAI,CAAC7B,EAASI,IAC7BU,EAAA,IAAC,OAEC,UAAU,oCAEV,SAACF,EAAA,WAAI,WAAU,+CACb,UAACE,MAAA,OAAI,UAAU,gBACb,SAAAA,EAAA,IAAC0C,EAAA,CACC,GAAI,IAAI/B,CAAe,qBACrBzB,EAAQ,MAAQA,EAAQ,IACxB,QAAQ,OAAQ,EAAE,CAAC,GACrB,QAAS,IAAMmD,EAAmBnD,CAAO,EAEzC,SAAAc,EAAA,IAAC,OACC,IACEd,EAAQ,cACJ,gDAIEA,EAAQ,aACV,GACA,yCAEN,MAAO,IACP,OAAQ,IACR,IACEA,EAAQ,kBAAoBA,EAAQ,MAEtC,UAAU,iBACV,oBAAkB,QACpB,GAEJ,EAGCY,OAAA,MAAI,WAAU,iCACb,UAACE,MAAA,MAAG,UAAU,kBACZ,SAAAA,EAAA,IAAC0C,EAAA,CACC,GAAI,IAAI/B,CAAe,qBACrBzB,EAAQ,MAAQA,EAAQ,IACxB,QAAQ,OAAQ,EAAE,CAAC,GACrB,QAAS,IAAMmD,EAAmBnD,CAAO,EAExC,SAAQA,EAAA,QAEb,EAECc,EAAA,WAAI,UAAU,6BACZ,WAAQ,QACX,EAGCF,OAAA,MAAI,WAAU,UAEb,UAACA,OAAA,OAAI,UAAU,oCACZ,UAAAZ,EAAQ,iBACPY,OAAC,MAAI,WAAU,kBACb,UAACA,OAAA,QAAK,UAAU,YACb,UAAAU,EACC,2BACF,EAAE,IACA,KACJ,EACCV,OAAA,OAAK,WAAU,qCAAqC,cACjDZ,EAAQ,gBACZ,IACF,EAEDA,EAAQ,mBACNY,OAAA,OAAI,UAAU,kBACb,UAACA,OAAA,QAAK,UAAU,YACb,UAAAU,EACC,6BACF,EAAE,IACA,KACJ,EACCV,OAAA,OAAK,WAAU,qCAAqC,cACjDZ,EAAQ,kBAAkB,MAC9B,GACF,IAEJ,QAGC,OAAI,UAAU,cACZ,WAAQ,SACPc,EAAA,IAAC,KACC,KAAMd,EAAQ,QACd,OAAO,SACP,IAAI,sBACJ,UAAU,yDACV,QAAUyD,GAAM,CAEd,WAAW,IAAM,CACfA,EAAE,OAAO,KAAK,GACb,GAAG,EAENL,GAAW,aAAc,CACvB,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,SAAUyB,EACV,OAAQ,mBACT,CACH,EAEA,eAAC,QACE,SAAUH,EAAA,oBAAoB,CACjC,IAGN,GACF,GACF,GACF,IAhHKtB,EAAQ,GAkHhB,EAEAc,EAAA,WAAI,UAAU,qBACb,SAACA,MAAA,IAAE,WAAU,aACV,SAAAQ,EAAU,sBAAsB,EACnC,EACF,CAEJ,IAEJ,CACF,IACF,QAEC,SAAO,WAAU,mCAChB,SAAAR,EAAA,IAACM,KAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHC9VMsC,GAAW,CACf,MACE,yFACF,YAAa,8DACf,EACA,SAAwBC,IAA2B,CACjD,OAEI/C,EAAA,KAAAC,WAAA,WAACC,MAAA8C,GAAA,CAAc,KAAMF,EAAU,SAC9B,MAAI,WAAU,gBACb,SAAC9C,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OACnB,qBAED,QACC,MAAI,WAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,OAAI,UAAU,4CACb,eAAC,IAAE,WAAU,sCAAsC,qDAEnD,EACF,CACF,GACF,GACF,EACAA,EAAA,IAAC,WACC,UAAW,gEAGX,GAAG,YAEH,eAAC+C,GAAU,IACb,QACC,MAAI,WAAU,+BACb,SAAA/C,MAACgD,IAAY,GACf,EACChD,MAAA,WAAQ,UAAU,4CACjB,eAAC,MAAI,WAAU,8BAIb,SAAAA,EAAA,IAAC,OAAI,UAAU,+BACb,SAACF,OAAA,OAAI,UAAU,6CACb,UAACE,EAAA,SAAE,UAAU,+BAA+B,SAG5C,4FACAA,MAAC,MAAI,WAAU,eACb,SAAAA,EAAA,IAAC0C,EAAA,CACC,GAAI,mBACJ,UAAU,wDAEV,SAAA1C,MAAC,QAAK,SAAU,gBAEpB,IACF,EACF,EACF,CACF,IACF,QACC,SAAO,WAAU,6DAChB,SAAAA,EAAA,IAACM,KAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHC/EA,SAAwB2C,IAAsB,WAC5C,KAAM,CAAE,EAAAC,EAAG,KAAAzC,CAAK,EAAIC,GAAe,EAC7BC,EAAkBF,EAAK,UAAY,KACnC,CAAC0C,EAAcC,CAAe,EAAIC,GAAgB,EAClD,CAACC,EAAWC,CAAY,EAAIzC,WAAS,EAAE,EACvC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAAC0C,EAAaC,CAAc,EAAI3C,WAAS,CAAC,EAC1C,CAAC4C,EAAYC,CAAa,EAAI7C,WAAS,CAAC,EACxC,CAACG,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAAC8C,EAAMC,CAAO,EAAI/C,WAAS,EAAE,EAC7B,CAACgD,EAAaC,CAAc,EAAIjD,WAAS,EAAE,EAG3CkD,EAAkBb,EAAa,IAAI,UAAU,EAC7Cc,EAAad,EAAa,IAAI,KAAK,EACnCe,EAAgBf,EAAa,IAAI,QAAQ,EAE/C1B,YAAU,IAAM,EACI,SAAY,SACxB,IACFH,EAAW,EAAI,EAGf,MAAM6C,EAAS,CACb,SAAUxD,EACV,KAAM6C,EACN,MAAO,CACT,EAEIQ,MAAwB,SAAWA,GACnCC,MAAmB,IAAMA,GACzBC,MAAsB,OAASA,GAEnC,MAAME,EAAa,MAAMC,GAAQ,aAAaF,CAAM,EAEpD,GAAIC,EAAW,SAAS,IAAMA,EAAW,KAAM,CAEvC,MAAAE,IACJC,EAAAH,EAAW,KAAK,OAAhB,YAAAG,EAAsB,OAAQH,EAAW,KAAK,MAAQ,CAAC,EACnDI,IACJC,EAAAL,EAAW,KAAK,OAAhB,YAAAK,EAAsB,aAAcL,EAAW,KAAK,WAC9C,YAAI,6BAA8BA,EAAW,IAAI,EACjD,YAAI,eAAgBE,CAAK,EACzB,YAAI,cAAeE,CAAU,EACrCjB,EAAa,MAAM,QAAQe,CAAK,EAAIA,EAAQ,EAAE,EAChCX,GAAAa,GAAA,YAAAA,EAAY,aAAc,CAAC,OAEjC,cACN,8BACAJ,EAAW,SAAS,MACtB,EACAb,EAAa,EAAE,EAIb,IACI,MAAAmB,EAAmB,MAAM1C,GAAc,cAAc,EACvD0C,EAAiB,SAAS,IAAMA,EAAiB,MACnDxD,EAAcwD,EAAiB,KAAK,MAAQ,EAAE,QAEzCnD,EAAO,CACN,cAAM,6BAA8BA,CAAK,EAI/C,IACI,MAAAoD,EAAa,MAAMC,GAAQ,QAAQ,EACrCD,EAAW,SAAS,IAAMA,EAAW,MACvCd,EAAQc,EAAW,KAAK,MAAQ,EAAE,QAE7BpD,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAIzC,IACI,MAAAsD,EAAgB,MAAMC,GAAW,WAAW,EAC9CD,EAAc,SAAS,IAAMA,EAAc,MAC7Cd,EAAec,EAAc,KAAK,SAAW,EAAE,QAE1CtD,EAAO,CACN,cAAM,0BAA2BA,CAAK,SAEzCA,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAC3CgC,EAAa,EAAE,SACf,CACAjC,EAAW,EAAK,EAEpB,GAEU,GACT,CACDX,EACA6C,EACAQ,EACAC,EACAC,CAAA,CACD,EAGK,MAAAa,EAAiB,CAACC,EAAMC,IAAU,WAChC,MAAAC,GAAcX,EAAAS,EAAK,eAAL,YAAAT,EAAmB,KACpCrB,GAAMA,EAAE,WAAavC,GAExB,OACEuE,GAAA,YAAAA,EAAcD,OACdE,GAAAV,EAAAO,EAAK,eAAL,YAAAP,EAAmB,KAAMvB,GAAMA,EAAE,WAAa,QAA9C,YAAAiC,EAAsDF,KACtD,EAEJ,EAGMhD,EAAwBC,GAAiB,CACvC,MAAAkD,EAAY,IAAI,gBAAgBjC,CAAY,EAC9CjB,EACQkD,EAAA,IAAI,WAAYlD,CAAY,EAEtCkD,EAAU,OAAO,UAAU,EAE7BA,EAAU,OAAO,MAAM,EACvBhC,EAAgBgC,CAAS,EACzB3B,EAAe,CAAC,CAClB,EAEM4B,EAAmBC,GAAY,CAC7B,MAAAF,EAAY,IAAI,gBAAgBjC,CAAY,EAC9CmC,EACQF,EAAA,IAAI,MAAOE,CAAO,EAE5BF,EAAU,OAAO,KAAK,EAExBA,EAAU,OAAO,MAAM,EACvBhC,EAAgBgC,CAAS,EACzB3B,EAAe,CAAC,CAClB,EAEM8B,EAAe,IAAM,CACzBnC,EAAgB,EAAE,EAClBK,EAAe,CAAC,CAClB,EAEM7D,EAAUL,GAAe,MAAM,EAErC,OAEIO,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAOiD,EAAE,iBAAiB,GAAKtD,EAAQ,MACvC,YAAasD,EAAE,uBAAuB,GAAKtD,EAAQ,YACnD,KAAK,OACL,KAAK,UACL,MAAM,gCACN,OAAQA,EAAQ,OAChB,SACEsD,EAAE,qBAAsB,CAAE,cAAe,EAAK,CAAC,GAAKtD,EAAQ,SAEhE,QACC,MAAI,WAAU,gBACb,SAACE,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAW,+CACTgE,GAAmBC,GAAcC,EAC7B,oBACA,EACN,GACA,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAApE,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,YAAY,EACjB,QACC,MAAI,WAAU,iBAAiB,iBAAe,OAC7C,eAAC,MAAI,WAAU,4CACb,SAAAA,MAAC,KAAE,UAAU,sCACV,WAAE,eAAe,EACpB,EACF,CACF,IAGEgE,GAAmBC,GAAcC,IAChClE,MAAA,OAAI,UAAU,wBACb,SAAAF,EAAA,KAAC,MAAI,WAAU,mEACb,UAACE,EAAA,YAAK,UAAU,qBAAqB,SAAY,iBAChDgE,GACClE,EAAA,KAAC,OAAK,WAAU,mBAAmB,sBACvB,KAERyE,GAAAtD,EAAW,KAAMuE,GAAMA,EAAE,OAASxB,CAAe,IAAjD,YAAAO,GACI,MAER,EAEDN,GACCnE,EAAA,KAAC,OAAK,WAAU,qBAAqB,mBAC7B2E,GAAAb,EAAK,KAAMV,GAAMA,EAAE,OAASe,CAAU,IAAtC,YAAAQ,GAAyC,MACjD,EAEDP,GACCpE,EAAA,KAAC,OAAK,WAAU,gBAAgB,sBACpBoE,EAAc,KAC1B,EAEFpE,EAAA,KAAC,UACC,QAASyF,EACT,UAAU,6CACV,oBAAkB,IAElB,UAACvF,EAAA,YAAK,UAAU,oCAAoC,SAEpD,kBACAA,EAAA,IAAC,QACC,UAAU,kCACV,cAAY,OACb,0BAED,GACF,EACF,CACF,GAEJ,GACF,EAEEF,OAAAC,WAAA,WAAAC,EAAA,IAAC,WACC,UAAU,uCACV,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,YAEb,UAAAA,EAAA,KAAC,OACC,UAAU,gCACV,kBAAiB,EAGhB,UACCuB,GAAArB,EAAA,IAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,WAAU,YAAa,SAAAkD,EAAE,cAAc,CAAE,GAChD,EAID,CAAC7B,GAAWiC,EAAU,SAAW,SAC/B,MAAI,WAAU,qBACb,SAAAtD,MAAC,OAAI,UAAU,YAAa,SAAEkD,EAAA,YAAY,CAAE,GAC9C,EAID,CAAC7B,GACA,MAAM,QAAQiC,CAAS,GACvBA,EAAU,IAAK0B,UACbhF,SAAA,IAAC,OAEC,UAAU,oCAEV,SAAAF,EAAA,KAAC,MAAI,WAAU,sBACb,UAACE,EAAA,WAAI,UAAU,gBACb,SAAAA,MAAC0C,GAAK,GAAI,gBAAgBsC,EAAK,IAAI,GACjC,SAAAhF,EAAA,IAAC,OACC,IACEgF,EAAK,eACL,yCAEF,MAAO,IACP,OAAQ,IACR,IAAKD,EAAeC,EAAM,OAAO,IAErC,CACF,GACChF,MAAA,MAAG,UAAU,kBACZ,eAAC0C,EAAK,IAAI,gBAAgBsC,EAAK,IAAI,GAChC,SAAAD,EAAeC,EAAM,OAAO,CAC/B,GACF,QACC,MAAI,WAAU,iBACZ,SAAeD,EAAAC,EAAM,SAAS,EACjC,EACAlF,OAAC,MAAI,WAAU,0BACb,UAACA,OAAA,OAAI,UAAU,cACb,UAACE,MAAA,KAAE,KAAK,IAAI,UAAU,cACpB,SAACA,MAAA,KAAE,UAAU,+BAA+B,CAC9C,SACC,IAAE,MAAK,IACL,WAAKuE,EAAAS,EAAA,qBAAAT,EAAQ,OAAQ,gBACxB,IACF,EACAzE,OAAC,MAAI,WAAU,YACb,UAACE,MAAA,KAAE,UAAU,kCAAmC,GAC/CA,EAAA,SAAE,KAAK,IACL,SAAI,SACHgF,EAAK,aAAeA,EAAK,SAC3B,EAAE,oBACJ,GACF,GACF,GACF,IA3CKA,EAAK,EA6Cb,KAIL,EAGAhF,EAAA,IAACyF,GAAA,CACC,YAAAjC,EACA,WAAAE,EACA,aAAcD,CAAA,EAChB,CAEF,GACF,EAGAzD,MAAC,KAAG,WAAU,iBAAkB,GAGhCA,EAAA,IAAC,UAAQ,WAAU,uCACjB,SAAAA,EAAA,IAAC,MAAI,WAAU,qBACb,SAAAF,OAAC,MAAI,WAAU,aACb,UAAAE,MAAC,OAAI,UAAU,0BAEb,SAACF,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eACX,SAAAkD,EAAE,iBAAiB,EACtB,EACClD,EAAA,WAAI,UAAU,cACb,SAACA,MAAA,MAAG,UAAU,wBACX,SAAWiB,EAAA,IAAKwB,GAAA,qBACd,KACC,WAAAzC,EAAA,IAAC,KACC,KAAK,IACL,MAAM,GACN,QAAU2C,GAAM,CACdA,EAAE,eAAe,EACjBV,EAAqBQ,EAAS,IAAI,CACpC,EACA,UACEuB,IAAoBvB,EAAS,KACzB,SACA,GAGL,SAASA,EAAA,KACZ,SACC,QACE,eAAI,OACF8B,EAAA9B,EAAS,SAAT,YAAA8B,EAAiB,YAAa,EAAG,IACtC,KAnBO9B,EAAS,EAoBlB,EACD,CACH,EACF,IACF,CAEF,SACC,MAAI,WAAU,0BAEb,SAAC3C,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eAAgB,SAAAkD,EAAE,WAAW,EAAE,EAC7ClD,EAAA,IAAC,MAAI,WAAU,cACb,SAAAA,MAAC,MAAI,WAAU,OACZ,SAAA4D,EAAK,IAAK8B,GACT1F,EAAA,IAAC,KACC,KAAK,IAEL,QAAU2C,GAAM,CACdA,EAAE,eAAe,EACjB0C,EAAgBK,EAAI,IAAI,CAC1B,EACA,UACEzB,IAAeyB,EAAI,KAAO,SAAW,GAGtC,SAAIA,EAAA,MATAA,EAAI,EAWZ,EACH,EACF,IACF,CAEF,SACC,MAAI,WAAU,0BAEb,SAAC5F,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eAAgB,SAAAkD,EAAE,cAAc,EAAE,EAC/ClD,MAAA,OAAI,UAAU,cACb,eAAC,KAAG,WAAU,wBACX,SAAA8D,EAAY,IAAI,CAAC6B,EAASrG,WACxB,KACC,WAAAQ,EAAA,KAAC,IAAE,MAAK,IAAI,MAAM,GACf,UAAQ6F,EAAA,UAAU,IAAEA,EAAQ,MAC/B,SACC,QAAM,iBAAIA,EAAQ,MAAM,IAAC,KAJnBrG,CAKT,CACD,EACH,CACF,IACF,CAEF,SACC,MAAI,WAAU,0BAEb,SAACQ,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eACX,SAAAkD,EAAE,yBAAyB,EAC9B,QACC,MAAI,WAAU,cACb,SAACpD,EAAA,YAAI,UAAU,uBACb,UAAAE,EAAA,IAAC,OACC,IAAI,4BACJ,IAAI,iBACJ,OAAQ,GACR,MAAO,GACP,UAAU,gBACV,MAAO,CAAE,aAAc,KAAM,EAC/B,EACCkD,EAAE,wBAAwB,GAC7B,CACF,IACF,CAEF,GACF,EACF,EACF,GACF,IACF,QACC,SAAO,WAAU,6DAChB,SAAAlD,EAAA,IAACM,KAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHCtcMsC,GAAW,CACf,MACE,gGACF,YAAa,8DACf,EACA,SAAwBgD,IAAiC,CACvD,IAAIzB,EAAS0B,GAAU,EACvB,MAAMC,EACJC,GAAc,OAAQC,GAAQA,EAAI,IAAM7B,EAAO,EAAE,EAAE,CAAC,GAAK4B,GAAc,CAAC,EAC1E,OAEIjG,EAAA,KAAAC,WAAA,WAACC,MAAA8C,GAAA,CAAc,KAAMF,EAAU,SAC9B,MAAI,WAAU,gBACb,SAAC9C,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,SAAc8F,EAAA,MACjB,QAEC,MAAI,WAAU,iBAAiB,iBAAe,OAC7C,SAAC9F,MAAA,OAAI,UAAU,4CACb,eAAC,IAAE,WAAU,sCAAsC,uDAEnD,EACF,CACF,GACF,GACF,EAGEF,OAAAC,WAAA,WAAAC,MAAC,WAAQ,UAAU,uCACjB,SAACF,EAAA,YAAI,UAAU,qBACb,UAACA,OAAA,OAAI,UAAU,qBAEb,UAACA,OAAA,OAAI,UAAU,oBACb,UAACE,EAAA,UAAG,UAAU,4BAA4B,SAE1C,oBACAA,MAAC,KAAG,WAAU,OAAQ,GACtBF,OAAC,MAAI,WAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,WACb,SAACA,MAAA,KAAE,iBAAK,CACV,GACCA,EAAA,WAAI,UAAU,WAAW,SAAa,mBACzC,EACAA,MAAC,KAAG,WAAU,OAAQ,GACtBF,OAAC,MAAI,WAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,WACb,SAACA,MAAA,KAAE,mBAAO,CACZ,GACCA,EAAA,WAAI,UAAU,WAAW,SAAY,kBACxC,EACAA,MAAC,KAAG,WAAU,OAAQ,GACtBF,OAAC,MAAI,WAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,WACb,SAACA,MAAA,KAAE,qBAAS,CACd,GACCA,EAAA,WAAI,UAAU,WAAW,SAG1B,yEACF,EACAA,MAAC,KAAG,WAAU,OAAQ,IACxB,EAGAF,OAAC,MAAI,WAAU,WACb,UAACE,EAAA,UAAG,UAAU,4BAA4B,SAE1C,gBACAA,MAAC,KAAG,WAAU,OAAQ,GACrBA,EAAA,SAAE,UAAU,iBAAiB,SAU9B,idACF,IAEF,EACAF,OAAC,MAAI,WAAU,aAEb,UAACE,MAAA,OAAI,UAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,OAEZ,EAGAA,MAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,OAEZ,EAGAA,MAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,OAEZ,EAGAA,MAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,MAEZ,GAEF,IACF,CACF,GAGAA,MAAC,KAAG,WAAU,iBAAkB,IAElC,QACC,UAAQ,WAAU,uCACjB,SAAAA,MAACiG,IAAgB,GACnB,EAGEnG,OAAAC,WAAA,WAACC,MAAA,MAAG,UAAU,iBAAkB,GAGhCF,OAAC,MAAI,WAAU,+EACb,UAAAE,MAAC0C,GAAK,GAAI,6BAA8B,UAAU,YAChD,gBAAC,OACC,WAAC1C,MAAA,KAAE,UAAU,oCAAqC,GAAG,IAAI,YAE3D,CACF,SACC,IAAE,MAAK,IAAI,UAAU,WACpB,gBAAC,OACC,WAACA,MAAA,KAAE,UAAU,+BAAgC,GAAE,cACjD,CACF,SACC0C,EAAK,IAAI,6BAA8B,UAAU,YAChD,gBAAC,OAAK,mBACC1C,MAAC,IAAE,WAAU,qCAAsC,IAC1D,CACF,GACF,GAEF,IACF,QACC,SAAO,WAAU,6DAChB,SAAAA,EAAA,IAACM,KAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHCtLA,SAAwB4F,IAAgC,CAChD,MAAE,GAAAC,CAAG,EAAIN,GAAU,EACnBO,EAAWC,GAAY,EACvB,CAAE,EAAG7F,EAAW,KAAAC,CAAA,EAASC,GAAe,EACxCC,EAAkBF,EAAK,UAAY,KAEnC,CAACvB,EAASoH,CAAU,EAAIxF,WAAS,IAAI,EACrC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAErCW,YAAU,IAAM,CACF8E,EAAA,GACX,CAACJ,EAAIxF,CAAe,CAAC,EAExB,MAAM4F,EAAc,SAAY,CAC1B,IACFjF,EAAW,EAAI,EAIT,MAAAO,EAAO,MAHI,MAAM,MACrB,iBAAiBsE,CAAE,aAAaxF,CAAe,EACjD,GAC4B,KAAK,EAE7BkB,EAAK,SACPyE,EAAWzE,EAAK,OAAO,EAGvBS,GAAW,sBAAuB,CAChC,WAAYT,EAAK,QAAQ,GACzB,cAAeA,EAAK,QAAQ,MAC5B,SAAUlB,EACV,OAAQ,cACT,GAEDa,EAAS,mBAAmB,QAEvBO,EAAK,CACJ,cAAM,yBAA0BA,CAAG,EAC3CP,EAAS,uBAAuB,SAChC,CACAF,EAAW,EAAK,EAEpB,EAEMkF,EAAkB,IAAM,CAC5BlE,GAAW,aAAc,CACvB,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,SAAUyB,EACV,OAAQ,iBACT,CACH,EAEM8F,EAAuBC,GAAS,CACpCpE,GAAW,kBAAmB,CAC5B,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,cAAewH,EACf,SAAU/F,EACV,OAAQ,iBACT,CACH,EAEMgG,EAAeC,GACdA,EACE,IAAI,KAAK,aAAa,QAAS,CACpC,MAAO,WACP,SAAU,MACX,EAAE,OAAOA,CAAK,EAJI,KAOrB,GAAIvF,EACF,aACG,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACrB,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,QACC,OAAK,IAAG,OACP,eAAC,UAAQ,WAAU,uCACjB,SAACsB,MAAA,MAAI,WAAU,6BACb,SAACA,MAAA,OAAI,UAAU,8BAA8B,KAAK,SAChD,SAACA,MAAA,QAAK,UAAU,kBAAkB,SAAU,eAC9C,EACF,EACF,CACF,GACF,EACF,GACF,EAIA,GAAAuB,GAAS,CAACrC,EACZ,aACG,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACc,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,QACC,OAAK,IAAG,OACP,SAACsB,EAAA,cAAQ,WAAU,uCACjB,SAAAF,OAAC,MAAI,WAAU,6BACb,UAACE,MAAA,MAAG,SAAiB,sBACpBA,MAAA,KAAE,SAA6C,kDAChDA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,IAAIzF,CAAe,WAAW,EACtD,UAAU,wBACX,6BAED,CACF,EACF,EACF,GACF,EACF,GACF,EAKJ,MAAMkG,EAAgB5H,GAAsB,CAC1C,MAAOC,EAAQ,MACf,YAAaA,EAAQ,QACrB,MAAOA,EAAQ,iBAAmBA,EAAQ,kBAC1C,cAAeA,EAAQ,cACvB,IAAK,wBAAwByB,CAAe,oBAAoBzB,EAAQ,IAAI,GAC7E,EAGK4H,EAAmB3H,GAAyB,CAChD,CAAE,KAAM,OAAQ,IAAK,wBAAwBwB,CAAe,EAAG,EAC/D,CACE,KAAM,WACN,IAAK,wBAAwBA,CAAe,WAC9C,EACA,CACE,KAAMzB,EAAQ,MACd,IAAK,wBAAwByB,CAAe,oBAAoBzB,EAAQ,IAAI,GAC9E,CACD,EAEK6H,EAAe7H,EAAQ,cACzB,gDAE2BA,EAAQ,aAAa,GAChD,oCAIA,OAAAY,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAOf,EAAQ,WAAaA,EAAQ,MACpC,YAAaA,EAAQ,iBAAmBA,EAAQ,QAChD,KAAM,mBAAmBA,EAAQ,IAAI,GACrC,KAAK,UACL,MAAO6H,EACP,SAAU7H,EAAQ,kBAAoBA,EAAQ,MAC9C,OAAQ,CAAC2H,EAAeC,CAAgB,EACxC,SACE5H,EAAQ,aACJA,EAAQ,aAAa,MAAM,GAAG,EAAE,IAAKqD,GAAMA,EAAE,KAAM,GACnD,CAAC,WAAY,WAAY,WAAW,EAE1C,YAAarD,EAAQ,YACrB,WAAYA,EAAQ,UACtB,QAEC,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACc,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,EAECoB,OAAA,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,yCACjB,cAAe,MACjB,EAEA,SAACA,EAAA,WAAI,UAAU,6CACb,eAAC,MAAI,WAAU,cACb,SAACA,MAAA,OAAI,UAAU,MACb,gBAAC,OAAI,UAAU,uBACb,UAACA,MAAA,MAAG,UAAU,mBACZ,SAAAA,EAAA,IAAC,QACC,UAAU,kBACV,iBAAe,QAEd,SAAQd,EAAA,QAEb,QACC,MAAI,WAAU,MACb,SAACc,MAAA,OAAI,UAAU,uBACb,SAAAA,EAAA,IAAC,KACC,UAAU,iCACV,iBAAe,OAEd,SAAQd,EAAA,QACX,EACF,CACF,IACF,EACF,EACF,CACF,GACF,EAGAc,EAAA,IAAC,WACC,UAAU,uCACV,MAAO,CAAE,WAAY,MAAO,EAE5B,eAAC,MAAI,WAAU,8BAEb,SAACF,OAAA,OAAI,UAAU,MACb,UAACA,OAAA,OAAI,UAAU,WAEb,UAAAE,EAAA,IAACgH,GAAA,CACC,OAAQ9H,EAAQ,OAChB,aAAcA,EAAQ,MACxB,EAGCc,MAAA,MAAI,WAAU,iBACb,SAAAA,EAAA,IAAC,OACC,UAAU,oBACV,wBAAyB,CAAE,OAAQd,EAAQ,OAAQ,IAEvD,EAGCY,OAAA,MAAI,WAAU,wCACZ,UAAQZ,EAAA,YACPA,EAAQ,WAAW,OAAS,GACzBY,EAAA,YAAI,UAAU,OACb,UAACE,MAAA,UAAO,SAAY,iBACnBd,EAAQ,WAAW,IAAI,CAACkD,EAAK9C,IAC3BU,MAAA,OACC,UAACA,MAAA,QAAK,UAAU,wBACb,WAAI,SAAS,IAChB,IAHSoC,EAAI,SAAS,EAIxB,CACD,GACH,EAGHlD,EAAQ,MAAQA,EAAQ,KAAK,OAAS,UACpC,MACC,WAACc,MAAA,UAAO,SAAM,WACbd,EAAQ,KAAK,IAAI,CAACwG,EAAKpG,IACrBU,MAAA,OACC,UAACA,MAAA,QAAK,UAAU,0BACb,WAAI,IAAI,IACX,IAHS0F,EAAI,IAAI,EAInB,CACD,EACH,GAEJ,IACF,QAGC,MAAI,WAAU,WACb,SAAC5F,EAAA,YAAI,UAAU,uBAEZ,UAAAZ,EAAQ,iBACPc,MAAC,MAAI,WAAU,cACb,SAAAF,EAAA,KAAC,OACC,UAAU,oCACV,MAAO,CACL,WAAY,4BACZ,OAAQ,sCACR,WAAY,eACd,EAEA,gBAAC,MAAG,UAAU,8BAA8B,SAE5C,uBACCA,OAAA,MAAI,WAAU,mBACb,UAAAE,EAAA,IAAC,OACC,UAAU,qBACV,MAAO,CACL,SAAU,SACV,WAAY,KACd,EAEC,SAAA2G,EAAYzH,EAAQ,eAAe,EACtC,QACC,SAAM,UAAU,YAAY,SAE7B,sBACF,QACC,KAAE,UAAU,mCAAmC,SAGhD,yDACCc,MAAA,MAAI,WAAU,cACb,SAAAA,EAAA,IAAC,UACC,UAAU,6DACV,QAAS,IACPyG,EAAoB,YAAY,EAElC,MAAO,CAAE,SAAU,OAAQ,EAE3B,SAACzG,MAAA,QAAK,SAAc,oBAExB,MAEJ,EAGDd,EAAQ,mBACNc,MAAA,OAAI,UAAU,cACb,SAAAF,EAAA,KAAC,OACC,UAAU,oCACV,MAAO,CACL,WAAY,4BACZ,OAAQ,sCACR,WAAY,eACd,EAEA,gBAAC,MAAG,UAAU,8BAA8B,SAE5C,iBACCA,OAAA,MAAI,WAAU,mBACb,UAAAA,EAAA,KAAC,OACC,UAAU,qBACV,MAAO,CACL,SAAU,SACV,WAAY,KACd,EAEC,UAAA6G,EAAYzH,EAAQ,iBAAiB,EACtCc,EAAA,IAAC,SACC,MAAO,CACL,SAAU,OACV,WAAY,KACd,EACD,gBAED,EACF,QACC,SAAM,UAAU,YAAY,SAE7B,qBACF,QACC,KAAE,UAAU,mCAAmC,SAGhD,sDACCA,MAAA,MAAI,WAAU,cACb,SAAAA,EAAA,IAAC,UACC,UAAU,mDACV,QAAS,IACPyG,EAAoB,cAAc,EAEpC,MAAO,CACL,SAAU,QACV,WAAY,UACZ,YAAa,UACb,MAAO,MACT,EAEA,SAACzG,MAAA,QAAK,SAAkB,wBAE5B,MAEJ,EAGDd,EAAQ,SACNc,MAAA,OAAI,UAAU,cACb,SAACA,MAAA,MAAI,WAAU,mBACb,SAAAA,EAAA,IAAC,KACC,KAAMd,EAAQ,QACd,OAAO,SACP,IAAI,sBACJ,UAAU,qBACV,QAASsH,EACT,MAAO,CAAE,OAAQ,SAAU,EAE3B,SAAAxG,EAAA,IAAC,QACC,UAAU,gDACV,mBAAiB,IAEjB,SAACF,EAAA,YAAK,WAAU,gBACd,gBAAC,QAAK,UAAU,kBAAkB,SAElC,mBACAE,EAAA,IAAC,QACC,UAAU,kBACV,cAAY,OACb,2BAED,CACF,IACF,CACF,EACF,EACF,EAIDF,OAAA,MAAI,WAAU,SACb,gBAAC,MAAG,UAAU,eAAe,SAAmB,wBAC/CA,OAAA,KAAG,WAAU,gBACZ,UAACA,OAAA,MAAG,UAAU,OACZ,UAACE,MAAA,UAAO,SAAU,eAAU,IAC3B,IAAI,KACHd,EAAQ,aACR,mBAAmB,GACvB,EACCY,OAAA,KAAG,WAAU,OACZ,UAACE,MAAA,UAAO,SAAa,kBAAU,IAC9B,IAAI,KAAKd,EAAQ,SAAS,EAAE,mBAAmB,GAClD,EACCY,OAAA,KAAG,WAAU,OACZ,UAACE,MAAA,UAAO,SAAM,WAAS,IAAEd,EAAQ,UACnC,GACF,GACF,IACF,CACF,IACF,CACF,IACF,EACF,QAEC,SAAO,WAAU,mCAChB,SAAAc,EAAA,IAACM,KAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHCrbA,SAAwB2G,IAA4B,iBAClD,IAAI9C,EAAS0B,GAAU,EACvB,KAAM,CAAE,EAAA3C,EAAG,KAAAzC,CAAK,EAAIC,GAAe,EAC7BC,EAAkBF,EAAK,UAAY,KACxB4F,GAAY,EAC7B,KAAM,CAACa,EAAMC,CAAO,EAAIrG,WAAS,IAAI,EAC/B,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAErCW,YAAU,IAAM,CACd,MAAM2F,EAAgB,SAAY,CAC5B,IACF9F,EAAW,EAAI,EACf,MAAM+F,EAAS,MAAMhD,GAAQ,QAAQF,EAAO,EAAE,EAE1CkD,EAAO,SAAS,IAAMA,EAAO,MACvB,YAAI,4BAA6BA,EAAO,IAAI,EACpDF,EAAQE,EAAO,KAAK,MAAQA,EAAO,IAAI,IAEvC,QAAQ,MAAM,6BAA8BA,EAAO,SAAS,MAAM,EAClE7F,EAAS,qBAAqB,SAEzBD,EAAO,CACN,cAAM,4BAA6BA,CAAK,EAChDC,EAAS,0BAA0B,SACnC,CACAF,EAAW,EAAK,EAEpB,EAEI6C,EAAO,IACKiD,EAAA,CAChB,EACC,CAACjD,EAAO,EAAE,CAAC,EAGd1C,YAAU,IAAM,CACVyF,GAAQ,CAAC7F,GAEX,WAAW,SAAY,CACjB,IAEF,KAAM,CAAE,oBAAAiG,CAAA,EAAwB,MAAAC,GAAA,oCAAAD,GAAA,KAAM,QACpC,kCACF,kFACM,MAAAA,EAAoB,yBAA0B,aAAa,QAC1D/F,EAAO,CACN,aAAK,sCAAuCA,CAAK,IAE1D,GAAG,CACR,EACC,CAAC2F,EAAM7F,CAAO,CAAC,EAGZ,MAAA0D,EAAiB,CAACC,EAAMC,IAAU,OACtC,GAAI,CAACD,GAAQ,CAACA,EAAK,aAAqB,SAClC,MAAAE,EAAcF,EAAK,aAAa,KACnC9B,GAAMA,EAAE,WAAavC,CACxB,EACA,OACEuE,GAAA,YAAAA,EAAcD,OACdV,EAAAS,EAAK,aAAa,KAAM9B,GAAMA,EAAE,WAAa,IAAI,IAAjD,YAAAqB,EAAqDU,KACrD,EAEJ,EAGA,GAAI5D,EACF,OACGrB,EAAA,WAAI,UAAU,gBACb,SAACA,MAAA,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,SACC,OAAK,IAAG,OACP,SAACsB,MAAA,WAAQ,UAAU,uCACjB,SAAAA,MAAC,OAAI,UAAU,YACb,eAAC,MAAI,WAAU,MACb,SAACF,EAAA,YAAI,UAAU,qBACb,UAAAE,MAAC,MAAG,SAAU,eACdA,MAAC,KAAE,SAAwC,4CAC7C,GACF,GACF,EACF,CACF,GACF,EACF,GACF,EAKA,IAACkH,GAAQ3F,EACX,OACGvB,EAAA,WAAI,UAAU,gBACb,SAACA,MAAA,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,SACC,OAAK,IAAG,OACP,SAACsB,MAAA,WAAQ,UAAU,uCACjB,SAAAA,MAAC,OAAI,UAAU,YACb,eAAC,MAAI,WAAU,MACb,SAACF,EAAA,YAAI,UAAU,qBACb,UAAAE,MAAC,MAAG,SAAmB,wBACvBA,MAAC,KAAE,SAGH,oDACAA,EAAA,IAAC,KACC,KAAK,QACL,UAAU,6CACX,yBAGH,GACF,GACF,EACF,CACF,GACF,EACF,GACF,EAKJ,MAAMwH,EAAgBzI,GAAsB,CAC1C,MAAOgG,EAAemC,EAAM,OAAO,EACnC,YAAanC,EAAemC,EAAM,SAAS,EAC3C,QAASnC,EAAemC,EAAM,SAAS,EACvC,cAAeA,EAAK,cACpB,OAAQA,EAAK,QAAU,YACvB,YAAaA,EAAK,YAClB,WAAYA,EAAK,UACjB,IAAK,wBAAwBvG,CAAe,gBAAgBuG,EAAK,IAAI,GACtE,EAED,OAEIpH,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAO8E,EAAemC,EAAM,OAAO,EACnC,YAAanC,EAAemC,EAAM,SAAS,EAC3C,KAAM,eAAeA,EAAK,IAAI,GAC9B,KAAK,UACL,MAAOA,EAAK,eAAiB,gCAC7B,SAAUnC,EAAemC,EAAM,OAAO,EACtC,OAAQA,EAAK,QAAU,YACvB,YAAaA,EAAK,YAClB,WAAYA,EAAK,UACjB,OAAQ,CAACM,CAAa,EACtB,SACEzC,EAAemC,EAAM,UAAU,GAAK,CAClC,OACA,uBACA,YACF,CAEJ,QACC,MAAI,WAAU,gBACb,SAACpH,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,gEACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,MAAC,OAAI,UAAU,MACb,SAACA,MAAA,OAAI,UAAU,wBACb,SAAAA,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,SAAA+E,EAAemC,EAAM,OAAO,IAEjC,CACF,GAEApH,EAAA,KAAC,OACC,UAAU,gDACV,iBAAe,OAEf,UAAAE,MAAC,OAAI,UAAU,sBACb,SAACF,EAAA,UAAE,KAAK,IACN,UAACE,MAAA,KAAE,UAAU,kBAAmB,GAC/BA,EAAA,YAAK,UAAU,kBAAkB,SAAK,UAAQ,IAC9C,IAAI,KACHkH,EAAK,aAAeA,EAAK,WACzB,mBAAmB,QAAS,CAC5B,KAAM,UACN,MAAO,OACP,IAAK,SACN,IACH,CACF,SACC,MAAI,WAAU,sBACb,SAACpH,EAAA,UAAE,KAAK,IACN,UAACE,MAAA,KAAE,UAAU,iBAAkB,GAC9BA,EAAA,YAAK,UAAU,kBAAkB,SAAO,YAAQ,MAChDuE,EAAA2C,EAAK,SAAL,YAAA3C,EAAa,OAAQ,kBACxB,CACF,GACC2C,EAAK,YAAcA,EAAK,WAAW,OAAS,GAC3CpH,EAAA,KAAC,MAAI,WAAU,sBACb,UAACE,MAAA,KAAE,UAAU,mBAAoB,GAChCA,EAAA,YAAK,UAAU,kBAAkB,SAAS,cAC3CA,MAAC,KAAE,KAAK,IAAK,WAAK,WAAW,CAAC,EAAE,IAAK,IACvC,EAEFF,OAAC,MAAI,WAAU,sBACb,UAACE,MAAA,KAAE,UAAU,iBAAkB,GAC9BA,EAAA,YAAK,UAAU,kBAAkB,SAAU,eAAQ,IACnDkH,EAAK,UAAY,EAAE,OACtB,IACF,QAIC,MAAI,WAAU,mBACb,SAACpH,EAAA,YAAI,UAAU,oDAEb,UAACA,OAAA,OAAI,UAAU,eACb,UAAAA,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,QACvB,UAAU,6CACV,oBAAkB,IAElB,UAACb,OAAA,QAAK,UAAU,oCACd,UAACE,MAAA,KAAE,UAAU,oCAAqC,GAAE,IAC7CkD,EAAE,mBAAmB,GAAK,gBACnC,EACApD,EAAA,KAAC,QACC,UAAU,kCACV,cAAY,OAEZ,UAACE,MAAA,KAAE,UAAU,oCAAqC,GAAE,IAC7CkD,EAAE,mBAAmB,GAAK,iBACnC,EACF,IAECuB,EAAAyC,GAAA,YAAAA,EAAM,aAAN,YAAAzC,EAAkB,WACjB3E,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,SAAS,IAAI,GACpE,UAAU,6CACV,oBAAkB,IAClB,MAAOA,EAAK,WAAW,SAAS,MAEhC,UAACpH,OAAA,QAAK,UAAU,oCACd,UAACE,MAAA,KAAE,UAAU,sCAAuC,GAAE,IAC/CkD,EAAE,eAAe,GAAK,YAC/B,EACApD,EAAA,KAAC,QACC,UAAU,kCACV,cAAY,OAEZ,UAACE,MAAA,KAAE,UAAU,sCAAuC,GAAE,IAC/CkD,EAAE,eAAe,GAAK,aAC/B,GACF,EAEJ,EAGClD,EAAA,WACE,WAAMmF,EAAA+B,GAAA,YAAAA,EAAA,yBAAA/B,EAAY,OACjBrF,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,KAAK,IAAI,GAChE,UAAU,6CACV,oBAAkB,IAClB,MAAOA,EAAK,WAAW,KAAK,MAE5B,UAACpH,OAAA,QAAK,UAAU,oCACb,UAAAoD,EAAE,WAAW,GAAK,OAAO,IAC1BlD,MAAC,IAAE,WAAU,uCAAwC,IACvD,EACAF,EAAA,KAAC,QACC,UAAU,kCACV,cAAY,OAEX,UAAAoD,EAAE,WAAW,GAAK,OAAO,IAC1BlD,MAAC,IAAE,WAAU,uCAAwC,KACvD,GAGN,IACF,CACF,GACF,GACF,EACAA,EAAA,IAAC,UAAQ,WAAU,uCACjB,SAAAA,EAAA,IAAC,MAAI,WAAU,qBACb,SAAAF,OAAC,MAAI,WAAU,MAEb,UAACA,OAAA,OAAI,UAAU,0DAEb,UAAAE,MAAC,OAAI,UAAU,2BACb,SAACF,EAAA,YAAI,UAAU,iBACZ,UAAAoH,EAAK,eACJlH,MAAC,MAAI,WAAU,iBACb,SAAAA,EAAA,IAAC,OACC,IAAKkH,EAAK,cACV,IAAKnC,EAAemC,EAAM,OAAO,EACjC,MAAO,KACP,OAAQ,MAEZ,QAID,MAAI,WAAU,aACZ,SAAenC,EAAAmC,EAAM,SAAS,EACjC,EAGAlH,EAAA,IAAC,OACC,UAAU,eACV,MAAO,CACL,WAAY,MACZ,SAAU,MACZ,EACA,wBAAyB,CACvB,OAAQ+E,EAAemC,EAAM,SAAS,EACxC,EACF,EACF,CACF,GAGApH,OAAC,MAAI,WAAU,iCACb,UAACA,OAAA,MAAG,UAAU,kBACX,UAAAoD,EAAE,qBAAqB,EAAG,IAC3BpD,OAAC,QAAM,WAAU,SAAS,gBACtB2H,EAAAP,EAAK,WAAL,YAAAO,EAAe,SAAU,EAAE,IAC/B,IACF,EACAzH,MAAC,KAAG,WAAU,oCACZ,SAAAA,MAAC0H,GAAS,UAAUR,EAAK,UAAY,EAAI,EAC3C,IACF,EAGApH,OAAC,MAAI,WAAU,iCACb,UAAAE,MAAC,KAAG,WAAU,kBACX,SAAAkD,EAAE,2BAA2B,EAChC,EAEClD,MAAA2H,GAAA,CAAK,SAAUT,EAAK,IAAM,IAE7B,EAGApH,OAAC,MAAI,WAAU,iBACZ,YAAA8H,EAAAV,GAAA,YAAAA,EAAM,aAAN,YAAAU,EAAkB,WACjB9H,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,SAAS,IAAI,GACpE,UAAU,sBAEV,UAAClH,MAAA,KAAE,UAAU,iBAAkB,GAAE,IAC1BkD,EAAE,eAAe,GAAK,YAC/B,IAED2E,EAAAX,GAAA,YAAAA,EAAM,aAAN,YAAAW,EAAkB,OACjB/H,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,KAAK,IAAI,GAChE,UAAU,uBAET,UAAAhE,EAAE,WAAW,GAAK,OAAO,IAC1BlD,MAAC,IAAE,WAAU,kBAAmB,KAClC,CAEJ,IAEF,EAGAA,MAAC,OAAI,UAAU,oBACb,eAAC8H,GAAQ,kBAAiB,0CAA2C,EAEvE,GAEF,EACF,EACF,IACF,QACC,SAAO,WAAU,6DAChB,SAAA9H,EAAA,IAACM,KAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHC5ZA,SAAwByH,IAAoB,CAC1C,KAAM,CAAE,EAAA7E,EAAG,KAAAzC,CAAK,EAAIC,GAAe,EAC7BC,EAAkBF,EAAK,UAAY,KAEzC,OAEIX,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAM,iBACN,YAAY,yHACZ,KAAK,iBACL,KAAK,UACL,SAAU,CAAC,iBAAkB,kBAAmB,YAAa,MAAM,EACrE,EAECD,MAAA,OAAI,UAAU,gBACb,SAACA,EAAA,WAAI,UAAU,YACb,SAACF,OAAA,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,8DACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GAEAoB,OAAC,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,eAAe,EACpB,EACCA,MAAA,OAAI,UAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,OAAI,UAAU,4CACb,SAACF,EAAA,UAAE,UAAU,sCACV,UAAAoD,EAAE,qBAAqB,EAAE,IAAE,IACvB,WAAO,mBAAmBvC,EAAiB,CAC9C,KAAM,UACN,MAAO,OACP,IAAK,SACN,GACH,EACF,GACF,EACAX,MAAC,MAAI,WAAU,cAAe,GAChC,GACF,EAGCA,MAAA,WAAQ,UAAU,uCACjB,eAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,OAAI,UAAU,uBAEb,UAACA,OAAA,OAAI,UAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,qBAAqB,CAAE,IACrD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QAEC,KAAG,WAAU,sBACX,SAAAA,EAAE,gCAAgC,EACrC,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,+BAA+B,EACpC,EACApD,OAAC,KAAG,WAAU,kBACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,gCAAgC,CAAE,GACxClD,EAAA,UAAI,SAAEkD,EAAA,gCAAgC,CAAE,GACxClD,EAAA,UAAI,SAAEkD,EAAA,gCAAgC,CAAE,GACxClD,MAAA,MAAI,SAAEkD,EAAA,gCAAgC,CAAE,IAC3C,QAEC,KAAG,WAAU,sBACX,SAAAA,EAAE,6BAA6B,EAClC,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,4BAA4B,EACjC,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACrClD,EAAA,UAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACrClD,EAAA,UAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACrClD,MAAA,MAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACxC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,mBAAmB,EACxB,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,kBAAkB,EACvB,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,MAAA,MAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC9B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,sBAAsB,EAC3B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,MAAA,MAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAClC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,wBAAwB,EAC7B,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,uBAAuB,CAC5B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,sBAAsB,EAC3B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,qBAAqB,EAC1B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,MAAA,MAAI,SAAEkD,EAAA,sBAAsB,CAAE,GACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,IACtD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,sBAAsB,EAC3B,EACApD,OAAC,MAAI,WAAU,YACb,UAAAE,MAAC,IACC,UAAAA,EAAA,IAAC,SAAO,wBAAY,GACtB,EACCA,EAAA,SAAG,SAAEkD,EAAA,yBAAyB,CAAE,GAChClD,EAAA,SAAG,SAAEkD,EAAA,uBAAuB,CAAE,GAC9BlD,MAAA,KAAG,SAAEkD,EAAA,uBAAuB,CAAE,GACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,GACtD,IACF,EACF,EACF,CACF,IACF,QAEC,SAAO,WAAU,6DAChB,SAAAlD,EAAA,IAACM,KAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHCjNA,SAAwB0H,IAAsB,CAC5C,KAAM,CAAE,EAAA9E,EAAG,KAAAzC,CAAK,EAAIC,GAAe,EAC7BC,EAAkBF,EAAK,UAAY,KAEzC,OAEIX,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAM,uBACN,YAAY,uIACZ,KAAK,mBACL,KAAK,UACL,SAAU,CACR,uBACA,QACA,YACA,oBACF,CACF,EAECD,MAAA,OAAI,UAAU,gBACb,SAACA,EAAA,WAAI,UAAU,YACb,SAACF,OAAA,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,8DACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GAEAoB,OAAC,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,aAAa,EAClB,EACCA,MAAA,OAAI,UAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,OAAI,UAAU,4CACb,SAACF,EAAA,UAAE,UAAU,sCACV,UAAAoD,EAAE,mBAAmB,EAAE,IAAE,IACrB,WAAO,mBAAmBvC,EAAiB,CAC9C,KAAM,UACN,MAAO,OACP,IAAK,SACN,GACH,EACF,GACF,EACAX,MAAC,MAAI,WAAU,cAAe,GAChC,GACF,EAGCA,MAAA,WAAQ,UAAU,uCACjB,eAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,OAAI,UAAU,uBAEb,UAACA,OAAA,OAAI,UAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,uBAAuB,CAC5B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,sBAAsB,EAC3B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,qBAAqB,EAC1B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,MAAA,MAAI,SAAEkD,EAAA,sBAAsB,CAAE,GACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,8BAA8B,EACnC,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,6BAA6B,EAClC,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,MAAA,MAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACzC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,oBAAoB,EACzB,EACApD,OAAC,KAAG,WAAU,kBACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,MAAA,MAAI,SAAEkD,EAAA,qBAAqB,CAAE,IAChC,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,qBAAqB,CAAE,IACrD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,gBAAgB,EACrB,QACC,IAAE,WAAU,kBAAmB,SAAAA,EAAE,cAAc,EAAE,EAClDpD,OAAC,IAAE,WAAU,kBACV,UAAAoD,EAAE,iBAAiB,EAAE,uDAExB,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,iBAAiB,CAAE,IACjD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,6BAA6B,EAClC,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,4BAA4B,CACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,sBAAsB,EAC3B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,MAAA,MAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAClC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,wBAAwB,EAC7B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,uBAAuB,EAC5B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,wBAAwB,CAAE,GAChClD,EAAA,UAAI,SAAEkD,EAAA,wBAAwB,CAAE,GAChClD,MAAA,MAAI,SAAEkD,EAAA,wBAAwB,CAAE,GACnC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,yBAAyB,EAC9B,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,wBAAwB,CAC7B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,IACtD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,oBAAoB,EACzB,EACApD,OAAC,MAAI,WAAU,YACb,UAAAE,MAAC,IACC,UAAAA,EAAA,IAAC,SAAO,wBAAY,GACtB,EACCA,EAAA,SAAG,SAAEkD,EAAA,uBAAuB,CAAE,GAC9BlD,EAAA,SAAG,SAAEkD,EAAA,qBAAqB,CAAE,GAC5BlD,MAAA,KAAG,SAAEkD,EAAA,qBAAqB,CAAE,GAC/B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,oBAAoB,CAAE,GACpD,IACF,EACF,EACF,CACF,IACF,QAEC,SAAO,WAAU,6DAChB,SAAAlD,EAAA,IAACM,KAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHCpPA,SAAwB2H,IAAmB,CACnC,MAAE,EAAA/E,CAAE,EAAIxC,GAAe,EAE7B,OAEIZ,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,GAAA,CACC,MAAM,uBACN,YAAY,6GACZ,KAAK,MACL,KAAK,UACL,MAAM,+BACN,SAAU,CAAC,MAAO,iBAAkB,QAAS,WAAW,EAC1D,EACCD,MAAA,OAAI,UAAU,gBACb,SAACA,EAAA,WAAI,UAAU,YACb,SAACF,OAAA,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAClB,MAAO,CAAE,SAAU,OAAQ,WAAY,KAAM,EAC9C,eAED,EACAA,MAAC,OAAI,UAAU,iBAAiB,iBAAe,OAC7C,SAAAA,MAAC,MAAI,WAAU,4CACb,SAAAA,EAAA,IAAC,KACC,UAAU,uCACV,MAAO,CAAE,SAAU,MAAO,EAC3B,4BAGH,CACF,GACAA,MAAC,MAAI,WAAU,cAAe,GAChC,GACF,EAGAA,MAAC,WAAQ,UAAU,uCACjB,eAAC,MAAI,WAAU,8BACb,SAACA,MAAA,OAAI,UAAU,MACb,SAAAA,MAAC,OAAI,UAAU,mCACb,gBAAC,MAAI,WAAU,eAAe,iBAAe,OAC3C,UAACF,OAAA,MAAG,UAAU,+BACZ,UAACE,EAAA,YAAK,UAAU,YAAY,SAAK,UAAO,wBAEvCA,EAAA,YAAK,UAAU,YAAY,SAAC,OAC/B,EACAF,OAAC,MAAI,WAAU,2BACb,UAACE,EAAA,SAAE,UAAU,QAAQ,SAIrB,uIACCA,EAAA,SAAE,UAAU,OAAO,SAGpB,iFACF,EACAF,OAAC,MAAI,WAAU,eACb,UAAAE,EAAA,IAAC0C,EAAA,CACC,GAAG,IACH,UAAU,wDACV,mBAAiB,IAEjB,SAAA5C,EAAA,KAAC,OAAK,WAAU,gBACd,UAACA,OAAA,QAAK,UAAU,kBACd,UAACE,MAAA,KAAE,UAAU,mCAAoC,GAAE,gBAErD,EACAF,EAAA,KAAC,QACC,UAAU,kBACV,cAAY,OAEZ,UAACE,MAAA,KAAE,UAAU,mCAAoC,GAAE,iBAErD,CACF,GACF,EACAA,EAAA,IAAC0C,EAAA,CACC,GAAG,YACH,UAAU,iDACV,mBAAiB,IAEjB,SAAA5C,EAAA,KAAC,OAAK,WAAU,gBACd,UAACE,EAAA,YAAK,UAAU,kBAAkB,SAElC,iBACAA,EAAA,IAAC,QACC,UAAU,kBACV,cAAY,OACb,yBAED,CACF,IACF,CACF,EACF,GACF,GACF,EACF,CACF,IACF,QAEC,SAAO,WAAU,6DAChB,SAAAA,EAAA,IAACM,KAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHC/HM4H,GAAa,IAAM,CACjB,MAAE,EAAAhF,CAAE,EAAIxC,GAAe,EACvB0F,EAAWC,GAAY,EACvB,CAAC8B,EAAUC,CAAW,EAAItH,WAAS,CACvC,MAAO,GACP,SAAU,GACX,EACK,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAK,EACtC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAE/BuH,EAAgB1F,GAAM,CACdyF,EAAA,CACV,GAAGD,EACH,CAACxF,EAAE,OAAO,IAAI,EAAGA,EAAE,OAAO,MAC3B,EAEGpB,KAAgB,EAAE,CACxB,EAEM+G,EAAe,MAAO3F,GAAM,CAChCA,EAAE,eAAe,EACjBrB,EAAW,EAAI,EACfE,EAAS,EAAE,EAEP,IACF,KAAM,CAAE,SAAAI,EAAU,KAAAC,CAAA,EAAS,MAAM0G,GAAQ,MAAMJ,CAAQ,EAEnDtG,EAAK,SAEM,qBAAQ,aAAcA,EAAK,KAAK,EAC7C,aAAa,QAAQ,YAAa,KAAK,UAAUA,EAAK,IAAI,CAAC,EAG3DuE,EAAS,kBAAkB,GAElB5E,EAAAK,EAAK,SAAW,cAAc,QAElCE,EAAK,CACJ,cAAM,eAAgBA,CAAG,EACjCP,EAAS,kCAAkC,SAC3C,CACAF,EAAW,EAAK,EAEpB,EAEA,OAEIxB,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAM,0BACN,YAAY,oDACZ,QAAS,GACX,EAGAxI,MAAC,OAAI,GAAG,OAAO,UAAU,OAEvB,SAAAA,MAAC,OAAK,IAAG,OAEP,SAAAA,EAAA,IAAC,WACC,UAAU,4EACV,GAAG,cAEH,SAACA,EAAA,WAAI,UAAU,qBACb,eAAC,MAAI,WAAU,MACb,SAAAA,MAAC,OAAI,UAAU,4CAEb,SAACF,EAAA,YAAI,UAAU,iBAEb,UAACA,OAAA,OAAI,UAAU,6BACb,UAACA,OAAA,OAAI,UAAU,0CACb,UAACE,EAAA,YAAK,UAAU,kBAAkB,SAAS,cAAO,UACpD,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAElC,2CACF,EAGCuB,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAACE,MAAA,KAAE,UAAU,YAAa,GACzBuB,CAAA,EACH,EAIDzB,EAAA,aAAK,UAAU,oBAAoB,SAAUwI,EAE5C,UAACxI,OAAA,OAAI,UAAU,aACb,UAAAE,MAAC,QAAM,SAAQ,QAAQ,UAAU,UAAU,SAE3C,kBACAA,EAAA,IAAC,SACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAY,gBACZ,MAAOmI,EAAS,MAChB,SAAUE,EACV,SAAQ,GACR,aAAa,SACf,EACF,EAGAvI,OAAC,MAAI,WAAU,aACb,UAAAE,MAAC,QAAM,SAAQ,WAAW,UAAU,UAAU,SAE9C,aACAA,EAAA,IAAC,SACC,KAAK,WACL,KAAK,WACL,GAAG,WACH,UAAU,8BACV,YAAY,WACZ,MAAOmI,EAAS,SAChB,SAAUE,EACV,SAAQ,GACR,aAAa,oBACf,EACF,EAGArI,MAAC,MAAI,WAAU,aACb,SAAAA,EAAA,IAAC,UACC,KAAK,SACL,UAAU,2DACV,SAAUqB,EAET,WAEGvB,OAAAC,EAAA,oBAACC,MAAA,KAAE,UAAU,4BAA6B,GAAI,iBAEhD,EAGEF,OAAAC,EAAA,oBAACC,MAAA,KAAE,UAAU,cAAe,GAAI,UAElC,IAGN,IACF,EAGAA,MAAC,OAAI,UAAU,oBACb,eAAC,IAAE,WAAU,mBAAmB,iDAEhC,EACF,IACF,EACF,EACF,CACF,KAEJ,CACF,IACF,CAEJ,2GChKMyI,GAAiB,IAAM,CAC3B,MAAMrC,EAAWC,GAAY,EACvB,CAACqC,EAAMC,CAAO,EAAI7H,WAAS,IAAI,EAC/B,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EA2C3C,OAzCAW,YAAU,IAAM,EACI,SAAY,CACtB,MAAAmH,EAAQ,aAAa,QAAQ,YAAY,EACzCC,EAAW,aAAa,QAAQ,WAAW,EAE7C,IAACD,GAAS,CAACC,EAAU,CACvBzC,EAAS,QAAQ,EACjB,OAGE,IAEF,KAAM,CAAE,SAAAxE,EAAU,KAAAC,CAAS,QAAM0G,GAAQ,MAAM,EAE3C3G,EAAS,IAAMC,EAAK,QACtB8G,EAAQ9G,EAAK,IAAI,GAGjB,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnCuE,EAAS,QAAQ,SAEZ7E,EAAO,CACN,cAAM,qBAAsBA,CAAK,EACzC,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnC6E,EAAS,QAAQ,SACjB,CACA9E,EAAW,EAAK,EAEpB,GAEU,GACT,CAAC8E,CAAQ,CAAC,EAQT/E,EAECrB,EAAA,WAAI,GAAG,OAAO,UAAU,OACvB,SAACA,MAAA,QAAK,GAAG,OACP,SAACA,EAAA,eAAQ,UAAU,eACjB,SAACA,EAAA,WAAI,UAAU,qBACb,SAACA,MAAA,OAAI,UAAU,MACb,SAACA,MAAA,OAAI,UAAU,qBACb,SAACF,OAAA,OAAI,UAAU,oBACb,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,kBACV,MAAO,CACL,SAAU,OACV,UAAW,0BACb,CACD,EACDA,MAAC,OAAI,UAAU,QACb,eAAC,MAAI,WAAU,2BAA2B,qBAAU,EACtD,GACF,EACF,EACF,EACF,EACF,EACF,GACF,EAMAF,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAM,8BACN,YAAY,mDACZ,QAAS,GACX,EAEA1I,OAACgJ,GAAY,OAAM,YAEjB,UAAChJ,OAAA,OAAI,UAAU,YAEb,UAAAE,MAAC,OAAI,UAAU,6BACb,SAACF,EAAA,YAAI,UAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,gBACb,eAAC,eAAa,MAAK,2BAA2B,CAChD,GACCA,EAAA,WAAI,UAAU,iBAAiB,SAAW,gBAC1CA,EAAA,WAAI,UAAU,kBAAkB,SAAC,OACpC,CACF,SAGC,MAAI,WAAU,6BACb,SAACF,EAAA,YAAI,UAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,gBACb,eAAC,eAAa,MAAK,oBAAoB,CACzC,GACCA,EAAA,WAAI,UAAU,iBAAiB,SAAU,eACzCA,EAAA,WAAI,UAAU,kBAAkB,SAAC,OACpC,CACF,SAGC,MAAI,WAAU,6BACb,SAACF,EAAA,YAAI,UAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,gBACb,eAAC,eAAa,MAAK,wBAAwB,CAC7C,GACCA,EAAA,WAAI,UAAU,iBAAiB,SAAQ,aACvCA,EAAA,WAAI,UAAU,kBAAkB,SAAC,OACpC,CACF,SAGC,MAAI,WAAU,6BACb,SAACF,EAAA,YAAI,UAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,gBACb,eAAC,eAAa,MAAK,iBAAiB,CACtC,GACCA,EAAA,WAAI,UAAU,iBAAiB,SAAU,eACzCA,EAAA,WAAI,UAAU,kBAAkB,SAAC,OACpC,CACF,IACF,QAGC,MAAI,WAAU,MACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACE,MAAA,OAAI,UAAU,QACb,SAAAA,EAAA,IAAC,MAAG,UAAU,0CAA0C,yBAExD,CACF,GAEAF,OAAC,MAAI,WAAU,MAEb,UAAAE,MAAC,OAAI,UAAU,6BACb,SAACF,EAAA,YAAI,UAAU,iCACb,UAAAE,MAAC,OAAI,UAAU,oBACb,eAAC,eAAa,MAAK,wBAAwB,CAC7C,GACCA,EAAA,UAAG,UAAU,8BAA8B,SAAa,kBACxDA,EAAA,WAAI,UAAU,qBAAqB,SAGpC,0EACAA,MAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,iBAAiB,EACzC,UAAU,kCACX,wBAGH,IACF,CACF,SAGC,MAAI,WAAU,6BACb,SAACtG,EAAA,YAAI,UAAU,iCACb,UAAAE,MAAC,OAAI,UAAU,oBACb,eAAC,eAAa,MAAK,uBAAuB,CAC5C,GACCA,EAAA,UAAG,UAAU,8BAA8B,SAAY,iBACvDA,EAAA,WAAI,UAAU,qBAAqB,SAGpC,oEACAA,MAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,cAAc,EACtC,UAAU,kCACX,yBAGH,IACF,CACF,SAGC,MAAI,WAAU,6BACb,SAACtG,EAAA,YAAI,UAAU,iCACb,UAAAE,MAAC,OAAI,UAAU,oBACb,eAAC,eAAa,MAAK,+BAA+B,CACpD,GACCA,EAAA,UAAG,UAAU,8BAA8B,SAE5C,sBACCA,EAAA,WAAI,UAAU,qBAAqB,SAGpC,0EACAA,MAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,mBAAmB,EAC3C,UAAU,kCACX,6BAGH,IACF,CACF,SAGC,MAAI,WAAU,6BACb,SAACtG,EAAA,YAAI,UAAU,iCACb,UAAAE,MAAC,OAAI,UAAU,oBACb,eAAC,eAAa,MAAK,wBAAwB,CAC7C,GACCA,EAAA,UAAG,UAAU,8BAA8B,SAE5C,uBACCA,EAAA,WAAI,UAAU,qBAAqB,SAEpC,iEACAA,MAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,iBAAiB,EACzC,UAAU,kCACX,4BAGH,IACF,CACF,SAGC,MAAI,WAAU,6BACb,SAACtG,EAAA,YAAI,UAAU,iCACb,UAAAE,MAAC,OAAI,UAAU,oBACb,eAAC,eAAa,MAAK,qBAAqB,CAC1C,GACCA,EAAA,UAAG,UAAU,8BAA8B,SAAS,cACpDA,EAAA,WAAI,UAAU,qBAAqB,SAGpC,sEACAA,MAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,kBAAkB,EAC1C,UAAU,kCACX,2BAGH,IACF,CACF,GACF,IACF,CACF,GACF,IACF,CAEJ,2GClQM2C,GAAiB,IAAM,CAC3B,MAAM3C,EAAWC,GAAY,EACvB,CAAC/B,EAAO0E,CAAQ,EAAIlI,WAAS,EAAE,EAC/B,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAACmI,EAASC,CAAU,EAAIpI,WAAS,CACrC,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,GACT,EACK,CAAC0D,EAAY2E,CAAa,EAAIrI,WAAS,EAAE,EAE/CW,YAAU,IAAM,CACJ2H,EAAA,GACT,CAACH,CAAO,CAAC,EAEZ,MAAMG,EAAY,SAAY,CACxB,IACF9H,EAAW,EAAI,EAEf,MAAM6C,EAAS,CAAC,EACT,eAAQ8E,CAAO,EAAE,QAAQ,CAAC,CAACI,EAAKC,CAAK,IAAM,CAC5CA,GAASA,IAAU,QACrBnF,EAAOkF,CAAG,EAAIC,EAChB,CACD,EAED,KAAM,CAAE,SAAA1H,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,SAASpF,CAAM,EAErDtC,EAAK,SACEmH,EAAAnH,EAAK,KAAK,KAAK,EACVsH,EAAAtH,EAAK,KAAK,UAAU,GAEzBL,EAAAK,EAAK,SAAW,sBAAsB,QAE1CN,EAAO,CACN,cAAM,oBAAqBA,CAAK,EACxCC,EAAS,kCAAkC,SAC3C,CACAF,EAAW,EAAK,EAEpB,EAEMkI,EAAe,MAAOC,GAAW,CACrC,GACG,QACC,iFAMA,IACF,KAAM,CAAE,SAAA7H,EAAU,KAAAC,CAAA,EAAS,MAAMwC,GAAQ,WAAWoF,CAAM,EAEtD5H,EAAK,QACGuH,EAAA,EAED5H,EAAAK,EAAK,SAAW,uBAAuB,QAE3CN,EAAO,CACN,cAAM,gBAAiBA,CAAK,EACpCC,EAAS,kCAAkC,EAE/C,EAEMkI,EAAyB,MAAOD,GAAW,CAC3C,IACF,KAAM,CAAE,SAAA7H,EAAU,KAAAC,CAAA,EAAS,MAAMwC,GAAQ,iBAAiBoF,CAAM,EAE5D5H,EAAK,QACGuH,EAAA,EAED5H,EAAAK,EAAK,SAAW,6BAA6B,QAEjDN,EAAO,CACN,cAAM,2BAA4BA,CAAK,EAC/CC,EAAS,kCAAkC,EAE/C,EAEMmI,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,UACT,EAGGC,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,wBAAwBD,CAAQ,GAT3B,KAYlBE,EAAkBhF,GACjBA,EAAK,UAYNA,EAAK,aAAe,IAAI,KAAKA,EAAK,WAAW,EAAQ,SAErDlF,EAAA,KAAC,OAAK,WAAU,mBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EAAe,aAElB,EAKFF,EAAA,KAAC,OAAK,WAAU,mBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EAAe,aAElB,EA7BEF,EAAA,KAAC,OAAK,WAAU,qBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,2BACL,UAAU,OACX,EAAe,SAElB,EA2BN,OAEIF,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAM,4BACN,YAAY,uCACZ,QAAS,GACX,EAEA1I,OAACgJ,GAAY,OAAM,aAEjB,UAAA9I,MAAC,OAAI,UAAU,QACb,SAACF,EAAA,YAAI,UAAU,yBACb,UAACE,MAAA,OAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,mFAGlC,CACF,GACAA,MAAC,MAAI,WAAU,8BACb,SAAAF,EAAA,KAAC,UACC,QAAS,IAAMsG,EAAS,iBAAiB,EACzC,UAAU,kDAEV,UAAApG,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,aAGpB,IACF,CACF,GAECA,EAAA,WAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,aACnD,SAAAF,OAAC,MAAI,WAAU,UACb,UAACA,OAAA,OAAI,UAAU,2BACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAY,iBAC1CA,EAAA,IAAC,SACC,KAAK,OACL,MAAOiJ,EAAQ,OACf,SAAWtG,GACTuG,EAAYe,IAAU,CACpB,GAAGA,EACH,OAAQtH,EAAE,OAAO,MACjB,KAAM,GACN,EAEJ,UAAU,eACV,YAAY,sBACd,EACF,EAEA7C,OAAC,MAAI,WAAU,0BACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAM,WACpCF,EAAA,KAAC,UACC,MAAOmJ,EAAQ,OACf,SAAWtG,GACTuG,EAAYe,IAAU,CACpB,GAAGA,EACH,OAAQtH,EAAE,OAAO,MACjB,KAAM,GACN,EAEJ,UAAU,eAEV,UAAC3C,EAAA,cAAO,MAAM,MAAM,SAAS,cAC5BA,EAAA,cAAO,MAAM,YAAY,SAAS,cAClCA,EAAA,cAAO,MAAM,QAAQ,SAAM,aAC9B,EACF,EAEAF,OAAC,MAAI,WAAU,0BACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAQ,aACtCF,EAAA,KAAC,UACC,MAAOmJ,EAAQ,MACf,SAAWtG,GACTuG,EAAYe,IAAU,CACpB,GAAGA,EACH,MAAO,SAAStH,EAAE,OAAO,KAAK,EAC9B,KAAM,GACN,EAEJ,UAAU,eAEV,UAAC3C,EAAA,cAAO,MAAO,GAAI,SAAE,OACpBA,EAAA,cAAO,MAAO,GAAI,SAAE,OACpBA,EAAA,cAAO,MAAO,GAAI,SAAE,SACvB,CACF,IACF,CACF,GAGCuB,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAAAE,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EACAuB,CAAA,EACH,EAIDvB,EAAA,WAAI,UAAU,cACZ,SACCqB,EAAAvB,EAAA,KAAC,MAAI,WAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,YAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,EAC/C,EACAA,EAAA,WAAI,UAAU,2BAA2B,SAAgB,sBAC5D,EACEsE,EAAM,SAAW,EAClBxE,EAAA,YAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,aACnD,UAAAE,EAAA,IAAC,gBACC,KAAK,2BACL,UAAU,iCACX,EACAA,EAAA,WAAI,UAAU,iCAAiC,SAEhD,wBACAA,MAAC,IAAE,WAAU,sBACV,SAAAiJ,EAAQ,QAAUA,EAAQ,SAAW,MAClC,oEACA,+CACN,GACAnJ,EAAA,KAAC,UACC,QAAS,IAAMsG,EAAS,iBAAiB,EACzC,UAAU,kCAEV,UAAApG,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,sBAElB,EACF,EAIEF,OAAAC,EAAA,oBAACC,EAAA,WAAI,UAAU,oBACb,SAACA,EAAA,WAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,WAAU,QACf,UAACE,EAAA,aACC,gBAAC,KACC,WAAAA,MAAC,MAAG,SAAK,UACTA,MAAC,MAAG,SAAM,WACVA,MAAC,MAAG,SAAM,WACVA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAO,aACb,CACF,GACCA,EAAA,aACE,SAAMsE,EAAA,IAAKU,GAAS,CACnB,MAAMkF,EACJlF,EAAK,aAAa,KAAM,GAAM,EAAE,WAAa,IAAI,GACjDA,EAAK,aAAa,CAAC,EAErB,cACG,KACC,WAAAhF,MAAC,KACC,UAAAF,OAAC,MAAI,WAAU,4BACZ,UAAAkF,EAAK,eACJhF,EAAA,IAAC,OACC,UAAU,eACV,IAAK6J,EAAY7E,EAAK,aAAa,EACnC,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAUrC,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,OAC3B,CACF,SAED,MACC,WAAA3C,MAAC,MAAI,WAAU,UACZ,UAAAkK,GAAA,YAAAA,EAAoB,QAAS,WAChC,EACApK,OAAC,QAAM,WAAU,aAAa,cAC1BkF,EAAK,KACT,GACF,IACF,CACF,UACC,KACE,WAAAgF,EAAehF,CAAI,EACnBA,EAAK,UACHlF,OAAA,QAAK,UAAU,wBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,WAElB,IAEJ,QACC,KAAI,UAAAgF,EAAK,OAAO,MAAQA,EAAK,OAAO,MAAM,EAC1ChF,EAAA,UAAI,SAAW2J,EAAA3E,EAAK,SAAS,EAAE,QAC/B,KACC,UAAAlF,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,UAAAE,EAAA,IAAC,UACC,QAAS,IACPoG,EAAS,oBAAoBpB,EAAK,EAAE,EAAE,EAExC,UAAU,iCACV,MAAM,OAEN,SAAAhF,MAAC,eAAa,MAAK,gBAAiB,GACtC,EAEAA,EAAA,IAAC,UACC,QAAS,IACP0J,EAAuB1E,EAAK,EAAE,EAEhC,UAAW,cACTA,EAAK,UACD,sBACA,qBACN,GACA,MACEA,EAAK,UAAY,YAAc,UAGjC,SAAAhF,EAAA,IAAC,gBACC,KACEgF,EAAK,UACD,wBACA,kBAEP,CACH,EAEAhF,EAAA,IAAC,UACC,QAAS,IAAMwJ,EAAaxE,EAAK,EAAE,EACnC,UAAU,gCACV,MAAM,SAEN,SAAAhF,MAAC,eAAa,MAAK,4BAA6B,IAClD,EACF,CACF,KApFOgF,EAAK,EAqFd,EAEH,CACH,GACF,EACF,GACF,EAGAhF,EAAA,IAAC,MAAI,WAAU,YACb,SAAAA,MAAC,MAAI,WAAU,UACZ,SAAAsE,EAAM,IAAKU,GAAS,CACnB,MAAMkF,EACJlF,EAAK,aAAa,KAAM,GAAM,EAAE,WAAa,IAAI,GACjDA,EAAK,aAAa,CAAC,EAErB,OACGhF,MAAA,OAAkB,UAAU,SAC3B,eAAC,MAAI,WAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,gBACb,SAACF,OAAA,OAAI,UAAU,yBACb,UAAAE,MAAC,OAAI,UAAU,cACb,SAACF,EAAA,YAAI,UAAU,4BACZ,UAAAkF,EAAK,eACJhF,EAAA,IAAC,OACC,UAAU,eACV,IAAK6J,EAAY7E,EAAK,aAAa,EACnC,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAUrC,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,OAC3B,CACF,EAEF7C,OAAC,MAAI,WAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eACX,UAAAkK,GAAA,YAAAA,EAAoB,QAAS,WAChC,EACApK,OAAC,QAAM,WAAU,aAAa,cAC1BkF,EAAK,KACT,GACF,IACF,CACF,GAEAlF,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,kBACC,MACE,WAAAgK,EAAehF,CAAI,EACnBA,EAAK,UACHlF,OAAA,QAAK,UAAU,wBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,WAElB,GAEJ,IACF,EAEAF,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,iBACC,QACE,UAAAgF,EAAK,OAAO,MAAQA,EAAK,OAAO,KACnC,IACF,EAEAlF,OAAC,MAAI,WAAU,uBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,YACCA,EAAA,aAAO,SAAW2J,EAAA3E,EAAK,SAAS,CAAE,IACrC,QAEC,MAAI,WAAU,SACb,SAAClF,EAAA,YAAI,UAAU,yBACb,UAAAA,EAAA,KAAC,UACC,QAAS,IACPsG,EAAS,oBAAoBpB,EAAK,EAAE,EAAE,EAExC,UAAU,2CACV,MAAM,OAEN,UAAAhF,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,QAElB,EAEAF,EAAA,KAAC,UACC,QAAS,IACP4J,EAAuB1E,EAAK,EAAE,EAEhC,UAAW,wBACTA,EAAK,UACD,sBACA,qBACN,GACA,MACEA,EAAK,UAAY,YAAc,UAGjC,UAAAhF,EAAA,IAAC,gBACC,KACEgF,EAAK,UACD,wBACA,iBAEN,UAAU,OACX,EACAA,EAAK,UAAY,OAAS,QAC7B,EAEAlF,EAAA,KAAC,UACC,QAAS,IAAM0J,EAAaxE,EAAK,EAAE,EACnC,UAAU,0CACV,MAAM,SAEN,UAAAhF,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EAAe,WAElB,EACF,CACF,IACF,CACF,GACF,CAzHQ,EAAAgF,EAAK,EA0Hf,CAEH,EACH,EACF,IACF,CAEJ,GAGCR,EAAW,MAAQ,GACjB1E,EAAA,YAAI,UAAU,+BACb,UAAAE,MAAC,OAAI,UAAU,+BACb,SAACF,EAAA,UAAE,UAAU,kDAAkD,sBACnD0E,EAAW,KAAO,GAAKA,EAAW,MAAQ,EAAE,MAAI,IACzD,KAAK,IAAIA,EAAW,KAAOA,EAAW,MAAOA,EAAW,KAAK,EAAG,IAAI,MACjEA,EAAW,MAAM,YACvB,CACF,GACAxE,EAAA,IAAC,MAAI,WAAU,kBACb,SAAAA,EAAA,IAAC,MAAI,cAAW,wBACd,SAAAF,OAAC,KAAG,WAAU,8EACZ,UAAAE,EAAA,IAAC,MACC,UAAW,aACTwE,EAAW,MAAQ,EAAI,WAAa,EACtC,GAEA,SAAAxE,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IACPkJ,EAAYe,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,IAEzD,SAAUzF,EAAW,MAAQ,EAC9B,qBAED,CACF,QAEC,KAAG,WAAU,mBACZ,SAAC1E,EAAA,aAAK,UAAU,YAAY,kBACpB0E,EAAW,KAAK,OAAKA,EAAW,OACxC,CACF,GAEAxE,EAAA,IAAC,MACC,UAAW,aACTwE,EAAW,MAAQA,EAAW,MAAQ,WAAa,EACrD,GAEA,SAAAxE,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IACPkJ,EAAYe,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,IAEzD,SAAUzF,EAAW,MAAQA,EAAW,MACzC,iBAED,EACF,CACF,EACF,EACF,GACF,GAEJ,IACF,CAEJ,2GCjlBM2F,GAAkB,IAAM,eAC5B,KAAM,CAAE,EAAAjH,EAAG,KAAAzC,CAAK,EAAIC,GAAe,EAC7B0F,EAAWC,GAAY,EACvB,CAAE,GAAAF,CAAG,EAAIN,GAAU,EACnBuE,EAAY,EAAQjE,EAGpB0D,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,wBAAwBD,CAAQ,GAT3B,KAYlB,CAACzI,EAASC,CAAU,EAAIR,WAAS,EAAK,EACtC,CAACuJ,EAAQC,CAAS,EAAIxJ,WAAS,EAAK,EACpC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAACyJ,EAASC,CAAU,EAAI1J,WAAS,EAAE,EAGnC,CAAC2J,CAAkB,EAAI3J,WAAS,IAAM,OAAO,KAAKL,EAAK,MAAM,IAAI,CAAC,EAGlE,CAAC0H,EAAUC,CAAW,EAAItH,WAAS,IAAM,CAE7C,MAAM4J,EAAsB,CAAC,EACV,OAAAD,EAAA,QAASE,GAAS,CACnCD,EAAoBC,CAAI,EAAI,CAC1B,MAAO,GACP,QAAS,GACT,QAAS,GACT,UAAW,GACX,SAAU,GACV,SAAU,EACZ,EACD,EAEM,CACL,KAAM,GACN,SAAU,GACV,UAAW,GACX,YAAa,GACb,cAAe,KACf,iBAAkB,GAClB,SAAU,GACV,YAAa,CAAC,EACd,OAAQ,CAAC,EACT,aAAcD,CAChB,EACD,EAEK,CAACE,EAAgBC,CAAiB,EAAI/J,WAAS,IAAI,EACnD,CAACG,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAAC8C,EAAMC,CAAO,EAAI/C,WAAS,EAAE,EAC7B,CAACgK,EAAcC,CAAe,EAAIjK,WAAS,IAAI,EAGrDW,YAAU,IAAM,EACG,SAAY,CACvB,IAMF,GALAH,EAAW,EAAI,EACfE,EAAS,EAAE,EAIP,CADU,aAAa,QAAQ,YAAY,EACnC,CACVA,EACE,6DACF,EACAF,EAAW,EAAK,EAChB,OAIF,KAAM,CAACoD,EAAkBC,CAAU,EAAI,MAAM,QAAQ,IAAI,CACvD4E,EAAS,cAAc,EACvBA,EAAS,QAAQ,EAClB,EAGD,GAAI7E,EAAiB,SAAS,IAAMA,EAAiB,KACnDxD,EAAcwD,EAAiB,KAAK,MAAQ,EAAE,MACzC,CAML,GALQ,cACN,yBACAA,EAAiB,SAAS,OAC1BA,EAAiB,SAAS,UAC5B,EAEEA,EAAiB,SAAS,SAAW,KACrCA,EAAiB,SAAS,SAAW,IACrC,CACAlD,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,OAEFN,EAAc,EAAE,EAIlB,GAAIyD,EAAW,SAAS,IAAMA,EAAW,KACvCd,EAAQc,EAAW,KAAK,MAAQ,EAAE,MAC7B,CAML,GALQ,cACN,mBACAA,EAAW,SAAS,OACpBA,EAAW,SAAS,UACtB,EAEEA,EAAW,SAAS,SAAW,KAC/BA,EAAW,SAAS,SAAW,IAC/B,CACAnD,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,OAEFqC,EAAQ,EAAE,EAIZ,GAAIuG,EAAW,CACb,KAAM,CAAE,SAAUY,GAAS,KAAMC,CAAS,EAAI,MAAM1B,EAAS,QAC3DpD,CACF,EAEI,GAAA6E,GAAQ,IAAMC,EAAS,QACrB,IACF,MAAMjG,EAAOiG,EAAS,KAGhBC,EAAkB,CAAC,EACrBlG,EAAK,cAAgB,MAAM,QAAQA,EAAK,YAAY,GACjDA,EAAA,aAAa,QAAS9B,GAAM,CACfA,IAAE,QAAQ,EAAIA,CAAA,CAC/B,EAGHkF,EAAa6B,IAAU,CACrB,GAAGA,EACH,KAAMjF,EAAK,MAAQ,GACnB,SAAUA,EAAK,UAAY,GAC3B,UAAWA,EAAK,WAAa,GAC7B,YAAaA,EAAK,YACd,IAAI,KAAKA,EAAK,WAAW,EAAE,YAAY,EAAE,MAAM,EAAG,EAAE,EACpD,GACJ,cAAe,KACf,iBAAkBA,EAAK,kBAAoB,GAC3C,SAAUA,EAAK,UAAY,GAC3B,YAAaA,EAAK,WACdA,EAAK,WAAW,IAAKQ,GAAMA,EAAE,EAAE,EAC/B,CAAC,EACL,OAAQR,EAAK,KAAOA,EAAK,KAAK,IAAK9B,GAAMA,EAAE,EAAE,EAAI,CAAC,EAClD,aAAc,CAAE,GAAG+G,EAAK,aAAc,GAAGiB,CAAgB,GACzD,EAEElG,EAAK,eACS+F,EAAAlB,EAAY7E,EAAK,aAAa,CAAC,QAE1CmG,EAAW,CACV,cAAM,iCAAkCA,CAAS,EACzD3J,EAAS,oDAAoD,OAGvD,cACN,mBACAwJ,GAAQ,OACRA,GAAQ,UACV,EACAxJ,EACEyJ,EAAS,SACP,wBAAwBD,GAAQ,MAAM,IAAIA,GAAQ,UAAU,EAChE,CACF,QAEKzJ,EAAO,CACN,cAAM,sBAAuBA,CAAK,EACtCA,EAAM,SAAWA,EAAM,QAAQ,SAAS,OAAO,EACjDC,EACE,2FACF,EAEAA,EAAS,wCAAwC,CACnD,QACA,CACAF,EAAW,EAAK,EAEpB,GAES,GACR,CAAC6E,EAAIiE,CAAS,CAAC,EAEZ,MAAAgB,EAAoB,CAACnG,EAAOqE,IAAU,CAC1ClB,EAAa6B,IAAU,CACrB,GAAGA,EACH,CAAChF,CAAK,EAAGqE,CAAA,EACT,CACJ,EAEM+B,EAA0B,CAAC3L,EAAUuF,EAAOqE,IAAU,CAC1DlB,EAAa6B,IAAU,CACrB,GAAGA,EACH,aAAc,CACZ,GAAGA,EAAK,aACR,CAACvK,CAAQ,EAAG,CACV,GAAGuK,EAAK,aAAavK,CAAQ,EAC7B,CAACuF,CAAK,EAAGqE,CAAA,CACX,CACF,EACA,CACJ,EAEMgC,GAAqB3I,GAAM,CAC/B,MAAM4I,EAAO5I,EAAE,OAAO,MAAM,CAAC,EAC7B,GAAI4I,EAAM,CACRnD,EAAa6B,IAAU,CACrB,GAAGA,EACH,cAAesB,CAAA,EACf,EAGI,MAAAC,EAAS,IAAI,WACZA,EAAA,OAAU7I,GAAM,CACLA,IAAE,OAAO,MAAM,CACjC,EACA6I,EAAO,cAAcD,CAAI,EAE7B,EAEMjD,GAAe,MAAO3F,GAAM,CAChCA,EAAE,eAAe,EACjB2H,EAAU,EAAI,EACd9I,EAAS,EAAE,EACXgJ,EAAW,EAAE,EAET,IACI,MAAA5B,EAAQ,aAAa,QAAQ,YAAY,EACzC6C,EAAiB,IAAI,SAGZA,EAAA,OAAO,OAAQtD,EAAS,IAAI,EAC5BsD,EAAA,OAAO,WAAYtD,EAAS,QAAQ,EACpCsD,EAAA,OAAO,YAAatD,EAAS,SAAS,EACjDA,EAAS,aACIsD,EAAA,OAAO,cAAetD,EAAS,WAAW,EAE5CsD,EAAA,OAAO,mBAAoBtD,EAAS,gBAAgB,EAC/DA,EAAS,UACIsD,EAAA,OAAO,WAAYtD,EAAS,QAAQ,EAItCsD,EAAA,OACb,cACA,KAAK,UAAUtD,EAAS,WAAW,CACrC,EACAsD,EAAe,OAAO,SAAU,KAAK,UAAUtD,EAAS,MAAM,CAAC,EAChDsD,EAAA,OACb,eACA,KAAK,UAAUtD,EAAS,YAAY,CACtC,EAGIA,EAAS,eACIsD,EAAA,OAAO,gBAAiBtD,EAAS,aAAa,EAI3D,IAAAd,EACA+C,EACF/C,EAAS,MAAMhD,GAAQ,WAAW8B,EAAIsF,CAAc,EAE3CpE,EAAA,MAAMhD,GAAQ,WAAWoH,CAAc,EAG5C,MAAE,SAAA7J,GAAU,KAAAC,CAAA,EAASwF,EAE3B,GAAIzF,GAAS,IAAMC,GAAQA,EAAK,QAC9B2I,EACE,aAAaJ,EAAY,UAAY,SAAS,gBAChD,EACA,WAAW,IAAM,CACfhE,EAAS,cAAc,GACtB,GAAI,MACF,CACL,MAAMsF,GACJ7J,GAAA,YAAAA,EAAM,UACN,aAAauI,EAAY,SAAW,QAAQ,aAC9C5I,EAASkK,CAAY,SAEhBnK,EAAO,CACN,cAAM,cAAeA,CAAK,EAClCC,EAAS,kCAAkC,SAC3C,CACA8I,EAAU,EAAK,EAEnB,EAEA,OAEIxK,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAO,GAAG4B,EAAY,OAAS,QAAQ,qBACvC,YAAY,+CACZ,QAAS,GACX,EAEApK,EAAA,IAAC8I,GAAA,CACC,MAAOsB,EAAY,iBAAmB,uBAEtC,SAACtK,OAAA,QAAK,SAAUwI,GAAc,UAAU,aAErC,UAAA/G,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAAAE,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EACAuB,CAAA,EACH,EAGDgJ,GACEzK,OAAA,OAAI,UAAU,4BAA4B,KAAK,QAC9C,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EACAuK,CAAA,EACH,EAIFzK,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,sBACL,UAAU,uBACX,EAAe,kBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAElC,sDACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,cAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,MAAOmI,EAAS,KAChB,SAAWxF,GAAMyI,EAAkB,OAAQzI,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,gBACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAGxC,0EACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EAAe,uBAElB,EACAA,EAAA,IAAC,SACC,KAAK,SACL,MAAOmI,EAAS,SAChB,SAAWxF,GACTyI,EAAkB,WAAYzI,EAAE,OAAO,KAAK,EAE9C,UAAU,eACV,YAAY,IACZ,IAAI,IACJ,IAAI,KACN,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,0CACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,sBACL,UAAU,OACX,EAAe,wBAElB,EACAA,EAAA,IAAC,SACC,KAAK,iBACL,MAAOmI,EAAS,YAChB,SAAWxF,GACTyI,EAAkB,cAAezI,EAAE,OAAO,KAAK,EAEjD,UAAU,eACZ,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,kEACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,gBAElB,EACAF,OAAC,MAAI,WAAU,2BACb,UAACA,OAAA,OAAI,UAAU,aACb,UAAAE,EAAA,IAAC,SACC,KAAK,WACL,GAAG,WACH,QAASmI,EAAS,SAClB,SAAWxF,GACTyI,EAAkB,WAAYzI,EAAE,OAAO,OAAO,EAEhD,UAAU,mBACZ,EACC7C,EAAA,cAAM,UAAU,mBAAmB,QAAQ,WAC1C,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,iBAElB,EACCA,EAAA,aAAM,UAAU,+BAA+B,SAEhD,gDACF,EAEAF,OAAC,MAAI,WAAU,aACb,UAAAE,EAAA,IAAC,SACC,KAAK,WACL,GAAG,YACH,QAASmI,EAAS,UAClB,SAAWxF,GACTyI,EAAkB,YAAazI,EAAE,OAAO,OAAO,EAEjD,UAAU,mBACZ,EACC7C,EAAA,cAAM,UAAU,mBAAmB,QAAQ,YAC1C,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EAAe,aAElB,EACCA,EAAA,aAAM,UAAU,+BAA+B,SAEhD,yCACF,GACF,GACF,GACF,IACF,EAGAF,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,uBACX,EAAe,kBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAGlC,wEACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EAAe,gBAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,OAAO,UACP,SAAUsL,GACV,UAAU,eACZ,EACCtL,EAAA,aAAM,UAAU,uBAAuB,SAGxC,qEACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,YAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,MAAOmI,EAAS,iBAChB,SAAWxF,GACTyI,EAAkB,mBAAoBzI,EAAE,OAAO,KAAK,EAEtD,UAAU,eACV,YAAY,uCACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,mDACF,EAEC8K,GACChL,EAAA,KAAC,MAAI,WAAU,SACb,UAACE,MAAA,OAAI,UAAU,QACb,SAAAA,EAAA,IAAC,SAAM,UAAU,aAAa,yBAAa,CAC7C,GACAA,MAAC,MAAI,WAAU,cACb,SAAAA,EAAA,IAAC,OACC,IAAK8K,EACL,IAAI,UACJ,UAAU,gBACV,MAAO,CAAE,SAAU,QAAS,OAAQ,MAAO,GAE/C,GACF,GAEJ,IACF,EAGAhL,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAACE,MAAA,KAAE,UAAU,+BAAgC,GAAI,4BAEnD,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAGlC,iFACF,CACF,SAGC,MAAI,WAAU,sBACZ,SAAmByK,EAAA,IAAKE,GACvB7K,EAAA,KAAC,UAEC,KAAK,SACL,QAAS,IAAM+K,EAAkBF,CAAI,EACrC,UAAW,gBACTC,IAAmBD,EAAO,SAAW,EACvC,GAEA,UAAC3K,MAAA,KAAE,UAAU,eAAgB,GAC5B2K,EAAK,YAAY,EACjBA,IAAS,MACR3K,MAAC,OAAK,WAAU,aAAa,SAAU,iBAVpC2K,CAaR,GACH,EAGA7K,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,cAAe,GAAI,UACxB4K,EAAe,YAAY,EAAE,IACpCA,IAAmB,MAClB5K,MAAC,OAAK,WAAU,mBAAmB,SAAC,OAExC,EACAA,EAAA,IAAC,SACC,KAAK,OACL,QAAOuE,EAAA4D,EAAS,aAAayC,CAAc,IAApC,YAAArG,EAAuC,QAAS,GACvD,SAAW5B,GACT0I,EACET,EACA,QACAjI,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,wBACZ,SAAUiI,IAAmB,KAC/B,EACA9K,OAAC,QAAM,WAAU,uBAAuB,gDACF,IACnC8K,EAAe,YAAY,EAC9B,IACF,EAEA9K,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,cAAe,GAAI,YACtB4K,EAAe,YAAY,EAAE,KACzC,EACA5K,EAAA,IAAC,YACC,QAAOyE,EAAA0D,EAAS,aAAayC,CAAc,IAApC,YAAAnG,EAAuC,UAAW,GACzD,SAAW9B,GACT0I,EACET,EACA,UACAjI,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,qCACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAGxC,iFACF,EAEAF,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,2BACL,UAAU,OACX,EAAe,YACN4K,EAAe,YAAY,EAAE,IACtCA,IAAmB,MAClB5K,MAAC,OAAK,WAAU,mBAAmB,SAAC,OAExC,EACAA,EAAA,IAAC2L,GAAA,CACC,UAASxG,EAAAgD,EAAS,aAAayC,CAAc,IAApC,YAAAzF,EAAuC,UAAW,GAC3D,SAAWyG,GACTP,EAAwBT,EAAgB,UAAWgB,CAAI,EAEzD,YAAY,8GACd,EACA9L,OAAC,QAAM,WAAU,uBACf,UAAAE,EAAA,IAAC,gBACC,KAAK,yBACL,UAAU,OACX,EAAe,qJAIlB,IACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,aAAc,GAAI,eAClB4K,EAAe,YAAY,EAAE,KAC5C,EACA5K,EAAA,IAAC,SACC,KAAK,OACL,QAAOyH,EAAAU,EAAS,aAAayC,CAAc,IAApC,YAAAnD,EAAuC,YAAa,GAC3D,SAAW9E,GACT0I,EACET,EACA,YACAjI,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,uBACZ,UAAU,KACZ,EACA7C,OAAC,QAAM,WAAU,uBACf,UAACE,MAAA,KAAE,UAAU,gBAAiB,GAAI,kEAGpC,IACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,qBAAsB,GAAI,qBACpB4K,EAAe,YAAY,EAAE,KAClD,EACA5K,EAAA,IAAC,YACC,QAAO4H,EAAAO,EAAS,aAAayC,CAAc,IAApC,YAAAhD,EAAuC,WAAY,GAC1D,SAAWjF,GACT0I,EACET,EACA,WACAjI,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,6BACZ,UAAU,MACZ,EACA7C,OAAC,QAAM,WAAU,uBACf,UAACE,MAAA,KAAE,UAAU,gBAAiB,GAAI,yEAGpC,GACF,GACF,IACF,EAGAF,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,uBACX,EAAe,qBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAElC,sDACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EAAe,cAElB,EACCA,MAAA,OAAI,UAAU,kBACZ,YAAciB,EAAW,OAAS,EACjCA,EAAW,IAAKwB,GACb3C,EAAA,YAAsB,UAAU,kBAC/B,UAAAE,EAAA,IAAC,SACC,UAAU,mBACV,KAAK,WACL,GAAI,YAAYyC,EAAS,EAAE,GAC3B,QAAS0F,EAAS,YAAY,SAAS1F,EAAS,EAAE,EAClD,SAAU,IAAM,CACR,MAAAoJ,EACJ1D,EAAS,YAAY,SAAS1F,EAAS,EAAE,EACrC0F,EAAS,YAAY,OAClBhC,GAAOA,IAAO1D,EAAS,IAE1B,CAAC,GAAG0F,EAAS,YAAa1F,EAAS,EAAE,EAC3C2F,EAAa6B,IAAU,CACrB,GAAGA,EACH,YAAa4B,CAAA,EACb,EACJ,CACF,EACA7L,EAAA,IAAC,SACC,UAAU,mBACV,QAAS,YAAYyC,EAAS,EAAE,GAE/B,SAASA,EAAA,MAvBJ,GAAAA,EAAS,EAyBnB,CACD,QAEA,IAAE,WAAU,aAAa,kCAAuB,EAErD,IACF,EAEA3C,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,OACX,EAAe,QAElB,EACCA,MAAA,OAAI,UAAU,YACZ,YAAQ4D,EAAK,OAAS,EACrBA,EAAK,IAAK8B,GACP5F,EAAA,YAAiB,UAAU,kBAC1B,UAAAE,EAAA,IAAC,SACC,UAAU,mBACV,KAAK,WACL,GAAI,OAAO0F,EAAI,EAAE,GACjB,QAASyC,EAAS,OAAO,SAASzC,EAAI,EAAE,EACxC,SAAU,IAAM,CACR,MAAAoG,EAAY3D,EAAS,OAAO,SAASzC,EAAI,EAAE,EAC7CyC,EAAS,OAAO,OAAQhC,GAAOA,IAAOT,EAAI,EAAE,EAC5C,CAAC,GAAGyC,EAAS,OAAQzC,EAAI,EAAE,EAC/B0C,EAAa6B,IAAU,CACrB,GAAGA,EACH,OAAQ6B,CAAA,EACR,EACJ,CACF,EACA9L,EAAA,IAAC,SACC,UAAU,mBACV,QAAS,OAAO0F,EAAI,EAAE,GAErB,SAAIA,EAAA,MApBC,GAAAA,EAAI,EAsBd,CACD,QAEA,IAAE,WAAU,aAAa,4BAAiB,EAE/C,GACF,GACF,IACF,QAGC,MAAI,WAAU,YACb,SAAC5F,EAAA,YAAI,UAAU,kBACb,UAAAE,EAAA,IAAC,UACC,KAAK,SACL,QAAS,IAAMoG,EAAS,cAAc,EACtC,UAAU,sCACX,kBAED,EAEApG,EAAA,IAAC,UACC,KAAK,SACL,SAAUqK,EACV,UAAU,kCAET,WAEGvK,OAAAC,EAAA,oBAACC,MAAA,KAAE,UAAU,4BAA6B,GAAI,aAEhD,EAGEF,OAAAC,EAAA,oBAACC,MAAA,KAAE,UAAU,eAAgB,GAC5BoK,EAAY,cAAgB,cAC/B,IAEJ,EACF,CACF,GACF,IACF,EACF,CAEJ,2GCn2BM2B,GAAgB,IAAM,CAC1B,MAAM3F,EAAWC,GAAY,EACvB,CAACzF,EAAUC,CAAW,EAAIC,WAAS,EAAE,EACrC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAACmI,EAASC,CAAU,EAAIpI,WAAS,CACrC,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,GACT,EACK,CAAC0D,EAAY2E,CAAa,EAAIrI,WAAS,EAAE,EAE/CW,YAAU,IAAM,CACDC,EAAA,GACZ,CAACuH,CAAO,CAAC,EAEZ,MAAMvH,EAAe,SAAY,SAC3B,IACFJ,EAAW,EAAI,EAEf,MAAM6C,EAAS,CAAC,EACT,eAAQ8E,CAAO,EAAE,QAAQ,CAAC,CAACI,EAAKC,CAAK,IAAM,CAC5CA,GAASA,IAAU,QACrBnF,EAAOkF,CAAG,EAAIC,EAChB,CACD,EAED,KAAM,CAAE,SAAA1H,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,YAAYpF,CAAM,EAExDtC,EAAK,SACPhB,IAAY0D,EAAA1C,EAAK,OAAL,YAAA0C,EAAW,WAAY1C,EAAK,UAAY,EAAE,EACtDsH,IAAc1E,EAAA5C,EAAK,OAAL,YAAA4C,EAAW,aAAc,EAAE,GAEhCjD,EAAAK,EAAK,SAAW,yBAAyB,QAE7CN,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAC3CC,EAAS,kCAAkC,SAC3C,CACAF,EAAW,EAAK,EAEpB,EAEMkI,EAAe,MAAOrD,GAAO,CACjC,GAAK,OAAO,QAAQ,+CAA+C,EAI/D,IACF,KAAM,CAAE,SAAAvE,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,cAAcpD,CAAE,EAEtDvE,EAAS,IAAMC,EAAK,QACtBhB,EAAYD,EAAS,OAAQ1B,GAAYA,EAAQ,KAAOiH,CAAE,CAAC,EAElD3E,EAAAK,EAAK,SAAW,0BAA0B,QAE9CN,EAAO,CACN,cAAM,wBAAyBA,CAAK,EAC5CC,EAAS,0BAA0B,EAEvC,EAEMkI,EAAyB,MAAOvD,GAAO,CACvC,IAEF,MAAM6F,EADUpL,EAAS,KAAMqL,GAAMA,EAAE,KAAO9F,CAAE,EACtB,SAAW,YAAc,QAAU,YAEvD,CAAE,SAAAvE,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,cAAcpD,EAAI,CAC1D,OAAQ6F,CAAA,CACT,EAEGpK,EAAS,IAAMC,EAAK,QACtBhB,EACED,EAAS,IAAKqL,GAAOA,EAAE,KAAO9F,EAAK,CAAE,GAAG8F,EAAG,OAAQD,GAAcC,CAAE,CACrE,EAESzK,EAAAK,EAAK,SAAW,iCAAiC,QAErDN,EAAO,CACN,cAAM,2BAA4BA,CAAK,EAC/CC,EAAS,iCAAiC,EAE9C,EAEM0K,EAAqB,CAAC7C,EAAKC,IAAU,CACzCJ,EAAYe,IAAU,CACpB,GAAGA,EACH,CAACZ,CAAG,EAAGC,EACP,KAAM,GACN,CACJ,EAEMK,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,UACN,EAIGC,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,2BAA2BD,CAAQ,GAT9B,KAalBqC,EAAmBjN,GACnBA,EAAQ,QAAUA,EAAQ,OAAO,OAAS,GAE1CA,EAAQ,OAAO,KAAMkN,GAAQA,EAAI,SAAS,GAAKlN,EAAQ,OAAO,CAAC,GAC7C,SAEfA,EAAQ,cAGX8K,EAAkB9K,GAClBA,EAAQ,SAAW,QAEnBY,EAAA,KAAC,OAAK,WAAU,qBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,2BACL,UAAU,OACX,EAAe,SAElB,EAKFF,EAAA,KAAC,OAAK,WAAU,mBACd,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EAAe,aAElB,EAIJ,OAEIF,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAM,0BACN,YAAY,qCACZ,QAAS,GACX,EAEA1I,OAACgJ,GAAY,OAAM,WAEjB,UAAA9I,MAAC,OAAI,UAAU,QACb,SAACF,EAAA,YAAI,UAAU,yBACb,UAACE,MAAA,OAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,uFAGlC,CACF,GACAA,MAAC,MAAI,WAAU,8BACb,SAAAF,EAAA,KAAC,UACC,QAAS,IAAMsG,EAAS,qBAAqB,EAC7C,UAAU,kDAEV,UAAApG,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,gBAGpB,IACF,CACF,GAGCA,EAAA,WAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,aACnD,SAAAF,OAAC,MAAI,WAAU,UACb,UAACA,OAAA,OAAI,UAAU,2BACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAe,oBAC7CA,EAAA,IAAC,SACC,KAAK,OACL,UAAU,eACV,YAAY,6BACZ,MAAOiJ,EAAQ,OACf,SAAWtG,GAAMuJ,EAAmB,SAAUvJ,EAAE,OAAO,KAAK,GAC9D,EACF,EAEA7C,OAAC,MAAI,WAAU,2BACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAM,WACpCF,EAAA,KAAC,UACC,UAAU,eACV,MAAOmJ,EAAQ,OACf,SAAWtG,GAAMuJ,EAAmB,SAAUvJ,EAAE,OAAO,KAAK,EAE5D,UAAC3C,EAAA,cAAO,MAAM,MAAM,SAAU,eAC7BA,EAAA,cAAO,MAAM,YAAY,SAAS,cAClCA,EAAA,cAAO,MAAM,QAAQ,SAAK,YAC7B,EACF,EAEAA,MAAC,MAAI,WAAU,yCACb,SAAAF,EAAA,KAAC,UACC,QAAS,IACPoJ,EAAW,CACT,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,GACT,EAEH,UAAU,yCAEV,UAAAlJ,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,OACX,EAAe,kBAGpB,IACF,CACF,GAGCuB,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAAAE,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EACAuB,CAAA,EACH,EAIDvB,EAAA,WAAI,UAAU,cACZ,SACCqB,EAAAvB,EAAA,KAAC,MAAI,WAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,YAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,EAC/C,EACAA,EAAA,WAAI,UAAU,2BAA2B,SAE1C,yBACF,EACEY,EAAS,SAAW,EACrBd,EAAA,YAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,aACnD,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,iCACX,EACAA,EAAA,WAAI,UAAU,iCAAiC,SAEhD,sBACAA,MAAC,IAAE,WAAU,sBACV,SAAAiJ,EAAQ,QAAUA,EAAQ,SAAW,MAClC,kEACA,6CACN,GACAnJ,EAAA,KAAC,UACC,QAAS,IAAMsG,EAAS,qBAAqB,EAC7C,UAAU,kCAEV,UAAApG,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,yBAElB,EACF,EAIEF,OAAAC,EAAA,oBAACC,EAAA,WAAI,UAAU,oBACb,SAACA,EAAA,WAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,WAAU,QACf,UAACE,EAAA,aACC,gBAAC,KACC,WAAAA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAM,WACVA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAO,aACb,CACF,GACCA,EAAA,aACE,SAASY,EAAA,IAAK1B,GAAY,SACnB,MAAAgL,IACJ3F,EAAArF,EAAQ,eAAR,YAAAqF,EAAsB,KACnBrB,GAAMA,EAAE,WAAa,UACnBuB,EAAAvF,EAAQ,eAAR,YAAAuF,EAAuB,IAE9B,cACG,KACC,WAAAzE,MAAC,KACC,UAAAF,OAAC,MAAI,WAAU,4BACZ,UAAAqM,EAAgBjN,CAAO,GACtBc,EAAA,IAAC,OACC,UAAU,eACV,IAAK6J,EAAYsC,EAAgBjN,CAAO,CAAC,EACzC,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAUyD,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,OAC3B,CACF,SAED,MACC,WAAA3C,MAAC,OAAI,UAAU,UACZ,2BAAoB,QACnBd,EAAQ,OACR,UACJ,GACAY,OAAC,QAAM,WAAU,aAAa,cAC1BZ,EAAQ,KACZ,GACF,IACF,CACF,GACCc,EAAA,UAAI,SAAegK,EAAA9K,CAAO,CAAE,GAC7Bc,EAAA,IAAC,KACC,UAAAF,OAAC,MACE,WAAAZ,EAAQ,iBACPY,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,UAAO,SAAW,gBAAS,KAC3Bd,EAAQ,iBACX,EAEDA,EAAQ,mBACNY,OAAA,OAAI,UAAU,QACb,UAAAE,MAAC,UAAO,SAAa,kBAAS,KAC7Bd,EAAQ,kBAAkB,OAC7B,EAED,CAACA,EAAQ,iBACR,CAACA,EAAQ,mBACNc,MAAA,QAAK,UAAU,aAAa,SAE7B,oBAEN,CACF,GACCA,EAAA,UAAI,SAAW2J,EAAAzK,EAAQ,SAAS,EAAE,QAClC,KACC,UAAAY,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,UAAAE,EAAA,IAAC,UACC,QAAS,IACPoG,EACE,wBAAwBlH,EAAQ,EAAE,EACpC,EAEF,UAAU,iCACV,MAAM,OAEN,SAAAc,MAAC,eAAa,MAAK,gBAAiB,GACtC,EAEAA,EAAA,IAAC,UACC,QAAS,IACP0J,EAAuBxK,EAAQ,EAAE,EAEnC,UAAW,cACTA,EAAQ,SAAW,YACf,sBACA,qBACN,GACA,MACEA,EAAQ,SAAW,YACf,YACA,UAGN,SAAAc,EAAA,IAAC,gBACC,KACEd,EAAQ,SAAW,YACf,wBACA,kBAEP,CACH,EAEAc,EAAA,IAAC,UACC,QAAS,IAAMwJ,EAAatK,EAAQ,EAAE,EACtC,UAAU,gCACV,MAAM,SAEN,SAAAc,MAAC,eAAa,MAAK,4BAA6B,IAClD,EACF,CACF,KApGOd,EAAQ,EAqGjB,EAEH,CACH,GACF,EACF,GACF,EAGAc,EAAA,IAAC,MAAI,WAAU,YACb,SAAAA,MAAC,MAAI,WAAU,UACZ,SAAAY,EAAS,IAAK1B,GAAY,SACzB,MAAMgL,IACJ3F,EAAArF,EAAQ,eAAR,YAAAqF,EAAsB,KAAMrB,GAAMA,EAAE,WAAa,UACjDuB,EAAAvF,EAAQ,eAAR,YAAAuF,EAAuB,IAEzB,OACGzE,MAAA,OAAqB,UAAU,SAC9B,eAAC,MAAI,WAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,gBACb,SAACF,OAAA,OAAI,UAAU,yBACb,UAAAE,MAAC,OAAI,UAAU,cACb,SAACF,EAAA,YAAI,UAAU,4BACZ,UAAAqM,EAAgBjN,CAAO,GACtBc,EAAA,IAAC,OACC,UAAU,eACV,IAAK6J,EACHsC,EAAgBjN,CAAO,CACzB,EACA,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAUyD,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,OAC3B,CACF,EAEF7C,OAAC,MAAI,WAAU,cACb,UAAAE,MAAC,MAAG,UAAU,eACX,2BAAoB,QACnBd,EAAQ,OACR,UACJ,GACAY,OAAC,QAAM,WAAU,aAAa,cAC1BZ,EAAQ,KACZ,GACF,IACF,CACF,GAEAY,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,WACCA,MAAA,OAAK,SAAegK,EAAA9K,CAAO,CAAE,IAChC,EAEAY,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,mBACC,MACE,WAAAd,EAAQ,iBACPY,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,UAAO,SAAG,QAAS,KACnBd,EAAQ,iBACX,EAEDA,EAAQ,mBACNY,OAAA,OAAI,UAAU,QACb,UAAAE,MAAC,UAAO,SAAI,SAAS,KACpBd,EAAQ,kBAAkB,OAC7B,EAED,CAACA,EAAQ,iBACR,CAACA,EAAQ,mBACNc,MAAA,QAAK,UAAU,mBAAmB,SAEnC,eAEN,IACF,EAEAF,OAAC,MAAI,WAAU,uBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,YACCA,EAAA,aAAO,SAAW2J,EAAAzK,EAAQ,SAAS,CAAE,IACxC,QAEC,MAAI,WAAU,SACb,SAACY,EAAA,YAAI,UAAU,yBACb,UAAAA,EAAA,KAAC,UACC,QAAS,IACPsG,EACE,wBAAwBlH,EAAQ,EAAE,EACpC,EAEF,UAAU,2CACV,MAAM,OAEN,UAAAc,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,QAElB,EAEAF,EAAA,KAAC,UACC,QAAS,IACP4J,EAAuBxK,EAAQ,EAAE,EAEnC,UAAW,wBACTA,EAAQ,SAAW,YACf,sBACA,qBACN,GACA,MACEA,EAAQ,SAAW,YACf,YACA,UAGN,UAAAc,EAAA,IAAC,gBACC,KACEd,EAAQ,SAAW,YACf,wBACA,iBAEN,UAAU,OACX,EACAA,EAAQ,SAAW,YAChB,OACA,QACN,EAEAY,EAAA,KAAC,UACC,QAAS,IAAM0J,EAAatK,EAAQ,EAAE,EACtC,UAAU,0CACV,MAAM,SAEN,UAAAc,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EAAe,WAElB,EACF,CACF,IACF,CACF,GACF,CAzIQ,EAAAd,EAAQ,EA0IlB,CAEH,EACH,EACF,IACF,CAEJ,GAGCsF,EAAW,MAAQ,GACjB1E,EAAA,YAAI,UAAU,+BACb,UAAAE,MAAC,OAAI,UAAU,+BACb,SAACF,EAAA,UAAE,UAAU,kDAAkD,sBACnD0E,EAAW,KAAO,GAAKA,EAAW,MAAQ,EAAE,MAAI,IACzD,KAAK,IAAIA,EAAW,KAAOA,EAAW,MAAOA,EAAW,KAAK,EAAG,IAAI,MACjEA,EAAW,MAAM,YACvB,CACF,GACAxE,EAAA,IAAC,MAAI,WAAU,kBACb,SAAAA,EAAA,IAAC,MAAI,cAAW,sBACd,SAAAF,OAAC,KAAG,WAAU,8EACZ,UAAAE,EAAA,IAAC,MACC,UAAW,aACTwE,EAAW,MAAQ,EAAI,WAAa,EACtC,GAEA,SAAAxE,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IACPkJ,EAAYe,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,IAEzD,SAAUzF,EAAW,MAAQ,EAC9B,qBAED,CACF,QAEC,KAAG,WAAU,mBACZ,SAAC1E,EAAA,aAAK,UAAU,YAAY,kBACpB0E,EAAW,KAAK,OAAKA,EAAW,OACxC,CACF,GAEAxE,EAAA,IAAC,MACC,UAAW,aACTwE,EAAW,MAAQA,EAAW,MAAQ,WAAa,EACrD,GAEA,SAAAxE,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IACPkJ,EAAYe,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,IAEzD,SAAUzF,EAAW,MAAQA,EAAW,MACzC,iBAED,EACF,CACF,EACF,EACF,GACF,GAEJ,IACF,CAEJ,2GClnBM6H,GAAqB,IAAM,gBAC/B,KAAM,CAAE,EAAAnJ,EAAG,KAAAzC,CAAK,EAAIC,GAAe,EAC7B0F,EAAWC,GAAY,EACvB,CAAE,GAAAF,CAAG,EAAIN,GAAU,EACnBuE,EAAY,EAAQjE,EAGpB0D,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,2BAA2BD,CAAQ,GAT9B,KAYlB,CAACzI,EAASC,CAAU,EAAIR,WAAS,EAAK,EACtC,CAACuJ,EAAQC,CAAS,EAAIxJ,WAAS,EAAK,EACpC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAACyJ,EAASC,CAAU,EAAI1J,WAAS,EAAE,EAGnC,CAAC2J,CAAkB,EAAI3J,WAAS,IAAM,OAAO,KAAKL,EAAK,MAAM,IAAI,CAAC,EAGlE,CAAC0H,EAAUC,CAAW,EAAItH,WAAS,IAAM,CAE7C,MAAM4J,EAAsB,CAAC,EACV,OAAAD,EAAA,QAASE,GAAS,CACnCD,EAAoBC,CAAI,EAAI,CAC1B,MAAO,GACP,QAAS,GACT,QAAS,GACT,UAAW,GACX,SAAU,GACV,SAAU,EACZ,EACD,EAEM,CACL,KAAM,GACN,gBAAiB,GACjB,kBAAmB,GACnB,QAAS,GACT,OAAQ,QACR,cAAe,KACf,iBAAkB,GAClB,OAAQ,CAAC,EACT,YAAa,CAAC,EACd,OAAQ,CAAC,EACT,aAAcD,CAChB,EACD,EAEK,CAACzJ,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAAC8C,EAAMC,CAAO,EAAI/C,WAAS,EAAE,EAC7B,CAAC8J,EAAgBC,CAAiB,EAAI/J,WAAS,IAAI,EACnD,CAACgK,EAAcC,CAAe,EAAIjK,WAAS,IAAI,EAC/C,CAACwL,EAAgBC,CAAiB,EAAIzL,WAAS,EAAE,EACjD,CAAC0L,GAAmBC,EAAoB,EAAI3L,WAAS,CAAC,EAE5DW,YAAU,IAAM,EACG,SAAY,WACvB,IACFH,EAAW,EAAI,EAGf,KAAM,CAACoD,EAAkBC,EAAU,EAAI,MAAM,QAAQ,IAAI,CACvD4E,EAAS,cAAc,EACvBA,EAAS,QAAQ,EAClB,EAWD,IATIhF,EAAAG,EAAiB,OAAjB,MAAAH,EAAuB,SACXrD,EAAAwD,EAAiB,KAAK,IAAI,GAGtCD,EAAAE,GAAW,OAAX,MAAAF,EAAiB,SACXZ,EAAAc,GAAW,KAAK,IAAI,EAI1ByF,GAAajE,EAAI,CACX,YAAI,2BAA4BA,CAAE,EAC1C,QAAQ,IAAI,gCAAgC,EAC5C,MAAMuG,GAAgB,MAAMnD,EAAS,WAAWpD,CAAE,EAG9C,GAFI,YAAI,wBAAyBuG,EAAa,GAE9CvH,EAAAuH,GAAc,OAAd,MAAAvH,EAAoB,QAAS,CACzB,MAAAjG,EAAUwN,GAAc,KAAK,QAC3B,YAAI,gBAAiBxN,CAAO,EAGpCkJ,EAAa6B,GAAU,iBACrB,GAAGA,EACH,KAAM/K,EAAQ,MAAQ,GACtB,gBAAiBA,EAAQ,iBAAmB,GAC5C,kBAAmBA,EAAQ,mBAAqB,GAChD,QAASA,EAAQ,SAAW,GAC5B,OAAQA,EAAQ,QAAU,QAC1B,iBAAkBA,EAAQ,kBAAoB,GAC9C,cAAaqF,GAAArF,EAAQ,aAAR,YAAAqF,GAAoB,IAAKiB,IAAMA,GAAE,cAAe,CAAC,EAC9D,SAAQf,EAAAvF,EAAQ,OAAR,YAAAuF,EAAc,IAAKvB,IAAMA,GAAE,SAAU,EAAC,EAC9C,EAGF,MAAMyJ,EAAsB,CAAE,GAAGxE,EAAS,YAAa,EAoBvD,GAnBIjJ,EAAQ,cAAgB,MAAM,QAAQA,EAAQ,YAAY,GACpDA,EAAA,aAAa,QAASgG,GAAgB,CACxByH,EAAAzH,EAAY,QAAQ,EAAI,CAC1C,MAAOA,EAAY,OAAS,GAC5B,QAASA,EAAY,SAAW,GAChC,QAASA,EAAY,SAAW,GAChC,UAAWA,EAAY,WAAa,GACpC,SAAUA,EAAY,UAAY,GAClC,SAAUA,EAAY,UAAY,EACpC,EACD,EAGHkD,EAAa6B,IAAU,CACrB,GAAGA,EACH,aAAc0C,CAAA,EACd,EAGEzN,EAAQ,QAAU,MAAM,QAAQA,EAAQ,MAAM,EAAG,CAC3C,YAAI,2BAA4BA,EAAQ,MAAM,EACtD,MAAM0N,EAAiB1N,EAAQ,OAAO,IAAI,CAACkN,EAAK9M,KAAU,CAClD,MAAAuN,GAAWhD,EAAYuC,EAAI,QAAQ,EACjC,mBAAI,SAAS9M,EAAK,KAAK8M,EAAI,QAAQ,OAAOS,EAAQ,EAAE,EACrD,CACL,GAAIT,EAAI,GACR,KAAM,KACN,QAASS,GACT,IAAKT,EAAI,KAAO,GAChB,UAAWA,EAAI,UACf,SAAUA,EAAI,SACd,UAAWA,EAAI,SACjB,EACD,EACO,YAAI,6BAA8BQ,CAAc,EACxDL,EAAkBK,CAAc,EAGhC,MAAME,GAAeF,EAAe,UACjCR,GAAQA,EAAI,SACf,EACIU,KAAiB,IACnBL,GAAqBK,EAAY,CACnC,MAEQ,YAAI,8BAA+B5N,EAAQ,MAAM,EAKzDA,EAAQ,gBACP,CAACA,EAAQ,QAAUA,EAAQ,OAAO,SAAW,IAE9B6L,EAAAlB,EAAY3K,EAAQ,aAAa,CAAC,CACpD,MAEQ,cAAM,0BAA2BwN,EAAa,EACtDlL,EAAS,6BAA6B,CACxC,QAEKD,EAAO,CACN,cAAM,sBAAuBA,CAAK,EAC1CC,EAAS,qBAAqB,SAC9B,CACAF,EAAW,EAAK,EAEpB,GAES,GACR,CAAC6E,EAAIiE,CAAS,CAAC,EAEZ,MAAAgB,EAAoB,CAACnG,EAAOqE,IAAU,CAC1ClB,EAAa6B,IAAU,CACrB,GAAGA,EACH,CAAChF,CAAK,EAAGqE,CAAA,EACT,CACJ,EAEM+B,EAA0B,CAAC3L,EAAUuF,EAAOqE,IAAU,CAC1DlB,EAAa6B,IAAU,CACrB,GAAGA,EACH,aAAc,CACZ,GAAGA,EAAK,aACR,CAACvK,CAAQ,EAAG,CACV,GAAGuK,EAAK,aAAavK,CAAQ,EAC7B,CAACuF,CAAK,EAAGqE,CAAA,CACX,CACF,EACA,CACJ,EAEMyD,EAAsBpK,GAAM,CAChC,MAAMqK,EAAQ,MAAM,KAAKrK,EAAE,OAAO,KAAK,EACnC,GAAAqK,EAAM,SAAW,EAAG,OAGxB,GAAIV,EAAe,OAASU,EAAM,OAAS,GAAI,CAC7CxL,EAAS,uCAAuC,EAChD,OAGF,MAAMyL,EAAYD,EAAM,IAAI,CAACzB,EAAMjM,KAAW,CAC5C,KAAAiM,EACA,QAAS,IAAI,gBAAgBA,CAAI,EACjC,IAAK,GACL,UAAWe,EAAe,SAAW,GAAKhN,IAAU,GACpD,EAEFiN,EAAmBtC,GAAS,CAAC,GAAGA,EAAM,GAAGgD,CAAS,CAAC,EACnDzL,EAAS,EAAE,CACb,EAEM0L,EAAe5N,GAAU,CAC7BiN,EAAmBtC,GAAS,OAC1B,MAAMkD,EAAUlD,EAAK,OAAO,CAACmD,EAAGC,KAAMA,KAAM/N,CAAK,EAEjD,OAAIiF,EAAA0F,EAAK3K,CAAK,IAAV,MAAAiF,EAAa,WAAa4I,EAAQ,OAAS,IACrCA,EAAA,CAAC,EAAE,UAAY,GACvBV,GAAqB,CAAC,GAEjBU,CAAA,CACR,CACH,EAEMG,EAAmBhO,GAAU,CACjCiN,EAAmBtC,GACjBA,EAAK,IAAI,CAACmC,EAAKiB,KAAO,CACpB,GAAGjB,EACH,UAAWiB,IAAM/N,CAAA,EACjB,CACJ,EACAmN,GAAqBnN,CAAK,CAC5B,EAEMiO,EAAiB,CAACjO,EAAOkO,IAAQ,CACrCjB,EAAmBtC,GACjBA,EAAK,IAAI,CAACmC,EAAKiB,IAAOA,IAAM/N,EAAQ,CAAE,GAAG8M,EAAK,IAAAoB,GAAQpB,CAAI,CAC5D,CACF,EAEM9D,EAAe,MAAO3F,GAAM,CAChCA,EAAE,eAAe,EACjB2H,EAAU,EAAI,EACd9I,EAAS,EAAE,EACXgJ,EAAW,EAAE,EAET,IACI,MAAA5B,EAAQ,aAAa,QAAQ,YAAY,EACzC6C,EAAiB,IAAI,SAGZA,EAAA,OAAO,OAAQtD,EAAS,IAAI,EAC5BsD,EAAA,OAAO,kBAAmBtD,EAAS,eAAe,EAClDsD,EAAA,OAAO,oBAAqBtD,EAAS,iBAAiB,EACtDsD,EAAA,OAAO,UAAWtD,EAAS,OAAO,EAClCsD,EAAA,OAAO,SAAUtD,EAAS,MAAM,EAChCsD,EAAA,OAAO,mBAAoBtD,EAAS,gBAAgB,EAGpDsD,EAAA,OACb,cACA,KAAK,UAAUtD,EAAS,WAAW,CACrC,EACAsD,EAAe,OAAO,SAAU,KAAK,UAAUtD,EAAS,MAAM,CAAC,EAChDsD,EAAA,OACb,eACA,KAAK,UAAUtD,EAAS,YAAY,CACtC,EAGA,IAAIsF,EAAa,EACFnB,EAAA,QAASoB,GAAc,CAEhCA,EAAU,OACGjC,EAAA,OAAO,SAAUiC,EAAU,IAAI,EAC9CjC,EAAe,OAAO,YAAYgC,CAAU,GAAIC,EAAU,GAAG,EAC7DjC,EAAe,OAAO,aAAagC,CAAU,GAAIC,EAAU,SAAS,EACpED,IACF,CACD,EAGD,MAAMb,EAAiBN,EACpB,OAAQF,GAAQ,CAACA,EAAI,MAAQA,EAAI,EAAE,EACnC,IAAKA,IAAS,CACb,GAAIA,EAAI,GACR,IAAKA,EAAI,IACT,UAAWA,EAAI,UACf,UAAWA,EAAI,WACf,EAEAQ,EAAe,OAAS,GAC1BnB,EAAe,OAAO,iBAAkB,KAAK,UAAUmB,CAAc,CAAC,EAIpE,IAAAvF,GACA+C,EACF/C,GAAS,MAAMkC,EAAS,cAAcpD,EAAIsF,CAAc,EAE/CpE,GAAA,MAAMkC,EAAS,cAAckC,CAAc,EAGhD,MAAE,SAAA7J,GAAU,KAAAC,CAAA,EAASwF,GAE3B,GAAIzF,GAAS,IAAMC,GAAQA,EAAK,QAC9B2I,EACE,WAAWJ,EAAY,UAAY,SAAS,gBAC9C,EACA,WAAW,IAAM,CACfhE,EAAS,iBAAiB,GACzB,GAAI,MACF,CACL,MAAMsF,GACJ7J,GAAA,YAAAA,EAAM,UACN,aAAauI,EAAY,SAAW,QAAQ,WAC9C5I,EAASkK,CAAY,SAEhBnK,EAAO,CACN,cAAM,wBAAyBA,CAAK,EAC5CC,EAAS,aAAa4I,EAAY,SAAW,QAAQ,UAAU,SAC/D,CACAE,EAAU,EAAK,EAEnB,EAEA,OAAIjJ,QAECyH,GACC,UAAA9I,EAAA,IAAC,OACC,UAAU,mDACV,MAAO,CAAE,UAAW,OAAQ,EAE5B,SAAAF,EAAA,KAAC,MAAI,WAAU,cACb,UAACE,EAAA,WAAI,UAAU,mCAAmC,KAAK,SACrD,eAAC,OAAK,WAAU,kBAAkB,qBAAU,GAC9C,EACCA,EAAA,SAAE,UAAU,aAAa,SAAuB,4BACnD,KAEJ,EAMAF,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAO,GAAG4B,EAAY,OAAS,QAAQ,mBACvC,YAAY,6CACd,EACApK,EAAA,IAAC8I,GAAY,OAAOsB,EAAY,eAAiB,qBAC/C,SAAAtK,OAAC,OAAK,UAAUwI,EAAc,UAAU,aAErC,UAAA/G,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAAAE,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EACAuB,CAAA,EACH,EAGDgJ,GACEzK,OAAA,OAAI,UAAU,4BAA4B,KAAK,QAC9C,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EACAuK,CAAA,EACH,EAIFzK,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,sBACL,UAAU,uBACX,EAAe,kBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAElC,oDACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,cAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,MAAOmI,EAAS,KAChB,SAAWxF,GAAMyI,EAAkB,OAAQzI,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,mBACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAGxC,4EACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EAAe,sBAElB,EACAF,EAAA,KAAC,UACC,MAAOqI,EAAS,OAChB,SAAWxF,GAAMyI,EAAkB,SAAUzI,EAAE,OAAO,KAAK,EAC3D,UAAU,eAEV,UAAC3C,EAAA,cAAO,MAAM,QAAQ,SAAK,UAC1BA,EAAA,cAAO,MAAM,YAAY,SAAS,eACrC,EACCA,EAAA,aAAM,UAAU,uBAAuB,SAExC,iDACF,GACF,IACF,EAGAF,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,iCACL,UAAU,uBACX,EAAe,kBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAElC,uDACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,0BAElB,EACAA,EAAA,IAAC,SACC,KAAK,SACL,KAAK,OACL,MAAOmI,EAAS,gBAChB,SAAWxF,GACTyI,EAAkB,kBAAmBzI,EAAE,OAAO,KAAK,EAErD,UAAU,eACV,YAAY,UACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,kEACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,OACX,EAAe,kCAElB,EACAA,EAAA,IAAC,SACC,KAAK,SACL,KAAK,OACL,MAAOmI,EAAS,kBAChB,SAAWxF,GACTyI,EAAkB,oBAAqBzI,EAAE,OAAO,KAAK,EAEvD,UAAU,eACV,YAAY,QACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,gDACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,YAElB,EACAA,EAAA,IAAC,SACC,KAAK,MACL,MAAOmI,EAAS,QAChB,SAAWxF,GAAMyI,EAAkB,UAAWzI,EAAE,OAAO,KAAK,EAC5D,UAAU,eACV,YAAY,2BACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,qCACF,GACF,IACF,EAGAF,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,uBACX,EAAe,kBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAGlC,sEACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EAAe,0BAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,OAAO,UACP,SAAQ,GACR,SAAU+M,EACV,UAAU,eACZ,EACC/M,EAAA,aAAM,UAAU,uBAAuB,SAGxC,mFACF,EAECsM,EAAe,OAAS,GACtBxM,EAAA,YAAI,UAAU,eACb,UAACA,OAAA,MAAG,UAAU,OAAO,6BACFwM,EAAe,OAAO,QACzC,QACC,MAAI,WAAU,MACZ,SAAAA,EAAe,IAAI,CAACoB,EAAWpO,IAC9BU,EAAA,IAAC,OAAgB,UAAU,gBACzB,SAACF,OAAA,OAAI,UAAU,OACb,UAACA,OAAA,OAAI,UAAU,oBACb,UAAAE,EAAA,IAAC,OACC,IAAK0N,EAAU,QACf,IAAK,WAAWpO,EAAQ,CAAC,GACzB,UAAU,eACV,MAAO,CAAE,OAAQ,QAAS,UAAW,OAAQ,EAC7C,QAAUqD,GAAM,CACN,cACN,wBACA+K,EAAU,OACZ,EACE/K,EAAA,OAAO,MAAM,QAAU,MAC3B,EACA,OAAQ,IAAM,CACJ,YACN,6BACA+K,EAAU,OACZ,EACF,CACF,EACA1N,EAAA,IAAC,UACC,KAAK,SACL,UAAU,0DACV,QAAS,IAAMkN,EAAY5N,CAAK,EACjC,aAED,EACCoO,EAAU,WACT1N,MAAC,OAAK,WAAU,uDAAuD,SAEvE,mBAEJ,EACAF,OAAC,MAAI,WAAU,YACb,UAACE,MAAA,OAAI,UAAU,OACb,SAAAA,EAAA,IAAC,SACC,KAAK,OACL,UAAU,+BACV,YAAY,WACZ,MAAO0N,EAAU,IACjB,SAAW/K,GACT4K,EAAejO,EAAOqD,EAAE,OAAO,KAAK,IAG1C,QACC,MAAI,WAAU,eACZ,UAAC+K,EAAU,WACV1N,EAAA,IAAC,UACC,KAAK,SACL,UAAU,iCACV,QAAS,IAAMsN,EAAgBhO,CAAK,EACrC,2BAIL,GACF,IACF,GA3DQA,CA4DV,CACD,CACH,GACF,GAEJ,IACF,EAGAQ,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAACE,MAAA,KAAE,UAAU,+BAAgC,GAAI,4BAEnD,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAGlC,iFACF,CACF,SAGC,MAAI,WAAU,sBACZ,SAAmByK,EAAA,IAAKE,GACvB7K,EAAA,KAAC,UAEC,KAAK,SACL,QAAS,IAAM+K,EAAkBF,CAAI,EACrC,UAAW,gBACTC,IAAmBD,EAAO,SAAW,EACvC,GAEA,UAAC3K,MAAA,KAAE,UAAU,eAAgB,GAC5B2K,EAAK,YAAY,EACjBA,IAAS,MACR3K,MAAC,OAAK,WAAU,aAAa,SAAU,iBAVpC2K,CAaR,GACH,EAGA7K,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,cAAe,GAAI,UACxB4K,EAAe,YAAY,EAAE,IACpCA,IAAmB,MAClB5K,MAAC,OAAK,WAAU,mBAAmB,SAAC,OAExC,EACAA,EAAA,IAAC,SACC,KAAK,OACL,QAAOuE,EAAA4D,EAAS,aAAayC,CAAc,IAApC,YAAArG,EAAuC,QAAS,GACvD,SAAW5B,GACT0I,EACET,EACA,QACAjI,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,sBACZ,SAAUiI,IAAmB,KAC/B,EACA9K,OAAC,QAAM,WAAU,uBAAuB,8CACJ,IACjC8K,EAAe,YAAY,EAC9B,IACF,EAEA9K,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,cAAe,GAAI,YACtB4K,EAAe,YAAY,EAAE,KACzC,EACA5K,EAAA,IAAC,YACC,QAAOyE,EAAA0D,EAAS,aAAayC,CAAc,IAApC,YAAAnG,EAAuC,UAAW,GACzD,SAAW9B,GACT0I,EACET,EACA,UACAjI,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,mCACd,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAGxC,oFACF,EAEAF,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,2BACL,UAAU,OACX,EAAe,YACN4K,EAAe,YAAY,EAAE,IACtCA,IAAmB,MAClB5K,MAAC,OAAK,WAAU,mBAAmB,SAAC,OAExC,EACAA,EAAA,IAAC2L,GAAA,CACC,UAASxG,GAAAgD,EAAS,aAAayC,CAAc,IAApC,YAAAzF,GAAuC,UAAW,GAC3D,SAAWyG,GACTP,EAAwBT,EAAgB,UAAWgB,CAAI,EAEzD,YAAY,gHACd,EACA9L,OAAC,QAAM,WAAU,uBACf,UAAAE,EAAA,IAAC,gBACC,KAAK,yBACL,UAAU,OACX,EAAe,qJAIlB,IACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,aAAc,GAAI,eAClB4K,EAAe,YAAY,EAAE,KAC5C,EACA5K,EAAA,IAAC,SACC,KAAK,OACL,QAAOyH,EAAAU,EAAS,aAAayC,CAAc,IAApC,YAAAnD,EAAuC,YAAa,GAC3D,SAAW9E,GACT0I,EACET,EACA,YACAjI,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,uBACZ,UAAU,KACZ,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,sDACF,EAEAF,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,aAAc,GAAI,qBACZ4K,EAAe,YAAY,EAAE,KAClD,EACA5K,EAAA,IAAC,YACC,QAAO4H,EAAAO,EAAS,aAAayC,CAAc,IAApC,YAAAhD,EAAuC,WAAY,GAC1D,SAAWjF,GACT0I,EACET,EACA,WACAjI,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,6BACZ,UAAU,MACZ,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,4DACF,GACF,IACF,EAGAF,OAAC,MAAI,WAAU,oBACb,UAAAE,MAAC,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,SACb,UAACA,OAAA,MAAG,UAAU,gCACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,uBACX,EAAe,qBAElB,EACCA,EAAA,SAAE,UAAU,qBAAqB,SAElC,oDACF,CACF,GAEAF,OAAC,MAAI,WAAU,MACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EAAe,cAElB,EACCA,MAAA,OAAI,UAAU,kBACZ,YAAciB,EAAW,OAAS,EACjCA,EAAW,IAAKwB,GACb3C,EAAA,YAAsB,UAAU,kBAC/B,UAAAE,EAAA,IAAC,SACC,UAAU,mBACV,KAAK,WACL,GAAI,YAAYyC,EAAS,EAAE,GAC3B,QAAS0F,EAAS,YAAY,SAAS1F,EAAS,EAAE,EAClD,SAAU,IAAM,CACR,MAAAoJ,EACJ1D,EAAS,YAAY,SAAS1F,EAAS,EAAE,EACrC0F,EAAS,YAAY,OAClBhC,GAAOA,IAAO1D,EAAS,IAE1B,CAAC,GAAG0F,EAAS,YAAa1F,EAAS,EAAE,EAC3C2I,EAAkB,cAAeS,CAAc,EACjD,CACF,EACA7L,EAAA,IAAC,SACC,UAAU,mBACV,QAAS,YAAYyC,EAAS,EAAE,GAE/B,SAASA,EAAA,MApBJ,GAAAA,EAAS,EAsBnB,CACD,QAEA,IAAE,WAAU,aAAa,kCAAuB,EAErD,IACF,EAEA3C,OAAC,MAAI,WAAU,iBACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,OACX,EAAe,QAElB,EACCA,MAAA,OAAI,UAAU,YACZ,YAAQ4D,EAAK,OAAS,EACrBA,EAAK,IAAK8B,GACP5F,EAAA,YAAiB,UAAU,kBAC1B,UAAAE,EAAA,IAAC,SACC,UAAU,mBACV,KAAK,WACL,GAAI,OAAO0F,EAAI,EAAE,GACjB,QAASyC,EAAS,OAAO,SAASzC,EAAI,EAAE,EACxC,SAAU,IAAM,CACR,MAAAoG,EAAY3D,EAAS,OAAO,SAASzC,EAAI,EAAE,EAC7CyC,EAAS,OAAO,OAAQhC,GAAOA,IAAOT,EAAI,EAAE,EAC5C,CAAC,GAAGyC,EAAS,OAAQzC,EAAI,EAAE,EAC/B0F,EAAkB,SAAUU,CAAS,EACvC,CACF,EACA9L,EAAA,IAAC,SACC,UAAU,mBACV,QAAS,OAAO0F,EAAI,EAAE,GAErB,SAAIA,EAAA,MAjBC,GAAAA,EAAI,EAmBd,CACD,QAEA,IAAE,WAAU,aAAa,4BAAiB,EAE/C,GACF,GACF,IACF,QAGC,MAAI,WAAU,MACb,SAAC5F,EAAA,YAAI,UAAU,qBACb,UAAAE,EAAA,IAAC,UACC,KAAK,SACL,UAAU,4CACV,SAAUqK,EAET,WAEGvK,OAAAC,EAAA,oBAACC,MAAA,QAAK,UAAU,uCAAwC,GACvDoK,EAAY,cAAgB,eAC/B,EAGEtK,OAAAC,EAAA,oBAACC,MAAA,KAAE,UAAU,eAAgB,GAC5BoK,EAAY,iBAAmB,iBAClC,GAEJ,QACC,MAAI,WAAU,OACb,SAACtK,EAAA,cAAM,UAAU,aACf,UAACE,MAAA,KAAE,UAAU,cAAe,GAC3BoK,EACG,wDACA,0DACN,CACF,IACF,CACF,IACF,CACF,IACF,CAEJ,2GC36BMuD,GAAqB,IAAM,OACzB,MAAE,EAAAzK,CAAE,EAAIxC,GAAe,EACvB,CAACW,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAAC8M,EAAWC,CAAY,EAAI/M,WAAS,YAAY,EACjD,CAACgN,EAAkBC,CAAmB,EAAIjN,WAAS,KAAK,EACxD,CAACkN,EAAeC,CAAgB,EAAInN,WAAS,IAAI,EACjD,CAACoN,EAAWC,CAAY,EAAIrN,WAAS,EAAE,EAGvCsN,EAAmB,CACvB,CAAE,MAAO,UAAW,MAAO,UAAW,EACtC,CAAE,MAAO,WAAY,MAAO,WAAY,EACxC,CAAE,MAAO,aAAc,MAAO,cAAe,EAC7C,CAAE,MAAO,aAAc,MAAO,cAAe,EAC7C,CAAE,MAAO,cAAe,MAAO,eAAgB,EAC/C,CAAE,MAAO,cAAe,MAAO,eAAgB,EAC/C,CAAE,MAAO,cAAe,MAAO,eAAgB,CACjD,EAGMC,EAAkB,CACtB,CAAE,MAAO,MAAO,MAAO,gBAAiB,KAAM,IAAK,EACnD,CAAE,MAAO,KAAM,MAAO,UAAW,KAAM,MAAO,EAC9C,CAAE,MAAO,KAAM,MAAO,WAAY,KAAM,MAAO,EAC/C,CAAE,MAAO,KAAM,MAAO,UAAW,KAAM,MAAO,EAC9C,CAAE,MAAO,KAAM,MAAO,SAAU,KAAM,MAAO,EAC7C,CAAE,MAAO,KAAM,MAAO,UAAW,KAAM,MAAO,CAChD,EAGA5M,YAAU,IAAM,EACY,SAAY,CAChC,IAMF,GALAH,EAAW,EAAI,EACfE,EAAS,EAAE,EAIP,CADU,aAAa,QAAQ,YAAY,EACnC,CACVA,EACE,6DACF,EACAF,EAAW,EAAK,EAChB,OAIF,KAAM,CAACgN,EAAiBC,CAAW,EAAI,MAAM,QAAQ,IAAI,CACvDhF,EAAS,iBAAiBqE,EAAWE,CAAgB,EACrDvE,EAAS,sBAAsBqE,EAAWE,CAAgB,EAC3D,EAGD,GAAIQ,EAAgB,SAAS,IAAMA,EAAgB,KACjDL,EAAiBK,EAAgB,KAAK,MAAQA,EAAgB,IAAI,MAC7D,CAML,GALQ,cACN,wBACAA,EAAgB,SAAS,OACzBA,EAAgB,SAAS,UAC3B,EAEEA,EAAgB,SAAS,SAAW,KACpCA,EAAgB,SAAS,SAAW,IACpC,CACA9M,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,OAEFA,EAAS,+BAA+B,EAItC+M,EAAY,SAAS,IAAMA,EAAY,KACzCJ,EAAaI,EAAY,KAAK,MAAQA,EAAY,IAAI,GAE9C,cACN,8BACAA,EAAY,SAAS,OACrBA,EAAY,SAAS,UACvB,EACAJ,EAAa,EAAE,SAEV5M,EAAO,CACN,cAAM,gCAAiCA,CAAK,EAChDA,EAAM,SAAWA,EAAM,QAAQ,SAAS,OAAO,EACjDC,EACE,0EACF,EAEAA,EAAS,kDAAkD,CAC7D,QACA,CACAF,EAAW,EAAK,EAEpB,GAEkB,GACjB,CAACsM,EAAWE,CAAgB,CAAC,EAE1B,MAAAU,EAAyBC,GAAiB,CAC9CZ,EAAaY,CAAY,CAC3B,EAEMC,EAAwBC,GAAgB,CAC5CZ,EAAoBY,CAAW,CACjC,EAEA,OAAItN,EAEAvB,EAAA,KAACgJ,GAAY,OAAM,iBACjB,UAAA9I,EAAA,IAACwI,GAAA,CACC,MAAM,yBACN,YAAY,yCACd,EACAxI,EAAA,IAAC,OACC,UAAU,mDACV,MAAO,CAAE,UAAW,OAAQ,EAE5B,SAAAA,EAAA,IAAC,MAAI,WAAU,8BAA8B,KAAK,SAChD,SAAAA,EAAA,IAAC,OAAK,WAAU,kBAAkB,sBAAU,CAC9C,IACF,EACF,EAKFF,EAAA,KAACgJ,GAAY,OAAM,sBACjB,UAAA9I,EAAA,IAACwI,GAAA,CACC,MAAM,8BACN,YAAY,oEACd,EAEA1I,OAAC,MAAI,WAAU,gBACb,UAACE,EAAA,WAAI,UAAU,oBACb,SAAAA,MAAC,OAAI,UAAU,oDACb,gBAAC,MACC,WAACA,EAAA,UAAG,UAAU,mBAAmB,SAAmB,wBACpDF,OAAC,IAAE,WAAU,iCAAiC,sFAEjC,IACXE,EAAA,IAAC,KACC,KAAK,mEACL,OAAO,SACP,IAAI,sBACJ,UAAU,eACX,uBAED,CACF,GACF,EACF,GACF,EAECuB,GACEzB,OAAA,OAAI,UAAU,0BAA0B,KAAK,QAC5C,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EACAuB,CAAA,EACH,EAIFzB,OAAC,MAAI,WAAU,WACb,UAACE,MAAA,OAAI,UAAU,WACb,SAAAA,EAAA,IAAC4O,GAAA,CACC,QAASR,EACT,MAAOR,EACP,SAAUY,EACV,eAAgBR,GAAA,YAAAA,EAAe,iBAEnC,EACAhO,MAAC,MAAI,WAAU,WACb,SAAAA,EAAA,IAAC6O,GAAA,CACC,QAASR,EACT,MAAOP,EACP,SAAUY,CAAA,EAEd,IACF,EAGCV,SACE,MAAI,WAAU,WACb,SAAChO,EAAA,WAAI,UAAU,SACb,SAAAA,EAAA,IAAC8O,GAAA,CACC,KAAMd,EAAc,SACpB,iBAAAF,CAAA,GAEJ,CACF,GAIFhO,OAAC,MAAI,WAAU,WAEb,UAACE,EAAA,WAAI,UAAU,uBACZ,SACCgO,GAAAhO,EAAA,IAAC+O,GAAA,CACC,KAAMf,EAAc,UACpB,UAAAJ,EACA,iBAAAE,CAAA,GAGN,EAGC9N,EAAA,WAAI,UAAU,uBACZ,SACCgO,GAAAhO,EAAA,IAACgP,GAAA,CACC,KAAMhB,EAAc,YACpB,MAAO,4BACLF,IAAqB,MACjB,MACEvJ,EAAA8J,EAAgB,KACbY,GAAMA,EAAE,QAAUnB,CACrB,IAFA,YAAAvJ,EAEG,KACL,IACA,EACN,GACA,iBAAAuJ,CAAA,EAGN,IACF,QAGC,MAAI,WAAU,WACb,SAAC9N,MAAA,OAAI,UAAU,SACb,SAAAA,EAAA,IAACkP,GAAA,CACC,KAAMhB,EACN,QAAA7M,EACA,UAAAuM,EACA,iBAAAE,CAAA,GAEJ,CACF,GAGA9N,EAAA,IAAC,MAAI,WAAU,WACb,SAAAA,EAAA,IAAC,MAAI,WAAU,SACb,SAAAF,OAAC,MAAI,WAAU,OACb,UAACA,OAAA,OAAI,UAAU,cACb,UAACA,OAAA,MAAG,UAAU,kBACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EAAe,wBAElB,EACCA,EAAA,SAAE,UAAU,uBAAuB,SAGpC,qEACF,EACAA,MAAC,MAAI,WAAU,YACb,SAAAA,EAAA,IAACmP,GAAA,CACC,UAAAvB,EACA,iBAAAE,CAAA,EAEJ,GACF,EACF,GACF,EAGA9N,EAAA,IAAC,MAAI,WAAU,WACb,SAAAA,EAAA,IAAC,MAAI,WAAU,SACb,SAAAF,OAAC,MAAI,WAAU,OACb,UAACA,OAAA,OAAI,UAAU,cACb,UAACA,OAAA,MAAG,UAAU,kBACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,sBACL,UAAU,OACX,EAAe,0BAElB,EACCA,EAAA,SAAE,UAAU,uBAAuB,SAEpC,mDACF,EACAA,MAAC,MAAI,WAAU,YACb,SAAAA,EAAA,IAACoP,GAAA,CACC,UAAAxB,EACA,iBAAAE,CAAA,EAEJ,GACF,EACF,EACF,GACF,IACF,CAEJ,2GCnTMuB,GAAkB,IAAM,CACXhJ,GAAY,EAC7B,KAAM,CAACpF,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAACyJ,EAASC,CAAU,EAAI1J,WAAS,EAAE,EACnC,CAACwO,EAAWC,CAAY,EAAIzO,WAAS,EAAK,EAC1C,CAAC0O,EAAiBC,CAAkB,EAAI3O,WAAS,IAAI,EACrD,CAACqH,EAAUC,CAAW,EAAItH,WAAS,CACvC,KAAM,GACN,YAAa,GACb,MAAO,UACR,EAGDW,YAAU,IAAM,CACCE,EAAA,CACjB,EAAG,EAAE,EAEL,MAAMA,EAAiB,SAAY,CAC7B,IACFL,EAAW,EAAI,EACf,KAAM,CAAE,SAAAM,EAAU,KAAAC,CAAS,QAAM0H,EAAS,cAAc,EAEpD1H,EAAK,QACOX,EAAAW,EAAK,MAAQ,EAAE,EAEpBL,EAAAK,EAAK,SAAW,2BAA2B,QAE/CN,EAAO,CACN,cAAM,yBAA0BA,CAAK,EAC7CC,EAAS,kCAAkC,SAC3C,CACAF,EAAW,EAAK,EAEpB,EAEM8J,EAAoB,CAACnG,EAAOqE,IAAU,CAC1ClB,EAAa6B,IAAU,CACrB,GAAGA,EACH,CAAChF,CAAK,EAAGqE,CAAA,EACT,CACJ,EAEMhB,EAAe,MAAO3F,GAAM,CAChCA,EAAE,eAAe,EACjBnB,EAAS,EAAE,EACXgJ,EAAW,EAAE,EAET,IACF,IAAI5I,EAAUC,EAEV2N,EACD,CAAE,SAAA5N,EAAU,KAAAC,GAAS,MAAM0H,EAAS,eACnCiG,EAAgB,GAChBrH,CACF,EAEC,CAAE,SAAAvG,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,eAAepB,CAAQ,EAG1DtG,EAAK,SACP2I,EACEgF,EACI,iCACA,gCACN,EACAD,EAAa,EAAK,EAClBE,EAAmB,IAAI,EACvBrH,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC5CzG,EAAA,GAENH,EAAAK,EAAK,SAAW,yBAAyB,QAE7CN,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAC3CC,EAAS,kCAAkC,EAE/C,EAEMkO,EAAcjN,GAAa,CAC/BgN,EAAmBhN,CAAQ,EACf2F,EAAA,CACV,KAAM3F,EAAS,KACf,YAAaA,EAAS,aAAe,GACrC,MAAOA,EAAS,OAAS,UAC1B,EACD8M,EAAa,EAAI,CACnB,EAEM/F,EAAe,MAAOmG,GAAe,CACzC,GACG,OAAO,QACN,gFAMA,IACF,KAAM,CAAE,SAAA/N,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,eAAeoG,CAAU,EAE/D9N,EAAK,SACP2I,EAAW,gCAAgC,EAC5B7I,EAAA,GAENH,EAAAK,EAAK,SAAW,2BAA2B,QAE/CN,EAAO,CACN,cAAM,yBAA0BA,CAAK,EAC7CC,EAAS,kCAAkC,EAE/C,EAEMoO,EAAkB,IAAM,CAC5BH,EAAmB,IAAI,EACvBrH,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC3DmH,EAAa,EAAI,CACnB,EAEMM,EAAa,IAAM,CACvBN,EAAa,EAAK,EAClBE,EAAmB,IAAI,EACvBrH,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC3D5G,EAAS,EAAE,CACb,EAEA,OAEI1B,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAM,4BACN,YAAY,4CACZ,QAAS,GACX,EAEA1I,OAACgJ,GAAY,OAAM,aAEjB,UAAA9I,MAAC,OAAI,UAAU,QACb,SAACF,EAAA,YAAI,UAAU,yBACb,UAACE,MAAA,OAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,kGAGlC,CACF,GACAA,MAAC,MAAI,WAAU,8BACb,SAAAF,EAAA,KAAC,UACC,QAAS8P,EACT,UAAU,kDAEV,UAAA5P,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,iBAGpB,IACF,CACF,GAGCuB,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAAAE,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EACAuB,CAAA,EACH,EAGDgJ,GACEzK,OAAA,OAAI,UAAU,4BAA4B,KAAK,QAC9C,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EACAuK,CAAA,EACH,EAIDvK,EAAA,WAAI,UAAU,cACZ,SACCqB,EAAAvB,EAAA,KAAC,MAAI,WAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,YAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,EAC/C,EACAA,EAAA,WAAI,UAAU,2BAA2B,SAE1C,2BACF,EACEiB,EAAW,SAAW,EACvBnB,EAAA,YAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,aACnD,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,iCACX,EACAA,EAAA,WAAI,UAAU,iCAAiC,SAEhD,wBACCA,EAAA,SAAE,UAAU,sBAAsB,SAEnC,oEACAF,EAAA,KAAC,UACC,QAAS8P,EACT,UAAU,kCAEV,UAAA5P,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,0BAElB,EACF,EAIEF,OAAAC,EAAA,oBAACC,EAAA,WAAI,UAAU,oBACb,SAACA,EAAA,WAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,WAAU,QACf,UAACE,EAAA,aACC,gBAAC,KACC,WAAAA,MAAC,MAAG,SAAQ,aACZA,MAAC,MAAG,SAAW,gBACfA,MAAC,MAAG,SAAK,UACTA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAO,aACb,CACF,SACC,QACE,UAAAiB,EAAW,IAAKwB,wBACd,KACC,WAAAzC,MAAC,KACC,UAAAF,OAAC,MAAI,WAAU,4BACb,UAAAE,EAAA,IAAC,OACC,UAAU,eACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,gBAAiByC,EAAS,OAAS,UACrC,CACD,SACA,MACC,WAAAzC,EAAA,IAAC,MAAI,WAAU,UAAW,SAAAyC,EAAS,KAAK,EACxC3C,OAAC,QAAM,WAAU,aAAa,cAC1B2C,EAAS,KACb,GACF,IACF,CACF,GACAzC,MAAC,MACC,SAACA,EAAA,YAAK,UAAU,aACb,SAAAyC,EAAS,aAAe,iBAC3B,CACF,GACCzC,MAAA,MACC,SAACF,OAAA,QAAK,UAAU,qBACb,YAAAyE,EAAA9B,EAAS,SAAT,YAAA8B,EAAiB,QAAS,EAAE,UAC/B,CACF,GACAvE,MAAC,MACE,SAAI,SAAKyC,EAAS,SAAS,EAAE,qBAChC,QACC,KACC,UAAA3C,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,UAAAE,EAAA,IAAC,UACC,QAAS,IAAM0P,EAAWjN,CAAQ,EAClC,UAAU,iCACV,MAAM,OAEN,SAAAzC,MAAC,eAAa,MAAK,gBAAiB,GACtC,EACAA,EAAA,IAAC,UACC,QAAS,IAAMwJ,EAAa/G,EAAS,EAAE,EACvC,UAAU,gCACV,MAAM,SAEN,SAAAzC,MAAC,eAAa,MAAK,4BAA6B,IAClD,EACF,CACF,KAjDOyC,EAAS,EAkDlB,EACD,CACH,GACF,EACF,GACF,EAGAzC,EAAA,IAAC,MAAI,WAAU,YACb,SAAAA,MAAC,MAAI,WAAU,UACZ,SAAAiB,EAAW,IAAKwB,UACfzC,SAAA,IAAC,MAAsB,WAAU,SAC/B,SAAAA,EAAA,IAAC,MAAI,WAAU,0BACb,SAAAA,MAAC,MAAI,WAAU,gBACb,SAAAF,EAAA,KAAC,MAAI,WAAU,yBACb,UAAAE,MAAC,OAAI,UAAU,cACb,SAACF,EAAA,YAAI,UAAU,4BACb,UAAAE,EAAA,IAAC,OACC,UAAU,eACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,gBACEyC,EAAS,OAAS,UACtB,CACD,EACD3C,OAAC,MAAI,WAAU,cACb,UAAAE,EAAA,IAAC,KAAG,WAAU,eACX,SAAAyC,EAAS,KACZ,EACA3C,OAAC,QAAM,WAAU,aAAa,cAC1B2C,EAAS,KACb,GACF,IACF,CACF,GAEA3C,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,gBACCA,EAAA,aACE,SAASyC,EAAA,aAAe,gBAC3B,IACF,EAEA3C,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,UACAF,OAAC,OAAK,WAAU,qBACb,YAAAyE,EAAA9B,EAAS,SAAT,YAAA8B,EAAiB,QAAS,EAAE,SAC/B,IACF,EAEAzE,OAAC,MAAI,WAAU,uBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,YACAA,MAAC,SACE,SAAI,SACHyC,EAAS,SACX,EAAE,oBACJ,IACF,QAEC,MAAI,WAAU,SACb,SAAC3C,EAAA,YAAI,UAAU,yBACb,UAAAA,EAAA,KAAC,UACC,QAAS,IAAM4P,EAAWjN,CAAQ,EAClC,UAAU,2CACV,MAAM,OAEN,UAAAzC,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,QAElB,EAEAF,EAAA,KAAC,UACC,QAAS,IAAM0J,EAAa/G,EAAS,EAAE,EACvC,UAAU,0CACV,MAAM,SAEN,UAAAzC,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EAAe,WAElB,EACF,CACF,GACF,EACF,GACF,GApFQyC,EAAS,EAqFnB,EACD,EACH,CACF,IACF,CAEJ,GAGC6M,GACEtP,MAAA,OAAI,UAAU,gBAAgB,QAAS6P,EACtC,SAAA/P,OAAC,MAAI,WAAU,gBAAgB,QAAU6C,GAAMA,EAAE,gBAC/C,YAAC7C,OAAA,OAAI,UAAU,eACb,UAACA,OAAA,MAAG,UAAU,cACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,oBACL,UAAU,OACX,EACAwP,EAAkB,gBAAkB,uBACvC,EACAxP,EAAA,IAAC,UACC,KAAK,SACL,UAAU,cACV,QAAS6P,EAET,SAAA7P,MAAC,eAAa,MAAK,yBAA0B,IAC/C,EACF,EAEAF,OAAC,OAAK,UAAUwI,EACd,UAAAtI,MAAC,OAAI,UAAU,aACb,SAACF,EAAA,YAAI,UAAU,MACb,UAACA,OAAA,OAAI,UAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,mBAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,MAAOmI,EAAS,KAChB,SAAWxF,GACTyI,EAAkB,OAAQzI,EAAE,OAAO,KAAK,EAE1C,UAAU,eACV,YAAY,sBACZ,SAAQ,IACV,EACF,EAEA7C,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,kBACL,UAAU,OACX,EAAe,eAElB,EACAA,EAAA,IAAC,YACC,MAAOmI,EAAS,YAChB,SAAWxF,GACTyI,EAAkB,cAAezI,EAAE,OAAO,KAAK,EAEjD,UAAU,eACV,KAAM,EACN,YAAY,sCACd,EACF,EAEA7C,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,OACX,EAAe,SAElB,EACAF,OAAC,MAAI,WAAU,kCACb,UAAAE,EAAA,IAAC,SACC,KAAK,QACL,MAAOmI,EAAS,MAChB,SAAWxF,GACTyI,EAAkB,QAASzI,EAAE,OAAO,KAAK,EAE3C,UAAU,kCACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,EACzC,EACA3C,EAAA,IAAC,SACC,KAAK,OACL,MAAOmI,EAAS,MAChB,SAAWxF,GACTyI,EAAkB,QAASzI,EAAE,OAAO,KAAK,EAE3C,UAAU,eACV,YAAY,WACd,EACF,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAExC,8CACF,IACF,CACF,GAEAF,OAAC,MAAI,WAAU,eACb,UAAAE,EAAA,IAAC,UACC,KAAK,SACL,QAAS6P,EACT,UAAU,sCACX,kBAED,EACA/P,EAAA,KAAC,UACC,KAAK,SACL,UAAU,kCAEV,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EACAwP,EAAkB,kBAAoB,oBACzC,CACF,GACF,IACF,CACF,GAEJ,IACF,CAEJ,2GChgBMM,GAAY,IAAM,CACLzJ,GAAY,EAC7B,KAAM,CAACzC,EAAMC,CAAO,EAAI/C,WAAS,EAAE,EAC7B,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAACyJ,EAASC,CAAU,EAAI1J,WAAS,EAAE,EACnC,CAACwO,EAAWC,CAAY,EAAIzO,WAAS,EAAK,EAC1C,CAACiP,EAAYC,CAAa,EAAIlP,WAAS,IAAI,EAC3C,CAACqH,EAAUC,CAAW,EAAItH,WAAS,CACvC,KAAM,GACP,EAGDW,YAAU,IAAM,CACLwO,EAAA,CACX,EAAG,EAAE,EAEL,MAAMA,EAAW,SAAY,CACvB,IACF3O,EAAW,EAAI,EACf,KAAM,CAAE,SAAAM,EAAU,KAAAC,CAAS,QAAM0H,EAAS,QAAQ,EAE9C1H,EAAK,QACCgC,EAAAhC,EAAK,MAAQ,EAAE,EAEdL,EAAAK,EAAK,SAAW,qBAAqB,QAEzCN,EAAO,CACN,cAAM,mBAAoBA,CAAK,EACvCC,EAAS,kCAAkC,SAC3C,CACAF,EAAW,EAAK,EAEpB,EAEM8J,EAAoB,CAACnG,EAAOqE,IAAU,CAC1ClB,EAAa6B,IAAU,CACrB,GAAGA,EACH,CAAChF,CAAK,EAAGqE,CAAA,EACT,CACJ,EAEMhB,EAAe,MAAO3F,GAAM,CAChCA,EAAE,eAAe,EACjBnB,EAAS,EAAE,EACXgJ,EAAW,EAAE,EAET,IACF,IAAI5I,EAAUC,EAEVkO,EACD,CAAE,SAAAnO,EAAU,KAAAC,GAAS,MAAM0H,EAAS,UACnCwG,EAAW,GACX5H,CACF,EAEC,CAAE,SAAAvG,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,UAAUpB,CAAQ,EAGrDtG,EAAK,SACP2I,EACEuF,EAAa,4BAA8B,2BAC7C,EACAR,EAAa,EAAK,EAClBS,EAAc,IAAI,EACN5H,EAAA,CAAE,KAAM,GAAI,EACf6H,EAAA,GAEAzO,EAAAK,EAAK,SAAW,oBAAoB,QAExCN,EAAO,CACN,cAAM,kBAAmBA,CAAK,EACtCC,EAAS,kCAAkC,EAE/C,EAEMkO,EAAchK,GAAQ,CAC1BsK,EAActK,CAAG,EACL0C,EAAA,CACV,KAAM1C,EAAI,KACX,EACD6J,EAAa,EAAI,CACnB,EAEM/F,EAAe,MAAO0G,GAAU,CACpC,GACG,OAAO,QACN,2EAMA,IACF,KAAM,CAAE,SAAAtO,EAAU,KAAAC,CAAA,EAAS,MAAM0H,EAAS,UAAU2G,CAAK,EAErDrO,EAAK,SACP2I,EAAW,2BAA2B,EAC7ByF,EAAA,GAEAzO,EAAAK,EAAK,SAAW,sBAAsB,QAE1CN,EAAO,CACN,cAAM,oBAAqBA,CAAK,EACxCC,EAAS,kCAAkC,EAE/C,EAEMoO,EAAkB,IAAM,CAC5BI,EAAc,IAAI,EACN5H,EAAA,CAAE,KAAM,GAAI,EACxBmH,EAAa,EAAI,CACnB,EAEMM,EAAa,IAAM,CACvBN,EAAa,EAAK,EAClBS,EAAc,IAAI,EACN5H,EAAA,CAAE,KAAM,GAAI,EACxB5G,EAAS,EAAE,CACb,EAEA,OAEI1B,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACwI,GAAA,CACC,MAAM,sBACN,YAAY,sCACZ,QAAS,GACX,EAEA1I,OAACgJ,GAAY,OAAM,OAEjB,UAAA9I,MAAC,OAAI,UAAU,QACb,SAACF,EAAA,YAAI,UAAU,yBACb,UAACE,MAAA,OAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,4GAGlC,CACF,GACAA,MAAC,MAAI,WAAU,8BACb,SAAAF,EAAA,KAAC,UACC,QAAS8P,EACT,UAAU,kDAEV,UAAA5P,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,YAGpB,IACF,CACF,GAGCuB,GACEzB,OAAA,OAAI,UAAU,2BAA2B,KAAK,QAC7C,UAAAE,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EACAuB,CAAA,EACH,EAGDgJ,GACEzK,OAAA,OAAI,UAAU,4BAA4B,KAAK,QAC9C,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EACAuK,CAAA,EACH,EAIDvK,EAAA,WAAI,UAAU,cACZ,SACCqB,EAAAvB,EAAA,KAAC,MAAI,WAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,YAAAE,EAAA,IAAC,gBACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,EAC/C,EACAA,EAAA,WAAI,UAAU,2BAA2B,SAAe,qBAC3D,EACE4D,EAAK,SAAW,EACjB9D,EAAA,YAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,aACnD,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,iCACX,EACAA,EAAA,WAAI,UAAU,iCAAiC,SAEhD,kBACCA,EAAA,SAAE,UAAU,sBAAsB,SAEnC,+DACAF,EAAA,KAAC,UACC,QAAS8P,EACT,UAAU,kCAEV,UAAA5P,EAAA,IAAC,gBACC,KAAK,wBACL,UAAU,OACX,EAAe,qBAElB,EACF,EAIEF,OAAAC,EAAA,oBAACC,EAAA,WAAI,UAAU,oBACb,SAACA,EAAA,WAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,WAAU,QACf,UAACE,EAAA,aACC,gBAAC,KACC,WAAAA,MAAC,MAAG,SAAQ,aACZA,MAAC,MAAG,SAAK,UACTA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAO,aACb,CACF,SACC,QACE,UAAA4D,EAAK,IAAK8B,wBACR,KACC,WAAA1F,MAAC,KACC,UAAAF,OAAC,MAAI,WAAU,4BACb,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,uBACX,SACA,MACC,WAAAA,EAAA,IAAC,MAAI,WAAU,UAAW,SAAA0F,EAAI,KAAK,EACnC5F,OAAC,QAAM,WAAU,aAAa,cAC1B4F,EAAI,KACR,GACF,IACF,CACF,GACC1F,MAAA,MACC,SAACF,OAAA,QAAK,UAAU,qBACb,YAAAyE,EAAAmB,EAAI,SAAJ,YAAAnB,EAAY,QAAS,EAAE,UAC1B,CACF,GACAvE,MAAC,MACE,SAAI,SAAK0F,EAAI,SAAS,EAAE,qBAC3B,QACC,KACC,UAAA5F,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,UAAAE,EAAA,IAAC,UACC,QAAS,IAAM0P,EAAWhK,CAAG,EAC7B,UAAU,iCACV,MAAM,OAEN,SAAA1F,MAAC,eAAa,MAAK,gBAAiB,GACtC,EACAA,EAAA,IAAC,UACC,QAAS,IAAMwJ,EAAa9D,EAAI,EAAE,EAClC,UAAU,gCACV,MAAM,SAEN,SAAA1F,MAAC,eAAa,MAAK,4BAA6B,IAClD,EACF,CACF,KAxCO0F,EAAI,EAyCb,EACD,CACH,GACF,EACF,GACF,EAGA1F,EAAA,IAAC,MAAI,WAAU,YACb,SAAAA,MAAC,MAAI,WAAU,UACZ,SAAA4D,EAAK,IAAK8B,UACT1F,SAAA,IAAC,MAAiB,WAAU,SAC1B,SAAAA,EAAA,IAAC,MAAI,WAAU,0BACb,SAAAA,MAAC,MAAI,WAAU,gBACb,SAAAF,EAAA,KAAC,MAAI,WAAU,yBACb,UAAAE,MAAC,OAAI,UAAU,cACb,SAACF,EAAA,YAAI,UAAU,4BACb,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,uBACV,MAAO,CAAE,SAAU,QAAS,EAC7B,EACDF,OAAC,MAAI,WAAU,cACb,UAAAE,EAAA,IAAC,KAAG,WAAU,eAAgB,SAAA0F,EAAI,KAAK,EACvC5F,OAAC,QAAM,WAAU,aAAa,cAC1B4F,EAAI,KACR,GACF,IACF,CACF,GAEA5F,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,UACAF,OAAC,OAAK,WAAU,qBACb,YAAAyE,EAAAmB,EAAI,SAAJ,YAAAnB,EAAY,QAAS,EAAE,SAC1B,IACF,EAEAzE,OAAC,MAAI,WAAU,sBACb,UAACE,EAAA,aAAM,UAAU,qBAAqB,SAEtC,YACAA,MAAC,SACE,SAAI,SAAK0F,EAAI,SAAS,EAAE,oBAC3B,IACF,QAEC,MAAI,WAAU,SACb,SAAC5F,EAAA,YAAI,UAAU,yBACb,UAAAA,EAAA,KAAC,UACC,QAAS,IAAM4P,EAAWhK,CAAG,EAC7B,UAAU,2CACV,MAAM,OAEN,UAAA1F,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,QAElB,EAEAF,EAAA,KAAC,UACC,QAAS,IAAM0J,EAAa9D,EAAI,EAAE,EAClC,UAAU,0CACV,MAAM,SAEN,UAAA1F,EAAA,IAAC,gBACC,KAAK,6BACL,UAAU,OACX,EAAe,WAElB,EACF,CACF,GACF,EACF,GACF,GAnEQ0F,EAAI,EAoEd,EACD,EACH,CACF,IACF,CAEJ,GAGC4J,GACEtP,MAAA,OAAI,UAAU,gBAAgB,QAAS6P,EACtC,SAAA/P,OAAC,MAAI,WAAU,gBAAgB,QAAU6C,GAAMA,EAAE,gBAC/C,YAAC7C,OAAA,OAAI,UAAU,eACb,UAACA,OAAA,MAAG,UAAU,cACZ,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EACA+P,EAAa,WAAa,kBAC7B,EACA/P,EAAA,IAAC,UACC,KAAK,SACL,UAAU,cACV,QAAS6P,EAET,SAAA7P,MAAC,eAAa,MAAK,yBAA0B,IAC/C,EACF,EAEAF,OAAC,OAAK,UAAUwI,EACd,UAACtI,EAAA,WAAI,UAAU,aACb,SAACA,EAAA,WAAI,UAAU,MACb,SAAAF,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,SAAM,UAAU,aACf,UAAAE,EAAA,IAAC,gBACC,KAAK,iBACL,UAAU,OACX,EAAe,cAElB,EACAA,EAAA,IAAC,SACC,KAAK,OACL,MAAOmI,EAAS,KAChB,SAAWxF,GACTyI,EAAkB,OAAQzI,EAAE,OAAO,KAAK,EAE1C,UAAU,eACV,YAAY,iBACZ,SAAQ,GACV,EACC3C,EAAA,aAAM,UAAU,uBAAuB,SAGxC,2EACF,EACF,GACF,EAEAF,OAAC,MAAI,WAAU,eACb,UAAAE,EAAA,IAAC,UACC,KAAK,SACL,QAAS6P,EACT,UAAU,sCACX,kBAED,EACA/P,EAAA,KAAC,UACC,KAAK,SACL,UAAU,kCAEV,UAAAE,EAAA,IAAC,gBACC,KAAK,0BACL,UAAU,OACX,EACA+P,EAAa,aAAe,eAC/B,CACF,GACF,IACF,CACF,GAEJ,IACF,CAEJ,2GChbaI,GAAmB,MAAOhM,EAAS,KAAO,CACrD,GAAI,CACF,MAAMiM,EAAc,IAAI,gBAEpBjM,EAAO,MAAMiM,EAAY,OAAO,OAAQjM,EAAO,IAAI,EACnDA,EAAO,OAAOiM,EAAY,OAAO,QAASjM,EAAO,KAAK,EACtDA,EAAO,QAAQiM,EAAY,OAAO,SAAUjM,EAAO,MAAM,EACzDA,EAAO,QAAQiM,EAAY,OAAO,SAAUjM,EAAO,MAAM,EACzDA,EAAO,YAAYiM,EAAY,OAAO,aAAcjM,EAAO,UAAU,EAEzE,KAAM,CAAE,SAAAvC,EAAU,KAAAC,GAAS,MAAMwO,GAAQ,mBAAmBD,CAAW,EAAE,EAEzE,GAAI,CAACxO,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACV,CACA,EAGa+O,GAAiB,MAAOC,GAAc,CACjD,GAAI,CACF,KAAM,CAAE,SAAA3O,EAAU,KAAAC,CAAM,EAAG,MAAMwO,GAC/B,mBAAmBE,CAAS,WAC5B,CACE,OAAQ,OAChB,CACK,EAED,GAAI,CAAC3O,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,yBAA0BA,CAAK,EACvCA,CACV,CACA,EAGaiP,GAAgB,MAAOD,GAAc,CAChD,GAAI,CACF,KAAM,CAAE,SAAA3O,EAAU,KAAAC,CAAM,EAAG,MAAMwO,GAC/B,mBAAmBE,CAAS,UAC5B,CACE,OAAQ,OAChB,CACK,EAED,GAAI,CAAC3O,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtCA,CACV,CACA,EAGakP,GAAgB,MAAOF,GAAc,CAChD,GAAI,CACF,KAAM,CAAE,SAAA3O,EAAU,KAAAC,GAAS,MAAMwO,GAAQ,mBAAmBE,CAAS,GAAI,CACvE,OAAQ,QACd,CAAK,EAED,GAAI,CAAC3O,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtCA,CACV,CACA,EC/EA,SAAwBmP,IAAoB,CAC1C,KAAM,CAACC,EAAUC,CAAW,EAAI9P,WAAS,EAAE,EACrC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAC/B,CAAC0C,EAAaC,CAAc,EAAI3C,WAAS,CAAC,EAC1C,CAAC4C,EAAYC,CAAa,EAAI7C,WAAS,CAAC,EACxC,CAAC+P,EAAcC,CAAe,EAAIhQ,WAAS,KAAK,EAChD,CAACiQ,EAAYC,CAAa,EAAIlQ,WAAS,EAAE,EACzC,CAACmQ,EAAOC,CAAQ,EAAIpQ,EAAS,UAAE,MAAO,EAAG,QAAS,EAAG,SAAU,EAAG,EAElEqQ,EAAgB,SAAY,CAC5B,IACF7P,EAAW,EAAI,EACT,MAAAM,EAAW,MAAMuO,GAAiB,CACtC,KAAM3M,EACN,MAAO,GACP,OAAQqN,EACR,OAAQE,CAAA,CACT,EAED,GAAInP,EAAS,QAAS,CACRgP,EAAAhP,EAAS,KAAK,QAAQ,EACpB+B,EAAA/B,EAAS,KAAK,WAAW,KAAK,EAGtC,MAAAwP,EAAgBxP,EAAS,KAAK,WAAW,MAC/CsP,MAAkB,CAAE,GAAGjH,EAAM,MAAOmH,GAAgB,SAE/CrP,EAAK,CACZP,EAAS,0BAA0B,EAC3B,cAAM,wBAAyBO,CAAG,SAC1C,CACAT,EAAW,EAAK,EAEpB,EAEAG,YAAU,IAAM,CACA0P,EAAA,CACb,GAAC3N,EAAaqN,EAAcE,CAAU,CAAC,EAEpC,MAAAM,EAAgB,MAAOd,GAAc,CACrC,IACF,MAAMD,GAAeC,CAAS,EAChBY,EAAA,OACF,CACZ3P,EAAS,2BAA2B,EAExC,EAEM8P,EAAe,MAAOf,GAAc,CACpC,IACF,MAAMC,GAAcD,CAAS,EACfY,EAAA,OACF,CACZ3P,EAAS,0BAA0B,EAEvC,EAEMgI,EAAe,MAAO+G,GAAc,CACpC,UAAO,QAAQ,6EAA6E,EAC1F,IACF,MAAME,GAAcF,CAAS,EACfY,EAAA,OACF,CACZ3P,EAAS,0BAA0B,EAGzC,EAEM+P,EAAgB5O,GAAM,CAC1BA,EAAE,eAAe,EACjBc,EAAe,CAAC,EACF0N,EAAA,CAChB,EAEMxH,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,UACT,EAGG4H,EAAe,CAACC,EAAMC,EAAY,MAC/BD,EAAK,OAASC,EAAYD,EAAK,UAAU,EAAGC,CAAS,EAAI,MAAQD,EAG1E,OACGzR,EAAA,IAAA8I,GAAA,CACC,SAAC9I,MAAA,OAAI,UAAU,kBACb,SAACA,MAAA,OAAI,UAAU,MACb,SAACF,OAAA,OAAI,UAAU,SACb,UAACA,OAAA,OAAI,UAAU,yDACb,UAACE,EAAA,UAAG,UAAU,UAAU,SAAkB,uBAC1CF,OAAC,MAAI,WAAU,eACb,UAACA,OAAA,OAAI,UAAU,mBAAmB,oBAAQmR,EAAM,OAAM,EACtDnR,OAAC,MAAI,WAAU,mBAAmB,sBAAUmR,EAAM,SAAQ,EAC1DnR,OAAC,MAAI,WAAU,mBAAmB,uBAAWmR,EAAM,SAAS,GAC9D,IACF,EAEC1P,GACEvB,MAAA,OAAI,UAAU,qBAAqB,KAAK,QACtC,SACHuB,EAAA,EAIFvB,EAAA,IAAC,MAAI,WAAU,YACb,SAAAA,EAAA,IAAC,MAAI,WAAU,YACb,SAAAF,OAAC,MAAI,WAAU,UACb,UAACA,OAAA,OAAI,UAAU,WACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAa,kBAC3CF,EAAA,KAAC,UACC,UAAU,cACV,MAAO+Q,EACP,SAAWlO,GAAM,CACCmO,EAAAnO,EAAE,OAAO,KAAK,EAC9Bc,EAAe,CAAC,CAClB,EAEA,UAACzD,EAAA,cAAO,MAAM,MAAM,SAAY,iBAC/BA,EAAA,cAAO,MAAM,UAAU,SAAgB,qBACvCA,EAAA,cAAO,MAAM,WAAW,SAAQ,eACnC,EACF,EACAF,OAAC,MAAI,WAAU,WACb,UAACE,EAAA,aAAM,UAAU,aAAa,SAAM,WACnCF,EAAA,aAAK,SAAUyR,EAAc,UAAU,SACtC,UAAAvR,EAAA,IAAC,SACC,KAAK,OACL,UAAU,eACV,YAAY,8CACZ,MAAO+Q,EACP,SAAWpO,GAAMqO,EAAcrO,EAAE,OAAO,KAAK,EAC/C,QACC,SAAO,MAAK,SAAS,UAAU,uBAAuB,SAEvD,WACF,GACF,GACF,EACF,GACF,QAGC,MAAI,WAAU,OACb,SAAC7C,EAAA,YAAI,UAAU,YACZ,UAAAuB,QACE,MAAI,WAAU,mBACb,SAAArB,MAAC,OAAI,UAAU,iBAAiB,KAAK,SACnC,eAAC,OAAK,WAAU,kBAAkB,SAAU,cAC9C,GACF,EACE2Q,EAAS,SAAW,QACrB,MAAI,WAAU,mBACb,SAAC3Q,MAAA,KAAE,UAAU,aAAa,8BAAkB,CAC9C,SAEC,MAAI,WAAU,mBACb,SAACF,OAAA,SAAM,UAAU,oBACf,UAACE,EAAA,aACC,gBAAC,KACC,WAAAA,MAAC,MAAG,SAAM,WACVA,MAAC,MAAG,SAAK,UACTA,MAAC,MAAG,SAAO,YACXA,MAAC,MAAG,SAAS,cACbA,MAAC,MAAG,SAAM,WACVA,MAAC,MAAG,SAAI,SACRA,MAAC,MAAG,SAAO,aACb,CACF,SACC,QACE,UAAA2Q,EAAS,IAAKgB,wBACZ,KACC,WAAA7R,OAAC,KACC,WAACE,MAAA,UAAQ,WAAQ,MAAO,GACvB2R,EAAQ,SACP3R,MAAC,OACC,SAACA,EAAA,aACC,eAAC,IAAE,MAAM2R,EAAQ,QAAS,OAAO,SAAS,IAAI,sBAC3C,SAAQA,EAAA,OACX,EACF,EACF,IAEJ,EACA3R,MAAC,MACC,SAACA,MAAA,SAAM,UAAU,aAAc,SAAA2R,EAAQ,MAAM,CAC/C,UACC,KACC,WAAA3R,MAAC,OAAI,MAAO2R,EAAQ,QACjB,SAAaH,EAAAG,EAAQ,OAAO,EAC/B,EACCA,EAAQ,QACN7R,OAAA,SAAM,UAAU,aAAa,uBACjB6R,EAAQ,OAAO,OAC5B,IAEJ,QACC,KACC,UAAA3R,EAAA,IAAC0C,EAAA,CACC,GAAI,gBAAgBiP,EAAQ,SAAS,IAAI,GACzC,UAAU,uBACV,OAAO,SAEN,WAAQpN,EAAAoN,EAAA,SAAS,aAAa,CAAC,IAAvB,YAAApN,EAA0B,QAAS,aAEhD,EACCvE,MAAA,MACC,SAACA,MAAA,QAAK,UAAW,SAAS2R,EAAQ,SAAW,aAAe,YAAY,GACrE,SAAAA,EAAQ,SAAW,WAAa,SACnC,GACF,EACA3R,MAAC,MACC,SAACA,EAAA,aAAO,WAAW2R,EAAQ,SAAS,EAAE,CACxC,GACC3R,MAAA,MACC,SAACF,OAAA,OAAI,UAAU,yBACZ,UAAC6R,EAAQ,SASR3R,EAAA,IAAC,UACC,UAAU,kBACV,QAAS,IAAMsR,EAAaK,EAAQ,EAAE,EACtC,MAAM,OAEN,SAAA3R,MAAC,IAAE,WAAU,YAAa,GAC5B,EAdAA,EAAA,IAAC,UACC,UAAU,kBACV,QAAS,IAAMqR,EAAcM,EAAQ,EAAE,EACvC,MAAM,UAEN,SAAA3R,MAAC,IAAE,WAAU,UAAW,KAW5BA,EAAA,IAAC,UACC,UAAU,iBACV,QAAS,IAAMwJ,EAAamI,EAAQ,EAAE,EACtC,MAAM,SAEN,SAAA3R,MAAC,IAAE,WAAU,UAAW,IAC1B,EACF,CACF,KAtEO2R,EAAQ,EAuEjB,EACD,CACH,IACF,CACF,GAIDjO,EAAa,GACX1D,MAAA,OAAI,UAAU,OACb,SAAAF,EAAA,KAAC,KAAG,WAAU,oCACZ,UAAAE,MAAC,MAAG,UAAW,aAAawD,IAAgB,EAAI,WAAa,EAAE,GAC7D,SAAAxD,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IAAMyD,EAAeD,EAAc,CAAC,EAC7C,SAAUA,IAAgB,EAC3B,sBAGH,EACC,CAAC,GAAG,MAAME,CAAU,CAAC,EAAE,IAAI,CAAC0J,EAAG9N,IAC7BU,MAAA,MAAmB,UAAW,aAAawD,IAAgBlE,EAAQ,EAAI,SAAW,EAAE,GACnF,SAAAU,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IAAMyD,EAAenE,EAAQ,CAAC,EAEtC,SAAQA,EAAA,KALJA,EAAQ,CAOjB,CACD,EACDU,MAAC,MAAG,UAAW,aAAawD,IAAgBE,EAAa,WAAa,EAAE,GACtE,SAAA1D,EAAA,IAAC,UACC,UAAU,YACV,QAAS,IAAMyD,EAAeD,EAAc,CAAC,EAC7C,SAAUA,IAAgBE,EAC3B,iBAGH,IACF,CACF,IAEJ,CACF,IACF,EACF,CACF,GACF,CAEJ", "names": ["menuItems", "BUSINESS_INFO", "generateLocalBusinessSchema", "service", "generateWebsiteSchema", "generateArticleSchema", "article", "generateProductSchema", "product", "generateBreadcrumbSchema", "breadcrumbs", "crumb", "index", "getPageSEOData", "pageType", "content", "language", "baseKeywords", "seoData", "Home5MainDemoMultiPageDark", "jsxs", "Fragment", "jsx", "UnifiedSEO", "Header", "ParallaxContainer", "Hero", "Home", "Footer", "ElegantWebstorePageDark", "translate", "i18n", "useTranslation", "currentLanguage", "products", "setProducts", "useState", "filteredProducts", "setFilteredProducts", "categories", "setCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "loading", "setLoading", "error", "setError", "useEffect", "loadProducts", "loadCategories", "response", "data", "productsAPI", "err", "categoriesAPI", "handleCategoryFilter", "categorySlug", "filtered", "cat", "handleProductClick", "trackEvent", "k", "AnimatedText", "category", "Link", "e", "metadata", "ElegantPortfolioPageDark", "MetaComponent", "Portfolio", "MarqueeDark", "ElegantBlogPageDark", "t", "searchParams", "setSearchParams", "useSearchParams", "blogPosts", "setBlogPosts", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "tags", "setTags", "archiveData", "setArchiveData", "currentCategory", "currentTag", "currentSearch", "params", "blogResult", "blogAPI", "posts", "_a", "pagination", "_b", "categoriesResult", "tagsResult", "tagsAPI", "archiveResult", "archiveAPI", "getTranslation", "post", "field", "translation", "_c", "newParams", "handleTagFilter", "tagSlug", "clearFilters", "c", "Pagination", "tag", "archive", "ElegantPortfolioSinglePageDark", "useParams", "portfolioItem", "allPortfolios", "elm", "RelatedProjects", "ElegantWebstoreSinglePageDark", "id", "navigate", "useNavigate", "setProduct", "loadProduct", "handleDemoClick", "handlePurchaseClick", "type", "formatPrice", "price", "productSchema", "breadcrumbSchema", "productImage", "ProductGallery", "ElegantBlogSinglePageDark", "blog", "setBlog", "fetchBlogPost", "result", "highlightCodeBlocks", "__vitePreload", "articleSchema", "_d", "Comments", "Form", "_e", "_f", "Widget1", "PrivacyPolicyPage", "TermsConditionsPage", "MainPageNotFound", "AdminLogin", "formData", "setFormData", "handleChange", "handleSubmit", "authAPI", "SEO", "AdminDashboard", "user", "setUser", "token", "userData", "AdminLayout", "AdminBlogPosts", "setPosts", "filters", "setFilters", "setPagination", "loadPosts", "key", "value", "adminAPI", "handleDelete", "postId", "handleToggleVisibility", "formatDate", "dateString", "getImageUrl", "filename", "API_BASE_URL", "getStatusBadge", "prev", "englishTranslation", "AdminBlogEditor", "isEditing", "saving", "setSaving", "success", "setSuccess", "availableLanguages", "initialTranslations", "lang", "activeLanguage", "setActiveLanguage", "imagePreview", "setImagePreview", "postRes", "postData", "translationsObj", "jsonError", "handleInputChange", "handleTranslationChange", "handleImageChange", "file", "reader", "formDataToSend", "errorMessage", "TipTapEditor", "html", "newCategoryIds", "newTagIds", "AdminProducts", "newStatus", "p", "handleFilterChange", "getDisplayImage", "img", "AdminProductEditor", "selectedImages", "setSelectedImages", "displayImageIndex", "setDisplayImageIndex", "productResult", "updatedTranslations", "existingImages", "imageUrl", "displayIndex", "handleImagesChange", "files", "newImages", "removeImage", "updated", "_", "i", "setDisplayImage", "updateImageAlt", "alt", "imageIndex", "imageData", "AdminBlogAnalytics", "timeRange", "setTimeRange", "selectedLanguage", "setSelectedLanguage", "analyticsData", "setAnalyticsData", "postsData", "setPostsData", "timeRangeOptions", "languageOptions", "analyticsResult", "postsResult", "handleTimeRangeChange", "newTimeRange", "handleLanguageChange", "newLanguage", "TimeRangeSelector", "LanguageSelector", "AnalyticsOverview", "AnalyticsChart", "HeatmapChart", "l", "PostsTable", "ConversionAnalytics", "StaticPagesAnalytics", "AdminCategories", "showModal", "setShowModal", "editingCategory", "setEditingCategory", "handleEdit", "categoryId", "openCreateModal", "closeModal", "AdminTags", "editingTag", "setEditingTag", "loadTags", "tagId", "getAdminComments", "queryParams", "apiCall", "approveComment", "commentId", "rejectComment", "deleteComment", "AdminCommentsPage", "comments", "setComments", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "stats", "setStats", "fetchComments", "totalComments", "handleApprove", "handleReject", "handleSearch", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "comment"], "ignoreList": [], "sources": ["../../src/data/menu.js", "../../src/utils/seoHelpers.js", "../../src/pages/home/<USER>", "../../src/pages/webstore/page.jsx", "../../src/pages/portfolio/page.jsx", "../../src/pages/blogs/page.jsx", "../../src/pages/portfolio-single/page.jsx", "../../src/pages/webstore-single/page.jsx", "../../src/pages/blog-single/page.jsx", "../../src/pages/privacy-policy/page.jsx", "../../src/pages/terms-conditions/page.jsx", "../../src/pages/otherPages/page.jsx", "../../src/pages/AdminLogin.jsx", "../../src/pages/AdminDashboard.jsx", "../../src/pages/AdminBlogPosts.jsx", "../../src/pages/AdminBlogEditor.jsx", "../../src/pages/AdminProducts.jsx", "../../src/pages/AdminProductEditor.jsx", "../../src/pages/AdminBlogAnalytics.jsx", "../../src/pages/AdminCategories.jsx", "../../src/pages/AdminTags.jsx", "../../src/utils/commentAPI.js", "../../src/pages/admin/comments/page.jsx"], "sourcesContent": ["export const menuItems = [\n  { href: \"/\", text: \"Home\" },\n  { href: \"/about\", text: \"About\" },\n  { href: \"/webstore\", text: \"Webstore\" },\n  { href: \"/services\", text: \"Services\" },\n  // { href: \"/portfolio\", text: \"Portfolio\" },\n  { href: \"/blog\", text: \"Blog\" },\n  { href: \"/contact\", text: \"Contact\" },\n];\n", "// client/src/utils/seoHelpers.js\n\n/**\n * SEO Helper functions for generating page-specific metadata and schema\n * Following 2025 SEO best practices\n */\n\n// Business information constants\nexport const BUSINESS_INFO = {\n  name: \"DevSkills\",\n  fullName: \"DevSkills OÜ\",\n  alternateName: \"DevSkills Development Studio\",\n  description:\n    \"Professional software development services and custom solutions\",\n  url: \"https://devskills.ee\",\n  address: {\n    streetAddress: \"Tornimäe tn 7\",\n    addressLocality: \"Tallinn\",\n    postalCode: \"10145\",\n    addressCountry: \"EE\",\n  },\n  geo: {\n    latitude: 59.437,\n    longitude: 24.7536,\n  },\n  contactPoint: {\n    telephone: \"+372 5628 2038\",\n    contactType: \"customer service\",\n    availableLanguage: [\"English\", \"Estonian\", \"Finnish\", \"German\", \"Swedish\"],\n  },\n  openingHours: {\n    dayOfWeek: [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\"],\n    opens: \"08:00\",\n    closes: \"17:00\",\n  },\n  services: [\n    \"Custom Software Development\",\n    \"Web Development\",\n    \"AI Solutions\",\n    \"White Label Software\",\n    \"Blockchain Development\",\n    \"Mobile Applications Development\",\n    \"Backend Development\",\n    \"Business Management Systems\",\n  ],\n  socialMedia: [\n    \"https://www.facebook.com/devskillsee\",\n    \"https://www.linkedin.com/company/devskills-development-studio\",\n    \"https://twitter.com/DevSkillsEE\",\n  ],\n};\n\n/**\n * Generate local business schema markup\n */\nexport const generateLocalBusinessSchema = () => ({\n  \"@type\": \"LocalBusiness\",\n  \"@id\": `${BUSINESS_INFO.url}/#organization`,\n  name: BUSINESS_INFO.fullName,\n  alternateName: BUSINESS_INFO.alternateName,\n  url: BUSINESS_INFO.url,\n  description: BUSINESS_INFO.description,\n  address: {\n    \"@type\": \"PostalAddress\",\n    ...BUSINESS_INFO.address,\n  },\n  geo: {\n    \"@type\": \"GeoCoordinates\",\n    ...BUSINESS_INFO.geo,\n  },\n  contactPoint: {\n    \"@type\": \"ContactPoint\",\n    ...BUSINESS_INFO.contactPoint,\n  },\n  openingHoursSpecification: {\n    \"@type\": \"OpeningHoursSpecification\",\n    ...BUSINESS_INFO.openingHours,\n  },\n  serviceArea: {\n    \"@type\": \"Country\",\n    name: \"Estonia\",\n  },\n  hasOfferCatalog: {\n    \"@type\": \"OfferCatalog\",\n    name: \"Software Development Services\",\n    itemListElement: BUSINESS_INFO.services.map((service) => ({\n      \"@type\": \"Offer\",\n      itemOffered: {\n        \"@type\": \"Service\",\n        name: service,\n      },\n    })),\n  },\n  logo: {\n    \"@type\": \"ImageObject\",\n    url: `${BUSINESS_INFO.url}/logo.png`,\n    width: \"180\",\n    height: \"60\",\n  },\n  sameAs: BUSINESS_INFO.socialMedia,\n});\n\n/**\n * Generate website schema markup\n */\nexport const generateWebsiteSchema = () => ({\n  \"@type\": \"WebSite\",\n  \"@id\": `${BUSINESS_INFO.url}/#website`,\n  name: BUSINESS_INFO.name,\n  alternateName: BUSINESS_INFO.alternateName,\n  url: BUSINESS_INFO.url,\n  description: BUSINESS_INFO.description,\n  publisher: {\n    \"@id\": `${BUSINESS_INFO.url}/#organization`,\n  },\n  potentialAction: {\n    \"@type\": \"SearchAction\",\n    target: `${BUSINESS_INFO.url}/search?q={search_term_string}`,\n    \"query-input\": \"required name=search_term_string\",\n  },\n  inLanguage: [\"en\", \"et\", \"fi\", \"de\", \"sv\"],\n});\n\n/**\n * Generate article schema for blog posts\n */\nexport const generateArticleSchema = (article) => ({\n  \"@type\": \"Article\",\n  headline: article.title,\n  description: article.excerpt || article.description,\n  image: article.featuredImage || `${BUSINESS_INFO.url}/home.jpg`,\n  author: {\n    \"@type\": \"Person\",\n    name: article.author || BUSINESS_INFO.name,\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: BUSINESS_INFO.name,\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: `${BUSINESS_INFO.url}/logo.png`,\n    },\n  },\n  datePublished: article.publishedAt,\n  dateModified: article.modifiedAt || article.publishedAt,\n  mainEntityOfPage: {\n    \"@type\": \"WebPage\",\n    \"@id\": article.url,\n  },\n});\n\n/**\n * Generate product schema for webstore items\n */\nexport const generateProductSchema = (product) => ({\n  \"@type\": \"SoftwareApplication\",\n  name: product.title,\n  description: product.description,\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  publisher: {\n    \"@id\": `${BUSINESS_INFO.url}/#organization`,\n  },\n  offers: {\n    \"@type\": \"Offer\",\n    price: product.price || \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n    availability: \"https://schema.org/InStock\",\n  },\n  aggregateRating: product.rating\n    ? {\n        \"@type\": \"AggregateRating\",\n        ratingValue: product.rating.value,\n        ratingCount: product.rating.count,\n      }\n    : undefined,\n});\n\n/**\n * Generate breadcrumb schema\n */\nexport const generateBreadcrumbSchema = (breadcrumbs) => ({\n  \"@type\": \"BreadcrumbList\",\n  itemListElement: breadcrumbs.map((crumb, index) => ({\n    \"@type\": \"ListItem\",\n    position: index + 1,\n    item: {\n      \"@id\": crumb.url,\n      name: crumb.name,\n    },\n  })),\n});\n\n/**\n * Get page-specific SEO data based on page type and content\n */\nexport const getPageSEOData = (pageType, content = {}, language = \"en\") => {\n  const baseKeywords = [\n    \"software development\",\n    \"custom software\",\n    \"web development\",\n    \"AI solutions\",\n    \"estonia\",\n    \"tallinn\",\n  ];\n\n  const seoData = {\n    homepage: {\n      title: \"Professional Software Development Services\",\n      description:\n        \"DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.\",\n      keywords: [...baseKeywords, \"white label software\", \"business comanager\"],\n      schema: [generateLocalBusinessSchema(), generateWebsiteSchema()],\n    },\n    about: {\n      title: \"About DevSkills - Professional Development Team\",\n      description:\n        \"Learn about DevSkills, our mission, values, and the professional team behind our innovative software development services and custom solutions.\",\n      keywords: [...baseKeywords, \"about\", \"team\", \"company\"],\n      schema: [generateLocalBusinessSchema()],\n    },\n    services: {\n      title: \"Software Development Services - Custom Solutions\",\n      description:\n        \"Comprehensive software development services including custom software, web development, AI solutions, blockchain development, and mobile applications.\",\n      keywords: [\n        ...baseKeywords,\n        \"services\",\n        \"custom development\",\n        \"blockchain\",\n        \"mobile apps\",\n      ],\n      schema: [generateLocalBusinessSchema()],\n    },\n    webstore: {\n      title: \"White Label Software Solutions - DevSkills Webstore\",\n      description:\n        \"Explore our white-label software solutions including Business Comanager and other professional business management tools.\",\n      keywords: [\n        ...baseKeywords,\n        \"white label\",\n        \"business software\",\n        \"webstore\",\n      ],\n      schema: [generateLocalBusinessSchema()],\n    },\n    blog: {\n      title: \"Software Development Blog - DevSkills Insights\",\n      description:\n        \"Latest insights, tutorials, and news about software development, AI, web technologies, and business solutions from DevSkills experts.\",\n      keywords: [\n        ...baseKeywords,\n        \"blog\",\n        \"tutorials\",\n        \"insights\",\n        \"technology news\",\n      ],\n      schema: [generateLocalBusinessSchema()],\n    },\n    contact: {\n      title: \"Contact DevSkills - Get Your Custom Software Quote\",\n      description:\n        \"Contact DevSkills for professional software development services. Get a quote for your custom software, web development, or AI solution project.\",\n      keywords: [...baseKeywords, \"contact\", \"quote\", \"consultation\"],\n      schema: [generateLocalBusinessSchema()],\n    },\n  };\n\n  // Handle dynamic content\n  if (content.title && content.description) {\n    return {\n      title: content.title,\n      description: content.description,\n      keywords: content.keywords || baseKeywords,\n      schema: content.schema || [generateLocalBusinessSchema()],\n    };\n  }\n\n  return seoData[pageType] || seoData.homepage;\n};\n\n/**\n * Generate language-specific keywords\n */\nexport const getLanguageSpecificKeywords = (baseKeywords, language) => {\n  const languageKeywords = {\n    et: [\n      \"tarkvara arendus\",\n      \"kohandatud tarkvara\",\n      \"veebiarendus\",\n      \"AI lahendused\",\n      \"eesti\",\n    ],\n    fi: [\n      \"ohjelmistokehitys\",\n      \"mukautettu ohjelmisto\",\n      \"web-kehitys\",\n      \"AI-ratkaisut\",\n      \"suomi\",\n    ],\n    de: [\n      \"softwareentwicklung\",\n      \"maßgeschneiderte software\",\n      \"webentwicklung\",\n      \"AI-lösungen\",\n      \"deutschland\",\n    ],\n    sv: [\n      \"mjukvaruutveckling\",\n      \"anpassad mjukvara\",\n      \"webbutveckling\",\n      \"AI-lösningar\",\n      \"sverige\",\n    ],\n  };\n\n  return [...baseKeywords, ...(languageKeywords[language] || [])];\n};\n\nexport default {\n  BUSINESS_INFO,\n  generateLocalBusinessSchema,\n  generateWebsiteSchema,\n  generateArticleSchema,\n  generateProductSchema,\n  generateBreadcrumbSchema,\n  getPageSEOData,\n  getLanguageSpecificKeywords,\n};\n", "import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport Home from \"@/components/home\";\nimport Hero from \"@/components/home/<USER>\";\nimport { menuItems } from \"@/data/menu\";\nimport ParallaxContainer from \"@/components/common/ParallaxContainer\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\n// JSON-LD structured data for the homepage\nconst homeSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"LocalBusiness\",\n  \"@id\": \"https://devskills.ee/#organization\",\n  name: \"DevSkills OÜ\",\n  alternateName: \"DevSkills Development Studio\",\n  url: \"https://devskills.ee\",\n  logo: {\n    \"@type\": \"ImageObject\",\n    url: \"https://devskills.ee/logo.png\",\n    width: \"180\",\n    height: \"60\",\n  },\n  description:\n    \"DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.\",\n  address: {\n    \"@type\": \"PostalAddress\",\n    streetAddress: \"Tornimäe tn 7\",\n    addressLocality: \"Tallinn\",\n    postalCode: \"10145\",\n    addressCountry: \"EE\",\n  },\n  geo: {\n    \"@type\": \"GeoCoordinates\",\n    latitude: 59.437,\n    longitude: 24.7536,\n  },\n  contactPoint: {\n    \"@type\": \"ContactPoint\",\n    telephone: \"+372 5628 2038\",\n    contactType: \"customer service\",\n    availableLanguage: [\"English\", \"Estonian\", \"Finnish\", \"German\", \"Swedish\"],\n  },\n  openingHoursSpecification: {\n    \"@type\": \"OpeningHoursSpecification\",\n    dayOfWeek: [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\"],\n    opens: \"08:00\",\n    closes: \"17:00\",\n  },\n  serviceArea: {\n    \"@type\": \"Country\",\n    name: \"Estonia\",\n  },\n  hasOfferCatalog: {\n    \"@type\": \"OfferCatalog\",\n    name: \"Software Development Services\",\n    itemListElement: [\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"Custom Software Development\",\n        },\n      },\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"Web Development\",\n        },\n      },\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"AI Solutions\",\n        },\n      },\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"White Label Software\",\n        },\n      },\n    ],\n  },\n  sameAs: [\n    \"https://www.facebook.com/devskillsee\",\n    \"https://www.linkedin.com/company/devskills-ee\",\n    \"https://twitter.com/DevSkillsEE\",\n  ],\n};\n\n// Additional WebSite schema\nconst websiteSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"WebSite\",\n  \"@id\": \"https://devskills.ee/#website\",\n  name: \"DevSkills\",\n  alternateName: \"DevSkills Development Studio\",\n  url: \"https://devskills.ee\",\n  description:\n    \"Professional software development services and custom solutions\",\n  publisher: {\n    \"@id\": \"https://devskills.ee/#organization\",\n  },\n  potentialAction: {\n    \"@type\": \"SearchAction\",\n    target: \"https://devskills.ee/search?q={search_term_string}\",\n    \"query-input\": \"required name=search_term_string\",\n  },\n  inLanguage: [\"en\", \"et\", \"fi\", \"de\", \"sv\"],\n};\nexport default function Home5MainDemoMultiPageDark() {\n  const seoData = getPageSEOData(\"homepage\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={seoData.title}\n        description={seoData.description}\n        slug=\"\"\n        type=\"website\"\n        image=\"https://devskills.ee/home.jpg\"\n        imageAlt=\"DevSkills Development Studio - Professional Software Development\"\n        schema={seoData.schema}\n        keywords={seoData.keywords}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <ParallaxContainer\n                className=\"home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <Hero />\n              </ParallaxContainer>\n\n              <Home dark />\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React, { useState, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\nimport Header from \"@/components/headers/Header\";\nimport AnimatedText from \"@/components/common/AnimatedText\";\nimport Footer from \"@/components/footers/Footer\";\nimport { trackEvent } from \"@/utils/analytics\";\nimport { menuItems } from \"@/data/menu\";\nimport { productsAPI, categoriesAPI } from \"@/utils/api\";\n\nexport default function ElegantWebstorePageDark() {\n  const { t: translate, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  const [products, setProducts] = useState([]);\n  const [filteredProducts, setFilteredProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadProducts();\n    loadCategories();\n  }, [currentLanguage]);\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await productsAPI.getProducts({\n        language: currentLanguage,\n        status: \"published\",\n      });\n\n      if (response.ok && data.success) {\n        console.log(\"Loaded products:\", data.products); // Debug log\n        setProducts(data.products);\n        setFilteredProducts(data.products);\n      } else {\n        setError(\"Failed to load products\");\n      }\n    } catch (err) {\n      console.error(\"Error loading products:\", err);\n      setError(\"Error loading products\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCategories = async () => {\n    try {\n      const { response, data } = await categoriesAPI.getCategories();\n\n      if (response.ok && data.success) {\n        setCategories(data.data || data.categories);\n      }\n    } catch (err) {\n      console.error(\"Error loading categories:\", err);\n    }\n  };\n\n  const handleCategoryFilter = (categorySlug) => {\n    setSelectedCategory(categorySlug);\n\n    if (categorySlug === \"all\") {\n      setFilteredProducts(products);\n    } else {\n      const filtered = products.filter((product) =>\n        product.categories.some((cat) => cat.category.slug === categorySlug)\n      );\n      setFilteredProducts(filtered);\n    }\n  };\n\n  const handleProductClick = (product) => {\n    trackEvent(\"product_view\", {\n      product_id: product.id,\n      product_title: product.title,\n      language: currentLanguage,\n      source: \"webstore_listing\",\n    });\n  };\n\n  const formatPrice = (price) => {\n    if (!price) return null;\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"EUR\",\n    }).format(price);\n  };\n\n  const seoData = getPageSEOData(\"webstore\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={translate(\"webstore.meta.title\") || seoData.title}\n        description={\n          translate(\"webstore.meta.description\") || seoData.description\n        }\n        slug=\"webstore\"\n        type=\"website\"\n        image=\"https://devskills.ee/webstore.jpg\"\n        schema={seoData.schema}\n        keywords={\n          translate(\"webstore.meta.keywords\")\n            ? translate(\"webstore.meta.keywords\")\n                .split(\",\")\n                .map((k) => k.trim())\n            : seoData.keywords\n        }\n      />\n      <style>\n        {`\n          .btn-mod:focus,\n          .btn-mod:active {\n            outline: none !important;\n            box-shadow: none !important;\n          }\n          .btn-mod.btn-w:focus,\n          .btn-mod.btn-w:active {\n            background: #fff !important;\n            color: var(--color-dark-1) !important;\n            border-color: #fff !important;\n          }\n        `}\n      </style>\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/3.jpg)\",\n                }}\n              >\n                <div className=\"container position-relative pt-30 pt-sm-50\">\n                  <div className=\"text-center\">\n                    <div className=\"row\">\n                      <div className=\"col-md-8 offset-md-2\">\n                        <h1 className=\"hs-title-1 mb-20\">\n                          <span\n                            className=\"wow charsAnimIn\"\n                            data-splitting=\"chars\"\n                          >\n                            <AnimatedText text={translate(\"webstore.title\")} />\n                          </span>\n                        </h1>\n                        <div className=\"row\">\n                          <div className=\"col-lg-8 offset-lg-2\">\n                            <p\n                              className=\"section-descr mb-0 wow fadeIn\"\n                              data-wow-delay=\"0.2s\"\n                            >\n                              {translate(\"webstore.description\")}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  {/* Category Filter */}\n                  <div className=\"row mb-60 mb-xs-40\">\n                    <div className=\"col-md-12\">\n                      <div className=\"works-filter works-filter-elegant text-center\">\n                        <a\n                          onClick={() => handleCategoryFilter(\"all\")}\n                          className={`filter ${\n                            selectedCategory === \"all\" ? \"active\" : \"\"\n                          }`}\n                          style={{ cursor: \"pointer\" }}\n                        >\n                          {translate(\"webstore.filter.all\")}\n                        </a>\n                        {categories.map((category) => (\n                          <a\n                            key={category.id}\n                            onClick={() => handleCategoryFilter(category.slug)}\n                            className={`filter ${\n                              selectedCategory === category.slug ? \"active\" : \"\"\n                            }`}\n                            style={{ cursor: \"pointer\" }}\n                          >\n                            {category.name}\n                          </a>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Products Grid */}\n                  {loading ? (\n                    <div className=\"text-center py-5\">\n                      <div\n                        className=\"spinner-border text-primary\"\n                        role=\"status\"\n                      >\n                        <span className=\"visually-hidden\">Loading...</span>\n                      </div>\n                    </div>\n                  ) : error ? (\n                    <div\n                      className=\"alert alert-danger text-center\"\n                      role=\"alert\"\n                    >\n                      {error}\n                    </div>\n                  ) : (\n                    <div className=\"row mt-n50\">\n                      {filteredProducts.length > 0 ? (\n                        filteredProducts.map((product, index) => (\n                          <div\n                            key={product.id}\n                            className=\"post-prev col-md-6 col-lg-4 mt-50\"\n                          >\n                            <div className=\"post-prev-container d-flex flex-column h-100\">\n                              <div className=\"post-prev-img\">\n                                <Link\n                                  to={`/${currentLanguage}/webstore-single/${(\n                                    product.slug || product.id\n                                  ).replace(/^\\/+/, \"\")}`}\n                                  onClick={() => handleProductClick(product)}\n                                >\n                                  <img\n                                    src={\n                                      product.featuredImage\n                                        ? `${\n                                            import.meta.env.VITE_API_BASE_URL ||\n                                            \"http://localhost:4004\"\n                                          }/uploads/product-images/${\n                                            product.featuredImage\n                                          }`\n                                        : \"/assets/images/demo-elegant/blog/1.jpg\"\n                                    }\n                                    width={650}\n                                    height={412}\n                                    alt={\n                                      product.featuredImageAlt || product.title\n                                    }\n                                    className=\"wow scaleOutIn\"\n                                    data-wow-duration=\"1.2s\"\n                                  />\n                                </Link>\n                              </div>\n\n                              {/* Content area that can grow */}\n                              <div className=\"flex-grow-1 d-flex flex-column\">\n                                <h4 className=\"post-prev-title\">\n                                  <Link\n                                    to={`/${currentLanguage}/webstore-single/${(\n                                      product.slug || product.id\n                                    ).replace(/^\\/+/, \"\")}`}\n                                    onClick={() => handleProductClick(product)}\n                                  >\n                                    {product.title}\n                                  </Link>\n                                </h4>\n\n                                <div className=\"post-prev-text flex-grow-1\">\n                                  {product.excerpt}\n                                </div>\n\n                                {/* Pricing and button section - always at bottom */}\n                                <div className=\"mt-auto\">\n                                  {/* Pricing Information */}\n                                  <div className=\"product-pricing mb-30 text-center\">\n                                    {product.whitelabelPrice && (\n                                      <div className=\"price-row mb-10\">\n                                        <span className=\"text-gray\">\n                                          {translate(\n                                            \"webstore.whitelabel_price\"\n                                          )}\n                                          :{\" \"}\n                                        </span>\n                                        <span className=\"price-value text-white h5 d-inline\">\n                                          €{product.whitelabelPrice}\n                                        </span>\n                                      </div>\n                                    )}\n                                    {product.subscriptionPrice && (\n                                      <div className=\"price-row mb-10\">\n                                        <span className=\"text-gray\">\n                                          {translate(\n                                            \"webstore.subscription_price\"\n                                          )}\n                                          :{\" \"}\n                                        </span>\n                                        <span className=\"price-value text-white h5 d-inline\">\n                                          €{product.subscriptionPrice}/mo\n                                        </span>\n                                      </div>\n                                    )}\n                                  </div>\n\n                                  {/* Demo Button */}\n                                  <div className=\"text-center\">\n                                    {product.demoUrl && (\n                                      <a\n                                        href={product.demoUrl}\n                                        target=\"_blank\"\n                                        rel=\"noopener noreferrer\"\n                                        className=\"btn btn-mod btn-medium btn-circle btn-hover-anim btn-w\"\n                                        onClick={(e) => {\n                                          // Remove focus after click to prevent focus state styling\n                                          setTimeout(() => {\n                                            e.target.blur();\n                                          }, 100);\n\n                                          trackEvent(\"demo_click\", {\n                                            product_id: product.id,\n                                            product_title: product.title,\n                                            language: currentLanguage,\n                                            source: \"webstore_listing\",\n                                          });\n                                        }}\n                                      >\n                                        <span>\n                                          {translate(\"webstore.view_demo\")}\n                                        </span>\n                                      </a>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ))\n                      ) : (\n                        <div className=\"col-12 text-center\">\n                          <p className=\"text-muted\">\n                            {translate(\"webstore.no_products\")}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"footer-1 bg-dark-2 light-content\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nconst dark = true;\nimport { menuItems } from \"@/data/menu\";\nimport Portfolio from \"@/components/home/<USER>\";\nimport MarqueeDark from \"@/components/home/<USER>\";\n\nimport MetaComponent from \"@/components/common/MetaComponent\";\nconst metadata = {\n  title:\n    \"Elegant Portfolio Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n  description: \"Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n};\nexport default function ElegantPortfolioPageDark() {\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    PORTFOLIO\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        Explore captivating web design solutions.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section\n                className={`page-section pb-0  scrollSpysection  ${\n                  dark ? \"bg-dark-1 light-content\" : \"\"\n                } `}\n                id=\"portfolio\"\n              >\n                <Portfolio />\n              </section>\n              <div className=\"page-section overflow-hidden\">\n                <MarqueeDark />\n              </div>\n              <section className=\"page-section bg-dark-1 light-content pt-0\">\n                <div className=\"container position-relative\">\n                  {/* Decorative Waves */}\n\n                  {/* End Decorative Waves */}\n                  <div className=\"row text-center wow fadeInUp\">\n                    <div className=\"col-md-10 offset-md-1 col-lg-6 offset-lg-3\">\n                      <p className=\"section-descr mb-50 mb-sm-30\">\n                        The power of design help us to solve complex problems\n                        and cultivate business solutions.\n                      </p>\n                      <div className=\"local-scroll\">\n                        <Link\n                          to={`/elegant-contact`}\n                          className=\"btn btn-mod btn-large btn-w btn-circle btn-hover-anim\"\n                        >\n                          <span>Contact us</span>\n                        </Link>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/blogs/page.jsx\n\nimport React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport { Link, useSearchParams } from \"react-router-dom\";\nimport { useState, useEffect } from \"react\";\nimport { menuItems } from \"@/data/menu\";\nimport Pagination from \"@/components/common/Pagination\";\nimport { blogAPI, categoriesAPI, tagsAPI, archiveAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\nexport default function ElegantBlogPageDark() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [blogPosts, setBlogPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [archiveData, setArchiveData] = useState([]);\n\n  // Get current filters from URL\n  const currentCategory = searchParams.get(\"category\");\n  const currentTag = searchParams.get(\"tag\");\n  const currentSearch = searchParams.get(\"search\");\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch blog posts with filters\n        const params = {\n          language: currentLanguage,\n          page: currentPage,\n          limit: 9,\n        };\n\n        if (currentCategory) params.category = currentCategory;\n        if (currentTag) params.tag = currentTag;\n        if (currentSearch) params.search = currentSearch;\n\n        const blogResult = await blogAPI.getBlogPosts(params);\n\n        if (blogResult.response.ok && blogResult.data) {\n          // Extract the posts array from the nested response structure\n          const posts =\n            blogResult.data.data?.data || blogResult.data.data || [];\n          const pagination =\n            blogResult.data.data?.pagination || blogResult.data.pagination;\n          console.log(\"Blog listing API response:\", blogResult.data);\n          console.log(\"Posts array:\", posts);\n          console.log(\"Pagination:\", pagination);\n          setBlogPosts(Array.isArray(posts) ? posts : []);\n          setTotalPages(pagination?.totalPages || 1);\n        } else {\n          console.error(\n            \"Failed to fetch blog posts:\",\n            blogResult.response.status\n          );\n          setBlogPosts([]);\n        }\n\n        // Fetch categories\n        try {\n          const categoriesResult = await categoriesAPI.getCategories();\n          if (categoriesResult.response.ok && categoriesResult.data) {\n            setCategories(categoriesResult.data.data || []);\n          }\n        } catch (error) {\n          console.error(\"Error fetching categories:\", error);\n        }\n\n        // Fetch tags\n        try {\n          const tagsResult = await tagsAPI.getTags();\n          if (tagsResult.response.ok && tagsResult.data) {\n            setTags(tagsResult.data.data || []);\n          }\n        } catch (error) {\n          console.error(\"Error fetching tags:\", error);\n        }\n\n        // Fetch archive data\n        try {\n          const archiveResult = await archiveAPI.getArchive();\n          if (archiveResult.response.ok && archiveResult.data) {\n            setArchiveData(archiveResult.data.archive || []);\n          }\n        } catch (error) {\n          console.error(\"Error fetching archive:\", error);\n        }\n      } catch (error) {\n        console.error(\"Error fetching data:\", error);\n        setBlogPosts([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [\n    currentLanguage,\n    currentPage,\n    currentCategory,\n    currentTag,\n    currentSearch,\n  ]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    const translation = post.translations?.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations?.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Filter handlers\n  const handleCategoryFilter = (categorySlug) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (categorySlug) {\n      newParams.set(\"category\", categorySlug);\n    } else {\n      newParams.delete(\"category\");\n    }\n    newParams.delete(\"page\"); // Reset to first page when filtering\n    setSearchParams(newParams);\n    setCurrentPage(1);\n  };\n\n  const handleTagFilter = (tagSlug) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (tagSlug) {\n      newParams.set(\"tag\", tagSlug);\n    } else {\n      newParams.delete(\"tag\");\n    }\n    newParams.delete(\"page\"); // Reset to first page when filtering\n    setSearchParams(newParams);\n    setCurrentPage(1);\n  };\n\n  const clearFilters = () => {\n    setSearchParams({});\n    setCurrentPage(1);\n  };\n\n  const seoData = getPageSEOData(\"blog\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={t(\"blog.page.title\") || seoData.title}\n        description={t(\"blog.page.description\") || seoData.description}\n        slug=\"blog\"\n        type=\"website\"\n        image=\"https://devskills.ee/blog.jpg\"\n        schema={seoData.schema}\n        keywords={\n          t(\"blog.page.keywords\", { returnObjects: true }) || seoData.keywords\n        }\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className={`page-section bg-dark-alpha-50 light-content ${\n                  currentCategory || currentTag || currentSearch\n                    ? \"blog-hero-minimal\"\n                    : \"\"\n                }`}\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"blog.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"blog.subtitle\")}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Filter Status */}\n                  {(currentCategory || currentTag || currentSearch) && (\n                    <div className=\"filter-status-minimal\">\n                      <div className=\"d-flex flex-wrap justify-content-center align-items-center gap-3\">\n                        <span className=\"text-white-opacity\">Filtered by:</span>\n                        {currentCategory && (\n                          <span className=\"badge bg-primary\">\n                            Category:{\" \"}\n                            {\n                              categories.find((c) => c.slug === currentCategory)\n                                ?.name\n                            }\n                          </span>\n                        )}\n                        {currentTag && (\n                          <span className=\"badge bg-secondary\">\n                            Tag: {tags.find((t) => t.slug === currentTag)?.name}\n                          </span>\n                        )}\n                        {currentSearch && (\n                          <span className=\"badge bg-info\">\n                            Search: \"{currentSearch}\"\n                          </span>\n                        )}\n                        <button\n                          onClick={clearFilters}\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            Clear Filters\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            Clear Filters\n                          </span>\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </section>\n              <>\n                <section\n                  className=\"page-section bg-dark-1 light-content\"\n                  id=\"blog\"\n                >\n                  <div className=\"container\">\n                    {/* Blog Posts Grid */}\n                    <div\n                      className=\"row mt-n50 mb-50 wow fadeInUp\"\n                      data-wow-offset={0}\n                    >\n                      {/* Loading State */}\n                      {loading && (\n                        <div className=\"col-12 text-center\">\n                          <div className=\"text-gray\">{t(\"blog.loading\")}</div>\n                        </div>\n                      )}\n\n                      {/* Empty State */}\n                      {!loading && blogPosts.length === 0 && (\n                        <div className=\"col-12 text-center\">\n                          <div className=\"text-gray\">{t(\"blog.empty\")}</div>\n                        </div>\n                      )}\n\n                      {/* Post Items */}\n                      {!loading &&\n                        Array.isArray(blogPosts) &&\n                        blogPosts.map((post) => (\n                          <div\n                            key={post.id}\n                            className=\"post-prev col-md-6 col-lg-4 mt-50\"\n                          >\n                            <div className=\"post-prev-container\">\n                              <div className=\"post-prev-img\">\n                                <Link to={`/blog-single/${post.slug}`}>\n                                  <img\n                                    src={\n                                      post.featuredImage ||\n                                      \"/assets/images/demo-elegant/blog/1.jpg\"\n                                    }\n                                    width={607}\n                                    height={358}\n                                    alt={getTranslation(post, \"title\")}\n                                  />\n                                </Link>\n                              </div>\n                              <h3 className=\"post-prev-title\">\n                                <Link to={`/blog-single/${post.slug}`}>\n                                  {getTranslation(post, \"title\")}\n                                </Link>\n                              </h3>\n                              <div className=\"post-prev-text\">\n                                {getTranslation(post, \"excerpt\")}\n                              </div>\n                              <div className=\"post-prev-info clearfix\">\n                                <div className=\"float-start\">\n                                  <a href=\"#\" className=\"icon-author\">\n                                    <i className=\"mi-user size-14 align-middle\" />\n                                  </a>\n                                  <a href=\"#\">\n                                    {post.author?.name || \"DevSkills Team\"}\n                                  </a>\n                                </div>\n                                <div className=\"float-end\">\n                                  <i className=\"mi-calendar size-14 align-middle\" />\n                                  <a href=\"#\">\n                                    {new Date(\n                                      post.publishedAt || post.createdAt\n                                    ).toLocaleDateString()}\n                                  </a>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      {/* End Post Item */}\n\n                      {/* End Post Item */}\n                    </div>\n                    {/* End Blog Posts Grid */}\n                    {/* Pagination */}\n                    <Pagination\n                      currentPage={currentPage}\n                      totalPages={totalPages}\n                      onPageChange={setCurrentPage}\n                    />\n                    {/* End Pagination */}\n                  </div>\n                </section>\n                {/* End Blog Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Section */}\n                <section className=\"page-section bg-dark-1 light-content\">\n                  <div className=\"container relative\">\n                    <div className=\"row mt-n60\">\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">\n                            {t(\"blog.categories\")}\n                          </h3>\n                          <div className=\"widget-body\">\n                            <ul className=\"clearlist widget-menu\">\n                              {categories.map((category) => (\n                                <li key={category.id}>\n                                  <a\n                                    href=\"#\"\n                                    title=\"\"\n                                    onClick={(e) => {\n                                      e.preventDefault();\n                                      handleCategoryFilter(category.slug);\n                                    }}\n                                    className={\n                                      currentCategory === category.slug\n                                        ? \"active\"\n                                        : \"\"\n                                    }\n                                  >\n                                    {category.name}\n                                  </a>\n                                  <small>\n                                    {\" \"}\n                                    - {category._count?.blogPosts || 0}{\" \"}\n                                  </small>\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">{t(\"blog.tags\")}</h3>\n                          <div className=\"widget-body\">\n                            <div className=\"tags\">\n                              {tags.map((tag) => (\n                                <a\n                                  href=\"#\"\n                                  key={tag.id}\n                                  onClick={(e) => {\n                                    e.preventDefault();\n                                    handleTagFilter(tag.slug);\n                                  }}\n                                  className={\n                                    currentTag === tag.slug ? \"active\" : \"\"\n                                  }\n                                >\n                                  {tag.name}\n                                </a>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">{t(\"blog.archive\")}</h3>\n                          <div className=\"widget-body\">\n                            <ul className=\"clearlist widget-menu\">\n                              {archiveData.map((archive, index) => (\n                                <li key={index}>\n                                  <a href=\"#\" title=\"\">\n                                    {archive.monthName} {archive.year}\n                                  </a>\n                                  <small> - {archive.count} </small>\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">\n                            {t(\"blog.about_widget.title\")}\n                          </h3>\n                          <div className=\"widget-body\">\n                            <div className=\"widget-text clearfix\">\n                              <img\n                                src=\"/assets/img/power-128.png\"\n                                alt=\"DevSkills Logo\"\n                                height={40}\n                                width={40}\n                                className=\"left img-left\"\n                                style={{ borderRadius: \"8px\" }}\n                              />\n                              {t(\"blog.about_widget.text\")}\n                            </div>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                    </div>\n                  </div>\n                </section>\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\nimport React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport { Link } from \"react-router-dom\";\nimport RelatedProjects from \"@/components/portfolio/RelatedProjects\";\nimport { allPortfolios } from \"@/data/portfolio\";\nimport MetaComponent from \"@/components/common/MetaComponent\";\nconst metadata = {\n  title:\n    \"Elegant Portfolio Single Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n  description: \"Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n};\nexport default function ElegantPortfolioSinglePageDark() {\n  let params = useParams();\n  const portfolioItem =\n    allPortfolios.filter((elm) => elm.id == params.id)[0] || allPortfolios[0];\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {portfolioItem.title}\n                  </h1>\n\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        Branding, UI/UX Design, No-code Development\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <>\n                {/* Section */}\n                <section className=\"page-section bg-dark-1 light-content\">\n                  <div className=\"container relative\">\n                    <div className=\"row mb-80 mb-sm-40\">\n                      {/* Project Details */}\n                      <div className=\"col-md-6 mb-sm-40\">\n                        <h2 className=\"section-title-small mb-20\">\n                          Project Details\n                        </h2>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Date:</b>\n                          </div>\n                          <div className=\"col-sm-8\">May 1th, 2023</div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Client:</b>\n                          </div>\n                          <div className=\"col-sm-8\">Envato Users</div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Services:</b>\n                          </div>\n                          <div className=\"col-sm-8\">\n                            Branding, UI/UX Design, Front-end Development,\n                            Back-end Development\n                          </div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                      </div>\n                      {/* End Project Details */}\n                      {/* Project Description */}\n                      <div className=\"col-md-6\">\n                        <h2 className=\"section-title-small mb-20\">\n                          Description\n                        </h2>\n                        <hr className=\"mb-20\" />\n                        <p className=\"text-gray mb-0\">\n                          Lorem ipsum dolor sit amet conseur adipisci inerene\n                          maximus ligula sempe metuse pelente mattis. Maecenas\n                          volutpat, diam eni sagittis quam porta quam. Sed id\n                          dolor consectetur fermentum volutpat accumsan purus\n                          iaculis libero. Donec vel ultricies purus iaculis\n                          libero. Etiam sit amet fringilla lacus susantebe sit\n                          ullamcorper pulvinar neque porttitor. Integere lectus.\n                          Praesent sede nisi eleifend fermum orci amet, iaculis\n                          libero. Donec vel ultricies purus quam.\n                        </p>\n                      </div>\n                      {/* End Project Description */}\n                    </div>\n                    <div className=\"row mb-n30\">\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/1-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/6-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/8-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/3-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                    </div>\n                  </div>\n                </section>\n                {/* End Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n              </>\n              <section className=\"page-section bg-dark-1 light-content\">\n                <RelatedProjects />\n              </section>\n              <>\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Work Navigation */}\n                <div className=\"work-navigation bg-dark-1 light-content clearfix z-index-1 position-relative\">\n                  <Link to={`/main-portfolio-single-1/1`} className=\"work-prev\">\n                    <span>\n                      <i className=\"mi-arrow-left size-24 align-middle\" />{\" \"}\n                      Previous\n                    </span>\n                  </Link>\n                  <a href=\"#\" className=\"work-all\">\n                    <span>\n                      <i className=\"mi-close size-24 align-middle\" /> All works\n                    </span>\n                  </a>\n                  <Link to={`/main-portfolio-single-3/1`} className=\"work-next\">\n                    <span>\n                      Next <i className=\"mi-arrow-right size-24 align-middle\" />\n                    </span>\n                  </Link>\n                </div>\n                {/* End Work Navigation */}\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React, { useState, useEffect } from \"react\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport {\n  generateProductSchema,\n  generateBreadcrumbSchema,\n} from \"@/utils/seoHelpers\";\n\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { trackEvent } from \"@/utils/analytics\";\nimport { menuItems } from \"@/data/menu\";\nimport ProductGallery from \"@/components/ProductGallery\";\n\nexport default function ElegantWebstoreSinglePageDark() {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { t: translate, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadProduct();\n  }, [id, currentLanguage]);\n\n  const loadProduct = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(\n        `/api/products/${id}?language=${currentLanguage}`\n      );\n      const data = await response.json();\n\n      if (data.success) {\n        setProduct(data.product);\n\n        // Track product view\n        trackEvent(\"product_detail_view\", {\n          product_id: data.product.id,\n          product_title: data.product.title,\n          language: currentLanguage,\n          source: \"direct_link\",\n        });\n      } else {\n        setError(\"Product not found\");\n      }\n    } catch (err) {\n      console.error(\"Error loading product:\", err);\n      setError(\"Error loading product\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoClick = () => {\n    trackEvent(\"demo_click\", {\n      product_id: product.id,\n      product_title: product.title,\n      language: currentLanguage,\n      source: \"product_detail\",\n    });\n  };\n\n  const handlePurchaseClick = (type) => {\n    trackEvent(\"purchase_intent\", {\n      product_id: product.id,\n      product_title: product.title,\n      purchase_type: type, // 'whitelabel' or 'subscription'\n      language: currentLanguage,\n      source: \"product_detail\",\n    });\n  };\n\n  const formatPrice = (price) => {\n    if (!price) return null;\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"EUR\",\n    }).format(price);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container text-center py-5\">\n                  <div className=\"spinner-border text-primary\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading...</span>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container text-center py-5\">\n                  <h1>Product Not Found</h1>\n                  <p>The product you're looking for doesn't exist.</p>\n                  <button\n                    onClick={() => navigate(`/${currentLanguage}/webstore`)}\n                    className=\"btn btn-mod btn-round\"\n                  >\n                    Back to Webstore\n                  </button>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Generate product schema\n  const productSchema = generateProductSchema({\n    title: product.title,\n    description: product.excerpt,\n    price: product.whitelabelPrice || product.subscriptionPrice,\n    featuredImage: product.featuredImage,\n    url: `https://devskills.ee/${currentLanguage}/webstore-single/${product.slug}`,\n  });\n\n  // Generate breadcrumb schema\n  const breadcrumbSchema = generateBreadcrumbSchema([\n    { name: \"Home\", url: `https://devskills.ee/${currentLanguage}` },\n    {\n      name: \"Webstore\",\n      url: `https://devskills.ee/${currentLanguage}/webstore`,\n    },\n    {\n      name: product.title,\n      url: `https://devskills.ee/${currentLanguage}/webstore-single/${product.slug}`,\n    },\n  ]);\n\n  const productImage = product.featuredImage\n    ? `${\n        import.meta.env.VITE_API_BASE_URL || \"http://localhost:4004\"\n      }/uploads/product-images/${product.featuredImage}`\n    : \"https://devskills.ee/webstore.jpg\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title={product.metaTitle || product.title}\n        description={product.metaDescription || product.excerpt}\n        slug={`webstore-single/${product.slug}`}\n        type=\"product\"\n        image={productImage}\n        imageAlt={product.featuredImageAlt || product.title}\n        schema={[productSchema, breadcrumbSchema]}\n        keywords={\n          product.metaKeywords\n            ? product.metaKeywords.split(\",\").map((k) => k.trim())\n            : [\"software\", \"business\", \"devskills\"]\n        }\n        publishedAt={product.publishedAt}\n        modifiedAt={product.updatedAt}\n      />\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              {/* Hero Section with Background */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/3.jpg)\",\n                  paddingBottom: \"40px\",\n                }}\n              >\n                <div className=\"container position-relative pt-30 pt-sm-50\">\n                  <div className=\"text-center\">\n                    <div className=\"row\">\n                      <div className=\"col-md-8 offset-md-2\">\n                        <h1 className=\"hs-title-1 mb-20\">\n                          <span\n                            className=\"wow charsAnimIn\"\n                            data-splitting=\"chars\"\n                          >\n                            {product.title}\n                          </span>\n                        </h1>\n                        <div className=\"row\">\n                          <div className=\"col-lg-8 offset-lg-2\">\n                            <p\n                              className=\"section-descr mb-20 wow fadeIn\"\n                              data-wow-delay=\"0.2s\"\n                            >\n                              {product.excerpt}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n\n              {/* Product Content Section */}\n              <section\n                className=\"page-section bg-dark-1 light-content\"\n                style={{ paddingTop: \"40px\" }}\n              >\n                <div className=\"container position-relative\">\n                  {/* Product Content */}\n                  <div className=\"row\">\n                    <div className=\"col-lg-8\">\n                      {/* Product Gallery */}\n                      <ProductGallery\n                        images={product.images}\n                        productTitle={product.title}\n                      />\n\n                      {/* Product Content */}\n                      <div className=\"blog-item-body\">\n                        <div\n                          className=\"blog-item-content\"\n                          dangerouslySetInnerHTML={{ __html: product.content }}\n                        />\n                      </div>\n\n                      {/* Categories and Tags */}\n                      <div className=\"blog-item-footer pt-4 mt-4 border-top\">\n                        {product.categories &&\n                          product.categories.length > 0 && (\n                            <div className=\"mb-3\">\n                              <strong>Categories: </strong>\n                              {product.categories.map((cat, index) => (\n                                <span key={cat.category.id}>\n                                  <span className=\"badge bg-primary me-2\">\n                                    {cat.category.name}\n                                  </span>\n                                </span>\n                              ))}\n                            </div>\n                          )}\n\n                        {product.tags && product.tags.length > 0 && (\n                          <div>\n                            <strong>Tags: </strong>\n                            {product.tags.map((tag, index) => (\n                              <span key={tag.tag.id}>\n                                <span className=\"badge bg-secondary me-2\">\n                                  {tag.tag.name}\n                                </span>\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Sidebar */}\n                    <div className=\"col-lg-4\">\n                      <div className=\"blog-sidebar ps-lg-4\">\n                        {/* Pricing Options */}\n                        {product.whitelabelPrice && (\n                          <div className=\"widget mb-4\">\n                            <div\n                              className=\"pricing-option mb-4 p-4 rounded-3\"\n                              style={{\n                                background: \"rgba(255, 255, 255, 0.02)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.15)\",\n                                transition: \"all 0.3s ease\",\n                              }}\n                            >\n                              <h6 className=\"text-white mb-3 text-center\">\n                                Whitelabel License\n                              </h6>\n                              <div className=\"text-center mb-3\">\n                                <div\n                                  className=\"price text-primary\"\n                                  style={{\n                                    fontSize: \"2.5rem\",\n                                    fontWeight: \"700\",\n                                  }}\n                                >\n                                  {formatPrice(product.whitelabelPrice)}\n                                </div>\n                                <small className=\"text-gray\">\n                                  One-time payment\n                                </small>\n                              </div>\n                              <p className=\"text-gray small mb-4 text-center\">\n                                Get the complete source code with commercial\n                                license\n                              </p>\n                              <div className=\"text-center\">\n                                <button\n                                  className=\"btn btn-mod btn-medium btn-circle btn-hover-anim btn-color\"\n                                  onClick={() =>\n                                    handlePurchaseClick(\"whitelabel\")\n                                  }\n                                  style={{ minWidth: \"180px\" }}\n                                >\n                                  <span>Buy Whitelabel</span>\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        {product.subscriptionPrice && (\n                          <div className=\"widget mb-4\">\n                            <div\n                              className=\"pricing-option mb-4 p-4 rounded-3\"\n                              style={{\n                                background: \"rgba(255, 255, 255, 0.02)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.15)\",\n                                transition: \"all 0.3s ease\",\n                              }}\n                            >\n                              <h6 className=\"text-white mb-3 text-center\">\n                                Subscription\n                              </h6>\n                              <div className=\"text-center mb-3\">\n                                <div\n                                  className=\"price text-success\"\n                                  style={{\n                                    fontSize: \"2.5rem\",\n                                    fontWeight: \"700\",\n                                  }}\n                                >\n                                  {formatPrice(product.subscriptionPrice)}\n                                  <small\n                                    style={{\n                                      fontSize: \"1rem\",\n                                      fontWeight: \"400\",\n                                    }}\n                                  >\n                                    /mo\n                                  </small>\n                                </div>\n                                <small className=\"text-gray\">\n                                  Monthly billing\n                                </small>\n                              </div>\n                              <p className=\"text-gray small mb-4 text-center\">\n                                Use the software as a service without source\n                                code\n                              </p>\n                              <div className=\"text-center\">\n                                <button\n                                  className=\"btn btn-mod btn-medium btn-circle btn-hover-anim\"\n                                  onClick={() =>\n                                    handlePurchaseClick(\"subscription\")\n                                  }\n                                  style={{\n                                    minWidth: \"180px\",\n                                    background: \"#22c55e\",\n                                    borderColor: \"#22c55e\",\n                                    color: \"#fff\",\n                                  }}\n                                >\n                                  <span>Start Subscription</span>\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        {product.demoUrl && (\n                          <div className=\"widget mb-4\">\n                            <div className=\"text-center mt-4\">\n                              <a\n                                href={product.demoUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"opacity-1 no-hover\"\n                                onClick={handleDemoClick}\n                                style={{ cursor: \"pointer\" }}\n                              >\n                                <span\n                                  className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                                  data-btn-animate=\"y\"\n                                >\n                                  <span className=\"btn-animate-y\">\n                                    <span className=\"btn-animate-y-1\">\n                                      View Live Demo\n                                    </span>\n                                    <span\n                                      className=\"btn-animate-y-2\"\n                                      aria-hidden=\"true\"\n                                    >\n                                      View Live Demo\n                                    </span>\n                                  </span>\n                                </span>\n                              </a>\n                            </div>\n                          </div>\n                        )}\n\n                        {/* Product Info */}\n                        <div className=\"widget\">\n                          <h5 className=\"widget-title\">Product Information</h5>\n                          <ul className=\"list-unstyled\">\n                            <li className=\"mb-2\">\n                              <strong>Published:</strong>{\" \"}\n                              {new Date(\n                                product.publishedAt\n                              ).toLocaleDateString()}\n                            </li>\n                            <li className=\"mb-2\">\n                              <strong>Last Updated:</strong>{\" \"}\n                              {new Date(product.updatedAt).toLocaleDateString()}\n                            </li>\n                            <li className=\"mb-2\">\n                              <strong>Views:</strong> {product.viewCount}\n                            </li>\n                          </ul>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"footer-1 bg-dark-2 light-content\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/blog-single/page.jsx\n\nimport Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { menuItems } from \"@/data/menu\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { Link } from \"react-router-dom\";\nimport Comments from \"@/components/blog/Comments\";\nimport Form from \"@/components/blog/commentForm/Form\";\nimport Widget1 from \"@/components/blog/widgets/Widget1\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { generateArticleSchema } from \"@/utils/seoHelpers\";\n// highlight.js will be lazy loaded when needed\n\nexport default function ElegantBlogSinglePageDark() {\n  let params = useParams();\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const navigate = useNavigate();\n  const [blog, setBlog] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    const fetchBlogPost = async () => {\n      try {\n        setLoading(true);\n        const result = await blogAPI.getPost(params.id);\n\n        if (result.response.ok && result.data) {\n          console.log(\"Blog single API response:\", result.data);\n          setBlog(result.data.data || result.data);\n        } else {\n          console.error(\"Failed to fetch blog post:\", result.response.status);\n          setError(\"Blog post not found\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching blog post:\", error);\n        setError(\"Failed to load blog post\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (params.id) {\n      fetchBlogPost();\n    }\n  }, [params.id]);\n\n  // Highlight code blocks when blog content is loaded using Shiki\n  useEffect(() => {\n    if (blog && !loading) {\n      // Small delay to ensure DOM is updated\n      setTimeout(async () => {\n        try {\n          // Dynamically import the syntax highlighting service\n          const { highlightCodeBlocks } = await import(\n            \"@/utils/syntaxHighlighting\"\n          );\n          await highlightCodeBlocks(\".blog-content pre code\", \"github-dark\");\n        } catch (error) {\n          console.warn(\"Failed to load syntax highlighting:\", error);\n        }\n      }, 100);\n    }\n  }, [blog, loading]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    if (!post || !post.translations) return \"\";\n    const translation = post.translations.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-12 text-center\">\n                      <h1>Loading...</h1>\n                      <p>Please wait while we load the blog post.</p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // If no blog post found or error, show 404 or redirect\n  if (!blog || error) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-12 text-center\">\n                      <h1>Blog Post Not Found</h1>\n                      <p>\n                        The blog post you&apos;re looking for doesn&apos;t\n                        exist.\n                      </p>\n                      <a\n                        href=\"/blog\"\n                        className=\"btn btn-mod btn-border btn-large btn-round\"\n                      >\n                        Back to Blog\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Generate article schema for the blog post\n  const articleSchema = generateArticleSchema({\n    title: getTranslation(blog, \"title\"),\n    description: getTranslation(blog, \"excerpt\"),\n    excerpt: getTranslation(blog, \"excerpt\"),\n    featuredImage: blog.featuredImage,\n    author: blog.author || \"DevSkills\",\n    publishedAt: blog.publishedAt,\n    modifiedAt: blog.updatedAt,\n    url: `https://devskills.ee/${currentLanguage}/blog-single/${blog.slug}`,\n  });\n\n  return (\n    <>\n      <UnifiedSEO\n        title={getTranslation(blog, \"title\")}\n        description={getTranslation(blog, \"excerpt\")}\n        slug={`blog-single/${blog.slug}`}\n        type=\"article\"\n        image={blog.featuredImage || \"https://devskills.ee/blog.jpg\"}\n        imageAlt={getTranslation(blog, \"title\")}\n        author={blog.author || \"DevSkills\"}\n        publishedAt={blog.publishedAt}\n        modifiedAt={blog.updatedAt}\n        schema={[articleSchema]}\n        keywords={\n          getTranslation(blog, \"keywords\") || [\n            \"blog\",\n            \"software development\",\n            \"devskills\",\n          ]\n        }\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content blog-hero-minimal\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-10 offset-lg-1\">\n                      <h1\n                        className=\"hs-title-3a mb-0 wow fadeInUpShort\"\n                        data-wow-duration=\"0.6s\"\n                      >\n                        {getTranslation(blog, \"title\")}\n                      </h1>\n                    </div>\n                  </div>\n                  {/* Author, Categories, Comments */}\n                  <div\n                    className=\"blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn\"\n                    data-wow-delay=\"0.2s\"\n                  >\n                    <div className=\"d-inline-block me-3\">\n                      <a href=\"#\">\n                        <i className=\"mi-clock size-16\" />\n                        <span className=\"visually-hidden\">Date:</span>{\" \"}\n                        {new Date(\n                          blog.publishedAt || blog.createdAt\n                        ).toLocaleDateString(\"en-US\", {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </a>\n                    </div>\n                    <div className=\"d-inline-block me-3\">\n                      <a href=\"#\">\n                        <i className=\"mi-user size-16\" />\n                        <span className=\"visually-hidden\">Author:</span>{\" \"}\n                        {blog.author?.name || \"DevSkills Team\"}\n                      </a>\n                    </div>\n                    {blog.categories && blog.categories.length > 0 && (\n                      <div className=\"d-inline-block me-3\">\n                        <i className=\"mi-folder size-16\" />\n                        <span className=\"visually-hidden\">Category:</span>\n                        <a href=\"#\">{blog.categories[0].name}</a>\n                      </div>\n                    )}\n                    <div className=\"d-inline-block me-3\">\n                      <i className=\"mi-time size-16\" />\n                      <span className=\"visually-hidden\">Read time:</span>{\" \"}\n                      {blog.readTime || 5} min\n                    </div>\n                  </div>\n                  {/* End Author, Categories, Comments */}\n\n                  {/* Navigation Buttons */}\n                  <div className=\"blog-nav-minimal\">\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      {/* Left side - Back to Blog and Previous */}\n                      <div className=\"d-flex gap-3\">\n                        <Link\n                          to={`/${currentLanguage}/blog`}\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            <i className=\"mi-arrow-left size-18 align-middle\" />\n                            &nbsp;{t(\"blog.back_to_blog\") || \"Back to Blog\"}\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            <i className=\"mi-arrow-left size-18 align-middle\" />\n                            &nbsp;{t(\"blog.back_to_blog\") || \"Back to Blog\"}\n                          </span>\n                        </Link>\n\n                        {blog?.navigation?.previous && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.previous.slug}`}\n                            className=\"link-hover-anim link-circle-1 align-middle\"\n                            data-link-animate=\"y\"\n                            title={blog.navigation.previous.title}\n                          >\n                            <span className=\"link-strong link-strong-unhovered\">\n                              <i className=\"mi-chevron-left size-18 align-middle\" />\n                              &nbsp;{t(\"blog.previous\") || \"Previous\"}\n                            </span>\n                            <span\n                              className=\"link-strong link-strong-hovered\"\n                              aria-hidden=\"true\"\n                            >\n                              <i className=\"mi-chevron-left size-18 align-middle\" />\n                              &nbsp;{t(\"blog.previous\") || \"Previous\"}\n                            </span>\n                          </Link>\n                        )}\n                      </div>\n\n                      {/* Right side - Next */}\n                      <div>\n                        {blog?.navigation?.next && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.next.slug}`}\n                            className=\"link-hover-anim link-circle-1 align-middle\"\n                            data-link-animate=\"y\"\n                            title={blog.navigation.next.title}\n                          >\n                            <span className=\"link-strong link-strong-unhovered\">\n                              {t(\"blog.next\") || \"Next\"}&nbsp;\n                              <i className=\"mi-chevron-right size-18 align-middle\" />\n                            </span>\n                            <span\n                              className=\"link-strong link-strong-hovered\"\n                              aria-hidden=\"true\"\n                            >\n                              {t(\"blog.next\") || \"Next\"}&nbsp;\n                              <i className=\"mi-chevron-right size-18 align-middle\" />\n                            </span>\n                          </Link>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container relative\">\n                  <div className=\"row\">\n                    {/* Content */}\n                    <div className=\"col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last\">\n                      {/* Post */}\n                      <div className=\"blog-item mb-80 mb-xs-40\">\n                        <div className=\"blog-item-body\">\n                          {blog.featuredImage && (\n                            <div className=\"mb-40 mb-xs-30\">\n                              <img\n                                src={blog.featuredImage}\n                                alt={getTranslation(blog, \"title\")}\n                                width={1350}\n                                height={796}\n                              />\n                            </div>\n                          )}\n\n                          {/* Blog excerpt */}\n                          <div className=\"lead mb-40\">\n                            {getTranslation(blog, \"excerpt\")}\n                          </div>\n\n                          {/* Blog content */}\n                          <div\n                            className=\"blog-content\"\n                            style={{\n                              lineHeight: \"1.8\",\n                              fontSize: \"16px\",\n                            }}\n                            dangerouslySetInnerHTML={{\n                              __html: getTranslation(blog, \"content\"),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/* End Post */}\n                      {/* Comments */}\n                      <div className=\"blog-comments-minimal mb-xs-40\">\n                        <h4 className=\"blog-page-title\">\n                          {t(\"blog.comments.title\")}{\" \"}\n                          <small className=\"number\">\n                            ({blog.comments?.length || 0})\n                          </small>\n                        </h4>\n                        <ul className=\"media-list comment-list clearlist\">\n                          <Comments comments={blog.comments || []} />\n                        </ul>\n                      </div>\n                      {/* End Comments */}\n                      {/* Add Comment */}\n                      <div className=\"blog-comments-minimal mb-xs-40\">\n                        <h4 className=\"blog-page-title\">\n                          {t(\"blog.comments.add_comment\")}\n                        </h4>\n                        {/* Form */}\n                        <Form blogSlug={blog.slug} />\n                        {/* End Form */}\n                      </div>\n                      {/* End Add Comment */}\n                      {/* Prev/Next Post */}\n                      <div className=\"clearfix mt-40\">\n                        {blog?.navigation?.previous && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.previous.slug}`}\n                            className=\"blog-item-more left\"\n                          >\n                            <i className=\"mi-chevron-left\" />\n                            &nbsp;{t(\"blog.previous\") || \"Previous\"}\n                          </Link>\n                        )}\n                        {blog?.navigation?.next && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.next.slug}`}\n                            className=\"blog-item-more right\"\n                          >\n                            {t(\"blog.next\") || \"Next\"}&nbsp;\n                            <i className=\"mi-chevron-right\" />\n                          </Link>\n                        )}\n                      </div>\n                      {/* End Prev/Next Post */}\n                    </div>\n                    {/* End Content */}\n                    {/* Sidebar */}\n                    <div className=\"col-lg-4 col-xl-3\">\n                      <Widget1 searchInputClass=\"form-control input-lg search-field round\" />\n                      {/* End Widget */}\n                    </div>\n                    {/* End Sidebar */}\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/privacy-policy/page.jsx\n\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { menuItems } from \"@/data/menu\";\n\nexport default function PrivacyPolicyPage() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title=\"Privacy Policy\"\n        description=\"DevSkills Privacy Policy - Learn how we collect, use, and protect your personal information when you use our services.\"\n        slug=\"privacy-policy\"\n        type=\"website\"\n        keywords={[\"privacy policy\", \"data protection\", \"devskills\", \"gdpr\"]}\n      />\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              {/* Page Header */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"privacy.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"privacy.lastUpdated\")}:{\" \"}\n                        {new Date().toLocaleDateString(currentLanguage, {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n\n              {/* Privacy Policy Content */}\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2\">\n                      {/* Introduction */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.intro.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.intro.text1\")}\n                        </p>\n                        <p className=\"text-gray\">{t(\"privacy.intro.text2\")}</p>\n                      </div>\n\n                      {/* Information We Collect */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.collect.title\")}\n                        </h2>\n\n                        <h3 className=\"h4 mb-20 text-white\">\n                          {t(\"privacy.collect.personal.title\")}\n                        </h3>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.collect.personal.text\")}\n                        </p>\n                        <ul className=\"text-gray mb-30\">\n                          <li>{t(\"privacy.collect.personal.item1\")}</li>\n                          <li>{t(\"privacy.collect.personal.item2\")}</li>\n                          <li>{t(\"privacy.collect.personal.item3\")}</li>\n                          <li>{t(\"privacy.collect.personal.item4\")}</li>\n                        </ul>\n\n                        <h3 className=\"h4 mb-20 text-white\">\n                          {t(\"privacy.collect.usage.title\")}\n                        </h3>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.collect.usage.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.collect.usage.item1\")}</li>\n                          <li>{t(\"privacy.collect.usage.item2\")}</li>\n                          <li>{t(\"privacy.collect.usage.item3\")}</li>\n                          <li>{t(\"privacy.collect.usage.item4\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* How We Use Information */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.use.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.use.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.use.item1\")}</li>\n                          <li>{t(\"privacy.use.item2\")}</li>\n                          <li>{t(\"privacy.use.item3\")}</li>\n                          <li>{t(\"privacy.use.item4\")}</li>\n                          <li>{t(\"privacy.use.item5\")}</li>\n                          <li>{t(\"privacy.use.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Information Sharing */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.sharing.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.sharing.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.sharing.item1\")}</li>\n                          <li>{t(\"privacy.sharing.item2\")}</li>\n                          <li>{t(\"privacy.sharing.item3\")}</li>\n                          <li>{t(\"privacy.sharing.item4\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Data Security */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.security.title\")}\n                        </h2>\n                        <p className=\"text-gray\">\n                          {t(\"privacy.security.text\")}\n                        </p>\n                      </div>\n\n                      {/* Your Rights */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.rights.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.rights.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.rights.item1\")}</li>\n                          <li>{t(\"privacy.rights.item2\")}</li>\n                          <li>{t(\"privacy.rights.item3\")}</li>\n                          <li>{t(\"privacy.rights.item4\")}</li>\n                          <li>{t(\"privacy.rights.item5\")}</li>\n                          <li>{t(\"privacy.rights.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Cookies */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.cookies.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"privacy.cookies.text\")}</p>\n                      </div>\n\n                      {/* Contact Information */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.contact.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.contact.text\")}\n                        </p>\n                        <div className=\"text-gray\">\n                          <p>\n                            <strong>DevSkills OÜ</strong>\n                          </p>\n                          <p>{t(\"privacy.contact.address\")}</p>\n                          <p>{t(\"privacy.contact.email\")}</p>\n                          <p>{t(\"privacy.contact.phone\")}</p>\n                        </div>\n                      </div>\n\n                      {/* Updates */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.updates.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"privacy.updates.text\")}</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/terms-conditions/page.jsx\n\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { menuItems } from \"@/data/menu\";\n\nconst dark = true;\n\nexport default function TermsConditionsPage() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"en\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title=\"Terms and Conditions\"\n        description=\"DevSkills Terms and Conditions - Legal terms governing the use of our software development services and Business Comanager platform.\"\n        slug=\"terms-conditions\"\n        type=\"website\"\n        keywords={[\n          \"terms and conditions\",\n          \"legal\",\n          \"devskills\",\n          \"service agreement\",\n        ]}\n      />\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              {/* Page Header */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"terms.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"terms.lastUpdated\")}:{\" \"}\n                        {new Date().toLocaleDateString(currentLanguage, {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n\n              {/* Terms Content */}\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2\">\n                      {/* Introduction */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.agreement.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.agreement.text1\")}\n                        </p>\n                        <p className=\"text-gray\">\n                          {t(\"terms.agreement.text2\")}\n                        </p>\n                      </div>\n\n                      {/* Services */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.services.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.services.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.services.item1\")}</li>\n                          <li>{t(\"terms.services.item2\")}</li>\n                          <li>{t(\"terms.services.item3\")}</li>\n                          <li>{t(\"terms.services.item4\")}</li>\n                          <li>{t(\"terms.services.item5\")}</li>\n                          <li>{t(\"terms.services.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* User Responsibilities */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.responsibilities.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.responsibilities.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.responsibilities.item1\")}</li>\n                          <li>{t(\"terms.responsibilities.item2\")}</li>\n                          <li>{t(\"terms.responsibilities.item3\")}</li>\n                          <li>{t(\"terms.responsibilities.item4\")}</li>\n                          <li>{t(\"terms.responsibilities.item5\")}</li>\n                          <li>{t(\"terms.responsibilities.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Payment Terms */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.payment.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.payment.text\")}\n                        </p>\n                        <ul className=\"text-gray mb-20\">\n                          <li>{t(\"terms.payment.item1\")}</li>\n                          <li>{t(\"terms.payment.item2\")}</li>\n                          <li>{t(\"terms.payment.item3\")}</li>\n                          <li>{t(\"terms.payment.item4\")}</li>\n                          <li>{t(\"terms.payment.item5\")}</li>\n                        </ul>\n                        <p className=\"text-gray\">{t(\"terms.payment.text2\")}</p>\n                      </div>\n\n                      {/* Intellectual Property */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.ip.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">{t(\"terms.ip.our\")}</p>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.ip.custom\")}\n                          will be specified in individual project agreements.\n                        </p>\n                        <p className=\"text-gray\">{t(\"terms.ip.client\")}</p>\n                      </div>\n\n                      {/* Confidentiality */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.confidentiality.title\")}\n                        </h2>\n                        <p className=\"text-gray\">\n                          {t(\"terms.confidentiality.text\")}\n                        </p>\n                      </div>\n\n                      {/* Limitation of Liability */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.liability.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.liability.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.liability.item1\")}</li>\n                          <li>{t(\"terms.liability.item2\")}</li>\n                          <li>{t(\"terms.liability.item3\")}</li>\n                          <li>{t(\"terms.liability.item4\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Warranties */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.warranties.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.warranties.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.warranties.item1\")}</li>\n                          <li>{t(\"terms.warranties.item2\")}</li>\n                          <li>{t(\"terms.warranties.item3\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Termination */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.termination.title\")}\n                        </h2>\n                        <p className=\"text-gray\">\n                          {t(\"terms.termination.text\")}\n                        </p>\n                      </div>\n\n                      {/* Governing Law */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.governing.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"terms.governing.text\")}</p>\n                      </div>\n\n                      {/* Contact Information */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.contact.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.contact.text\")}\n                        </p>\n                        <div className=\"text-gray\">\n                          <p>\n                            <strong>DevSkills OÜ</strong>\n                          </p>\n                          <p>{t(\"terms.contact.address\")}</p>\n                          <p>{t(\"terms.contact.email\")}</p>\n                          <p>{t(\"terms.contact.phone\")}</p>\n                        </div>\n                      </div>\n\n                      {/* Changes to Terms */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.changes.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"terms.changes.text\")}</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport { Link } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function MainPageNotFound() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <UnifiedSEO\n        title=\"Page Not Found - 404\"\n        description=\"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.\"\n        slug=\"404\"\n        type=\"website\"\n        image=\"https://devskills.ee/404.jpg\"\n        keywords={[\"404\", \"page not found\", \"error\", \"devskills\"]}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              {/* Hero Section */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-1 mb-20 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                    style={{ fontSize: \"8rem\", fontWeight: \"700\" }}\n                  >\n                    404\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p\n                        className=\"section-title-small mb-0 opacity-075\"\n                        style={{ fontSize: \"2rem\" }}\n                      >\n                        Page Not Found\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n\n              {/* Content Section */}\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2 text-center\">\n                      <div className=\"wow fadeInUp\" data-wow-delay=\"0.1s\">\n                        <h2 className=\"section-title mb-30 mb-sm-20\">\n                          <span className=\"text-gray\">Oops!</span> Something\n                          went wrong\n                          <span className=\"text-gray\">.</span>\n                        </h2>\n                        <div className=\"text-gray mb-40 mb-sm-30\">\n                          <p className=\"mb-20\">\n                            The page you were looking for could not be found. It\n                            might have been removed, had its name changed, or is\n                            temporarily unavailable.\n                          </p>\n                          <p className=\"mb-0\">\n                            Don't worry, you can navigate back to our homepage\n                            or explore our services.\n                          </p>\n                        </div>\n                        <div className=\"local-scroll\">\n                          <Link\n                            to=\"/\"\n                            className=\"btn btn-mod btn-w btn-circle btn-medium me-3 mb-xs-10\"\n                            data-btn-animate=\"y\"\n                          >\n                            <span className=\"btn-animate-y\">\n                              <span className=\"btn-animate-y-1\">\n                                <i className=\"mi-home size-18 align-center me-2\" />\n                                Back to Home\n                              </span>\n                              <span\n                                className=\"btn-animate-y-2\"\n                                aria-hidden=\"true\"\n                              >\n                                <i className=\"mi-home size-18 align-center me-2\" />\n                                Back to Home\n                              </span>\n                            </span>\n                          </Link>\n                          <Link\n                            to=\"/services\"\n                            className=\"btn btn-mod btn-border-w btn-circle btn-medium\"\n                            data-btn-animate=\"y\"\n                          >\n                            <span className=\"btn-animate-y\">\n                              <span className=\"btn-animate-y-1\">\n                                Our Services\n                              </span>\n                              <span\n                                className=\"btn-animate-y-2\"\n                                aria-hidden=\"true\"\n                              >\n                                Our Services\n                              </span>\n                            </span>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport { authAPI } from \"../utils/api\";\n\nconst AdminLogin = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n    // Clear error when user starts typing\n    if (error) setError(\"\");\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const { response, data } = await authAPI.login(formData);\n\n      if (data.success) {\n        // Store token in localStorage\n        localStorage.setItem(\"adminToken\", data.token);\n        localStorage.setItem(\"adminUser\", JSON.stringify(data.user));\n\n        // Redirect to admin dashboard\n        navigate(\"/admin/dashboard\");\n      } else {\n        setError(data.message || \"Login failed\");\n      }\n    } catch (err) {\n      console.error(\"Login error:\", err);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Admin Login - DevSkills\"\n        description=\"Admin login page for DevSkills content management\"\n        noIndex={true}\n      />\n\n      {/* Page Wrapper */}\n      <div id=\"page\" className=\"page\">\n        {/* Main Content */}\n        <main id=\"main\">\n          {/* Admin Login Section */}\n          <section\n            className=\"page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section\"\n            id=\"admin-login\"\n          >\n            <div className=\"container relative\">\n              <div className=\"row\">\n                <div className=\"col-md-6 offset-md-3 col-lg-4 offset-lg-4\">\n                  {/* Login Form */}\n                  <div className=\"form-container\">\n                    {/* Header */}\n                    <div className=\"text-center mb-60 mb-sm-40\">\n                      <div className=\"hs-line-4 font-alt black mb-20 mb-xs-10\">\n                        <span className=\"color-primary-1\">DevSkills</span> Admin\n                      </div>\n                      <p className=\"section-descr mb-0\">\n                        Sign in to access the admin dashboard\n                      </p>\n                    </div>\n\n                    {/* Error Message */}\n                    {error && (\n                      <div className=\"alert alert-danger mb-30\" role=\"alert\">\n                        <i className=\"mi-warning\"></i>\n                        {error}\n                      </div>\n                    )}\n\n                    {/* Login Form */}\n                    <form className=\"form contact-form\" onSubmit={handleSubmit}>\n                      {/* Email Field */}\n                      <div className=\"form-group\">\n                        <label htmlFor=\"email\" className=\"sr-only\">\n                          Email Address\n                        </label>\n                        <input\n                          type=\"email\"\n                          name=\"email\"\n                          id=\"email\"\n                          className=\"input-lg round form-control\"\n                          placeholder=\"Email Address\"\n                          value={formData.email}\n                          onChange={handleChange}\n                          required\n                          autoComplete=\"email\"\n                        />\n                      </div>\n\n                      {/* Password Field */}\n                      <div className=\"form-group\">\n                        <label htmlFor=\"password\" className=\"sr-only\">\n                          Password\n                        </label>\n                        <input\n                          type=\"password\"\n                          name=\"password\"\n                          id=\"password\"\n                          className=\"input-lg round form-control\"\n                          placeholder=\"Password\"\n                          value={formData.password}\n                          onChange={handleChange}\n                          required\n                          autoComplete=\"current-password\"\n                        />\n                      </div>\n\n                      {/* Submit Button */}\n                      <div className=\"form-group\">\n                        <button\n                          type=\"submit\"\n                          className=\"btn btn-mod btn-color btn-large btn-round btn-full-width\"\n                          disabled={loading}\n                        >\n                          {loading ? (\n                            <>\n                              <i className=\"fa fa-spinner fa-spin me-2\"></i>\n                              Signing in...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"mi-lock me-2\"></i>\n                              Sign In\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </form>\n\n                    {/* Footer */}\n                    <div className=\"text-center mt-40\">\n                      <p className=\"small opacity-07\">\n                        © 2024 DevSkills. All rights reserved.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n        </main>\n      </div>\n    </>\n  );\n};\n\nexport default AdminLogin;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { authAPI, adminAPI } from \"../utils/api\";\n\nconst AdminDashboard = () => {\n  const navigate = useNavigate();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem(\"adminToken\");\n      const userData = localStorage.getItem(\"adminUser\");\n\n      if (!token || !userData) {\n        navigate(\"/admin\");\n        return;\n      }\n\n      try {\n        // Verify token with backend\n        const { response, data } = await authAPI.getMe();\n\n        if (response.ok && data.success) {\n          setUser(data.user);\n        } else {\n          // Token invalid, redirect to login\n          localStorage.removeItem(\"adminToken\");\n          localStorage.removeItem(\"adminUser\");\n          navigate(\"/admin\");\n        }\n      } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        localStorage.removeItem(\"adminToken\");\n        localStorage.removeItem(\"adminUser\");\n        navigate(\"/admin\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [navigate]);\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"adminToken\");\n    localStorage.removeItem(\"adminUser\");\n    navigate(\"/admin\");\n  };\n\n  if (loading) {\n    return (\n      <div id=\"page\" className=\"page\">\n        <main id=\"main\">\n          <section className=\"page-section\">\n            <div className=\"container relative\">\n              <div className=\"row\">\n                <div className=\"col-12 text-center\">\n                  <div className=\"loading-animation\">\n                    <iconify-icon\n                      icon=\"solar:refresh-bold\"\n                      className=\"color-primary-1\"\n                      style={{\n                        fontSize: \"3rem\",\n                        animation: \"spin 1s linear infinite\",\n                      }}\n                    ></iconify-icon>\n                    <div className=\"mt-20\">\n                      <div className=\"hs-line-4 font-alt black\">Loading...</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <SEO\n        title=\"Admin Dashboard - DevSkills\"\n        description=\"DevSkills admin dashboard for content management\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Dashboard\">\n        {/* Stats Cards */}\n        <div className=\"row mb-40\">\n          {/* Total Posts */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:document-text-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Total Posts</div>\n              <div className=\"number-2-number\">0</div>\n            </div>\n          </div>\n\n          {/* Categories */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:folder-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Categories</div>\n              <div className=\"number-2-number\">5</div>\n            </div>\n          </div>\n\n          {/* Comments */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:chat-round-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Comments</div>\n              <div className=\"number-2-number\">0</div>\n            </div>\n          </div>\n\n          {/* Page Views */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:eye-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Page Views</div>\n              <div className=\"number-2-number\">-</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"mb-20\">\n              <h3 className=\"hs-line-4 font-alt black mb-20 mb-xs-10\">\n                Quick Actions\n              </h3>\n            </div>\n\n            <div className=\"row\">\n              {/* New Blog Post */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:add-circle-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">New Blog Post</h3>\n                  <div className=\"alt-features-descr\">\n                    Create a new multilingual blog post with rich content and\n                    scheduling.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/blog/new\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Create Post\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Manage Posts */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:documents-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">Manage Posts</h3>\n                  <div className=\"alt-features-descr\">\n                    Edit, publish, schedule, and organize your existing blog\n                    posts.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/posts\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Manage Posts\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Categories & Tags */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:folder-with-files-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">\n                    Categories & Tags\n                  </h3>\n                  <div className=\"alt-features-descr\">\n                    Organize your content with categories and tags for better\n                    navigation.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/categories\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Organize Content\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Comment Management */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:chat-round-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">\n                    Comment Management\n                  </h3>\n                  <div className=\"alt-features-descr\">\n                    Review, approve, and manage comments from your blog readers.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/comments\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Manage Comments\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Analytics */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:chart-2-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">Analytics</h3>\n                  <div className=\"alt-features-descr\">\n                    View detailed analytics and insights about your blog\n                    performance.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/analytics\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      View Analytics\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminDashboard;\n", "// client/src/pages/AdminBlogPosts.jsx\n\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI, blogAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminBlogPosts = () => {\n  const navigate = useNavigate();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 10,\n    status: \"all\",\n    search: \"\",\n  });\n  const [pagination, setPagination] = useState({});\n\n  useEffect(() => {\n    loadPosts();\n  }, [filters]);\n\n  const loadPosts = async () => {\n    try {\n      setLoading(true);\n\n      const params = {};\n      Object.entries(filters).forEach(([key, value]) => {\n        if (value && value !== \"all\") {\n          params[key] = value;\n        }\n      });\n\n      const { response, data } = await adminAPI.getPosts(params);\n\n      if (data.success) {\n        setPosts(data.data.posts);\n        setPagination(data.data.pagination);\n      } else {\n        setError(data.message || \"Failed to load posts\");\n      }\n    } catch (error) {\n      console.error(\"Load posts error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (postId) => {\n    if (\n      !confirm(\n        \"Are you sure you want to delete this blog post? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const { response, data } = await blogAPI.deletePost(postId);\n\n      if (data.success) {\n        loadPosts(); // Reload the list\n      } else {\n        setError(data.message || \"Failed to delete post\");\n      }\n    } catch (error) {\n      console.error(\"Delete error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleToggleVisibility = async (postId) => {\n    try {\n      const { response, data } = await blogAPI.toggleVisibility(postId);\n\n      if (data.success) {\n        loadPosts(); // Reload the list\n      } else {\n        setError(data.message || \"Failed to toggle visibility\");\n      }\n    } catch (error) {\n      console.error(\"Toggle visibility error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/blog-images/${filename}`;\n  };\n\n  const getStatusBadge = (post) => {\n    if (!post.published) {\n      return (\n        <span className=\"badge bg-secondary\">\n          <iconify-icon\n            icon=\"solar:document-text-bold\"\n            className=\"me-1\"\n          ></iconify-icon>\n          Draft\n        </span>\n      );\n    }\n\n    if (post.scheduledAt && new Date(post.scheduledAt) > new Date()) {\n      return (\n        <span className=\"badge bg-warning\">\n          <iconify-icon\n            icon=\"solar:clock-circle-bold\"\n            className=\"me-1\"\n          ></iconify-icon>\n          Scheduled\n        </span>\n      );\n    }\n\n    return (\n      <span className=\"badge bg-success\">\n        <iconify-icon\n          icon=\"solar:check-circle-bold\"\n          className=\"me-1\"\n        ></iconify-icon>\n        Published\n      </span>\n    );\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Blog Posts - Admin\"\n        description=\"Manage blog posts in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Blog Posts\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Manage your blog posts, create new content, and organize your\n                articles.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={() => navigate(\"/admin/blog/new\")}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Post\n              </button>\n            </div>\n          </div>\n        </div>\n        {/* Filters */}\n        <div className=\"admin-table mb-30\" style={{ padding: \"15px 20px\" }}>\n          <div className=\"row g-3\">\n            <div className=\"col-12 col-md-6 col-lg-4\">\n              <label className=\"form-label\">Search Posts</label>\n              <input\n                type=\"text\"\n                value={filters.search}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    search: e.target.value,\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n                placeholder=\"Search by title...\"\n              />\n            </div>\n\n            <div className=\"col-6 col-md-3 col-lg-3\">\n              <label className=\"form-label\">Status</label>\n              <select\n                value={filters.status}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    status: e.target.value,\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n              >\n                <option value=\"all\">All Posts</option>\n                <option value=\"published\">Published</option>\n                <option value=\"draft\">Drafts</option>\n              </select>\n            </div>\n\n            <div className=\"col-6 col-md-3 col-lg-2\">\n              <label className=\"form-label\">Per Page</label>\n              <select\n                value={filters.limit}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    limit: parseInt(e.target.value),\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n              >\n                <option value={10}>10</option>\n                <option value={25}>25</option>\n                <option value={50}>50</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {/* Posts Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">Loading posts...</div>\n            </div>\n          ) : posts.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:document-text-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No blog posts found\n              </div>\n              <p className=\"section-descr mb-30\">\n                {filters.search || filters.status !== \"all\"\n                  ? \"Try adjusting your search filters or create your first blog post.\"\n                  : \"Get started by creating your first blog post.\"}\n              </p>\n              <button\n                onClick={() => navigate(\"/admin/blog/new\")}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Post\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Title</th>\n                        <th>Status</th>\n                        <th>Author</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {posts.map((post) => {\n                        const englishTranslation =\n                          post.translations.find((t) => t.language === \"en\") ||\n                          post.translations[0];\n\n                        return (\n                          <tr key={post.id}>\n                            <td>\n                              <div className=\"d-flex align-items-center\">\n                                {post.featuredImage && (\n                                  <img\n                                    className=\"rounded me-3\"\n                                    src={getImageUrl(post.featuredImage)}\n                                    alt=\"\"\n                                    style={{\n                                      width: \"50px\",\n                                      height: \"50px\",\n                                      objectFit: \"cover\",\n                                    }}\n                                    onError={(e) => {\n                                      e.target.style.display = \"none\";\n                                    }}\n                                  />\n                                )}\n                                <div>\n                                  <div className=\"fw-bold\">\n                                    {englishTranslation?.title || \"Untitled\"}\n                                  </div>\n                                  <small className=\"text-muted\">\n                                    /{post.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </td>\n                            <td>\n                              {getStatusBadge(post)}\n                              {post.featured && (\n                                <span className=\"badge bg-primary ms-2\">\n                                  <iconify-icon\n                                    icon=\"solar:star-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Featured\n                                </span>\n                              )}\n                            </td>\n                            <td>{post.author.name || post.author.email}</td>\n                            <td>{formatDate(post.createdAt)}</td>\n                            <td>\n                              <div className=\"btn-group\" role=\"group\">\n                                <button\n                                  onClick={() =>\n                                    navigate(`/admin/blog/edit/${post.id}`)\n                                  }\n                                  className=\"btn btn-sm btn-outline-primary\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() =>\n                                    handleToggleVisibility(post.id)\n                                  }\n                                  className={`btn btn-sm ${\n                                    post.published\n                                      ? \"btn-outline-warning\"\n                                      : \"btn-outline-success\"\n                                  }`}\n                                  title={\n                                    post.published ? \"Unpublish\" : \"Publish\"\n                                  }\n                                >\n                                  <iconify-icon\n                                    icon={\n                                      post.published\n                                        ? \"solar:eye-closed-bold\"\n                                        : \"solar:eye-bold\"\n                                    }\n                                  ></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(post.id)}\n                                  className=\"btn btn-sm btn-outline-danger\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {posts.map((post) => {\n                    const englishTranslation =\n                      post.translations.find((t) => t.language === \"en\") ||\n                      post.translations[0];\n\n                    return (\n                      <div key={post.id} className=\"col-12\">\n                        <div className=\"card border-0 shadow-sm\">\n                          <div className=\"card-body p-3\">\n                            <div className=\"row align-items-center\">\n                              <div className=\"col-12 mb-2\">\n                                <div className=\"d-flex align-items-center\">\n                                  {post.featuredImage && (\n                                    <img\n                                      className=\"rounded me-3\"\n                                      src={getImageUrl(post.featuredImage)}\n                                      alt=\"\"\n                                      style={{\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        objectFit: \"cover\",\n                                      }}\n                                      onError={(e) => {\n                                        e.target.style.display = \"none\";\n                                      }}\n                                    />\n                                  )}\n                                  <div className=\"flex-grow-1\">\n                                    <h6 className=\"mb-1 fw-bold\">\n                                      {englishTranslation?.title || \"Untitled\"}\n                                    </h6>\n                                    <small className=\"text-muted\">\n                                      /{post.slug}\n                                    </small>\n                                  </div>\n                                </div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Status\n                                </small>\n                                <div>\n                                  {getStatusBadge(post)}\n                                  {post.featured && (\n                                    <span className=\"badge bg-primary ms-1\">\n                                      <iconify-icon\n                                        icon=\"solar:star-bold\"\n                                        className=\"me-1\"\n                                      ></iconify-icon>\n                                      Featured\n                                    </span>\n                                  )}\n                                </div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Author\n                                </small>\n                                <small>\n                                  {post.author.name || post.author.email}\n                                </small>\n                              </div>\n\n                              <div className=\"col-12 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Created\n                                </small>\n                                <small>{formatDate(post.createdAt)}</small>\n                              </div>\n\n                              <div className=\"col-12\">\n                                <div className=\"d-flex gap-2 flex-wrap\">\n                                  <button\n                                    onClick={() =>\n                                      navigate(`/admin/blog/edit/${post.id}`)\n                                    }\n                                    className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                    title=\"Edit\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:pen-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Edit\n                                  </button>\n\n                                  <button\n                                    onClick={() =>\n                                      handleToggleVisibility(post.id)\n                                    }\n                                    className={`btn btn-sm flex-fill ${\n                                      post.published\n                                        ? \"btn-outline-warning\"\n                                        : \"btn-outline-success\"\n                                    }`}\n                                    title={\n                                      post.published ? \"Unpublish\" : \"Publish\"\n                                    }\n                                  >\n                                    <iconify-icon\n                                      icon={\n                                        post.published\n                                          ? \"solar:eye-closed-bold\"\n                                          : \"solar:eye-bold\"\n                                      }\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    {post.published ? \"Hide\" : \"Show\"}\n                                  </button>\n\n                                  <button\n                                    onClick={() => handleDelete(post.id)}\n                                    className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                    title=\"Delete\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:trash-bin-trash-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Delete\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {pagination.pages > 1 && (\n          <div className=\"row mt-30 align-items-center\">\n            <div className=\"col-12 col-md-6 mb-3 mb-md-0\">\n              <p className=\"small text-muted mb-0 text-center text-md-start\">\n                Showing {(pagination.page - 1) * pagination.limit + 1} to{\" \"}\n                {Math.min(pagination.page * pagination.limit, pagination.total)}{\" \"}\n                of {pagination.total} results\n              </p>\n            </div>\n            <div className=\"col-12 col-md-6\">\n              <nav aria-label=\"Blog posts pagination\">\n                <ul className=\"pagination pagination-sm justify-content-center justify-content-md-end mb-0\">\n                  <li\n                    className={`page-item ${\n                      pagination.page <= 1 ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))\n                      }\n                      disabled={pagination.page <= 1}\n                    >\n                      Previous\n                    </button>\n                  </li>\n\n                  <li className=\"page-item active\">\n                    <span className=\"page-link\">\n                      Page {pagination.page} of {pagination.pages}\n                    </span>\n                  </li>\n\n                  <li\n                    className={`page-item ${\n                      pagination.page >= pagination.pages ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))\n                      }\n                      disabled={pagination.page >= pagination.pages}\n                    >\n                      Next\n                    </button>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminBlogPosts;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport TipTapEditor from \"../components/editor/TipTapEditor\";\nimport { adminAPI, blogAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminBlogEditor = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { id } = useParams(); // For editing existing posts\n  const isEditing = Boolean(id);\n\n  // Utility function to construct image URLs\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/blog-images/${filename}`;\n  };\n\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  // Available languages from i18n (memoized to prevent infinite re-renders)\n  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));\n\n  // Form state\n  const [formData, setFormData] = useState(() => {\n    // Initialize translations for all available languages\n    const initialTranslations = {};\n    availableLanguages.forEach((lang) => {\n      initialTranslations[lang] = {\n        title: \"\",\n        excerpt: \"\",\n        content: \"\",\n        metaTitle: \"\",\n        metaDesc: \"\",\n        keywords: [],\n      };\n    });\n\n    return {\n      slug: \"\",\n      featured: false,\n      published: false,\n      scheduledAt: \"\",\n      featuredImage: null,\n      featuredImageAlt: \"\",\n      readTime: \"\",\n      categoryIds: [],\n      tagIds: [],\n      translations: initialTranslations,\n    };\n  });\n\n  const [activeLanguage, setActiveLanguage] = useState(\"en\");\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n\n  // Load categories and tags\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n\n        // Check if user is authenticated\n        const token = localStorage.getItem(\"adminToken\");\n        if (!token) {\n          setError(\n            \"Authentication required. Please log in to access this page.\"\n          );\n          setLoading(false);\n          return;\n        }\n\n        // Load categories and tags using the API utility\n        const [categoriesResult, tagsResult] = await Promise.all([\n          adminAPI.getCategories(),\n          adminAPI.getTags(),\n        ]);\n\n        // Handle categories response\n        if (categoriesResult.response.ok && categoriesResult.data) {\n          setCategories(categoriesResult.data.data || []);\n        } else {\n          console.error(\n            \"Categories API failed:\",\n            categoriesResult.response.status,\n            categoriesResult.response.statusText\n          );\n          if (\n            categoriesResult.response.status === 401 ||\n            categoriesResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setCategories([]);\n        }\n\n        // Handle tags response\n        if (tagsResult.response.ok && tagsResult.data) {\n          setTags(tagsResult.data.data || []);\n        } else {\n          console.error(\n            \"Tags API failed:\",\n            tagsResult.response.status,\n            tagsResult.response.statusText\n          );\n          if (\n            tagsResult.response.status === 401 ||\n            tagsResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setTags([]);\n        }\n\n        // Load existing post if editing\n        if (isEditing) {\n          const { response: postRes, data: postData } = await adminAPI.getPost(\n            id\n          );\n\n          if (postRes.ok && postData.success) {\n            try {\n              const post = postData.data;\n\n              // Convert translations array to object\n              const translationsObj = {};\n              if (post.translations && Array.isArray(post.translations)) {\n                post.translations.forEach((t) => {\n                  translationsObj[t.language] = t;\n                });\n              }\n\n              setFormData((prev) => ({\n                ...prev,\n                slug: post.slug || \"\",\n                featured: post.featured || false,\n                published: post.published || false,\n                scheduledAt: post.scheduledAt\n                  ? new Date(post.scheduledAt).toISOString().slice(0, 16)\n                  : \"\",\n                featuredImage: null,\n                featuredImageAlt: post.featuredImageAlt || \"\",\n                readTime: post.readTime || \"\",\n                categoryIds: post.categories\n                  ? post.categories.map((c) => c.id)\n                  : [],\n                tagIds: post.tags ? post.tags.map((t) => t.id) : [],\n                translations: { ...prev.translations, ...translationsObj },\n              }));\n\n              if (post.featuredImage) {\n                setImagePreview(getImageUrl(post.featuredImage));\n              }\n            } catch (jsonError) {\n              console.error(\"Failed to parse post response:\", jsonError);\n              setError(\"Failed to load post data - invalid response format\");\n            }\n          } else {\n            console.error(\n              \"Post API failed:\",\n              postRes.status,\n              postRes.statusText\n            );\n            setError(\n              postData.message ||\n                `Failed to load post: ${postRes.status} ${postRes.statusText}`\n            );\n          }\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n        if (error.message && error.message.includes(\"fetch\")) {\n          setError(\n            \"Failed to connect to the server. Please check if the backend is running on localhost:4004\"\n          );\n        } else {\n          setError(\"Failed to load data. Please try again.\");\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [id, isEditing]);\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleTranslationChange = (language, field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      translations: {\n        ...prev.translations,\n        [language]: {\n          ...prev.translations[language],\n          [field]: value,\n        },\n      },\n    }));\n  };\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData((prev) => ({\n        ...prev,\n        featuredImage: file,\n      }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreview(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const formDataToSend = new FormData();\n\n      // Add basic fields\n      formDataToSend.append(\"slug\", formData.slug);\n      formDataToSend.append(\"featured\", formData.featured);\n      formDataToSend.append(\"published\", formData.published);\n      if (formData.scheduledAt) {\n        formDataToSend.append(\"scheduledAt\", formData.scheduledAt);\n      }\n      formDataToSend.append(\"featuredImageAlt\", formData.featuredImageAlt);\n      if (formData.readTime) {\n        formDataToSend.append(\"readTime\", formData.readTime);\n      }\n\n      // Add arrays\n      formDataToSend.append(\n        \"categoryIds\",\n        JSON.stringify(formData.categoryIds)\n      );\n      formDataToSend.append(\"tagIds\", JSON.stringify(formData.tagIds));\n      formDataToSend.append(\n        \"translations\",\n        JSON.stringify(formData.translations)\n      );\n\n      // Add image if selected\n      if (formData.featuredImage) {\n        formDataToSend.append(\"featuredImage\", formData.featuredImage);\n      }\n\n      // Use the proper API utility\n      let result;\n      if (isEditing) {\n        result = await blogAPI.updatePost(id, formDataToSend);\n      } else {\n        result = await blogAPI.createPost(formDataToSend);\n      }\n\n      const { response, data } = result;\n\n      if (response.ok && data && data.success) {\n        setSuccess(\n          `Blog post ${isEditing ? \"updated\" : \"created\"} successfully!`\n        );\n        setTimeout(() => {\n          navigate(\"/admin/posts\");\n        }, 2000);\n      } else {\n        const errorMessage =\n          data?.message ||\n          `Failed to ${isEditing ? \"update\" : \"create\"} blog post`;\n        setError(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Save error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <>\n      <SEO\n        title={`${isEditing ? \"Edit\" : \"Create\"} Blog Post - Admin`}\n        description=\"Create or edit blog posts in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout\n        title={isEditing ? \"Edit Blog Post\" : \"Create New Blog Post\"}\n      >\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:danger-triangle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"alert alert-success mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:check-circle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {success}\n            </div>\n          )}\n\n          {/* Basic Settings */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:settings-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Basic Settings\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Configure the basic properties of your blog post\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Slug (URL)\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.slug}\n                  onChange={(e) => handleInputChange(\"slug\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"blog-post-url\"\n                />\n                <small className=\"form-text text-muted\">\n                  This will be the URL path for your blog post (e.g.,\n                  /blog/your-slug)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:clock-circle-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Read Time (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.readTime}\n                  onChange={(e) =>\n                    handleInputChange(\"readTime\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"5\"\n                  min=\"1\"\n                  max=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  Estimated reading time for this post\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:calendar-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Schedule Publication\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  value={formData.scheduledAt}\n                  onChange={(e) =>\n                    handleInputChange(\"scheduledAt\", e.target.value)\n                  }\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Leave empty to publish immediately when published is checked\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:star-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Post Options\n                </label>\n                <div className=\"d-flex flex-column gap-2\">\n                  <div className=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"featured\"\n                      checked={formData.featured}\n                      onChange={(e) =>\n                        handleInputChange(\"featured\", e.target.checked)\n                      }\n                      className=\"form-check-input\"\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"featured\">\n                      <iconify-icon\n                        icon=\"solar:star-bold\"\n                        className=\"me-1\"\n                      ></iconify-icon>\n                      Featured Post\n                    </label>\n                    <small className=\"form-text text-muted d-block\">\n                      Show this post prominently on the homepage\n                    </small>\n                  </div>\n\n                  <div className=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"published\"\n                      checked={formData.published}\n                      onChange={(e) =>\n                        handleInputChange(\"published\", e.target.checked)\n                      }\n                      className=\"form-check-input\"\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"published\">\n                      <iconify-icon\n                        icon=\"solar:check-circle-bold\"\n                        className=\"me-1\"\n                      ></iconify-icon>\n                      Published\n                    </label>\n                    <small className=\"form-text text-muted d-block\">\n                      Make this post visible to the public\n                    </small>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Featured Image */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:gallery-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Featured Image\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Upload a featured image that will be displayed with your blog\n                  post\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:upload-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Upload Image\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageChange}\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Recommended size: 1200x630px. Supported formats: JPG, PNG,\n                  WebP\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:eye-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Alt Text\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.featuredImageAlt}\n                  onChange={(e) =>\n                    handleInputChange(\"featuredImageAlt\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Describe the image for accessibility\"\n                />\n                <small className=\"form-text text-muted\">\n                  Describe the image for screen readers and SEO\n                </small>\n              </div>\n\n              {imagePreview && (\n                <div className=\"col-12\">\n                  <div className=\"mb-20\">\n                    <label className=\"form-label\">Image Preview</label>\n                  </div>\n                  <div className=\"text-center\">\n                    <img\n                      src={imagePreview}\n                      alt=\"Preview\"\n                      className=\"image-preview\"\n                      style={{ maxWidth: \"400px\", height: \"auto\" }}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Language Tabs */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <i className=\"mi-globe me-2 color-primary-1\"></i>\n                  Content (Multi-language)\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Create content in multiple languages. At least English content\n                  is required.\n                </p>\n              </div>\n            </div>\n\n            {/* Language Selector */}\n            <div className=\"language-tabs mb-30\">\n              {availableLanguages.map((lang) => (\n                <button\n                  key={lang}\n                  type=\"button\"\n                  onClick={() => setActiveLanguage(lang)}\n                  className={`language-tab ${\n                    activeLanguage === lang ? \"active\" : \"\"\n                  }`}\n                >\n                  <i className=\"mi-globe me-2\"></i>\n                  {lang.toUpperCase()}\n                  {lang === \"en\" && (\n                    <span className=\"ms-1 small\">(Required)</span>\n                  )}\n                </button>\n              ))}\n            </div>\n\n            {/* Content for Active Language */}\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-edit me-2\"></i>\n                  Title ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.title || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"title\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Enter blog post title\"\n                  required={activeLanguage === \"en\"}\n                />\n                <small className=\"form-text text-muted\">\n                  The main title of your blog post in{\" \"}\n                  {activeLanguage.toUpperCase()}\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-text me-2\"></i>\n                  Excerpt ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.excerpt || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"excerpt\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"Brief description of the blog post\"\n                />\n                <small className=\"form-text text-muted\">\n                  A short summary that will appear in blog listings and social\n                  media previews\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:document-text-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Content ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <TipTapEditor\n                  content={formData.translations[activeLanguage]?.content || \"\"}\n                  onChange={(html) =>\n                    handleTranslationChange(activeLanguage, \"content\", html)\n                  }\n                  placeholder=\"Write your blog post content here. You can paste formatted text and code snippets with syntax highlighting.\"\n                />\n                <small className=\"form-text text-muted\">\n                  <iconify-icon\n                    icon=\"solar:info-circle-bold\"\n                    className=\"me-1\"\n                  ></iconify-icon>\n                  Rich text editor with syntax highlighting. Paste code snippets\n                  and they will be automatically highlighted. Use the toolbar\n                  for formatting options.\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Title ({activeLanguage.toUpperCase()})\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.metaTitle || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaTitle\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"SEO title (optional)\"\n                  maxLength=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-search me-1\"></i>\n                  Title that appears in search engine results (max 60\n                  characters)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-description me-2\"></i>\n                  Meta Description ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.metaDesc || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaDesc\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"SEO description (optional)\"\n                  maxLength=\"160\"\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-search me-1\"></i>\n                  Description that appears in search engine results (max 160\n                  characters)\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Categories and Tags */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:tag-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Categories & Tags\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Organize your blog post with categories and tags\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:folder-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Categories\n                </label>\n                <div className=\"categories-grid\">\n                  {categories && categories.length > 0 ? (\n                    categories.map((category) => (\n                      <div key={category.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`category-${category.id}`}\n                          checked={formData.categoryIds.includes(category.id)}\n                          onChange={() => {\n                            const newCategoryIds =\n                              formData.categoryIds.includes(category.id)\n                                ? formData.categoryIds.filter(\n                                    (id) => id !== category.id\n                                  )\n                                : [...formData.categoryIds, category.id];\n                            setFormData((prev) => ({\n                              ...prev,\n                              categoryIds: newCategoryIds,\n                            }));\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`category-${category.id}`}\n                        >\n                          {category.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No categories available</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:hashtag-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Tags\n                </label>\n                <div className=\"tags-grid\">\n                  {tags && tags.length > 0 ? (\n                    tags.map((tag) => (\n                      <div key={tag.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`tag-${tag.id}`}\n                          checked={formData.tagIds.includes(tag.id)}\n                          onChange={() => {\n                            const newTagIds = formData.tagIds.includes(tag.id)\n                              ? formData.tagIds.filter((id) => id !== tag.id)\n                              : [...formData.tagIds, tag.id];\n                            setFormData((prev) => ({\n                              ...prev,\n                              tagIds: newTagIds,\n                            }));\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`tag-${tag.id}`}\n                        >\n                          {tag.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No tags available</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"row mt-40\">\n            <div className=\"col-12 text-end\">\n              <button\n                type=\"button\"\n                onClick={() => navigate(\"/admin/posts\")}\n                className=\"btn btn-mod btn-gray btn-round me-3\"\n              >\n                Cancel\n              </button>\n\n              <button\n                type=\"submit\"\n                disabled={saving}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                {saving ? (\n                  <>\n                    <i className=\"fa fa-spinner fa-spin me-2\"></i>\n                    Saving...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"mi-check me-2\"></i>\n                    {isEditing ? \"Update Post\" : \"Create Post\"}\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </form>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminBlogEditor;\n", "// client/src/pages/AdminProducts.jsx\n\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminProducts = () => {\n  const navigate = useNavigate();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 10,\n    status: \"all\",\n    search: \"\",\n  });\n  const [pagination, setPagination] = useState({});\n\n  useEffect(() => {\n    loadProducts();\n  }, [filters]);\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n\n      const params = {};\n      Object.entries(filters).forEach(([key, value]) => {\n        if (value && value !== \"all\") {\n          params[key] = value;\n        }\n      });\n\n      const { response, data } = await adminAPI.getProducts(params);\n\n      if (data.success) {\n        setProducts(data.data?.products || data.products || []);\n        setPagination(data.data?.pagination || {});\n      } else {\n        setError(data.message || \"Failed to load products\");\n      }\n    } catch (error) {\n      console.error(\"Load products error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Are you sure you want to delete this product?\")) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteProduct(id);\n\n      if (response.ok && data.success) {\n        setProducts(products.filter((product) => product.id !== id));\n      } else {\n        setError(data.message || \"Failed to delete product\");\n      }\n    } catch (error) {\n      console.error(\"Delete product error:\", error);\n      setError(\"Failed to delete product\");\n    }\n  };\n\n  const handleToggleVisibility = async (id) => {\n    try {\n      const product = products.find((p) => p.id === id);\n      const newStatus = product.status === \"published\" ? \"draft\" : \"published\";\n\n      const { response, data } = await adminAPI.updateProduct(id, {\n        status: newStatus,\n      });\n\n      if (response.ok && data.success) {\n        setProducts(\n          products.map((p) => (p.id === id ? { ...p, status: newStatus } : p))\n        );\n      } else {\n        setError(data.message || \"Failed to update product status\");\n      }\n    } catch (error) {\n      console.error(\"Toggle visibility error:\", error);\n      setError(\"Failed to update product status\");\n    }\n  };\n\n  const handleFilterChange = (key, value) => {\n    setFilters((prev) => ({\n      ...prev,\n      [key]: value,\n      page: 1, // Reset to first page when filtering\n    }));\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n    });\n  };\n\n  // Utility function to construct image URLs\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/product-images/${filename}`;\n  };\n\n  // Get display image from product images array\n  const getDisplayImage = (product) => {\n    if (product.images && product.images.length > 0) {\n      const displayImage =\n        product.images.find((img) => img.isDisplay) || product.images[0];\n      return displayImage.filename;\n    }\n    return product.featuredImage;\n  };\n\n  const getStatusBadge = (product) => {\n    if (product.status === \"draft\") {\n      return (\n        <span className=\"badge bg-secondary\">\n          <iconify-icon\n            icon=\"solar:document-text-bold\"\n            className=\"me-1\"\n          ></iconify-icon>\n          Draft\n        </span>\n      );\n    }\n\n    return (\n      <span className=\"badge bg-success\">\n        <iconify-icon\n          icon=\"solar:check-circle-bold\"\n          className=\"me-1\"\n        ></iconify-icon>\n        Published\n      </span>\n    );\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Products - Admin\"\n        description=\"Manage products in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Products\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Manage your webstore products, create new items, and organize\n                your catalog.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={() => navigate(\"/admin/products/new\")}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Product\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"admin-table mb-30\" style={{ padding: \"15px 20px\" }}>\n          <div className=\"row g-3\">\n            <div className=\"col-12 col-md-6 col-lg-4\">\n              <label className=\"form-label\">Search Products</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                placeholder=\"Search by title or slug...\"\n                value={filters.search}\n                onChange={(e) => handleFilterChange(\"search\", e.target.value)}\n              />\n            </div>\n\n            <div className=\"col-12 col-md-6 col-lg-4\">\n              <label className=\"form-label\">Status</label>\n              <select\n                className=\"form-control\"\n                value={filters.status}\n                onChange={(e) => handleFilterChange(\"status\", e.target.value)}\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"published\">Published</option>\n                <option value=\"draft\">Draft</option>\n              </select>\n            </div>\n\n            <div className=\"col-12 col-lg-4 d-flex align-items-end\">\n              <button\n                onClick={() =>\n                  setFilters({\n                    page: 1,\n                    limit: 10,\n                    status: \"all\",\n                    search: \"\",\n                  })\n                }\n                className=\"btn btn-mod btn-border btn-round w-100\"\n              >\n                <iconify-icon\n                  icon=\"solar:refresh-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Reset Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {/* Products Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">\n                Loading products...\n              </div>\n            </div>\n          ) : products.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:shop-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No products found\n              </div>\n              <p className=\"section-descr mb-30\">\n                {filters.search || filters.status !== \"all\"\n                  ? \"Try adjusting your search filters or create your first product.\"\n                  : \"Get started by creating your first product.\"}\n              </p>\n              <button\n                onClick={() => navigate(\"/admin/products/new\")}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Product\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Product</th>\n                        <th>Status</th>\n                        <th>Pricing</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {products.map((product) => {\n                        const englishTranslation =\n                          product.translations?.find(\n                            (t) => t.language === \"en\"\n                          ) || product.translations?.[0];\n\n                        return (\n                          <tr key={product.id}>\n                            <td>\n                              <div className=\"d-flex align-items-center\">\n                                {getDisplayImage(product) && (\n                                  <img\n                                    className=\"rounded me-3\"\n                                    src={getImageUrl(getDisplayImage(product))}\n                                    alt=\"\"\n                                    style={{\n                                      width: \"50px\",\n                                      height: \"50px\",\n                                      objectFit: \"cover\",\n                                    }}\n                                    onError={(e) => {\n                                      e.target.style.display = \"none\";\n                                    }}\n                                  />\n                                )}\n                                <div>\n                                  <div className=\"fw-bold\">\n                                    {englishTranslation?.title ||\n                                      product.title ||\n                                      \"Untitled\"}\n                                  </div>\n                                  <small className=\"text-muted\">\n                                    /{product.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </td>\n                            <td>{getStatusBadge(product)}</td>\n                            <td>\n                              <div>\n                                {product.whitelabelPrice && (\n                                  <div className=\"small\">\n                                    <strong>Whitelabel:</strong> €\n                                    {product.whitelabelPrice}\n                                  </div>\n                                )}\n                                {product.subscriptionPrice && (\n                                  <div className=\"small\">\n                                    <strong>Subscription:</strong> €\n                                    {product.subscriptionPrice}/mo\n                                  </div>\n                                )}\n                                {!product.whitelabelPrice &&\n                                  !product.subscriptionPrice && (\n                                    <span className=\"text-muted\">\n                                      No pricing set\n                                    </span>\n                                  )}\n                              </div>\n                            </td>\n                            <td>{formatDate(product.createdAt)}</td>\n                            <td>\n                              <div className=\"btn-group\" role=\"group\">\n                                <button\n                                  onClick={() =>\n                                    navigate(\n                                      `/admin/products/edit/${product.id}`\n                                    )\n                                  }\n                                  className=\"btn btn-sm btn-outline-primary\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() =>\n                                    handleToggleVisibility(product.id)\n                                  }\n                                  className={`btn btn-sm ${\n                                    product.status === \"published\"\n                                      ? \"btn-outline-warning\"\n                                      : \"btn-outline-success\"\n                                  }`}\n                                  title={\n                                    product.status === \"published\"\n                                      ? \"Unpublish\"\n                                      : \"Publish\"\n                                  }\n                                >\n                                  <iconify-icon\n                                    icon={\n                                      product.status === \"published\"\n                                        ? \"solar:eye-closed-bold\"\n                                        : \"solar:eye-bold\"\n                                    }\n                                  ></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(product.id)}\n                                  className=\"btn btn-sm btn-outline-danger\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {products.map((product) => {\n                    const englishTranslation =\n                      product.translations?.find((t) => t.language === \"en\") ||\n                      product.translations?.[0];\n\n                    return (\n                      <div key={product.id} className=\"col-12\">\n                        <div className=\"card border-0 shadow-sm\">\n                          <div className=\"card-body p-3\">\n                            <div className=\"row align-items-center\">\n                              <div className=\"col-12 mb-2\">\n                                <div className=\"d-flex align-items-center\">\n                                  {getDisplayImage(product) && (\n                                    <img\n                                      className=\"rounded me-3\"\n                                      src={getImageUrl(\n                                        getDisplayImage(product)\n                                      )}\n                                      alt=\"\"\n                                      style={{\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        objectFit: \"cover\",\n                                      }}\n                                      onError={(e) => {\n                                        e.target.style.display = \"none\";\n                                      }}\n                                    />\n                                  )}\n                                  <div className=\"flex-grow-1\">\n                                    <h6 className=\"mb-1 fw-bold\">\n                                      {englishTranslation?.title ||\n                                        product.title ||\n                                        \"Untitled\"}\n                                    </h6>\n                                    <small className=\"text-muted\">\n                                      /{product.slug}\n                                    </small>\n                                  </div>\n                                </div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Status\n                                </small>\n                                <div>{getStatusBadge(product)}</div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Pricing\n                                </small>\n                                <div>\n                                  {product.whitelabelPrice && (\n                                    <div className=\"small\">\n                                      <strong>WL:</strong> €\n                                      {product.whitelabelPrice}\n                                    </div>\n                                  )}\n                                  {product.subscriptionPrice && (\n                                    <div className=\"small\">\n                                      <strong>Sub:</strong> €\n                                      {product.subscriptionPrice}/mo\n                                    </div>\n                                  )}\n                                  {!product.whitelabelPrice &&\n                                    !product.subscriptionPrice && (\n                                      <span className=\"text-muted small\">\n                                        No pricing\n                                      </span>\n                                    )}\n                                </div>\n                              </div>\n\n                              <div className=\"col-12 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Created\n                                </small>\n                                <small>{formatDate(product.createdAt)}</small>\n                              </div>\n\n                              <div className=\"col-12\">\n                                <div className=\"d-flex gap-2 flex-wrap\">\n                                  <button\n                                    onClick={() =>\n                                      navigate(\n                                        `/admin/products/edit/${product.id}`\n                                      )\n                                    }\n                                    className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                    title=\"Edit\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:pen-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Edit\n                                  </button>\n\n                                  <button\n                                    onClick={() =>\n                                      handleToggleVisibility(product.id)\n                                    }\n                                    className={`btn btn-sm flex-fill ${\n                                      product.status === \"published\"\n                                        ? \"btn-outline-warning\"\n                                        : \"btn-outline-success\"\n                                    }`}\n                                    title={\n                                      product.status === \"published\"\n                                        ? \"Unpublish\"\n                                        : \"Publish\"\n                                    }\n                                  >\n                                    <iconify-icon\n                                      icon={\n                                        product.status === \"published\"\n                                          ? \"solar:eye-closed-bold\"\n                                          : \"solar:eye-bold\"\n                                      }\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    {product.status === \"published\"\n                                      ? \"Hide\"\n                                      : \"Show\"}\n                                  </button>\n\n                                  <button\n                                    onClick={() => handleDelete(product.id)}\n                                    className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                    title=\"Delete\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:trash-bin-trash-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Delete\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {pagination.pages > 1 && (\n          <div className=\"row mt-30 align-items-center\">\n            <div className=\"col-12 col-md-6 mb-3 mb-md-0\">\n              <p className=\"small text-muted mb-0 text-center text-md-start\">\n                Showing {(pagination.page - 1) * pagination.limit + 1} to{\" \"}\n                {Math.min(pagination.page * pagination.limit, pagination.total)}{\" \"}\n                of {pagination.total} results\n              </p>\n            </div>\n            <div className=\"col-12 col-md-6\">\n              <nav aria-label=\"Products pagination\">\n                <ul className=\"pagination pagination-sm justify-content-center justify-content-md-end mb-0\">\n                  <li\n                    className={`page-item ${\n                      pagination.page <= 1 ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))\n                      }\n                      disabled={pagination.page <= 1}\n                    >\n                      Previous\n                    </button>\n                  </li>\n\n                  <li className=\"page-item active\">\n                    <span className=\"page-link\">\n                      Page {pagination.page} of {pagination.pages}\n                    </span>\n                  </li>\n\n                  <li\n                    className={`page-item ${\n                      pagination.page >= pagination.pages ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))\n                      }\n                      disabled={pagination.page >= pagination.pages}\n                    >\n                      Next\n                    </button>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminProducts;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport TipTapEditor from \"../components/editor/TipTapEditor\";\nimport { adminAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminProductEditor = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { id } = useParams(); // For editing existing products\n  const isEditing = Boolean(id);\n\n  // Utility function to construct image URLs\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/product-images/${filename}`;\n  };\n\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  // Available languages from i18n (memoized to prevent infinite re-renders)\n  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));\n\n  // Form state\n  const [formData, setFormData] = useState(() => {\n    // Initialize translations for all available languages\n    const initialTranslations = {};\n    availableLanguages.forEach((lang) => {\n      initialTranslations[lang] = {\n        title: \"\",\n        excerpt: \"\",\n        content: \"\",\n        metaTitle: \"\",\n        metaDesc: \"\",\n        keywords: [],\n      };\n    });\n\n    return {\n      slug: \"\",\n      whitelabelPrice: \"\",\n      subscriptionPrice: \"\",\n      demoUrl: \"\",\n      status: \"draft\",\n      featuredImage: null,\n      featuredImageAlt: \"\",\n      images: [], // Array of image objects\n      categoryIds: [],\n      tagIds: [],\n      translations: initialTranslations,\n    };\n  });\n\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [activeLanguage, setActiveLanguage] = useState(\"en\");\n  const [imagePreview, setImagePreview] = useState(null);\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [displayImageIndex, setDisplayImageIndex] = useState(0);\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Load categories and tags\n        const [categoriesResult, tagsResult] = await Promise.all([\n          adminAPI.getCategories(),\n          adminAPI.getTags(),\n        ]);\n\n        if (categoriesResult.data?.success) {\n          setCategories(categoriesResult.data.data);\n        }\n\n        if (tagsResult.data?.success) {\n          setTags(tagsResult.data.data);\n        }\n\n        // Load existing product if editing\n        if (isEditing && id) {\n          console.log(\"Loading product with ID:\", id);\n          console.log(\"Calling adminAPI.getProduct...\");\n          const productResult = await adminAPI.getProduct(id);\n          console.log(\"Product API response:\", productResult);\n\n          if (productResult.data?.success) {\n            const product = productResult.data.product;\n            console.log(\"Product data:\", product);\n\n            // Set basic fields\n            setFormData((prev) => ({\n              ...prev,\n              slug: product.slug || \"\",\n              whitelabelPrice: product.whitelabelPrice || \"\",\n              subscriptionPrice: product.subscriptionPrice || \"\",\n              demoUrl: product.demoUrl || \"\",\n              status: product.status || \"draft\",\n              featuredImageAlt: product.featuredImageAlt || \"\",\n              categoryIds: product.categories?.map((c) => c.categoryId) || [],\n              tagIds: product.tags?.map((t) => t.tagId) || [],\n            }));\n\n            // Handle translations - products have translations array, not single language\n            const updatedTranslations = { ...formData.translations };\n            if (product.translations && Array.isArray(product.translations)) {\n              product.translations.forEach((translation) => {\n                updatedTranslations[translation.language] = {\n                  title: translation.title || \"\",\n                  excerpt: translation.excerpt || \"\",\n                  content: translation.content || \"\",\n                  metaTitle: translation.metaTitle || \"\",\n                  metaDesc: translation.metaDesc || \"\",\n                  keywords: translation.keywords || [],\n                };\n              });\n            }\n\n            setFormData((prev) => ({\n              ...prev,\n              translations: updatedTranslations,\n            }));\n\n            // Load existing images\n            if (product.images && Array.isArray(product.images)) {\n              console.log(\"Loading existing images:\", product.images);\n              const existingImages = product.images.map((img, index) => {\n                const imageUrl = getImageUrl(img.filename);\n                console.log(`Image ${index}: ${img.filename} -> ${imageUrl}`);\n                return {\n                  id: img.id,\n                  file: null, // No file for existing images\n                  preview: imageUrl,\n                  alt: img.alt || \"\",\n                  isDisplay: img.isDisplay,\n                  filename: img.filename, // Store filename for existing images\n                  sortOrder: img.sortOrder,\n                };\n              });\n              console.log(\"Processed existing images:\", existingImages);\n              setSelectedImages(existingImages);\n\n              // Set display image index\n              const displayIndex = existingImages.findIndex(\n                (img) => img.isDisplay\n              );\n              if (displayIndex !== -1) {\n                setDisplayImageIndex(displayIndex);\n              }\n            } else {\n              console.log(\"No images found in product:\", product.images);\n            }\n\n            // Set legacy featured image preview if exists (fallback)\n            if (\n              product.featuredImage &&\n              (!product.images || product.images.length === 0)\n            ) {\n              setImagePreview(getImageUrl(product.featuredImage));\n            }\n          } else {\n            console.error(\"Failed to load product:\", productResult);\n            setError(\"Failed to load product data\");\n          }\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n        setError(\"Failed to load data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [id, isEditing]);\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleTranslationChange = (language, field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      translations: {\n        ...prev.translations,\n        [language]: {\n          ...prev.translations[language],\n          [field]: value,\n        },\n      },\n    }));\n  };\n\n  const handleImagesChange = (e) => {\n    const files = Array.from(e.target.files);\n    if (files.length === 0) return;\n\n    // Check if total images would exceed 10\n    if (selectedImages.length + files.length > 10) {\n      setError(\"Maximum 10 images allowed per product\");\n      return;\n    }\n\n    const newImages = files.map((file, index) => ({\n      file,\n      preview: URL.createObjectURL(file),\n      alt: \"\",\n      isDisplay: selectedImages.length === 0 && index === 0, // First image of first upload is display\n    }));\n\n    setSelectedImages((prev) => [...prev, ...newImages]);\n    setError(\"\"); // Clear any previous errors\n  };\n\n  const removeImage = (index) => {\n    setSelectedImages((prev) => {\n      const updated = prev.filter((_, i) => i !== index);\n      // If we removed the display image, make the first image the display\n      if (prev[index]?.isDisplay && updated.length > 0) {\n        updated[0].isDisplay = true;\n        setDisplayImageIndex(0);\n      }\n      return updated;\n    });\n  };\n\n  const setDisplayImage = (index) => {\n    setSelectedImages((prev) =>\n      prev.map((img, i) => ({\n        ...img,\n        isDisplay: i === index,\n      }))\n    );\n    setDisplayImageIndex(index);\n  };\n\n  const updateImageAlt = (index, alt) => {\n    setSelectedImages((prev) =>\n      prev.map((img, i) => (i === index ? { ...img, alt } : img))\n    );\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const formDataToSend = new FormData();\n\n      // Add basic fields\n      formDataToSend.append(\"slug\", formData.slug);\n      formDataToSend.append(\"whitelabelPrice\", formData.whitelabelPrice);\n      formDataToSend.append(\"subscriptionPrice\", formData.subscriptionPrice);\n      formDataToSend.append(\"demoUrl\", formData.demoUrl);\n      formDataToSend.append(\"status\", formData.status);\n      formDataToSend.append(\"featuredImageAlt\", formData.featuredImageAlt);\n\n      // Add arrays\n      formDataToSend.append(\n        \"categoryIds\",\n        JSON.stringify(formData.categoryIds)\n      );\n      formDataToSend.append(\"tagIds\", JSON.stringify(formData.tagIds));\n      formDataToSend.append(\n        \"translations\",\n        JSON.stringify(formData.translations)\n      );\n\n      // Add images if selected\n      let imageIndex = 0;\n      selectedImages.forEach((imageData) => {\n        // Only append new images (with file), existing images are handled separately\n        if (imageData.file) {\n          formDataToSend.append(\"images\", imageData.file);\n          formDataToSend.append(`imageAlt_${imageIndex}`, imageData.alt);\n          formDataToSend.append(`isDisplay_${imageIndex}`, imageData.isDisplay);\n          imageIndex++;\n        }\n      });\n\n      // Send existing images data separately\n      const existingImages = selectedImages\n        .filter((img) => !img.file && img.id)\n        .map((img) => ({\n          id: img.id,\n          alt: img.alt,\n          isDisplay: img.isDisplay,\n          sortOrder: img.sortOrder,\n        }));\n\n      if (existingImages.length > 0) {\n        formDataToSend.append(\"existingImages\", JSON.stringify(existingImages));\n      }\n\n      // Use the proper API utility\n      let result;\n      if (isEditing) {\n        result = await adminAPI.updateProduct(id, formDataToSend);\n      } else {\n        result = await adminAPI.createProduct(formDataToSend);\n      }\n\n      const { response, data } = result;\n\n      if (response.ok && data && data.success) {\n        setSuccess(\n          `Product ${isEditing ? \"updated\" : \"created\"} successfully!`\n        );\n        setTimeout(() => {\n          navigate(\"/admin/products\");\n        }, 2000);\n      } else {\n        const errorMessage =\n          data?.message ||\n          `Failed to ${isEditing ? \"update\" : \"create\"} product`;\n        setError(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Error saving product:\", error);\n      setError(`Failed to ${isEditing ? \"update\" : \"create\"} product`);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div\n          className=\"d-flex justify-content-center align-items-center\"\n          style={{ minHeight: \"400px\" }}\n        >\n          <div className=\"text-center\">\n            <div className=\"spinner-border text-primary mb-3\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            <p className=\"text-muted\">Loading product data...</p>\n          </div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <>\n      <SEO\n        title={`${isEditing ? \"Edit\" : \"Create\"} Product | Admin`}\n        description=\"Create and manage products in the webstore\"\n      />\n      <AdminLayout title={isEditing ? \"Edit Product\" : \"Create New Product\"}>\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:danger-triangle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"alert alert-success mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:check-circle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {success}\n            </div>\n          )}\n\n          {/* Basic Settings */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:settings-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Basic Settings\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Configure the basic properties of your product\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Slug (URL)\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.slug}\n                  onChange={(e) => handleInputChange(\"slug\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"product-url-slug\"\n                />\n                <small className=\"form-text text-muted\">\n                  This will be the URL path for your product (e.g.,\n                  /webstore/your-slug)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:check-circle-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Publication Status\n                </label>\n                <select\n                  value={formData.status}\n                  onChange={(e) => handleInputChange(\"status\", e.target.value)}\n                  className=\"form-control\"\n                >\n                  <option value=\"draft\">Draft</option>\n                  <option value=\"published\">Published</option>\n                </select>\n                <small className=\"form-text text-muted\">\n                  Draft products are not visible to the public\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Pricing Information */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:dollar-minimalistic-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Pricing & Demo\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Set pricing options and demo URL for your product\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-4 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:code-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Whitelabel Price (EUR)\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={formData.whitelabelPrice}\n                  onChange={(e) =>\n                    handleInputChange(\"whitelabelPrice\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"2999.99\"\n                />\n                <small className=\"form-text text-muted\">\n                  Price for purchasing the source code with commercial license\n                </small>\n              </div>\n\n              <div className=\"col-md-4 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:refresh-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Subscription Price (EUR/month)\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={formData.subscriptionPrice}\n                  onChange={(e) =>\n                    handleInputChange(\"subscriptionPrice\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"99.99\"\n                />\n                <small className=\"form-text text-muted\">\n                  Monthly subscription price for SaaS access\n                </small>\n              </div>\n\n              <div className=\"col-md-4 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:eye-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Demo URL\n                </label>\n                <input\n                  type=\"url\"\n                  value={formData.demoUrl}\n                  onChange={(e) => handleInputChange(\"demoUrl\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"https://demo.example.com\"\n                />\n                <small className=\"form-text text-muted\">\n                  Link to live demo of the product\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Featured Image */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:gallery-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Featured Image\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Upload a featured image that will be displayed with your\n                  product\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:upload-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Upload Images (Max 10)\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  multiple\n                  onChange={handleImagesChange}\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Recommended size: 800x600px. Max file size: 5MB per image.\n                  Maximum 10 images.\n                </small>\n              </div>\n\n              {selectedImages.length > 0 && (\n                <div className=\"col-12 mb-30\">\n                  <h6 className=\"mb-3\">\n                    Product Images ({selectedImages.length}/10)\n                  </h6>\n                  <div className=\"row\">\n                    {selectedImages.map((imageData, index) => (\n                      <div key={index} className=\"col-md-4 mb-3\">\n                        <div className=\"card\">\n                          <div className=\"position-relative\">\n                            <img\n                              src={imageData.preview}\n                              alt={`Preview ${index + 1}`}\n                              className=\"card-img-top\"\n                              style={{ height: \"200px\", objectFit: \"cover\" }}\n                              onError={(e) => {\n                                console.error(\n                                  \"Failed to load image:\",\n                                  imageData.preview\n                                );\n                                e.target.style.display = \"none\";\n                              }}\n                              onLoad={() => {\n                                console.log(\n                                  \"Successfully loaded image:\",\n                                  imageData.preview\n                                );\n                              }}\n                            />\n                            <button\n                              type=\"button\"\n                              className=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-2\"\n                              onClick={() => removeImage(index)}\n                            >\n                              ×\n                            </button>\n                            {imageData.isDisplay && (\n                              <span className=\"badge bg-primary position-absolute top-0 start-0 m-2\">\n                                Display Image\n                              </span>\n                            )}\n                          </div>\n                          <div className=\"card-body\">\n                            <div className=\"mb-2\">\n                              <input\n                                type=\"text\"\n                                className=\"form-control form-control-sm\"\n                                placeholder=\"Alt text\"\n                                value={imageData.alt}\n                                onChange={(e) =>\n                                  updateImageAlt(index, e.target.value)\n                                }\n                              />\n                            </div>\n                            <div className=\"d-flex gap-2\">\n                              {!imageData.isDisplay && (\n                                <button\n                                  type=\"button\"\n                                  className=\"btn btn-outline-primary btn-sm\"\n                                  onClick={() => setDisplayImage(index)}\n                                >\n                                  Set as Display\n                                </button>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Language Tabs */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <i className=\"mi-globe me-2 color-primary-1\"></i>\n                  Content (Multi-language)\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Create content in multiple languages. At least English content\n                  is required.\n                </p>\n              </div>\n            </div>\n\n            {/* Language Selector */}\n            <div className=\"language-tabs mb-30\">\n              {availableLanguages.map((lang) => (\n                <button\n                  key={lang}\n                  type=\"button\"\n                  onClick={() => setActiveLanguage(lang)}\n                  className={`language-tab ${\n                    activeLanguage === lang ? \"active\" : \"\"\n                  }`}\n                >\n                  <i className=\"mi-globe me-2\"></i>\n                  {lang.toUpperCase()}\n                  {lang === \"en\" && (\n                    <span className=\"ms-1 small\">(Required)</span>\n                  )}\n                </button>\n              ))}\n            </div>\n\n            {/* Content for Active Language */}\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-edit me-2\"></i>\n                  Title ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.title || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"title\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Enter product title\"\n                  required={activeLanguage === \"en\"}\n                />\n                <small className=\"form-text text-muted\">\n                  The main title of your product in{\" \"}\n                  {activeLanguage.toUpperCase()}\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-text me-2\"></i>\n                  Excerpt ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.excerpt || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"excerpt\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"Brief description of the product\"\n                />\n                <small className=\"form-text text-muted\">\n                  A short summary that will appear in product listings and\n                  social media previews\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:document-text-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Content ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <TipTapEditor\n                  content={formData.translations[activeLanguage]?.content || \"\"}\n                  onChange={(html) =>\n                    handleTranslationChange(activeLanguage, \"content\", html)\n                  }\n                  placeholder=\"Write your product description here. You can paste formatted text and code snippets with syntax highlighting.\"\n                />\n                <small className=\"form-text text-muted\">\n                  <iconify-icon\n                    icon=\"solar:info-circle-bold\"\n                    className=\"me-1\"\n                  ></iconify-icon>\n                  Rich text editor with syntax highlighting. Paste code snippets\n                  and they will be automatically highlighted. Use the toolbar\n                  for formatting options.\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Title ({activeLanguage.toUpperCase()})\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.metaTitle || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaTitle\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"SEO title (optional)\"\n                  maxLength=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  SEO title for search engines (max 60 characters)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Description ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.metaDesc || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaDesc\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"SEO description (optional)\"\n                  maxLength=\"160\"\n                />\n                <small className=\"form-text text-muted\">\n                  SEO description for search engines (max 160 characters)\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Categories and Tags */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:tag-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Categories & Tags\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Organize your product with categories and tags\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:folder-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Categories\n                </label>\n                <div className=\"categories-grid\">\n                  {categories && categories.length > 0 ? (\n                    categories.map((category) => (\n                      <div key={category.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`category-${category.id}`}\n                          checked={formData.categoryIds.includes(category.id)}\n                          onChange={() => {\n                            const newCategoryIds =\n                              formData.categoryIds.includes(category.id)\n                                ? formData.categoryIds.filter(\n                                    (id) => id !== category.id\n                                  )\n                                : [...formData.categoryIds, category.id];\n                            handleInputChange(\"categoryIds\", newCategoryIds);\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`category-${category.id}`}\n                        >\n                          {category.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No categories available</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:hashtag-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Tags\n                </label>\n                <div className=\"tags-grid\">\n                  {tags && tags.length > 0 ? (\n                    tags.map((tag) => (\n                      <div key={tag.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`tag-${tag.id}`}\n                          checked={formData.tagIds.includes(tag.id)}\n                          onChange={() => {\n                            const newTagIds = formData.tagIds.includes(tag.id)\n                              ? formData.tagIds.filter((id) => id !== tag.id)\n                              : [...formData.tagIds, tag.id];\n                            handleInputChange(\"tagIds\", newTagIds);\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`tag-${tag.id}`}\n                        >\n                          {tag.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No tags available</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"row\">\n            <div className=\"col-12 text-center\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-mod btn-color btn-large btn-round\"\n                disabled={saving}\n              >\n                {saving ? (\n                  <>\n                    <span className=\"spinner-border spinner-border-sm me-2\"></span>\n                    {isEditing ? \"Updating...\" : \"Creating...\"}\n                  </>\n                ) : (\n                  <>\n                    <i className=\"mi-check me-2\"></i>\n                    {isEditing ? \"Update Product\" : \"Create Product\"}\n                  </>\n                )}\n              </button>\n              <div className=\"mt-3\">\n                <small className=\"text-muted\">\n                  <i className=\"mi-info me-1\"></i>\n                  {isEditing\n                    ? \"Changes will be saved and the product will be updated\"\n                    : \"The product will be created and added to your webstore\"}\n                </small>\n              </div>\n            </div>\n          </div>\n        </form>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminProductEditor;\n", "// client/src/pages/AdminBlogAnalytics.jsx\n\nimport React, { useState, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport TimeRangeSelector from \"../components/analytics/TimeRangeSelector\";\nimport LanguageSelector from \"../components/analytics/LanguageSelector\";\nimport AnalyticsOverview from \"../components/analytics/AnalyticsOverview\";\nimport AnalyticsChart from \"../components/analytics/AnalyticsChart\";\nimport HeatmapChart from \"../components/analytics/HeatmapChart\";\nimport PostsTable from \"../components/analytics/PostsTable\";\nimport ConversionAnalytics from \"../components/analytics/ConversionAnalytics\";\nimport StaticPagesAnalytics from \"../components/analytics/StaticPagesAnalytics\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminBlogAnalytics = () => {\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [timeRange, setTimeRange] = useState(\"last30days\");\n  const [selectedLanguage, setSelectedLanguage] = useState(\"all\");\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [postsData, setPostsData] = useState([]);\n\n  // Time range options\n  const timeRangeOptions = [\n    { value: \"lastday\", label: \"Last day\" },\n    { value: \"lastweek\", label: \"Last week\" },\n    { value: \"last14days\", label: \"Last 14 days\" },\n    { value: \"last30days\", label: \"Last 30 days\" },\n    { value: \"last2months\", label: \"Last 2 months\" },\n    { value: \"last4months\", label: \"Last 4 months\" },\n    { value: \"last6months\", label: \"Last 6 months\" },\n  ];\n\n  // Language options for analytics\n  const languageOptions = [\n    { value: \"all\", label: \"All languages\", flag: \"🌐\" },\n    { value: \"en\", label: \"English\", flag: \"🇬🇧\" },\n    { value: \"et\", label: \"Estonian\", flag: \"🇪🇪\" },\n    { value: \"fi\", label: \"Finnish\", flag: \"🇫🇮\" },\n    { value: \"de\", label: \"German\", flag: \"🇩🇪\" },\n    { value: \"sv\", label: \"Swedish\", flag: \"🇸🇪\" },\n  ];\n\n  // Load analytics data\n  useEffect(() => {\n    const loadAnalyticsData = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n\n        // Check if user is authenticated\n        const token = localStorage.getItem(\"adminToken\");\n        if (!token) {\n          setError(\n            \"Authentication required. Please log in to access this page.\"\n          );\n          setLoading(false);\n          return;\n        }\n\n        // Load analytics data\n        const [analyticsResult, postsResult] = await Promise.all([\n          adminAPI.getBlogAnalytics(timeRange, selectedLanguage),\n          adminAPI.getBlogPostsAnalytics(timeRange, selectedLanguage),\n        ]);\n\n        // Handle analytics response\n        if (analyticsResult.response.ok && analyticsResult.data) {\n          setAnalyticsData(analyticsResult.data.data || analyticsResult.data);\n        } else {\n          console.error(\n            \"Analytics API failed:\",\n            analyticsResult.response.status,\n            analyticsResult.response.statusText\n          );\n          if (\n            analyticsResult.response.status === 401 ||\n            analyticsResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setError(\"Failed to load analytics data\");\n        }\n\n        // Handle posts response\n        if (postsResult.response.ok && postsResult.data) {\n          setPostsData(postsResult.data.data || postsResult.data);\n        } else {\n          console.error(\n            \"Posts analytics API failed:\",\n            postsResult.response.status,\n            postsResult.response.statusText\n          );\n          setPostsData([]);\n        }\n      } catch (error) {\n        console.error(\"Error loading analytics data:\", error);\n        if (error.message && error.message.includes(\"fetch\")) {\n          setError(\n            \"Failed to connect to the server. Please check if the backend is running.\"\n          );\n        } else {\n          setError(\"Failed to load analytics data. Please try again.\");\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadAnalyticsData();\n  }, [timeRange, selectedLanguage]);\n\n  const handleTimeRangeChange = (newTimeRange) => {\n    setTimeRange(newTimeRange);\n  };\n\n  const handleLanguageChange = (newLanguage) => {\n    setSelectedLanguage(newLanguage);\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout title=\"Blog Analytics\">\n        <SEO\n          title=\"Blog Analytics - Admin\"\n          description=\"Blog analytics and performance metrics\"\n        />\n        <div\n          className=\"d-flex justify-content-center align-items-center\"\n          style={{ minHeight: \"400px\" }}\n        >\n          <div className=\"spinner-border text-primary\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout title=\"Analytics Dashboard\">\n      <SEO\n        title=\"Analytics Dashboard - Admin\"\n        description=\"Blog analytics, conversion tracking, and page performance metrics\"\n      />\n\n      <div className=\"admin-content\">\n        <div className=\"admin-header mb-4\">\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h1 className=\"admin-title mb-2\">Analytics Dashboard</h1>\n              <p className=\"admin-subtitle text-muted mb-0\">\n                Track and analyze your blog performance, conversions, and page\n                analytics.{\" \"}\n                <a\n                  href=\"https://developers.google.com/analytics/devguides/collection/ga4\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-primary\"\n                >\n                  Learn more\n                </a>\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"alert alert-danger mb-4\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {/* Time Range and Language Selectors */}\n        <div className=\"row mb-4\">\n          <div className=\"col-md-6\">\n            <TimeRangeSelector\n              options={timeRangeOptions}\n              value={timeRange}\n              onChange={handleTimeRangeChange}\n              comparedPeriod={analyticsData?.comparedPeriod}\n            />\n          </div>\n          <div className=\"col-md-6\">\n            <LanguageSelector\n              options={languageOptions}\n              value={selectedLanguage}\n              onChange={handleLanguageChange}\n            />\n          </div>\n        </div>\n\n        {/* Analytics Overview Cards */}\n        {analyticsData && (\n          <div className=\"row mb-4\">\n            <div className=\"col-12\">\n              <AnalyticsOverview\n                data={analyticsData.overview}\n                selectedLanguage={selectedLanguage}\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Charts Row */}\n        <div className=\"row mb-4\">\n          {/* Main Analytics Chart */}\n          <div className=\"col-lg-8 col-12 mb-4\">\n            {analyticsData && (\n              <AnalyticsChart\n                data={analyticsData.chartData}\n                timeRange={timeRange}\n                selectedLanguage={selectedLanguage}\n              />\n            )}\n          </div>\n\n          {/* Heatmap Chart */}\n          <div className=\"col-lg-4 col-12 mb-4\">\n            {analyticsData && (\n              <HeatmapChart\n                data={analyticsData.heatmapData}\n                title={`Post views by time of day${\n                  selectedLanguage !== \"all\"\n                    ? ` (${\n                        languageOptions.find(\n                          (l) => l.value === selectedLanguage\n                        )?.label\n                      })`\n                    : \"\"\n                }`}\n                selectedLanguage={selectedLanguage}\n              />\n            )}\n          </div>\n        </div>\n\n        {/* Posts Analytics Table */}\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <PostsTable\n              data={postsData}\n              loading={loading}\n              timeRange={timeRange}\n              selectedLanguage={selectedLanguage}\n            />\n          </div>\n        </div>\n\n        {/* Conversion Analytics Section */}\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title mb-0\">\n                  <iconify-icon\n                    icon=\"solar:target-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Conversion Analytics\n                </h3>\n                <p className=\"text-muted mb-0 mt-1\">\n                  Track Business Comanager CTA performance and conversion\n                  metrics\n                </p>\n              </div>\n              <div className=\"card-body\">\n                <ConversionAnalytics\n                  timeRange={timeRange}\n                  selectedLanguage={selectedLanguage}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Static Pages Analytics Section */}\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title mb-0\">\n                  <iconify-icon\n                    icon=\"solar:document-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Static Pages Analytics\n                </h3>\n                <p className=\"text-muted mb-0 mt-1\">\n                  Performance metrics for all application pages\n                </p>\n              </div>\n              <div className=\"card-body\">\n                <StaticPagesAnalytics\n                  timeRange={timeRange}\n                  selectedLanguage={selectedLanguage}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminBlogAnalytics;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminCategories = () => {\n  const navigate = useNavigate();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [showModal, setShowModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n    color: \"#4567e7\",\n  });\n\n  // Load categories on component mount\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await adminAPI.getCategories();\n\n      if (data.success) {\n        setCategories(data.data || []);\n      } else {\n        setError(data.message || \"Failed to load categories\");\n      }\n    } catch (error) {\n      console.error(\"Load categories error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      let response, data;\n\n      if (editingCategory) {\n        ({ response, data } = await adminAPI.updateCategory(\n          editingCategory.id,\n          formData\n        ));\n      } else {\n        ({ response, data } = await adminAPI.createCategory(formData));\n      }\n\n      if (data.success) {\n        setSuccess(\n          editingCategory\n            ? \"Category updated successfully!\"\n            : \"Category created successfully!\"\n        );\n        setShowModal(false);\n        setEditingCategory(null);\n        setFormData({ name: \"\", description: \"\", color: \"#4567e7\" });\n        loadCategories();\n      } else {\n        setError(data.message || \"Failed to save category\");\n      }\n    } catch (error) {\n      console.error(\"Save category error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleEdit = (category) => {\n    setEditingCategory(category);\n    setFormData({\n      name: category.name,\n      description: category.description || \"\",\n      color: category.color || \"#4567e7\",\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (categoryId) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this category? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteCategory(categoryId);\n\n      if (data.success) {\n        setSuccess(\"Category deleted successfully!\");\n        loadCategories();\n      } else {\n        setError(data.message || \"Failed to delete category\");\n      }\n    } catch (error) {\n      console.error(\"Delete category error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const openCreateModal = () => {\n    setEditingCategory(null);\n    setFormData({ name: \"\", description: \"\", color: \"#4567e7\" });\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    setEditingCategory(null);\n    setFormData({ name: \"\", description: \"\", color: \"#4567e7\" });\n    setError(\"\");\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Categories - Admin\"\n        description=\"Manage blog categories in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Categories\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Organize your blog posts with categories. Create, edit, and\n                manage content categories.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Category\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"alert alert-success mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:check-circle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {success}\n          </div>\n        )}\n\n        {/* Categories Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">\n                Loading categories...\n              </div>\n            </div>\n          ) : categories.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:folder-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No categories found\n              </div>\n              <p className=\"section-descr mb-30\">\n                Create your first category to start organizing your blog posts.\n              </p>\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Category\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Category</th>\n                        <th>Description</th>\n                        <th>Posts</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {categories.map((category) => (\n                        <tr key={category.id}>\n                          <td>\n                            <div className=\"d-flex align-items-center\">\n                              <div\n                                className=\"rounded me-3\"\n                                style={{\n                                  width: \"20px\",\n                                  height: \"20px\",\n                                  backgroundColor: category.color || \"#4567e7\",\n                                }}\n                              ></div>\n                              <div>\n                                <div className=\"fw-bold\">{category.name}</div>\n                                <small className=\"text-muted\">\n                                  /{category.slug}\n                                </small>\n                              </div>\n                            </div>\n                          </td>\n                          <td>\n                            <span className=\"text-muted\">\n                              {category.description || \"No description\"}\n                            </span>\n                          </td>\n                          <td>\n                            <span className=\"badge bg-secondary\">\n                              {category._count?.posts || 0} posts\n                            </span>\n                          </td>\n                          <td>\n                            {new Date(category.createdAt).toLocaleDateString()}\n                          </td>\n                          <td>\n                            <div className=\"btn-group\" role=\"group\">\n                              <button\n                                onClick={() => handleEdit(category)}\n                                className=\"btn btn-sm btn-outline-primary\"\n                                title=\"Edit\"\n                              >\n                                <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                              </button>\n                              <button\n                                onClick={() => handleDelete(category.id)}\n                                className=\"btn btn-sm btn-outline-danger\"\n                                title=\"Delete\"\n                              >\n                                <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {categories.map((category) => (\n                    <div key={category.id} className=\"col-12\">\n                      <div className=\"card border-0 shadow-sm\">\n                        <div className=\"card-body p-3\">\n                          <div className=\"row align-items-center\">\n                            <div className=\"col-12 mb-2\">\n                              <div className=\"d-flex align-items-center\">\n                                <div\n                                  className=\"rounded me-3\"\n                                  style={{\n                                    width: \"30px\",\n                                    height: \"30px\",\n                                    backgroundColor:\n                                      category.color || \"#4567e7\",\n                                  }}\n                                ></div>\n                                <div className=\"flex-grow-1\">\n                                  <h6 className=\"mb-1 fw-bold\">\n                                    {category.name}\n                                  </h6>\n                                  <small className=\"text-muted\">\n                                    /{category.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </div>\n\n                            <div className=\"col-6 col-sm-4 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Description\n                              </small>\n                              <small>\n                                {category.description || \"No description\"}\n                              </small>\n                            </div>\n\n                            <div className=\"col-6 col-sm-4 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Posts\n                              </small>\n                              <span className=\"badge bg-secondary\">\n                                {category._count?.posts || 0} posts\n                              </span>\n                            </div>\n\n                            <div className=\"col-12 col-sm-4 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Created\n                              </small>\n                              <small>\n                                {new Date(\n                                  category.createdAt\n                                ).toLocaleDateString()}\n                              </small>\n                            </div>\n\n                            <div className=\"col-12\">\n                              <div className=\"d-flex gap-2 flex-wrap\">\n                                <button\n                                  onClick={() => handleEdit(category)}\n                                  className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:pen-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Edit\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(category.id)}\n                                  className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:trash-bin-trash-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Delete\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Modal */}\n        {showModal && (\n          <div className=\"modal-overlay\" onClick={closeModal}>\n            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"modal-header\">\n                <h4 className=\"modal-title\">\n                  <iconify-icon\n                    icon=\"solar:folder-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  {editingCategory ? \"Edit Category\" : \"Create New Category\"}\n                </h4>\n                <button\n                  type=\"button\"\n                  className=\"modal-close\"\n                  onClick={closeModal}\n                >\n                  <iconify-icon icon=\"solar:close-circle-bold\"></iconify-icon>\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div className=\"modal-body\">\n                  <div className=\"row\">\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:pen-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Category Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) =>\n                          handleInputChange(\"name\", e.target.value)\n                        }\n                        className=\"form-control\"\n                        placeholder=\"Enter category name\"\n                        required\n                      />\n                    </div>\n\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:text-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Description\n                      </label>\n                      <textarea\n                        value={formData.description}\n                        onChange={(e) =>\n                          handleInputChange(\"description\", e.target.value)\n                        }\n                        className=\"form-control\"\n                        rows={3}\n                        placeholder=\"Brief description of this category\"\n                      />\n                    </div>\n\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:palette-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Color\n                      </label>\n                      <div className=\"d-flex align-items-center gap-3\">\n                        <input\n                          type=\"color\"\n                          value={formData.color}\n                          onChange={(e) =>\n                            handleInputChange(\"color\", e.target.value)\n                          }\n                          className=\"form-control form-control-color\"\n                          style={{ width: \"60px\", height: \"40px\" }}\n                        />\n                        <input\n                          type=\"text\"\n                          value={formData.color}\n                          onChange={(e) =>\n                            handleInputChange(\"color\", e.target.value)\n                          }\n                          className=\"form-control\"\n                          placeholder=\"#4567e7\"\n                        />\n                      </div>\n                      <small className=\"form-text text-muted\">\n                        Choose a color to represent this category\n                      </small>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-mod btn-gray btn-round me-3\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-mod btn-color btn-round\"\n                  >\n                    <iconify-icon\n                      icon=\"solar:check-circle-bold\"\n                      className=\"me-2\"\n                    ></iconify-icon>\n                    {editingCategory ? \"Update Category\" : \"Create Category\"}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminCategories;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminTags = () => {\n  const navigate = useNavigate();\n  const [tags, setTags] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [showModal, setShowModal] = useState(false);\n  const [editingTag, setEditingTag] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n  });\n\n  // Load tags on component mount\n  useEffect(() => {\n    loadTags();\n  }, []);\n\n  const loadTags = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await adminAPI.getTags();\n\n      if (data.success) {\n        setTags(data.data || []);\n      } else {\n        setError(data.message || \"Failed to load tags\");\n      }\n    } catch (error) {\n      console.error(\"Load tags error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      let response, data;\n\n      if (editingTag) {\n        ({ response, data } = await adminAPI.updateTag(\n          editingTag.id,\n          formData\n        ));\n      } else {\n        ({ response, data } = await adminAPI.createTag(formData));\n      }\n\n      if (data.success) {\n        setSuccess(\n          editingTag ? \"Tag updated successfully!\" : \"Tag created successfully!\"\n        );\n        setShowModal(false);\n        setEditingTag(null);\n        setFormData({ name: \"\" });\n        loadTags();\n      } else {\n        setError(data.message || \"Failed to save tag\");\n      }\n    } catch (error) {\n      console.error(\"Save tag error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleEdit = (tag) => {\n    setEditingTag(tag);\n    setFormData({\n      name: tag.name,\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (tagId) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this tag? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteTag(tagId);\n\n      if (data.success) {\n        setSuccess(\"Tag deleted successfully!\");\n        loadTags();\n      } else {\n        setError(data.message || \"Failed to delete tag\");\n      }\n    } catch (error) {\n      console.error(\"Delete tag error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const openCreateModal = () => {\n    setEditingTag(null);\n    setFormData({ name: \"\" });\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    setEditingTag(null);\n    setFormData({ name: \"\" });\n    setError(\"\");\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Tags - Admin\"\n        description=\"Manage blog tags in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Tags\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Tag your blog posts for better organization and discoverability.\n                Create and manage content tags.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Tag\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"alert alert-success mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:check-circle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {success}\n          </div>\n        )}\n\n        {/* Tags Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">Loading tags...</div>\n            </div>\n          ) : tags.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:tag-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No tags found\n              </div>\n              <p className=\"section-descr mb-30\">\n                Create your first tag to start organizing your blog posts.\n              </p>\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Tag\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Tag Name</th>\n                        <th>Posts</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {tags.map((tag) => (\n                        <tr key={tag.id}>\n                          <td>\n                            <div className=\"d-flex align-items-center\">\n                              <iconify-icon\n                                icon=\"solar:tag-bold\"\n                                className=\"me-3 color-primary-1\"\n                              ></iconify-icon>\n                              <div>\n                                <div className=\"fw-bold\">{tag.name}</div>\n                                <small className=\"text-muted\">\n                                  /{tag.slug}\n                                </small>\n                              </div>\n                            </div>\n                          </td>\n                          <td>\n                            <span className=\"badge bg-secondary\">\n                              {tag._count?.posts || 0} posts\n                            </span>\n                          </td>\n                          <td>\n                            {new Date(tag.createdAt).toLocaleDateString()}\n                          </td>\n                          <td>\n                            <div className=\"btn-group\" role=\"group\">\n                              <button\n                                onClick={() => handleEdit(tag)}\n                                className=\"btn btn-sm btn-outline-primary\"\n                                title=\"Edit\"\n                              >\n                                <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                              </button>\n                              <button\n                                onClick={() => handleDelete(tag.id)}\n                                className=\"btn btn-sm btn-outline-danger\"\n                                title=\"Delete\"\n                              >\n                                <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {tags.map((tag) => (\n                    <div key={tag.id} className=\"col-12\">\n                      <div className=\"card border-0 shadow-sm\">\n                        <div className=\"card-body p-3\">\n                          <div className=\"row align-items-center\">\n                            <div className=\"col-12 mb-2\">\n                              <div className=\"d-flex align-items-center\">\n                                <iconify-icon\n                                  icon=\"solar:tag-bold\"\n                                  className=\"me-3 color-primary-1\"\n                                  style={{ fontSize: \"1.5rem\" }}\n                                ></iconify-icon>\n                                <div className=\"flex-grow-1\">\n                                  <h6 className=\"mb-1 fw-bold\">{tag.name}</h6>\n                                  <small className=\"text-muted\">\n                                    /{tag.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </div>\n\n                            <div className=\"col-6 col-sm-6 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Posts\n                              </small>\n                              <span className=\"badge bg-secondary\">\n                                {tag._count?.posts || 0} posts\n                              </span>\n                            </div>\n\n                            <div className=\"col-6 col-sm-6 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Created\n                              </small>\n                              <small>\n                                {new Date(tag.createdAt).toLocaleDateString()}\n                              </small>\n                            </div>\n\n                            <div className=\"col-12\">\n                              <div className=\"d-flex gap-2 flex-wrap\">\n                                <button\n                                  onClick={() => handleEdit(tag)}\n                                  className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:pen-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Edit\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(tag.id)}\n                                  className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:trash-bin-trash-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Delete\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Modal */}\n        {showModal && (\n          <div className=\"modal-overlay\" onClick={closeModal}>\n            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"modal-header\">\n                <h4 className=\"modal-title\">\n                  <iconify-icon\n                    icon=\"solar:tag-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  {editingTag ? \"Edit Tag\" : \"Create New Tag\"}\n                </h4>\n                <button\n                  type=\"button\"\n                  className=\"modal-close\"\n                  onClick={closeModal}\n                >\n                  <iconify-icon icon=\"solar:close-circle-bold\"></iconify-icon>\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div className=\"modal-body\">\n                  <div className=\"row\">\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:pen-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Tag Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) =>\n                          handleInputChange(\"name\", e.target.value)\n                        }\n                        className=\"form-control\"\n                        placeholder=\"Enter tag name\"\n                        required\n                      />\n                      <small className=\"form-text text-muted\">\n                        Keep it short and descriptive (e.g., \"JavaScript\",\n                        \"Tutorial\", \"News\")\n                      </small>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-mod btn-gray btn-round me-3\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-mod btn-color btn-round\"\n                  >\n                    <iconify-icon\n                      icon=\"solar:check-circle-bold\"\n                      className=\"me-2\"\n                    ></iconify-icon>\n                    {editingTag ? \"Update Tag\" : \"Create Tag\"}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminTags;\n", "import { apiCall } from \"./api\";\n\n// Get comments for admin\nexport const getAdminComments = async (params = {}) => {\n  try {\n    const queryParams = new URLSearchParams();\n\n    if (params.page) queryParams.append(\"page\", params.page);\n    if (params.limit) queryParams.append(\"limit\", params.limit);\n    if (params.status) queryParams.append(\"status\", params.status);\n    if (params.search) queryParams.append(\"search\", params.search);\n    if (params.blogPostId) queryParams.append(\"blogPostId\", params.blogPostId);\n\n    const { response, data } = await apiCall(`/admin/comments?${queryParams}`);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Get admin comments error:\", error);\n    throw error;\n  }\n};\n\n// Approve comment\nexport const approveComment = async (commentId) => {\n  try {\n    const { response, data } = await apiCall(\n      `/admin/comments/${commentId}/approve`,\n      {\n        method: \"PATCH\",\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Approve comment error:\", error);\n    throw error;\n  }\n};\n\n// Reject/Hide comment\nexport const rejectComment = async (commentId) => {\n  try {\n    const { response, data } = await apiCall(\n      `/admin/comments/${commentId}/reject`,\n      {\n        method: \"PATCH\",\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Reject comment error:\", error);\n    throw error;\n  }\n};\n\n// Delete comment\nexport const deleteComment = async (commentId) => {\n  try {\n    const { response, data } = await apiCall(`/admin/comments/${commentId}`, {\n      method: \"DELETE\",\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Delete comment error:\", error);\n    throw error;\n  }\n};\n\n// Get comment statistics\nexport const getCommentStats = async () => {\n  try {\n    const { response, data: result } = await apiCall(\"/admin/comments?limit=1\");\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    // Get counts for different statuses\n    const [pendingResult, approvedResult] = await Promise.all([\n      apiCall(\"/admin/comments?status=pending&limit=1\"),\n      apiCall(\"/admin/comments?status=approved&limit=1\"),\n    ]);\n\n    return {\n      total: result.data.pagination.total,\n      pending: pendingResult.data.data.pagination.total,\n      approved: approvedResult.data.data.pagination.total,\n    };\n  } catch (error) {\n    console.error(\"Get comment stats error:\", error);\n    throw error;\n  }\n};\n", "import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport AdminLayout from '@/components/admin/AdminLayout';\nimport { getAdminComments, approveComment, rejectComment, deleteComment } from '@/utils/commentAPI';\n\nexport default function AdminCommentsPage() {\n  const [comments, setComments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [stats, setStats] = useState({ total: 0, pending: 0, approved: 0 });\n\n  const fetchComments = async () => {\n    try {\n      setLoading(true);\n      const response = await getAdminComments({\n        page: currentPage,\n        limit: 10,\n        status: statusFilter,\n        search: searchTerm,\n      });\n\n      if (response.success) {\n        setComments(response.data.comments);\n        setTotalPages(response.data.pagination.pages);\n        \n        // Update stats\n        const totalComments = response.data.pagination.total;\n        setStats(prev => ({ ...prev, total: totalComments }));\n      }\n    } catch (err) {\n      setError('Failed to fetch comments');\n      console.error('Fetch comments error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchComments();\n  }, [currentPage, statusFilter, searchTerm]);\n\n  const handleApprove = async (commentId) => {\n    try {\n      await approveComment(commentId);\n      fetchComments(); // Refresh the list\n    } catch (err) {\n      setError('Failed to approve comment');\n    }\n  };\n\n  const handleReject = async (commentId) => {\n    try {\n      await rejectComment(commentId);\n      fetchComments(); // Refresh the list\n    } catch (err) {\n      setError('Failed to reject comment');\n    }\n  };\n\n  const handleDelete = async (commentId) => {\n    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {\n      try {\n        await deleteComment(commentId);\n        fetchComments(); // Refresh the list\n      } catch (err) {\n        setError('Failed to delete comment');\n      }\n    }\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    setCurrentPage(1);\n    fetchComments();\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const truncateText = (text, maxLength = 100) => {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"container-fluid\">\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"d-flex justify-content-between align-items-center mb-4\">\n              <h1 className=\"h3 mb-0\">Comment Management</h1>\n              <div className=\"d-flex gap-3\">\n                <div className=\"badge bg-primary\">Total: {stats.total}</div>\n                <div className=\"badge bg-warning\">Pending: {stats.pending}</div>\n                <div className=\"badge bg-success\">Approved: {stats.approved}</div>\n              </div>\n            </div>\n\n            {error && (\n              <div className=\"alert alert-danger\" role=\"alert\">\n                {error}\n              </div>\n            )}\n\n            {/* Filters */}\n            <div className=\"card mb-4\">\n              <div className=\"card-body\">\n                <div className=\"row g-3\">\n                  <div className=\"col-md-4\">\n                    <label className=\"form-label\">Status Filter</label>\n                    <select\n                      className=\"form-select\"\n                      value={statusFilter}\n                      onChange={(e) => {\n                        setStatusFilter(e.target.value);\n                        setCurrentPage(1);\n                      }}\n                    >\n                      <option value=\"all\">All Comments</option>\n                      <option value=\"pending\">Pending Approval</option>\n                      <option value=\"approved\">Approved</option>\n                    </select>\n                  </div>\n                  <div className=\"col-md-8\">\n                    <label className=\"form-label\">Search</label>\n                    <form onSubmit={handleSearch} className=\"d-flex\">\n                      <input\n                        type=\"text\"\n                        className=\"form-control\"\n                        placeholder=\"Search by author name, email, or content...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                      />\n                      <button type=\"submit\" className=\"btn btn-primary ms-2\">\n                        Search\n                      </button>\n                    </form>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Comments Table */}\n            <div className=\"card\">\n              <div className=\"card-body\">\n                {loading ? (\n                  <div className=\"text-center py-4\">\n                    <div className=\"spinner-border\" role=\"status\">\n                      <span className=\"visually-hidden\">Loading...</span>\n                    </div>\n                  </div>\n                ) : comments.length === 0 ? (\n                  <div className=\"text-center py-4\">\n                    <p className=\"text-muted\">No comments found.</p>\n                  </div>\n                ) : (\n                  <div className=\"table-responsive\">\n                    <table className=\"table table-hover\">\n                      <thead>\n                        <tr>\n                          <th>Author</th>\n                          <th>Email</th>\n                          <th>Content</th>\n                          <th>Blog Post</th>\n                          <th>Status</th>\n                          <th>Date</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {comments.map((comment) => (\n                          <tr key={comment.id}>\n                            <td>\n                              <strong>{comment.author}</strong>\n                              {comment.website && (\n                                <div>\n                                  <small>\n                                    <a href={comment.website} target=\"_blank\" rel=\"noopener noreferrer\">\n                                      {comment.website}\n                                    </a>\n                                  </small>\n                                </div>\n                              )}\n                            </td>\n                            <td>\n                              <small className=\"text-muted\">{comment.email}</small>\n                            </td>\n                            <td>\n                              <div title={comment.content}>\n                                {truncateText(comment.content)}\n                              </div>\n                              {comment.parent && (\n                                <small className=\"text-muted\">\n                                  Reply to: {comment.parent.author}\n                                </small>\n                              )}\n                            </td>\n                            <td>\n                              <Link \n                                to={`/blog-single/${comment.blogPost.slug}`}\n                                className=\"text-decoration-none\"\n                                target=\"_blank\"\n                              >\n                                {comment.blogPost.translations[0]?.title || 'Untitled'}\n                              </Link>\n                            </td>\n                            <td>\n                              <span className={`badge ${comment.approved ? 'bg-success' : 'bg-warning'}`}>\n                                {comment.approved ? 'Approved' : 'Pending'}\n                              </span>\n                            </td>\n                            <td>\n                              <small>{formatDate(comment.createdAt)}</small>\n                            </td>\n                            <td>\n                              <div className=\"btn-group btn-group-sm\">\n                                {!comment.approved ? (\n                                  <button\n                                    className=\"btn btn-success\"\n                                    onClick={() => handleApprove(comment.id)}\n                                    title=\"Approve\"\n                                  >\n                                    <i className=\"mi-check\"></i>\n                                  </button>\n                                ) : (\n                                  <button\n                                    className=\"btn btn-warning\"\n                                    onClick={() => handleReject(comment.id)}\n                                    title=\"Hide\"\n                                  >\n                                    <i className=\"mi-eye-off\"></i>\n                                  </button>\n                                )}\n                                <button\n                                  className=\"btn btn-danger\"\n                                  onClick={() => handleDelete(comment.id)}\n                                  title=\"Delete\"\n                                >\n                                  <i className=\"mi-trash\"></i>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n\n                {/* Pagination */}\n                {totalPages > 1 && (\n                  <nav className=\"mt-4\">\n                    <ul className=\"pagination justify-content-center\">\n                      <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>\n                        <button\n                          className=\"page-link\"\n                          onClick={() => setCurrentPage(currentPage - 1)}\n                          disabled={currentPage === 1}\n                        >\n                          Previous\n                        </button>\n                      </li>\n                      {[...Array(totalPages)].map((_, index) => (\n                        <li key={index + 1} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>\n                          <button\n                            className=\"page-link\"\n                            onClick={() => setCurrentPage(index + 1)}\n                          >\n                            {index + 1}\n                          </button>\n                        </li>\n                      ))}\n                      <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>\n                        <button\n                          className=\"page-link\"\n                          onClick={() => setCurrentPage(currentPage + 1)}\n                          disabled={currentPage === totalPages}\n                        >\n                          Next\n                        </button>\n                      </li>\n                    </ul>\n                  </nav>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n}\n"], "file": "assets/pages-other-C4S7sxMn.js"}