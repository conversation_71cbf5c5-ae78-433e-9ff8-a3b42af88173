import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import { authAPI, adminAPI } from "../utils/api";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem("adminToken");
      const userData = localStorage.getItem("adminUser");

      if (!token || !userData) {
        navigate("/admin");
        return;
      }

      try {
        // Verify token with backend
        const { response, data } = await authAPI.getMe();

        if (response.ok && data.success) {
          setUser(data.user);
        } else {
          // Token invalid, redirect to login
          localStorage.removeItem("adminToken");
          localStorage.removeItem("adminUser");
          navigate("/admin");
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        localStorage.removeItem("adminToken");
        localStorage.removeItem("adminUser");
        navigate("/admin");
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("adminToken");
    localStorage.removeItem("adminUser");
    navigate("/admin");
  };

  if (loading) {
    return (
      <div id="page" className="page">
        <main id="main">
          <section className="page-section">
            <div className="container relative">
              <div className="row">
                <div className="col-12 text-center">
                  <div className="loading-animation">
                    <iconify-icon
                      icon="solar:refresh-bold"
                      className="color-primary-1"
                      style={{
                        fontSize: "3rem",
                        animation: "spin 1s linear infinite",
                      }}
                    ></iconify-icon>
                    <div className="mt-20">
                      <div className="hs-line-4 font-alt black">Loading...</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    );
  }

  return (
    <>
      <SEO
        title="Admin Dashboard - DevSkills"
        description="DevSkills admin dashboard for content management"
        noIndex={true}
      />

      <AdminLayout title="Dashboard">
        {/* Stats Cards */}
        <div className="row mb-40">
          {/* Total Posts */}
          <div className="col-sm-6 col-lg-3 mb-md-50">
            <div className="number-2-item">
              <div className="number-2-icon">
                <iconify-icon icon="solar:document-text-bold"></iconify-icon>
              </div>
              <div className="number-2-title">Total Posts</div>
              <div className="number-2-number">0</div>
            </div>
          </div>

          {/* Categories */}
          <div className="col-sm-6 col-lg-3 mb-md-50">
            <div className="number-2-item">
              <div className="number-2-icon">
                <iconify-icon icon="solar:folder-bold"></iconify-icon>
              </div>
              <div className="number-2-title">Categories</div>
              <div className="number-2-number">5</div>
            </div>
          </div>

          {/* Comments */}
          <div className="col-sm-6 col-lg-3 mb-md-50">
            <div className="number-2-item">
              <div className="number-2-icon">
                <iconify-icon icon="solar:chat-round-bold"></iconify-icon>
              </div>
              <div className="number-2-title">Comments</div>
              <div className="number-2-number">0</div>
            </div>
          </div>

          {/* Page Views */}
          <div className="col-sm-6 col-lg-3 mb-md-50">
            <div className="number-2-item">
              <div className="number-2-icon">
                <iconify-icon icon="solar:eye-bold"></iconify-icon>
              </div>
              <div className="number-2-title">Page Views</div>
              <div className="number-2-number">-</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="row">
          <div className="col-12">
            <div className="mb-20">
              <h3 className="hs-line-4 font-alt black mb-20 mb-xs-10">
                Quick Actions
              </h3>
            </div>

            <div className="row">
              {/* New Blog Post */}
              <div className="col-sm-6 col-lg-4 mb-md-50">
                <div className="alt-features-item align-center">
                  <div className="alt-features-icon">
                    <iconify-icon icon="solar:add-circle-bold"></iconify-icon>
                  </div>
                  <h3 className="alt-features-title font-alt">New Blog Post</h3>
                  <div className="alt-features-descr">
                    Create a new multilingual blog post with rich content and
                    scheduling.
                  </div>
                  <div className="local-scroll mt-20">
                    <button
                      onClick={() => navigate("/admin/blog/new")}
                      className="btn btn-mod btn-color btn-round"
                    >
                      Create Post
                    </button>
                  </div>
                </div>
              </div>

              {/* Manage Posts */}
              <div className="col-sm-6 col-lg-4 mb-md-50">
                <div className="alt-features-item align-center">
                  <div className="alt-features-icon">
                    <iconify-icon icon="solar:documents-bold"></iconify-icon>
                  </div>
                  <h3 className="alt-features-title font-alt">Manage Posts</h3>
                  <div className="alt-features-descr">
                    Edit, publish, schedule, and organize your existing blog
                    posts.
                  </div>
                  <div className="local-scroll mt-20">
                    <button
                      onClick={() => navigate("/admin/posts")}
                      className="btn btn-mod btn-color btn-round"
                    >
                      Manage Posts
                    </button>
                  </div>
                </div>
              </div>

              {/* Categories & Tags */}
              <div className="col-sm-6 col-lg-4 mb-md-50">
                <div className="alt-features-item align-center">
                  <div className="alt-features-icon">
                    <iconify-icon icon="solar:folder-with-files-bold"></iconify-icon>
                  </div>
                  <h3 className="alt-features-title font-alt">
                    Categories & Tags
                  </h3>
                  <div className="alt-features-descr">
                    Organize your content with categories and tags for better
                    navigation.
                  </div>
                  <div className="local-scroll mt-20">
                    <button
                      onClick={() => navigate("/admin/categories")}
                      className="btn btn-mod btn-color btn-round"
                    >
                      Organize Content
                    </button>
                  </div>
                </div>
              </div>

              {/* Comment Management */}
              <div className="col-sm-6 col-lg-4 mb-md-50">
                <div className="alt-features-item align-center">
                  <div className="alt-features-icon">
                    <iconify-icon icon="solar:chat-round-bold"></iconify-icon>
                  </div>
                  <h3 className="alt-features-title font-alt">
                    Comment Management
                  </h3>
                  <div className="alt-features-descr">
                    Review, approve, and manage comments from your blog readers.
                  </div>
                  <div className="local-scroll mt-20">
                    <button
                      onClick={() => navigate("/admin/comments")}
                      className="btn btn-mod btn-color btn-round"
                    >
                      Manage Comments
                    </button>
                  </div>
                </div>
              </div>

              {/* Analytics */}
              <div className="col-sm-6 col-lg-4 mb-md-50">
                <div className="alt-features-item align-center">
                  <div className="alt-features-icon">
                    <iconify-icon icon="solar:chart-2-bold"></iconify-icon>
                  </div>
                  <h3 className="alt-features-title font-alt">Analytics</h3>
                  <div className="alt-features-descr">
                    View detailed analytics and insights about your blog
                    performance.
                  </div>
                  <div className="local-scroll mt-20">
                    <button
                      onClick={() => navigate("/admin/analytics")}
                      className="btn btn-mod btn-color btn-round"
                    >
                      View Analytics
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default AdminDashboard;
