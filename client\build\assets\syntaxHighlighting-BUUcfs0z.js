const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/vendor-misc-j6k8kvFA.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css"])))=>i.map(i=>d[i]);
import{_ as r}from"./admin-routes-D7V7jRaZ.js";import"./vendor-react-EBZQFYZ5.js";import"./vendor-misc-j6k8kvFA.js";import"./vendor-animations-Dl3DQHMd.js";import"./vendor-gallery-BKyWYjF6.js";import"./vendor-admin-DvrlCxcB.js";let l=null,c=null;async function _(){return l||c||(c=(async()=>{try{const i=(await r(async()=>{const{default:t}=await import("./vendor-misc-j6k8kvFA.js").then(a=>a.p);return{default:t}},__vite__mapDeps([0,1,2]))).default;return await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.t),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.u),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.v),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.w),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.x),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.y),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.z),__vite__mapDeps([0,1,2])),await r(()=>import("./vendor-misc-j6k8kvFA.js").then(t=>t.B),__vite__mapDeps([0,1,2])),l=i,console.log("Prism.js initialized successfully with essential languages"),l}catch(i){throw console.error("Failed to initialize Prism.js:",i),i}})(),c)}async function w(i=".blog-content pre code"){try{const t=await _();document.querySelectorAll(i).forEach(e=>{const s=e.textContent||"",n=d(e)||"javascript";try{const o=t.languages[n];if(o){const u=t.highlight(s,o,n);e.innerHTML=u,e.classList.add("prism-highlighted"),e.classList.add(`language-${n}`)}else e.classList.add("prism-fallback"),console.warn(`Language "${n}" not supported by Prism.js`)}catch(o){console.warn(`Failed to highlight code block with language "${n}":`,o),e.classList.add("prism-fallback")}})}catch(t){console.error("Failed to highlight code blocks:",t)}}function d(i){const t=i.className.match(/language-(\w+)/);if(t)return t[1];const a=i.closest("pre");if(a){const s=a.className.match(/language-(\w+)/);if(s)return s[1]}const e=i.textContent||"";return e.includes("function")&&e.includes("{")?"javascript":e.includes("def ")&&e.includes(":")?"python":e.includes("<?php")?"php":e.includes("<html")||e.includes("<!DOCTYPE")?"html":e.includes("SELECT")||e.includes("FROM")?"sql":"text"}export{w as highlightCodeBlocks};
//# sourceMappingURL=syntaxHighlighting-BUUcfs0z.js.map
