import{i as J,u as A,r as b,j as e,a as L,b as I,L as R,R as _,H as O,c as X,G as K,I as Q,d as Z,E as Y,e as ee}from"./vendor-react-BE9lZbv0.js";import{P as n}from"./vendor-misc-BUjjPnRU.js";import{i as se,B as te,a as ae}from"./vendor-i18n-CQXQruTg.js";import{j as ie}from"./vendor-animations-Dl3DQHMd.js";import{S as ne,C as re,a as le,b as oe,c as ce,L as de,P as me,d as ge,p as pe,e as he,f as ue,i as xe}from"./vendor-admin-DSFDn6-z.js";const E={en:{name:"English",flag:"🇬🇧"},et:{name:"Eesti",flag:"🇪🇪"},fi:{name:"<PERSON><PERSON>",flag:"🇫🇮"},de:{name:"<PERSON><PERSON><PERSON>",flag:"🇩🇪"},sv:{name:"<PERSON><PERSON>",flag:"🇸🇪"}};se.use(te).use(ae).use(J).init({lng:"et",fallbackLng:"en",supportedLngs:Object.keys(E),ns:["translation"],defaultNS:"translation",backend:{loadPath:"/locales/{{lng}}/{{ns}}.json"},detection:{order:["path","localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"i18nextLng",lookupFromPathIndex:0,checkWhitelist:!0,lookupFromPathIndex:0,checkWhitelist:!0},interpolation:{escapeValue:!1,formatSeparator:",",format:(s,t,i)=>t==="uppercase"?s.toUpperCase():t==="lowercase"?s.toLowerCase():t==="capitalize"?s.charAt(0).toUpperCase()+s.slice(1):s},react:{useSuspense:!1,bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transKeepBasicHtmlNodesFor:["br","strong","i","em","span"]},debug:!1,load:"languageOnly",preload:Object.keys(E),cleanCode:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",returnObjects:!1,returnEmptyString:!1,returnNull:!1,joinArrays:!1,postProcess:!1,saveMissing:!1,saveMissingTo:"current",missingKeyHandler:(s,t,i,a)=>{},updateMissing:!1,ignoreJSONStructure:!0});const as=()=>{const{t:s}=A(),[t,i]=b.useState(!1),[a,c]=b.useState(!1);b.useEffect(()=>{if(document.body.classList.contains("gdpr-banner-needed"))i(!0),document.body.classList.remove("gdpr-banner-needed");else{const x=localStorage.getItem("gdpr-consent");x&&p(JSON.parse(x))}},[]);const p=l=>{setTimeout(()=>{typeof window<"u"&&window.gtag&&window.gtag("consent","update",{analytics_storage:l.analytics?"granted":"denied",ad_storage:l.marketing?"granted":"denied",ad_user_data:l.marketing?"granted":"denied",ad_personalization:l.marketing?"granted":"denied"})},0)},h=()=>{const l={necessary:!0,analytics:!0,marketing:!0,timestamp:new Date().toISOString()};localStorage.setItem("gdpr-consent",JSON.stringify(l)),p(l),i(!1),setTimeout(()=>{typeof window<"u"&&window.gtag&&window.gtag("event","consent_granted",{event_category:"GDPR",event_label:"Accept All"})},100)},r=()=>{const l={necessary:!0,analytics:!1,marketing:!1,timestamp:new Date().toISOString()};localStorage.setItem("gdpr-consent",JSON.stringify(l)),p(l),i(!1),setTimeout(()=>{typeof window<"u"&&window.gtag&&window.gtag("event","consent_denied",{event_category:"GDPR",event_label:"Reject All"})},100)},o=l=>{const x={necessary:!0,analytics:l.analytics,marketing:l.marketing,timestamp:new Date().toISOString()};localStorage.setItem("gdpr-consent",JSON.stringify(x)),p(x),i(!1),c(!1),setTimeout(()=>{typeof window<"u"&&window.gtag&&window.gtag("event","consent_customized",{event_category:"GDPR",event_label:`Analytics: ${l.analytics}, Marketing: ${l.marketing}`})},100)};return t?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"gdpr-banner gdpr-show",children:e.jsx("div",{className:"container",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-lg-8 col-md-7",children:e.jsxs("div",{className:"gdpr-content",children:[e.jsx("h6",{className:"gdpr-title mb-2",children:s("gdpr.title")}),e.jsx("p",{className:"gdpr-description mb-0",children:s("gdpr.description")})]})}),e.jsx("div",{className:"col-lg-4 col-md-5",children:e.jsxs("div",{className:"gdpr-buttons",children:[e.jsx("button",{className:"btn btn-mod btn-small btn-w btn-circle gdpr-btn-accept",onClick:h,"data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:s("gdpr.acceptAll")}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:s("gdpr.acceptAll")})]})}),e.jsx("button",{className:"btn btn-mod btn-small btn-border-w btn-circle",onClick:r,"data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:s("gdpr.rejectAll")}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:s("gdpr.rejectAll")})]})}),e.jsx("button",{className:"btn btn-mod btn-small btn-border-w btn-circle",onClick:()=>c(!0),"data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:s("gdpr.customize")}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:s("gdpr.customize")})]})})]})})]})})}),a&&e.jsx(B,{onClose:()=>c(!1),onSave:o,t:s})]}):null},B=({onClose:s,onSave:t,t:i})=>{const[a,c]=b.useState({analytics:!1,marketing:!1}),p=()=>{t(a)};return e.jsx("div",{className:"gdpr-modal",children:e.jsx("div",{className:"gdpr-modal-content",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-6 offset-lg-3",children:e.jsxs("div",{className:"gdpr-modal-inner",children:[e.jsx("div",{className:"gdpr-modal-header mb-30",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsx("h5",{className:"gdpr-modal-title mb-0",children:i("gdpr.customizeTitle")}),e.jsx("button",{className:"gdpr-close",onClick:s,children:e.jsx("i",{className:"mi-close size-18"})})]})}),e.jsxs("div",{className:"gdpr-modal-body",children:[e.jsxs("div",{className:"gdpr-category mb-25",children:[e.jsx("div",{className:"gdpr-category-header mb-10",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsx("h6",{className:"gdpr-category-title mb-0 mr-2",children:i("gdpr.necessary")}),e.jsx("span",{className:"gdpr-required-badge",children:i("gdpr.required")})]})}),e.jsx("p",{className:"gdpr-category-desc",children:i("gdpr.necessaryDesc")})]}),e.jsxs("div",{className:"gdpr-category mb-25",children:[e.jsx("div",{className:"gdpr-category-header mb-10",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsx("h6",{className:"gdpr-category-title mb-0",children:i("gdpr.analytics")}),e.jsxs("label",{className:"gdpr-switch",children:[e.jsx("input",{type:"checkbox",checked:a.analytics,onChange:h=>c(r=>({...r,analytics:h.target.checked}))}),e.jsx("span",{className:"gdpr-slider"})]})]})}),e.jsx("p",{className:"gdpr-category-desc",children:i("gdpr.analyticsDesc")})]}),e.jsxs("div",{className:"gdpr-category mb-30",children:[e.jsx("div",{className:"gdpr-category-header mb-10",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsx("h6",{className:"gdpr-category-title mb-0",children:i("gdpr.marketing")}),e.jsxs("label",{className:"gdpr-switch",children:[e.jsx("input",{type:"checkbox",checked:a.marketing,onChange:h=>c(r=>({...r,marketing:h.target.checked}))}),e.jsx("span",{className:"gdpr-slider"})]})]})}),e.jsx("p",{className:"gdpr-category-desc",children:i("gdpr.marketingDesc")})]})]}),e.jsx("div",{className:"gdpr-modal-footer",children:e.jsxs("div",{className:"d-flex justify-content-end gap-2",children:[e.jsx("button",{className:"btn btn-mod btn-small btn-border-w btn-circle",onClick:s,"data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:i("gdpr.cancel")}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:i("gdpr.cancel")})]})}),e.jsx("button",{className:"btn btn-mod btn-small btn-w btn-circle",onClick:p,"data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:i("gdpr.savePreferences")}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:i("gdpr.savePreferences")})]})})]})})]})})})})})})};B.propTypes={onClose:n.func.isRequired,onSave:n.func.isRequired,t:n.func.isRequired};function is(){const{pathname:s}=L();return b.useEffect(()=>{window.scrollTo(0,0)},[s]),e.jsx(e.Fragment,{})}const W=["en","et","fi","de","sv"],je="et",ns=({children:s})=>{const t=I(),i=L(),{i18n:a}=A();return b.useEffect(()=>{const c=navigator.language.split("-")[0],p=localStorage.getItem("i18nextLng"),h=W.includes(p)?p:W.includes(c)?c:je;a.language!==h&&a.changeLanguage(h);const r=i.pathname,o=i.search;r==="/"?t(`/${h}${o}`,{replace:!0}):t(`/${h}${r}${o}`,{replace:!0})},[t,i,a]),null},be=s=>{const t=s.match(/^\/([a-z]{2})\//);return t?t[1]:"et"},q=(s,t)=>{if(s.match(/^\/[a-z]{2}\//))return s;if(s==="/")return`/${t}`;const i=s.startsWith("/")?s:`/${s}`;return`/${t}${i}`},rs=({to:s,children:t,className:i,...a})=>{const c=L(),p=be(c.pathname),h=q(s,p);return e.jsx(R,{to:h,className:i,...a,children:t})};function ls(){const{i18n:s}=A(),t=I(),i=L(),[a,c]=b.useState(!1),p=b.useRef(null),[h,r]=b.useState(typeof window<"u"?window.innerWidth<=1024:!1);b.useEffect(()=>{const m=()=>{r(window.innerWidth<=1024)};return window.addEventListener("resize",m),()=>{window.removeEventListener("resize",m)}},[]),b.useEffect(()=>{function m(g){p.current&&!p.current.contains(g.target)&&c(!1)}return document.addEventListener("mousedown",m),()=>{document.removeEventListener("mousedown",m)}},[]);const o=()=>{c(!a)},l=m=>{s.changeLanguage(m),c(!1);const g=i.pathname,y=i.search;let u;g.match(/^\/[a-z]{2}(\/|$)/)?(u=g.replace(/^\/[a-z]{2}/,`/${m}`),u===`/${m}`&&(u=`/${m}/`)):u=q(g,m),t(`${u}${y}`,{replace:!0}),typeof window<"u"&&window.gtag&&window.gtag("event","language_change",{event_category:"User Interaction",event_label:`${s.language} to ${m}`,from_language:s.language,to_language:m,send_to:"G-8NEGL4LL8Q"})},x=s.language||"et";return h?e.jsx("div",{className:"language-selector-mobile d-flex align-items-center",children:Object.entries(E).map(([m,g])=>e.jsx("button",{className:`btn btn-link p-0 mx-2 ${m===x?"text-white":"text-muted opacity-50"}`,onClick:()=>l(m),"aria-label":`Switch to ${g.name}`,children:e.jsx("span",{className:"language-code text-uppercase font-weight-bold",children:m})},m))}):e.jsxs("div",{className:"language-selector position-relative",ref:p,children:[e.jsx("button",{className:"language-toggle btn btn-link p-0 d-flex align-items-center",onClick:o,"aria-expanded":a,"aria-haspopup":"true",children:e.jsx("span",{className:"language-code text-uppercase",children:x})}),a&&e.jsx("div",{className:"language-dropdown position-absolute bg-dark-1 py-2 rounded shadow-sm",children:Object.entries(E).map(([m,g])=>e.jsx("button",{className:`dropdown-item d-flex align-items-center px-3 py-1 ${m===x?"active":""}`,onClick:()=>l(m),children:e.jsx("span",{className:"language-name",children:g.name})},m))})]})}const fe=()=>"https://devskills.ee/api",V=fe(),v=async(s,t={})=>{const i=`${V}${s}`,a=t.body instanceof FormData,c={};a||(c["Content-Type"]="application/json"),s.includes("/contact")&&(c["X-API-Key"]="9afe34d2134b43e19163c50924df6714");const p=localStorage.getItem("adminToken");p&&(c.Authorization=`Bearer ${p}`);const h={...t,headers:{...c,...t.headers}};try{const r=await fetch(i,h),o=r.headers.get("content-type");if(o&&o.includes("application/json")){const l=await r.json();return{response:r,data:l}}else return{response:r,data:null}}catch(r){throw console.error("API call failed:",r),r}},os={login:s=>v("/auth/login",{method:"POST",body:JSON.stringify(s)}),getMe:()=>v("/auth/me"),logout:()=>v("/auth/logout",{method:"POST"})},F={getPosts:(s={})=>{const t=new URLSearchParams(s).toString();return v(`/blog${t?`?${t}`:""}`)},getPost:s=>v(`/blog/${s}`),createPost:s=>v("/blog",{method:"POST",body:s,headers:{}}),updatePost:(s,t)=>v(`/blog/${s}`,{method:"PUT",body:t,headers:{}}),deletePost:s=>v(`/blog/${s}`,{method:"DELETE"}),toggleVisibility:s=>v(`/blog/${s}/toggle-visibility`,{method:"PATCH"}),getFeaturedPosts:(s="en",t=3)=>v(`/blog?featured=true&limit=${t}&published=true&language=${s}`),getBlogPosts:(s={})=>{const i={...{language:"en",page:1,limit:9,published:"true"},...s},a=new URLSearchParams(i).toString();return v(`/blog?${a}`)},createComment:(s,t)=>v(`/blog/${s}/comments`,{method:"POST",body:JSON.stringify(t),headers:{"Content-Type":"application/json"}})},H={getDashboard:()=>v("/admin/dashboard"),getBlogAnalytics:(s="last30days",t="all")=>v(`/admin/analytics/blog?timeRange=${s}&language=${t}`),getBlogPostsAnalytics:(s="last30days",t="all")=>v(`/admin/analytics/posts?timeRange=${s}&language=${t}`),getConversionAnalytics:(s,t,i="all")=>v(`/admin/analytics/conversions?startDate=${s}&endDate=${t}&language=${i}`),getStaticPagesAnalytics:(s="last30days",t="all")=>v(`/admin/analytics/pages?timeRange=${s}&language=${t}`),getPosts:(s={})=>{const t=new URLSearchParams(s).toString();return v(`/admin/posts${t?`?${t}`:""}`)},getPost:s=>v(`/admin/posts/${s}`),uploadImage:s=>{const t=new FormData;return t.append("image",s),v("/admin/upload-image",{method:"POST",body:t,headers:{}})},getCategories:()=>v("/admin/categories"),createCategory:s=>v("/admin/categories",{method:"POST",body:JSON.stringify(s)}),updateCategory:(s,t)=>v(`/admin/categories/${s}`,{method:"PUT",body:JSON.stringify(t)}),deleteCategory:s=>v(`/admin/categories/${s}`,{method:"DELETE"}),getTags:()=>v("/admin/tags"),createTag:s=>v("/admin/tags",{method:"POST",body:JSON.stringify(s)}),updateTag:(s,t)=>v(`/admin/tags/${s}`,{method:"PUT",body:JSON.stringify(t)}),deleteTag:s=>v(`/admin/tags/${s}`,{method:"DELETE"}),getProducts:(s={})=>{const t=new URLSearchParams(s).toString();return v(`/admin/products${t?`?${t}`:""}`)},getProduct:s=>v(`/admin/products/${s}`),createProduct:s=>v("/admin/products",{method:"POST",body:s,headers:{}}),updateProduct:(s,t)=>v(`/admin/products/${s}`,{method:"PUT",body:t,headers:{}}),deleteProduct:s=>v(`/admin/products/${s}`,{method:"DELETE"})},cs={getProducts:(s={})=>{const t=new URLSearchParams(s).toString();return v(`/products${t?`?${t}`:""}`)},getProduct:(s,t="en")=>v(`/products/${s}?language=${t}`)},ye={getCategories:()=>v("/categories")},ve={getTags:()=>v("/tags")},Ne={getArchive:()=>v("/blog/archive")};function we({text:s="Grow your business with a new website."}){return e.jsx(e.Fragment,{children:e.jsx("span",{className:"wow charsAnimIn words chars splitting","data-splitting":"chars","aria-hidden":"true",style:{"--word-total":s.split(" ").length,"--char-total":s.split("").length,visibility:"visible"},children:s.trim().split(" ").map((t,i)=>e.jsxs(_.Fragment,{children:[e.jsx("span",{className:"word","data-word":"Grow",style:{"--word-index":i},children:t.split("").map((a,c)=>e.jsx("span",{className:"char","data-char":"G",style:{"--char-index":i+c},children:a},c))}),e.jsx("span",{className:"whitespace",children:" "})]},i))})})}we.propTypes={text:n.string};function ke(s){return b.useEffect(()=>{ie(document.querySelectorAll(".parallax-5"),{speed:.5})},[]),e.jsx("div",{...s,children:s.children})}ke.propTypes={children:n.node};const Se=({title:s,description:t,slug:i="",type:a="website",schema:c=null,keywords:p=[],image:h="https://devskills.ee/home.jpg",imageAlt:r=null,author:o="DevSkills",publishedAt:l="",modifiedAt:x="",alternateUrls:m=null,noIndex:g=!1,canonicalUrl:y=null})=>{const{i18n:u}=A(),f=u.language||"en",j="https://devskills.ee",d="DevSkills",w=(()=>{if(m)return m;const S=["en","et","fi","de","sv"],D={};return S.forEach($=>{i?D[$]=`${j}/${$}/${i}`:D[$]=`${j}/${$}`}),D})(),k=y||w[f]||`${j}/${f}`,C=()=>{const S={en:"en_US",et:"et_EE",fi:"fi_FI",de:"de_DE",sv:"sv_SE"},D={en:"English",et:"Estonian",fi:"Finnish",de:"German",sv:"Swedish"};return{locale:S[f]||"en_US",language:D[f]||"English"}},{locale:P,language:G}=C(),T=s?`${s} | ${d}`:`${d} - Professional Software Development Services`,U=r||`${d} - ${s||"Professional Software Development Services"}`,M=g?"noindex, nofollow":"index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1";return e.jsxs(O,{children:[e.jsx("title",{children:T}),e.jsx("meta",{name:"description",content:t}),e.jsx("link",{rel:"canonical",href:k}),p.length>0&&e.jsx("meta",{name:"keywords",content:p.join(", ")}),e.jsx("meta",{name:"language",content:G}),e.jsx("meta",{httpEquiv:"Content-Language",content:f}),Object.entries(w).map(([S,D])=>e.jsx("link",{rel:"alternate",hrefLang:S,href:D},S)),e.jsx("link",{rel:"alternate",hrefLang:"x-default",href:w.en||k}),e.jsx("meta",{property:"og:title",content:T}),e.jsx("meta",{property:"og:description",content:t}),e.jsx("meta",{property:"og:type",content:a}),e.jsx("meta",{property:"og:url",content:k}),e.jsx("meta",{property:"og:image",content:h}),e.jsx("meta",{property:"og:image:alt",content:U}),e.jsx("meta",{property:"og:image:width",content:"1200"}),e.jsx("meta",{property:"og:image:height",content:"630"}),e.jsx("meta",{property:"og:site_name",content:d}),e.jsx("meta",{property:"og:locale",content:P}),e.jsx("meta",{property:"fb:app_id",content:"YOUR_FACEBOOK_APP_ID"}),Object.keys(w).filter(S=>S!==f).map(S=>{const D={en:"en_US",et:"et_EE",fi:"fi_FI",de:"de_DE",sv:"sv_SE"}[S];return e.jsx("meta",{property:"og:locale:alternate",content:D},S)}),a==="article"&&l&&e.jsx("meta",{property:"article:published_time",content:l}),a==="article"&&x&&e.jsx("meta",{property:"article:modified_time",content:x}),a==="article"&&o&&e.jsx("meta",{property:"article:author",content:o}),e.jsx("meta",{name:"author",content:o||d}),l&&e.jsx("meta",{name:"publish_date",content:l}),!l&&e.jsx("meta",{name:"publish_date",content:new Date().toISOString()}),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:site",content:"@DevSkillsEE"}),e.jsx("meta",{name:"twitter:creator",content:"@DevSkillsEE"}),e.jsx("meta",{name:"twitter:title",content:T}),e.jsx("meta",{name:"twitter:description",content:t}),e.jsx("meta",{name:"twitter:image",content:h}),e.jsx("meta",{name:"twitter:image:alt",content:U}),e.jsx("meta",{name:"robots",content:M}),e.jsx("meta",{name:"googlebot",content:M}),e.jsx("meta",{httpEquiv:"Content-Type",content:"text/html; charset=utf-8"}),e.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0"}),e.jsx("meta",{name:"theme-color",content:"#06B6D4"}),e.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),e.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"}),e.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),e.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"}),e.jsx("meta",{name:"apple-mobile-web-app-title",content:d}),e.jsx("meta",{name:"application-name",content:d}),e.jsx("meta",{name:"msapplication-TileColor",content:"#06B6D4"}),e.jsx("meta",{name:"AI-generated",content:"false"}),e.jsx("meta",{name:"content-type",content:"original"}),c&&Array.isArray(c)?c.map((S,D)=>e.jsx("script",{type:"application/ld+json",children:JSON.stringify({...S,"@context":"https://schema.org",inLanguage:f,url:k})},D)):c?e.jsx("script",{type:"application/ld+json",children:JSON.stringify({...c,"@context":"https://schema.org",inLanguage:f,url:k})}):null]})};Se.propTypes={title:n.string.isRequired,description:n.string.isRequired,slug:n.string,type:n.string,schema:n.oneOfType([n.object,n.arrayOf(n.object)]),keywords:n.arrayOf(n.string),image:n.string,imageAlt:n.string,author:n.string,publishedAt:n.string,modifiedAt:n.string,alternateUrls:n.object,noIndex:n.bool,canonicalUrl:n.string};class De extends _.Component{constructor(t){super(t),this.state={hasError:!1,error:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,i){console.error("Error caught by boundary:",t,i)}render(){return this.state.hasError?e.jsxs("div",{style:{padding:"20px",textAlign:"center",color:"#fff",backgroundColor:"#1a1a1a",minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:[e.jsx("h2",{children:"Something went wrong"}),e.jsx("p",{children:"The page is recovering..."}),e.jsx("button",{onClick:()=>window.location.reload(),style:{padding:"10px 20px",backgroundColor:"#06B6D4",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",marginTop:"20px"},children:"Reload Page"})]}):this.props.children}}De.propTypes={children:n.node.isRequired};const Ce=[{id:1,href:"strong-portfolio-single.html",imgSrc:"/assets/images/demo-strong/portfolio/1.jpg",imgAlt:"Image Description",title:"Rise of Design",descr:"Branding, UI/UX Design"},{id:2,href:"strong-portfolio-single.html",imgSrc:"/assets/images/demo-strong/portfolio/2.jpg",imgAlt:"Image Description",title:"Amplitude",descr:"UI/UX Design, Development"},{id:3,href:"strong-portfolio-single.html",imgSrc:"/assets/images/demo-strong/portfolio/3.jpg",imgAlt:"Image Description",title:"Medium Scene",descr:"Branding, Design"},{id:4,href:"strong-portfolio-single.html",imgSrc:"/assets/images/demo-strong/portfolio/4.jpg",imgAlt:"Image Description",title:"Rise of Design",descr:"Branding, UI/UX Design"},{id:5,href:"strong-portfolio-single.html",imgSrc:"/assets/images/demo-strong/portfolio/5.jpg",imgAlt:"Image Description",title:"Amplitude",descr:"UI/UX Design, Development"},{id:6,href:"strong-portfolio-single.html",imgSrc:"/assets/images/demo-strong/portfolio/6.jpg",imgAlt:"Image Description",title:"Medium Scene",descr:"Branding, Design"}],Ae=[{id:7,className:"work-item mt-90 mt-md-0 mix development",href:"/assets/images/portfolio/masonry/full-project-1.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-1.jpg",imgAlt:"Work Description",delay:"1s",title:"Medium Scene",description:"Lightbox"},{id:8,className:"work-item mix branding design",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-2.jpg",imgAlt:"Work Description",delay:"1s",title:"Rise of Design",description:"External Page"},{id:9,className:"work-item mt-90 mt-md-0 mix branding",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-3.jpg",imgAlt:"Work Description",delay:"1s",title:"Visual Stranger",description:"External Page"},{id:10,className:"work-item mix design development",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-4.jpg",imgAlt:"Work Description",delay:"1s",title:"Amplitude",description:"External Page"},{id:11,className:"work-item mix design",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-5.jpg",imgAlt:"Work Description",delay:"1s",title:"Super Awards",description:"External Page"},{id:12,className:"work-item mix design branding",href:"/assets/images/portfolio/masonry/full-project-6.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-6.jpg",imgAlt:"Work Description",delay:"1s",title:"Design System",description:"Lightbox"},{id:13,className:"work-item mix mix design",href:"/assets/images/portfolio/masonry/full-project-7.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-6.jpg",imgAlt:"Work Description",delay:"1s",title:"Amplitude",description:"External Page"},{id:14,className:"work-item mix design development",href:"/assets/images/portfolio/masonry/full-project-8.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-6.jpg",imgAlt:"Work Description",delay:"1s",title:"Super Awards",description:"External Page"}],Pe=[{id:15,imageUrl:"/assets/images/demo-bold/portfolio/1.jpg",title:"Medium Scene",description:"Lorem ipsum dolor siter amet consectetur adipiscing elit sed do eiusmod tempor incididunt labore dolore magna aliqua.",link:"bold-portfolio-single.html",categories:["development"]},{id:16,imageUrl:"/assets/images/demo-bold/portfolio/2.jpg",title:"Rise of Design",description:"Proin elementum ipsum vel mauris pellentesque accumsan. Nulla in erat ligula vivamus sed egestas elit, sit amet convallis metus.",link:"bold-portfolio-single.html",categories:["branding"]},{id:17,imageUrl:"/assets/images/demo-bold/portfolio/3.jpg",title:"Visual Stranger",description:"Suspendisse scelerisque convallis nibh. Maecenas bibendum porta mattis. Donec quis nibh porta dolor ultrices bibendum vel quis leo.",link:"bold-portfolio-single.html",categories:["design","development"]},{id:18,imageUrl:"/assets/images/demo-bold/portfolio/4.jpg",title:"Amplitude",description:"Aliquam tempus nunc nec rutrum malesuada. Proin pulvinar augue quis pharetra vulputate. Sed lacinia convallis orci vitae condimentum.",link:"bold-portfolio-single.html",categories:["branding","design"]},{id:19,imageUrl:"/assets/images/demo-bold/portfolio/5.jpg",title:"Super Awards",description:"Praesent est lacus, fringilla et justo vel, scelerisque aliquet elit. Mauris malesuada eleifend sapien irere semper a orci ac turpis luctus.",link:"bold-portfolio-single.html",categories:["design","development"]}],Le=[{id:20,imgSrc:"/assets/images/demo-brutalist/portfolio/1.jpg",imgWidth:700,imgHeight:848,title:"Medium Scene",description:"Take maximus ligula semper metus pellente mattis. Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet adipiscing elit."},{id:21,imgSrc:"/assets/images/demo-brutalist/portfolio/2.jpg",imgWidth:848,imgHeight:700,title:"Rise of Design",description:"Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet, cetere adipiscing elit. Maximus ligula semper metus pellentesque mattis."},{id:22,imgSrc:"/assets/images/demo-brutalist/portfolio/3.jpg",imgWidth:700,imgHeight:848,title:"Visual Stranger",description:"Curabitur iaculis accumsan augue, finibus mauris pretium eu. Duis placerat ex gravida nibh tristique porta nulla facilisi."},{id:23,imgSrc:"/assets/images/demo-brutalist/portfolio/4.jpg",imgWidth:848,imgHeight:700,title:"Rise of Design",description:"Take maximus ligula semper metus pellente mattis. Maecenas volutpat, diam enim. Lorem ipsum dolor sit amet adipiscing elit."},{id:24,imgSrc:"/assets/images/demo-brutalist/portfolio/5.jpg",imgWidth:700,imgHeight:848,title:"Amplitude",description:"Posuere felis id arcu blandit sagittis. Eleifeni vestibulum purus, sit amet vulputate risusece fusce aliquet quam eget neque."}],$e=[{id:25,imageSrc:"/assets/images/demo-corporate/portfolio/project-1.jpg",title:"How Marketing Wire Support Increased Data Accuracy by 70%",number:"70%",description:"growth with Resonance"},{id:26,imageSrc:"/assets/images/demo-corporate/portfolio/project-2.jpg",title:"How Surface Mobility Increased Sales 3X During the Latest Six Months",number:"3x",description:"sales increased with Resonance"},{id:27,imageSrc:"/assets/images/demo-corporate/portfolio/project-3.jpg",title:"How Gen Machine Uses Automations to Grow Their Subscriber Base",number:"Zero",description:"negative reviews with Resonance"}],z=[{id:28,imageSrc:"/assets/images/demo-elegant/portfolio/1.jpg",title:"Medium Scene",type:"Lightbox",categories:["development"]},{id:29,imageSrc:"/assets/images/demo-elegant/portfolio/2.jpg",title:"Rise of Design",type:"External Page",categories:["branding","design"]},{id:30,imageSrc:"/assets/images/demo-elegant/portfolio/3.jpg",title:"Visual Stranger",type:"External Page",categories:["branding"]},{id:31,imageSrc:"/assets/images/demo-elegant/portfolio/4.jpg",title:"Amplitude",type:"External Page",categories:["design","development"]},{id:32,imageSrc:"/assets/images/demo-elegant/portfolio/5.jpg",title:"Super Awards",type:"External Page",categories:["design"]},{id:33,imageSrc:"/assets/images/demo-elegant/portfolio/6.jpg",title:"Design System",type:"Lightbox",categories:["design","branding"]},{id:34,imageSrc:"/assets/images/demo-elegant/portfolio/7.jpg",title:"Rise of Design",type:"External Page",categories:["branding","design"]},{id:35,imageSrc:"/assets/images/demo-elegant/portfolio/8.jpg",title:"Medium Scene",type:"Lightbox",categories:["development"]}],Ee=[{id:36,categories:["development"],imgSrc:"/assets/images/demo-fancy/portfolio/project-1.jpg",title:"Medium Scene",description:"Lightbox",lightbox:!0,lightboxLink:"/assets/images/demo-fancy/portfolio/project-1-large.jpg"},{id:37,categories:["branding","design"],imgSrc:"/assets/images/demo-fancy/portfolio/project-2.jpg",title:"Rise of Design",description:"External Page",lightbox:!1,externalLink:"fancy-portfolio-single.html"},{id:38,categories:["branding"],imgSrc:"/assets/images/demo-fancy/portfolio/project-3.jpg",title:"Visual Stranger",description:"External Page",lightbox:!1,externalLink:"fancy-portfolio-single.html"},{id:39,categories:["design","development"],imgSrc:"/assets/images/demo-fancy/portfolio/project-4.jpg",title:"Amplitude",description:"External Page",lightbox:!1,externalLink:"fancy-portfolio-single.html"},{id:40,categories:["design"],imgSrc:"/assets/images/demo-fancy/portfolio/project-5.jpg",title:"Super Awards",description:"External Page",lightbox:!1,externalLink:"fancy-portfolio-single.html"},{id:41,categories:["design","branding"],imgSrc:"/assets/images/demo-fancy/portfolio/project-6.jpg",title:"Design System",description:"Lightbox",lightbox:!0,lightboxLink:"/assets/images/demo-fancy/portfolio/project-6-large.jpg"}],Te=[{id:42,categories:["development"],imgSrc:"/assets/images/demo-gradient/portfolio/project-1.jpg",title:"Medium Scene",description:"Lightbox",dataWowDelay:"1s"},{id:43,categories:["branding","design"],imgSrc:"/assets/images/demo-gradient/portfolio/project-2.jpg",title:"Rise of Design",description:"External Page",dataWowDelay:"1s"},{id:44,categories:["branding"],imgSrc:"/assets/images/demo-gradient/portfolio/project-3.jpg",title:"Visual Stranger",description:"External Page",dataWowDelay:"1s"},{id:45,categories:["design","development"],imgSrc:"/assets/images/demo-gradient/portfolio/project-4.jpg",title:"Amplitude",description:"External Page",dataWowDelay:"1s"},{id:46,categories:["design"],imgSrc:"/assets/images/demo-gradient/portfolio/project-5.jpg",title:"Super Awards",description:"External Page",dataWowDelay:"1s"},{id:47,categories:["design","branding"],imgSrc:"/assets/images/demo-gradient/portfolio/project-6.jpg",title:"Design System",description:"Lightbox",dataWowDelay:"1s"}],Ie=[{id:48,imageSrc:"/assets/images/demo-modern/portfolio/1.jpg",title:"Medium Scene",categories:"Branding, Design",align:"text-center"},{id:49,imageSrc:"/assets/images/demo-modern/portfolio/2.jpg",title:"The Rise of Design",categories:"Branding, Design",align:"text-end"},{id:50,imageSrc:"/assets/images/demo-modern/portfolio/3.jpg",title:"Visual Stranger",categories:"Branding, Design, Development",align:"text-start"},{id:51,imageSrc:"/assets/images/demo-modern/portfolio/4.jpg",title:"Amplitude Studios",categories:"Branding, Design",align:"text-end"},{id:52,imageSrc:"/assets/images/demo-modern/portfolio/5.jpg",title:"Super Awards",categories:"Design, Development",align:"text-center"}],Re=[{id:53,className:"work-item",categories:["mix","development"],imgSrc:"/assets/images/demo-slick/portfolio/project-1.jpg",imgAlt:"Work Description",title:"Medium Scene",description:"Lightbox",isLightbox:!0},{id:54,className:"work-item",categories:["mt-80","mt-sm-0","mix","branding","design"],imgSrc:"/assets/images/demo-slick/portfolio/project-2.jpg",imgAlt:"Work Description",title:"Rise of Design",description:"External Page",isLightbox:!1},{id:55,className:"work-item",categories:["mix","branding"],imgSrc:"/assets/images/demo-slick/portfolio/project-3.jpg",imgAlt:"Work Description",title:"Visual Stranger",description:"External Page",isLightbox:!1},{id:56,className:"work-item",categories:["mix","design","development"],imgSrc:"/assets/images/demo-slick/portfolio/project-4.jpg",imgAlt:"Work Description",title:"Amplitude",description:"External Page",isLightbox:!1},{id:57,className:"work-item",categories:["mix","design"],imgSrc:"/assets/images/demo-slick/portfolio/project-5.jpg",imgAlt:"Work Description",title:"Super Awards",description:"External Page",isLightbox:!1},{id:58,className:"work-item",categories:["mix","design","branding"],imgSrc:"/assets/images/demo-slick/portfolio/project-6.jpg",imgAlt:"Work Description",title:"Design System",description:"Lightbox",isLightbox:!0}],_e=[{id:59,imgSrc:"/assets/images/demo-strong/portfolio/1.jpg",imgAlt:"Image Description",title:"Rise of Design",description:"Branding, UI/UX Design"},{id:60,imgSrc:"/assets/images/demo-strong/portfolio/2.jpg",imgAlt:"Image Description",title:"Amplitude",description:"UI/UX Design, Development"},{id:61,imgSrc:"/assets/images/demo-strong/portfolio/3.jpg",imgAlt:"Image Description",title:"Medium Scene",description:"Branding, Design"},{id:62,imgSrc:"/assets/images/demo-strong/portfolio/4.jpg",imgAlt:"Image Description",title:"Visual Stranger",description:"Branding, UI/UX Design"},{id:63,imgSrc:"/assets/images/demo-strong/portfolio/5.jpg",imgAlt:"Image Description",title:"Super Awards",description:"UI/UX Design, Development"},{id:64,imgSrc:"/assets/images/demo-strong/portfolio/6.jpg",imgAlt:"Image Description",title:"Design System",description:"Branding, Design"}],Oe=[{id:65,title:"How Marketing Wire Support Increased Data Accuracy by 70%",imageUrl:"/assets/images/demo-corporate/portfolio/project-1.jpg",number:"70%",description:"growth with Resonance"},{id:66,title:"How Surface Mobility Increased Sales 3X During the Latest Six Months",imageUrl:"/assets/images/demo-corporate/portfolio/project-2.jpg",number:"3x",description:"sales increased with Resonance"},{id:67,title:"How Gen Machine Uses Automations to Grow Their Subscriber Base",imageUrl:"/assets/images/demo-corporate/portfolio/project-3.jpg",number:"Zero",description:"negative reviews with Resonance"},{id:68,title:"How Surface Mobility Increased Sales 3X During the Latest Six Months",imageUrl:"/assets/images/demo-corporate/portfolio/project-4.jpg",number:"2x",description:"sales increased with Resonance"},{id:69,title:"How Gen Machine Uses Automations to Grow Their Subscriber Base",imageUrl:"/assets/images/demo-corporate/portfolio/project-5.jpg",number:"Zero",description:"negative reviews with Resonance"},{id:70,title:"How Marketing Wire Support Increased Data Accuracy by 70%",imageUrl:"/assets/images/demo-corporate/portfolio/project-6.jpg",number:"80%",description:"growth with Resonance"}],Ue=[{id:71,className:"work-item mix development",href:"/assets/images/portfolio/masonry/full-project-1.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-1.jpg",imgAlt:"Work Description",delay:"1s",title:"Medium Scene",description:"Lightbox"},{id:72,className:"work-item mix branding design",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-2.jpg",imgAlt:"Work Description",delay:"1s",title:"Rise of Design",description:"External Page"},{id:73,className:"work-item  mix branding",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-3.jpg",imgAlt:"Work Description",delay:"1s",title:"Visual Stranger",description:"External Page"},{id:74,className:"work-item mix design development",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-4.jpg",imgAlt:"Work Description",delay:"1s",title:"Amplitude",description:"External Page"},{id:75,className:"work-item mix design",href:"main-portfolio-single-1.html",linkClassName:"work-ext-link",imgSrc:"/assets/images/portfolio/masonry/projects-5.jpg",imgAlt:"Work Description",delay:"1s",title:"Super Awards",description:"External Page"},{id:76,className:"work-item mix design branding",href:"/assets/images/portfolio/masonry/full-project-6.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-6.jpg",imgAlt:"Work Description",delay:"1s",title:"Design System",description:"Lightbox"},{id:77,className:"work-item mix mix design",href:"/assets/images/portfolio/masonry/full-project-7.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-7.jpg",imgAlt:"Work Description",delay:"1s",title:"Amplitude",description:"External Page"},{id:78,className:"work-item mix design development",href:"/assets/images/portfolio/masonry/full-project-8.jpg",linkClassName:"work-lightbox-link mfp-image",imgSrc:"/assets/images/portfolio/masonry/projects-8.jpg",imgAlt:"Work Description",delay:"1s",title:"Super Awards",description:"External Page"}],Me=[{id:79,type:"lightbox",mix:"development",href:"/assets/images/portfolio/full-project-1.jpg",imgSrc:"/assets/images/portfolio/projects-1.jpg",imgAlt:"Work Description",title:"Green Leaf",descr:"Lightbox"},{id:80,type:"external",mix:"branding design",href:"main-portfolio-single-1.html",imgSrc:"/assets/images/portfolio/projects-2.jpg",imgAlt:"Work Description",title:"Photo Lighting",descr:"External Page"},{id:81,type:"external",mix:"branding",href:"main-portfolio-single-1.html",imgSrc:"/assets/images/portfolio/projects-3.jpg",imgAlt:"Work Description",title:"Green Branch",descr:"External Page"},{id:82,type:"external",mix:"design development",href:"main-portfolio-single-1.html",imgSrc:"/assets/images/portfolio/projects-4.jpg",imgAlt:"Work Description",title:"White Chair",descr:"External Page"},{id:83,type:"external",mix:"design",href:"main-portfolio-single-1.html",imgSrc:"/assets/images/portfolio/projects-5.jpg",imgAlt:"Work Description",title:"White Table",descr:"External Page"},{id:84,type:"lightbox",mix:"design branding",href:"/assets/images/portfolio/full-project-6.jpg",imgSrc:"/assets/images/portfolio/projects-6.jpg",imgAlt:"Work Description",title:"The Book",descr:"Lightbox"},{id:85,type:"external",mix:"branding",href:"main-portfolio-single-1.html",imgSrc:"/assets/images/portfolio/projects-7.jpg",imgAlt:"Work Description",title:"Green Branch",descr:"External Page"},{id:86,type:"external",mix:"design development",href:"main-portfolio-single-1.html",imgSrc:"/assets/images/portfolio/projects-8.jpg",imgAlt:"Work Description",title:"White Chair",descr:"External Page"}],ds=[...Ce,...Ae,...Pe,...Le,...$e,...z,...Ee,...Te,...Ie,...Re,..._e,...Oe,...Ue,...Me];function We({meta:s}){return e.jsx(X,{children:e.jsxs(O,{children:[e.jsx("title",{children:s==null?void 0:s.title}),e.jsx("meta",{name:"description",content:s==null?void 0:s.description})]})})}We.propTypes={meta:n.shape({title:n.string,description:n.string})};function Be({className:s,currentPage:t=1,totalPages:i=1,onPageChange:a=()=>{}}){const c=r=>{r>=1&&r<=i&&r!==t&&a(r)};if(i<=1)return null;const h=(()=>{const r=[];if(i<=5)for(let l=1;l<=i;l++)r.push(l);else if(t<=3){for(let l=1;l<=4;l++)r.push(l);r.push("..."),r.push(i)}else if(t>=i-2){r.push(1),r.push("...");for(let l=i-3;l<=i;l++)r.push(l)}else{r.push(1),r.push("...");for(let l=t-1;l<=t+1;l++)r.push(l);r.push("..."),r.push(i)}return r})();return e.jsxs("div",{className:s||"pagination justify-content-center",children:[e.jsxs("a",{onClick:()=>c(t-1),className:t===1?"disabled":"",style:{cursor:t===1?"not-allowed":"pointer"},children:[e.jsx("i",{className:"mi-chevron-left"}),e.jsx("span",{className:"visually-hidden",children:"Previous page"})]}),h.map((r,o)=>r==="..."?e.jsx("span",{className:"no-active",children:"..."},`ellipsis-${o}`):e.jsx("a",{onClick:()=>c(r),className:t===r?"active":"",style:{cursor:"pointer"},children:r},r)),e.jsxs("a",{onClick:()=>c(t+1),className:t===i?"disabled":"",style:{cursor:t===i?"not-allowed":"pointer"},children:[e.jsx("i",{className:"mi-chevron-right"}),e.jsx("span",{className:"visually-hidden",children:"Next page"})]})]})}Be.propTypes={className:n.string,currentPage:n.number,totalPages:n.number,onPageChange:n.func};function ms(){return e.jsxs("div",{className:"container relative",children:[e.jsx("div",{className:"text-center mb-60 mb-sm-40",children:e.jsx("h2",{className:"section-title-small",children:"Related Projects"})}),e.jsx("ul",{className:"works-grid work-grid-4 work-grid-gut-sm hide-titles",id:"work-grid",children:e.jsxs(K,{children:[z.slice(1,5).map((s,t)=>e.jsx("li",{className:`work-item mix ${s.categories.join(" ")}`,children:s.type==="Lightbox"?e.jsx(Q,{original:s.imageSrc,thumbnail:s.imageSrc,width:650,height:773,children:({ref:i,open:a})=>e.jsxs("a",{onClick:a,className:"work-lightbox-link mfp-image",children:[e.jsxs("div",{className:"work-img",children:[e.jsx("div",{className:"work-img-bg wow-p scalexIn"}),e.jsx("img",{src:s.imageSrc,ref:i,width:650,height:761,alt:"Work Description"})]}),e.jsxs("div",{className:"work-intro",children:[e.jsx("h3",{className:"work-title",children:s.title}),e.jsx("div",{className:"work-descr",children:s.type})]})]})}):e.jsxs(R,{to:`/elegant-portfolio-single/${s.id}`,className:"work-ext-link",children:[e.jsxs("div",{className:"work-img",children:[e.jsx("div",{className:"work-img-bg"}),e.jsx("img",{src:s.imageSrc,width:650,height:761,alt:"Work Description"})]}),e.jsxs("div",{className:"work-intro",children:[e.jsx("h3",{className:"work-title",children:s.title}),e.jsx("div",{className:"work-descr",children:s.type})]})]})},t))," "]})})]})}const gs=({images:s,productTitle:t})=>{const[i,a]=b.useState(0),[c,p]=b.useState(!1);if(!s||s.length===0)return null;const h=s[i],r=s.length>1,o=()=>{a(u=>(u+1)%s.length)},l=()=>{a(u=>(u-1+s.length)%s.length)},x=u=>{a(u)},m=()=>{p(!0)},g=()=>{p(!1)},y=u=>`http://localhost:4004/uploads/product-images/${u}`;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"product-gallery mb-60 mb-xs-40",children:[e.jsxs("div",{className:"position-relative",children:[e.jsx("img",{src:y(h.filename),alt:h.alt||t,className:"w-100 rounded cursor-pointer",style:{maxHeight:"500px",objectFit:"cover"},onClick:m}),r&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3",onClick:l,style:{zIndex:2},children:e.jsx("i",{className:"mi-arrow-left"})}),e.jsx("button",{className:"btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3",onClick:o,style:{zIndex:2},children:e.jsx("i",{className:"mi-arrow-right"})})]})]}),r&&e.jsx("div",{className:"gallery-thumbnails mt-3",children:e.jsx("div",{className:"d-flex gap-2 flex-wrap",children:s.map((u,f)=>e.jsx("div",{className:`thumbnail-item cursor-pointer ${f===i?"active":""}`,onClick:()=>x(f),children:e.jsx("img",{src:y(u.filename),alt:u.alt||`${t} ${f+1}`,className:"rounded",style:{width:"80px",height:"60px",objectFit:"cover",border:f===i?"2px solid #007bff":"2px solid transparent",opacity:f===i?1:.7}})},u.id))})})]}),c&&e.jsx("div",{className:"fullscreen-gallery position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center",style:{backgroundColor:"rgba(0, 0, 0, 0.95)",zIndex:9999},onClick:g,children:e.jsxs("div",{className:"position-relative",onClick:u=>u.stopPropagation(),children:[e.jsx("img",{src:y(h.filename),alt:h.alt||t,className:"img-fluid",style:{maxHeight:"90vh",maxWidth:"90vw"}}),e.jsx("button",{className:"btn btn-light btn-lg position-absolute top-0 end-0 m-3",onClick:g,style:{zIndex:1e4},children:"×"}),r&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"btn btn-light btn-lg position-absolute top-50 start-0 translate-middle-y ms-3",onClick:l,style:{zIndex:1e4},children:e.jsx("i",{className:"mi-arrow-left"})}),e.jsx("button",{className:"btn btn-light btn-lg position-absolute top-50 end-0 translate-middle-y me-3",onClick:o,style:{zIndex:1e4},children:e.jsx("i",{className:"mi-arrow-right"})})]})]})}),e.jsx("style",{jsx:!0,children:`
        .cursor-pointer {
          cursor: pointer;
        }
        .thumbnail-item:hover img {
          opacity: 1 !important;
        }
        .fullscreen-gallery {
          backdrop-filter: blur(5px);
        }
      `})]})};function qe({comments:s=[]}){const{t}=A(),i=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return!s||s.length===0?e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-muted",children:t("blog.comments.no_comments")})}):e.jsx(e.Fragment,{children:s.map((a,c)=>e.jsx("li",{className:"media comment-item",children:e.jsxs("div",{className:"media-body",children:[e.jsxs("div",{className:"comment-item-data",children:[e.jsx("div",{className:"comment-author",children:e.jsx("a",{href:"#",children:a.author})}),i(a.createdAt)," ",e.jsx("span",{className:"separator",children:"—"}),e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"fa fa-comment"})," ",t("blog.comments.reply")]})]}),e.jsx("p",{children:a.content}),a.replies&&a.replies.length>0&&a.replies.map(p=>e.jsxs("div",{className:"media comment-item",children:[e.jsx("a",{className:"float-start",href:"#",children:e.jsx("img",{className:"media-object comment-avatar",src:"/assets/images/user-avatar.png",alt:"",width:100,height:100})}),e.jsxs("div",{className:"media-body",children:[e.jsxs("div",{className:"comment-item-data",children:[e.jsx("div",{className:"comment-author",children:e.jsx("a",{href:"#",children:p.author})}),i(p.createdAt)," ",e.jsx("span",{className:"separator",children:"—"}),e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"fa fa-comment"})," ",t("blog.comments.reply")]})]}),e.jsx("p",{children:p.content})]})]},p.id))]})},c))})}qe.propTypes={comments:n.arrayOf(n.shape({id:n.string.isRequired,author:n.string.isRequired,content:n.string.isRequired,createdAt:n.string.isRequired,replies:n.arrayOf(n.shape({id:n.string.isRequired,author:n.string.isRequired,content:n.string.isRequired,createdAt:n.string.isRequired}))}))};function Ve({blogSlug:s,onCommentSubmitted:t}){const{t:i}=A(),[a,c]=b.useState({author:"",email:"",website:"",content:""}),[p,h]=b.useState(!1),[r,o]=b.useState({type:"",text:""}),l=m=>{const{name:g,value:y}=m.target;c(u=>({...u,[g]:y}))},x=async m=>{var g;m.preventDefault(),h(!0),o({type:"",text:""});try{const y=await F.createComment(s,a);y.response.ok?(o({type:"success",text:i("blog.comment.success")||"Comment submitted successfully! It will be reviewed before being published."}),c({author:"",email:"",website:"",content:""}),t&&t()):o({type:"error",text:((g=y.data)==null?void 0:g.message)||i("blog.comment.error")||"Failed to submit comment. Please try again."})}catch(y){console.error("Comment submission error:",y),o({type:"error",text:i("blog.comment.error")||"Failed to submit comment. Please try again."})}finally{h(!1)}};return e.jsxs("form",{className:"form",onSubmit:x,children:[e.jsxs("div",{className:"row mb-30 mb-md-20",children:[e.jsxs("div",{className:"col-md-6 mb-md-20",children:[e.jsxs("label",{htmlFor:"name",children:[i("blog.comments.name")," *"]}),e.jsx("input",{type:"text",name:"author",id:"name",className:"input-lg round form-control",placeholder:i("blog.comments.name_placeholder"),maxLength:100,value:a.author,onChange:l,required:!0,"aria-required":"true"})]}),e.jsxs("div",{className:"col-md-6",children:[e.jsxs("label",{htmlFor:"email",children:[i("blog.comments.email")," *"]}),e.jsx("input",{type:"email",name:"email",id:"email",className:"input-lg round form-control",placeholder:i("blog.comments.email_placeholder"),maxLength:100,value:a.email,onChange:l,required:!0,"aria-required":"true"})]})]}),e.jsxs("div",{className:"mb-30 mb-md-20",children:[e.jsx("label",{htmlFor:"website",children:i("blog.comments.website")}),e.jsx("input",{type:"text",name:"website",id:"website",className:"input-lg round form-control",placeholder:i("blog.comments.website_placeholder"),maxLength:100,value:a.website,onChange:l})]}),e.jsxs("div",{className:"mb-30 mb-md-20",children:[e.jsx("label",{htmlFor:"comment",children:i("blog.comments.message")}),e.jsx("textarea",{name:"content",id:"comment",className:"input-lg round form-control",rows:6,placeholder:i("blog.comments.message_placeholder"),maxLength:1e3,value:a.content,onChange:l,required:!0})]}),r.text&&e.jsx("div",{className:`alert ${r.type==="success"?"alert-success":"alert-danger"} mb-30`,children:r.text}),e.jsxs("button",{type:"submit",className:"submit_btn link-hover-anim link-circle-1 align-middle","data-link-animate":"y",disabled:p,children:[e.jsxs("span",{className:"link-strong link-strong-unhovered",children:[i(p?"blog.comments.submitting":"blog.comments.submit")," ",e.jsx("i",{className:"mi-arrow-right size-18 align-middle","aria-hidden":"true"})]}),e.jsxs("span",{className:"link-strong link-strong-hovered","aria-hidden":"true",children:[i(p?"blog.comments.submitting":"blog.comments.submit")," ",e.jsx("i",{className:"mi-arrow-right size-18 align-middle","aria-hidden":"true"})]})]}),e.jsxs("div",{className:"form-tip form-tip-2   bg-gray-light-1 round mt-30 p-3",children:["* - these fields are required. By sending the form you agree to the"," ",e.jsx("a",{href:"#",children:"Terms & Conditions"})," and"," ",e.jsx("a",{href:"#",children:"Privacy Policy"}),"."]})]})}Ve.propTypes={blogSlug:n.string.isRequired,onCommentSubmitted:n.func};function Fe({searchInputClass:s="form-control input-md search-field input-circle"}){const{t,i18n:i}=A(),a=i.language||"et",[c,p]=b.useState([]),[h,r]=b.useState([]),[o,l]=b.useState([]),[x,m]=b.useState([]),[g,y]=b.useState("");b.useEffect(()=>{(async()=>{var d;try{const N=await ye.getCategories();N.response.ok&&N.data&&p(N.data.data||N.data.categories||[]);const w=await ve.getTags();w.response.ok&&w.data&&r(w.data.data||w.data.tags||[]);const k=await Ne.getArchive();k.response.ok&&k.data&&l(k.data.archive||[]);const C=await F.getBlogPosts(a,1,5);if(C.response.ok&&C.data){const P=((d=C.data.data)==null?void 0:d.data)||C.data.data||[];m(Array.isArray(P)?P:[])}}catch(N){console.error("Error fetching widget data:",N)}})()},[a]);const u=j=>{j.preventDefault(),g.trim()&&(window.location.href=`/blog?search=${encodeURIComponent(g)}`)},f=(j,d)=>{var w;const N=(w=j.translations)==null?void 0:w.find(k=>k.language===a);return(N==null?void 0:N[d])||""};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"widget",children:e.jsx("form",{onSubmit:u,className:"form",children:e.jsxs("div",{className:"search-wrap",children:[e.jsxs("button",{className:"search-button animate",type:"submit",title:"Start Search",children:[e.jsx("i",{className:"mi-search size-18"}),e.jsx("span",{className:"visually-hidden",children:"Start search"})]}),e.jsx("input",{type:"text",className:s,placeholder:t("blog.search_placeholder")||"Search...",value:g,onChange:j=>y(j.target.value),required:!0})]})})}),e.jsxs("div",{className:"widget",children:[e.jsx("h3",{className:"widget-title",children:t("blog.categories")}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-menu",children:c.length>0?c.map(j=>{var d;return e.jsxs("li",{children:[e.jsx("a",{href:"#",title:"",onClick:N=>{N.preventDefault(),window.location.href=`/${a}/blog?category=${j.slug}`},children:j.name}),e.jsxs("small",{children:[" - ",((d=j._count)==null?void 0:d.blogPosts)||0," "]})]},j.id)}):e.jsx("li",{children:t("blog.no_categories")})})})]}),e.jsxs("div",{className:"widget",children:[e.jsx("h3",{className:"widget-title",children:t("blog.tags")}),e.jsx("div",{className:"widget-body",children:e.jsx("div",{className:"tags",children:h.length>0?h.map(j=>e.jsx("a",{href:"#",onClick:d=>{d.preventDefault(),window.location.href=`/${a}/blog?tag=${j.slug}`},children:j.name},j.id)):e.jsx("span",{children:t("blog.no_tags")})})})]}),e.jsxs("div",{className:"widget",children:[e.jsx("h3",{className:"widget-title",children:t("blog.latest_posts")}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-posts",children:x.length>0?x.map((j,d)=>{var N;return e.jsxs("li",{className:"clearfix",children:[e.jsx("a",{href:`/${a}/blog-single/${j.slug}`,children:e.jsx("img",{src:j.featuredImage||"/assets/images/demo-elegant/blog/1.jpg",height:140,style:{height:"fit-content"},alt:f(j,"title"),width:100,className:"widget-posts-img"})}),e.jsxs("div",{className:"widget-posts-descr",children:[e.jsx("a",{href:`/${a}/blog-single/${j.slug}`,title:"",children:f(j,"title")}),e.jsxs("span",{children:[t("blog.posted_by")," ",((N=j.author)==null?void 0:N.name)||"DevSkills Team"]})]})]},j.id||d)}):e.jsx("li",{children:t("blog.no_recent_posts")})})})]}),e.jsxs("div",{className:"widget",children:[e.jsx("h3",{className:"widget-title",children:t("blog.archive")}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-menu",children:o.length>0?o.map((j,d)=>e.jsxs("li",{children:[e.jsxs("a",{href:"#",title:"",children:[j.monthName," ",j.year]}),e.jsxs("small",{children:[" - ",j.count," "]})]},d)):e.jsx("li",{children:t("blog.no_archive")})})})]})]})}Fe.propTypes={searchInputClass:n.string};function ps(){const[s,t]=b.useState(!1),{t:i}=A();return e.jsxs(e.Fragment,{children:[e.jsx("a",{href:"#",className:`map-section ${s?"js-active":""}`,children:e.jsxs("div",{className:"map-toggle wow fadeInUpShort","aria-hidden":"true",children:[e.jsx("div",{className:"mt-icon",children:e.jsx("i",{className:"mi-location"})}),e.jsxs("div",{className:"mt-text",children:[e.jsxs("div",{onClick:()=>t(a=>!a),className:"mt-open",children:[i("contact.map.open")," ",e.jsx("i",{className:"mt-open-icon"})]}),e.jsxs("div",{onClick:()=>t(a=>!a),className:"mt-close",children:[i("contact.map.close")," ",e.jsx("i",{className:"mt-close-icon"})]})]})]})}),e.jsx("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2028.7573128553793!2d24.7553!3d59.4372!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4692935c7d5dfa3b%3A0x4b0f5f5c5d9c1f0!2sTornim%C3%A4e%20tn%207%2C%2010145%20Tallinn!5e0!3m2!1sen!2see!4v1684450429598!5m2!1sen!2see",width:600,height:450,loading:"lazy",style:{border:0},allowFullScreen:"","aria-hidden":"false",tabIndex:0})]})}const He=({title:s="DevSkills - Professional Software Development Services",description:t="DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.",canonical:i="https://devskills.ee",image:a="https://devskills.ee/home.jpg",imageAlt:c="DevSkills Development Studio",imageWidth:p="1162",imageHeight:h="630",type:r="website",schema:o=null,twitterHandle:l="@DevSkillsEE",publishedAt:x="",modifiedAt:m="",author:g="DevSkills",keywords:y=["software development","custom software","web development","AI solutions","white label software","business comanager"],locale:u="en_US"})=>{const f=`${s} | DevSkills`;return e.jsxs(O,{children:[e.jsx("title",{children:f}),e.jsx("meta",{name:"description",content:t}),e.jsx("link",{rel:"canonical",href:i}),e.jsx("meta",{name:"keywords",content:y.join(", ")}),e.jsx("meta",{property:"og:title",content:f}),e.jsx("meta",{property:"og:description",content:t}),e.jsx("meta",{property:"og:type",content:r}),e.jsx("meta",{property:"og:url",content:i}),e.jsx("meta",{property:"og:image",content:a}),e.jsx("meta",{property:"og:image:alt",content:c}),e.jsx("meta",{property:"og:image:width",content:p}),e.jsx("meta",{property:"og:image:height",content:h}),e.jsx("meta",{property:"og:site_name",content:"DevSkills"}),e.jsx("meta",{property:"og:locale",content:u}),e.jsx("meta",{property:"fb:app_id",content:"YOUR_FACEBOOK_APP_ID"}),r==="article"&&x&&e.jsx("meta",{property:"article:published_time",content:x}),r==="article"&&m&&e.jsx("meta",{property:"article:modified_time",content:m}),r==="article"&&g&&e.jsx("meta",{property:"article:author",content:g}),e.jsx("meta",{name:"author",content:g||"DevSkills"}),x&&e.jsx("meta",{name:"publish_date",content:x}),!x&&e.jsx("meta",{name:"publish_date",content:new Date().toISOString()}),e.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),e.jsx("meta",{name:"twitter:site",content:l}),e.jsx("meta",{name:"twitter:creator",content:l}),e.jsx("meta",{name:"twitter:title",content:f}),e.jsx("meta",{name:"twitter:description",content:t}),e.jsx("meta",{name:"twitter:image",content:a}),e.jsx("meta",{name:"twitter:image:alt",content:c}),e.jsx("meta",{name:"robots",content:"index, follow, max-image-preview:large"}),e.jsx("meta",{name:"googlebot",content:"index, follow"}),e.jsx("meta",{httpEquiv:"Content-Type",content:"text/html; charset=utf-8"}),e.jsx("meta",{name:"language",content:"English"}),e.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0"}),e.jsx("meta",{name:"theme-color",content:"#06B6D4"}),e.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),e.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"}),o&&Array.isArray(o)?o.map((j,d)=>e.jsx("script",{type:"application/ld+json",children:JSON.stringify(j)},d)):o?e.jsx("script",{type:"application/ld+json",children:JSON.stringify(o)}):null]})};He.propTypes={title:n.string,description:n.string,canonical:n.string,image:n.string,imageAlt:n.string,imageWidth:n.string,imageHeight:n.string,type:n.string,schema:n.oneOfType([n.object,n.arrayOf(n.object)]),twitterHandle:n.string,publishedAt:n.string,modifiedAt:n.string,author:n.string,keywords:n.arrayOf(n.string),locale:n.string};const ze=({children:s,title:t})=>{const i=I(),a=L(),[c,p]=b.useState(!1),[h,r]=b.useState(!1),[o,l]=b.useState(!1),x=b.useRef(null);b.useEffect(()=>{const j=d=>{x.current&&!x.current.contains(d.target)&&l(!1)};return document.addEventListener("mousedown",j),()=>{document.removeEventListener("mousedown",j)}},[]);const m=()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),i("/admin"),l(!1)},g=()=>{l(!o)},y=j=>a.pathname===j||a.pathname.startsWith(j+"/"),u=JSON.parse(localStorage.getItem("adminUser")||"{}"),f=[{path:"/admin/dashboard",icon:"solar:widget-2-bold",label:"Dashboard",exact:!0},{path:"/admin/posts",icon:"solar:document-text-bold",label:"Blog Posts",exact:!1},{path:"/admin/blog/new",icon:"solar:add-circle-bold",label:"New Post",exact:!0},{path:"/admin/products",icon:"solar:shop-bold",label:"Products",exact:!1},{path:"/admin/products/new",icon:"solar:add-square-bold",label:"New Product",exact:!0},{path:"/admin/analytics",icon:"solar:chart-2-bold",label:"Analytics",exact:!0},{path:"/admin/categories",icon:"solar:folder-bold",label:"Categories",exact:!0},{path:"/admin/tags",icon:"solar:tag-bold",label:"Tags",exact:!0},{path:"/admin/comments",icon:"solar:chat-round-bold",label:"Comments",exact:!0}];return e.jsxs("div",{className:"admin-layout-wrapper",children:[h&&e.jsx("div",{className:"admin-mobile-overlay",onClick:()=>r(!1)}),e.jsxs("aside",{className:`admin-sidebar ${c?"collapsed":""} ${h?"mobile-open":""}`,children:[e.jsx("div",{className:"admin-sidebar-header",children:e.jsx("div",{className:"admin-logo",children:c?e.jsx("span",{className:"color-primary-1",children:"DS"}):e.jsxs("span",{children:[e.jsx("span",{className:"color-primary-1",children:"DevSkills"})," Admin"]})})}),e.jsx("nav",{className:"admin-sidebar-nav",children:e.jsxs("ul",{className:"admin-nav-list",children:[f.map(j=>e.jsx("li",{className:`admin-nav-item ${y(j.path)?"active":""}`,children:e.jsxs("a",{href:"#",onClick:d=>{d.preventDefault(),i(j.path),r(!1)},className:"admin-nav-link",title:j.label,children:[e.jsx("iconify-icon",{icon:j.icon,className:"admin-nav-icon"}),e.jsx("span",{className:"admin-nav-text",children:j.label})]})},j.path)),e.jsx("li",{className:"admin-nav-divider"}),e.jsx("li",{className:"admin-nav-item",children:e.jsxs("a",{href:"/",target:"_blank",className:"admin-nav-link",title:"View Site",children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"admin-nav-icon"}),e.jsx("span",{className:"admin-nav-text",children:"View Site"})]})})]})}),e.jsx("div",{className:"admin-sidebar-footer",children:e.jsxs("div",{className:"admin-user-menu",ref:x,children:[e.jsxs("div",{className:"admin-user-info clickable",onClick:g,title:"User menu",children:[e.jsx("div",{className:"admin-user-avatar",children:e.jsx("iconify-icon",{icon:"solar:user-bold"})}),!c&&e.jsxs("div",{className:"admin-user-details",children:[e.jsx("div",{className:"admin-user-name",children:u.name||"Admin"}),e.jsx("div",{className:"admin-user-email",children:u.email})]}),e.jsx("div",{className:"admin-user-dropdown-arrow",children:e.jsx("iconify-icon",{icon:`solar:alt-arrow-${o?"up":"down"}-bold`})})]}),o&&e.jsxs("div",{className:"admin-user-dropdown",children:[e.jsxs("div",{className:"admin-dropdown-item",onClick:()=>{i("/admin/dashboard"),l(!1)},children:[e.jsx("iconify-icon",{icon:"solar:widget-2-bold",className:"me-2"}),"Dashboard"]}),e.jsxs("div",{className:"admin-dropdown-item",onClick:()=>{window.open("/","_blank"),l(!1)},children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"me-2"}),"View Site"]}),e.jsx("div",{className:"admin-dropdown-divider"}),e.jsxs("div",{className:"admin-dropdown-item logout",onClick:m,children:[e.jsx("iconify-icon",{icon:"solar:power-bold",className:"me-2"}),"Logout"]})]})]})})]}),e.jsxs("main",{className:`admin-main-content ${c?"sidebar-collapsed":""}`,children:[e.jsxs("header",{className:"admin-topbar",children:[e.jsxs("div",{className:"admin-topbar-left",children:[e.jsx("button",{className:"admin-sidebar-toggle",onClick:()=>p(!c),children:e.jsx("iconify-icon",{icon:"solar:hamburger-menu-bold"})}),e.jsx("button",{className:"admin-mobile-toggle",onClick:()=>r(!h),children:e.jsx("iconify-icon",{icon:"solar:hamburger-menu-bold"})}),t&&e.jsx("h1",{className:"admin-page-title",children:t})]}),e.jsx("div",{className:"admin-topbar-right",children:e.jsxs("span",{className:"admin-welcome",children:["Welcome, ",u.name||u.email]})})]}),e.jsx("div",{className:"admin-content",children:s})]})]})};ze.propTypes={children:n.node.isRequired,title:n.string};const hs=({content:s,onChange:t,placeholder:i="Start writing..."})=>{const a=Z({extensions:[ne.configure({code:!1,codeBlock:!1}),re.configure({HTMLAttributes:{class:"inline-code"}}),le.configure({defaultLanguage:"javascript",HTMLAttributes:{class:"code-block"}})],content:s,onUpdate:({editor:c})=>{const p=c.getHTML();t(p)},editorProps:{attributes:{class:"tiptap-editor"}}});return b.useEffect(()=>{if(a&&s!==void 0){const c=a.getHTML();s!==c&&!a.isFocused&&a.commands.setContent(s)}},[a,s]),a?e.jsxs("div",{className:"tiptap-wrapper",children:[e.jsxs("div",{className:"tiptap-toolbar",children:[e.jsxs("div",{className:"toolbar-group",children:[e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleBold().run(),className:`toolbar-btn ${a.isActive("bold")?"active":""}`,title:"Bold",children:e.jsx("strong",{children:"B"})}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleItalic().run(),className:`toolbar-btn ${a.isActive("italic")?"active":""}`,title:"Italic",children:e.jsx("em",{children:"I"})}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleCode().run(),className:`toolbar-btn ${a.isActive("code")?"active":""}`,title:"Inline Code",children:"</>"})]}),e.jsxs("div",{className:"toolbar-group",children:[e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleHeading({level:1}).run(),className:`toolbar-btn ${a.isActive("heading",{level:1})?"active":""}`,title:"Heading 1",children:"H1"}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleHeading({level:2}).run(),className:`toolbar-btn ${a.isActive("heading",{level:2})?"active":""}`,title:"Heading 2",children:"H2"}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleHeading({level:3}).run(),className:`toolbar-btn ${a.isActive("heading",{level:3})?"active":""}`,title:"Heading 3",children:"H3"})]}),e.jsxs("div",{className:"toolbar-group",children:[e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleBulletList().run(),className:`toolbar-btn ${a.isActive("bulletList")?"active":""}`,title:"Bullet List",children:"• List"}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleOrderedList().run(),className:`toolbar-btn ${a.isActive("orderedList")?"active":""}`,title:"Numbered List",children:"1. List"}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleCodeBlock().run(),className:`toolbar-btn ${a.isActive("codeBlock")?"active":""}`,title:"Code Block",children:"{ }"})]}),e.jsxs("div",{className:"toolbar-group",children:[e.jsx("button",{type:"button",onClick:()=>a.chain().focus().toggleBlockquote().run(),className:`toolbar-btn ${a.isActive("blockquote")?"active":""}`,title:"Quote"}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().setHorizontalRule().run(),className:"toolbar-btn",title:"Horizontal Rule",children:"―"})]}),e.jsxs("div",{className:"toolbar-group",children:[e.jsx("button",{type:"button",onClick:()=>a.chain().focus().undo().run(),disabled:!a.can().undo(),className:"toolbar-btn",title:"Undo",children:"↶"}),e.jsx("button",{type:"button",onClick:()=>a.chain().focus().redo().run(),disabled:!a.can().redo(),className:"toolbar-btn",title:"Redo",children:"↷"})]})]}),e.jsx(Y,{editor:a,className:"tiptap-content",placeholder:i})]}):e.jsx("div",{className:"tiptap-wrapper",children:e.jsx("div",{className:"tiptap-loading",children:e.jsx("p",{children:"Loading editor..."})})})},Ge=({options:s,value:t,onChange:i,comparedPeriod:a})=>{const[c,p]=b.useState(!1),h=b.useRef(null);b.useEffect(()=>{const l=x=>{h.current&&!h.current.contains(x.target)&&p(!1)};return document.addEventListener("mousedown",l),()=>{document.removeEventListener("mousedown",l)}},[]);const r=s.find(l=>l.value===t),o=l=>{i(l),p(!1)};return e.jsx("div",{className:"time-range-selector-wrapper d-flex align-items-center gap-3",children:e.jsxs("div",{className:"position-relative",ref:h,children:[e.jsxs("button",{className:"btn btn-outline-secondary d-flex align-items-center gap-2 text-nowrap",onClick:()=>p(!c),"aria-expanded":c,children:[e.jsx("iconify-icon",{icon:"solar:calendar-bold",className:"text-primary"}),e.jsx("span",{className:"text-nowrap",children:(r==null?void 0:r.label)||"Select time range"}),e.jsx("iconify-icon",{icon:"solar:alt-arrow-down-bold",className:`transition-transform ${c?"rotate-180":""}`})]}),c&&e.jsx("div",{className:"dropdown-menu show position-absolute mt-1 shadow-lg border-0 bg-white rounded-3",children:s.map(l=>e.jsx("button",{className:`dropdown-item px-3 py-2 text-nowrap ${l.value===t?"active bg-primary text-white":"text-dark"}`,onClick:()=>o(l.value),children:l.label},l.value))})]})})};Ge.propTypes={options:n.arrayOf(n.shape({value:n.string.isRequired,label:n.string.isRequired})).isRequired,value:n.string.isRequired,onChange:n.func.isRequired,comparedPeriod:n.string};const us=({options:s,value:t,onChange:i})=>{const[a,c]=b.useState(!1),p=b.useRef(null);b.useEffect(()=>{const o=l=>{p.current&&!p.current.contains(l.target)&&c(!1)};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]);const h=s.find(o=>o.value===t),r=o=>{i(o),c(!1)};return e.jsx("div",{className:"language-selector-wrapper d-flex justify-content-end",children:e.jsxs("div",{className:"position-relative",ref:p,children:[e.jsxs("button",{className:"btn btn-outline-secondary d-flex align-items-center gap-2",onClick:()=>c(!a),"aria-expanded":a,children:[e.jsx("span",{className:"fs-5",children:(h==null?void 0:h.flag)||"🌐"}),e.jsx("span",{children:(h==null?void 0:h.label)||"Select language"}),e.jsx("iconify-icon",{icon:"solar:alt-arrow-down-bold",className:`transition-transform ${a?"rotate-180":""}`})]}),a&&e.jsx("div",{className:"dropdown-menu show position-absolute mt-1 shadow-lg border-0 bg-white rounded-3",style:{right:0,minWidth:"200px"},children:s.map(o=>e.jsxs("button",{className:`dropdown-item px-3 py-2 d-flex align-items-center gap-2 text-nowrap ${o.value===t?"active bg-primary text-white":"text-dark"}`,onClick:()=>r(o.value),children:[e.jsx("span",{className:"fs-6",children:o.flag}),e.jsx("span",{children:o.label}),o.value===t&&e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"ms-auto"})]},o.value))})]})})},Je=({data:s})=>{var c,p,h,r,o,l;if(!s)return e.jsx("div",{className:"analytics-overview-skeleton",children:e.jsx("div",{className:"row",children:[1,2,3].map(x=>e.jsx("div",{className:"col-md-4 mb-3",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-4",children:e.jsxs("div",{className:"placeholder-glow",children:[e.jsx("div",{className:"placeholder col-6 mb-2"}),e.jsx("div",{className:"placeholder col-8 mb-1",style:{height:"2rem"}}),e.jsx("div",{className:"placeholder col-4"})]})})})},x))})});const t=x=>x>=1e6?(x/1e6).toFixed(1)+"M":x>=1e3?(x/1e3).toFixed(1)+"K":(x==null?void 0:x.toLocaleString())||"0",i=(x,m)=>{if(!m||m===0)return null;const g=(x-m)/m*100;return{value:Math.abs(g).toFixed(1),isPositive:g>=0,isSignificant:Math.abs(g)>=1}},a=({title:x,value:m,previousValue:g,icon:y,color:u="primary"})=>{const f=i(m,g);return e.jsx("div",{className:"col-md-4 mb-3",children:e.jsx("div",{className:"card border-0 shadow-sm h-100 bg-white",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-3",children:[e.jsx("h6",{className:"card-title text-muted mb-0 fw-normal",children:x}),e.jsx("iconify-icon",{icon:y,className:`text-${u} fs-4`})]}),e.jsx("div",{className:"mb-2",children:e.jsx("h2",{className:"mb-0 fw-bold text-dark",children:t(m)})}),f&&f.isSignificant&&e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("iconify-icon",{icon:f.isPositive?"solar:arrow-up-bold":"solar:arrow-down-bold",className:`me-1 ${f.isPositive?"text-success":"text-danger"}`}),e.jsxs("span",{className:`small fw-medium ${f.isPositive?"text-success":"text-danger"}`,children:[f.value,"%"]}),e.jsx("span",{className:"text-muted small ms-1",children:"vs previous period"})]}),(!f||!f.isSignificant)&&e.jsx("div",{className:"text-muted small",children:"No significant change"})]})})})};return e.jsx("div",{className:"analytics-overview",children:e.jsxs("div",{className:"row",children:[e.jsx(a,{title:"Post views",value:((c=s.pageViews)==null?void 0:c.current)||0,previousValue:((p=s.pageViews)==null?void 0:p.previous)||0,icon:"solar:eye-bold",color:"primary"}),e.jsx(a,{title:"Visitors",value:((h=s.visitors)==null?void 0:h.current)||0,previousValue:((r=s.visitors)==null?void 0:r.previous)||0,icon:"solar:users-group-rounded-bold",color:"info"}),e.jsx(a,{title:"Engagement",value:((o=s.engagement)==null?void 0:o.current)||0,previousValue:((l=s.engagement)==null?void 0:l.previous)||0,icon:"solar:heart-bold",color:"success"})]})})};Je.propTypes={data:n.shape({totalPageViews:n.number,totalVisitors:n.number,avgEngagement:n.number,pageViewsChange:n.number,visitorsChange:n.number,engagementChange:n.number})};oe.register(ce,de,me,ge,pe,he,ue,xe);const Xe=({data:s})=>{const t=b.useRef(null);if(!s||!s.labels||s.labels.length===0)return e.jsx("div",{className:"card border-0 shadow-sm h-100",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h5",{className:"card-title mb-0",children:"Analytics Overview"}),e.jsx("a",{href:"https://analytics.google.com",target:"_blank",rel:"noopener noreferrer",className:"text-primary small text-decoration-none",children:"View Report"})]}),e.jsx("div",{className:"d-flex align-items-center justify-content-center",style:{height:"300px"},children:e.jsxs("div",{className:"text-center text-muted",children:[e.jsx("iconify-icon",{icon:"solar:chart-2-bold",className:"fs-1 mb-3 d-block"}),e.jsx("p",{className:"mb-0",children:"No data available for the selected period"})]})})]})});const i={labels:s.labels,datasets:[{label:"Page Views",data:s.pageViews||[],borderColor:"#3b82f6",backgroundColor:"rgba(59, 130, 246, 0.1)",borderWidth:2,fill:!0,tension:.4,pointRadius:4,pointHoverRadius:6,pointBackgroundColor:"#3b82f6",pointBorderColor:"#ffffff",pointBorderWidth:2},{label:"Visitors",data:s.visitors||[],borderColor:"#06b6d4",backgroundColor:"rgba(6, 182, 212, 0.1)",borderWidth:2,fill:!1,tension:.4,pointRadius:4,pointHoverRadius:6,pointBackgroundColor:"#06b6d4",pointBorderColor:"#ffffff",pointBorderWidth:2},{label:"Engagement",data:s.engagement||[],borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.1)",borderWidth:2,fill:!1,tension:.4,pointRadius:4,pointHoverRadius:6,pointBackgroundColor:"#10b981",pointBorderColor:"#ffffff",pointBorderWidth:2}]},a={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{usePointStyle:!0,padding:20,font:{size:12}}},tooltip:{mode:"index",intersect:!1,backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,padding:12,displayColors:!0,callbacks:{label:function(c){return`${c.dataset.label}: ${c.parsed.y.toLocaleString()}`}}}},scales:{x:{grid:{display:!1},border:{display:!1},ticks:{color:"#6b7280",font:{size:11}}},y:{beginAtZero:!0,grid:{color:"rgba(107, 114, 128, 0.1)",borderDash:[2,2]},border:{display:!1},ticks:{color:"#6b7280",font:{size:11},callback:function(c){return c>=1e6?(c/1e6).toFixed(1)+"M":c>=1e3?(c/1e3).toFixed(1)+"K":c}}}},interaction:{mode:"nearest",axis:"x",intersect:!1},elements:{point:{hoverRadius:8}}};return e.jsx("div",{className:"card border-0 shadow-sm h-100",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h5",{className:"card-title mb-0",children:"Analytics Overview"}),e.jsx("a",{href:"https://analytics.google.com",target:"_blank",rel:"noopener noreferrer",className:"text-primary small text-decoration-none",children:"View Report"})]}),e.jsx("div",{style:{height:"300px"},children:e.jsx(ee,{ref:t,data:i,options:a})})]})})};Xe.propTypes={data:n.shape({labels:n.arrayOf(n.string),pageViews:n.arrayOf(n.number),visitors:n.arrayOf(n.number),engagement:n.arrayOf(n.number)}),timeRange:n.string};const Ke=({data:s,title:t})=>{if(!s||!s.heatmapData)return e.jsx("div",{className:"card border-0 shadow-sm h-100",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h6",{className:"card-title mb-0",children:t}),e.jsx("a",{href:"https://analytics.google.com",target:"_blank",rel:"noopener noreferrer",className:"text-primary small text-decoration-none",children:"View Report"})]}),e.jsx("div",{className:"d-flex align-items-center justify-content-center",style:{height:"250px"},children:e.jsxs("div",{className:"text-center text-muted",children:[e.jsx("iconify-icon",{icon:"solar:chart-square-bold",className:"fs-1 mb-3 d-block"}),e.jsx("p",{className:"mb-0 small",children:"No heatmap data available"})]})})]})});const i=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],a=Array.from({length:24},(r,o)=>{const l=o===0?12:o>12?o-12:o,x=o<12?"AM":"PM";return`${l} ${x}`}),c=Math.max(...s.heatmapData.flat()),p=r=>{if(r===0)return 0;const o=r/c;return o<=.25?1:o<=.5?2:o<=.75?3:4},h=r=>{const o={0:"#f3f4f6",1:"#dbeafe",2:"#93c5fd",3:"#3b82f6",4:"#1d4ed8"};return o[r]||o[0]};return e.jsx("div",{className:"card border-0 shadow-sm h-100",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h6",{className:"card-title mb-0",children:t}),e.jsx("a",{href:"#",className:"text-primary small text-decoration-none",children:"View Report"})]}),e.jsxs("div",{className:"heatmap-container",children:[e.jsxs("div",{className:"heatmap-time-labels mb-2",children:[e.jsx("div",{})," ",e.jsxs("div",{className:"heatmap-time-row",children:[e.jsx("div",{className:"heatmap-time-label",children:"12 AM"}),e.jsx("div",{className:"heatmap-time-label",children:"6 AM"}),e.jsx("div",{className:"heatmap-time-label",children:"12 PM"}),e.jsx("div",{className:"heatmap-time-label",children:"6 PM"}),e.jsx("div",{className:"heatmap-time-label",children:"12 AM"})]})]}),e.jsx("div",{className:"heatmap-grid-container",children:i.map((r,o)=>e.jsxs("div",{className:"heatmap-row",children:[e.jsx("div",{className:"heatmap-day-label",children:r}),e.jsx("div",{className:"heatmap-cells",children:Array.from({length:24},(l,x)=>{var y;const m=((y=s.heatmapData[o])==null?void 0:y[x])||0,g=p(m);return e.jsx("div",{className:"heatmap-cell",style:{backgroundColor:h(g)},title:`${r} ${a[x]}: ${m} views`,onMouseEnter:u=>{u.target.style.transform="scale(1.2)",u.target.style.zIndex="10"},onMouseLeave:u=>{u.target.style.transform="scale(1)",u.target.style.zIndex="1"}},x)})})]},r))}),e.jsxs("div",{className:"d-flex align-items-center justify-content-between mt-3",children:[e.jsx("span",{className:"text-muted small",children:"Less"}),e.jsx("div",{className:"d-flex gap-1",children:[0,1,2,3,4].map(r=>e.jsx("div",{style:{width:"12px",height:"12px",backgroundColor:h(r),borderRadius:"2px"}},r))}),e.jsx("span",{className:"text-muted small",children:"More"})]})]})]})})};Ke.propTypes={data:n.shape({heatmapData:n.arrayOf(n.shape({hour:n.number,day:n.number,value:n.number}))}),title:n.string};const Qe=({data:s,loading:t,timeRange:i})=>{const[a,c]=b.useState("publishDate"),[p,h]=b.useState("desc"),r=d=>d?d.startsWith("http")?d:`${V.replace("/api","")}/uploads/blog-images/${d}`:null,[o,l]=b.useState({publishDate:!0,views:!0,clicks:!0,readingTime:!0,engagement:!0,categories:!1}),x=[{key:"publishDate",label:"Publish date",icon:"solar:calendar-bold"},{key:"views",label:"Views",icon:"solar:eye-bold"},{key:"clicks",label:"Clicks",icon:"solar:cursor-bold"},{key:"readingTime",label:"Reading time",icon:"solar:clock-circle-bold"},{key:"engagement",label:"Engagement",icon:"solar:heart-bold"},{key:"categories",label:"Categories",icon:"solar:folder-bold"}],m=d=>{a===d?h(p==="asc"?"desc":"asc"):(c(d),h("desc"))},g=d=>{l(N=>({...N,[d]:!N[d]}))},y=d=>new Date(d).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),u=d=>d?`${d}m`:"0m",f=d=>{if(!d||d===0)return"0s";if(d<60)return`${d}s`;const N=Math.floor(d/60),w=d%60;return w>0?`${N}m ${w}s`:`${N}m`},j=_.useMemo(()=>!s||!Array.isArray(s)?[]:[...s].sort((d,N)=>{let w=d[a],k=N[a];return a==="publishDate"&&(w=new Date(w),k=new Date(k)),p==="asc"?w>k?1:-1:w<k?1:-1}),[s,a,p]);return t?e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsx("h5",{className:"card-title mb-4",children:"Posts Analytics"}),e.jsx("div",{className:"d-flex justify-content-center py-5",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})})]})}):e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h5",{className:"card-title mb-0",children:"Posts by"}),e.jsx("a",{href:"#",className:"text-primary small text-decoration-none",children:"View Report"})]}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-md-6",children:e.jsx("div",{className:"d-flex flex-wrap gap-2",children:x.map(d=>e.jsxs("button",{className:`btn btn-sm d-flex align-items-center gap-1 ${o[d.key]?"btn-primary":"btn-outline-secondary"}`,onClick:()=>g(d.key),children:[e.jsx("iconify-icon",{icon:d.icon,className:"fs-6"}),e.jsx("span",{children:d.label})]},d.key))})})}),e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table table-hover align-middle",children:[e.jsx("thead",{className:"table-light",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted",children:"Post title"}),o.publishDate&&e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted cursor-pointer",onClick:()=>m("publishDate"),children:e.jsxs("div",{className:"d-flex align-items-center gap-1",children:[e.jsx("iconify-icon",{icon:"solar:calendar-bold"}),e.jsx("span",{children:"Publish date"}),a==="publishDate"&&e.jsx("iconify-icon",{icon:p==="asc"?"solar:arrow-up-bold":"solar:arrow-down-bold",className:"text-primary"})]})}),o.views&&e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted cursor-pointer text-center",onClick:()=>m("views"),children:e.jsxs("div",{className:"d-flex align-items-center justify-content-center gap-1",children:[e.jsx("iconify-icon",{icon:"solar:eye-bold"}),e.jsx("span",{children:"Views"}),a==="views"&&e.jsx("iconify-icon",{icon:p==="asc"?"solar:arrow-up-bold":"solar:arrow-down-bold",className:"text-primary"})]})}),o.clicks&&e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted cursor-pointer text-center",onClick:()=>m("clicks"),children:e.jsxs("div",{className:"d-flex align-items-center justify-content-center gap-1",children:[e.jsx("iconify-icon",{icon:"solar:cursor-bold"}),e.jsx("span",{children:"Clicks"}),a==="clicks"&&e.jsx("iconify-icon",{icon:p==="asc"?"solar:arrow-up-bold":"solar:arrow-down-bold",className:"text-primary"})]})}),o.readingTime&&e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted cursor-pointer text-center",onClick:()=>m("readingTime"),children:e.jsxs("div",{className:"d-flex align-items-center justify-content-center gap-1",children:[e.jsx("iconify-icon",{icon:"solar:clock-circle-bold"}),e.jsx("span",{children:"Time"}),a==="readingTime"&&e.jsx("iconify-icon",{icon:p==="asc"?"solar:arrow-up-bold":"solar:arrow-down-bold",className:"text-primary"})]})}),o.engagement&&e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted cursor-pointer text-center",onClick:()=>m("engagement"),children:e.jsxs("div",{className:"d-flex align-items-center justify-content-center gap-1",children:[e.jsx("iconify-icon",{icon:"solar:heart-bold"}),e.jsx("span",{children:"Engagement"}),a==="engagement"&&e.jsx("iconify-icon",{icon:p==="asc"?"solar:arrow-up-bold":"solar:arrow-down-bold",className:"text-primary"})]})}),o.categories&&e.jsx("th",{scope:"col",className:"border-0 fw-semibold text-muted",children:e.jsxs("div",{className:"d-flex align-items-center gap-1",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold"}),e.jsx("span",{children:"Categories"})]})})]})}),e.jsx("tbody",{children:j.length===0?e.jsx("tr",{children:e.jsxs("td",{colSpan:"7",className:"text-center py-5 text-muted",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"fs-1 mb-3 d-block"}),"No posts data available for the selected period"]})}):j.map(d=>{var N,w,k,C;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center gap-3",children:[d.featuredImage&&e.jsx("img",{src:r(d.featuredImage),alt:"",className:"rounded",style:{width:"40px",height:"40px",objectFit:"cover"}}),e.jsxs("div",{children:[e.jsx(R,{to:`/admin/blog/edit/${d.id}`,className:"text-decoration-none fw-medium text-dark",children:d.title}),e.jsxs("div",{className:"text-muted small",children:["Published on ",y(d.publishedAt)]})]})]})}),o.publishDate&&e.jsx("td",{className:"text-muted small",children:y(d.publishedAt)}),o.views&&e.jsx("td",{className:"text-center fw-medium",children:((N=d.views)==null?void 0:N.toLocaleString())||"0"}),o.clicks&&e.jsx("td",{className:"text-center fw-medium",children:((w=d.clicks)==null?void 0:w.toLocaleString())||"0"}),o.readingTime&&e.jsx("td",{className:"text-center text-muted",children:e.jsxs("div",{className:"small",children:[e.jsxs("div",{children:["Est: ",u(d.readingTime)]}),e.jsxs("div",{className:"text-primary",children:["Avg: ",f(d.avgViewTime)]})]})}),o.engagement&&e.jsx("td",{className:"text-center fw-medium",children:((k=d.engagement)==null?void 0:k.toLocaleString())||"0"}),o.categories&&e.jsx("td",{children:e.jsx("div",{className:"d-flex flex-wrap gap-1",children:(C=d.categories)==null?void 0:C.map(P=>e.jsx("span",{className:"badge bg-light text-dark",children:P.name},P.id))})})]},d.id)})})]})})]})})};Qe.propTypes={data:n.arrayOf(n.shape({id:n.string,title:n.string,publishedAt:n.string,featuredImage:n.string,readingTime:n.number,avgViewTime:n.number,categories:n.arrayOf(n.shape({id:n.string,name:n.string})),views:n.number,clicks:n.number,engagement:n.number})),loading:n.bool,timeRange:n.string};function xs({timeRange:s,selectedLanguage:t}){const[i,a]=b.useState(null),[c,p]=b.useState(!0),[h,r]=b.useState("");b.useEffect(()=>{o()},[s,t]);const o=async()=>{try{if(p(!0),r(""),!localStorage.getItem("adminToken")){r("Authentication required. Please log in to access this page."),p(!1);return}const y=new Date;let u=new Date;switch(s){case"last7days":u.setDate(y.getDate()-7);break;case"last30days":u.setDate(y.getDate()-30);break;case"last90days":u.setDate(y.getDate()-90);break;default:u.setDate(y.getDate()-30)}const f=await H.getConversionAnalytics(u.toISOString().split("T")[0],y.toISOString().split("T")[0],t);if(f.response.ok&&f.data)a(f.data.data||f.data);else{if(console.error("Conversion API failed:",f.response.status,f.response.statusText),f.response.status===401||f.response.status===403){r("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}r("Failed to load conversion data")}}catch(g){console.error("Error loading conversion analytics:",g),r("Error loading conversion analytics")}finally{p(!1)}};if(c)return e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsx("h5",{className:"card-title mb-4",children:"Conversion Analytics"}),e.jsx("div",{className:"d-flex justify-content-center py-5",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})})]})});if(h)return e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsx("h5",{className:"card-title mb-4",children:"Conversion Analytics"}),e.jsxs("div",{className:"alert alert-danger",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-bold",className:"me-2"}),h]})]})});if(!i)return e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsx("h5",{className:"card-title mb-4",children:"Conversion Analytics"}),e.jsxs("div",{className:"text-center py-5 text-muted",children:[e.jsx("iconify-icon",{icon:"solar:target-bold",className:"fs-1 mb-3 d-block"}),"No conversion data available for the selected time range."]})]})});const{summary:l,conversions_by_source:x,recent_events:m}=i;return console.log("🎨 Rendering ConversionAnalytics with data:",{summary:l,conversions_by_source:x,recent_events:m}),e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h5",{className:"card-title mb-0",children:"Conversion Analytics"}),e.jsxs("span",{className:"text-primary small",children:[l.total_conversions," conversions • €",l.total_value," ","total value"]})]}),e.jsxs("div",{className:"row mb-4",children:[e.jsx("div",{className:"col-sm-6 col-lg-3 mb-3",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"flex-shrink-0 me-3",children:e.jsx("div",{className:"bg-primary rounded-circle d-flex align-items-center justify-content-center",style:{width:"40px",height:"40px"},children:e.jsx("iconify-icon",{icon:"solar:target-bold",className:"text-white"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted mb-1 small",children:"Total Conversions"}),e.jsx("h4",{className:"mb-0",children:l.total_conversions})]})]})})})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-3",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"flex-shrink-0 me-3",children:e.jsx("div",{className:"bg-success rounded-circle d-flex align-items-center justify-content-center",style:{width:"40px",height:"40px"},children:e.jsx("iconify-icon",{icon:"solar:euro-bold",className:"text-white"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted mb-1 small",children:"Total Value"}),e.jsxs("h4",{className:"mb-0",children:["€",l.total_value]})]})]})})})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-3",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"flex-shrink-0 me-3",children:e.jsx("div",{className:"bg-info rounded-circle d-flex align-items-center justify-content-center",style:{width:"40px",height:"40px"},children:e.jsx("iconify-icon",{icon:"solar:chart-bold",className:"text-white"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted mb-1 small",children:"Average Value"}),e.jsxs("h4",{className:"mb-0",children:["€",l.average_value]})]})]})})})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-3",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"flex-shrink-0 me-3",children:e.jsx("div",{className:"bg-warning rounded-circle d-flex align-items-center justify-content-center",style:{width:"40px",height:"40px"},children:e.jsx("iconify-icon",{icon:"solar:layers-bold",className:"text-white"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted mb-1 small",children:"Sources"}),e.jsx("h4",{className:"mb-0",children:x.length})]})]})})})})]}),e.jsxs("div",{className:"card mb-4",children:[e.jsx("div",{className:"card-header",children:e.jsx("h5",{className:"card-title mb-0",children:"Conversions by Source"})}),e.jsx("div",{className:"card-body p-0",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table table-hover mb-0",children:[e.jsx("thead",{className:"table-light",children:e.jsxs("tr",{children:[e.jsx("th",{children:"Source"}),e.jsx("th",{children:"Conversions"}),e.jsx("th",{children:"Value"}),e.jsx("th",{children:"Top CTA Type"}),e.jsx("th",{children:"Languages"})]})}),e.jsx("tbody",{children:x.map((g,y)=>{const u=Object.entries(g.cta_types).sort((j,d)=>d[1]-j[1])[0],f=Object.entries(g.languages).sort((j,d)=>d[1]-j[1]).slice(0,2);return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("strong",{children:g.source.replace(/_/g," ").replace(/\b\w/g,j=>j.toUpperCase())})}),e.jsx("td",{children:g.total_conversions}),e.jsxs("td",{children:["€",g.total_value]}),e.jsx("td",{children:u?`${u[0]} (${u[1]})`:"-"}),e.jsx("td",{children:f.map(([j,d])=>`${j.toUpperCase()} (${d})`).join(", ")})]},y)})})]})})})]}),e.jsxs("div",{className:"card mb-4",children:[e.jsx("div",{className:"card-header",children:e.jsx("h5",{className:"card-title mb-0",children:"Recent Conversions"})}),e.jsx("div",{className:"card-body p-0",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table table-hover mb-0",children:[e.jsx("thead",{className:"table-light",children:e.jsxs("tr",{children:[e.jsx("th",{children:"Timestamp"}),e.jsx("th",{children:"Source"}),e.jsx("th",{children:"CTA Type"}),e.jsx("th",{children:"Language"}),e.jsx("th",{children:"Value"})]})}),e.jsx("tbody",{children:m.slice(0,10).map((g,y)=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsx("small",{children:new Date(g.timestamp).toLocaleString()})}),e.jsx("td",{children:g.source.replace(/_/g," ").replace(/\b\w/g,u=>u.toUpperCase())}),e.jsx("td",{children:e.jsx("span",{className:"badge bg-secondary",children:g.cta_type.replace(/_/g," ").replace(/\b\w/g,u=>u.toUpperCase())})}),e.jsx("td",{children:e.jsx("span",{className:"badge bg-primary",children:g.language.toUpperCase()})}),e.jsxs("td",{children:["€",g.value]})]},y))})]})})})]})]})})}function js({timeRange:s,selectedLanguage:t}){const[i,a]=b.useState([]),[c,p]=b.useState(!0),[h,r]=b.useState("");b.useEffect(()=>{o()},[s,t]);const o=async()=>{try{if(p(!0),r(""),!localStorage.getItem("adminToken")){r("Authentication required. Please log in to access this page."),p(!1);return}const g=await H.getStaticPagesAnalytics(s,t);if(g.response.ok&&g.data)a(g.data.data||g.data);else{if(console.error("Static pages API failed:",g.response.status,g.response.statusText),g.response.status===401||g.response.status===403){r("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}r("Failed to load static pages data")}}catch(m){console.error("Error loading static pages analytics:",m),r("Error loading static pages analytics")}finally{p(!1)}},l=m=>{const g={home:"bg-primary",about:"bg-success",services:"bg-info",products:"bg-warning",contact:"bg-danger",blog:"bg-secondary",other:"bg-dark"};return g[m]||g.other},x=m=>{const g=m.split("/").filter(Boolean);return g.length===0?"Home":g.length===1?g[0]:g.slice(1).join(" / ").replace(/-/g," ").replace(/\b\w/g,function(u){return u.toUpperCase()})};return c?e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsx("h5",{className:"card-title mb-4",children:"Static Pages Analytics"}),e.jsx("div",{className:"d-flex justify-content-center py-5",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})})]})}):h?e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsx("h5",{className:"card-title mb-4",children:"Static Pages Analytics"}),e.jsxs("div",{className:"alert alert-danger",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-bold",className:"me-2"}),h]})]})}):e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsxs("div",{className:"card-body p-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-between mb-4",children:[e.jsx("h5",{className:"card-title mb-0",children:"Static Pages Analytics"}),e.jsxs("span",{className:"text-primary small",children:[i.length," pages tracked"]})]}),e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table table-hover align-middle",children:[e.jsx("thead",{className:"table-light",children:e.jsxs("tr",{children:[e.jsx("th",{children:"Page"}),e.jsx("th",{children:"Type"}),e.jsx("th",{children:"Views"}),e.jsx("th",{children:"Unique Visitors"}),e.jsx("th",{children:"Languages"})]})}),e.jsx("tbody",{children:i.map((m,g)=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:m.page_title||x(m.path)}),e.jsx("small",{className:"text-muted",children:m.path})]})}),e.jsx("td",{children:e.jsx("span",{className:`badge ${l(m.page_type)} text-white`,children:m.page_type})}),e.jsx("td",{children:m.views.toLocaleString()}),e.jsx("td",{children:m.unique_visitors.toLocaleString()}),e.jsx("td",{children:e.jsx("small",{children:m.languages?Object.entries(m.languages).map(([y,u])=>`${y.toUpperCase()} (${u})`).join(", "):"-"})})]},g))})]})}),i.length===0&&e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-muted mb-0",children:"No page data available for the selected time range."})})]})})}export{we as A,qe as C,De as E,Ve as F,as as G,Ke as H,rs as L,We as M,ke as P,ms as R,He as S,hs as T,Se as U,Fe as W,ls as a,F as b,cs as c,ye as d,Be as e,Ne as f,ds as g,gs as h,os as i,ze as j,H as k,V as l,Ge as m,us as n,Je as o,z as p,Xe as q,Qe as r,xs as s,ve as t,js as u,v,ps as w,ns as x,is as y};
//# sourceMappingURL=components-common-DkhrGJpG.js.map
