// client/src/components/common/UnifiedSEO.jsx
import React from "react";
import PropTypes from "prop-types";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

/**
 * Unified SEO component following 2025 best practices
 * Combines functionality from SEO.jsx, MultilingualSEO.jsx, and MetaComponent.jsx
 *
 * Features:
 * - Multilingual support with hreflang tags
 * - Local business schema markup
 * - AI Overview optimization
 * - Core Web Vitals meta tags
 * - Enhanced social media support
 * - 2025 SEO best practices
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Page title (without brand suffix)
 * @param {string} props.description - Page description
 * @param {string} props.slug - Page slug for generating language URLs
 * @param {string} props.type - Page type (website, article, product, etc.)
 * @param {Object|Array} props.schema - JSON-LD structured data
 * @param {string[]} props.keywords - Keywords for SEO
 * @param {string} props.image - OG image URL
 * @param {string} props.imageAlt - Alt text for the OG image
 * @param {string} props.author - Content author
 * @param {string} props.publishedAt - Published date (ISO format)
 * @param {string} props.modifiedAt - Modified date (ISO format)
 * @param {Object} props.alternateUrls - Custom alternate URLs for languages
 * @param {boolean} props.noIndex - Whether to prevent indexing
 * @param {string} props.canonicalUrl - Custom canonical URL
 */
const UnifiedSEO = ({
  title,
  description,
  slug = "",
  type = "website",
  schema = null,
  keywords = [],
  image = "https://devskills.ee/home.jpg",
  imageAlt = null,
  author = "DevSkills",
  publishedAt = "",
  modifiedAt = "",
  alternateUrls = null,
  noIndex = false,
  canonicalUrl = null,
}) => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || "en";

  // Base URL and business information
  const baseUrl = "https://devskills.ee";
  const businessName = "DevSkills";

  // Generate language-specific URLs
  const generateLanguageUrls = () => {
    if (alternateUrls) {
      return alternateUrls;
    }

    const supportedLanguages = ["en", "et", "fi", "de", "sv"];
    const urls = {};

    supportedLanguages.forEach((lang) => {
      if (slug) {
        urls[lang] = `${baseUrl}/${lang}/${slug}`;
      } else {
        urls[lang] = `${baseUrl}/${lang}`;
      }
    });

    return urls;
  };

  const languageUrls = generateLanguageUrls();
  const currentUrl =
    canonicalUrl ||
    languageUrls[currentLanguage] ||
    `${baseUrl}/${currentLanguage}`;

  // Language-specific meta data
  const getLanguageSpecificData = () => {
    const localeMap = {
      en: "en_US",
      et: "et_EE",
      fi: "fi_FI",
      de: "de_DE",
      sv: "sv_SE",
    };

    const languageMap = {
      en: "English",
      et: "Estonian",
      fi: "Finnish",
      de: "German",
      sv: "Swedish",
    };

    return {
      locale: localeMap[currentLanguage] || "en_US",
      language: languageMap[currentLanguage] || "English",
    };
  };

  const { locale, language } = getLanguageSpecificData();

  // Format the title with brand suffix
  const formattedTitle = title
    ? `${title} | ${businessName}`
    : `${businessName} - Professional Software Development Services`;

  // Default image alt text
  const defaultImageAlt =
    imageAlt ||
    `${businessName} - ${
      title || "Professional Software Development Services"
    }`;

  // Robots directive
  const robotsContent = noIndex
    ? "noindex, nofollow"
    : "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1";

  return (
    <Helmet>
      {/* Basic meta tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={description} />
      <link rel="canonical" href={currentUrl} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(", ")} />
      )}
      <meta name="language" content={language} />
      <meta httpEquiv="Content-Language" content={currentLanguage} />

      {/* Hreflang tags for multilingual SEO */}
      {Object.entries(languageUrls).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}

      {/* x-default for international targeting */}
      <link
        rel="alternate"
        hrefLang="x-default"
        href={languageUrls.en || currentUrl}
      />

      {/* Open Graph meta tags */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:image:alt" content={defaultImageAlt} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content={businessName} />
      <meta property="og:locale" content={locale} />

      {/* Facebook App ID for Facebook Debugger - Replace with your actual Facebook App ID */}
      <meta property="fb:app_id" content="YOUR_FACEBOOK_APP_ID" />

      {/* Alternate locales for Facebook */}
      {Object.keys(languageUrls)
        .filter((lang) => lang !== currentLanguage)
        .map((lang) => {
          const altLocale = {
            en: "en_US",
            et: "et_EE",
            fi: "fi_FI",
            de: "de_DE",
            sv: "sv_SE",
          }[lang];
          return (
            <meta
              key={lang}
              property="og:locale:alternate"
              content={altLocale}
            />
          );
        })}

      {/* Article specific meta tags */}
      {type === "article" && publishedAt && (
        <meta property="article:published_time" content={publishedAt} />
      )}
      {type === "article" && modifiedAt && (
        <meta property="article:modified_time" content={modifiedAt} />
      )}
      {type === "article" && author && (
        <meta property="article:author" content={author} />
      )}

      {/* LinkedIn Author and Publication Date */}
      <meta name="author" content={author || businessName} />
      {publishedAt && <meta name="publish_date" content={publishedAt} />}
      {!publishedAt && (
        <meta name="publish_date" content={new Date().toISOString()} />
      )}

      {/* Twitter Card meta tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@DevSkillsEE" />
      <meta name="twitter:creator" content="@DevSkillsEE" />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content={defaultImageAlt} />

      {/* SEO and crawler directives */}
      <meta name="robots" content={robotsContent} />
      <meta name="googlebot" content={robotsContent} />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />

      {/* Core Web Vitals and Performance hints */}
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=5.0"
      />
      <meta name="theme-color" content="#06B6D4" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link
        rel="preconnect"
        href="https://fonts.gstatic.com"
        crossOrigin="anonymous"
      />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />

      {/* Mobile and PWA meta tags */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta
        name="apple-mobile-web-app-status-bar-style"
        content="black-translucent"
      />
      <meta name="apple-mobile-web-app-title" content={businessName} />
      <meta name="application-name" content={businessName} />
      <meta name="msapplication-TileColor" content="#06B6D4" />

      {/* AI Overview optimization */}
      <meta name="AI-generated" content="false" />
      <meta name="content-type" content="original" />

      {/* JSON-LD structured data */}
      {schema && Array.isArray(schema) ? (
        // Handle array of schema objects
        schema.map((schemaItem, index) => (
          <script key={index} type="application/ld+json">
            {JSON.stringify({
              ...schemaItem,
              "@context": "https://schema.org",
              inLanguage: currentLanguage,
              url: currentUrl,
            })}
          </script>
        ))
      ) : schema ? (
        // Handle single schema object
        <script type="application/ld+json">
          {JSON.stringify({
            ...schema,
            "@context": "https://schema.org",
            inLanguage: currentLanguage,
            url: currentUrl,
          })}
        </script>
      ) : null}
    </Helmet>
  );
};

UnifiedSEO.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  slug: PropTypes.string,
  type: PropTypes.string,
  schema: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.arrayOf(PropTypes.object),
  ]),
  keywords: PropTypes.arrayOf(PropTypes.string),
  image: PropTypes.string,
  imageAlt: PropTypes.string,
  author: PropTypes.string,
  publishedAt: PropTypes.string,
  modifiedAt: PropTypes.string,
  alternateUrls: PropTypes.object,
  noIndex: PropTypes.bool,
  canonicalUrl: PropTypes.string,
};

export default UnifiedSEO;
