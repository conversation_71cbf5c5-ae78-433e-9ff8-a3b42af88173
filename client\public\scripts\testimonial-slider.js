// Testimonial slider with draggable functionality
document.addEventListener("DOMContentLoaded", function () {
  // Wait a bit for the DOM to fully render
  setTimeout(function () {
    initTestimonialSlider();
  }, 500);
});

function initTestimonialSlider() {
  const slider = document.getElementById("testimonial-marquee");
  const track = document.getElementById("testimonial-track");

  if (!slider || !track) {
    console.log(
      "Testimonial slider elements not found, will try again in 1 second"
    );
    setTimeout(initTestimonialSlider, 1000);
    return;
  }

  console.log("Initializing testimonial slider");

  let isDragging = false;
  let startPosition = 0;
  let currentTranslate = 0;
  let previousTranslate = 0;

  // Store the original animation
  const originalAnimation = getComputedStyle(track).animation;

  // Mouse events
  slider.addEventListener("mousedown", dragStart);
  slider.addEventListener("mouseup", dragEnd);
  slider.addEventListener("mouseleave", dragEnd);
  slider.addEventListener("mousemove", drag);

  // Touch events
  slider.addEventListener("touchstart", dragStart, { passive: false });
  slider.addEventListener("touchend", dragEnd);
  slider.addEventListener("touchmove", drag, { passive: false });

  // Prevent context menu on right click
  slider.addEventListener("contextmenu", (e) => e.preventDefault());

  function dragStart(e) {
    e.preventDefault();

    // Pause the animation
    track.style.animationPlayState = "paused";

    // Get the current position
    startPosition = getPositionX(e);
    isDragging = true;

    // Get the current transform value
    const transform = window
      .getComputedStyle(track)
      .getPropertyValue("transform");
    if (transform !== "none") {
      // Extract the translateX value
      const matrix = new DOMMatrix(transform);
      currentTranslate = matrix.m41;
    } else {
      currentTranslate = 0;
    }

    previousTranslate = currentTranslate;

    // Change cursor and add active class
    slider.style.cursor = "grabbing";
    slider.classList.add("active");
  }

  function drag(e) {
    if (!isDragging) return;
    e.preventDefault();

    const currentPosition = getPositionX(e);
    const moveDistance = currentPosition - startPosition;
    currentTranslate = previousTranslate + moveDistance;

    // Apply the transform
    track.style.transform = `translateX(${currentTranslate}px)`;
  }

  function dragEnd() {
    if (!isDragging) return;

    isDragging = false;

    // Resume the animation after a short delay
    setTimeout(() => {
      // Reset the transform
      track.style.transform = "";
      // Resume the animation
      track.style.animationPlayState = "running";
    }, 100);

    // Change cursor back
    slider.style.cursor = "grab";
    slider.classList.remove("active");
  }

  function getPositionX(e) {
    return e.type.includes("mouse") ? e.pageX : e.touches[0].clientX;
  }

  // Pause animation on hover
  slider.addEventListener("mouseenter", () => {
    track.style.animationPlayState = "paused";
  });

  // Resume animation when mouse leaves
  slider.addEventListener("mouseleave", () => {
    if (!isDragging) {
      track.style.animationPlayState = "running";
    }
  });

  console.log("Testimonial slider initialized");
}
