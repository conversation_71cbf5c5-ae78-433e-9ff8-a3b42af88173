/* Critical CSS for Above-the-Fold Content - DevSkills */
/* This file contains only essential styles for first paint performance */

/* CSS Variables - Essential for theme */
:root {
  --font-global: "DM Sans", sans-serif;
  --font-alt: "DM Sans", sans-serif;
  --container-width: 1350px;
  --section-padding-y: 120px;
  --menu-bar-height: 85px;
  --menu-bar-height-scrolled: 65px;
  --color-dark-1: #010101;
  --color-dark-2: #171717;
  --color-dark-3: #272727;
  --color-gray-1: #757575;
  --color-gray-2: #888;
  --color-gray-3: #999;
  --color-dark-mode-gray-1: rgba(255, 255, 255, 0.7);
  --color-dark-mode-gray-2: rgba(255, 255, 255, 0.1275);
}

/* Theme Elegant Variables */
.theme-elegant {
  --font-global: "Poppins", sans-serif;
  --container-width: 1230px;
  --section-padding-y: 160px;
  --color-dark-1: #111;
  --color-gray-1: #777;
}

.theme-elegant .dark-mode {
  --color-dark-1: #171717;
  --color-dark-2: #222;
}

/* Essential Reset and Base */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  background-color: #000000 !important;
  background: #000000 !important;
}

body {
  font-family: var(--font-global);
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-dark-1);
}

.theme-elegant body {
  color: var(--color-dark-1);
  font-family: var(--font-global);
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 2;
}

/* Container System */
.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 30px;
}

/* Essential Grid */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
.col-lg-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

/* Page Structure */
.page {
  background-color: var(--color-dark-1);
  min-height: 100vh;
}

.bg-dark-1 {
  background-color: var(--color-dark-1) !important;
}

/* Navigation - Critical */
.main-nav {
  display: flex;
  width: 100%;
  height: var(--menu-bar-height);
  position: relative;
  background: rgba(255, 255, 255, 0.98);
  z-index: 1030;
  transition: all 0.2s ease;
}

.main-nav.dark {
  background-color: rgba(10, 10, 10, 0.905);
  box-shadow: none;
}

.main-nav.dark-mode {
  background-color: rgba(27, 27, 27, 0.905);
}

.main-nav.transparent {
  background: transparent;
  box-shadow: none;
}

.main-nav-sub {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 30px;
}

/* Logo - Critical */
.nav-logo-wrap {
  display: flex;
  align-items: center;
}

.logo {
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif !important;
  font-size: 18px;
  font-weight: 600 !important;
  text-decoration: none;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo .mt-1 {
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif !important;
  font-weight: 600 !important;
}

/* Hero Section - Critical */
.home-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.parallax-5 {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-dark-alpha-30::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.light-content {
  color: #ffffff;
}

/* Typography - Critical */
.section-title-tiny {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 50px;
  opacity: 0.8;
}

.hs-title-3 {
  font-size: 4rem;
  font-weight: 300;
  line-height: 1.1;
  margin-bottom: 120px;
}

/* Animation Base - Critical */
.wow {
  opacity: 0.001;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  opacity: 1;
  transform: scale(1);
}

.appear-animate .wow.animated {
  opacity: 1;
  transform: scale(1);
}

/* Essential Animations */
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fadeInDown {
  animation-name: fadeInDown;
}

.fadeInUp {
  animation-name: fadeInUp;
}

/* Utility Classes */
.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.text-center {
  text-align: center;
}
.position-relative {
  position: relative;
}
.z-index-1 {
  z-index: 1;
}

/* Home section positioning for scroll indicator */
.home-section {
  position: relative !important;
}

/* Scroll Down Indicator - Absolute positioning relative to home-section */
.scroll-down-3-wrap {
  position: absolute;
  bottom: 50px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 3;
  pointer-events: none;
}

.scroll-down-3-wrap .scroll-down-3 {
  pointer-events: auto;
}

.scroll-down-3 {
  position: relative;
  color: var(--color-dark-1);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 0.1em;
  transition: letter-spacing 0.27s ease;
}

.scroll-down-3:hover {
  color: unset;
  text-decoration: none;
  letter-spacing: 0.2em;
}

.scroll-down-3:after {
  content: "";
  display: block;
  width: 1px;
  height: 106px;
  margin: 6px auto -53px;
  background: var(--color-dark-1);
}

.light-content .scroll-down-3 {
  color: #fff;
}

.light-content .scroll-down-3:after {
  background: rgba(193, 193, 193, 0.9);
}

/* Mobile Responsive - Critical */
@media (max-width: 768px) {
  .hs-title-3 {
    font-size: 2.5rem;
    margin-bottom: 80px;
  }

  .section-title-tiny {
    margin-bottom: 30px;
  }

  .main-nav-sub {
    padding: 0 15px;
  }
}
