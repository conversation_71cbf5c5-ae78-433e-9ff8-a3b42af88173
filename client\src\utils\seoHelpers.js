// client/src/utils/seoHelpers.js

/**
 * SEO Helper functions for generating page-specific metadata and schema
 * Following 2025 SEO best practices
 */

// Business information constants
export const BUSINESS_INFO = {
  name: "DevSkills",
  fullName: "DevSkills OÜ",
  alternateName: "DevSkills Development Studio",
  description:
    "Professional software development services and custom solutions",
  url: "https://devskills.ee",
  address: {
    streetAddress: "Tornimäe tn 7",
    addressLocality: "Tallinn",
    postalCode: "10145",
    addressCountry: "EE",
  },
  geo: {
    latitude: 59.437,
    longitude: 24.7536,
  },
  contactPoint: {
    telephone: "+372 5628 2038",
    contactType: "customer service",
    availableLanguage: ["English", "Estonian", "Finnish", "German", "Swedish"],
  },
  openingHours: {
    dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
    opens: "08:00",
    closes: "17:00",
  },
  services: [
    "Custom Software Development",
    "Web Development",
    "AI Solutions",
    "White Label Software",
    "Blockchain Development",
    "Mobile Applications Development",
    "Backend Development",
    "Business Management Systems",
  ],
  socialMedia: [
    "https://www.facebook.com/devskillsee",
    "https://www.linkedin.com/company/devskills-development-studio",
    "https://twitter.com/DevSkillsEE",
  ],
};

/**
 * Generate local business schema markup
 */
export const generateLocalBusinessSchema = () => ({
  "@type": "LocalBusiness",
  "@id": `${BUSINESS_INFO.url}/#organization`,
  name: BUSINESS_INFO.fullName,
  alternateName: BUSINESS_INFO.alternateName,
  url: BUSINESS_INFO.url,
  description: BUSINESS_INFO.description,
  address: {
    "@type": "PostalAddress",
    ...BUSINESS_INFO.address,
  },
  geo: {
    "@type": "GeoCoordinates",
    ...BUSINESS_INFO.geo,
  },
  contactPoint: {
    "@type": "ContactPoint",
    ...BUSINESS_INFO.contactPoint,
  },
  openingHoursSpecification: {
    "@type": "OpeningHoursSpecification",
    ...BUSINESS_INFO.openingHours,
  },
  serviceArea: {
    "@type": "Country",
    name: "Estonia",
  },
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: "Software Development Services",
    itemListElement: BUSINESS_INFO.services.map((service) => ({
      "@type": "Offer",
      itemOffered: {
        "@type": "Service",
        name: service,
      },
    })),
  },
  logo: {
    "@type": "ImageObject",
    url: `${BUSINESS_INFO.url}/logo.png`,
    width: "180",
    height: "60",
  },
  sameAs: BUSINESS_INFO.socialMedia,
});

/**
 * Generate website schema markup
 */
export const generateWebsiteSchema = () => ({
  "@type": "WebSite",
  "@id": `${BUSINESS_INFO.url}/#website`,
  name: BUSINESS_INFO.name,
  alternateName: BUSINESS_INFO.alternateName,
  url: BUSINESS_INFO.url,
  description: BUSINESS_INFO.description,
  publisher: {
    "@id": `${BUSINESS_INFO.url}/#organization`,
  },
  potentialAction: {
    "@type": "SearchAction",
    target: `${BUSINESS_INFO.url}/search?q={search_term_string}`,
    "query-input": "required name=search_term_string",
  },
  inLanguage: ["en", "et", "fi", "de", "sv"],
});

/**
 * Generate article schema for blog posts
 */
export const generateArticleSchema = (article) => ({
  "@type": "Article",
  headline: article.title,
  description: article.excerpt || article.description,
  image: article.featuredImage || `${BUSINESS_INFO.url}/home.jpg`,
  author: {
    "@type": "Person",
    name: article.author || BUSINESS_INFO.name,
  },
  publisher: {
    "@type": "Organization",
    name: BUSINESS_INFO.name,
    logo: {
      "@type": "ImageObject",
      url: `${BUSINESS_INFO.url}/logo.png`,
    },
  },
  datePublished: article.publishedAt,
  dateModified: article.modifiedAt || article.publishedAt,
  mainEntityOfPage: {
    "@type": "WebPage",
    "@id": article.url,
  },
});

/**
 * Generate product schema for webstore items
 */
export const generateProductSchema = (product) => ({
  "@type": "SoftwareApplication",
  name: product.title,
  description: product.description,
  applicationCategory: "BusinessApplication",
  operatingSystem: "Web",
  publisher: {
    "@id": `${BUSINESS_INFO.url}/#organization`,
  },
  offers: {
    "@type": "Offer",
    price: product.price || "Contact for pricing",
    priceCurrency: "EUR",
    availability: "https://schema.org/InStock",
  },
  aggregateRating: product.rating
    ? {
        "@type": "AggregateRating",
        ratingValue: product.rating.value,
        ratingCount: product.rating.count,
      }
    : undefined,
});

/**
 * Generate breadcrumb schema
 */
export const generateBreadcrumbSchema = (breadcrumbs) => ({
  "@type": "BreadcrumbList",
  itemListElement: breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    position: index + 1,
    item: {
      "@id": crumb.url,
      name: crumb.name,
    },
  })),
});

/**
 * Get page-specific SEO data based on page type and content
 */
export const getPageSEOData = (pageType, content = {}, language = "en") => {
  const baseKeywords = [
    "software development",
    "custom software",
    "web development",
    "AI solutions",
    "estonia",
    "tallinn",
  ];

  const seoData = {
    homepage: {
      title: "Professional Software Development Services",
      description:
        "DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.",
      keywords: [...baseKeywords, "white label software", "business comanager"],
      schema: [generateLocalBusinessSchema(), generateWebsiteSchema()],
    },
    about: {
      title: "About DevSkills - Professional Development Team",
      description:
        "Learn about DevSkills, our mission, values, and the professional team behind our innovative software development services and custom solutions.",
      keywords: [...baseKeywords, "about", "team", "company"],
      schema: [generateLocalBusinessSchema()],
    },
    services: {
      title: "Software Development Services - Custom Solutions",
      description:
        "Comprehensive software development services including custom software, web development, AI solutions, blockchain development, and mobile applications.",
      keywords: [
        ...baseKeywords,
        "services",
        "custom development",
        "blockchain",
        "mobile apps",
      ],
      schema: [generateLocalBusinessSchema()],
    },
    webstore: {
      title: "White Label Software Solutions - DevSkills Webstore",
      description:
        "Explore our white-label software solutions including Business Comanager and other professional business management tools.",
      keywords: [
        ...baseKeywords,
        "white label",
        "business software",
        "webstore",
      ],
      schema: [generateLocalBusinessSchema()],
    },
    blog: {
      title: "Software Development Blog - DevSkills Insights",
      description:
        "Latest insights, tutorials, and news about software development, AI, web technologies, and business solutions from DevSkills experts.",
      keywords: [
        ...baseKeywords,
        "blog",
        "tutorials",
        "insights",
        "technology news",
      ],
      schema: [generateLocalBusinessSchema()],
    },
    contact: {
      title: "Contact DevSkills - Get Your Custom Software Quote",
      description:
        "Contact DevSkills for professional software development services. Get a quote for your custom software, web development, or AI solution project.",
      keywords: [...baseKeywords, "contact", "quote", "consultation"],
      schema: [generateLocalBusinessSchema()],
    },
  };

  // Handle dynamic content
  if (content.title && content.description) {
    return {
      title: content.title,
      description: content.description,
      keywords: content.keywords || baseKeywords,
      schema: content.schema || [generateLocalBusinessSchema()],
    };
  }

  return seoData[pageType] || seoData.homepage;
};

/**
 * Generate language-specific keywords
 */
export const getLanguageSpecificKeywords = (baseKeywords, language) => {
  const languageKeywords = {
    et: [
      "tarkvara arendus",
      "kohandatud tarkvara",
      "veebiarendus",
      "AI lahendused",
      "eesti",
    ],
    fi: [
      "ohjelmistokehitys",
      "mukautettu ohjelmisto",
      "web-kehitys",
      "AI-ratkaisut",
      "suomi",
    ],
    de: [
      "softwareentwicklung",
      "maßgeschneiderte software",
      "webentwicklung",
      "AI-lösungen",
      "deutschland",
    ],
    sv: [
      "mjukvaruutveckling",
      "anpassad mjukvara",
      "webbutveckling",
      "AI-lösningar",
      "sverige",
    ],
  };

  return [...baseKeywords, ...(languageKeywords[language] || [])];
};

export default {
  BUSINESS_INFO,
  generateLocalBusinessSchema,
  generateWebsiteSchema,
  generateArticleSchema,
  generateProductSchema,
  generateBreadcrumbSchema,
  getPageSEOData,
  getLanguageSpecificKeywords,
};
