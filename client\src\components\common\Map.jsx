import React from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export default function Map() {
  const [mapOpen, setMapOpen] = useState(false);
  const { t } = useTranslation();
  return (
    <>
      <a href="#" className={`map-section ${mapOpen ? "js-active" : ""}`}>
        <div className="map-toggle wow fadeInUpShort" aria-hidden="true">
          <div className="mt-icon">
            <i className="mi-location"></i>
          </div>
          <div className="mt-text">
            <div onClick={() => setMapOpen((pre) => !pre)} className="mt-open">
              {t("contact.map.open")} <i className="mt-open-icon"></i>
            </div>
            <div onClick={() => setMapOpen((pre) => !pre)} className="mt-close">
              {t("contact.map.close")} <i className="mt-close-icon"></i>
            </div>
          </div>
        </div>
      </a>

      <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2028.7573128553793!2d24.7553!3d59.4372!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4692935c7d5dfa3b%3A0x4b0f5f5c5d9c1f0!2sTornim%C3%A4e%20tn%207%2C%2010145%20Tallinn!5e0!3m2!1sen!2see!4v1684450429598!5m2!1sen!2see"
        width={600}
        height={450}
        loading="lazy"
        style={{ border: 0 }}
        allowFullScreen=""
        aria-hidden="false"
        tabIndex={0}
      />
    </>
  );
}
