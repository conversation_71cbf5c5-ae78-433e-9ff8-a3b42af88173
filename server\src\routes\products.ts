import express from "express";
import {
  getProducts,
  getProductBySlug,
  createProduct,
  updateProduct,
  deleteProduct,
  getAdminProducts,
} from "../controllers/productController";
import { authenticate } from "../middleware/auth";
import { apiCache } from "../middleware/cache";

const router = express.Router();

// Public routes with caching (1 hour cache)
router.get("/", apiCache(3600), getProducts);
router.get("/:slug", apiCache(3600), getProductBySlug);

export default router;
