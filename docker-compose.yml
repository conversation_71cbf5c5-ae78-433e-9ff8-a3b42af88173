services:
  # Redis Cache (using port 6380 to avoid conflicts)
  redis:
    image: redis:7-alpine
    container_name: devskills-redis
    ports:
      - "6381:6379" # Map host 6381 to container 6379
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - devskills-network

  frontend-blue:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "8082:3000"
    networks:
      - devskills-network
    restart: always
    depends_on:
      - backend

  frontend-green:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "8083:3000"
    networks:
      - devskills-network
    restart: always
    depends_on:
      - backend

  # Backend (API + Blog Management)
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: devskills-backend
    ports:
      - "4005:4004"
    env_file:
      - ./server/.env.production
    environment:
      - NODE_ENV=production
      - CORS_ORIGIN=https://devskills.ee
      - REDIS_URL=redis://redis:6379
    volumes:
      # Map uploads directory for persistent file storage
      - ./uploads:/app/uploads
    restart: always
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test:
        [
          "CMD",
          "node",
          "-e",
          "require('http').get('http://localhost:4004/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - devskills-network

volumes:
  redis-data:
    driver: local

networks:
  devskills-network:
    external: true
