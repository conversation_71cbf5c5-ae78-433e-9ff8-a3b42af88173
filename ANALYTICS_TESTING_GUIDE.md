# Google Analytics 4 Testing Guide

## 🎯 Overview
This guide will help you test all the analytics tracking features implemented in the DevSkills website.

## 🔧 Prerequisites

### 1. Google Analytics 4 Setup
- ✅ GA4 Property ID: `G-8NEGL4LL8Q`
- ✅ Google Tag installed in HTML
- ✅ Consent mode configured

### 2. Testing Tools
- **Real-time Reports** in GA4 Dashboard
- **Tag Assistant** Chrome Extension
- **Analytics Debug Panel** (Development only)
- **Browser Developer Tools**

## 📊 Testing Methods

### Method 1: Real-time Reports in GA4 Dashboard

1. **Access GA4 Dashboard:**
   - Go to [Google Analytics](https://analytics.google.com)
   - Select your property (G-8NEGL4LL8Q)
   - Navigate to **Reports > Real-time**

2. **What to Monitor:**
   - **Users by page title and screen name** - Shows page views
   - **Event count by Event name** - Shows all custom events
   - **Users by country** - Geographic data
   - **Users by device category** - Device information

### Method 2: Tag Assistant (Recommended)

1. **Install Tag Assistant:**
   - Install [Tag Assistant Chrome Extension](https://chrome.google.com/webstore/detail/tag-assistant-legacy-by/kejbdjndbnbjgmefkgdddjlbokphdefk)

2. **Start Recording:**
   - Click Tag Assistant icon
   - Click "Record"
   - Navigate to your website

3. **Test Actions:**
   - Visit different pages
   - Submit contact form
   - Change language
   - Scroll through pages
   - Click buttons

4. **Review Results:**
   - Stop recording
   - Review all fired tags and events

### Method 3: Analytics Debug Panel (Development)

1. **Access Debug Panel:**
   - In development mode, look for "📊 Analytics Debug" button in bottom-right
   - Click to open the debug panel

2. **Run Tests:**
   - Click "Check Status" to verify analytics setup
   - Click "Run All Tests" to test all event types
   - Use individual buttons to test specific events

## 🧪 Testing Checklist

### ✅ Page Views & Engagement

| Test | How to Test | Expected Result |
|------|-------------|-----------------|
| **Page View** | Visit any page | `page_view` event in GA4 |
| **Time on Page** | Stay on page for 30+ seconds, then navigate away | `timing_complete` event |
| **Scroll Depth** | Scroll to 25%, 50%, 75%, 90%, 100% | `scroll` events at each milestone |

### ✅ User Interactions

| Test | How to Test | Expected Result |
|------|-------------|-----------------|
| **Language Change** | Click language selector (ET/EN) | `language_change` event |
| **Button Click** | Use debug panel or add tracking to buttons | `click` event with button details |
| **Navigation** | Click menu items | `navigation_click` event |

### ✅ Conversions

| Test | How to Test | Expected Result |
|------|-------------|-----------------|
| **Contact Form Success** | Submit contact form successfully | `conversion` and `generate_lead` events |
| **Contact Form Error** | Submit form with invalid data | `form_error` event |

### ✅ GDPR Compliance

| Test | How to Test | Expected Result |
|------|-------------|-----------------|
| **Consent Granted** | Accept all cookies | `consent_granted` event |
| **Consent Denied** | Reject all cookies | `consent_denied` event |
| **Custom Consent** | Customize cookie preferences | `consent_customized` event |

## 🔍 Detailed Testing Steps

### 1. Test Page Views and Engagement

```bash
# Steps:
1. Open website in incognito mode
2. Accept GDPR consent
3. Navigate to homepage
4. Wait 30 seconds
5. Scroll slowly to bottom
6. Navigate to about page
7. Wait 30 seconds
8. Navigate to contact page

# Expected GA4 Events:
- page_view (for each page)
- scroll (at 25%, 50%, 75%, 90%, 100%)
- timing_complete (when leaving each page)
```

### 2. Test Contact Form Conversion

```bash
# Steps:
1. Go to contact page
2. Fill out form with valid data:
   - Name: "Test User"
   - Email: "<EMAIL>"
   - Message: "This is a test message for analytics"
3. Submit form
4. Wait for success message

# Expected GA4 Events:
- form_submit
- conversion (value: 1, currency: EUR)
- generate_lead
```

### 3. Test Language Switching

```bash
# Steps:
1. Click language selector
2. Switch from EN to ET
3. Switch back from ET to EN

# Expected GA4 Events:
- language_change (from: en, to: et)
- language_change (from: et, to: en)
```

### 4. Test GDPR Consent

```bash
# Steps:
1. Clear browser data/use incognito
2. Visit website
3. Test each consent option:
   a. Click "Accept All"
   b. Refresh, click "Reject All"  
   c. Refresh, click "Customize" and select specific options

# Expected GA4 Events:
- consent_granted
- consent_denied  
- consent_customized
```

## 📈 GA4 Dashboard Views

### Real-time Events to Monitor:

1. **Events in the last 30 minutes:**
   - `page_view`
   - `scroll`
   - `timing_complete`
   - `language_change`
   - `form_submit`
   - `conversion`
   - `generate_lead`
   - `consent_granted`
   - `consent_denied`

2. **Custom Dimensions:**
   - `page_location`
   - `from_language` / `to_language`
   - `scroll_percentage`
   - `form_location`
   - `button_location`

### Enhanced Ecommerce Events:
- `generate_lead` (contact form submissions)
- `conversion` (successful form submissions)

## 🐛 Troubleshooting

### Common Issues:

1. **No Events Showing:**
   - Check GDPR consent is granted
   - Verify gtag is loaded: `console.log(window.gtag)`
   - Check dataLayer: `console.log(window.dataLayer)`

2. **Events Not Real-time:**
   - GA4 real-time can have 1-2 minute delay
   - Use Tag Assistant for immediate feedback

3. **Consent Mode Issues:**
   - Check localStorage: `localStorage.getItem('gdpr-consent')`
   - Verify consent update calls in Network tab

### Debug Commands:

```javascript
// Check if analytics is working
window.gtag('event', 'debug_test', {
  event_category: 'Debug',
  send_to: 'G-8NEGL4LL8Q'
});

// Check dataLayer
console.log(window.dataLayer);

// Check consent status
console.log(localStorage.getItem('gdpr-consent'));
```

## 🎯 Success Criteria

Your analytics implementation is working correctly if you can see:

✅ **Page views** for each page visit  
✅ **Scroll events** at different percentages  
✅ **Time on page** events when navigating away  
✅ **Language change** events when switching languages  
✅ **Contact form conversions** when submitting forms  
✅ **GDPR consent** events when making choices  
✅ **Real-time data** appearing in GA4 dashboard within 1-2 minutes  

## 📞 Next Steps

After confirming all events are working:

1. **Set up Custom Conversions** in GA4 for contact form submissions
2. **Create Custom Audiences** based on user behavior
3. **Set up Goals** for key user actions
4. **Configure Enhanced Ecommerce** if needed
5. **Add more button tracking** throughout the site
6. **Set up automated reports** for key metrics
