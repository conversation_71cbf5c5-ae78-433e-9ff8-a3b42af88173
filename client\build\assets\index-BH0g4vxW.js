const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/pages-static-Dj38j6FX.js","assets/vendor-react-BE9lZbv0.js","assets/vendor-misc-BUjjPnRU.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css","assets/vendor-gallery-BKyWYjF6.js","assets/vendor-admin-DSFDn6-z.js","assets/components-layout-DatcrN6Q.js","assets/components-common-BqxwEfqr.js","assets/vendor-i18n-Bi47f5qT.js","assets/pages-other-Brska3YB.js","assets/components-home-F0XSl50W.js","assets/vendor-utils-t--hEgTQ.js","assets/components-layout-LOVgDiEq.css","assets/pages-static-D4YCAEbJ.css","assets/vendor-ui-CeoT1yjb.js"])))=>i.map(i=>d[i]);
import{r as i,a as w,j as e,h as L,k as s,R as A,c as P,B as b,l as f}from"./vendor-react-BE9lZbv0.js";import{_ as n,H as R}from"./pages-other-Brska3YB.js";import"./components-layout-DatcrN6Q.js";import{x as d,y as D,G as T,E as S}from"./components-common-BqxwEfqr.js";import{R as y,W as g}from"./vendor-animations-Dl3DQHMd.js";import{c as O}from"./components-home-F0XSl50W.js";import"./vendor-misc-BUjjPnRU.js";import"./vendor-gallery-BKyWYjF6.js";import"./vendor-admin-DSFDn6-z.js";import"./vendor-i18n-Bi47f5qT.js";import"./vendor-utils-t--hEgTQ.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const c of l)if(c.type==="childList")for(const m of c.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&r(m)}).observe(document,{childList:!0,subtree:!0});function t(l){const c={};return l.integrity&&(c.integrity=l.integrity),l.referrerPolicy&&(c.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?c.credentials="include":l.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function r(l){if(l.ep)return;l.ep=!0;const c=t(l);fetch(l.href,c)}})();const I=()=>{document.querySelectorAll(".parallax-mousemove-scene").forEach(o=>{o.addEventListener("mousemove",function(a){const t=window.innerWidth,r=window.innerHeight,l=.5-(a.pageX-this.offsetLeft)/t,c=.5-(a.pageY-this.offsetTop)/r;this.querySelectorAll(".parallax-mousemove").forEach(p=>{const _=parseInt(p.getAttribute("data-offset")),E=`translate3d(${Math.round(l*_)}px, ${Math.round(c*_)}px, 0px)`;p.style.transform=E});let m=a.pageX-this.offsetLeft;a.pageY-this.offsetTop,this.querySelectorAll(".parallax-mousemove-follow").forEach(p=>{p.style.left=`${m}px`,p.style.top="31px"})}),o.addEventListener("mouseenter",function(a){this.querySelectorAll(".parallax-mousemove-follow").forEach(t=>{setTimeout(()=>{t.style.transition="all .27s var(--ease-out-short)",t.style.willChange="transform"},27)})}),o.addEventListener("mouseout",function(a){this.querySelectorAll(".parallax-mousemove-follow").forEach(t=>{t.style.transition="none"})})})};function z(){if(document.querySelectorAll("[data-rellax-y]").length&&window.innerWidth>=1280){let a=function(){document.querySelectorAll("[data-rellax-y]").forEach(t=>{t.getBoundingClientRect().top<window.innerHeight&&t.getBoundingClientRect().bottom>0?t.classList.contains("js-in-viewport")||(t.classList.add("js-in-viewport"),o.refresh()):t.classList.contains("js-in-viewport")&&t.classList.remove("js-in-viewport")})};const o=new y("[data-rellax-y]",{vertical:!0,horizontal:!1});window.addEventListener("scroll",a)}if(document.querySelectorAll("[data-rellax-x]").length&&window.innerWidth>=1280){let a=function(){document.querySelectorAll("[data-rellax-x]").forEach(t=>{t.getBoundingClientRect().top<window.innerHeight&&t.getBoundingClientRect().bottom>0?t.classList.contains("js-in-viewport")||(t.classList.add("js-in-viewport"),o.refresh()):t.classList.contains("js-in-viewport")&&t.classList.remove("js-in-viewport")})};const o=new y("[data-rellax-x]",{horizontal:!0});window.addEventListener("scroll",a)}}function V(){setTimeout(()=>{try{document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow").forEach(t=>t.classList.add("no-animate"));var o=new g({boxClass:"wow",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(t){t.classList.add("animated"),t.style.opacity="1",t.style.visibility="visible"}});document.body.classList.contains("appear-animate")||document.body.classList.add("appear-animate"),o.init(),setTimeout(()=>{document.querySelectorAll(".wow").forEach(t=>{t.classList.contains("animated")||(t.style.opacity="1",t.style.visibility="visible",t.classList.add("animated"))})},2e3),document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow-p").forEach(t=>t.classList.add("no-animate"));var a=new g({boxClass:"wow-p",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(t){t.classList.add("animated"),t.style.opacity="1",t.style.visibility="visible"}});document.body.classList.contains("appear-animate")?a.init():document.querySelectorAll(".wow-p").forEach(t=>t.style.opacity="1"),document.body.classList.contains("appear-animate")&&window.innerWidth>=1024&&document.documentElement.classList.contains("no-mobile")?document.querySelectorAll(".wow-menubar").forEach(t=>{t.classList.add("no-animate","fadeInDown","animated"),setInterval(()=>{t.classList.remove("no-animate")},1500)}):document.querySelectorAll(".wow-menubar").forEach(t=>t.style.opacity="1")}catch(t){console.error("Error initializing WOW.js:",t),document.querySelectorAll(".wow, .wow-p, .wow-menubar").forEach(r=>{r.style.opacity="1",r.classList.add("animated")})}},100)}const h=()=>{var o=document.querySelector(".main-nav"),a=document.querySelector(".nav-logo-wrap .logo"),t=document.querySelector(".light-after-scroll");if(!o)return;const r=o.classList.contains("dark-mode")||window.location.pathname==="/"||window.location.pathname.match(/^\/[a-z]{2}\/?$/);window.scrollY>0?(o.classList.remove("transparent"),o.classList.add("small-height","body-scrolled"),a&&a.classList.add("small-height"),t&&!r&&t.classList.remove("dark"),r&&!o.classList.contains("dark")&&o.classList.add("dark")):window.scrollY===0&&(o.classList.add("transparent"),o.classList.remove("small-height","body-scrolled"),a&&a.classList.remove("small-height"),t&&!r&&t.classList.add("dark"),r&&!o.classList.contains("dark")&&o.classList.add("dark"))},k=i.lazy(()=>n(()=>import("./pages-static-Dj38j6FX.js").then(o=>o.p),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14]))),q=i.lazy(()=>n(()=>import("./pages-static-Dj38j6FX.js").then(o=>o.a),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14]))),C=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.p),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),B=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.a),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),W=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.b),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),M=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.c),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),N=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.d),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),H=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.e),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),X=i.lazy(()=>n(()=>import("./pages-static-Dj38j6FX.js").then(o=>o.b),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14]))),Y=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.f),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),$=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.h),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),F=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.i),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),G=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.A),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),K=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.j),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),J=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.k),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),j=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.l),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),Q=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.n),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),v=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.o),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),U=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.q),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),Z=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.r),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),ee=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.s),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),te=i.lazy(()=>n(()=>import("./pages-other-Brska3YB.js").then(o=>o.t),__vite__mapDeps([10,1,2,3,4,5,6,7,8,9,11,12,13]))),oe=()=>e.jsxs("div",{className:"page-loader",style:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:"#1a1a1a",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:[e.jsx("div",{className:"loader",style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #fff",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("style",{children:`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `})]});function se(){const{pathname:o}=w();return i.useEffect(()=>{V(),I();const a=()=>{var r;var t=document.querySelector(".main-nav");if(!t){setTimeout(a,100);return}t.style.display="",t!=null&&t.classList.contains("transparent")?t.classList.add("js-transparent"):(r=t==null?void 0:t.classList)!=null&&r.contains("dark")||t==null||t.classList.add("js-no-transparent-white"),h()};return a(),setTimeout(a,50),window.addEventListener("scroll",h),z(),setTimeout(()=>{const t=document.title||"DevSkills",r=window.location.href;O(t,r)},100),()=>{window.removeEventListener("scroll",h)}},[o]),i.useEffect(()=>{(()=>{const t=document.querySelector(".main-nav");t&&(t.style.display="",t.style.visibility="visible",t.style.opacity="1",setTimeout(()=>{h()},100))})()},[o]),i.useEffect(()=>{typeof window<"u"&&n(()=>import("./vendor-ui-CeoT1yjb.js"),__vite__mapDeps([15,2,3,4])).then(()=>{console.log("Bootstrap loaded")})},[]),e.jsxs(e.Fragment,{children:[e.jsx(i.Suspense,{fallback:e.jsx(oe,{}),children:e.jsxs(L,{children:[e.jsxs(s,{path:"/:lang",children:[e.jsx(s,{index:!0,element:e.jsx(R,{})}),e.jsx(s,{path:"about",element:e.jsx(k,{})}),e.jsx(s,{path:"services",element:e.jsx(q,{})}),e.jsx(s,{path:"portfolio",element:e.jsx(B,{})}),e.jsx(s,{path:"portfolio-single/:id",element:e.jsx(M,{})}),e.jsx(s,{path:"webstore",element:e.jsx(C,{})}),e.jsx(s,{path:"webstore-single/:id",element:e.jsx(N,{})}),e.jsx(s,{path:"blog",element:e.jsx(W,{})}),e.jsx(s,{path:"blog-single/:id",element:e.jsx(H,{})}),e.jsx(s,{path:"contact",element:e.jsx(X,{})}),e.jsx(s,{path:"privacy-policy",element:e.jsx(Y,{})}),e.jsx(s,{path:"terms-conditions",element:e.jsx($,{})})]}),e.jsx(s,{path:"/admin",element:e.jsx(G,{})}),e.jsx(s,{path:"/admin/dashboard",element:e.jsx(K,{})}),e.jsx(s,{path:"/admin/posts",element:e.jsx(J,{})}),e.jsx(s,{path:"/admin/blog/new",element:e.jsx(j,{})}),e.jsx(s,{path:"/admin/blog/edit/:id",element:e.jsx(j,{})}),e.jsx(s,{path:"/admin/products",element:e.jsx(Q,{})}),e.jsx(s,{path:"/admin/products/new",element:e.jsx(v,{})}),e.jsx(s,{path:"/admin/products/edit/:id",element:e.jsx(v,{})}),e.jsx(s,{path:"/admin/analytics",element:e.jsx(U,{})}),e.jsx(s,{path:"/admin/categories",element:e.jsx(Z,{})}),e.jsx(s,{path:"/admin/tags",element:e.jsx(ee,{})}),e.jsx(s,{path:"/admin/comments",element:e.jsx(te,{})}),e.jsx(s,{path:"/",element:e.jsx(d,{})}),e.jsx(s,{path:"/about",element:e.jsx(d,{})}),e.jsx(s,{path:"/services",element:e.jsx(d,{})}),e.jsx(s,{path:"/portfolio",element:e.jsx(d,{})}),e.jsx(s,{path:"/portfolio-single/*",element:e.jsx(d,{})}),e.jsx(s,{path:"/blog",element:e.jsx(d,{})}),e.jsx(s,{path:"/blog-single/*",element:e.jsx(d,{})}),e.jsx(s,{path:"/contact",element:e.jsx(d,{})}),e.jsx(s,{path:"/privacy-policy",element:e.jsx(d,{})}),e.jsx(s,{path:"/terms-conditions",element:e.jsx(d,{})}),e.jsx(s,{path:"*",element:e.jsx(F,{})})]})}),e.jsx(D,{}),e.jsx(T,{})]})}const u=document.getElementById("root"),x=e.jsx(A.StrictMode,{children:e.jsx(S,{children:e.jsx(P,{children:e.jsx(b,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:e.jsx(se,{})})})})}),ae=u&&u.hasChildNodes();try{ae?f.hydrateRoot(u,x):f.createRoot(u).render(x)}catch(o){console.error("Error rendering app:",o),f.createRoot(u).render(x)}
//# sourceMappingURL=index-BH0g4vxW.js.map
