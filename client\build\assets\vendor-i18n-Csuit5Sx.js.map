{"version": 3, "mappings": ";+CAAA,MAAMA,EAAWC,GAAO,OAAOA,GAAQ,SACjCC,EAAQ,IAAM,CAClB,IAAIC,EACAC,EACJ,MAAMC,EAAU,IAAI,QAAQ,CAACC,EAASC,IAAW,CAC/CJ,EAAMG,EACNF,EAAMG,CACV,CAAG,EACD,OAAAF,EAAQ,QAAUF,EAClBE,EAAQ,OAASD,EACVC,CACT,EACMG,GAAaC,GACbA,GAAU,KAAa,GACpB,GAAKA,EAERC,GAAO,CAACC,EAAGC,EAAG,IAAM,CACxBD,EAAE,QAAQE,GAAK,CACTD,EAAEC,CAAC,IAAG,EAAEA,CAAC,EAAID,EAAEC,CAAC,EACxB,CAAG,CACH,EACMC,GAA4B,OAC5BC,GAAWC,GAAOA,GAAOA,EAAI,QAAQ,KAAK,EAAI,GAAKA,EAAI,QAAQF,GAA2B,GAAG,EAAIE,EACjGC,GAAuBR,GAAU,CAACA,GAAUT,EAASS,CAAM,EAC3DS,EAAgB,CAACT,EAAQU,EAAMC,IAAU,CAC7C,MAAMC,EAASrB,EAASmB,CAAI,EAAWA,EAAK,MAAM,GAAG,EAArBA,EAChC,IAAIG,EAAa,EACjB,KAAOA,EAAaD,EAAM,OAAS,GAAG,CACpC,GAAIJ,GAAqBR,CAAM,EAAG,MAAO,CAAE,EAC3C,MAAMO,EAAMD,GAASM,EAAMC,CAAU,CAAC,EAClC,CAACb,EAAOO,CAAG,GAAKI,IAAOX,EAAOO,CAAG,EAAI,IAAII,GACzC,OAAO,UAAU,eAAe,KAAKX,EAAQO,CAAG,EAClDP,EAASA,EAAOO,CAAG,EAEnBP,EAAS,CAAE,EAEb,EAAEa,CACN,CACE,OAAIL,GAAqBR,CAAM,EAAU,CAAE,EACpC,CACL,IAAKA,EACL,EAAGM,GAASM,EAAMC,CAAU,CAAC,CAC9B,CACH,EACMC,GAAU,CAACd,EAAQU,EAAMK,IAAa,CAC1C,KAAM,CACJ,IAAAvB,EACA,EAAAwB,CACD,EAAGP,EAAcT,EAAQU,EAAM,MAAM,EACtC,GAAIlB,IAAQ,QAAakB,EAAK,SAAW,EAAG,CAC1ClB,EAAIwB,CAAC,EAAID,EACT,MACJ,CACE,IAAIE,EAAIP,EAAKA,EAAK,OAAS,CAAC,EACxBQ,EAAIR,EAAK,MAAM,EAAGA,EAAK,OAAS,CAAC,EACjCS,EAAOV,EAAcT,EAAQkB,EAAG,MAAM,EAC1C,KAAOC,EAAK,MAAQ,QAAaD,EAAE,QACjCD,EAAI,GAAGC,EAAEA,EAAE,OAAS,CAAC,CAAC,IAAID,CAAC,GAC3BC,EAAIA,EAAE,MAAM,EAAGA,EAAE,OAAS,CAAC,EAC3BC,EAAOV,EAAcT,EAAQkB,EAAG,MAAM,EAClCC,GAAA,MAAAA,EAAM,KAAO,OAAOA,EAAK,IAAI,GAAGA,EAAK,CAAC,IAAIF,CAAC,EAAE,EAAM,MACrDE,EAAK,IAAM,QAGfA,EAAK,IAAI,GAAGA,EAAK,CAAC,IAAIF,CAAC,EAAE,EAAIF,CAC/B,EACMK,GAAW,CAACpB,EAAQU,EAAMK,EAAUM,IAAW,CACnD,KAAM,CACJ,IAAA7B,EACA,EAAAwB,CACD,EAAGP,EAAcT,EAAQU,EAAM,MAAM,EACtClB,EAAIwB,CAAC,EAAIxB,EAAIwB,CAAC,GAAK,CAAE,EACrBxB,EAAIwB,CAAC,EAAE,KAAKD,CAAQ,CACtB,EACMO,EAAU,CAACtB,EAAQU,IAAS,CAChC,KAAM,CACJ,IAAAlB,EACA,EAAAwB,CACJ,EAAMP,EAAcT,EAAQU,CAAI,EAC9B,GAAKlB,GACA,OAAO,UAAU,eAAe,KAAKA,EAAKwB,CAAC,EAChD,OAAOxB,EAAIwB,CAAC,CACd,EACMO,GAAsB,CAACC,EAAMC,EAAalB,IAAQ,CACtD,MAAMmB,EAAQJ,EAAQE,EAAMjB,CAAG,EAC/B,OAAImB,IAAU,OACLA,EAEFJ,EAAQG,EAAalB,CAAG,CACjC,EACMoB,GAAa,CAACC,EAAQC,EAAQC,IAAc,CAChD,UAAWC,KAAQF,EACbE,IAAS,aAAeA,IAAS,gBAC/BA,KAAQH,EACNrC,EAASqC,EAAOG,CAAI,CAAC,GAAKH,EAAOG,CAAI,YAAa,QAAUxC,EAASsC,EAAOE,CAAI,CAAC,GAAKF,EAAOE,CAAI,YAAa,OAC5GD,IAAWF,EAAOG,CAAI,EAAIF,EAAOE,CAAI,GAEzCJ,GAAWC,EAAOG,CAAI,EAAGF,EAAOE,CAAI,EAAGD,CAAS,EAGlDF,EAAOG,CAAI,EAAIF,EAAOE,CAAI,GAIhC,OAAOH,CACT,EACMI,EAAcC,GAAOA,EAAI,QAAQ,sCAAuC,MAAM,EACpF,IAAIC,GAAa,CACf,IAAK,QACL,IAAK,OACL,IAAK,OACL,IAAK,SACL,IAAK,QACL,IAAK,QACP,EACA,MAAMC,GAASX,GACTjC,EAASiC,CAAI,EACRA,EAAK,QAAQ,aAAcrB,GAAK+B,GAAW/B,CAAC,CAAC,EAE/CqB,EAET,MAAMY,EAAY,CAChB,YAAYC,EAAU,CACpB,KAAK,SAAWA,EAChB,KAAK,UAAY,IAAI,IACrB,KAAK,YAAc,CAAE,CACzB,CACE,UAAUC,EAAS,CACjB,MAAMC,EAAkB,KAAK,UAAU,IAAID,CAAO,EAClD,GAAIC,IAAoB,OACtB,OAAOA,EAET,MAAMC,EAAY,IAAI,OAAOF,CAAO,EACpC,OAAI,KAAK,YAAY,SAAW,KAAK,UACnC,KAAK,UAAU,OAAO,KAAK,YAAY,MAAK,CAAE,EAEhD,KAAK,UAAU,IAAIA,EAASE,CAAS,EACrC,KAAK,YAAY,KAAKF,CAAO,EACtBE,CACX,CACA,CACA,MAAMC,GAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAChCC,GAAiC,IAAIN,GAAY,EAAE,EACnDO,GAAsB,CAACpC,EAAKqC,EAAaC,IAAiB,CAC9DD,EAAcA,GAAe,GAC7BC,EAAeA,GAAgB,GAC/B,MAAMC,EAAgBL,GAAM,OAAOM,GAAKH,EAAY,QAAQG,CAAC,EAAI,GAAKF,EAAa,QAAQE,CAAC,EAAI,CAAC,EACjG,GAAID,EAAc,SAAW,EAAG,MAAO,GACvC,MAAME,EAAIN,GAA+B,UAAU,IAAII,EAAc,IAAIC,GAAKA,IAAM,IAAM,MAAQA,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EACjH,IAAIE,EAAU,CAACD,EAAE,KAAKzC,CAAG,EACzB,GAAI,CAAC0C,EAAS,CACZ,MAAMC,EAAK3C,EAAI,QAAQsC,CAAY,EAC/BK,EAAK,GAAK,CAACF,EAAE,KAAKzC,EAAI,UAAU,EAAG2C,CAAE,CAAC,IACxCD,EAAU,GAEhB,CACE,OAAOA,CACT,EACME,GAAW,CAAC3D,EAAKkB,EAAMmC,EAAe,MAAQ,CAClD,GAAI,CAACrD,EAAK,OACV,GAAIA,EAAIkB,CAAI,EACV,OAAK,OAAO,UAAU,eAAe,KAAKlB,EAAKkB,CAAI,EAC5ClB,EAAIkB,CAAI,EADuC,OAGxD,MAAM0C,EAAS1C,EAAK,MAAMmC,CAAY,EACtC,IAAIQ,EAAU7D,EACd,QAAS8D,EAAI,EAAGA,EAAIF,EAAO,QAAS,CAClC,GAAI,CAACC,GAAW,OAAOA,GAAY,SACjC,OAEF,IAAIE,EACAC,EAAW,GACf,QAASC,EAAIH,EAAGG,EAAIL,EAAO,OAAQ,EAAEK,EAMnC,GALIA,IAAMH,IACRE,GAAYX,GAEdW,GAAYJ,EAAOK,CAAC,EACpBF,EAAOF,EAAQG,CAAQ,EACnBD,IAAS,OAAW,CACtB,GAAI,CAAC,SAAU,SAAU,SAAS,EAAE,QAAQ,OAAOA,CAAI,EAAI,IAAME,EAAIL,EAAO,OAAS,EACnF,SAEFE,GAAKG,EAAIH,EAAI,EACb,KACR,CAEID,EAAUE,CACd,CACE,OAAOF,CACT,EACMK,EAAiBC,GAAQA,GAAA,YAAAA,EAAM,QAAQ,IAAK,KAE5CC,GAAgB,CACpB,KAAM,SACN,IAAIC,EAAM,CACR,KAAK,OAAO,MAAOA,CAAI,CACxB,EACD,KAAKA,EAAM,CACT,KAAK,OAAO,OAAQA,CAAI,CACzB,EACD,MAAMA,EAAM,CACV,KAAK,OAAO,QAASA,CAAI,CAC1B,EACD,OAAOC,EAAMD,EAAM,UACjBE,GAAAC,EAAA,6BAAUF,KAAV,YAAAE,EAAiB,QAAjB,MAAAD,EAAA,KAAAC,EAAyB,QAASH,EACtC,CACA,EACA,MAAMI,CAAO,CACX,YAAYC,EAAgBC,EAAU,GAAI,CACxC,KAAK,KAAKD,EAAgBC,CAAO,CACrC,CACE,KAAKD,EAAgBC,EAAU,GAAI,CACjC,KAAK,OAASA,EAAQ,QAAU,WAChC,KAAK,OAASD,GAAkBN,GAChC,KAAK,QAAUO,EACf,KAAK,MAAQA,EAAQ,KACzB,CACE,OAAON,EAAM,CACX,OAAO,KAAK,QAAQA,EAAM,MAAO,GAAI,EAAI,CAC7C,CACE,QAAQA,EAAM,CACZ,OAAO,KAAK,QAAQA,EAAM,OAAQ,GAAI,EAAI,CAC9C,CACE,SAASA,EAAM,CACb,OAAO,KAAK,QAAQA,EAAM,QAAS,EAAE,CACzC,CACE,aAAaA,EAAM,CACjB,OAAO,KAAK,QAAQA,EAAM,OAAQ,uBAAwB,EAAI,CAClE,CACE,QAAQA,EAAMO,EAAKC,EAAQC,EAAW,CACpC,OAAIA,GAAa,CAAC,KAAK,MAAc,MACjC/E,EAASsE,EAAK,CAAC,CAAC,IAAGA,EAAK,CAAC,EAAI,GAAGQ,CAAM,GAAG,KAAK,MAAM,IAAIR,EAAK,CAAC,CAAC,IAC5D,KAAK,OAAOO,CAAG,EAAEP,CAAI,EAChC,CACE,OAAOU,EAAY,CACjB,OAAO,IAAIN,EAAO,KAAK,OAAQ,CAE3B,OAAQ,GAAG,KAAK,MAAM,IAAIM,CAAU,IAEtC,GAAG,KAAK,OACd,CAAK,CACL,CACE,MAAMJ,EAAS,CACb,OAAAA,EAAUA,GAAW,KAAK,QAC1BA,EAAQ,OAASA,EAAQ,QAAU,KAAK,OACjC,IAAIF,EAAO,KAAK,OAAQE,CAAO,CAC1C,CACA,CACA,IAAIK,EAAa,IAAIP,EAErB,MAAMQ,EAAa,CACjB,aAAc,CACZ,KAAK,UAAY,CAAE,CACvB,CACE,GAAGC,EAAQC,EAAU,CACnB,OAAAD,EAAO,MAAM,GAAG,EAAE,QAAQE,GAAS,CAC5B,KAAK,UAAUA,CAAK,IAAG,KAAK,UAAUA,CAAK,EAAI,IAAI,KACxD,MAAMC,EAAe,KAAK,UAAUD,CAAK,EAAE,IAAID,CAAQ,GAAK,EAC5D,KAAK,UAAUC,CAAK,EAAE,IAAID,EAAUE,EAAe,CAAC,CAC1D,CAAK,EACM,IACX,CACE,IAAID,EAAOD,EAAU,CACnB,GAAK,KAAK,UAAUC,CAAK,EACzB,IAAI,CAACD,EAAU,CACb,OAAO,KAAK,UAAUC,CAAK,EAC3B,MACN,CACI,KAAK,UAAUA,CAAK,EAAE,OAAOD,CAAQ,EACzC,CACE,KAAKC,KAAUf,EAAM,CACf,KAAK,UAAUe,CAAK,GACP,MAAM,KAAK,KAAK,UAAUA,CAAK,EAAE,SAAS,EAClD,QAAQ,CAAC,CAACE,EAAUC,CAAa,IAAM,CAC5C,QAASzB,EAAI,EAAGA,EAAIyB,EAAezB,IACjCwB,EAAS,GAAGjB,CAAI,CAE1B,CAAO,EAEC,KAAK,UAAU,GAAG,GACL,MAAM,KAAK,KAAK,UAAU,GAAG,EAAE,SAAS,EAChD,QAAQ,CAAC,CAACiB,EAAUC,CAAa,IAAM,CAC5C,QAASzB,EAAI,EAAGA,EAAIyB,EAAezB,IACjCwB,EAAS,MAAMA,EAAU,CAACF,EAAO,GAAGf,CAAI,CAAC,CAEnD,CAAO,CAEP,CACA,CAEA,MAAMmB,WAAsBP,EAAa,CACvC,YAAYjD,EAAM2C,EAAU,CAC1B,GAAI,CAAC,aAAa,EAClB,UAAW,aACf,EAAK,CACD,MAAO,EACP,KAAK,KAAO3C,GAAQ,CAAE,EACtB,KAAK,QAAU2C,EACX,KAAK,QAAQ,eAAiB,SAChC,KAAK,QAAQ,aAAe,KAE1B,KAAK,QAAQ,sBAAwB,SACvC,KAAK,QAAQ,oBAAsB,GAEzC,CACE,cAAcc,EAAI,CACZ,KAAK,QAAQ,GAAG,QAAQA,CAAE,EAAI,GAChC,KAAK,QAAQ,GAAG,KAAKA,CAAE,CAE7B,CACE,iBAAiBA,EAAI,CACnB,MAAMC,EAAQ,KAAK,QAAQ,GAAG,QAAQD,CAAE,EACpCC,EAAQ,IACV,KAAK,QAAQ,GAAG,OAAOA,EAAO,CAAC,CAErC,CACE,YAAYC,EAAKF,EAAI1E,EAAK4D,EAAU,GAAI,SACtC,MAAMtB,EAAesB,EAAQ,eAAiB,OAAYA,EAAQ,aAAe,KAAK,QAAQ,aACxFiB,EAAsBjB,EAAQ,sBAAwB,OAAYA,EAAQ,oBAAsB,KAAK,QAAQ,oBACnH,IAAIzD,EACAyE,EAAI,QAAQ,GAAG,EAAI,GACrBzE,EAAOyE,EAAI,MAAM,GAAG,GAEpBzE,EAAO,CAACyE,EAAKF,CAAE,EACX1E,IACE,MAAM,QAAQA,CAAG,EACnBG,EAAK,KAAK,GAAGH,CAAG,EACPhB,EAASgB,CAAG,GAAKsC,EAC1BnC,EAAK,KAAK,GAAGH,EAAI,MAAMsC,CAAY,CAAC,EAEpCnC,EAAK,KAAKH,CAAG,IAInB,MAAM8E,EAAS/D,EAAQ,KAAK,KAAMZ,CAAI,EAMtC,MALI,CAAC2E,GAAU,CAACJ,GAAM,CAAC1E,GAAO4E,EAAI,QAAQ,GAAG,EAAI,KAC/CA,EAAMzE,EAAK,CAAC,EACZuE,EAAKvE,EAAK,CAAC,EACXH,EAAMG,EAAK,MAAM,CAAC,EAAE,KAAK,GAAG,GAE1B2E,GAAU,CAACD,GAAuB,CAAC7F,EAASgB,CAAG,EAAU8E,EACtDlC,IAASY,GAAAC,EAAA,KAAK,OAAL,YAAAA,EAAYmB,KAAZ,YAAApB,EAAmBkB,GAAK1E,EAAKsC,CAAY,CAC7D,CACE,YAAYsC,EAAKF,EAAI1E,EAAKmB,EAAOyC,EAAU,CACzC,OAAQ,EACZ,EAAK,CACD,MAAMtB,EAAesB,EAAQ,eAAiB,OAAYA,EAAQ,aAAe,KAAK,QAAQ,aAC9F,IAAIzD,EAAO,CAACyE,EAAKF,CAAE,EACf1E,IAAKG,EAAOA,EAAK,OAAOmC,EAAetC,EAAI,MAAMsC,CAAY,EAAItC,CAAG,GACpE4E,EAAI,QAAQ,GAAG,EAAI,KACrBzE,EAAOyE,EAAI,MAAM,GAAG,EACpBzD,EAAQuD,EACRA,EAAKvE,EAAK,CAAC,GAEb,KAAK,cAAcuE,CAAE,EACrBnE,GAAQ,KAAK,KAAMJ,EAAMgB,CAAK,EACzByC,EAAQ,QAAQ,KAAK,KAAK,QAASgB,EAAKF,EAAI1E,EAAKmB,CAAK,CAC/D,CACE,aAAayD,EAAKF,EAAIK,EAAWnB,EAAU,CACzC,OAAQ,EACZ,EAAK,CACD,UAAW/D,KAAKkF,GACV/F,EAAS+F,EAAUlF,CAAC,CAAC,GAAK,MAAM,QAAQkF,EAAUlF,CAAC,CAAC,IAAG,KAAK,YAAY+E,EAAKF,EAAI7E,EAAGkF,EAAUlF,CAAC,EAAG,CACpG,OAAQ,EAChB,CAAO,EAEE+D,EAAQ,QAAQ,KAAK,KAAK,QAASgB,EAAKF,EAAIK,CAAS,CAC9D,CACE,kBAAkBH,EAAKF,EAAIK,EAAWC,EAAMzD,EAAWqC,EAAU,CAC/D,OAAQ,GACR,SAAU,EACd,EAAK,CACD,IAAIzD,EAAO,CAACyE,EAAKF,CAAE,EACfE,EAAI,QAAQ,GAAG,EAAI,KACrBzE,EAAOyE,EAAI,MAAM,GAAG,EACpBI,EAAOD,EACPA,EAAYL,EACZA,EAAKvE,EAAK,CAAC,GAEb,KAAK,cAAcuE,CAAE,EACrB,IAAIO,EAAOlE,EAAQ,KAAK,KAAMZ,CAAI,GAAK,CAAE,EACpCyD,EAAQ,WAAUmB,EAAY,KAAK,MAAM,KAAK,UAAUA,CAAS,CAAC,GACnEC,EACF5D,GAAW6D,EAAMF,EAAWxD,CAAS,EAErC0D,EAAO,CACL,GAAGA,EACH,GAAGF,CACJ,EAEHxE,GAAQ,KAAK,KAAMJ,EAAM8E,CAAI,EACxBrB,EAAQ,QAAQ,KAAK,KAAK,QAASgB,EAAKF,EAAIK,CAAS,CAC9D,CACE,qBAAqBH,EAAKF,EAAI,CACxB,KAAK,kBAAkBE,EAAKF,CAAE,GAChC,OAAO,KAAK,KAAKE,CAAG,EAAEF,CAAE,EAE1B,KAAK,iBAAiBA,CAAE,EACxB,KAAK,KAAK,UAAWE,EAAKF,CAAE,CAChC,CACE,kBAAkBE,EAAKF,EAAI,CACzB,OAAO,KAAK,YAAYE,EAAKF,CAAE,IAAM,MACzC,CACE,kBAAkBE,EAAKF,EAAI,CACzB,OAAKA,IAAIA,EAAK,KAAK,QAAQ,WACpB,KAAK,YAAYE,EAAKF,CAAE,CACnC,CACE,kBAAkBE,EAAK,CACrB,OAAO,KAAK,KAAKA,CAAG,CACxB,CACE,4BAA4BA,EAAK,CAC/B,MAAM3D,EAAO,KAAK,kBAAkB2D,CAAG,EAEvC,MAAO,CAAC,EADE3D,GAAQ,OAAO,KAAKA,CAAI,GAAK,CAAE,GAC9B,KAAKiE,GAAKjE,EAAKiE,CAAC,GAAK,OAAO,KAAKjE,EAAKiE,CAAC,CAAC,EAAE,OAAS,CAAC,CACnE,CACE,QAAS,CACP,OAAO,KAAK,IAChB,CACA,CAEA,IAAIC,GAAgB,CAClB,WAAY,CAAE,EACd,iBAAiBC,EAAQ,CACvB,KAAK,WAAWA,EAAO,IAAI,EAAIA,CAChC,EACD,OAAOC,EAAYlE,EAAOnB,EAAK4D,EAAS0B,EAAY,CAClD,OAAAD,EAAW,QAAQE,GAAa,OAC9BpE,IAAQsC,EAAA,KAAK,WAAW8B,CAAS,IAAzB,YAAA9B,EAA4B,QAAQtC,EAAOnB,EAAK4D,EAAS0B,KAAenE,CACtF,CAAK,EACMA,CACX,CACA,EAEA,MAAMqE,GAAmB,CAAE,EACrBC,GAAuBtG,GAAO,CAACH,EAASG,CAAG,GAAK,OAAOA,GAAQ,WAAa,OAAOA,GAAQ,SACjG,MAAMuG,UAAmBxB,EAAa,CACpC,YAAYyB,EAAU/B,EAAU,GAAI,CAClC,MAAO,EACPlE,GAAK,CAAC,gBAAiB,gBAAiB,iBAAkB,eAAgB,mBAAoB,aAAc,OAAO,EAAGiG,EAAU,IAAI,EACpI,KAAK,QAAU/B,EACX,KAAK,QAAQ,eAAiB,SAChC,KAAK,QAAQ,aAAe,KAE9B,KAAK,OAASK,EAAW,OAAO,YAAY,CAChD,CACE,eAAeW,EAAK,CACdA,IAAK,KAAK,SAAWA,EAC7B,CACE,OAAO5E,EAAK4F,EAAI,CACd,cAAe,EACnB,EAAK,CACD,MAAMC,EAAM,CACV,GAAGD,CACJ,EACD,GAAI5F,GAAO,KAAM,MAAO,GACxB,MAAM8F,EAAW,KAAK,QAAQ9F,EAAK6F,CAAG,EACtC,OAAOC,GAAA,YAAAA,EAAU,OAAQ,MAC7B,CACE,eAAe9F,EAAK6F,EAAK,CACvB,IAAIxD,EAAcwD,EAAI,cAAgB,OAAYA,EAAI,YAAc,KAAK,QAAQ,YAC7ExD,IAAgB,SAAWA,EAAc,KAC7C,MAAMC,EAAeuD,EAAI,eAAiB,OAAYA,EAAI,aAAe,KAAK,QAAQ,aACtF,IAAIE,EAAaF,EAAI,IAAM,KAAK,QAAQ,WAAa,CAAE,EACvD,MAAMG,EAAuB3D,GAAerC,EAAI,QAAQqC,CAAW,EAAI,GACjE4D,EAAuB,CAAC,KAAK,QAAQ,yBAA2B,CAACJ,EAAI,cAAgB,CAAC,KAAK,QAAQ,wBAA0B,CAACA,EAAI,aAAe,CAACzD,GAAoBpC,EAAKqC,EAAaC,CAAY,EAC1M,GAAI0D,GAAwB,CAACC,EAAsB,CACjD,MAAMpG,EAAIG,EAAI,MAAM,KAAK,aAAa,aAAa,EACnD,GAAIH,GAAKA,EAAE,OAAS,EAClB,MAAO,CACL,IAAAG,EACA,WAAYhB,EAAS+G,CAAU,EAAI,CAACA,CAAU,EAAIA,CACnD,EAEH,MAAMG,EAAQlG,EAAI,MAAMqC,CAAW,GAC/BA,IAAgBC,GAAgBD,IAAgBC,GAAgB,KAAK,QAAQ,GAAG,QAAQ4D,EAAM,CAAC,CAAC,EAAI,MAAIH,EAAaG,EAAM,MAAO,GACtIlG,EAAMkG,EAAM,KAAK5D,CAAY,CACnC,CACI,MAAO,CACL,IAAAtC,EACA,WAAYhB,EAAS+G,CAAU,EAAI,CAACA,CAAU,EAAIA,CACnD,CACL,CACE,UAAUI,EAAMP,EAAGQ,EAAS,CAC1B,IAAIP,EAAM,OAAOD,GAAM,SAAW,CAChC,GAAGA,CACT,EAAQA,EAQJ,GAPI,OAAOC,GAAQ,UAAY,KAAK,QAAQ,mCAC1CA,EAAM,KAAK,QAAQ,iCAAiC,SAAS,GAE3D,OAAO,SAAY,WAAUA,EAAM,CACrC,GAAGA,CACJ,GACIA,IAAKA,EAAM,CAAE,GACdM,GAAQ,KAAM,MAAO,GACpB,MAAM,QAAQA,CAAI,IAAGA,EAAO,CAAC,OAAOA,CAAI,CAAC,GAC9C,MAAME,EAAgBR,EAAI,gBAAkB,OAAYA,EAAI,cAAgB,KAAK,QAAQ,cACnFvD,EAAeuD,EAAI,eAAiB,OAAYA,EAAI,aAAe,KAAK,QAAQ,aAChF,CACJ,IAAA7F,EACA,WAAA+F,CACN,EAAQ,KAAK,eAAeI,EAAKA,EAAK,OAAS,CAAC,EAAGN,CAAG,EAC5CS,EAAYP,EAAWA,EAAW,OAAS,CAAC,EAClD,IAAI1D,EAAcwD,EAAI,cAAgB,OAAYA,EAAI,YAAc,KAAK,QAAQ,YAC7ExD,IAAgB,SAAWA,EAAc,KAC7C,MAAMuC,EAAMiB,EAAI,KAAO,KAAK,SACtBU,EAA0BV,EAAI,yBAA2B,KAAK,QAAQ,wBAC5E,IAAIjB,GAAA,YAAAA,EAAK,iBAAkB,SACzB,OAAI2B,EACEF,EACK,CACL,IAAK,GAAGC,CAAS,GAAGjE,CAAW,GAAGrC,CAAG,GACrC,QAASA,EACT,aAAcA,EACd,QAAS4E,EACT,OAAQ0B,EACR,WAAY,KAAK,qBAAqBT,CAAG,CAC1C,EAEI,GAAGS,CAAS,GAAGjE,CAAW,GAAGrC,CAAG,GAErCqG,EACK,CACL,IAAKrG,EACL,QAASA,EACT,aAAcA,EACd,QAAS4E,EACT,OAAQ0B,EACR,WAAY,KAAK,qBAAqBT,CAAG,CAC1C,EAEI7F,EAET,MAAM8F,EAAW,KAAK,QAAQK,EAAMN,CAAG,EACvC,IAAI1G,EAAM2G,GAAA,YAAAA,EAAU,IACpB,MAAMU,GAAaV,GAAA,YAAAA,EAAU,UAAW9F,EAClCyG,GAAkBX,GAAA,YAAAA,EAAU,eAAgB9F,EAC5C0G,EAAW,CAAC,kBAAmB,oBAAqB,iBAAiB,EACrEC,EAAad,EAAI,aAAe,OAAYA,EAAI,WAAa,KAAK,QAAQ,WAC1Ee,EAA6B,CAAC,KAAK,YAAc,KAAK,WAAW,eACjEC,EAAsBhB,EAAI,QAAU,QAAa,CAAC7G,EAAS6G,EAAI,KAAK,EACpEiB,EAAkBpB,EAAW,gBAAgBG,CAAG,EAChDkB,EAAqBF,EAAsB,KAAK,eAAe,UAAUjC,EAAKiB,EAAI,MAAOA,CAAG,EAAI,GAChGmB,EAAoCnB,EAAI,SAAWgB,EAAsB,KAAK,eAAe,UAAUjC,EAAKiB,EAAI,MAAO,CAC3H,QAAS,EACV,GAAI,GACCoB,EAAwBJ,GAAuB,CAAChB,EAAI,SAAWA,EAAI,QAAU,EAC7EqB,EAAeD,GAAyBpB,EAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,GAAKA,EAAI,eAAekB,CAAkB,EAAE,GAAKlB,EAAI,eAAemB,CAAiC,EAAE,GAAKnB,EAAI,aACnN,IAAIsB,EAAgBhI,EAChByH,GAA8B,CAACzH,GAAO2H,IACxCK,EAAgBD,GAElB,MAAME,GAAiB3B,GAAqB0B,CAAa,EACnDE,GAAU,OAAO,UAAU,SAAS,MAAMF,CAAa,EAC7D,GAAIP,GAA8BO,GAAiBC,IAAkBV,EAAS,QAAQW,EAAO,EAAI,GAAK,EAAErI,EAAS2H,CAAU,GAAK,MAAM,QAAQQ,CAAa,GAAI,CAC7J,GAAI,CAACtB,EAAI,eAAiB,CAAC,KAAK,QAAQ,cAAe,CAChD,KAAK,QAAQ,uBAChB,KAAK,OAAO,KAAK,iEAAiE,EAEpF,MAAMpD,EAAI,KAAK,QAAQ,sBAAwB,KAAK,QAAQ,sBAAsB+D,EAAYW,EAAe,CAC3G,GAAGtB,EACH,GAAIE,CACd,CAAS,EAAI,QAAQ/F,CAAG,KAAK,KAAK,QAAQ,2CAClC,OAAIqG,GACFP,EAAS,IAAMrD,EACfqD,EAAS,WAAa,KAAK,qBAAqBD,CAAG,EAC5CC,GAEFrD,CACf,CACM,GAAIH,EAAc,CAChB,MAAMgF,EAAiB,MAAM,QAAQH,CAAa,EAC5CzH,EAAO4H,EAAiB,GAAK,CAAE,EAC/BC,GAAcD,EAAiBb,EAAkBD,EACvD,UAAW3G,KAAKsH,EACd,GAAI,OAAO,UAAU,eAAe,KAAKA,EAAetH,CAAC,EAAG,CAC1D,MAAM2H,EAAU,GAAGD,EAAW,GAAGjF,CAAY,GAAGzC,CAAC,GAC7CiH,GAAmB,CAAC3H,EACtBO,EAAKG,CAAC,EAAI,KAAK,UAAU2H,EAAS,CAChC,GAAG3B,EACH,aAAcJ,GAAqByB,CAAY,EAAIA,EAAarH,CAAC,EAAI,OAEnE,WAAY,GACZ,GAAIkG,CAEtB,CAAe,EAEDrG,EAAKG,CAAC,EAAI,KAAK,UAAU2H,EAAS,CAChC,GAAG3B,EAED,WAAY,GACZ,GAAIE,CAEtB,CAAe,EAECrG,EAAKG,CAAC,IAAM2H,IAAS9H,EAAKG,CAAC,EAAIsH,EAActH,CAAC,EAC9D,CAEQV,EAAMO,CACd,CACA,SAAekH,GAA8B5H,EAAS2H,CAAU,GAAK,MAAM,QAAQxH,CAAG,EAChFA,EAAMA,EAAI,KAAKwH,CAAU,EACrBxH,IAAKA,EAAM,KAAK,kBAAkBA,EAAKgH,EAAMN,EAAKO,CAAO,OACxD,CACL,IAAIqB,EAAc,GACdC,EAAU,GACV,CAAC,KAAK,cAAcvI,CAAG,GAAK2H,IAC9BW,EAAc,GACdtI,EAAM+H,GAEH,KAAK,cAAc/H,CAAG,IACzBuI,EAAU,GACVvI,EAAMa,GAGR,MAAM2H,GADiC9B,EAAI,gCAAkC,KAAK,QAAQ,iCAClC6B,EAAU,OAAYvI,EACxEyI,EAAgBd,GAAmBI,IAAiB/H,GAAO,KAAK,QAAQ,cAC9E,GAAIuI,GAAWD,GAAeG,EAAe,CAE3C,GADA,KAAK,OAAO,IAAIA,EAAgB,YAAc,aAAchD,EAAK0B,EAAWtG,EAAK4H,EAAgBV,EAAe/H,CAAG,EAC/GmD,EAAc,CAChB,MAAMuF,EAAK,KAAK,QAAQ7H,EAAK,CAC3B,GAAG6F,EACH,aAAc,EAC1B,CAAW,EACGgC,GAAMA,EAAG,KAAK,KAAK,OAAO,KAAK,iLAAiL,CAC9N,CACQ,IAAIC,EAAO,CAAE,EACb,MAAMC,EAAe,KAAK,cAAc,iBAAiB,KAAK,QAAQ,YAAalC,EAAI,KAAO,KAAK,QAAQ,EAC3G,GAAI,KAAK,QAAQ,gBAAkB,YAAckC,GAAgBA,EAAa,CAAC,EAC7E,QAAShF,EAAI,EAAGA,EAAIgF,EAAa,OAAQhF,IACvC+E,EAAK,KAAKC,EAAahF,CAAC,CAAC,OAElB,KAAK,QAAQ,gBAAkB,MACxC+E,EAAO,KAAK,cAAc,mBAAmBjC,EAAI,KAAO,KAAK,QAAQ,EAErEiC,EAAK,KAAKjC,EAAI,KAAO,KAAK,QAAQ,EAEpC,MAAMmC,GAAO,CAACC,EAAGxH,EAAGyH,IAAyB,QAC3C,MAAMC,GAAoBrB,GAAmBoB,IAAyB/I,EAAM+I,EAAuBP,EAC/F,KAAK,QAAQ,kBACf,KAAK,QAAQ,kBAAkBM,EAAG3B,EAAW7F,EAAG0H,GAAmBP,EAAe/B,CAAG,GAC5EpC,GAAA,KAAK,mBAAL,MAAAA,GAAuB,aAChC,KAAK,iBAAiB,YAAYwE,EAAG3B,EAAW7F,EAAG0H,GAAmBP,EAAe/B,CAAG,EAE1F,KAAK,KAAK,aAAcoC,EAAG3B,EAAW7F,EAAGtB,CAAG,CAC7C,EACG,KAAK,QAAQ,cACX,KAAK,QAAQ,oBAAsB0H,EACrCiB,EAAK,QAAQM,GAAY,CACvB,MAAMC,EAAW,KAAK,eAAe,YAAYD,EAAUvC,CAAG,EAC1DoB,GAAyBpB,EAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,GAAKwC,EAAS,QAAQ,GAAG,KAAK,QAAQ,eAAe,MAAM,EAAI,GAC/IA,EAAS,KAAK,GAAG,KAAK,QAAQ,eAAe,MAAM,EAErDA,EAAS,QAAQC,GAAU,CACzBN,GAAK,CAACI,CAAQ,EAAGpI,EAAMsI,EAAQzC,EAAI,eAAeyC,CAAM,EAAE,GAAKpB,CAAY,CAC3F,CAAe,CACf,CAAa,EAEDc,GAAKF,EAAM9H,EAAKkH,CAAY,EAGxC,CACM/H,EAAM,KAAK,kBAAkBA,EAAKgH,EAAMN,EAAKC,EAAUM,CAAO,EAC1DsB,GAAWvI,IAAQa,GAAO,KAAK,QAAQ,8BACzCb,EAAM,GAAGmH,CAAS,GAAGjE,CAAW,GAAGrC,CAAG,KAEnC0H,GAAWD,IAAgB,KAAK,QAAQ,yBAC3CtI,EAAM,KAAK,QAAQ,uBAAuB,KAAK,QAAQ,4BAA8B,GAAGmH,CAAS,GAAGjE,CAAW,GAAGrC,CAAG,GAAKA,EAAKyH,EAActI,EAAM,OAAW0G,CAAG,EAEzK,CACI,OAAIQ,GACFP,EAAS,IAAM3G,EACf2G,EAAS,WAAa,KAAK,qBAAqBD,CAAG,EAC5CC,GAEF3G,CACX,CACE,kBAAkBA,EAAKa,EAAK6F,EAAKC,EAAUM,EAAS,SAClD,IAAI3C,EAAA,KAAK,aAAL,MAAAA,EAAiB,MACnBtE,EAAM,KAAK,WAAW,MAAMA,EAAK,CAC/B,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAG0G,CACJ,EAAEA,EAAI,KAAO,KAAK,UAAYC,EAAS,QAASA,EAAS,OAAQA,EAAS,QAAS,CAClF,SAAAA,CACR,CAAO,UACQ,CAACD,EAAI,kBAAmB,CAC7BA,EAAI,eAAe,KAAK,aAAa,KAAK,CAC5C,GAAGA,EAED,cAAe,CACb,GAAG,KAAK,QAAQ,cAChB,GAAGA,EAAI,aACnB,CAEA,CAAO,EACD,MAAM0C,EAAkBvJ,EAASG,CAAG,MAAMqE,EAAAqC,GAAA,YAAAA,EAAK,gBAAL,YAAArC,EAAoB,mBAAoB,OAAYqC,EAAI,cAAc,gBAAkB,KAAK,QAAQ,cAAc,iBAC7J,IAAI2C,EACJ,GAAID,EAAiB,CACnB,MAAME,EAAKtJ,EAAI,MAAM,KAAK,aAAa,aAAa,EACpDqJ,EAAUC,GAAMA,EAAG,MAC3B,CACM,IAAIxH,EAAO4E,EAAI,SAAW,CAAC7G,EAAS6G,EAAI,OAAO,EAAIA,EAAI,QAAUA,EAMjE,GALI,KAAK,QAAQ,cAAc,mBAAkB5E,EAAO,CACtD,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAGA,CACJ,GACD9B,EAAM,KAAK,aAAa,YAAYA,EAAK8B,EAAM4E,EAAI,KAAO,KAAK,UAAYC,EAAS,QAASD,CAAG,EAC5F0C,EAAiB,CACnB,MAAMG,EAAKvJ,EAAI,MAAM,KAAK,aAAa,aAAa,EAC9CwJ,EAAUD,GAAMA,EAAG,OACrBF,EAAUG,IAAS9C,EAAI,KAAO,GAC1C,CACU,CAACA,EAAI,KAAOC,GAAYA,EAAS,MAAKD,EAAI,IAAM,KAAK,UAAYC,EAAS,SAC1ED,EAAI,OAAS,KAAO1G,EAAM,KAAK,aAAa,KAAKA,EAAK,IAAImE,KACxD8C,GAAA,YAAAA,EAAU,MAAO9C,EAAK,CAAC,GAAK,CAACuC,EAAI,SACnC,KAAK,OAAO,KAAK,6CAA6CvC,EAAK,CAAC,CAAC,YAAYtD,EAAI,CAAC,CAAC,EAAE,EAClF,MAEF,KAAK,UAAU,GAAGsD,EAAMtD,CAAG,EACjC6F,CAAG,GACFA,EAAI,eAAe,KAAK,aAAa,MAAO,CACtD,CACI,MAAM+C,EAAc/C,EAAI,aAAe,KAAK,QAAQ,YAC9CgD,EAAqB7J,EAAS4J,CAAW,EAAI,CAACA,CAAW,EAAIA,EACnE,OAAIzJ,GAAO,OAAQ0J,GAAA,MAAAA,EAAoB,SAAUhD,EAAI,qBAAuB,KAC1E1G,EAAMgG,GAAc,OAAO0D,EAAoB1J,EAAKa,EAAK,KAAK,SAAW,KAAK,QAAQ,wBAA0B,CAC9G,aAAc,CACZ,GAAG8F,EACH,WAAY,KAAK,qBAAqBD,CAAG,CAC1C,EACD,GAAGA,CACX,EAAUA,EAAK,IAAI,GAER1G,CACX,CACE,QAAQgH,EAAMN,EAAM,GAAI,CACtB,IAAIiD,EACApB,EACAqB,EACAC,EACAC,EACJ,OAAIjK,EAASmH,CAAI,IAAGA,EAAO,CAACA,CAAI,GAChCA,EAAK,QAAQ1F,GAAK,CAChB,GAAI,KAAK,cAAcqI,CAAK,EAAG,OAC/B,MAAMI,EAAY,KAAK,eAAezI,EAAGoF,CAAG,EACtC7F,EAAMkJ,EAAU,IACtBxB,EAAU1H,EACV,IAAI+F,EAAamD,EAAU,WACvB,KAAK,QAAQ,aAAYnD,EAAaA,EAAW,OAAO,KAAK,QAAQ,UAAU,GACnF,MAAMc,EAAsBhB,EAAI,QAAU,QAAa,CAAC7G,EAAS6G,EAAI,KAAK,EACpEoB,EAAwBJ,GAAuB,CAAChB,EAAI,SAAWA,EAAI,QAAU,EAC7EsD,EAAuBtD,EAAI,UAAY,SAAc7G,EAAS6G,EAAI,OAAO,GAAK,OAAOA,EAAI,SAAY,WAAaA,EAAI,UAAY,GAClIuD,EAAQvD,EAAI,KAAOA,EAAI,KAAO,KAAK,cAAc,mBAAmBA,EAAI,KAAO,KAAK,SAAUA,EAAI,WAAW,EACnHE,EAAW,QAAQrB,GAAM,SACnB,KAAK,cAAcoE,CAAK,IAC5BG,EAASvE,EACL,CAACc,GAAiB,GAAG4D,EAAM,CAAC,CAAC,IAAI1E,CAAE,EAAE,KAAKjB,EAAA,KAAK,QAAL,MAAAA,EAAY,qBAAsB,GAACD,EAAA,KAAK,QAAL,MAAAA,EAAY,mBAAmByF,MAC9GzD,GAAiB,GAAG4D,EAAM,CAAC,CAAC,IAAI1E,CAAE,EAAE,EAAI,GACxC,KAAK,OAAO,KAAK,QAAQgD,CAAO,oBAAoB0B,EAAM,KAAK,IAAI,CAAC,sCAAsCH,CAAM,uBAAwB,0NAA0N,GAEpWG,EAAM,QAAQhG,GAAQ,OACpB,GAAI,KAAK,cAAc0F,CAAK,EAAG,OAC/BE,EAAU5F,EACV,MAAMiG,EAAY,CAACrJ,CAAG,EACtB,IAAIyD,EAAA,KAAK,aAAL,MAAAA,EAAiB,cACnB,KAAK,WAAW,cAAc4F,EAAWrJ,EAAKoD,EAAMsB,EAAImB,CAAG,MACtD,CACL,IAAIyD,EACAzC,IAAqByC,EAAe,KAAK,eAAe,UAAUlG,EAAMyC,EAAI,MAAOA,CAAG,GAC1F,MAAM0D,EAAa,GAAG,KAAK,QAAQ,eAAe,OAC5CC,EAAgB,GAAG,KAAK,QAAQ,eAAe,UAAU,KAAK,QAAQ,eAAe,GAU3F,GATI3C,IACFwC,EAAU,KAAKrJ,EAAMsJ,CAAY,EAC7BzD,EAAI,SAAWyD,EAAa,QAAQE,CAAa,IAAM,GACzDH,EAAU,KAAKrJ,EAAMsJ,EAAa,QAAQE,EAAe,KAAK,QAAQ,eAAe,CAAC,EAEpFvC,GACFoC,EAAU,KAAKrJ,EAAMuJ,CAAU,GAG/BJ,EAAsB,CACxB,MAAMM,EAAa,GAAGzJ,CAAG,GAAG,KAAK,QAAQ,gBAAgB,GAAG6F,EAAI,OAAO,GACvEwD,EAAU,KAAKI,CAAU,EACrB5C,IACFwC,EAAU,KAAKI,EAAaH,CAAY,EACpCzD,EAAI,SAAWyD,EAAa,QAAQE,CAAa,IAAM,GACzDH,EAAU,KAAKI,EAAaH,EAAa,QAAQE,EAAe,KAAK,QAAQ,eAAe,CAAC,EAE3FvC,GACFoC,EAAU,KAAKI,EAAaF,CAAU,EAGxD,CACA,CACU,IAAIG,EACJ,KAAOA,EAAcL,EAAU,OACxB,KAAK,cAAcP,CAAK,IAC3BC,EAAeW,EACfZ,EAAQ,KAAK,YAAY1F,EAAMsB,EAAIgF,EAAa7D,CAAG,EAGjE,CAAS,EACT,CAAO,CACP,CAAK,EACM,CACL,IAAKiD,EACL,QAAApB,EACA,aAAAqB,EACA,QAAAC,EACA,OAAAC,CACD,CACL,CACE,cAAc9J,EAAK,CACjB,OAAOA,IAAQ,QAAa,EAAE,CAAC,KAAK,QAAQ,YAAcA,IAAQ,OAAS,EAAE,CAAC,KAAK,QAAQ,mBAAqBA,IAAQ,GAC5H,CACE,YAAYiE,EAAMsB,EAAI1E,EAAK4D,EAAU,GAAI,OACvC,OAAIH,EAAA,KAAK,aAAL,MAAAA,EAAiB,YAAoB,KAAK,WAAW,YAAYL,EAAMsB,EAAI1E,EAAK4D,CAAO,EACpF,KAAK,cAAc,YAAYR,EAAMsB,EAAI1E,EAAK4D,CAAO,CAChE,CACE,qBAAqBA,EAAU,GAAI,CACjC,MAAM+F,EAAc,CAAC,eAAgB,UAAW,UAAW,UAAW,MAAO,OAAQ,cAAe,KAAM,eAAgB,cAAe,gBAAiB,gBAAiB,aAAc,cAAe,eAAe,EACjNC,EAA2BhG,EAAQ,SAAW,CAAC5E,EAAS4E,EAAQ,OAAO,EAC7E,IAAI3C,EAAO2I,EAA2BhG,EAAQ,QAAUA,EAUxD,GATIgG,GAA4B,OAAOhG,EAAQ,MAAU,MACvD3C,EAAK,MAAQ2C,EAAQ,OAEnB,KAAK,QAAQ,cAAc,mBAC7B3C,EAAO,CACL,GAAG,KAAK,QAAQ,cAAc,iBAC9B,GAAGA,CACJ,GAEC,CAAC2I,EAA0B,CAC7B3I,EAAO,CACL,GAAGA,CACJ,EACD,UAAWjB,KAAO2J,EAChB,OAAO1I,EAAKjB,CAAG,CAEvB,CACI,OAAOiB,CACX,CACE,OAAO,gBAAgB2C,EAAS,CAC9B,MAAME,EAAS,eACf,UAAW+F,KAAUjG,EACnB,GAAI,OAAO,UAAU,eAAe,KAAKA,EAASiG,CAAM,GAAK/F,IAAW+F,EAAO,UAAU,EAAG/F,EAAO,MAAM,GAAmBF,EAAQiG,CAAM,IAA5B,OAC5G,MAAO,GAGX,MAAO,EACX,CACA,CAEA,MAAMC,EAAa,CACjB,YAAYlG,EAAS,CACnB,KAAK,QAAUA,EACf,KAAK,cAAgB,KAAK,QAAQ,eAAiB,GACnD,KAAK,OAASK,EAAW,OAAO,eAAe,CACnD,CACE,sBAAsBb,EAAM,CAE1B,GADAA,EAAOD,EAAeC,CAAI,EACtB,CAACA,GAAQA,EAAK,QAAQ,GAAG,EAAI,EAAG,OAAO,KAC3C,MAAMzC,EAAIyC,EAAK,MAAM,GAAG,EAGxB,OAFIzC,EAAE,SAAW,IACjBA,EAAE,IAAK,EACHA,EAAEA,EAAE,OAAS,CAAC,EAAE,YAAa,IAAK,KAAY,KAC3C,KAAK,mBAAmBA,EAAE,KAAK,GAAG,CAAC,CAC9C,CACE,wBAAwByC,EAAM,CAE5B,GADAA,EAAOD,EAAeC,CAAI,EACtB,CAACA,GAAQA,EAAK,QAAQ,GAAG,EAAI,EAAG,OAAOA,EAC3C,MAAMzC,EAAIyC,EAAK,MAAM,GAAG,EACxB,OAAO,KAAK,mBAAmBzC,EAAE,CAAC,CAAC,CACvC,CACE,mBAAmByC,EAAM,CACvB,GAAIpE,EAASoE,CAAI,GAAKA,EAAK,QAAQ,GAAG,EAAI,GAAI,CAC5C,IAAI2G,EACJ,GAAI,CACFA,EAAgB,KAAK,oBAAoB3G,CAAI,EAAE,CAAC,CACjD,MAAW,EAIZ,OAHI2G,GAAiB,KAAK,QAAQ,eAChCA,EAAgBA,EAAc,YAAa,GAEzCA,IACA,KAAK,QAAQ,aACR3G,EAAK,YAAa,EAEpBA,EACb,CACI,OAAO,KAAK,QAAQ,WAAa,KAAK,QAAQ,aAAeA,EAAK,YAAW,EAAKA,CACtF,CACE,gBAAgBA,EAAM,CACpB,OAAI,KAAK,QAAQ,OAAS,gBAAkB,KAAK,QAAQ,4BACvDA,EAAO,KAAK,wBAAwBA,CAAI,GAEnC,CAAC,KAAK,eAAiB,CAAC,KAAK,cAAc,QAAU,KAAK,cAAc,QAAQA,CAAI,EAAI,EACnG,CACE,sBAAsBgG,EAAO,CAC3B,GAAI,CAACA,EAAO,OAAO,KACnB,IAAIN,EACJ,OAAAM,EAAM,QAAQhG,GAAQ,CACpB,GAAI0F,EAAO,OACX,MAAMkB,EAAa,KAAK,mBAAmB5G,CAAI,GAC3C,CAAC,KAAK,QAAQ,eAAiB,KAAK,gBAAgB4G,CAAU,KAAGlB,EAAQkB,EACnF,CAAK,EACG,CAAClB,GAAS,KAAK,QAAQ,eACzBM,EAAM,QAAQhG,GAAQ,CACpB,GAAI0F,EAAO,OACX,MAAMmB,EAAY,KAAK,sBAAsB7G,CAAI,EACjD,GAAI,KAAK,gBAAgB6G,CAAS,EAAG,OAAOnB,EAAQmB,EACpD,MAAMC,EAAU,KAAK,wBAAwB9G,CAAI,EACjD,GAAI,KAAK,gBAAgB8G,CAAO,EAAG,OAAOpB,EAAQoB,EAClDpB,EAAQ,KAAK,QAAQ,cAAc,KAAKqB,GAAgB,CACtD,GAAIA,IAAiBD,EAAS,OAAOC,EACrC,GAAI,EAAAA,EAAa,QAAQ,GAAG,EAAI,GAAKD,EAAQ,QAAQ,GAAG,EAAI,KACxDC,EAAa,QAAQ,GAAG,EAAI,GAAKD,EAAQ,QAAQ,GAAG,EAAI,GAAKC,EAAa,UAAU,EAAGA,EAAa,QAAQ,GAAG,CAAC,IAAMD,GACtHC,EAAa,QAAQD,CAAO,IAAM,GAAKA,EAAQ,OAAS,GAAG,OAAOC,CAChF,CAAS,CACT,CAAO,EAEErB,IAAOA,EAAQ,KAAK,iBAAiB,KAAK,QAAQ,WAAW,EAAE,CAAC,GAC9DA,CACX,CACE,iBAAiBsB,EAAWhH,EAAM,CAChC,GAAI,CAACgH,EAAW,MAAO,CAAE,EAGzB,GAFI,OAAOA,GAAc,aAAYA,EAAYA,EAAUhH,CAAI,GAC3DpE,EAASoL,CAAS,IAAGA,EAAY,CAACA,CAAS,GAC3C,MAAM,QAAQA,CAAS,EAAG,OAAOA,EACrC,GAAI,CAAChH,EAAM,OAAOgH,EAAU,SAAW,CAAE,EACzC,IAAItB,EAAQsB,EAAUhH,CAAI,EAC1B,OAAK0F,IAAOA,EAAQsB,EAAU,KAAK,sBAAsBhH,CAAI,CAAC,GACzD0F,IAAOA,EAAQsB,EAAU,KAAK,mBAAmBhH,CAAI,CAAC,GACtD0F,IAAOA,EAAQsB,EAAU,KAAK,wBAAwBhH,CAAI,CAAC,GAC3D0F,IAAOA,EAAQsB,EAAU,SACvBtB,GAAS,CAAE,CACtB,CACE,mBAAmB1F,EAAMiH,EAAc,CACrC,MAAMC,EAAgB,KAAK,kBAAkBD,IAAiB,GAAQ,GAAKA,IAAiB,KAAK,QAAQ,aAAe,GAAIjH,CAAI,EAC1HgG,EAAQ,CAAE,EACVmB,EAAU/H,GAAK,CACdA,IACD,KAAK,gBAAgBA,CAAC,EACxB4G,EAAM,KAAK5G,CAAC,EAEZ,KAAK,OAAO,KAAK,uDAAuDA,CAAC,EAAE,EAE9E,EACD,OAAIxD,EAASoE,CAAI,IAAMA,EAAK,QAAQ,GAAG,EAAI,IAAMA,EAAK,QAAQ,GAAG,EAAI,KAC/D,KAAK,QAAQ,OAAS,gBAAgBmH,EAAQ,KAAK,mBAAmBnH,CAAI,CAAC,EAC3E,KAAK,QAAQ,OAAS,gBAAkB,KAAK,QAAQ,OAAS,eAAemH,EAAQ,KAAK,sBAAsBnH,CAAI,CAAC,EACrH,KAAK,QAAQ,OAAS,eAAemH,EAAQ,KAAK,wBAAwBnH,CAAI,CAAC,GAC1EpE,EAASoE,CAAI,GACtBmH,EAAQ,KAAK,mBAAmBnH,CAAI,CAAC,EAEvCkH,EAAc,QAAQE,GAAM,CACtBpB,EAAM,QAAQoB,CAAE,EAAI,GAAGD,EAAQ,KAAK,mBAAmBC,CAAE,CAAC,CACpE,CAAK,EACMpB,CACX,CACA,CAEA,MAAMqB,GAAgB,CACpB,KAAM,EACN,IAAK,EACL,IAAK,EACL,IAAK,EACL,KAAM,EACN,MAAO,CACT,EACMC,GAAY,CAChB,OAAQC,GAASA,IAAU,EAAI,MAAQ,QACvC,gBAAiB,KAAO,CACtB,iBAAkB,CAAC,MAAO,OAAO,CAClC,EACH,EACA,MAAMC,EAAe,CACnB,YAAYC,EAAejH,EAAU,GAAI,CACvC,KAAK,cAAgBiH,EACrB,KAAK,QAAUjH,EACf,KAAK,OAASK,EAAW,OAAO,gBAAgB,EAChD,KAAK,iBAAmB,CAAE,CAC9B,CACE,QAAQW,EAAK3F,EAAK,CAChB,KAAK,MAAM2F,CAAG,EAAI3F,CACtB,CACE,YAAa,CACX,KAAK,iBAAmB,CAAE,CAC9B,CACE,QAAQmE,EAAMQ,EAAU,GAAI,CAC1B,MAAMkH,EAAc3H,EAAeC,IAAS,MAAQ,KAAOA,CAAI,EACzDG,EAAOK,EAAQ,QAAU,UAAY,WACrCmH,EAAW,KAAK,UAAU,CAC9B,YAAAD,EACA,KAAAvH,CACN,CAAK,EACD,GAAIwH,KAAY,KAAK,iBACnB,OAAO,KAAK,iBAAiBA,CAAQ,EAEvC,IAAIC,EACJ,GAAI,CACFA,EAAO,IAAI,KAAK,YAAYF,EAAa,CACvC,KAAAvH,CACR,CAAO,CACF,MAAa,CACZ,GAAI,CAAC,KACH,YAAK,OAAO,MAAM,+CAA+C,EAC1DmH,GAET,GAAI,CAACtH,EAAK,MAAM,KAAK,EAAG,OAAOsH,GAC/B,MAAMO,EAAU,KAAK,cAAc,wBAAwB7H,CAAI,EAC/D4H,EAAO,KAAK,QAAQC,EAASrH,CAAO,CAC1C,CACI,YAAK,iBAAiBmH,CAAQ,EAAIC,EAC3BA,CACX,CACE,YAAY5H,EAAMQ,EAAU,GAAI,CAC9B,IAAIoH,EAAO,KAAK,QAAQ5H,EAAMQ,CAAO,EACrC,OAAKoH,IAAMA,EAAO,KAAK,QAAQ,MAAOpH,CAAO,IACtCoH,GAAA,YAAAA,EAAM,kBAAkB,iBAAiB,QAAS,CAC7D,CACE,oBAAoB5H,EAAMpD,EAAK4D,EAAU,GAAI,CAC3C,OAAO,KAAK,YAAYR,EAAMQ,CAAO,EAAE,IAAI0E,GAAU,GAAGtI,CAAG,GAAGsI,CAAM,EAAE,CAC1E,CACE,YAAYlF,EAAMQ,EAAU,GAAI,CAC9B,IAAIoH,EAAO,KAAK,QAAQ5H,EAAMQ,CAAO,EAErC,OADKoH,IAAMA,EAAO,KAAK,QAAQ,MAAOpH,CAAO,GACxCoH,EACEA,EAAK,gBAAiB,EAAC,iBAAiB,KAAK,CAACE,EAAiBC,IAAoBV,GAAcS,CAAe,EAAIT,GAAcU,CAAe,CAAC,EAAE,IAAIC,GAAkB,GAAG,KAAK,QAAQ,OAAO,GAAGxH,EAAQ,QAAU,UAAU,KAAK,QAAQ,OAAO,GAAK,EAAE,GAAGwH,CAAc,EAAE,EADnQ,CAAE,CAExB,CACE,UAAUhI,EAAMuH,EAAO/G,EAAU,GAAI,CACnC,MAAMoH,EAAO,KAAK,QAAQ5H,EAAMQ,CAAO,EACvC,OAAIoH,EACK,GAAG,KAAK,QAAQ,OAAO,GAAGpH,EAAQ,QAAU,UAAU,KAAK,QAAQ,OAAO,GAAK,EAAE,GAAGoH,EAAK,OAAOL,CAAK,CAAC,IAE/G,KAAK,OAAO,KAAK,6BAA6BvH,CAAI,EAAE,EAC7C,KAAK,UAAU,MAAOuH,EAAO/G,CAAO,EAC/C,CACA,CAEA,MAAMyH,GAAuB,CAACpK,EAAMC,EAAalB,EAAKsC,EAAe,IAAKuC,EAAsB,KAAS,CACvG,IAAI1E,EAAOa,GAAoBC,EAAMC,EAAalB,CAAG,EACrD,MAAI,CAACG,GAAQ0E,GAAuB7F,EAASgB,CAAG,IAC9CG,EAAOyC,GAAS3B,EAAMjB,EAAKsC,CAAY,EACnCnC,IAAS,SAAWA,EAAOyC,GAAS1B,EAAalB,EAAKsC,CAAY,IAEjEnC,CACT,EACMmL,GAAYC,GAAOA,EAAI,QAAQ,MAAO,MAAM,EAClD,MAAMC,EAAa,CACjB,YAAY5H,EAAU,GAAI,OACxB,KAAK,OAASK,EAAW,OAAO,cAAc,EAC9C,KAAK,QAAUL,EACf,KAAK,SAASH,EAAAG,GAAA,YAAAA,EAAS,gBAAT,YAAAH,EAAwB,UAAWtC,GAASA,GAC1D,KAAK,KAAKyC,CAAO,CACrB,CACE,KAAKA,EAAU,GAAI,CACZA,EAAQ,gBAAeA,EAAQ,cAAgB,CAClD,YAAa,EACd,GACD,KAAM,CACJ,OAAQ6H,EACR,YAAAC,EACA,oBAAAC,EACA,OAAA7H,EACA,cAAA8H,EACA,OAAAtD,EACA,cAAAuD,EACA,gBAAAC,EACA,eAAAC,EACA,eAAAC,EACA,cAAAC,EACA,qBAAAC,EACA,cAAAC,EACA,qBAAAC,EACA,wBAAAC,EACA,YAAAC,EACA,aAAAC,CACD,EAAG3I,EAAQ,cACZ,KAAK,OAAS6H,IAAa,OAAYA,EAAW7J,GAClD,KAAK,YAAc8J,IAAgB,OAAYA,EAAc,GAC7D,KAAK,oBAAsBC,IAAwB,OAAYA,EAAsB,GACrF,KAAK,OAAS7H,EAASrC,EAAYqC,CAAM,EAAI8H,GAAiB,KAC9D,KAAK,OAAStD,EAAS7G,EAAY6G,CAAM,EAAIuD,GAAiB,KAC9D,KAAK,gBAAkBC,GAAmB,IAC1C,KAAK,eAAiBC,EAAiB,GAAKC,GAAkB,IAC9D,KAAK,eAAiB,KAAK,eAAiB,GAAKD,GAAkB,GACnE,KAAK,cAAgBE,EAAgBxK,EAAYwK,CAAa,EAAIC,GAAwBzK,EAAY,KAAK,EAC3G,KAAK,cAAgB0K,EAAgB1K,EAAY0K,CAAa,EAAIC,GAAwB3K,EAAY,GAAG,EACzG,KAAK,wBAA0B4K,GAA2B,IAC1D,KAAK,YAAcC,GAAe,IAClC,KAAK,aAAeC,IAAiB,OAAYA,EAAe,GAChE,KAAK,YAAa,CACtB,CACE,OAAQ,CACF,KAAK,SAAS,KAAK,KAAK,KAAK,OAAO,CAC5C,CACE,aAAc,CACZ,MAAMC,EAAmB,CAACC,EAAgB1K,KACpC0K,GAAA,YAAAA,EAAgB,UAAW1K,GAC7B0K,EAAe,UAAY,EACpBA,GAEF,IAAI,OAAO1K,EAAS,GAAG,EAEhC,KAAK,OAASyK,EAAiB,KAAK,OAAQ,GAAG,KAAK,MAAM,QAAQ,KAAK,MAAM,EAAE,EAC/E,KAAK,eAAiBA,EAAiB,KAAK,eAAgB,GAAG,KAAK,MAAM,GAAG,KAAK,cAAc,QAAQ,KAAK,cAAc,GAAG,KAAK,MAAM,EAAE,EAC3I,KAAK,cAAgBA,EAAiB,KAAK,cAAe,GAAG,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE,CAC/G,CACE,YAAY9K,EAAKT,EAAM2D,EAAKhB,EAAS,OACnC,IAAI8I,EACAvL,EACAwL,EACJ,MAAMzL,EAAc,KAAK,SAAW,KAAK,QAAQ,eAAiB,KAAK,QAAQ,cAAc,kBAAoB,CAAE,EAC7G0L,EAAe5M,GAAO,CAC1B,GAAIA,EAAI,QAAQ,KAAK,eAAe,EAAI,EAAG,CACzC,MAAMG,EAAOkL,GAAqBpK,EAAMC,EAAalB,EAAK,KAAK,QAAQ,aAAc,KAAK,QAAQ,mBAAmB,EACrH,OAAO,KAAK,aAAe,KAAK,OAAOG,EAAM,OAAWyE,EAAK,CAC3D,GAAGhB,EACH,GAAG3C,EACH,iBAAkBjB,CACnB,GAAIG,CACb,CACM,MAAMQ,EAAIX,EAAI,MAAM,KAAK,eAAe,EAClCS,EAAIE,EAAE,MAAK,EAAG,KAAM,EACpBkM,EAAIlM,EAAE,KAAK,KAAK,eAAe,EAAE,KAAM,EAC7C,OAAO,KAAK,OAAO0K,GAAqBpK,EAAMC,EAAaT,EAAG,KAAK,QAAQ,aAAc,KAAK,QAAQ,mBAAmB,EAAGoM,EAAGjI,EAAK,CAClI,GAAGhB,EACH,GAAG3C,EACH,iBAAkBR,CAC1B,CAAO,CACF,EACD,KAAK,YAAa,EAClB,MAAMqM,GAA8BlJ,GAAA,YAAAA,EAAS,8BAA+B,KAAK,QAAQ,4BACnF2E,IAAkB9E,EAAAG,GAAA,YAAAA,EAAS,gBAAT,YAAAH,EAAwB,mBAAoB,OAAYG,EAAQ,cAAc,gBAAkB,KAAK,QAAQ,cAAc,gBAQnJ,MAPc,CAAC,CACb,MAAO,KAAK,eACZ,UAAW2H,GAAOD,GAAUC,CAAG,CACrC,EAAO,CACD,MAAO,KAAK,OACZ,UAAWA,GAAO,KAAK,YAAcD,GAAU,KAAK,OAAOC,CAAG,CAAC,EAAID,GAAUC,CAAG,CACtF,CAAK,EACK,QAAQwB,GAAQ,CAEpB,IADAJ,EAAW,EACJD,EAAQK,EAAK,MAAM,KAAKrL,CAAG,GAAG,CACnC,MAAMsL,EAAaN,EAAM,CAAC,EAAE,KAAM,EAElC,GADAvL,EAAQyL,EAAaI,CAAU,EAC3B7L,IAAU,OACZ,GAAI,OAAO2L,GAAgC,WAAY,CACrD,MAAMG,EAAOH,EAA4BpL,EAAKgL,EAAO9I,CAAO,EAC5DzC,EAAQnC,EAASiO,CAAI,EAAIA,EAAO,EAC5C,SAAqBrJ,GAAW,OAAO,UAAU,eAAe,KAAKA,EAASoJ,CAAU,EAC5E7L,EAAQ,WACCoH,EAAiB,CAC1BpH,EAAQuL,EAAM,CAAC,EACf,QACZ,MACY,KAAK,OAAO,KAAK,8BAA8BM,CAAU,sBAAsBtL,CAAG,EAAE,EACpFP,EAAQ,OAED,CAACnC,EAASmC,CAAK,GAAK,CAAC,KAAK,sBACnCA,EAAQ3B,GAAW2B,CAAK,GAE1B,MAAM+L,EAAYH,EAAK,UAAU5L,CAAK,EAStC,GARAO,EAAMA,EAAI,QAAQgL,EAAM,CAAC,EAAGQ,CAAS,EACjC3E,GACFwE,EAAK,MAAM,WAAa5L,EAAM,OAC9B4L,EAAK,MAAM,WAAaL,EAAM,CAAC,EAAE,QAEjCK,EAAK,MAAM,UAAY,EAEzBJ,IACIA,GAAY,KAAK,YACnB,KAEV,CACA,CAAK,EACMjL,CACX,CACE,KAAKA,EAAK8I,EAAI5G,EAAU,GAAI,CAC1B,IAAI8I,EACAvL,EACAgM,EACJ,MAAMC,EAAmB,CAACpN,EAAKqN,IAAqB,CAClD,MAAMC,EAAM,KAAK,wBACjB,GAAItN,EAAI,QAAQsN,CAAG,EAAI,EAAG,OAAOtN,EACjC,MAAMwC,EAAIxC,EAAI,MAAM,IAAI,OAAO,GAAGsN,CAAG,OAAO,CAAC,EAC7C,IAAIC,EAAgB,IAAI/K,EAAE,CAAC,CAAC,GAC5BxC,EAAMwC,EAAE,CAAC,EACT+K,EAAgB,KAAK,YAAYA,EAAeJ,CAAa,EAC7D,MAAMK,EAAsBD,EAAc,MAAM,IAAI,EAC9CE,EAAsBF,EAAc,MAAM,IAAI,KAC/CC,GAAA,YAAAA,EAAqB,SAAU,GAAK,IAAM,GAAK,CAACC,GAAuBA,EAAoB,OAAS,IAAM,KAC7GF,EAAgBA,EAAc,QAAQ,KAAM,GAAG,GAEjD,GAAI,CACFJ,EAAgB,KAAK,MAAMI,CAAa,EACpCF,IAAkBF,EAAgB,CACpC,GAAGE,EACH,GAAGF,CACJ,EACF,OAAQzM,EAAG,CACV,YAAK,OAAO,KAAK,oDAAoDV,CAAG,GAAIU,CAAC,EACtE,GAAGV,CAAG,GAAGsN,CAAG,GAAGC,CAAa,EAC3C,CACM,OAAIJ,EAAc,cAAgBA,EAAc,aAAa,QAAQ,KAAK,MAAM,EAAI,IAAI,OAAOA,EAAc,aACtGnN,CACR,EACD,KAAO0M,EAAQ,KAAK,cAAc,KAAKhL,CAAG,GAAG,CAC3C,IAAIgM,EAAa,CAAE,EACnBP,EAAgB,CACd,GAAGvJ,CACJ,EACDuJ,EAAgBA,EAAc,SAAW,CAACnO,EAASmO,EAAc,OAAO,EAAIA,EAAc,QAAUA,EACpGA,EAAc,mBAAqB,GACnC,OAAOA,EAAc,aACrB,IAAIQ,EAAW,GACf,GAAIjB,EAAM,CAAC,EAAE,QAAQ,KAAK,eAAe,IAAM,IAAM,CAAC,OAAO,KAAKA,EAAM,CAAC,CAAC,EAAG,CAC3E,MAAMjK,EAAIiK,EAAM,CAAC,EAAE,MAAM,KAAK,eAAe,EAAE,IAAIkB,GAAQA,EAAK,KAAI,CAAE,EACtElB,EAAM,CAAC,EAAIjK,EAAE,MAAO,EACpBiL,EAAajL,EACbkL,EAAW,EACnB,CAEM,GADAxM,EAAQqJ,EAAG4C,EAAiB,KAAK,KAAMV,EAAM,CAAC,EAAE,KAAI,EAAIS,CAAa,EAAGA,CAAa,EACjFhM,GAASuL,EAAM,CAAC,IAAMhL,GAAO,CAAC1C,EAASmC,CAAK,EAAG,OAAOA,EACrDnC,EAASmC,CAAK,IAAGA,EAAQ3B,GAAW2B,CAAK,GACzCA,IACH,KAAK,OAAO,KAAK,qBAAqBuL,EAAM,CAAC,CAAC,gBAAgBhL,CAAG,EAAE,EACnEP,EAAQ,IAENwM,IACFxM,EAAQuM,EAAW,OAAO,CAACxI,EAAG,IAAM,KAAK,OAAOA,EAAG,EAAGtB,EAAQ,IAAK,CACjE,GAAGA,EACH,iBAAkB8I,EAAM,CAAC,EAAE,KAAI,CACzC,CAAS,EAAGvL,EAAM,MAAM,GAElBO,EAAMA,EAAI,QAAQgL,EAAM,CAAC,EAAGvL,CAAK,EACjC,KAAK,OAAO,UAAY,CAC9B,CACI,OAAOO,CACX,CACA,CAEA,MAAMmM,GAAiBC,GAAa,CAClC,IAAIC,EAAaD,EAAU,YAAW,EAAG,KAAM,EAC/C,MAAME,EAAgB,CAAE,EACxB,GAAIF,EAAU,QAAQ,GAAG,EAAI,GAAI,CAC/B,MAAMnN,EAAImN,EAAU,MAAM,GAAG,EAC7BC,EAAapN,EAAE,CAAC,EAAE,YAAW,EAAG,KAAM,EACtC,MAAMsN,EAAStN,EAAE,CAAC,EAAE,UAAU,EAAGA,EAAE,CAAC,EAAE,OAAS,CAAC,EAC5CoN,IAAe,YAAcE,EAAO,QAAQ,GAAG,EAAI,EAChDD,EAAc,WAAUA,EAAc,SAAWC,EAAO,KAAM,GAC1DF,IAAe,gBAAkBE,EAAO,QAAQ,GAAG,EAAI,EAC3DD,EAAc,QAAOA,EAAc,MAAQC,EAAO,KAAM,GAEhDA,EAAO,MAAM,GAAG,EACxB,QAAQpI,GAAO,CAClB,GAAIA,EAAK,CACP,KAAM,CAAC7F,EAAK,GAAGkO,CAAI,EAAIrI,EAAI,MAAM,GAAG,EAC9B0F,EAAM2C,EAAK,KAAK,GAAG,EAAE,OAAO,QAAQ,WAAY,EAAE,EAClDC,EAAanO,EAAI,KAAM,EACxBgO,EAAcG,CAAU,IAAGH,EAAcG,CAAU,EAAI5C,GACxDA,IAAQ,UAASyC,EAAcG,CAAU,EAAI,IAC7C5C,IAAQ,SAAQyC,EAAcG,CAAU,EAAI,IAC3C,MAAM5C,CAAG,IAAGyC,EAAcG,CAAU,EAAI,SAAS5C,EAAK,EAAE,EACvE,CACA,CAAO,CAEP,CACE,MAAO,CACL,WAAAwC,EACA,cAAAC,CACD,CACH,EACMI,GAAwBC,GAAM,CAClC,MAAMC,EAAQ,CAAE,EAChB,MAAO,CAACpJ,EAAG+C,EAAGrC,IAAM,CAClB,IAAI2I,EAAc3I,EACdA,GAAKA,EAAE,kBAAoBA,EAAE,cAAgBA,EAAE,aAAaA,EAAE,gBAAgB,GAAKA,EAAEA,EAAE,gBAAgB,IACzG2I,EAAc,CACZ,GAAGA,EACH,CAAC3I,EAAE,gBAAgB,EAAG,MACvB,GAEH,MAAM5F,EAAMiI,EAAI,KAAK,UAAUsG,CAAW,EAC1C,IAAIC,EAAMF,EAAMtO,CAAG,EACnB,OAAKwO,IACHA,EAAMH,EAAGlL,EAAe8E,CAAC,EAAGrC,CAAC,EAC7B0I,EAAMtO,CAAG,EAAIwO,GAERA,EAAItJ,CAAC,CACb,CACH,EACMuJ,GAA2BJ,GAAM,CAACnJ,EAAG+C,EAAGrC,IAAMyI,EAAGlL,EAAe8E,CAAC,EAAGrC,CAAC,EAAEV,CAAC,EAC9E,MAAMwJ,EAAU,CACd,YAAY9K,EAAU,GAAI,CACxB,KAAK,OAASK,EAAW,OAAO,WAAW,EAC3C,KAAK,QAAUL,EACf,KAAK,KAAKA,CAAO,CACrB,CACE,KAAK+B,EAAU/B,EAAU,CACvB,cAAe,EACnB,EAAK,CACD,KAAK,gBAAkBA,EAAQ,cAAc,iBAAmB,IAChE,MAAM+K,EAAK/K,EAAQ,oBAAsBwK,GAAwBK,GACjE,KAAK,QAAU,CACb,OAAQE,EAAG,CAAC/J,EAAKiB,IAAQ,CACvB,MAAM+I,EAAY,IAAI,KAAK,aAAahK,EAAK,CAC3C,GAAGiB,CACb,CAAS,EACD,OAAO0F,GAAOqD,EAAU,OAAOrD,CAAG,CAC1C,CAAO,EACD,SAAUoD,EAAG,CAAC/J,EAAKiB,IAAQ,CACzB,MAAM+I,EAAY,IAAI,KAAK,aAAahK,EAAK,CAC3C,GAAGiB,EACH,MAAO,UACjB,CAAS,EACD,OAAO0F,GAAOqD,EAAU,OAAOrD,CAAG,CAC1C,CAAO,EACD,SAAUoD,EAAG,CAAC/J,EAAKiB,IAAQ,CACzB,MAAM+I,EAAY,IAAI,KAAK,eAAehK,EAAK,CAC7C,GAAGiB,CACb,CAAS,EACD,OAAO0F,GAAOqD,EAAU,OAAOrD,CAAG,CAC1C,CAAO,EACD,aAAcoD,EAAG,CAAC/J,EAAKiB,IAAQ,CAC7B,MAAM+I,EAAY,IAAI,KAAK,mBAAmBhK,EAAK,CACjD,GAAGiB,CACb,CAAS,EACD,OAAO0F,GAAOqD,EAAU,OAAOrD,EAAK1F,EAAI,OAAS,KAAK,CAC9D,CAAO,EACD,KAAM8I,EAAG,CAAC/J,EAAKiB,IAAQ,CACrB,MAAM+I,EAAY,IAAI,KAAK,WAAWhK,EAAK,CACzC,GAAGiB,CACb,CAAS,EACD,OAAO0F,GAAOqD,EAAU,OAAOrD,CAAG,CACnC,EACF,CACL,CACE,IAAIsD,EAAMrE,EAAI,CACZ,KAAK,QAAQqE,EAAK,YAAW,EAAG,KAAM,GAAIrE,CAC9C,CACE,UAAUqE,EAAMrE,EAAI,CAClB,KAAK,QAAQqE,EAAK,YAAW,EAAG,MAAM,EAAIT,GAAsB5D,CAAE,CACtE,CACE,OAAOrJ,EAAO2N,EAAQlK,EAAKhB,EAAU,GAAI,CACvC,MAAMmL,EAAUD,EAAO,MAAM,KAAK,eAAe,EACjD,GAAIC,EAAQ,OAAS,GAAKA,EAAQ,CAAC,EAAE,QAAQ,GAAG,EAAI,GAAKA,EAAQ,CAAC,EAAE,QAAQ,GAAG,EAAI,GAAKA,EAAQ,KAAKlC,GAAKA,EAAE,QAAQ,GAAG,EAAI,EAAE,EAAG,CAC9H,MAAMmC,EAAYD,EAAQ,UAAUlC,GAAKA,EAAE,QAAQ,GAAG,EAAI,EAAE,EAC5DkC,EAAQ,CAAC,EAAI,CAACA,EAAQ,CAAC,EAAG,GAAGA,EAAQ,OAAO,EAAGC,CAAS,CAAC,EAAE,KAAK,KAAK,eAAe,CAC1F,CAyBI,OAxBeD,EAAQ,OAAO,CAACE,EAAKpC,IAAM,OACxC,KAAM,CACJ,WAAAkB,EACA,cAAAC,CACR,EAAUH,GAAehB,CAAC,EACpB,GAAI,KAAK,QAAQkB,CAAU,EAAG,CAC5B,IAAImB,EAAYD,EAChB,GAAI,CACF,MAAME,IAAa1L,EAAAG,GAAA,YAAAA,EAAS,eAAT,YAAAH,EAAwBG,EAAQ,oBAAqB,CAAE,EACpEqE,EAAIkH,EAAW,QAAUA,EAAW,KAAOvL,EAAQ,QAAUA,EAAQ,KAAOgB,EAClFsK,EAAY,KAAK,QAAQnB,CAAU,EAAEkB,EAAKhH,EAAG,CAC3C,GAAG+F,EACH,GAAGpK,EACH,GAAGuL,CACf,CAAW,CACF,OAAQC,EAAO,CACd,KAAK,OAAO,KAAKA,CAAK,CAChC,CACQ,OAAOF,CACf,MACQ,KAAK,OAAO,KAAK,oCAAoCnB,CAAU,EAAE,EAEnE,OAAOkB,CACR,EAAE9N,CAAK,CAEZ,CACA,CAEA,MAAMkO,GAAgB,CAACC,EAAGT,IAAS,CAC7BS,EAAE,QAAQT,CAAI,IAAM,SACtB,OAAOS,EAAE,QAAQT,CAAI,EACrBS,EAAE,eAEN,EACA,MAAMC,WAAkBrL,EAAa,CACnC,YAAYsL,EAASC,EAAO9J,EAAU/B,EAAU,GAAI,SAClD,MAAO,EACP,KAAK,QAAU4L,EACf,KAAK,MAAQC,EACb,KAAK,SAAW9J,EAChB,KAAK,cAAgBA,EAAS,cAC9B,KAAK,QAAU/B,EACf,KAAK,OAASK,EAAW,OAAO,kBAAkB,EAClD,KAAK,aAAe,CAAE,EACtB,KAAK,iBAAmBL,EAAQ,kBAAoB,GACpD,KAAK,aAAe,EACpB,KAAK,WAAaA,EAAQ,YAAc,EAAIA,EAAQ,WAAa,EACjE,KAAK,aAAeA,EAAQ,cAAgB,EAAIA,EAAQ,aAAe,IACvE,KAAK,MAAQ,CAAE,EACf,KAAK,MAAQ,CAAE,GACfJ,GAAAC,EAAA,KAAK,UAAL,YAAAA,EAAc,OAAd,MAAAD,EAAA,KAAAC,EAAqBkC,EAAU/B,EAAQ,QAASA,EACpD,CACE,UAAU8L,EAAW3J,EAAYnC,EAAS+L,EAAU,CAClD,MAAMC,EAAS,CAAE,EACXC,EAAU,CAAE,EACZC,EAAkB,CAAE,EACpBC,EAAmB,CAAE,EAC3B,OAAAL,EAAU,QAAQ9K,GAAO,CACvB,IAAIoL,EAAmB,GACvBjK,EAAW,QAAQrB,GAAM,CACvB,MAAMmK,EAAO,GAAGjK,CAAG,IAAIF,CAAE,GACrB,CAACd,EAAQ,QAAU,KAAK,MAAM,kBAAkBgB,EAAKF,CAAE,EACzD,KAAK,MAAMmK,CAAI,EAAI,EACV,KAAK,MAAMA,CAAI,EAAI,IAAc,KAAK,MAAMA,CAAI,IAAM,EAC3DgB,EAAQhB,CAAI,IAAM,SAAWgB,EAAQhB,CAAI,EAAI,KAEjD,KAAK,MAAMA,CAAI,EAAI,EACnBmB,EAAmB,GACfH,EAAQhB,CAAI,IAAM,SAAWgB,EAAQhB,CAAI,EAAI,IAC7Ce,EAAOf,CAAI,IAAM,SAAWe,EAAOf,CAAI,EAAI,IAC3CkB,EAAiBrL,CAAE,IAAM,SAAWqL,EAAiBrL,CAAE,EAAI,KAEzE,CAAO,EACIsL,IAAkBF,EAAgBlL,CAAG,EAAI,GACpD,CAAK,GACG,OAAO,KAAKgL,CAAM,EAAE,QAAU,OAAO,KAAKC,CAAO,EAAE,SACrD,KAAK,MAAM,KAAK,CACd,QAAAA,EACA,aAAc,OAAO,KAAKA,CAAO,EAAE,OACnC,OAAQ,CAAE,EACV,OAAQ,CAAE,EACV,SAAAF,CACR,CAAO,EAEI,CACL,OAAQ,OAAO,KAAKC,CAAM,EAC1B,QAAS,OAAO,KAAKC,CAAO,EAC5B,gBAAiB,OAAO,KAAKC,CAAe,EAC5C,iBAAkB,OAAO,KAAKC,CAAgB,CAC/C,CACL,CACE,OAAOlB,EAAMoB,EAAKhP,EAAM,CACtB,MAAMrB,EAAIiP,EAAK,MAAM,GAAG,EAClBjK,EAAMhF,EAAE,CAAC,EACT8E,EAAK9E,EAAE,CAAC,EACVqQ,GAAK,KAAK,KAAK,gBAAiBrL,EAAKF,EAAIuL,CAAG,EAC5C,CAACA,GAAOhP,GACV,KAAK,MAAM,kBAAkB2D,EAAKF,EAAIzD,EAAM,OAAW,OAAW,CAChE,SAAU,EAClB,CAAO,EAEH,KAAK,MAAM4N,CAAI,EAAIoB,EAAM,GAAK,EAC1BA,GAAOhP,IAAM,KAAK,MAAM4N,CAAI,EAAI,GACpC,MAAMqB,EAAS,CAAE,EACjB,KAAK,MAAM,QAAQZ,GAAK,CACtBzO,GAASyO,EAAE,OAAQ,CAAC1K,CAAG,EAAGF,CAAE,EAC5B2K,GAAcC,EAAGT,CAAI,EACjBoB,GAAKX,EAAE,OAAO,KAAKW,CAAG,EACtBX,EAAE,eAAiB,GAAK,CAACA,EAAE,OAC7B,OAAO,KAAKA,EAAE,MAAM,EAAE,QAAQrH,GAAK,CAC5BiI,EAAOjI,CAAC,IAAGiI,EAAOjI,CAAC,EAAI,CAAE,GAC9B,MAAMkI,EAAab,EAAE,OAAOrH,CAAC,EACzBkI,EAAW,QACbA,EAAW,QAAQC,GAAK,CAClBF,EAAOjI,CAAC,EAAEmI,CAAC,IAAM,SAAWF,EAAOjI,CAAC,EAAEmI,CAAC,EAAI,GAC7D,CAAa,CAEb,CAAS,EACDd,EAAE,KAAO,GACLA,EAAE,OAAO,OACXA,EAAE,SAASA,EAAE,MAAM,EAEnBA,EAAE,SAAU,EAGtB,CAAK,EACD,KAAK,KAAK,SAAUY,CAAM,EAC1B,KAAK,MAAQ,KAAK,MAAM,OAAOZ,GAAK,CAACA,EAAE,IAAI,CAC/C,CACE,KAAK1K,EAAKF,EAAI2L,EAAQC,EAAQ,EAAGC,EAAO,KAAK,aAAcZ,EAAU,CACnE,GAAI,CAAC/K,EAAI,OAAQ,OAAO+K,EAAS,KAAM,EAAE,EACzC,GAAI,KAAK,cAAgB,KAAK,iBAAkB,CAC9C,KAAK,aAAa,KAAK,CACrB,IAAA/K,EACA,GAAAF,EACA,OAAA2L,EACA,MAAAC,EACA,KAAAC,EACA,SAAAZ,CACR,CAAO,EACD,MACN,CACI,KAAK,eACL,MAAMa,EAAW,CAACP,EAAKhP,IAAS,CAE9B,GADA,KAAK,eACD,KAAK,aAAa,OAAS,EAAG,CAChC,MAAM+B,EAAO,KAAK,aAAa,MAAO,EACtC,KAAK,KAAKA,EAAK,IAAKA,EAAK,GAAIA,EAAK,OAAQA,EAAK,MAAOA,EAAK,KAAMA,EAAK,QAAQ,CACtF,CACM,GAAIiN,GAAOhP,GAAQqP,EAAQ,KAAK,WAAY,CAC1C,WAAW,IAAM,CACf,KAAK,KAAK,KAAK,KAAM1L,EAAKF,EAAI2L,EAAQC,EAAQ,EAAGC,EAAO,EAAGZ,CAAQ,CACpE,EAAEY,CAAI,EACP,MACR,CACMZ,EAASM,EAAKhP,CAAI,CACnB,EACKuJ,EAAK,KAAK,QAAQ6F,CAAM,EAAE,KAAK,KAAK,OAAO,EACjD,GAAI7F,EAAG,SAAW,EAAG,CACnB,GAAI,CACF,MAAM/H,EAAI+H,EAAG5F,EAAKF,CAAE,EAChBjC,GAAK,OAAOA,EAAE,MAAS,WACzBA,EAAE,KAAKxB,GAAQuP,EAAS,KAAMvP,CAAI,CAAC,EAAE,MAAMuP,CAAQ,EAEnDA,EAAS,KAAM/N,CAAC,CAEnB,OAAQwN,EAAK,CACZO,EAASP,CAAG,CACpB,CACM,MACN,CACI,OAAOzF,EAAG5F,EAAKF,EAAI8L,CAAQ,CAC/B,CACE,eAAed,EAAW3J,EAAYnC,EAAU,GAAI+L,EAAU,CAC5D,GAAI,CAAC,KAAK,QACR,YAAK,OAAO,KAAK,gEAAgE,EAC1EA,GAAYA,EAAU,EAE3B3Q,EAAS0Q,CAAS,IAAGA,EAAY,KAAK,cAAc,mBAAmBA,CAAS,GAChF1Q,EAAS+G,CAAU,IAAGA,EAAa,CAACA,CAAU,GAClD,MAAM6J,EAAS,KAAK,UAAUF,EAAW3J,EAAYnC,EAAS+L,CAAQ,EACtE,GAAI,CAACC,EAAO,OAAO,OACjB,OAAKA,EAAO,QAAQ,QAAQD,EAAU,EAC/B,KAETC,EAAO,OAAO,QAAQf,GAAQ,CAC5B,KAAK,QAAQA,CAAI,CACvB,CAAK,CACL,CACE,KAAKa,EAAW3J,EAAY4J,EAAU,CACpC,KAAK,eAAeD,EAAW3J,EAAY,GAAI4J,CAAQ,CAC3D,CACE,OAAOD,EAAW3J,EAAY4J,EAAU,CACtC,KAAK,eAAeD,EAAW3J,EAAY,CACzC,OAAQ,EACT,EAAE4J,CAAQ,CACf,CACE,QAAQd,EAAM/K,EAAS,GAAI,CACzB,MAAMlE,EAAIiP,EAAK,MAAM,GAAG,EAClBjK,EAAMhF,EAAE,CAAC,EACT8E,EAAK9E,EAAE,CAAC,EACd,KAAK,KAAKgF,EAAKF,EAAI,OAAQ,OAAW,OAAW,CAACuL,EAAKhP,IAAS,CAC1DgP,GAAK,KAAK,OAAO,KAAK,GAAGnM,CAAM,qBAAqBY,CAAE,iBAAiBE,CAAG,UAAWqL,CAAG,EACxF,CAACA,GAAOhP,GAAM,KAAK,OAAO,IAAI,GAAG6C,CAAM,oBAAoBY,CAAE,iBAAiBE,CAAG,GAAI3D,CAAI,EAC7F,KAAK,OAAO4N,EAAMoB,EAAKhP,CAAI,CACjC,CAAK,CACL,CACE,YAAYyO,EAAWpJ,EAAWtG,EAAKyQ,EAAeC,EAAU9M,EAAU,CAAE,EAAE+M,EAAM,IAAM,GAAI,eAC5F,IAAInN,GAAAC,EAAA,KAAK,WAAL,YAAAA,EAAe,QAAf,MAAAD,EAAsB,oBAAsB,GAACoN,GAAAC,EAAA,KAAK,WAAL,YAAAA,EAAe,QAAf,MAAAD,EAAsB,mBAAmBtK,IAAY,CACpG,KAAK,OAAO,KAAK,qBAAqBtG,CAAG,uBAAuBsG,CAAS,uBAAwB,0NAA0N,EAC3T,MACN,CACI,GAAI,EAAqBtG,GAAQ,MAAQA,IAAQ,IACjD,KAAI8Q,EAAA,KAAK,UAAL,MAAAA,EAAc,OAAQ,CACxB,MAAMC,EAAO,CACX,GAAGnN,EACH,SAAA8M,CACD,EACKlG,EAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,OAAO,EAChD,GAAIA,EAAG,OAAS,EACd,GAAI,CACF,IAAI/H,EACA+H,EAAG,SAAW,EAChB/H,EAAI+H,EAAGkF,EAAWpJ,EAAWtG,EAAKyQ,EAAeM,CAAI,EAErDtO,EAAI+H,EAAGkF,EAAWpJ,EAAWtG,EAAKyQ,CAAa,EAE7ChO,GAAK,OAAOA,EAAE,MAAS,WACzBA,EAAE,KAAKxB,GAAQ0P,EAAI,KAAM1P,CAAI,CAAC,EAAE,MAAM0P,CAAG,EAEzCA,EAAI,KAAMlO,CAAC,CAEd,OAAQwN,EAAK,CACZU,EAAIV,CAAG,CACjB,MAEQzF,EAAGkF,EAAWpJ,EAAWtG,EAAKyQ,EAAeE,EAAKI,CAAI,CAE9D,CACQ,CAACrB,GAAa,CAACA,EAAU,CAAC,GAC9B,KAAK,MAAM,YAAYA,EAAU,CAAC,EAAGpJ,EAAWtG,EAAKyQ,CAAa,EACtE,CACA,CAEA,MAAMO,GAAM,KAAO,CACjB,MAAO,GACP,UAAW,GACX,GAAI,CAAC,aAAa,EAClB,UAAW,CAAC,aAAa,EACzB,YAAa,CAAC,KAAK,EACnB,WAAY,GACZ,cAAe,GACf,yBAA0B,GAC1B,KAAM,MACN,QAAS,GACT,qBAAsB,GACtB,aAAc,IACd,YAAa,IACb,gBAAiB,IACjB,iBAAkB,IAClB,wBAAyB,GACzB,YAAa,GACb,cAAe,GACf,cAAe,WACf,mBAAoB,GACpB,kBAAmB,GACnB,4BAA6B,GAC7B,YAAa,GACb,wBAAyB,GACzB,WAAY,GACZ,kBAAmB,GACnB,cAAe,GACf,WAAY,GACZ,sBAAuB,GACvB,uBAAwB,GACxB,4BAA6B,GAC7B,wBAAyB,GACzB,iCAAkC1N,GAAQ,CACxC,IAAI2N,EAAM,CAAE,EAIZ,GAHI,OAAO3N,EAAK,CAAC,GAAM,WAAU2N,EAAM3N,EAAK,CAAC,GACzCtE,EAASsE,EAAK,CAAC,CAAC,IAAG2N,EAAI,aAAe3N,EAAK,CAAC,GAC5CtE,EAASsE,EAAK,CAAC,CAAC,IAAG2N,EAAI,aAAe3N,EAAK,CAAC,GAC5C,OAAOA,EAAK,CAAC,GAAM,UAAY,OAAOA,EAAK,CAAC,GAAM,SAAU,CAC9D,MAAMM,EAAUN,EAAK,CAAC,GAAKA,EAAK,CAAC,EACjC,OAAO,KAAKM,CAAO,EAAE,QAAQ5D,GAAO,CAClCiR,EAAIjR,CAAG,EAAI4D,EAAQ5D,CAAG,CAC9B,CAAO,CACP,CACI,OAAOiR,CACR,EACD,cAAe,CACb,YAAa,GACb,OAAQ9P,GAASA,EACjB,OAAQ,KACR,OAAQ,KACR,gBAAiB,IACjB,eAAgB,IAChB,cAAe,MACf,cAAe,IACf,wBAAyB,IACzB,YAAa,IACb,gBAAiB,EAClB,EACD,oBAAqB,EACvB,GACM+P,GAAmBtN,GAAW,SAClC,OAAI5E,EAAS4E,EAAQ,EAAE,IAAGA,EAAQ,GAAK,CAACA,EAAQ,EAAE,GAC9C5E,EAAS4E,EAAQ,WAAW,IAAGA,EAAQ,YAAc,CAACA,EAAQ,WAAW,GACzE5E,EAAS4E,EAAQ,UAAU,IAAGA,EAAQ,WAAa,CAACA,EAAQ,UAAU,KACtEJ,GAAAC,EAAAG,EAAQ,gBAAR,YAAAH,EAAuB,UAAvB,YAAAD,EAAA,KAAAC,EAAiC,WAAY,IAC/CG,EAAQ,cAAgBA,EAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC,GAE7D,OAAOA,EAAQ,eAAkB,YAAWA,EAAQ,UAAYA,EAAQ,eACrEA,CACT,EAEMuN,EAAO,IAAM,CAAE,EACfC,GAAsBC,GAAQ,CACrB,OAAO,oBAAoB,OAAO,eAAeA,CAAI,CAAC,EAC9D,QAAQpC,GAAO,CACd,OAAOoC,EAAKpC,CAAG,GAAM,aACvBoC,EAAKpC,CAAG,EAAIoC,EAAKpC,CAAG,EAAE,KAAKoC,CAAI,EAErC,CAAG,CACH,EACA,MAAMC,UAAapN,EAAa,CAC9B,YAAYN,EAAU,CAAE,EAAE+L,EAAU,CASlC,GARA,MAAO,EACP,KAAK,QAAUuB,GAAiBtN,CAAO,EACvC,KAAK,SAAW,CAAE,EAClB,KAAK,OAASK,EACd,KAAK,QAAU,CACb,SAAU,EACX,EACDmN,GAAoB,IAAI,EACpBzB,GAAY,CAAC,KAAK,eAAiB,CAAC/L,EAAQ,QAAS,CACvD,GAAI,CAAC,KAAK,QAAQ,UAChB,YAAK,KAAKA,EAAS+L,CAAQ,EACpB,KAET,WAAW,IAAM,CACf,KAAK,KAAK/L,EAAS+L,CAAQ,CAC5B,EAAE,CAAC,CACV,CACA,CACE,KAAK/L,EAAU,CAAE,EAAE+L,EAAU,CAC3B,KAAK,eAAiB,GAClB,OAAO/L,GAAY,aACrB+L,EAAW/L,EACXA,EAAU,CAAE,GAEVA,EAAQ,WAAa,MAAQA,EAAQ,KACnC5E,EAAS4E,EAAQ,EAAE,EACrBA,EAAQ,UAAYA,EAAQ,GACnBA,EAAQ,GAAG,QAAQ,aAAa,EAAI,IAC7CA,EAAQ,UAAYA,EAAQ,GAAG,CAAC,IAGpC,MAAM2N,EAAUP,GAAK,EACrB,KAAK,QAAU,CACb,GAAGO,EACH,GAAG,KAAK,QACR,GAAGL,GAAiBtN,CAAO,CAC5B,EACD,KAAK,QAAQ,cAAgB,CAC3B,GAAG2N,EAAQ,cACX,GAAG,KAAK,QAAQ,aACjB,EACG3N,EAAQ,eAAiB,SAC3B,KAAK,QAAQ,wBAA0BA,EAAQ,cAE7CA,EAAQ,cAAgB,SAC1B,KAAK,QAAQ,uBAAyBA,EAAQ,aAEhD,MAAM4N,EAAsBC,GACrBA,EACD,OAAOA,GAAkB,WAAmB,IAAIA,EAC7CA,EAFoB,KAI7B,GAAI,CAAC,KAAK,QAAQ,QAAS,CACrB,KAAK,QAAQ,OACfxN,EAAW,KAAKuN,EAAoB,KAAK,QAAQ,MAAM,EAAG,KAAK,OAAO,EAEtEvN,EAAW,KAAK,KAAM,KAAK,OAAO,EAEpC,IAAI2K,EACA,KAAK,QAAQ,UACfA,EAAY,KAAK,QAAQ,UAEzBA,EAAYF,GAEd,MAAMgD,EAAK,IAAI5H,GAAa,KAAK,OAAO,EACxC,KAAK,MAAQ,IAAIrF,GAAc,KAAK,QAAQ,UAAW,KAAK,OAAO,EACnE,MAAM7E,EAAI,KAAK,SACfA,EAAE,OAASqE,EACXrE,EAAE,cAAgB,KAAK,MACvBA,EAAE,cAAgB8R,EAClB9R,EAAE,eAAiB,IAAIgL,GAAe8G,EAAI,CACxC,QAAS,KAAK,QAAQ,gBACtB,qBAAsB,KAAK,QAAQ,oBAC3C,CAAO,EACG9C,IAAc,CAAC,KAAK,QAAQ,cAAc,QAAU,KAAK,QAAQ,cAAc,SAAW2C,EAAQ,cAAc,UAClH3R,EAAE,UAAY4R,EAAoB5C,CAAS,EAC3ChP,EAAE,UAAU,KAAKA,EAAG,KAAK,OAAO,EAChC,KAAK,QAAQ,cAAc,OAASA,EAAE,UAAU,OAAO,KAAKA,EAAE,SAAS,GAEzEA,EAAE,aAAe,IAAI4L,GAAa,KAAK,OAAO,EAC9C5L,EAAE,MAAQ,CACR,mBAAoB,KAAK,mBAAmB,KAAK,IAAI,CACtD,EACDA,EAAE,iBAAmB,IAAI2P,GAAUiC,EAAoB,KAAK,QAAQ,OAAO,EAAG5R,EAAE,cAAeA,EAAG,KAAK,OAAO,EAC9GA,EAAE,iBAAiB,GAAG,IAAK,CAACyE,KAAUf,IAAS,CAC7C,KAAK,KAAKe,EAAO,GAAGf,CAAI,CAChC,CAAO,EACG,KAAK,QAAQ,mBACf1D,EAAE,iBAAmB4R,EAAoB,KAAK,QAAQ,gBAAgB,EAClE5R,EAAE,iBAAiB,MAAMA,EAAE,iBAAiB,KAAKA,EAAG,KAAK,QAAQ,UAAW,KAAK,OAAO,GAE1F,KAAK,QAAQ,aACfA,EAAE,WAAa4R,EAAoB,KAAK,QAAQ,UAAU,EACtD5R,EAAE,WAAW,MAAMA,EAAE,WAAW,KAAK,IAAI,GAE/C,KAAK,WAAa,IAAI8F,EAAW,KAAK,SAAU,KAAK,OAAO,EAC5D,KAAK,WAAW,GAAG,IAAK,CAACrB,KAAUf,IAAS,CAC1C,KAAK,KAAKe,EAAO,GAAGf,CAAI,CAChC,CAAO,EACD,KAAK,QAAQ,SAAS,QAAQzD,GAAK,CAC7BA,EAAE,MAAMA,EAAE,KAAK,IAAI,CAC/B,CAAO,CACP,CAGI,GAFA,KAAK,OAAS,KAAK,QAAQ,cAAc,OACpC8P,IAAUA,EAAWwB,GACtB,KAAK,QAAQ,aAAe,CAAC,KAAK,SAAS,kBAAoB,CAAC,KAAK,QAAQ,IAAK,CACpF,MAAM/H,EAAQ,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW,EAC/EA,EAAM,OAAS,GAAKA,EAAM,CAAC,IAAM,QAAO,KAAK,QAAQ,IAAMA,EAAM,CAAC,EAC5E,CACQ,CAAC,KAAK,SAAS,kBAAoB,CAAC,KAAK,QAAQ,KACnD,KAAK,OAAO,KAAK,yDAAyD,EAE3D,CAAC,cAAe,oBAAqB,oBAAqB,mBAAmB,EACrF,QAAQiH,GAAU,CACzB,KAAKA,CAAM,EAAI,IAAI/M,IAAS,KAAK,MAAM+M,CAAM,EAAE,GAAG/M,CAAI,CAC5D,CAAK,EACuB,CAAC,cAAe,eAAgB,oBAAqB,sBAAsB,EACnF,QAAQ+M,GAAU,CAChC,KAAKA,CAAM,EAAI,IAAI/M,KACjB,KAAK,MAAM+M,CAAM,EAAE,GAAG/M,CAAI,EACnB,KAEf,CAAK,EACD,MAAMqO,EAAWzS,EAAO,EAClB0S,EAAO,IAAM,CACjB,MAAMC,EAAS,CAAC5B,EAAK6B,IAAM,CACzB,KAAK,eAAiB,GAClB,KAAK,eAAiB,CAAC,KAAK,sBAAsB,KAAK,OAAO,KAAK,uEAAuE,EAC9I,KAAK,cAAgB,GAChB,KAAK,QAAQ,SAAS,KAAK,OAAO,IAAI,cAAe,KAAK,OAAO,EACtE,KAAK,KAAK,cAAe,KAAK,OAAO,EACrCH,EAAS,QAAQG,CAAC,EAClBnC,EAASM,EAAK6B,CAAC,CAChB,EACD,GAAI,KAAK,WAAa,CAAC,KAAK,cAAe,OAAOD,EAAO,KAAM,KAAK,EAAE,KAAK,IAAI,CAAC,EAChF,KAAK,eAAe,KAAK,QAAQ,IAAKA,CAAM,CAC7C,EACD,OAAI,KAAK,QAAQ,WAAa,CAAC,KAAK,QAAQ,UAC1CD,EAAM,EAEN,WAAWA,EAAM,CAAC,EAEbD,CACX,CACE,cAAcvJ,EAAUuH,EAAWwB,EAAM,SACvC,IAAIY,EAAepC,EACnB,MAAM3G,EAAUhK,EAASoJ,CAAQ,EAAIA,EAAW,KAAK,SAErD,GADI,OAAOA,GAAa,aAAY2J,EAAe3J,GAC/C,CAAC,KAAK,QAAQ,WAAa,KAAK,QAAQ,wBAAyB,CACnE,IAAIY,GAAA,YAAAA,EAAS,iBAAkB,WAAa,CAAC,KAAK,QAAQ,SAAW,KAAK,QAAQ,QAAQ,SAAW,GAAI,OAAO+I,EAAc,EAC9H,MAAMnC,EAAS,CAAE,EACXoC,EAASpN,GAAO,CAEpB,GADI,CAACA,GACDA,IAAQ,SAAU,OACT,KAAK,SAAS,cAAc,mBAAmBA,CAAG,EAC1D,QAAQqD,GAAK,CACZA,IAAM,UACN2H,EAAO,QAAQ3H,CAAC,EAAI,GAAG2H,EAAO,KAAK3H,CAAC,CAClD,CAAS,CACF,EACIe,EAIHgJ,EAAOhJ,CAAO,EAHI,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW,EAC7E,QAAQf,GAAK+J,EAAO/J,CAAC,CAAC,GAIlCzE,GAAAC,EAAA,KAAK,QAAQ,UAAb,YAAAA,EAAsB,UAAtB,MAAAD,EAAA,KAAAC,EAAgCwE,GAAK+J,EAAO/J,CAAC,GAC7C,KAAK,SAAS,iBAAiB,KAAK2H,EAAQ,KAAK,QAAQ,GAAIlP,GAAK,CAC5D,CAACA,GAAK,CAAC,KAAK,kBAAoB,KAAK,UAAU,KAAK,oBAAoB,KAAK,QAAQ,EACzFqR,EAAarR,CAAC,CACtB,CAAO,CACP,MACMqR,EAAa,IAAI,CAEvB,CACE,gBAAgBjK,EAAMpD,EAAIiL,EAAU,CAClC,MAAMgC,EAAWzS,EAAO,EACxB,OAAI,OAAO4I,GAAS,aAClB6H,EAAW7H,EACXA,EAAO,QAEL,OAAOpD,GAAO,aAChBiL,EAAWjL,EACXA,EAAK,QAEFoD,IAAMA,EAAO,KAAK,WAClBpD,IAAIA,EAAK,KAAK,QAAQ,IACtBiL,IAAUA,EAAWwB,GAC1B,KAAK,SAAS,iBAAiB,OAAOrJ,EAAMpD,EAAIuL,GAAO,CACrD0B,EAAS,QAAS,EAClBhC,EAASM,CAAG,CAClB,CAAK,EACM0B,CACX,CACE,IAAIvM,EAAQ,CACV,GAAI,CAACA,EAAQ,MAAM,IAAI,MAAM,+FAA+F,EAC5H,GAAI,CAACA,EAAO,KAAM,MAAM,IAAI,MAAM,0FAA0F,EAC5H,OAAIA,EAAO,OAAS,YAClB,KAAK,QAAQ,QAAUA,IAErBA,EAAO,OAAS,UAAYA,EAAO,KAAOA,EAAO,MAAQA,EAAO,SAClE,KAAK,QAAQ,OAASA,GAEpBA,EAAO,OAAS,qBAClB,KAAK,QAAQ,iBAAmBA,GAE9BA,EAAO,OAAS,eAClB,KAAK,QAAQ,WAAaA,GAExBA,EAAO,OAAS,iBAClBD,GAAc,iBAAiBC,CAAM,EAEnCA,EAAO,OAAS,cAClB,KAAK,QAAQ,UAAYA,GAEvBA,EAAO,OAAS,YAClB,KAAK,QAAQ,SAAS,KAAKA,CAAM,EAE5B,IACX,CACE,oBAAoB6C,EAAG,CACrB,GAAI,GAACA,GAAK,CAAC,KAAK,YACZ,GAAC,SAAU,KAAK,EAAE,QAAQA,CAAC,EAAI,IACnC,SAASgK,EAAK,EAAGA,EAAK,KAAK,UAAU,OAAQA,IAAM,CACjD,MAAMC,EAAY,KAAK,UAAUD,CAAE,EACnC,GAAI,GAAC,SAAU,KAAK,EAAE,QAAQC,CAAS,EAAI,KACvC,KAAK,MAAM,4BAA4BA,CAAS,EAAG,CACrD,KAAK,iBAAmBA,EACxB,KACR,CACA,CACQ,CAAC,KAAK,kBAAoB,KAAK,UAAU,QAAQjK,CAAC,EAAI,GAAK,KAAK,MAAM,4BAA4BA,CAAC,IACrG,KAAK,iBAAmBA,EACxB,KAAK,UAAU,QAAQA,CAAC,GAE9B,CACE,eAAerD,EAAK+K,EAAU,CAC5B,KAAK,qBAAuB/K,EAC5B,MAAM+M,EAAWzS,EAAO,EACxB,KAAK,KAAK,mBAAoB0F,CAAG,EACjC,MAAMuN,EAAclK,GAAK,CACvB,KAAK,SAAWA,EAChB,KAAK,UAAY,KAAK,SAAS,cAAc,mBAAmBA,CAAC,EACjE,KAAK,iBAAmB,OACxB,KAAK,oBAAoBA,CAAC,CAC3B,EACKmK,EAAO,CAACnC,EAAK,IAAM,CACnB,EACE,KAAK,uBAAyBrL,IAChCuN,EAAY,CAAC,EACb,KAAK,WAAW,eAAe,CAAC,EAChC,KAAK,qBAAuB,OAC5B,KAAK,KAAK,kBAAmB,CAAC,EAC9B,KAAK,OAAO,IAAI,kBAAmB,CAAC,GAGtC,KAAK,qBAAuB,OAE9BR,EAAS,QAAQ,IAAIrO,IAAS,KAAK,EAAE,GAAGA,CAAI,CAAC,EACzCqM,GAAUA,EAASM,EAAK,IAAI3M,IAAS,KAAK,EAAE,GAAGA,CAAI,CAAC,CACzD,EACK+O,EAASvK,GAAQ,SACjB,CAAClD,GAAO,CAACkD,GAAQ,KAAK,SAAS,mBAAkBA,EAAO,CAAE,GAC9D,MAAMwK,EAAKtT,EAAS8I,CAAI,EAAIA,EAAOA,GAAQA,EAAK,CAAC,EAC3CG,EAAI,KAAK,MAAM,4BAA4BqK,CAAE,EAAIA,EAAK,KAAK,SAAS,cAAc,sBAAsBtT,EAAS8I,CAAI,EAAI,CAACA,CAAI,EAAIA,CAAI,EACxIG,IACG,KAAK,UACRkK,EAAYlK,CAAC,EAEV,KAAK,WAAW,UAAU,KAAK,WAAW,eAAeA,CAAC,GAC/DzE,GAAAC,EAAA,KAAK,SAAS,mBAAd,YAAAA,EAAgC,oBAAhC,MAAAD,EAAA,KAAAC,EAAoDwE,IAEtD,KAAK,cAAcA,EAAGgI,GAAO,CAC3BmC,EAAKnC,EAAKhI,CAAC,CACnB,CAAO,CACF,EACD,MAAI,CAACrD,GAAO,KAAK,SAAS,kBAAoB,CAAC,KAAK,SAAS,iBAAiB,MAC5EyN,EAAO,KAAK,SAAS,iBAAiB,OAAM,CAAE,EACrC,CAACzN,GAAO,KAAK,SAAS,kBAAoB,KAAK,SAAS,iBAAiB,MAC9E,KAAK,SAAS,iBAAiB,OAAO,SAAW,EACnD,KAAK,SAAS,iBAAiB,OAAM,EAAG,KAAKyN,CAAM,EAEnD,KAAK,SAAS,iBAAiB,OAAOA,CAAM,EAG9CA,EAAOzN,CAAG,EAEL+M,CACX,CACE,UAAU/M,EAAKF,EAAI6N,EAAW,CAC5B,MAAMC,EAAS,CAACxS,EAAK+Q,KAAS7C,IAAS,CACrC,IAAItI,EACA,OAAOmL,GAAS,SAClBnL,EAAI,KAAK,QAAQ,iCAAiC,CAAC5F,EAAK+Q,CAAI,EAAE,OAAO7C,CAAI,CAAC,EAE1EtI,EAAI,CACF,GAAGmL,CACJ,EAEHnL,EAAE,IAAMA,EAAE,KAAO4M,EAAO,IACxB5M,EAAE,KAAOA,EAAE,MAAQ4M,EAAO,KAC1B5M,EAAE,GAAKA,EAAE,IAAM4M,EAAO,GAClB5M,EAAE,YAAc,KAAIA,EAAE,UAAYA,EAAE,WAAa2M,GAAaC,EAAO,WACzE,MAAMlQ,EAAe,KAAK,QAAQ,cAAgB,IAClD,IAAImQ,EACJ,OAAI7M,EAAE,WAAa,MAAM,QAAQ5F,CAAG,EAClCyS,EAAYzS,EAAI,IAAIS,GAAK,GAAGmF,EAAE,SAAS,GAAGtD,CAAY,GAAG7B,CAAC,EAAE,EAE5DgS,EAAY7M,EAAE,UAAY,GAAGA,EAAE,SAAS,GAAGtD,CAAY,GAAGtC,CAAG,GAAKA,EAE7D,KAAK,EAAEyS,EAAW7M,CAAC,CAC3B,EACD,OAAI5G,EAAS4F,CAAG,EACd4N,EAAO,IAAM5N,EAEb4N,EAAO,KAAO5N,EAEhB4N,EAAO,GAAK9N,EACZ8N,EAAO,UAAYD,EACZC,CACX,CACE,KAAKlP,EAAM,OACT,OAAOG,EAAA,KAAK,aAAL,YAAAA,EAAiB,UAAU,GAAGH,EACzC,CACE,UAAUA,EAAM,OACd,OAAOG,EAAA,KAAK,aAAL,YAAAA,EAAiB,OAAO,GAAGH,EACtC,CACE,oBAAoBoB,EAAI,CACtB,KAAK,QAAQ,UAAYA,CAC7B,CACE,mBAAmBA,EAAId,EAAU,GAAI,CACnC,GAAI,CAAC,KAAK,cACR,YAAK,OAAO,KAAK,kDAAmD,KAAK,SAAS,EAC3E,GAET,GAAI,CAAC,KAAK,WAAa,CAAC,KAAK,UAAU,OACrC,YAAK,OAAO,KAAK,6DAA8D,KAAK,SAAS,EACtF,GAET,MAAMgB,EAAMhB,EAAQ,KAAO,KAAK,kBAAoB,KAAK,UAAU,CAAC,EAC9D8O,EAAc,KAAK,QAAU,KAAK,QAAQ,YAAc,GACxDC,EAAU,KAAK,UAAU,KAAK,UAAU,OAAS,CAAC,EACxD,GAAI/N,EAAI,gBAAkB,SAAU,MAAO,GAC3C,MAAMgO,EAAiB,CAAC3K,EAAGmI,IAAM,CAC/B,MAAMyC,EAAY,KAAK,SAAS,iBAAiB,MAAM,GAAG5K,CAAC,IAAImI,CAAC,EAAE,EAClE,OAAOyC,IAAc,IAAMA,IAAc,GAAKA,IAAc,CAC7D,EACD,GAAIjP,EAAQ,SAAU,CACpB,MAAMkP,EAAYlP,EAAQ,SAAS,KAAMgP,CAAc,EACvD,GAAIE,IAAc,OAAW,OAAOA,CAC1C,CAGI,MAFI,QAAK,kBAAkBlO,EAAKF,CAAE,GAC9B,CAAC,KAAK,SAAS,iBAAiB,SAAW,KAAK,QAAQ,WAAa,CAAC,KAAK,QAAQ,yBACnFkO,EAAehO,EAAKF,CAAE,IAAM,CAACgO,GAAeE,EAAeD,EAASjO,CAAE,GAE9E,CACE,eAAeA,EAAIiL,EAAU,CAC3B,MAAMgC,EAAWzS,EAAO,EACxB,OAAK,KAAK,QAAQ,IAIdF,EAAS0F,CAAE,IAAGA,EAAK,CAACA,CAAE,GAC1BA,EAAG,QAAQ,GAAK,CACV,KAAK,QAAQ,GAAG,QAAQ,CAAC,EAAI,GAAG,KAAK,QAAQ,GAAG,KAAK,CAAC,CAChE,CAAK,EACD,KAAK,cAAcuL,GAAO,CACxB0B,EAAS,QAAS,EACdhC,GAAUA,EAASM,CAAG,CAChC,CAAK,EACM0B,IAXDhC,GAAUA,EAAU,EACjB,QAAQ,QAAS,EAW9B,CACE,cAAc7H,EAAM6H,EAAU,CAC5B,MAAMgC,EAAWzS,EAAO,EACpBF,EAAS8I,CAAI,IAAGA,EAAO,CAACA,CAAI,GAChC,MAAMiL,EAAY,KAAK,QAAQ,SAAW,CAAE,EACtCC,EAAUlL,EAAK,OAAOlD,GAAOmO,EAAU,QAAQnO,CAAG,EAAI,GAAK,KAAK,SAAS,cAAc,gBAAgBA,CAAG,CAAC,EACjH,OAAKoO,EAAQ,QAIb,KAAK,QAAQ,QAAUD,EAAU,OAAOC,CAAO,EAC/C,KAAK,cAAc/C,GAAO,CACxB0B,EAAS,QAAS,EACdhC,GAAUA,EAASM,CAAG,CAChC,CAAK,EACM0B,IARDhC,GAAUA,EAAU,EACjB,QAAQ,QAAS,EAQ9B,CACE,IAAI/K,EAAK,SAEP,GADKA,IAAKA,EAAM,KAAK,qBAAqBnB,EAAA,KAAK,YAAL,YAAAA,EAAgB,QAAS,EAAI,KAAK,UAAU,CAAC,EAAI,KAAK,WAC5F,CAACmB,EAAK,MAAO,MACjB,MAAMqO,EAAU,CAAC,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,KAAK,EACjbpI,IAAgBrH,EAAA,KAAK,WAAL,YAAAA,EAAe,gBAAiB,IAAIsG,GAAakH,IAAK,EAC5E,OAAOiC,EAAQ,QAAQpI,EAAc,wBAAwBjG,CAAG,CAAC,EAAI,IAAMA,EAAI,YAAa,EAAC,QAAQ,OAAO,EAAI,EAAI,MAAQ,KAChI,CACE,OAAO,eAAehB,EAAU,CAAE,EAAE+L,EAAU,CAC5C,OAAO,IAAI2B,EAAK1N,EAAS+L,CAAQ,CACrC,CACE,cAAc/L,EAAU,GAAI+L,EAAWwB,EAAM,CAC3C,MAAM+B,EAAoBtP,EAAQ,kBAC9BsP,GAAmB,OAAOtP,EAAQ,kBACtC,MAAMuP,EAAgB,CACpB,GAAG,KAAK,QACR,GAAGvP,EAED,QAAS,EAEZ,EACKwP,EAAQ,IAAI9B,EAAK6B,CAAa,EAcpC,IAbIvP,EAAQ,QAAU,QAAaA,EAAQ,SAAW,UACpDwP,EAAM,OAASA,EAAM,OAAO,MAAMxP,CAAO,GAErB,CAAC,QAAS,WAAY,UAAU,EACxC,QAAQ/D,GAAK,CACzBuT,EAAMvT,CAAC,EAAI,KAAKA,CAAC,CACvB,CAAK,EACDuT,EAAM,SAAW,CACf,GAAG,KAAK,QACT,EACDA,EAAM,SAAS,MAAQ,CACrB,mBAAoBA,EAAM,mBAAmB,KAAKA,CAAK,CACxD,EACGF,EAAmB,CACrB,MAAMG,EAAa,OAAO,KAAK,KAAK,MAAM,IAAI,EAAE,OAAO,CAACC,EAAMrL,KAC5DqL,EAAKrL,CAAC,EAAI,CACR,GAAG,KAAK,MAAM,KAAKA,CAAC,CACrB,EACDqL,EAAKrL,CAAC,EAAI,OAAO,KAAKqL,EAAKrL,CAAC,CAAC,EAAE,OAAO,CAACsL,EAAKnD,KAC1CmD,EAAInD,CAAC,EAAI,CACP,GAAGkD,EAAKrL,CAAC,EAAEmI,CAAC,CACb,EACMmD,GACND,EAAKrL,CAAC,CAAC,EACHqL,GACN,EAAE,EACLF,EAAM,MAAQ,IAAI3O,GAAc4O,EAAYF,CAAa,EACzDC,EAAM,SAAS,cAAgBA,EAAM,KAC3C,CACI,OAAAA,EAAM,WAAa,IAAI1N,EAAW0N,EAAM,SAAUD,CAAa,EAC/DC,EAAM,WAAW,GAAG,IAAK,CAAC/O,KAAUf,IAAS,CAC3C8P,EAAM,KAAK/O,EAAO,GAAGf,CAAI,CAC/B,CAAK,EACD8P,EAAM,KAAKD,EAAexD,CAAQ,EAClCyD,EAAM,WAAW,QAAUD,EAC3BC,EAAM,WAAW,iBAAiB,SAAS,MAAQ,CACjD,mBAAoBA,EAAM,mBAAmB,KAAKA,CAAK,CACxD,EACMA,CACX,CACE,QAAS,CACP,MAAO,CACL,QAAS,KAAK,QACd,MAAO,KAAK,MACZ,SAAU,KAAK,SACf,UAAW,KAAK,UAChB,iBAAkB,KAAK,gBACxB,CACL,CACA,CACK,MAACI,EAAWlC,EAAK,eAAc,EACpCkC,EAAS,eAAiBlC,EAAK,eAERkC,EAAS,eACpBA,EAAS,IACRA,EAAS,KACAA,EAAS,cACPA,EAAS,gBACrBA,EAAS,IACEA,EAAS,eACdA,EAAS,UACjBA,EAAS,EACJA,EAAS,OACIA,EAAS,oBACVA,EAAS,mBACbA,EAAS,eACVA,EAAS,cCvmE/B,KAAM,CACJ,MAAAC,GACA,QAAAC,EACF,EAAI,CAAE,EACN,SAASC,GAAS1U,EAAK,CACrB,OAAAyU,GAAQ,KAAKD,GAAM,KAAK,UAAW,CAAC,EAAGnS,GAAU,CAC/C,GAAIA,EACF,UAAWE,KAAQF,EACbrC,EAAIuC,CAAI,IAAM,SAAWvC,EAAIuC,CAAI,EAAIF,EAAOE,CAAI,EAG5D,CAAG,EACMvC,CACT,CACA,SAAS2U,GAAOC,EAAO,CACrB,OAAI,OAAOA,GAAU,SAAiB,GAGlB,CAAC,kBAAmB,uBAAwB,uBAAwB,2BAA4B,kBAAmB,gBAAiB,mBAAoB,aAAc,cAAe,oBAAqB,wBAAyB,oBAAqB,YAAY,EACrQ,KAAK9R,GAAWA,EAAQ,KAAK8R,CAAK,CAAC,CACxD,CAGA,MAAMC,GAAqB,wCACrBC,GAAkB,SAAUlF,EAAMtD,EAAK,CAI3C,MAAM1F,EAHQ,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAChF,KAAM,GACP,EAEK1E,EAAQ,mBAAmBoK,CAAG,EACpC,IAAI7J,EAAM,GAAGmN,CAAI,IAAI1N,CAAK,GAC1B,GAAI0E,EAAI,OAAS,EAAG,CAClB,MAAMmO,EAASnO,EAAI,OAAS,EAC5B,GAAI,OAAO,MAAMmO,CAAM,EAAG,MAAM,IAAI,MAAM,2BAA2B,EACrEtS,GAAO,aAAa,KAAK,MAAMsS,CAAM,CAAC,EAC1C,CACE,GAAInO,EAAI,OAAQ,CACd,GAAI,CAACiO,GAAmB,KAAKjO,EAAI,MAAM,EACrC,MAAM,IAAI,UAAU,0BAA0B,EAEhDnE,GAAO,YAAYmE,EAAI,MAAM,EACjC,CACE,GAAIA,EAAI,KAAM,CACZ,GAAI,CAACiO,GAAmB,KAAKjO,EAAI,IAAI,EACnC,MAAM,IAAI,UAAU,wBAAwB,EAE9CnE,GAAO,UAAUmE,EAAI,IAAI,EAC7B,CACE,GAAIA,EAAI,QAAS,CACf,GAAI,OAAOA,EAAI,QAAQ,aAAgB,WACrC,MAAM,IAAI,UAAU,2BAA2B,EAEjDnE,GAAO,aAAamE,EAAI,QAAQ,YAAa,GACjD,CAGE,GAFIA,EAAI,WAAUnE,GAAO,cACrBmE,EAAI,SAAQnE,GAAO,YACnBmE,EAAI,SAEN,OADiB,OAAOA,EAAI,UAAa,SAAWA,EAAI,SAAS,cAAgBA,EAAI,SACrE,CACd,IAAK,GACHnE,GAAO,oBACP,MACF,IAAK,MACHA,GAAO,iBACP,MACF,IAAK,SACHA,GAAO,oBACP,MACF,IAAK,OACHA,GAAO,kBACP,MACF,QACE,MAAM,IAAI,UAAU,4BAA4B,CACxD,CAEE,OAAImE,EAAI,cAAanE,GAAO,iBACrBA,CACT,EACMuS,GAAS,CACb,OAAOpF,EAAM1N,EAAO+S,EAASC,EAAQ,CACnC,IAAIC,EAAgB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CACtF,KAAM,IACN,SAAU,QACX,EACGF,IACFE,EAAc,QAAU,IAAI,KAC5BA,EAAc,QAAQ,QAAQA,EAAc,QAAQ,UAAYF,EAAU,GAAK,GAAI,GAEjFC,IAAQC,EAAc,OAASD,GACnC,SAAS,OAASJ,GAAgBlF,EAAM1N,EAAOiT,CAAa,CAC7D,EACD,KAAKvF,EAAM,CACT,MAAMwF,EAAS,GAAGxF,CAAI,IAChByF,EAAK,SAAS,OAAO,MAAM,GAAG,EACpC,QAAS,EAAI,EAAG,EAAIA,EAAG,OAAQ,IAAK,CAClC,IAAI9R,EAAI8R,EAAG,CAAC,EACZ,KAAO9R,EAAE,OAAO,CAAC,IAAM,KAAKA,EAAIA,EAAE,UAAU,EAAGA,EAAE,MAAM,EACvD,GAAIA,EAAE,QAAQ6R,CAAM,IAAM,EAAG,OAAO7R,EAAE,UAAU6R,EAAO,OAAQ7R,EAAE,MAAM,CAC7E,CACI,OAAO,IACR,EACD,OAAOqM,EAAMsF,EAAQ,CACnB,KAAK,OAAOtF,EAAM,GAAI,GAAIsF,CAAM,CACpC,CACA,EACA,IAAII,GAAW,CACb,KAAM,SAEN,OAAOC,EAAM,CACX,GAAI,CACF,aAAAC,CACN,EAAQD,EACJ,GAAIC,GAAgB,OAAO,SAAa,IACtC,OAAOR,GAAO,KAAKQ,CAAY,GAAK,MAGvC,EAED,kBAAkB7P,EAAK8P,EAAO,CAC5B,GAAI,CACF,aAAAD,EACA,cAAAE,EACA,aAAAC,EACA,cAAAR,CACN,EAAQM,EACAD,GAAgB,OAAO,SAAa,KACtCR,GAAO,OAAOQ,EAAc7P,EAAK+P,EAAeC,EAAcR,CAAa,CAEjF,CACA,EAEIS,GAAc,CAChB,KAAM,cAEN,OAAOL,EAAM,OACX,GAAI,CACF,kBAAAM,CACN,EAAQN,EACA1L,EACJ,GAAI,OAAO,OAAW,IAAa,CACjC,GAAI,CACF,OAAAiM,CACD,EAAG,OAAO,SACP,CAAC,OAAO,SAAS,UAAUtR,EAAA,OAAO,SAAS,OAAhB,YAAAA,EAAsB,QAAQ,MAAO,KAClEsR,EAAS,OAAO,SAAS,KAAK,UAAU,OAAO,SAAS,KAAK,QAAQ,GAAG,CAAC,GAG3E,MAAMC,EADQD,EAAO,UAAU,CAAC,EACX,MAAM,GAAG,EAC9B,QAAShS,EAAI,EAAGA,EAAIiS,EAAO,OAAQjS,IAAK,CACtC,MAAMkS,EAAMD,EAAOjS,CAAC,EAAE,QAAQ,GAAG,EAC7BkS,EAAM,GACID,EAAOjS,CAAC,EAAE,UAAU,EAAGkS,CAAG,IAC1BH,IACVhM,EAAQkM,EAAOjS,CAAC,EAAE,UAAUkS,EAAM,CAAC,EAG/C,CACA,CACI,OAAOnM,CACX,CACA,EAEIoM,GAAO,CACT,KAAM,OAEN,OAAOV,EAAM,OACX,GAAI,CACF,WAAAW,EACA,oBAAAC,CACN,EAAQZ,EACA1L,EACJ,GAAI,OAAO,OAAW,IAAa,CACjC,KAAM,CACJ,KAAAoM,CACD,EAAG,OAAO,SACX,GAAIA,GAAQA,EAAK,OAAS,EAAG,CAC3B,MAAMG,EAAQH,EAAK,UAAU,CAAC,EAC9B,GAAIC,EAAY,CACd,MAAMH,EAASK,EAAM,MAAM,GAAG,EAC9B,QAAStS,EAAI,EAAGA,EAAIiS,EAAO,OAAQjS,IAAK,CACtC,MAAMkS,EAAMD,EAAOjS,CAAC,EAAE,QAAQ,GAAG,EAC7BkS,EAAM,GACID,EAAOjS,CAAC,EAAE,UAAU,EAAGkS,CAAG,IAC1BE,IACVrM,EAAQkM,EAAOjS,CAAC,EAAE,UAAUkS,EAAM,CAAC,EAGnD,CACA,CACQ,GAAInM,EAAO,OAAOA,EAClB,GAAI,CAACA,GAASsM,EAAsB,GAAI,CACtC,MAAMhN,EAAW8M,EAAK,MAAM,iBAAiB,EAC7C,OAAK,MAAM,QAAQ9M,CAAQ,GAEpB3E,EAAA2E,EADO,OAAOgN,GAAwB,SAAWA,EAAsB,CACzD,IAAd,YAAA3R,EAAiB,QAAQ,IAAK,IAFP,MAGxC,CACA,CACA,CACI,OAAOqF,CACX,CACA,EAEA,IAAIwM,EAAyB,KAC7B,MAAMC,GAAwB,IAAM,CAClC,GAAID,IAA2B,KAAM,OAAOA,EAC5C,GAAI,CAEF,GADAA,EAAyB,OAAO,OAAW,KAAe,OAAO,eAAiB,KAC9E,CAACA,EACH,MAAO,GAET,MAAME,EAAU,wBAChB,OAAO,aAAa,QAAQA,EAAS,KAAK,EAC1C,OAAO,aAAa,WAAWA,CAAO,CACvC,MAAW,CACVF,EAAyB,EAC7B,CACE,OAAOA,CACT,EACA,IAAIG,GAAe,CACjB,KAAM,eAEN,OAAOjB,EAAM,CACX,GAAI,CACF,mBAAAkB,CACN,EAAQlB,EACJ,GAAIkB,GAAsBH,KACxB,OAAO,OAAO,aAAa,QAAQG,CAAkB,GAAK,MAG7D,EAED,kBAAkB9Q,EAAK8P,EAAO,CAC5B,GAAI,CACF,mBAAAgB,CACN,EAAQhB,EACAgB,GAAsBH,MACxB,OAAO,aAAa,QAAQG,EAAoB9Q,CAAG,CAEzD,CACA,EAEA,IAAI+Q,EAA2B,KAC/B,MAAMC,GAA0B,IAAM,CACpC,GAAID,IAA6B,KAAM,OAAOA,EAC9C,GAAI,CAEF,GADAA,EAA2B,OAAO,OAAW,KAAe,OAAO,iBAAmB,KAClF,CAACA,EACH,MAAO,GAET,MAAMH,EAAU,wBAChB,OAAO,eAAe,QAAQA,EAAS,KAAK,EAC5C,OAAO,eAAe,WAAWA,CAAO,CACzC,MAAW,CACVG,EAA2B,EAC/B,CACE,OAAOA,CACT,EACA,IAAIE,GAAiB,CACnB,KAAM,iBACN,OAAOrB,EAAM,CACX,GAAI,CACF,qBAAAsB,CACN,EAAQtB,EACJ,GAAIsB,GAAwBF,KAC1B,OAAO,OAAO,eAAe,QAAQE,CAAoB,GAAK,MAGjE,EACD,kBAAkBlR,EAAK8P,EAAO,CAC5B,GAAI,CACF,qBAAAoB,CACN,EAAQpB,EACAoB,GAAwBF,MAC1B,OAAO,eAAe,QAAQE,EAAsBlR,CAAG,CAE7D,CACA,EAEImR,GAAc,CAChB,KAAM,YACN,OAAOnS,EAAS,CACd,MAAMkF,EAAQ,CAAE,EAChB,GAAI,OAAO,UAAc,IAAa,CACpC,KAAM,CACJ,UAAA4G,EACA,aAAAsG,EACA,SAAA5N,CACR,EAAU,UACJ,GAAIsH,EAEF,QAAS3M,EAAI,EAAGA,EAAI2M,EAAU,OAAQ3M,IACpC+F,EAAM,KAAK4G,EAAU3M,CAAC,CAAC,EAGvBiT,GACFlN,EAAM,KAAKkN,CAAY,EAErB5N,GACFU,EAAM,KAAKV,CAAQ,CAE3B,CACI,OAAOU,EAAM,OAAS,EAAIA,EAAQ,MACtC,CACA,EAEImN,GAAU,CACZ,KAAM,UAEN,OAAOzB,EAAM,CACX,GAAI,CACF,QAAAyB,CACN,EAAQzB,EACA1L,EACJ,MAAMoN,EAAkBD,IAAY,OAAO,SAAa,IAAc,SAAS,gBAAkB,MACjG,OAAIC,GAAmB,OAAOA,EAAgB,cAAiB,aAC7DpN,EAAQoN,EAAgB,aAAa,MAAM,GAEtCpN,CACX,CACA,EAEI3I,GAAO,CACT,KAAM,OAEN,OAAOqU,EAAM,OACX,GAAI,CACF,oBAAA2B,CACN,EAAQ3B,EACJ,GAAI,OAAO,OAAW,IAAa,OACnC,MAAMpM,EAAW,OAAO,SAAS,SAAS,MAAM,iBAAiB,EACjE,OAAK,MAAM,QAAQA,CAAQ,GAEpB3E,EAAA2E,EADO,OAAO+N,GAAwB,SAAWA,EAAsB,CACzD,IAAd,YAAA1S,EAAiB,QAAQ,IAAK,IAFP,MAGlC,CACA,EAEI2S,GAAY,CACd,KAAM,YACN,OAAO5B,EAAM,SACX,GAAI,CACF,yBAAA6B,CACN,EAAQ7B,EAEJ,MAAM8B,EAAmC,OAAOD,GAA6B,SAAWA,EAA2B,EAAI,EAIjHjO,EAAW,OAAO,OAAW,OAAe5E,GAAAC,EAAA,OAAO,WAAP,YAAAA,EAAiB,WAAjB,YAAAD,EAA2B,MAAM,2DAGnF,GAAK4E,EAEL,OAAOA,EAASkO,CAAgC,CACpD,CACA,EAGA,IAAIC,GAAa,GACjB,GAAI,CAEF,SAAS,OACTA,GAAa,EAEf,MAAY,EACZ,MAAMC,GAAQ,CAAC,cAAe,SAAU,eAAgB,iBAAkB,YAAa,SAAS,EAC3FD,IAAYC,GAAM,OAAO,EAAG,CAAC,EAClC,MAAMC,GAAc,KAAO,CACzB,MAAAD,GACA,kBAAmB,MACnB,aAAc,UACd,mBAAoB,aACpB,qBAAsB,aAEtB,OAAQ,CAAC,cAAc,EACvB,gBAAiB,CAAC,QAAQ,EAI1B,wBAAyBvO,GAAKA,CAChC,GACA,MAAMyO,EAAQ,CACZ,YAAY/Q,EAAU,CACpB,IAAI/B,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACpF,KAAK,KAAO,mBACZ,KAAK,UAAY,CAAE,EACnB,KAAK,KAAK+B,EAAU/B,CAAO,CAC/B,CACE,MAAO,CACL,IAAI+B,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CACjF,cAAe,EAChB,EACG/B,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EAChF+S,EAAc,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACxF,KAAK,SAAWhR,EAChB,KAAK,QAAUgO,GAAS/P,EAAS,KAAK,SAAW,GAAI6S,IAAa,EAC9D,OAAO,KAAK,QAAQ,yBAA4B,UAAY,KAAK,QAAQ,wBAAwB,QAAQ,OAAO,EAAI,KACtH,KAAK,QAAQ,wBAA0BxO,GAAKA,EAAE,QAAQ,IAAK,GAAG,GAI5D,KAAK,QAAQ,qBAAoB,KAAK,QAAQ,oBAAsB,KAAK,QAAQ,oBACrF,KAAK,YAAc0O,EACnB,KAAK,YAAYpC,EAAQ,EACzB,KAAK,YAAYM,EAAW,EAC5B,KAAK,YAAYY,EAAY,EAC7B,KAAK,YAAYI,EAAc,EAC/B,KAAK,YAAYE,EAAW,EAC5B,KAAK,YAAYE,EAAO,EACxB,KAAK,YAAY9V,EAAI,EACrB,KAAK,YAAYiW,EAAS,EAC1B,KAAK,YAAYlB,EAAI,CACzB,CACE,YAAY0B,EAAU,CACpB,YAAK,UAAUA,EAAS,IAAI,EAAIA,EACzB,IACX,CACE,QAAS,CACP,IAAIC,EAAiB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAAK,QAAQ,MAClGC,EAAW,CAAE,EASjB,OARAD,EAAe,QAAQE,GAAgB,CACrC,GAAI,KAAK,UAAUA,CAAY,EAAG,CAChC,IAAIC,EAAS,KAAK,UAAUD,CAAY,EAAE,OAAO,KAAK,OAAO,EACzDC,GAAU,OAAOA,GAAW,WAAUA,EAAS,CAACA,CAAM,GACtDA,IAAQF,EAAWA,EAAS,OAAOE,CAAM,EACrD,CACA,CAAK,EACDF,EAAWA,EAAS,OAAOG,GAAwBA,GAAM,MAAQ,CAACrD,GAAOqD,CAAC,CAAC,EAAE,IAAIA,GAAK,KAAK,QAAQ,wBAAwBA,CAAC,CAAC,EACzH,KAAK,UAAY,KAAK,SAAS,eAAiB,KAAK,SAAS,cAAc,sBAA8BH,EACvGA,EAAS,OAAS,EAAIA,EAAS,CAAC,EAAI,IAC/C,CACE,kBAAkBlS,EAAK,CACrB,IAAIsS,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,KAAK,QAAQ,OACzFA,IACD,KAAK,QAAQ,iBAAmB,KAAK,QAAQ,gBAAgB,QAAQtS,CAAG,EAAI,IAChFsS,EAAO,QAAQC,GAAa,CACtB,KAAK,UAAUA,CAAS,GAAG,KAAK,UAAUA,CAAS,EAAE,kBAAkBvS,EAAK,KAAK,OAAO,CAClG,CAAK,EACL,CACA,CACA8R,GAAQ,KAAO,mBCvbf,SAASU,GAAQxR,EAAG,CAAE,0BAA2B,OAAOwR,GAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUxR,EAAG,CAAE,OAAO,OAAOA,GAAO,SAAUA,EAAG,CAAE,OAAOA,GAAmB,OAAO,QAArB,YAA+BA,EAAE,cAAgB,QAAUA,IAAM,OAAO,UAAY,SAAW,OAAOA,CAAE,EAAIwR,GAAQxR,CAAC,CAAE,CAcrT,SAASyR,IAAoB,CAClC,OAAO,OAAO,gBAAmB,aAAe,OAAO,eAAmB,IAAc,YAAcD,GAAQ,cAAc,KAAO,QACrI,CACA,SAASE,GAAUC,EAAc,CAC/B,MAAO,CAAC,CAACA,GAAgB,OAAOA,EAAa,MAAS,UACxD,CACO,SAASC,GAAYD,EAAc,CACxC,OAAID,GAAUC,CAAY,EACjBA,EAEF,QAAQ,QAAQA,CAAY,CACrC,CCzBA,SAASE,GAAQ/W,EAAG+B,EAAG,CAAE,IAAI,EAAI,OAAO,KAAK/B,CAAC,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIkF,EAAI,OAAO,sBAAsBlF,CAAC,EAAG+B,IAAMmD,EAAIA,EAAE,OAAO,SAAUnD,EAAG,CAAE,OAAO,OAAO,yBAAyB/B,EAAG+B,CAAC,EAAE,UAAa,IAAI,EAAE,KAAK,MAAM,EAAGmD,CAAC,EAAK,OAAO,CAAE,CAC7P,SAAS8R,GAAchX,EAAG,CAAE,QAAS+B,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CAAE,IAAI,EAAY,UAAUA,CAAC,GAAnB,KAAuB,UAAUA,CAAC,EAAI,GAAIA,EAAI,EAAIgV,GAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,QAAQ,SAAUhV,EAAG,CAAEkV,GAAgBjX,EAAG+B,EAAG,EAAEA,CAAC,CAAC,CAAI,GAAI,OAAO,0BAA4B,OAAO,iBAAiB/B,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI+W,GAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUhV,EAAG,CAAE,OAAO,eAAe/B,EAAG+B,EAAG,OAAO,yBAAyB,EAAGA,CAAC,CAAC,CAAI,EAAE,CAAG,OAAO/B,CAAE,CACrb,SAASiX,GAAgBjX,EAAG+B,EAAG,EAAG,CAAE,OAAQA,EAAImV,GAAenV,CAAC,KAAM/B,EAAI,OAAO,eAAeA,EAAG+B,EAAG,CAAE,MAAO,EAAG,WAAY,GAAI,aAAc,GAAI,SAAU,EAAE,CAAE,EAAI/B,EAAE+B,CAAC,EAAI,EAAG/B,CAAE,CAClL,SAASkX,GAAe9F,EAAG,CAAE,IAAI/O,EAAI8U,GAAa/F,EAAG,QAAQ,EAAG,OAAmBsF,EAAQrU,CAAC,GAArB,SAAyBA,EAAIA,EAAI,EAAG,CAC3G,SAAS8U,GAAa/F,EAAGrP,EAAG,CAAE,GAAgB2U,EAAQtF,CAAC,GAArB,UAA0B,CAACA,EAAG,OAAOA,EAAG,IAAIpR,EAAIoR,EAAE,OAAO,WAAW,EAAG,GAAepR,IAAX,OAAc,CAAE,IAAI,EAAIA,EAAE,KAAKoR,EAAGrP,CAAc,EAAG,GAAgB2U,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAG,MAAM,IAAI,UAAU,8CAA8C,CAAE,CAAG,OAAqB3U,IAAb,SAAiB,OAAS,QAAQqP,CAAC,CAAE,CAC1T,SAASsF,EAAQxR,EAAG,CAAE,0BAA2B,OAAOwR,EAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUxR,EAAG,CAAE,OAAO,OAAOA,GAAO,SAAUA,EAAG,CAAE,OAAOA,GAAmB,OAAO,QAArB,YAA+BA,EAAE,cAAgB,QAAUA,IAAM,OAAO,UAAY,SAAW,OAAOA,CAAE,EAAIwR,EAAQxR,CAAC,CAAE,CAE5T,IAAIkS,EAAW,OAAO,OAAU,WAAa,MAAQ,OACjD,OAAO,OAAW,KAAe,OAAO,MAC1CA,EAAW,OAAO,MACT,OAAO,OAAW,KAAe,OAAO,QACjDA,EAAW,OAAO,OAEpB,IAAIC,EACAV,OACE,OAAO,OAAW,KAAe,OAAO,eAC1CU,EAAoB,OAAO,eAClB,OAAO,OAAW,KAAe,OAAO,iBACjDA,EAAoB,OAAO,iBAG/B,IAAIC,GACA,OAAO,eAAkB,aACvB,OAAO,OAAW,KAAe,OAAO,cAC1CA,GAAmB,OAAO,cACjB,OAAO,OAAW,KAAe,OAAO,gBACjDA,GAAmB,OAAO,gBAG1B,OAAOF,GAAa,aAAYA,EAAW,QAC/C,GAAI,CAACA,GAAY,CAACC,GAAqB,CAACC,GACtC,GAAI,CACFC,GAAA,WAAO,2BAAa,OAAA7H,KAAA,6BAAE,KAAK,SAAU8H,EAAK,CACxCJ,EAAWI,EAAI,OACrB,CAAK,EAAE,MAAM,UAAY,EAAE,CACxB,MAAW,EAEd,IAAIC,GAAiB,SAAwBC,EAAKpD,EAAQ,CACxD,GAAIA,GAAUoC,EAAQpC,CAAM,IAAM,SAAU,CAC1C,IAAIqD,EAAc,GAClB,QAASC,KAAatD,EACpBqD,GAAe,IAAM,mBAAmBC,CAAS,EAAI,IAAM,mBAAmBtD,EAAOsD,CAAS,CAAC,EAEjG,GAAI,CAACD,EAAa,OAAOD,EACzBA,EAAMA,GAAOA,EAAI,QAAQ,GAAG,IAAM,GAAK,IAAM,KAAOC,EAAY,MAAM,CAAC,CAC3E,CACE,OAAOD,CACT,EACIG,GAAU,SAAiBH,EAAKI,EAAc7I,EAAU8I,EAAU,CACpE,IAAIjI,EAAW,SAAkBkI,EAAU,CACzC,GAAI,CAACA,EAAS,GAAI,OAAO/I,EAAS+I,EAAS,YAAc,QAAS,CAChE,OAAQA,EAAS,MACvB,CAAK,EACDA,EAAS,KAAI,EAAG,KAAK,SAAUzX,EAAM,CACnC0O,EAAS,KAAM,CACb,OAAQ+I,EAAS,OACjB,KAAMzX,CACd,CAAO,CACP,CAAK,EAAE,MAAM0O,CAAQ,CAClB,EACD,GAAI8I,EAAU,CACZ,IAAIE,EAAcF,EAASL,EAAKI,CAAY,EAC5C,GAAIG,aAAuB,QAAS,CAClCA,EAAY,KAAKnI,CAAQ,EAAE,MAAMb,CAAQ,EACzC,MACN,CACA,CACM,OAAO,OAAU,WACnB,MAAMyI,EAAKI,CAAY,EAAE,KAAKhI,CAAQ,EAAE,MAAMb,CAAQ,EAEtDmI,EAASM,EAAKI,CAAY,EAAE,KAAKhI,CAAQ,EAAE,MAAMb,CAAQ,CAE7D,EACIiJ,GAAmB,GACnBC,GAAmB,SAA0BjV,EAASwU,EAAKU,EAASnJ,EAAU,CAC5E/L,EAAQ,oBACVwU,EAAMD,GAAeC,EAAKxU,EAAQ,iBAAiB,GAErD,IAAImV,EAAUrB,GAAc,CAAE,EAAE,OAAO9T,EAAQ,eAAkB,WAAaA,EAAQ,gBAAkBA,EAAQ,aAAa,EACzH,OAAO,OAAW,KAAe,OAAO,OAAW,KAAe,OAAO,OAAO,QAAY,KAAe,OAAO,QAAQ,UAAY,OAAO,QAAQ,SAAS,OAChKmV,EAAQ,YAAY,EAAI,8BAA8B,OAAO,OAAO,QAAQ,QAAS,IAAI,EAAE,OAAO,OAAO,QAAQ,SAAU,GAAG,EAAE,OAAO,OAAO,QAAQ,KAAM,GAAG,GAE7JD,IAASC,EAAQ,cAAc,EAAI,oBACvC,IAAIC,EAAa,OAAOpV,EAAQ,gBAAmB,WAAaA,EAAQ,eAAekV,CAAO,EAAIlV,EAAQ,eACtG4U,EAAed,GAAc,CAC/B,OAAQoB,EAAU,OAAS,MAC3B,KAAMA,EAAUlV,EAAQ,UAAUkV,CAAO,EAAI,OAC7C,QAASC,CACb,EAAKH,GAAmB,CAAE,EAAGI,CAAU,EACjCP,EAAW,OAAO7U,EAAQ,gBAAmB,YAAcA,EAAQ,eAAe,QAAU,EAAIA,EAAQ,eAAiB,OAC7H,GAAI,CACF2U,GAAQH,EAAKI,EAAc7I,EAAU8I,CAAQ,CAC9C,OAAQ/X,EAAG,CACV,GAAI,CAACsY,GAAc,OAAO,KAAKA,CAAU,EAAE,SAAW,GAAK,CAACtY,EAAE,SAAWA,EAAE,QAAQ,QAAQ,iBAAiB,EAAI,EAC9G,OAAOiP,EAASjP,CAAC,EAEnB,GAAI,CACF,OAAO,KAAKsY,CAAU,EAAE,QAAQ,SAAUnT,EAAK,CAC7C,OAAO2S,EAAa3S,CAAG,CAC/B,CAAO,EACD0S,GAAQH,EAAKI,EAAc7I,EAAU8I,CAAQ,EAC7CG,GAAmB,EACpB,OAAQ3I,EAAK,CACZN,EAASM,CAAG,CAClB,CACA,CACA,EACIgJ,GAA4B,SAAmCrV,EAASwU,EAAKU,EAASnJ,EAAU,CAC9FmJ,GAAW1B,EAAQ0B,CAAO,IAAM,WAClCA,EAAUX,GAAe,GAAIW,CAAO,EAAE,MAAM,CAAC,GAE3ClV,EAAQ,oBACVwU,EAAMD,GAAeC,EAAKxU,EAAQ,iBAAiB,GAErD,GAAI,CACF,IAAIsV,EAAInB,EAAoB,IAAIA,EAAsB,IAAIC,GAAiB,oBAAoB,EAC/FkB,EAAE,KAAKJ,EAAU,OAAS,MAAOV,EAAK,CAAC,EAClCxU,EAAQ,aACXsV,EAAE,iBAAiB,mBAAoB,gBAAgB,EAEzDA,EAAE,gBAAkB,CAAC,CAACtV,EAAQ,gBAC1BkV,GACFI,EAAE,iBAAiB,eAAgB,mCAAmC,EAEpEA,EAAE,kBACJA,EAAE,iBAAiB,kBAAkB,EAEvC,IAAIC,EAAIvV,EAAQ,cAEhB,GADAuV,EAAI,OAAOA,GAAM,WAAaA,EAAG,EAAGA,EAChCA,EACF,QAASpW,KAAKoW,EACZD,EAAE,iBAAiBnW,EAAGoW,EAAEpW,CAAC,CAAC,EAG9BmW,EAAE,mBAAqB,UAAY,CACjCA,EAAE,WAAa,GAAKvJ,EAASuJ,EAAE,QAAU,IAAMA,EAAE,WAAa,KAAM,CAClE,OAAQA,EAAE,OACV,KAAMA,EAAE,YAChB,CAAO,CACF,EACDA,EAAE,KAAKJ,CAAO,CACf,OAAQpY,EAAG,CACV,SAAW,QAAQ,IAAIA,CAAC,CAC5B,CACA,EACI0Y,GAAU,SAAiBxV,EAASwU,EAAKU,EAASnJ,EAAU,CAM9D,GALI,OAAOmJ,GAAY,aACrBnJ,EAAWmJ,EACXA,EAAU,QAEZnJ,EAAWA,GAAY,UAAY,CAAE,EACjCmI,GAAYM,EAAI,QAAQ,OAAO,IAAM,EACvC,OAAOS,GAAiBjV,EAASwU,EAAKU,EAASnJ,CAAQ,EAEzD,GAAI0H,GAAmB,GAAI,OAAO,eAAkB,WAClD,OAAO4B,GAA0BrV,EAASwU,EAAKU,EAASnJ,CAAQ,EAElEA,EAAS,IAAI,MAAM,2CAA2C,CAAC,CACjE,EC9JA,SAASyH,EAAQxR,EAAG,CAAE,0BAA2B,OAAOwR,EAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUxR,EAAG,CAAE,OAAO,OAAOA,GAAO,SAAUA,EAAG,CAAE,OAAOA,GAAmB,OAAO,QAArB,YAA+BA,EAAE,cAAgB,QAAUA,IAAM,OAAO,UAAY,SAAW,OAAOA,CAAE,EAAIwR,EAAQxR,CAAC,CAAE,CAC5T,SAAS6R,GAAQ/W,EAAG+B,EAAG,CAAE,IAAI,EAAI,OAAO,KAAK/B,CAAC,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIkF,EAAI,OAAO,sBAAsBlF,CAAC,EAAG+B,IAAMmD,EAAIA,EAAE,OAAO,SAAUnD,EAAG,CAAE,OAAO,OAAO,yBAAyB/B,EAAG+B,CAAC,EAAE,UAAa,IAAI,EAAE,KAAK,MAAM,EAAGmD,CAAC,EAAK,OAAO,CAAE,CAC7P,SAAS8R,GAAchX,EAAG,CAAE,QAAS+B,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CAAE,IAAI,EAAY,UAAUA,CAAC,GAAnB,KAAuB,UAAUA,CAAC,EAAI,GAAIA,EAAI,EAAIgV,GAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,QAAQ,SAAUhV,EAAG,CAAEkV,GAAgBjX,EAAG+B,EAAG,EAAEA,CAAC,CAAC,CAAI,GAAI,OAAO,0BAA4B,OAAO,iBAAiB/B,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI+W,GAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUhV,EAAG,CAAE,OAAO,eAAe/B,EAAG+B,EAAG,OAAO,yBAAyB,EAAGA,CAAC,CAAC,CAAI,EAAE,CAAG,OAAO/B,CAAE,CACrb,SAAS2Y,GAAgB1Z,EAAGyQ,EAAG,CAAE,GAAI,EAAEzQ,aAAayQ,GAAI,MAAM,IAAI,UAAU,mCAAmC,CAAE,CACjH,SAASkJ,GAAkB5Y,EAAG+B,EAAG,CAAE,QAAS,EAAI,EAAG,EAAIA,EAAE,OAAQ,IAAK,CAAE,IAAImD,EAAInD,EAAE,CAAC,EAAGmD,EAAE,WAAaA,EAAE,YAAc,GAAIA,EAAE,aAAe,GAAI,UAAWA,IAAMA,EAAE,SAAW,IAAK,OAAO,eAAelF,EAAGkX,GAAehS,EAAE,GAAG,EAAGA,CAAC,CAAI,EACtO,SAAS2T,GAAa7Y,EAAG+B,EAAG,EAAG,CAAE,OAAOA,GAAK6W,GAAkB5Y,EAAE,UAAW+B,CAAC,EAAiC,OAAO,eAAe/B,EAAG,YAAa,CAAE,SAAU,GAAI,EAAGA,CAAE,CACzK,SAASiX,GAAgBjX,EAAG+B,EAAG,EAAG,CAAE,OAAQA,EAAImV,GAAenV,CAAC,KAAM/B,EAAI,OAAO,eAAeA,EAAG+B,EAAG,CAAE,MAAO,EAAG,WAAY,GAAI,aAAc,GAAI,SAAU,EAAE,CAAE,EAAI/B,EAAE+B,CAAC,EAAI,EAAG/B,CAAE,CAClL,SAASkX,GAAe9F,EAAG,CAAE,IAAI/O,EAAI8U,GAAa/F,EAAG,QAAQ,EAAG,OAAmBsF,EAAQrU,CAAC,GAArB,SAAyBA,EAAIA,EAAI,EAAG,CAC3G,SAAS8U,GAAa/F,EAAGrP,EAAG,CAAE,GAAgB2U,EAAQtF,CAAC,GAArB,UAA0B,CAACA,EAAG,OAAOA,EAAG,IAAIpR,EAAIoR,EAAE,OAAO,WAAW,EAAG,GAAepR,IAAX,OAAc,CAAE,IAAI,EAAIA,EAAE,KAAKoR,EAAGrP,CAAc,EAAG,GAAgB2U,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAG,MAAM,IAAI,UAAU,8CAA8C,CAAI,CAAC,OAAyB,OAAiBtF,CAAC,CAAE,CAG1T,IAAI2E,GAAc,UAAuB,CACvC,MAAO,CACL,SAAU,+BACV,QAAS,8BACT,MAAO,SAAexV,EAAM,CAC1B,OAAO,KAAK,MAAMA,CAAI,CACvB,EACD,UAAW,KAAK,UAChB,aAAc,SAAsBqF,EAAWtG,EAAKyQ,EAAe,CACjE,OAAOkH,GAAgB,CAAE,EAAE3X,EAAKyQ,GAAiB,EAAE,CACpD,EACD,iBAAkB,SAA0Bf,EAAW3J,EAAY,CAElE,EACD,QAASqT,GACT,eAAgB,OAAO,OAAW,IAAc,GAAQ,GAAK,GAAK,IAClE,cAAe,CAAE,EACjB,kBAAmB,CAAE,EACrB,YAAa,GACb,gBAAiB,GACjB,iBAAkB,GAClB,eAAgB,CACd,KAAM,OACN,YAAa,cACb,MAAO,SACb,CACG,CACH,EACII,GAAU,UAAY,CACxB,SAASA,EAAQ7T,EAAU,CACzB,IAAI/B,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EAChF6V,EAAa,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EACvFJ,GAAgB,KAAMG,CAAO,EAC7B,KAAK,SAAW7T,EAChB,KAAK,QAAU/B,EACf,KAAK,WAAa6V,EAClB,KAAK,KAAO,UACZ,KAAK,KAAK9T,EAAU/B,EAAS6V,CAAU,CAC3C,CACE,OAAOF,GAAaC,EAAS,CAAC,CAC5B,IAAK,OACL,MAAO,SAAc7T,EAAU,CAC7B,IAAI+T,EAAQ,KACR9V,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EAChF6V,EAAa,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAE,EAIvF,GAHA,KAAK,SAAW9T,EAChB,KAAK,QAAU+R,GAAcA,GAAcA,GAAc,CAAE,EAAEjB,GAAa,GAAG,KAAK,SAAW,CAAE,GAAG7S,CAAO,EACzG,KAAK,WAAa6V,EACd,KAAK,UAAY,KAAK,QAAQ,eAAgB,CAChD,IAAIE,EAAQ,YAAY,UAAY,CAClC,OAAOD,EAAM,OAAQ,CAC/B,EAAW,KAAK,QAAQ,cAAc,EAC1BtC,EAAQuC,CAAK,IAAM,UAAY,OAAOA,EAAM,OAAU,YAAYA,EAAM,MAAO,CAC3F,CACA,CACA,EAAK,CACD,IAAK,YACL,MAAO,SAAmBjK,EAAW3J,EAAY4J,EAAU,CACzD,KAAK,SAASD,EAAWA,EAAW3J,EAAYA,EAAY4J,CAAQ,CAC1E,CACA,EAAK,CACD,IAAK,OACL,MAAO,SAAcvH,EAAU9B,EAAWqJ,EAAU,CAClD,KAAK,SAAS,CAACvH,CAAQ,EAAGA,EAAU,CAAC9B,CAAS,EAAGA,EAAWqJ,CAAQ,CAC1E,CACA,EAAK,CACD,IAAK,WACL,MAAO,SAAkBD,EAAWkK,EAAkB7T,EAAY8T,EAAmBlK,EAAU,CAC7F,IAAImK,EAAS,KACTC,EAAW,KAAK,QAAQ,SACxB,OAAO,KAAK,QAAQ,UAAa,aACnCA,EAAW,KAAK,QAAQ,SAASrK,EAAW3J,CAAU,GAExDgU,EAAWvC,GAAYuC,CAAQ,EAC/BA,EAAS,KAAK,SAAUC,EAAkB,CACxC,GAAI,CAACA,EAAkB,OAAOrK,EAAS,KAAM,EAAE,EAC/C,IAAIyI,EAAM0B,EAAO,SAAS,aAAa,YAAYE,EAAkB,CACnE,IAAKtK,EAAU,KAAK,GAAG,EACvB,GAAI3J,EAAW,KAAK,GAAG,CACjC,CAAS,EACD+T,EAAO,QAAQ1B,EAAKzI,EAAUiK,EAAkBC,CAAiB,CACzE,CAAO,CACP,CACA,EAAK,CACD,IAAK,UACL,MAAO,SAAiBzB,EAAKzI,EAAUD,EAAW3J,EAAY,CAC5D,IAAIkU,EAAS,KACTrV,EAAM,OAAO8K,GAAc,SAAW,CAACA,CAAS,EAAIA,EACpDhL,EAAK,OAAOqB,GAAe,SAAW,CAACA,CAAU,EAAIA,EACrD+S,EAAU,KAAK,QAAQ,iBAAiBlU,EAAKF,CAAE,EACnD,KAAK,QAAQ,QAAQ,KAAK,QAAS0T,EAAKU,EAAS,SAAU7I,EAAK9Q,EAAK,CACnE,GAAIA,IAAQA,EAAI,QAAU,KAAOA,EAAI,OAAS,KAAO,CAACA,EAAI,QAAS,OAAOwQ,EAAS,kBAAoByI,EAAM,kBAAoBjZ,EAAI,OAAQ,EAAI,EACjJ,GAAIA,GAAOA,EAAI,QAAU,KAAOA,EAAI,OAAS,IAAK,OAAOwQ,EAAS,kBAAoByI,EAAM,kBAAoBjZ,EAAI,OAAQ,EAAK,EACjI,GAAI,CAACA,GAAO8Q,GAAOA,EAAI,QAAS,CAC9B,IAAIiK,EAAejK,EAAI,QAAQ,YAAa,EACxCkK,EAAiB,CAAC,SAAU,QAAS,UAAW,MAAM,EAAE,KAAK,SAAUC,EAAM,CAC/E,OAAOF,EAAa,QAAQE,CAAI,EAAI,EAChD,CAAW,EACD,GAAID,EACF,OAAOxK,EAAS,kBAAoByI,EAAM,KAAOnI,EAAI,QAAS,EAAI,CAE9E,CACQ,GAAIA,EAAK,OAAON,EAASM,EAAK,EAAK,EACnC,IAAIgB,EAAKoJ,EACT,GAAI,CACE,OAAOlb,EAAI,MAAS,SACtB8R,EAAMgJ,EAAO,QAAQ,MAAM9a,EAAI,KAAMuQ,EAAW3J,CAAU,EAE1DkL,EAAM9R,EAAI,IAEb,MAAW,CACVkb,EAAW,kBAAoBjC,EAAM,UAC/C,CACQ,GAAIiC,EAAU,OAAO1K,EAAS0K,EAAU,EAAK,EAC7C1K,EAAS,KAAMsB,CAAG,CAC1B,CAAO,CACP,CACA,EAAK,CACD,IAAK,SACL,MAAO,SAAgBvB,EAAWpJ,EAAWtG,EAAKyQ,EAAed,EAAU,CACzE,IAAI2K,EAAS,KACb,GAAK,KAAK,QAAQ,QAClB,CAAI,OAAO5K,GAAc,WAAUA,EAAY,CAACA,CAAS,GACzD,IAAIoJ,EAAU,KAAK,QAAQ,aAAaxS,EAAWtG,EAAKyQ,CAAa,EACjE8J,EAAW,EACXC,EAAY,CAAE,EACdC,EAAW,CAAE,EACjB/K,EAAU,QAAQ,SAAU9K,EAAK,CAC/B,IAAI8V,EAAUJ,EAAO,QAAQ,QACzB,OAAOA,EAAO,QAAQ,SAAY,aACpCI,EAAUJ,EAAO,QAAQ,QAAQ1V,EAAK0B,CAAS,GAEjD,IAAI8R,EAAMkC,EAAO,SAAS,aAAa,YAAYI,EAAS,CAC1D,IAAK9V,EACL,GAAI0B,CACd,CAAS,EACDgU,EAAO,QAAQ,QAAQA,EAAO,QAASlC,EAAKU,EAAS,SAAU7X,EAAM9B,EAAK,CACxEob,GAAY,EACZC,EAAU,KAAKvZ,CAAI,EACnBwZ,EAAS,KAAKtb,CAAG,EACbob,IAAa7K,EAAU,QACrB,OAAOC,GAAa,YAAYA,EAAS6K,EAAWC,CAAQ,CAE5E,CAAS,CACT,CAAO,EACP,CACA,EAAK,CACD,IAAK,SACL,MAAO,UAAkB,CACvB,IAAIE,EAAS,KACTC,EAAiB,KAAK,SACxBC,EAAmBD,EAAe,iBAClC/P,EAAgB+P,EAAe,cAC/BE,EAASF,EAAe,OACtBG,EAAkBF,EAAiB,SACvC,GAAI,EAAAE,GAAmBA,EAAgB,YAAW,IAAO,UACzD,KAAInL,EAAS,CAAE,EACXoC,EAAS,SAAgBpN,EAAK,CAChC,IAAIkD,EAAO+C,EAAc,mBAAmBjG,CAAG,EAC/CkD,EAAK,QAAQ,SAAUG,EAAG,CACpB2H,EAAO,QAAQ3H,CAAC,EAAI,GAAG2H,EAAO,KAAK3H,CAAC,CAClD,CAAS,CACF,EACD+J,EAAO+I,CAAe,EAClB,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ,QAAQ,SAAU9S,EAAG,CACxE,OAAO+J,EAAO/J,CAAC,CACvB,CAAO,EACD2H,EAAO,QAAQ,SAAUhL,EAAK,CAC5B+V,EAAO,WAAW,GAAG,QAAQ,SAAUjW,EAAI,CACzCmW,EAAiB,KAAKjW,EAAKF,EAAI,OAAQ,KAAM,KAAM,SAAUuL,EAAKhP,EAAM,CAClEgP,GAAK6K,EAAO,KAAK,qBAAqB,OAAOpW,EAAI,gBAAgB,EAAE,OAAOE,EAAK,SAAS,EAAGqL,CAAG,EAC9F,CAACA,GAAOhP,GAAM6Z,EAAO,IAAI,oBAAoB,OAAOpW,EAAI,gBAAgB,EAAE,OAAOE,CAAG,EAAG3D,CAAI,EAC/F4Z,EAAiB,OAAO,GAAG,OAAOjW,EAAK,GAAG,EAAE,OAAOF,CAAE,EAAGuL,EAAKhP,CAAI,CAC7E,CAAW,CACX,CAAS,CACT,CAAO,EACP,CACA,CAAG,CAAC,CACJ,EAAC,EACDuY,GAAQ,KAAO", "names": ["isString", "obj", "defer", "res", "rej", "promise", "resolve", "reject", "makeString", "object", "copy", "a", "s", "m", "lastOfPathSeparatorRegExp", "<PERSON><PERSON><PERSON>", "key", "canNotTraverseDeeper", "getLastOfPath", "path", "Empty", "stack", "stackIndex", "set<PERSON>ath", "newValue", "k", "e", "p", "last", "push<PERSON><PERSON>", "concat", "<PERSON><PERSON><PERSON>", "getPathWithDefaults", "data", "defaultData", "value", "deepExtend", "target", "source", "overwrite", "prop", "regexEscape", "str", "_entityMap", "escape", "RegExpCache", "capacity", "pattern", "regExpFromCache", "regExpNew", "chars", "looksLikeObjectPathRegExpCache", "looksLikeObjectPath", "nsSeparator", "keySeparator", "possibleChars", "c", "r", "matched", "ki", "deepFind", "tokens", "current", "i", "next", "nextPath", "j", "getCleanedCode", "code", "consoleLogger", "args", "type", "_b", "_a", "<PERSON><PERSON>", "concreteLogger", "options", "lvl", "prefix", "debugOnly", "moduleName", "baseLogger", "EventEmitter", "events", "listener", "event", "numListeners", "observer", "numTimesAdded", "ResourceStore", "ns", "index", "lng", "ignoreJSONStructure", "result", "resources", "deep", "pack", "v", "postProcessor", "module", "processors", "translator", "processor", "checkedLoadedFor", "shouldHandleAsObject", "Translator", "services", "o", "opt", "resolved", "namespaces", "wouldCheckForNsInKey", "seemsNaturalLanguage", "parts", "keys", "last<PERSON>ey", "returnDetails", "namespace", "appendNamespaceToCIMode", "resUsed<PERSON><PERSON>", "resExactUsedKey", "noObject", "joinArrays", "handleAsObjectInI18nFormat", "needsPluralHandling", "hasDefaultValue", "defaultValueSuffix", "defaultValueSuffixOrdinalFallback", "needsZeroSuffixLookup", "defaultValue", "resForObjHndl", "handleAsObject", "resType", "resTypeIsArray", "newKeyToUse", "<PERSON><PERSON><PERSON>", "usedDefault", "usedKey", "resForMissing", "updateMissing", "fk", "lngs", "fallbackLngs", "send", "l", "specificDefaultValue", "defaultForMissing", "language", "suffixes", "suffix", "skipOnVariables", "nestBef", "nb", "na", "nestAft", "postProcess", "postProcessorNames", "found", "exactUsed<PERSON>ey", "usedLng", "usedNS", "extracted", "needsContextHandling", "codes", "finalKeys", "pluralSuffix", "zeroSuffix", "ordinalPrefix", "<PERSON><PERSON>ey", "<PERSON><PERSON><PERSON>", "optionsKeys", "useOptionsReplaceForData", "option", "LanguageUtil", "formattedCode", "cleanedLng", "lngScOnly", "lngOnly", "supportedLng", "fallbacks", "fallbackCode", "fallbackCodes", "addCode", "fc", "suffixesOrder", "dummyRule", "count", "PluralResolver", "languageUtils", "cleanedCode", "cache<PERSON>ey", "rule", "lngPart", "pluralCategory1", "pluralCategory2", "pluralCategory", "deepFindWithDefaults", "regexSafe", "val", "Interpolator", "escape$1", "escapeValue", "useRawValueToEscape", "prefixEscaped", "suffixEscaped", "formatSeparator", "unescapeSuffix", "unescapePrefix", "nestingPrefix", "nestingPrefixEscaped", "nestingSuffix", "nestingSuffixEscaped", "nestingOptionsSeparator", "maxReplaces", "alwaysFormat", "getOrResetRegExp", "existingRegExp", "match", "replaces", "handleFormat", "f", "missingInterpolationHandler", "todo", "matchedVar", "temp", "safeValue", "clonedOptions", "handleHasOptions", "inheritedOptions", "sep", "optionsString", "matchedSingleQuotes", "matchedDoubleQuotes", "formatters", "doReduce", "elem", "parseFormatStr", "formatStr", "formatName", "formatOptions", "optStr", "rest", "<PERSON><PERSON><PERSON>", "createCachedFormatter", "fn", "cache", "optForCache", "frm", "createNonCachedFormatter", "<PERSON><PERSON><PERSON>", "cf", "formatter", "name", "format", "formats", "lastIndex", "mem", "formatted", "valOptions", "error", "removePending", "q", "Connector", "backend", "store", "languages", "callback", "toLoad", "pending", "toLoadLanguages", "toLoadNamespaces", "hasAllNamespaces", "err", "loaded", "loadedKeys", "n", "fcName", "tried", "wait", "resolver", "fallback<PERSON><PERSON><PERSON>", "isUpdate", "clb", "_d", "_c", "_e", "opts", "get", "ret", "transformOptions", "noop", "bindMemberFunctions", "inst", "I18n", "defOpts", "createClassOnDemand", "ClassOrObject", "lu", "deferred", "load", "finish", "t", "usedCallback", "append", "li", "lngInLngs", "setLngProps", "done", "setLng", "fl", "keyPrefix", "fixedT", "<PERSON><PERSON><PERSON>", "fallbackLng", "lastLng", "loadNotPending", "loadState", "preResult", "preloaded", "newLngs", "rtlLngs", "forkResourceStore", "mergedOptions", "clone", "clonedData", "prev", "acc", "instance", "slice", "for<PERSON>ach", "defaults", "hasXSS", "input", "fieldContentRegExp", "serializeCookie", "maxAge", "cookie", "minutes", "domain", "cookieOptions", "nameEQ", "ca", "cookie$1", "_ref", "lookup<PERSON><PERSON><PERSON>", "_ref2", "cookieMinutes", "cookieDomain", "querystring", "lookupQuerystring", "search", "params", "pos", "hash", "lookupHash", "lookupFromHashIndex", "query", "hasLocalStorageSupport", "localStorageAvailable", "<PERSON><PERSON><PERSON>", "localStorage", "lookupLocalStorage", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "userLanguage", "htmlTag", "internalHtmlTag", "lookupFromPathIndex", "subdomain", "lookupFromSubdomainIndex", "internalLookupFromSubdomainIndex", "canCookies", "order", "getDefaults", "Browser", "i18nOptions", "detector", "detectionOrder", "detected", "detectorName", "lookup", "d", "caches", "cacheName", "_typeof", "hasXMLHttpRequest", "isPromise", "<PERSON><PERSON><PERSON><PERSON>", "makePromise", "ownKeys", "_objectSpread", "_defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "fetchApi", "XmlHttpRequestApi", "ActiveXObjectApi", "__vitePreload", "mod", "addQueryString", "url", "queryString", "paramName", "fetchIt", "fetchOptions", "altFetch", "response", "altResponse", "omitFetchOptions", "requestWithFetch", "payload", "headers", "reqOptions", "requestWithXmlHttpRequest", "x", "h", "request", "_classCallCheck", "_defineProperties", "_createClass", "Backend", "allOptions", "_this", "timer", "loadUrlLanguages", "loadUrlNamespaces", "_this2", "loadPath", "resolvedLoadPath", "_this3", "errorMessage", "isNetworkError", "term", "parseErr", "_this4", "finished", "dataArray", "resArray", "addPath", "_this5", "_this$services", "backendConnector", "logger", "currentLanguage"], "ignoreList": [0, 1, 2, 3, 4], "sources": ["../../node_modules/.pnpm/i18next@25.2.1/node_modules/i18next/dist/esm/i18next.js", "../../node_modules/.pnpm/i18next-browser-languagedetector@8.2.0/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "../../node_modules/.pnpm/i18next-http-backend@3.0.2/node_modules/i18next-http-backend/esm/utils.js", "../../node_modules/.pnpm/i18next-http-backend@3.0.2/node_modules/i18next-http-backend/esm/request.js", "../../node_modules/.pnpm/i18next-http-backend@3.0.2/node_modules/i18next-http-backend/esm/index.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes((fallbackCode === false ? [] : fallbackCode) || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n", "const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, value, cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name, domain) {\n    this.create(name, '', -1, domain);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hash = {\n  name: 'hash',\n  // Deconstruct the options object and extract the lookupHash property and the lookupFromHashIndex property\n  lookup(_ref) {\n    let {\n      lookupHash,\n      lookupFromHashIndex\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      const {\n        hash\n      } = window.location;\n      if (hash && hash.length > 2) {\n        const query = hash.substring(1);\n        if (lookupHash) {\n          const params = query.split('&');\n          for (let i = 0; i < params.length; i++) {\n            const pos = params[i].indexOf('=');\n            if (pos > 0) {\n              const key = params[i].substring(0, pos);\n              if (key === lookupHash) {\n                found = params[i].substring(pos + 1);\n              }\n            }\n          }\n        }\n        if (found) return found;\n        if (!found && lookupFromHashIndex > -1) {\n          const language = hash.match(/\\/([a-zA-Z-]*)/g);\n          if (!Array.isArray(language)) return undefined;\n          const index = typeof lookupFromHashIndex === 'number' ? lookupFromHashIndex : 0;\n          return language[index]?.replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n    this.addDetector(hash);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport { hasXMLHttpRequest } from './utils.js';\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nif (!fetchApi && !XmlHttpRequestApi && !ActiveXObjectApi) {\n  try {\n    import('cross-fetch').then(function (mod) {\n      fetchApi = mod.default;\n    }).catch(function () {});\n  } catch (e) {}\n}\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x = XmlHttpRequestApi ? new XmlHttpRequestApi() : new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\nexport default request;", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;"], "file": "assets/vendor-i18n-Csuit5Sx.js"}