// Simple testimonial slider with draggable functionality
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM loaded, initializing slider");

  // Try to initialize immediately
  initSlider();

  // And also try again after a delay to ensure everything is loaded
  setTimeout(initSlider, 1000);
  setTimeout(initSlider, 2000);

  // Final attempt after page is fully loaded
  window.addEventListener("load", function () {
    setTimeout(initSlider, 500);
  });
});

function initSlider() {
  const slider = document.getElementById("testimonial-marquee");
  const track = document.getElementById("testimonial-track");

  if (!slider || !track) {
    // Only log if we're on a page that should have the slider
    if (
      document.querySelector(".testimonial-section") ||
      document.querySelector("#testimonial-marquee")
    ) {
      console.log("Slider elements not found yet");
    }
    return;
  }

  console.log("Slider elements found, setting up events");

  // Variables for dragging
  let isDown = false;
  let startX;
  let walkX = 0;

  // Add event listeners for mouse
  slider.addEventListener("mousedown", function (e) {
    isDown = true;
    slider.classList.add("active");
    startX = e.pageX;

    // Stop the animation while dragging
    track.style.animationPlayState = "paused";
    console.log("Mouse down detected at", startX);
    e.preventDefault();
  });

  slider.addEventListener("mouseleave", function () {
    if (isDown) {
      isDown = false;
      slider.classList.remove("active");
      // Reset transform and resume animation
      setTimeout(function () {
        track.style.transform = "";
        track.style.animationPlayState = "running";
      }, 100);
    }
  });

  slider.addEventListener("mouseup", function () {
    if (isDown) {
      isDown = false;
      slider.classList.remove("active");
      // Reset transform and resume animation
      setTimeout(function () {
        track.style.transform = "";
        track.style.animationPlayState = "running";
      }, 100);
      console.log("Mouse up detected");
    }
  });

  slider.addEventListener("mousemove", function (e) {
    if (!isDown) return;
    e.preventDefault();

    const x = e.pageX;
    const walk = x - startX;
    walkX = walk;

    // Apply the transform directly
    track.style.transform = `translateX(${walkX}px)`;
    console.log("Dragging: " + walkX + "px");

    // Update start position for smoother dragging
    startX = x;
  });

  // Add event listeners for touch
  slider.addEventListener(
    "touchstart",
    function (e) {
      isDown = true;
      slider.classList.add("active");
      startX = e.touches[0].pageX;

      // Stop the animation while dragging
      track.style.animationPlayState = "paused";
      e.preventDefault();
    },
    { passive: false }
  );

  slider.addEventListener("touchend", function () {
    if (isDown) {
      isDown = false;
      slider.classList.remove("active");
      // Reset transform and resume animation
      setTimeout(function () {
        track.style.transform = "";
        track.style.animationPlayState = "running";
      }, 100);
    }
  });

  slider.addEventListener(
    "touchmove",
    function (e) {
      if (!isDown) return;
      e.preventDefault();

      const x = e.touches[0].pageX;
      const walk = x - startX;
      walkX = walk;

      // Apply the transform directly
      track.style.transform = `translateX(${walkX}px)`;

      // Update start position for smoother dragging
      startX = x;
    },
    { passive: false }
  );

  // Add a click handler to pause/resume animation on click
  slider.addEventListener("click", function (e) {
    // Only handle clicks, not drag end events
    if (Math.abs(walkX) < 5) {
      if (track.style.animationPlayState === "paused") {
        track.style.animationPlayState = "running";
      } else {
        track.style.animationPlayState = "paused";
      }
    }
  });

  console.log("Slider initialized successfully");
}
