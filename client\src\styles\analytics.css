/* Analytics Components Styles */

/* Time Range Selector */
.time-range-selector-wrapper .dropdown-menu {
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.time-range-selector-wrapper .dropdown-item:hover {
  background-color: #f8f9fa;
}

.time-range-selector-wrapper .dropdown-item.active {
  background-color: #0d6efd !important;
  color: white !important;
}

/* Language Selector */
.language-selector-wrapper .dropdown-menu {
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.language-selector-wrapper .dropdown-item:hover {
  background-color: #f8f9fa;
}

.language-selector-wrapper .dropdown-item.active {
  background-color: #0d6efd !important;
  color: white !important;
}

/* Analytics Dropdowns */
.time-range-selector-wrapper .dropdown-item,
.language-selector-wrapper .dropdown-item {
  color: #212529 !important;
  white-space: nowrap;
  transition: background-color 0.15s ease-in-out;
}

.time-range-selector-wrapper .dropdown-item:hover,
.language-selector-wrapper .dropdown-item:hover {
  background-color: #f8f9fa !important;
  color: #212529 !important;
}

.time-range-selector-wrapper .dropdown-item.active,
.language-selector-wrapper .dropdown-item.active {
  background-color: #0d6efd !important;
  color: white !important;
}

/* Analytics Overview Cards */
.analytics-overview .card {
  background-color: #ffffff !important;
  border: 1px solid #e9ecef !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analytics-overview .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Analytics Chart */
.analytics-chart-container {
  position: relative;
}

/* Heatmap */
.heatmap-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow: hidden;
}

/* Time labels */
.heatmap-time-labels {
  display: grid;
  grid-template-columns: 40px 1fr;
  gap: 8px;
  align-items: center;
}

.heatmap-time-row {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0;
}

.heatmap-time-label {
  font-size: 11px;
  color: #6c757d;
  text-align: center;
}

/* Grid container */
.heatmap-grid-container {
  user-select: none;
  overflow: hidden;
}

.heatmap-row {
  display: grid;
  grid-template-columns: 40px 1fr;
  gap: 8px;
  margin-bottom: 2px;
  align-items: center;
}

.heatmap-day-label {
  font-size: 11px;
  color: #6c757d;
  text-align: right;
  padding-right: 4px;
}

.heatmap-cells {
  display: grid;
  grid-template-columns: repeat(24, 1fr);
  gap: 1px;
  overflow: hidden;
}

.heatmap-cell {
  aspect-ratio: 1;
  min-width: 8px;
  max-width: 12px;
  width: 100%;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.heatmap-cell:hover {
  transform: scale(1.2);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .heatmap-cell {
    min-width: 6px;
    max-width: 10px;
  }
}

@media (max-width: 992px) {
  .heatmap-cell {
    min-width: 4px;
    max-width: 8px;
  }

  .heatmap-day-label {
    font-size: 10px;
  }

  .heatmap-time-label {
    font-size: 10px;
  }
}

@media (max-width: 576px) {
  .heatmap-cells {
    grid-template-columns: repeat(12, 1fr);
  }

  .heatmap-cell {
    min-width: 8px;
    max-width: 12px;
  }
}

/* Posts Table */
.posts-table th {
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.posts-table .cursor-pointer {
  cursor: pointer;
  user-select: none;
}

.posts-table .cursor-pointer:hover {
  background-color: #f8f9fa;
}

.posts-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Column selector buttons */
.posts-table .btn-sm {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Skeleton loading */
.analytics-overview-skeleton .placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .time-range-selector-wrapper,
  .language-selector-wrapper {
    margin-bottom: 1rem;
  }

  .analytics-overview .col-md-4 {
    margin-bottom: 1rem;
  }

  .heatmap-cell {
    width: 8px !important;
    height: 8px !important;
  }

  .posts-table .table-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .heatmap-cell {
    width: 6px !important;
    height: 6px !important;
  }

  .posts-table .btn-sm {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Force light mode for admin analytics - override dark mode */
@media (prefers-color-scheme: dark) {
  .analytics-overview .card {
    background-color: #ffffff !important;
    border-color: #e9ecef !important;
    color: #212529 !important;
  }

  .analytics-overview .card .text-dark {
    color: #212529 !important;
  }

  .analytics-overview .card .text-muted {
    color: #6c757d !important;
  }
}

/* Utility classes */
.transition-transform {
  transition: transform 0.2s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}
