// client/src/pages/AdminProducts.jsx

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import { adminAPI, API_BASE_URL } from "../utils/api";

const AdminProducts = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    status: "all",
    search: "",
  });
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    loadProducts();
  }, [filters]);

  const loadProducts = async () => {
    try {
      setLoading(true);

      const params = {};
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== "all") {
          params[key] = value;
        }
      });

      const { response, data } = await adminAPI.getProducts(params);

      if (data.success) {
        setProducts(data.data?.products || data.products || []);
        setPagination(data.data?.pagination || {});
      } else {
        setError(data.message || "Failed to load products");
      }
    } catch (error) {
      console.error("Load products error:", error);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this product?")) {
      return;
    }

    try {
      const { response, data } = await adminAPI.deleteProduct(id);

      if (response.ok && data.success) {
        setProducts(products.filter((product) => product.id !== id));
      } else {
        setError(data.message || "Failed to delete product");
      }
    } catch (error) {
      console.error("Delete product error:", error);
      setError("Failed to delete product");
    }
  };

  const handleToggleVisibility = async (id) => {
    try {
      const product = products.find((p) => p.id === id);
      const newStatus = product.status === "published" ? "draft" : "published";

      const { response, data } = await adminAPI.updateProduct(id, {
        status: newStatus,
      });

      if (response.ok && data.success) {
        setProducts(
          products.map((p) => (p.id === id ? { ...p, status: newStatus } : p))
        );
      } else {
        setError(data.message || "Failed to update product status");
      }
    } catch (error) {
      console.error("Toggle visibility error:", error);
      setError("Failed to update product status");
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filtering
    }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Utility function to construct image URLs
  const getImageUrl = (filename) => {
    if (!filename) return null;

    // If it's already a full URL, return as is
    if (filename.startsWith("http")) {
      return filename;
    }

    // Construct the full URL for uploaded images
    const baseUrl = API_BASE_URL.replace("/api", "");
    return `${baseUrl}/uploads/product-images/${filename}`;
  };

  // Get display image from product images array
  const getDisplayImage = (product) => {
    if (product.images && product.images.length > 0) {
      const displayImage =
        product.images.find((img) => img.isDisplay) || product.images[0];
      return displayImage.filename;
    }
    return product.featuredImage;
  };

  const getStatusBadge = (product) => {
    if (product.status === "draft") {
      return (
        <span className="badge bg-secondary">
          <iconify-icon
            icon="solar:document-text-bold"
            className="me-1"
          ></iconify-icon>
          Draft
        </span>
      );
    }

    return (
      <span className="badge bg-success">
        <iconify-icon
          icon="solar:check-circle-bold"
          className="me-1"
        ></iconify-icon>
        Published
      </span>
    );
  };

  return (
    <>
      <SEO
        title="Manage Products - Admin"
        description="Manage products in the admin panel"
        noIndex={true}
      />

      <AdminLayout title="Products">
        {/* Action Bar */}
        <div className="mb-30">
          <div className="row align-items-center">
            <div className="col-12 col-lg-6 mb-3 mb-lg-0">
              <p className="section-descr mb-0">
                Manage your webstore products, create new items, and organize
                your catalog.
              </p>
            </div>
            <div className="col-12 col-lg-6 text-lg-end">
              <button
                onClick={() => navigate("/admin/products/new")}
                className="btn btn-mod btn-color btn-round w-100 w-lg-auto"
              >
                <iconify-icon
                  icon="solar:add-circle-bold"
                  className="me-2"
                ></iconify-icon>
                New Product
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="admin-table mb-30" style={{ padding: "15px 20px" }}>
          <div className="row g-3">
            <div className="col-12 col-md-6 col-lg-4">
              <label className="form-label">Search Products</label>
              <input
                type="text"
                className="form-control"
                placeholder="Search by title or slug..."
                value={filters.search}
                onChange={(e) => handleFilterChange("search", e.target.value)}
              />
            </div>

            <div className="col-12 col-md-6 col-lg-4">
              <label className="form-label">Status</label>
              <select
                className="form-control"
                value={filters.status}
                onChange={(e) => handleFilterChange("status", e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            <div className="col-12 col-lg-4 d-flex align-items-end">
              <button
                onClick={() =>
                  setFilters({
                    page: 1,
                    limit: 10,
                    status: "all",
                    search: "",
                  })
                }
                className="btn btn-mod btn-border btn-round w-100"
              >
                <iconify-icon
                  icon="solar:refresh-bold"
                  className="me-2"
                ></iconify-icon>
                Reset Filters
              </button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-danger mb-30" role="alert">
            <iconify-icon
              icon="solar:danger-triangle-bold"
              className="me-2"
            ></iconify-icon>
            {error}
          </div>
        )}

        {/* Products Table */}
        <div className="admin-table">
          {loading ? (
            <div className="text-center py-60" style={{ padding: "40px 20px" }}>
              <iconify-icon
                icon="solar:refresh-bold"
                className="fa-2x color-primary-1 mb-20"
                style={{ animation: "spin 1s linear infinite" }}
              ></iconify-icon>
              <div className="hs-line-4 font-alt black">
                Loading products...
              </div>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-60" style={{ padding: "40px 20px" }}>
              <iconify-icon
                icon="solar:shop-bold"
                className="fa-3x color-gray-light-1 mb-20"
              ></iconify-icon>
              <div className="hs-line-4 font-alt black mb-10">
                No products found
              </div>
              <p className="section-descr mb-30">
                {filters.search || filters.status !== "all"
                  ? "Try adjusting your search filters or create your first product."
                  : "Get started by creating your first product."}
              </p>
              <button
                onClick={() => navigate("/admin/products/new")}
                className="btn btn-mod btn-color btn-round"
              >
                <iconify-icon
                  icon="solar:add-circle-bold"
                  className="me-2"
                ></iconify-icon>
                Create First Product
              </button>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="d-none d-lg-block">
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Product</th>
                        <th>Status</th>
                        <th>Pricing</th>
                        <th>Created</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {products.map((product) => {
                        const englishTranslation =
                          product.translations?.find(
                            (t) => t.language === "en"
                          ) || product.translations?.[0];

                        return (
                          <tr key={product.id}>
                            <td>
                              <div className="d-flex align-items-center">
                                {getDisplayImage(product) && (
                                  <img
                                    className="rounded me-3"
                                    src={getImageUrl(getDisplayImage(product))}
                                    alt=""
                                    style={{
                                      width: "50px",
                                      height: "50px",
                                      objectFit: "cover",
                                    }}
                                    onError={(e) => {
                                      e.target.style.display = "none";
                                    }}
                                  />
                                )}
                                <div>
                                  <div className="fw-bold">
                                    {englishTranslation?.title ||
                                      product.title ||
                                      "Untitled"}
                                  </div>
                                  <small className="text-muted">
                                    /{product.slug}
                                  </small>
                                </div>
                              </div>
                            </td>
                            <td>{getStatusBadge(product)}</td>
                            <td>
                              <div>
                                {product.whitelabelPrice && (
                                  <div className="small">
                                    <strong>Whitelabel:</strong> €
                                    {product.whitelabelPrice}
                                  </div>
                                )}
                                {product.subscriptionPrice && (
                                  <div className="small">
                                    <strong>Subscription:</strong> €
                                    {product.subscriptionPrice}/mo
                                  </div>
                                )}
                                {!product.whitelabelPrice &&
                                  !product.subscriptionPrice && (
                                    <span className="text-muted">
                                      No pricing set
                                    </span>
                                  )}
                              </div>
                            </td>
                            <td>{formatDate(product.createdAt)}</td>
                            <td>
                              <div className="btn-group" role="group">
                                <button
                                  onClick={() =>
                                    navigate(
                                      `/admin/products/edit/${product.id}`
                                    )
                                  }
                                  className="btn btn-sm btn-outline-primary"
                                  title="Edit"
                                >
                                  <iconify-icon icon="solar:pen-bold"></iconify-icon>
                                </button>

                                <button
                                  onClick={() =>
                                    handleToggleVisibility(product.id)
                                  }
                                  className={`btn btn-sm ${
                                    product.status === "published"
                                      ? "btn-outline-warning"
                                      : "btn-outline-success"
                                  }`}
                                  title={
                                    product.status === "published"
                                      ? "Unpublish"
                                      : "Publish"
                                  }
                                >
                                  <iconify-icon
                                    icon={
                                      product.status === "published"
                                        ? "solar:eye-closed-bold"
                                        : "solar:eye-bold"
                                    }
                                  ></iconify-icon>
                                </button>

                                <button
                                  onClick={() => handleDelete(product.id)}
                                  className="btn btn-sm btn-outline-danger"
                                  title="Delete"
                                >
                                  <iconify-icon icon="solar:trash-bin-trash-bold"></iconify-icon>
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Mobile Card View */}
              <div className="d-lg-none">
                <div className="row g-3">
                  {products.map((product) => {
                    const englishTranslation =
                      product.translations?.find((t) => t.language === "en") ||
                      product.translations?.[0];

                    return (
                      <div key={product.id} className="col-12">
                        <div className="card border-0 shadow-sm">
                          <div className="card-body p-3">
                            <div className="row align-items-center">
                              <div className="col-12 mb-2">
                                <div className="d-flex align-items-center">
                                  {getDisplayImage(product) && (
                                    <img
                                      className="rounded me-3"
                                      src={getImageUrl(
                                        getDisplayImage(product)
                                      )}
                                      alt=""
                                      style={{
                                        width: "40px",
                                        height: "40px",
                                        objectFit: "cover",
                                      }}
                                      onError={(e) => {
                                        e.target.style.display = "none";
                                      }}
                                    />
                                  )}
                                  <div className="flex-grow-1">
                                    <h6 className="mb-1 fw-bold">
                                      {englishTranslation?.title ||
                                        product.title ||
                                        "Untitled"}
                                    </h6>
                                    <small className="text-muted">
                                      /{product.slug}
                                    </small>
                                  </div>
                                </div>
                              </div>

                              <div className="col-6 col-sm-4 mb-2">
                                <small className="text-muted d-block">
                                  Status
                                </small>
                                <div>{getStatusBadge(product)}</div>
                              </div>

                              <div className="col-6 col-sm-4 mb-2">
                                <small className="text-muted d-block">
                                  Pricing
                                </small>
                                <div>
                                  {product.whitelabelPrice && (
                                    <div className="small">
                                      <strong>WL:</strong> €
                                      {product.whitelabelPrice}
                                    </div>
                                  )}
                                  {product.subscriptionPrice && (
                                    <div className="small">
                                      <strong>Sub:</strong> €
                                      {product.subscriptionPrice}/mo
                                    </div>
                                  )}
                                  {!product.whitelabelPrice &&
                                    !product.subscriptionPrice && (
                                      <span className="text-muted small">
                                        No pricing
                                      </span>
                                    )}
                                </div>
                              </div>

                              <div className="col-12 col-sm-4 mb-2">
                                <small className="text-muted d-block">
                                  Created
                                </small>
                                <small>{formatDate(product.createdAt)}</small>
                              </div>

                              <div className="col-12">
                                <div className="d-flex gap-2 flex-wrap">
                                  <button
                                    onClick={() =>
                                      navigate(
                                        `/admin/products/edit/${product.id}`
                                      )
                                    }
                                    className="btn btn-sm btn-outline-primary flex-fill"
                                    title="Edit"
                                  >
                                    <iconify-icon
                                      icon="solar:pen-bold"
                                      className="me-1"
                                    ></iconify-icon>
                                    Edit
                                  </button>

                                  <button
                                    onClick={() =>
                                      handleToggleVisibility(product.id)
                                    }
                                    className={`btn btn-sm flex-fill ${
                                      product.status === "published"
                                        ? "btn-outline-warning"
                                        : "btn-outline-success"
                                    }`}
                                    title={
                                      product.status === "published"
                                        ? "Unpublish"
                                        : "Publish"
                                    }
                                  >
                                    <iconify-icon
                                      icon={
                                        product.status === "published"
                                          ? "solar:eye-closed-bold"
                                          : "solar:eye-bold"
                                      }
                                      className="me-1"
                                    ></iconify-icon>
                                    {product.status === "published"
                                      ? "Hide"
                                      : "Show"}
                                  </button>

                                  <button
                                    onClick={() => handleDelete(product.id)}
                                    className="btn btn-sm btn-outline-danger flex-fill"
                                    title="Delete"
                                  >
                                    <iconify-icon
                                      icon="solar:trash-bin-trash-bold"
                                      className="me-1"
                                    ></iconify-icon>
                                    Delete
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="row mt-30 align-items-center">
            <div className="col-12 col-md-6 mb-3 mb-md-0">
              <p className="small text-muted mb-0 text-center text-md-start">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                of {pagination.total} results
              </p>
            </div>
            <div className="col-12 col-md-6">
              <nav aria-label="Products pagination">
                <ul className="pagination pagination-sm justify-content-center justify-content-md-end mb-0">
                  <li
                    className={`page-item ${
                      pagination.page <= 1 ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))
                      }
                      disabled={pagination.page <= 1}
                    >
                      Previous
                    </button>
                  </li>

                  <li className="page-item active">
                    <span className="page-link">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                  </li>

                  <li
                    className={`page-item ${
                      pagination.page >= pagination.pages ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))
                      }
                      disabled={pagination.page >= pagination.pages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default AdminProducts;
