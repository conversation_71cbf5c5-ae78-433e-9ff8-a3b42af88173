#!/bin/bash

# Setup script for upload directories (local development only)
echo "🚀 Setting up upload directories for local development..."

# Create upload directories for local development
mkdir -p uploads/blog-images
mkdir -p uploads/temp

# Set proper permissions
chmod 755 uploads
chmod 755 uploads/blog-images
chmod 755 uploads/temp

echo "✅ Upload directories created successfully!"
echo "📁 uploads/blog-images - Ready for blog images"
echo "📁 uploads/temp - Ready for temporary files"
echo ""
echo "ℹ️  Note: These directories are for local development only."
echo "ℹ️  In production, directories are created by Docker entrypoint script."

# List the structure
echo ""
echo "📋 Directory structure:"
ls -la uploads/
ls -la uploads/blog-images/
ls -la uploads/temp/
