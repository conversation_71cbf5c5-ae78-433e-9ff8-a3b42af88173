/* Module <PERSON><PERSON> Styles */
.works-filter.works-filter-elegant {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px; /* Creates space between buttons in all directions */
}

.works-filter.works-filter-elegant a.filter {
  display: inline-block;
  margin: 0;
  padding: 5px 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  color: #fff;
  cursor: pointer;
  transition: all 0.27s cubic-bezier(0, 0, 0.58, 1);
}

.works-filter.works-filter-elegant a.filter:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background-color: rgba(255, 255, 255, 0.1);
}

.works-filter.works-filter-elegant a.filter.active {
  border-color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}
