@import "swiper/css";
@import "../../public/assets/css/styles.css";
@import "jarallax/dist/jarallax.min.css";
@import "swiper/css/effect-fade";
@import "react-modal-video/css/modal-video.css";
@import "photoswipe/dist/photoswipe.css";

@import "tippy.js/dist/tippy.css";

/* Custom styles */
@import "./admin.css";

/* Global black background to prevent white flash on macOS bounce scroll */
html,
body {
  background-color: #000000 !important;
  background: #000000 !important;
}

/* Ensure the root app container also has black background */
#root {
  background-color: #000000 !important;
  min-height: 100vh;
}

/* Prevent white background on overscroll (macOS bounce effect) */
body::before {
  content: "";
  position: fixed;
  top: -100vh;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000000;
  z-index: -1;
}

body::after {
  content: "";
  position: fixed;
  bottom: -100vh;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000000;
  z-index: -1;
}

/* Navbar fixes */
.main-nav {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
}

/* Ensure dark navbar stays dark on all pages */
.main-nav.dark-mode {
  background-color: rgba(10, 10, 10, 0.905) !important;
}

.main-nav.dark-mode.transparent {
  background: transparent !important;
}

.main-nav.dark-mode:not(.transparent) {
  background-color: rgba(10, 10, 10, 0.905) !important;
  backdrop-filter: blur(10px);
}

/* Dark theme for feature cards */
.light-content .alt-features-item {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
  padding: 30px 20px !important;
  transition: all 0.3s ease !important;
}

.light-content .alt-features-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
  transform: translateY(-5px) !important;
}

.light-content .alt-features-title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

.light-content .alt-features-descr {
  color: rgba(255, 255, 255, 0.8) !important;
}

.light-content .alt-features-icon {
  color: #ffffff !important;
}

/* Service cards with blog-style design */
.service-card-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card-container:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.08);
}

.service-card-img {
  width: 100%;
  height: 250px !important;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-card-image {
  width: 100% !important;
  height: 250px !important;
  object-fit: cover !important;
  object-position: center !important;
  transition: all 0.4s ease;
  filter: grayscale(100%);
  display: block;
}

.service-card-container:hover .service-card-image {
  filter: grayscale(0%);
  transform: scale(1.05);
}

.service-card-content {
  padding: 30px 25px;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.service-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
  line-height: 1.3;
}

.service-card-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .service-card-img {
    height: 200px;
  }

  .service-card-content {
    padding: 25px 20px;
  }

  .service-card-title {
    font-size: 1.1rem;
  }

  .service-card-text {
    font-size: 0.9rem;
  }
}
