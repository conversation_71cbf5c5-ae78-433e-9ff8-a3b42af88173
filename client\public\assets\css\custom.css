.Typewriter,
.typewrite {
  display: inline-block;
}
.typewrite {
  margin-left: 10px;
}
.dark-mode .dark-white {
  color: white !important;
}
.dark-mode-logo {
  display: none;
}
.dark .dark-mode-logo {
  display: block;
}
.light-mode-logo {
  display: block;
}
.dark .light-mode-logo {
  display: none;
}
@media (min-width: 1025px) {
  .mn-has-sub:hover + *,
  .mn-has-sub + *:hover {
    display: block !important;
    z-index: 1;
  }
}

.fadeInText {
  animation: animationFadeText 0.5s linear 0s 1;
}
@keyframes animationFadeText {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.dark-mode .svg-shape {
  filter: invert(1) brightness(2); /* Inverts the colors and increases brightness */
}
.dark-mode .svg-dark {
  filter: invert(1) brightness(2); /* Inverts the colors and increases brightness */
}
.dark-mode .image-filter {
  filter: invert(1) brightness(2); /* Inverts the colors and increases brightness */
}
html {
  scroll-behavior: initial !important;
}

/* Blog content inherits normal text colors from theme */

/* Blog Content Code Styling - Only for code elements */
.blog-content code:not(pre code) {
  background: #000 !important;
  color: #00ff00 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace !important;
  font-size: 0.9em !important;
  font-weight: 500 !important;
}

.blog-content pre {
  background: #000 !important;
  color: #fff !important;
  padding: 20px !important;
  border-radius: 8px !important;
  margin: 16px 0 !important;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  overflow-x: auto !important;
  position: relative !important;
}

.blog-content pre code {
  background: transparent !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit !important;
  font-size: inherit !important;
}

/* Syntax Highlighting for Code Blocks Only */
.blog-content pre .hljs-keyword {
  color: #ff79c6 !important;
}

.blog-content pre .hljs-string {
  color: #f1fa8c !important;
}

.blog-content pre .hljs-number {
  color: #bd93f9 !important;
}

.blog-content pre .hljs-comment {
  color: #6272a4 !important;
  font-style: italic !important;
}

.blog-content pre .hljs-function {
  color: #50fa7b !important;
}

.blog-content pre .hljs-variable {
  color: #8be9fd !important;
}

.blog-content pre .hljs-title {
  color: #50fa7b !important;
}

.blog-content pre .hljs-attr {
  color: #50fa7b !important;
}

.blog-content pre .hljs-tag {
  color: #ff79c6 !important;
}

.blog-content pre .hljs-name {
  color: #ff79c6 !important;
}

.blog-content pre .hljs-selector-tag {
  color: #ff79c6 !important;
}

.blog-content pre .hljs-selector-class {
  color: #50fa7b !important;
}

.blog-content pre .hljs-selector-id {
  color: #50fa7b !important;
}

.blog-content pre .hljs-built_in {
  color: #8be9fd !important;
}

.blog-content pre .hljs-type {
  color: #8be9fd !important;
}

.blog-content pre .hljs-literal {
  color: #bd93f9 !important;
}

.blog-content pre .hljs-meta {
  color: #ff79c6 !important;
}

.blog-content pre .hljs-doctag {
  color: #ff79c6 !important;
}
a {
  cursor: pointer;
}
.mobile-on .desktop-nav {
  height: fit-content;
  max-height: 0px;
  display: block !important;
  overflow: scroll;
  transition: 0.6s;
}

@media (min-width: 1025px) {
  .mobile-on .mn-sub {
    display: none;
  }
}
@media (max-width: 1024px) {
  .mobile-on .js-opened .mn-sub.mobile-sub-active {
    max-height: 1000px;
  }
}
.dark-mode .form .form-tip-2 {
  color: var(--color-dark-mode-gray-1);
  background-color: var(--color-dark-2) !important;
}
.btn-mod.btn-w:hover,
.btn-mod.btn-w:focus {
  background: none !important;
}
@media (max-width: 500px) {
  .team-carousel .owl-prev.owl-prev-testimonial-1 {
    left: 21px;
  }
}

.mn-sub {
  transition: max-height 0.3s ease-in-out;
}

.mn-sub.open {
  max-height: 200px;
  overflow: auto !important;
  opacity: 1 !important;
}

html {
  scroll-behavior: smooth !important;
}

.grayscale {
  filter: grayscale(100%);
  transition: filter 0.3s ease; /* Smooth transition for hover */
}

.grayscale:hover {
  filter: none; /* Removes grayscale on hover */
}

.form-tip i {
  margin-right: 8px;
}

/* Dropdown menu styles */
.mn-sub {
  display: none;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  min-width: 200px;
  background: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease-in-out;
}

/* Show dropdown when active */
.js-opened .mn-sub.mobile-sub-active {
  display: block;
  opacity: 1;
  visibility: visible;
}

/* Rotate chevron icon */
.mi-chevron-down {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

/* Mobile styles */
@media (max-width: 1024px) {
  .mn-sub {
    position: static;
    box-shadow: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
  }

  .mobile-sub-active {
    max-height: 1000px;
  }
}

/* Desktop styles */
@media (min-width: 1025px) {
  .mn-sub {
    position: absolute;
    top: 100%;
    left: 0;
  }
}

/* Products dropdown specific styles */
.products-menu {
  position: relative;
}

.products-menu a {
  display: flex !important;
  align-items: center;
  gap: 8px;
}

/* Show dropdown on hover for desktop */
@media (min-width: 1025px) {
  .products-menu:hover .mn-sub {
    display: block;
    opacity: 1;
    visibility: visible;
  }
}

.products-menu .mn-sub {
  display: none;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 220px;
  background: rgba(35, 35, 35, 0.9927);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 8px 0;
}

.products-menu .mn-sub a {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
  padding: 8px 16px;
}

.products-menu .mn-sub a:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.products-menu .mn-sub .mn-group-title {
  color: rgba(255, 255, 255, 0.5);
  font-weight: normal;
  font-size: 0.9em;
  padding: 8px 16px;
  margin-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 8px;
}

.products-menu .mn-sub .mn-sub-multi ul {
  padding-left: 16px;
}

/* Mobile styles - keep click behavior for mobile */
@media (max-width: 1024px) {
  .products-menu .mn-sub {
    position: static;
    box-shadow: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
  }

  .products-menu .mn-sub.mobile-sub-active {
    max-height: 1000px;
  }

  .products-menu.js-opened .mn-sub.mobile-sub-active {
    display: block;
    opacity: 1;
    visibility: visible;
  }
}

.mi-chevron-down {
  display: inline-block;
  transition: transform 0.3s ease;
  font-size: 16px;
  line-height: 1;
}

.rotate-180 {
  transform: rotate(180deg);
}

/* Blog navigation spacing overrides */
.blog-nav-minimal {
  margin-top: 30px !important;
  margin-bottom: 10px !important;
}

.blog-comments-minimal {
  margin-bottom: 20px !important;
}

/* Filter status minimal spacing */
.filter-status-minimal {
  margin-top: 30px !important;
  margin-bottom: 10px !important;
}

/* BMS Section Image Grayscale Effect */
.bms-image-container {
  position: relative;
  width: 100%;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.bms-image-container img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20px 20px 0 0;
  filter: saturate(30%);
  transition: filter 0.3s ease;
}

.bms-image-container:hover img {
  filter: saturate(100%);
}

/* Blog hero section with minimal bottom padding */
.blog-hero-minimal {
  padding-bottom: 30px !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .blog-hero-minimal {
    padding-bottom: 20px !important;
  }
}
