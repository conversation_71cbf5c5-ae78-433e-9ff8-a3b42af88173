// client/src/pages/privacy-policy/page.jsx

import React from "react";
import { useTranslation } from "react-i18next";
import Header from "@/components/headers/Header";
import Footer from "@/components/footers/Footer";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import { menuItems } from "@/data/menu";

export default function PrivacyPolicyPage() {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";

  return (
    <>
      <UnifiedSEO
        title="Privacy Policy"
        description="DevSkills Privacy Policy - Learn how we collect, use, and protect your personal information when you use our services."
        slug="privacy-policy"
        type="website"
        keywords={["privacy policy", "data protection", "devskills", "gdpr"]}
      />

      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>

            <main id="main">
              {/* Page Header */}
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("privacy.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("privacy.lastUpdated")}:{" "}
                        {new Date().toLocaleDateString(currentLanguage, {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="spacer-small"></div>
                </div>
              </section>

              {/* Privacy Policy Content */}
              <section className="page-section bg-dark-1 light-content">
                <div className="container position-relative">
                  <div className="row">
                    <div className="col-lg-8 offset-lg-2">
                      {/* Introduction */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.intro.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("privacy.intro.text1")}
                        </p>
                        <p className="text-gray">{t("privacy.intro.text2")}</p>
                      </div>

                      {/* Information We Collect */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.collect.title")}
                        </h2>

                        <h3 className="h4 mb-20 text-white">
                          {t("privacy.collect.personal.title")}
                        </h3>
                        <p className="text-gray mb-20">
                          {t("privacy.collect.personal.text")}
                        </p>
                        <ul className="text-gray mb-30">
                          <li>{t("privacy.collect.personal.item1")}</li>
                          <li>{t("privacy.collect.personal.item2")}</li>
                          <li>{t("privacy.collect.personal.item3")}</li>
                          <li>{t("privacy.collect.personal.item4")}</li>
                        </ul>

                        <h3 className="h4 mb-20 text-white">
                          {t("privacy.collect.usage.title")}
                        </h3>
                        <p className="text-gray mb-20">
                          {t("privacy.collect.usage.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("privacy.collect.usage.item1")}</li>
                          <li>{t("privacy.collect.usage.item2")}</li>
                          <li>{t("privacy.collect.usage.item3")}</li>
                          <li>{t("privacy.collect.usage.item4")}</li>
                        </ul>
                      </div>

                      {/* How We Use Information */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.use.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("privacy.use.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("privacy.use.item1")}</li>
                          <li>{t("privacy.use.item2")}</li>
                          <li>{t("privacy.use.item3")}</li>
                          <li>{t("privacy.use.item4")}</li>
                          <li>{t("privacy.use.item5")}</li>
                          <li>{t("privacy.use.item6")}</li>
                        </ul>
                      </div>

                      {/* Information Sharing */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.sharing.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("privacy.sharing.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("privacy.sharing.item1")}</li>
                          <li>{t("privacy.sharing.item2")}</li>
                          <li>{t("privacy.sharing.item3")}</li>
                          <li>{t("privacy.sharing.item4")}</li>
                        </ul>
                      </div>

                      {/* Data Security */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.security.title")}
                        </h2>
                        <p className="text-gray">
                          {t("privacy.security.text")}
                        </p>
                      </div>

                      {/* Your Rights */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.rights.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("privacy.rights.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("privacy.rights.item1")}</li>
                          <li>{t("privacy.rights.item2")}</li>
                          <li>{t("privacy.rights.item3")}</li>
                          <li>{t("privacy.rights.item4")}</li>
                          <li>{t("privacy.rights.item5")}</li>
                          <li>{t("privacy.rights.item6")}</li>
                        </ul>
                      </div>

                      {/* Cookies */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.cookies.title")}
                        </h2>
                        <p className="text-gray">{t("privacy.cookies.text")}</p>
                      </div>

                      {/* Contact Information */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.contact.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("privacy.contact.text")}
                        </p>
                        <div className="text-gray">
                          <p>
                            <strong>DevSkills OÜ</strong>
                          </p>
                          <p>{t("privacy.contact.address")}</p>
                          <p>{t("privacy.contact.email")}</p>
                          <p>{t("privacy.contact.phone")}</p>
                        </div>
                      </div>

                      {/* Updates */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("privacy.updates.title")}
                        </h2>
                        <p className="text-gray">{t("privacy.updates.text")}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </main>

            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>
        </div>
      </div>
    </>
  );
}
