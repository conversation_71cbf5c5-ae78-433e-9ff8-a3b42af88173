import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import { getPageSEOData } from "@/utils/seoHelpers";

import Header from "@/components/headers/Header";
import AnimatedText from "@/components/common/AnimatedText";
import Footer from "@/components/footers/Footer";
import { trackEvent } from "@/utils/analytics";
import { menuItems } from "@/data/menu";
import { productsAPI, categoriesAPI } from "@/utils/api";

export default function ElegantWebstorePageDark() {
  const { t: translate, i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";

  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    loadProducts();
    loadCategories();
  }, [currentLanguage]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const { response, data } = await productsAPI.getProducts({
        language: currentLanguage,
        status: "published",
      });

      if (response.ok && data.success) {
        console.log("Loaded products:", data.products); // Debug log
        setProducts(data.products);
        setFilteredProducts(data.products);
      } else {
        setError("Failed to load products");
      }
    } catch (err) {
      console.error("Error loading products:", err);
      setError("Error loading products");
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { response, data } = await categoriesAPI.getCategories();

      if (response.ok && data.success) {
        setCategories(data.data || data.categories);
      }
    } catch (err) {
      console.error("Error loading categories:", err);
    }
  };

  const handleCategoryFilter = (categorySlug) => {
    setSelectedCategory(categorySlug);

    if (categorySlug === "all") {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter((product) =>
        product.categories.some((cat) => cat.category.slug === categorySlug)
      );
      setFilteredProducts(filtered);
    }
  };

  const handleProductClick = (product) => {
    trackEvent("product_view", {
      product_id: product.id,
      product_title: product.title,
      language: currentLanguage,
      source: "webstore_listing",
    });
  };

  const formatPrice = (price) => {
    if (!price) return null;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "EUR",
    }).format(price);
  };

  const seoData = getPageSEOData("webstore");

  return (
    <>
      <UnifiedSEO
        title={translate("webstore.meta.title") || seoData.title}
        description={
          translate("webstore.meta.description") || seoData.description
        }
        slug="webstore"
        type="website"
        image="https://devskills.ee/webstore.jpg"
        schema={seoData.schema}
        keywords={
          translate("webstore.meta.keywords")
            ? translate("webstore.meta.keywords")
                .split(",")
                .map((k) => k.trim())
            : seoData.keywords
        }
      />
      <style>
        {`
          .btn-mod:focus,
          .btn-mod:active {
            outline: none !important;
            box-shadow: none !important;
          }
          .btn-mod.btn-w:focus,
          .btn-mod.btn-w:active {
            background: #fff !important;
            color: var(--color-dark-1) !important;
            border-color: #fff !important;
          }
        `}
      </style>

      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>

            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/3.jpg)",
                }}
              >
                <div className="container position-relative pt-30 pt-sm-50">
                  <div className="text-center">
                    <div className="row">
                      <div className="col-md-8 offset-md-2">
                        <h1 className="hs-title-1 mb-20">
                          <span
                            className="wow charsAnimIn"
                            data-splitting="chars"
                          >
                            <AnimatedText text={translate("webstore.title")} />
                          </span>
                        </h1>
                        <div className="row">
                          <div className="col-lg-8 offset-lg-2">
                            <p
                              className="section-descr mb-0 wow fadeIn"
                              data-wow-delay="0.2s"
                            >
                              {translate("webstore.description")}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              <section className="page-section bg-dark-1 light-content">
                <div className="container position-relative">
                  {/* Category Filter */}
                  <div className="row mb-60 mb-xs-40">
                    <div className="col-md-12">
                      <div className="works-filter works-filter-elegant text-center">
                        <a
                          onClick={() => handleCategoryFilter("all")}
                          className={`filter ${
                            selectedCategory === "all" ? "active" : ""
                          }`}
                          style={{ cursor: "pointer" }}
                        >
                          {translate("webstore.filter.all")}
                        </a>
                        {categories.map((category) => (
                          <a
                            key={category.id}
                            onClick={() => handleCategoryFilter(category.slug)}
                            className={`filter ${
                              selectedCategory === category.slug ? "active" : ""
                            }`}
                            style={{ cursor: "pointer" }}
                          >
                            {category.name}
                          </a>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Products Grid */}
                  {loading ? (
                    <div className="text-center py-5">
                      <div
                        className="spinner-border text-primary"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </div>
                  ) : error ? (
                    <div
                      className="alert alert-danger text-center"
                      role="alert"
                    >
                      {error}
                    </div>
                  ) : (
                    <div className="row mt-n50">
                      {filteredProducts.length > 0 ? (
                        filteredProducts.map((product, index) => (
                          <div
                            key={product.id}
                            className="post-prev col-md-6 col-lg-4 mt-50"
                          >
                            <div className="post-prev-container d-flex flex-column h-100">
                              <div className="post-prev-img">
                                <Link
                                  to={`/${currentLanguage}/webstore-single/${(
                                    product.slug || product.id
                                  ).replace(/^\/+/, "")}`}
                                  onClick={() => handleProductClick(product)}
                                >
                                  <img
                                    src={
                                      product.featuredImage
                                        ? `${
                                            import.meta.env.VITE_API_BASE_URL ||
                                            "http://localhost:4004"
                                          }/uploads/product-images/${
                                            product.featuredImage
                                          }`
                                        : "/assets/images/demo-elegant/blog/1.jpg"
                                    }
                                    width={650}
                                    height={412}
                                    alt={
                                      product.featuredImageAlt || product.title
                                    }
                                    className="wow scaleOutIn"
                                    data-wow-duration="1.2s"
                                  />
                                </Link>
                              </div>

                              {/* Content area that can grow */}
                              <div className="flex-grow-1 d-flex flex-column">
                                <h4 className="post-prev-title">
                                  <Link
                                    to={`/${currentLanguage}/webstore-single/${(
                                      product.slug || product.id
                                    ).replace(/^\/+/, "")}`}
                                    onClick={() => handleProductClick(product)}
                                  >
                                    {product.title}
                                  </Link>
                                </h4>

                                <div className="post-prev-text flex-grow-1">
                                  {product.excerpt}
                                </div>

                                {/* Pricing and button section - always at bottom */}
                                <div className="mt-auto">
                                  {/* Pricing Information */}
                                  <div className="product-pricing mb-30 text-center">
                                    {product.whitelabelPrice && (
                                      <div className="price-row mb-10">
                                        <span className="text-gray">
                                          {translate(
                                            "webstore.whitelabel_price"
                                          )}
                                          :{" "}
                                        </span>
                                        <span className="price-value text-white h5 d-inline">
                                          €{product.whitelabelPrice}
                                        </span>
                                      </div>
                                    )}
                                    {product.subscriptionPrice && (
                                      <div className="price-row mb-10">
                                        <span className="text-gray">
                                          {translate(
                                            "webstore.subscription_price"
                                          )}
                                          :{" "}
                                        </span>
                                        <span className="price-value text-white h5 d-inline">
                                          €{product.subscriptionPrice}/mo
                                        </span>
                                      </div>
                                    )}
                                  </div>

                                  {/* Demo Button */}
                                  <div className="text-center">
                                    {product.demoUrl && (
                                      <a
                                        href={product.demoUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="btn btn-mod btn-medium btn-circle btn-hover-anim btn-w"
                                        onClick={(e) => {
                                          // Remove focus after click to prevent focus state styling
                                          setTimeout(() => {
                                            e.target.blur();
                                          }, 100);

                                          trackEvent("demo_click", {
                                            product_id: product.id,
                                            product_title: product.title,
                                            language: currentLanguage,
                                            source: "webstore_listing",
                                          });
                                        }}
                                      >
                                        <span>
                                          {translate("webstore.view_demo")}
                                        </span>
                                      </a>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="col-12 text-center">
                          <p className="text-muted">
                            {translate("webstore.no_products")}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </section>
            </main>

            <footer className="footer-1 bg-dark-2 light-content">
              <Footer />
            </footer>
          </div>
        </div>
      </div>
    </>
  );
}
