// client/src/pages/terms-conditions/page.jsx

import React from "react";
import { useTranslation } from "react-i18next";
import Header from "@/components/headers/Header";
import Footer from "@/components/footers/Footer";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import { menuItems } from "@/data/menu";

const dark = true;

export default function TermsConditionsPage() {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || "en";

  return (
    <>
      <UnifiedSEO
        title="Terms and Conditions"
        description="DevSkills Terms and Conditions - Legal terms governing the use of our software development services and Business Comanager platform."
        slug="terms-conditions"
        type="website"
        keywords={[
          "terms and conditions",
          "legal",
          "devskills",
          "service agreement",
        ]}
      />

      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>

            <main id="main">
              {/* Page Header */}
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("terms.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("terms.lastUpdated")}:{" "}
                        {new Date().toLocaleDateString(currentLanguage, {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="spacer-small"></div>
                </div>
              </section>

              {/* Terms Content */}
              <section className="page-section bg-dark-1 light-content">
                <div className="container position-relative">
                  <div className="row">
                    <div className="col-lg-8 offset-lg-2">
                      {/* Introduction */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.agreement.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.agreement.text1")}
                        </p>
                        <p className="text-gray">
                          {t("terms.agreement.text2")}
                        </p>
                      </div>

                      {/* Services */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.services.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.services.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("terms.services.item1")}</li>
                          <li>{t("terms.services.item2")}</li>
                          <li>{t("terms.services.item3")}</li>
                          <li>{t("terms.services.item4")}</li>
                          <li>{t("terms.services.item5")}</li>
                          <li>{t("terms.services.item6")}</li>
                        </ul>
                      </div>

                      {/* User Responsibilities */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.responsibilities.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.responsibilities.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("terms.responsibilities.item1")}</li>
                          <li>{t("terms.responsibilities.item2")}</li>
                          <li>{t("terms.responsibilities.item3")}</li>
                          <li>{t("terms.responsibilities.item4")}</li>
                          <li>{t("terms.responsibilities.item5")}</li>
                          <li>{t("terms.responsibilities.item6")}</li>
                        </ul>
                      </div>

                      {/* Payment Terms */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.payment.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.payment.text")}
                        </p>
                        <ul className="text-gray mb-20">
                          <li>{t("terms.payment.item1")}</li>
                          <li>{t("terms.payment.item2")}</li>
                          <li>{t("terms.payment.item3")}</li>
                          <li>{t("terms.payment.item4")}</li>
                          <li>{t("terms.payment.item5")}</li>
                        </ul>
                        <p className="text-gray">{t("terms.payment.text2")}</p>
                      </div>

                      {/* Intellectual Property */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.ip.title")}
                        </h2>
                        <p className="text-gray mb-20">{t("terms.ip.our")}</p>
                        <p className="text-gray mb-20">
                          {t("terms.ip.custom")}
                          will be specified in individual project agreements.
                        </p>
                        <p className="text-gray">{t("terms.ip.client")}</p>
                      </div>

                      {/* Confidentiality */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.confidentiality.title")}
                        </h2>
                        <p className="text-gray">
                          {t("terms.confidentiality.text")}
                        </p>
                      </div>

                      {/* Limitation of Liability */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.liability.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.liability.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("terms.liability.item1")}</li>
                          <li>{t("terms.liability.item2")}</li>
                          <li>{t("terms.liability.item3")}</li>
                          <li>{t("terms.liability.item4")}</li>
                        </ul>
                      </div>

                      {/* Warranties */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.warranties.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.warranties.text")}
                        </p>
                        <ul className="text-gray">
                          <li>{t("terms.warranties.item1")}</li>
                          <li>{t("terms.warranties.item2")}</li>
                          <li>{t("terms.warranties.item3")}</li>
                        </ul>
                      </div>

                      {/* Termination */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.termination.title")}
                        </h2>
                        <p className="text-gray">
                          {t("terms.termination.text")}
                        </p>
                      </div>

                      {/* Governing Law */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.governing.title")}
                        </h2>
                        <p className="text-gray">{t("terms.governing.text")}</p>
                      </div>

                      {/* Contact Information */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.contact.title")}
                        </h2>
                        <p className="text-gray mb-20">
                          {t("terms.contact.text")}
                        </p>
                        <div className="text-gray">
                          <p>
                            <strong>DevSkills OÜ</strong>
                          </p>
                          <p>{t("terms.contact.address")}</p>
                          <p>{t("terms.contact.email")}</p>
                          <p>{t("terms.contact.phone")}</p>
                        </div>
                      </div>

                      {/* Changes to Terms */}
                      <div className="mb-50">
                        <h2 className="section-title-small mb-30">
                          {t("terms.changes.title")}
                        </h2>
                        <p className="text-gray">{t("terms.changes.text")}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </main>

            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>
        </div>
      </div>
    </>
  );
}
