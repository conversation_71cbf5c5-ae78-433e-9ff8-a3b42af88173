const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/syntaxHighlighting-DKW5YAVq.js","assets/vendor-react-BE9lZbv0.js","assets/vendor-misc-BUjjPnRU.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css","assets/vendor-gallery-BKyWYjF6.js","assets/vendor-admin-DSFDn6-z.js","assets/components-layout-vmRMeu3w.js","assets/components-common-CR_Ate1J.js","assets/vendor-i18n-B6YYbpZm.js","assets/components-home-D8-KZiGy.js","assets/vendor-utils-t--hEgTQ.js","assets/components-layout-LOVgDiEq.css"])))=>i.map(i=>d[i]);
import{j as e,u as se,r as c,L as G,f as Ce,g as pe,b as le}from"./vendor-react-BE9lZbv0.js";import{H as J,F as ie}from"./components-layout-vmRMeu3w.js";import{H as Ie,a as Ae,b as ue,P as De,M as Te}from"./components-home-D8-KZiGy.js";import{U as ce,P as Ee,c as _e,d as we,A as Fe,M as ke,e as Le,b as me,t as $e,f as Ue,g as ve,R as Oe,h as Me,C as Be,F as ze,W as Re,S as te,i as Se,j as ee,k as z,l as je,T as Pe,m as We,n as He,o as qe,q as Ge,H as Ve,r as Je,s as Ke,u as Ye,v as be}from"./components-common-CR_Ate1J.js";const Qe="modulepreload",Xe=function(s){return"/"+s},ye={},Ze=function(m,N,f){let l=Promise.resolve();if(N&&N.length>0){document.getElementsByTagName("link");const b=document.querySelector("meta[property=csp-nonce]"),u=(b==null?void 0:b.nonce)||(b==null?void 0:b.getAttribute("nonce"));l=Promise.allSettled(N.map(w=>{if(w=Xe(w),w in ye)return;ye[w]=!0;const g=w.endsWith(".css"),y=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${w}"]${y}`))return;const v=document.createElement("link");if(v.rel=g?"stylesheet":Qe,g||(v.as="script"),v.crossOrigin="",v.href=w,u&&v.setAttribute("nonce",u),document.head.appendChild(v),g)return new Promise((S,C)=>{v.addEventListener("load",S),v.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${w}`)))})}))}function n(b){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=b,window.dispatchEvent(u),!u.defaultPrevented)throw b}return l.then(b=>{for(const u of b||[])u.status==="rejected"&&n(u.reason);return m().catch(n)})},K=[{href:"/",text:"Home"},{href:"/about",text:"About"},{href:"/webstore",text:"Webstore"},{href:"/services",text:"Services"},{href:"/blog",text:"Blog"},{href:"/contact",text:"Contact"}],$={name:"DevSkills",fullName:"DevSkills OÜ",alternateName:"DevSkills Development Studio",description:"Professional software development services and custom solutions",url:"https://devskills.ee",address:{streetAddress:"Tornimäe tn 7",addressLocality:"Tallinn",postalCode:"10145",addressCountry:"EE"},geo:{latitude:59.437,longitude:24.7536},contactPoint:{telephone:"+372 5628 2038",contactType:"customer service",availableLanguage:["English","Estonian","Finnish","German","Swedish"]},openingHours:{dayOfWeek:["Monday","Tuesday","Wednesday","Thursday","Friday"],opens:"08:00",closes:"17:00"},services:["Custom Software Development","Web Development","AI Solutions","White Label Software","Blockchain Development","Mobile Applications Development","Backend Development","Business Management Systems"],socialMedia:["https://www.facebook.com/devskillsee","https://www.linkedin.com/company/devskills-development-studio","https://twitter.com/DevSkillsEE"]},oe=()=>({"@type":"LocalBusiness","@id":`${$.url}/#organization`,name:$.fullName,alternateName:$.alternateName,url:$.url,description:$.description,address:{"@type":"PostalAddress",...$.address},geo:{"@type":"GeoCoordinates",...$.geo},contactPoint:{"@type":"ContactPoint",...$.contactPoint},openingHoursSpecification:{"@type":"OpeningHoursSpecification",...$.openingHours},serviceArea:{"@type":"Country",name:"Estonia"},hasOfferCatalog:{"@type":"OfferCatalog",name:"Software Development Services",itemListElement:$.services.map(s=>({"@type":"Offer",itemOffered:{"@type":"Service",name:s}}))},logo:{"@type":"ImageObject",url:`${$.url}/logo.png`,width:"180",height:"60"},sameAs:$.socialMedia}),es=()=>({"@type":"WebSite","@id":`${$.url}/#website`,name:$.name,alternateName:$.alternateName,url:$.url,description:$.description,publisher:{"@id":`${$.url}/#organization`},potentialAction:{"@type":"SearchAction",target:`${$.url}/search?q={search_term_string}`,"query-input":"required name=search_term_string"},inLanguage:["en","et","fi","de","sv"]}),ss=s=>({"@type":"Article",headline:s.title,description:s.excerpt||s.description,image:s.featuredImage||`${$.url}/home.jpg`,author:{"@type":"Person",name:s.author||$.name},publisher:{"@type":"Organization",name:$.name,logo:{"@type":"ImageObject",url:`${$.url}/logo.png`}},datePublished:s.publishedAt,dateModified:s.modifiedAt||s.publishedAt,mainEntityOfPage:{"@type":"WebPage","@id":s.url}}),as=s=>({"@type":"SoftwareApplication",name:s.title,description:s.description,applicationCategory:"BusinessApplication",operatingSystem:"Web",publisher:{"@id":`${$.url}/#organization`},offers:{"@type":"Offer",price:s.price||"Contact for pricing",priceCurrency:"EUR",availability:"https://schema.org/InStock"},aggregateRating:s.rating?{"@type":"AggregateRating",ratingValue:s.rating.value,ratingCount:s.rating.count}:void 0}),ts=s=>({"@type":"BreadcrumbList",itemListElement:s.map((m,N)=>({"@type":"ListItem",position:N+1,item:{"@id":m.url,name:m.name}}))}),Ne=(s,m={},N="en")=>{const f=["software development","custom software","web development","AI solutions","estonia","tallinn"],l={homepage:{title:"Professional Software Development Services",description:"DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.",keywords:[...f,"white label software","business comanager"],schema:[oe(),es()]},about:{title:"About DevSkills - Professional Development Team",description:"Learn about DevSkills, our mission, values, and the professional team behind our innovative software development services and custom solutions.",keywords:[...f,"about","team","company"],schema:[oe()]},services:{title:"Software Development Services - Custom Solutions",description:"Comprehensive software development services including custom software, web development, AI solutions, blockchain development, and mobile applications.",keywords:[...f,"services","custom development","blockchain","mobile apps"],schema:[oe()]},webstore:{title:"White Label Software Solutions - DevSkills Webstore",description:"Explore our white-label software solutions including Business Comanager and other professional business management tools.",keywords:[...f,"white label","business software","webstore"],schema:[oe()]},blog:{title:"Software Development Blog - DevSkills Insights",description:"Latest insights, tutorials, and news about software development, AI, web technologies, and business solutions from DevSkills experts.",keywords:[...f,"blog","tutorials","insights","technology news"],schema:[oe()]},contact:{title:"Contact DevSkills - Get Your Custom Software Quote",description:"Contact DevSkills for professional software development services. Get a quote for your custom software, web development, or AI solution project.",keywords:[...f,"contact","quote","consultation"],schema:[oe()]}};return m.title&&m.description?{title:m.title,description:m.description,keywords:m.keywords||f,schema:m.schema||[oe()]}:l[s]||l.homepage};function _s(){const s=Ne("homepage");return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:s.title,description:s.description,slug:"",type:"website",image:"https://devskills.ee/home.jpg",imageAlt:"DevSkills Development Studio - Professional Software Development",schema:s.schema,keywords:s.keywords}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx(Ee,{className:"home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection",style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsx(Ie,{})}),e.jsx(Ae,{dark:!0})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})," "]})})]})}function ls(){const{t:s,i18n:m}=se(),N=m.language||"et",[f,l]=c.useState([]),[n,b]=c.useState([]),[u,w]=c.useState([]),[g,y]=c.useState("all"),[v,S]=c.useState(!0),[C,T]=c.useState("");c.useEffect(()=>{h(),A()},[N]);const h=async()=>{try{S(!0);const{response:r,data:a}=await _e.getProducts({language:N,status:"published"});r.ok&&a.success?(console.log("Loaded products:",a.products),l(a.products),b(a.products)):T("Failed to load products")}catch(r){console.error("Error loading products:",r),T("Error loading products")}finally{S(!1)}},A=async()=>{try{const{response:r,data:a}=await we.getCategories();r.ok&&a.success&&w(a.data||a.categories)}catch(r){console.error("Error loading categories:",r)}},i=r=>{if(y(r),r==="all")b(f);else{const a=f.filter(j=>j.categories.some(o=>o.category.slug===r));b(a)}},p=r=>{ue("product_view",{product_id:r.id,product_title:r.title,language:N,source:"webstore_listing"})},t=Ne("webstore");return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:s("webstore.meta.title")||t.title,description:s("webstore.meta.description")||t.description,slug:"webstore",type:"website",image:"https://devskills.ee/webstore.jpg",schema:t.schema,keywords:s("webstore.meta.keywords")?s("webstore.meta.keywords").split(",").map(r=>r.trim()):t.keywords}),e.jsx("style",{children:`
          .btn-mod:focus,
          .btn-mod:active {
            outline: none !important;
            box-shadow: none !important;
          }
          .btn-mod.btn-w:focus,
          .btn-mod.btn-w:active {
            background: #fff !important;
            color: var(--color-dark-1) !important;
            border-color: #fff !important;
          }
        `}),e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/3.jpg)"},children:e.jsx("div",{className:"container position-relative pt-30 pt-sm-50",children:e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-md-8 offset-md-2",children:[e.jsx("h1",{className:"hs-title-1 mb-20",children:e.jsx("span",{className:"wow charsAnimIn","data-splitting":"chars",children:e.jsx(Fe,{text:s("webstore.title")})})}),e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-8 offset-lg-2",children:e.jsx("p",{className:"section-descr mb-0 wow fadeIn","data-wow-delay":"0.2s",children:s("webstore.description")})})})]})})})})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsxs("div",{className:"container position-relative",children:[e.jsx("div",{className:"row mb-60 mb-xs-40",children:e.jsx("div",{className:"col-md-12",children:e.jsxs("div",{className:"works-filter works-filter-elegant text-center",children:[e.jsx("a",{onClick:()=>i("all"),className:`filter ${g==="all"?"active":""}`,style:{cursor:"pointer"},children:s("webstore.filter.all")}),u.map(r=>e.jsx("a",{onClick:()=>i(r.slug),className:`filter ${g===r.slug?"active":""}`,style:{cursor:"pointer"},children:r.name},r.id))]})})}),v?e.jsx("div",{className:"text-center py-5",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})}):C?e.jsx("div",{className:"alert alert-danger text-center",role:"alert",children:C}):e.jsx("div",{className:"row mt-n50",children:n.length>0?n.map((r,a)=>e.jsx("div",{className:"post-prev col-md-6 col-lg-4 mt-50",children:e.jsxs("div",{className:"post-prev-container d-flex flex-column h-100",children:[e.jsx("div",{className:"post-prev-img",children:e.jsx(G,{to:`/${N}/webstore-single/${(r.slug||r.id).replace(/^\/+/,"")}`,onClick:()=>p(r),children:e.jsx("img",{src:r.featuredImage?`http://localhost:4004/uploads/product-images/${r.featuredImage}`:"/assets/images/demo-elegant/blog/1.jpg",width:650,height:412,alt:r.featuredImageAlt||r.title,className:"wow scaleOutIn","data-wow-duration":"1.2s"})})}),e.jsxs("div",{className:"flex-grow-1 d-flex flex-column",children:[e.jsx("h4",{className:"post-prev-title",children:e.jsx(G,{to:`/${N}/webstore-single/${(r.slug||r.id).replace(/^\/+/,"")}`,onClick:()=>p(r),children:r.title})}),e.jsx("div",{className:"post-prev-text flex-grow-1",children:r.excerpt}),e.jsxs("div",{className:"mt-auto",children:[e.jsxs("div",{className:"product-pricing mb-30 text-center",children:[r.whitelabelPrice&&e.jsxs("div",{className:"price-row mb-10",children:[e.jsxs("span",{className:"text-gray",children:[s("webstore.whitelabel_price"),":"," "]}),e.jsxs("span",{className:"price-value text-white h5 d-inline",children:["€",r.whitelabelPrice]})]}),r.subscriptionPrice&&e.jsxs("div",{className:"price-row mb-10",children:[e.jsxs("span",{className:"text-gray",children:[s("webstore.subscription_price"),":"," "]}),e.jsxs("span",{className:"price-value text-white h5 d-inline",children:["€",r.subscriptionPrice,"/mo"]})]})]}),e.jsx("div",{className:"text-center",children:r.demoUrl&&e.jsx("a",{href:r.demoUrl,target:"_blank",rel:"noopener noreferrer",className:"btn btn-mod btn-medium btn-circle btn-hover-anim btn-w",onClick:j=>{setTimeout(()=>{j.target.blur()},100),ue("demo_click",{product_id:r.id,product_title:r.title,language:N,source:"webstore_listing"})},children:e.jsx("span",{children:s("webstore.view_demo")})})})]})]})]})},r.id)):e.jsx("div",{className:"col-12 text-center",children:e.jsx("p",{className:"text-muted",children:s("webstore.no_products")})})})]})})]}),e.jsx("footer",{className:"footer-1 bg-dark-2 light-content",children:e.jsx(ie,{})})]})})})]})}const Fs=Object.freeze(Object.defineProperty({__proto__:null,default:ls},Symbol.toStringTag,{value:"Module"})),is={title:"Elegant Portfolio Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template",description:"Resonance &mdash; One & Multi Page Reactjs Creative Template"};function ns(){return e.jsxs(e.Fragment,{children:[e.jsx(ke,{meta:is}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/section-bg-1.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:"PORTFOLIO"}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-tiny mb-0 opacity-075",children:"Explore captivating web design solutions."})})})]})}),e.jsx("section",{className:"page-section pb-0  scrollSpysection  bg-dark-1 light-content ",id:"portfolio",children:e.jsx(De,{})}),e.jsx("div",{className:"page-section overflow-hidden",children:e.jsx(Te,{})}),e.jsx("section",{className:"page-section bg-dark-1 light-content pt-0",children:e.jsx("div",{className:"container position-relative",children:e.jsx("div",{className:"row text-center wow fadeInUp",children:e.jsxs("div",{className:"col-md-10 offset-md-1 col-lg-6 offset-lg-3",children:[e.jsx("p",{className:"section-descr mb-50 mb-sm-30",children:"The power of design help us to solve complex problems and cultivate business solutions."}),e.jsx("div",{className:"local-scroll",children:e.jsx(G,{to:"/elegant-contact",className:"btn btn-mod btn-large btn-w btn-circle btn-hover-anim",children:e.jsx("span",{children:"Contact us"})})})]})})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})," "]})})]})}const Ls=Object.freeze(Object.defineProperty({__proto__:null,default:ns},Symbol.toStringTag,{value:"Module"}));function rs(){var he,ne;const{t:s,i18n:m}=se(),N=m.language||"et",[f,l]=Ce(),[n,b]=c.useState([]),[u,w]=c.useState(!0),[g,y]=c.useState(1),[v,S]=c.useState(1),[C,T]=c.useState([]),[h,A]=c.useState([]),[i,p]=c.useState([]),t=f.get("category"),r=f.get("tag"),a=f.get("search");c.useEffect(()=>{(async()=>{var E,Y;try{w(!0);const V={language:N,page:g,limit:9};t&&(V.category=t),r&&(V.tag=r),a&&(V.search=a);const H=await me.getBlogPosts(V);if(H.response.ok&&H.data){const d=((E=H.data.data)==null?void 0:E.data)||H.data.data||[],_=((Y=H.data.data)==null?void 0:Y.pagination)||H.data.pagination;console.log("Blog listing API response:",H.data),console.log("Posts array:",d),console.log("Pagination:",_),b(Array.isArray(d)?d:[]),S((_==null?void 0:_.totalPages)||1)}else console.error("Failed to fetch blog posts:",H.response.status),b([]);try{const d=await we.getCategories();d.response.ok&&d.data&&T(d.data.data||[])}catch(d){console.error("Error fetching categories:",d)}try{const d=await $e.getTags();d.response.ok&&d.data&&A(d.data.data||[])}catch(d){console.error("Error fetching tags:",d)}try{const d=await Ue.getArchive();d.response.ok&&d.data&&p(d.data.archive||[])}catch(d){console.error("Error fetching archive:",d)}}catch(V){console.error("Error fetching data:",V),b([])}finally{w(!1)}})()},[N,g,t,r,a]);const j=(k,E)=>{var V,H,d;const Y=(V=k.translations)==null?void 0:V.find(_=>_.language===N);return(Y==null?void 0:Y[E])||((d=(H=k.translations)==null?void 0:H.find(_=>_.language==="en"))==null?void 0:d[E])||""},o=k=>{const E=new URLSearchParams(f);k?E.set("category",k):E.delete("category"),E.delete("page"),l(E),y(1)},F=k=>{const E=new URLSearchParams(f);k?E.set("tag",k):E.delete("tag"),E.delete("page"),l(E),y(1)},M=()=>{l({}),y(1)},W=Ne("blog");return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:s("blog.page.title")||W.title,description:s("blog.page.description")||W.description,slug:"blog",type:"website",image:"https://devskills.ee/blog.jpg",schema:W.schema,keywords:s("blog.page.keywords",{returnObjects:!0})||W.keywords}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:`page-section bg-dark-alpha-50 light-content ${t||r||a?"blog-hero-minimal":""}`,style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:s("blog.title")}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-tiny mb-0 opacity-075",children:s("blog.subtitle")})})}),(t||r||a)&&e.jsx("div",{className:"filter-status-minimal",children:e.jsxs("div",{className:"d-flex flex-wrap justify-content-center align-items-center gap-3",children:[e.jsx("span",{className:"text-white-opacity",children:"Filtered by:"}),t&&e.jsxs("span",{className:"badge bg-primary",children:["Category:"," ",(he=C.find(k=>k.slug===t))==null?void 0:he.name]}),r&&e.jsxs("span",{className:"badge bg-secondary",children:["Tag: ",(ne=h.find(k=>k.slug===r))==null?void 0:ne.name]}),a&&e.jsxs("span",{className:"badge bg-info",children:['Search: "',a,'"']}),e.jsxs("button",{onClick:M,className:"link-hover-anim link-circle-1 align-middle","data-link-animate":"y",children:[e.jsx("span",{className:"link-strong link-strong-unhovered",children:"Clear Filters"}),e.jsx("span",{className:"link-strong link-strong-hovered","aria-hidden":"true",children:"Clear Filters"})]})]})})]})}),e.jsxs(e.Fragment,{children:[e.jsx("section",{className:"page-section bg-dark-1 light-content",id:"blog",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"row mt-n50 mb-50 wow fadeInUp","data-wow-offset":0,children:[u&&e.jsx("div",{className:"col-12 text-center",children:e.jsx("div",{className:"text-gray",children:s("blog.loading")})}),!u&&n.length===0&&e.jsx("div",{className:"col-12 text-center",children:e.jsx("div",{className:"text-gray",children:s("blog.empty")})}),!u&&Array.isArray(n)&&n.map(k=>{var E;return e.jsx("div",{className:"post-prev col-md-6 col-lg-4 mt-50",children:e.jsxs("div",{className:"post-prev-container",children:[e.jsx("div",{className:"post-prev-img",children:e.jsx(G,{to:`/blog-single/${k.slug}`,children:e.jsx("img",{src:k.featuredImage||"/assets/images/demo-elegant/blog/1.jpg",width:607,height:358,alt:j(k,"title")})})}),e.jsx("h3",{className:"post-prev-title",children:e.jsx(G,{to:`/blog-single/${k.slug}`,children:j(k,"title")})}),e.jsx("div",{className:"post-prev-text",children:j(k,"excerpt")}),e.jsxs("div",{className:"post-prev-info clearfix",children:[e.jsxs("div",{className:"float-start",children:[e.jsx("a",{href:"#",className:"icon-author",children:e.jsx("i",{className:"mi-user size-14 align-middle"})}),e.jsx("a",{href:"#",children:((E=k.author)==null?void 0:E.name)||"DevSkills Team"})]}),e.jsxs("div",{className:"float-end",children:[e.jsx("i",{className:"mi-calendar size-14 align-middle"}),e.jsx("a",{href:"#",children:new Date(k.publishedAt||k.createdAt).toLocaleDateString()})]})]})]})},k.id)})]}),e.jsx(Le,{currentPage:g,totalPages:v,onPageChange:y})]})}),e.jsx("hr",{className:"mt-0 mb-0 white"}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container relative",children:e.jsxs("div",{className:"row mt-n60",children:[e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:s("blog.categories")}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-menu",children:C.map(k=>{var E;return e.jsxs("li",{children:[e.jsx("a",{href:"#",title:"",onClick:Y=>{Y.preventDefault(),o(k.slug)},className:t===k.slug?"active":"",children:k.name}),e.jsxs("small",{children:[" ","- ",((E=k._count)==null?void 0:E.blogPosts)||0," "]})]},k.id)})})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:s("blog.tags")}),e.jsx("div",{className:"widget-body",children:e.jsx("div",{className:"tags",children:h.map(k=>e.jsx("a",{href:"#",onClick:E=>{E.preventDefault(),F(k.slug)},className:r===k.slug?"active":"",children:k.name},k.id))})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:s("blog.archive")}),e.jsx("div",{className:"widget-body",children:e.jsx("ul",{className:"clearlist widget-menu",children:i.map((k,E)=>e.jsxs("li",{children:[e.jsxs("a",{href:"#",title:"",children:[k.monthName," ",k.year]}),e.jsxs("small",{children:[" - ",k.count," "]})]},E))})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mt-60",children:e.jsxs("div",{className:"widget mb-0",children:[e.jsx("h3",{className:"widget-title",children:s("blog.about_widget.title")}),e.jsx("div",{className:"widget-body",children:e.jsxs("div",{className:"widget-text clearfix",children:[e.jsx("img",{src:"/assets/img/power-128.png",alt:"DevSkills Logo",height:40,width:40,className:"left img-left",style:{borderRadius:"8px"}}),s("blog.about_widget.text")]})})]})})]})})})]})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})," "]})})]})}const $s=Object.freeze(Object.defineProperty({__proto__:null,default:rs},Symbol.toStringTag,{value:"Module"})),cs={title:"Elegant Portfolio Single Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template",description:"Resonance &mdash; One & Multi Page Reactjs Creative Template"};function os(){let s=pe();const m=ve.filter(N=>N.id==s.id)[0]||ve[0];return e.jsxs(e.Fragment,{children:[e.jsx(ke,{meta:cs}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/section-bg-1.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:m.title}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-tiny mb-0 opacity-075",children:"Branding, UI/UX Design, No-code Development"})})})]})}),e.jsxs(e.Fragment,{children:[e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsxs("div",{className:"container relative",children:[e.jsxs("div",{className:"row mb-80 mb-sm-40",children:[e.jsxs("div",{className:"col-md-6 mb-sm-40",children:[e.jsx("h2",{className:"section-title-small mb-20",children:"Project Details"}),e.jsx("hr",{className:"mb-20"}),e.jsxs("div",{className:"row text-gray",children:[e.jsx("div",{className:"col-sm-4",children:e.jsx("b",{children:"Date:"})}),e.jsx("div",{className:"col-sm-8",children:"May 1th, 2023"})]}),e.jsx("hr",{className:"mb-20"}),e.jsxs("div",{className:"row text-gray",children:[e.jsx("div",{className:"col-sm-4",children:e.jsx("b",{children:"Client:"})}),e.jsx("div",{className:"col-sm-8",children:"Envato Users"})]}),e.jsx("hr",{className:"mb-20"}),e.jsxs("div",{className:"row text-gray",children:[e.jsx("div",{className:"col-sm-4",children:e.jsx("b",{children:"Services:"})}),e.jsx("div",{className:"col-sm-8",children:"Branding, UI/UX Design, Front-end Development, Back-end Development"})]}),e.jsx("hr",{className:"mb-20"})]}),e.jsxs("div",{className:"col-md-6",children:[e.jsx("h2",{className:"section-title-small mb-20",children:"Description"}),e.jsx("hr",{className:"mb-20"}),e.jsx("p",{className:"text-gray mb-0",children:"Lorem ipsum dolor sit amet conseur adipisci inerene maximus ligula sempe metuse pelente mattis. Maecenas volutpat, diam eni sagittis quam porta quam. Sed id dolor consectetur fermentum volutpat accumsan purus iaculis libero. Donec vel ultricies purus iaculis libero. Etiam sit amet fringilla lacus susantebe sit ullamcorper pulvinar neque porttitor. Integere lectus. Praesent sede nisi eleifend fermum orci amet, iaculis libero. Donec vel ultricies purus quam."})]})]}),e.jsxs("div",{className:"row mb-n30",children:[e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/1-large.jpg",alt:"Image Description",width:970,height:1136})}),e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/6-large.jpg",alt:"Image Description",width:970,height:1136})}),e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/8-large.jpg",alt:"Image Description",width:970,height:1136})}),e.jsx("div",{className:"col-md-6 mb-30 wow fadeInUp",children:e.jsx("img",{src:"/assets/images/demo-elegant/portfolio/3-large.jpg",alt:"Image Description",width:970,height:1136})})]})]})}),e.jsx("hr",{className:"mt-0 mb-0 white"})]}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx(Oe,{})}),e.jsxs(e.Fragment,{children:[e.jsx("hr",{className:"mt-0 mb-0 white"}),e.jsxs("div",{className:"work-navigation bg-dark-1 light-content clearfix z-index-1 position-relative",children:[e.jsx(G,{to:"/main-portfolio-single-1/1",className:"work-prev",children:e.jsxs("span",{children:[e.jsx("i",{className:"mi-arrow-left size-24 align-middle"})," ","Previous"]})}),e.jsx("a",{href:"#",className:"work-all",children:e.jsxs("span",{children:[e.jsx("i",{className:"mi-close size-24 align-middle"})," All works"]})}),e.jsx(G,{to:"/main-portfolio-single-3/1",className:"work-next",children:e.jsxs("span",{children:["Next ",e.jsx("i",{className:"mi-arrow-right size-24 align-middle"})]})})]})]})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})," "]})})]})}const Us=Object.freeze(Object.defineProperty({__proto__:null,default:os},Symbol.toStringTag,{value:"Module"}));function ds(){const{id:s}=pe(),m=le(),{t:N,i18n:f}=se(),l=f.language||"et",[n,b]=c.useState(null),[u,w]=c.useState(!0),[g,y]=c.useState("");c.useEffect(()=>{v()},[s,l]);const v=async()=>{try{w(!0);const t=await(await fetch(`/api/products/${s}?language=${l}`)).json();t.success?(b(t.product),ue("product_detail_view",{product_id:t.product.id,product_title:t.product.title,language:l,source:"direct_link"})):y("Product not found")}catch(p){console.error("Error loading product:",p),y("Error loading product")}finally{w(!1)}},S=()=>{ue("demo_click",{product_id:n.id,product_title:n.title,language:l,source:"product_detail"})},C=p=>{ue("purchase_intent",{product_id:n.id,product_title:n.title,purchase_type:p,language:l,source:"product_detail"})},T=p=>p?new Intl.NumberFormat("en-US",{style:"currency",currency:"EUR"}).format(p):null;if(u)return e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container text-center py-5",children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})})})})]})})});if(g||!n)return e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsxs("div",{className:"container text-center py-5",children:[e.jsx("h1",{children:"Product Not Found"}),e.jsx("p",{children:"The product you're looking for doesn't exist."}),e.jsx("button",{onClick:()=>m(`/${l}/webstore`),className:"btn btn-mod btn-round",children:"Back to Webstore"})]})})})]})})});const h=as({title:n.title,description:n.excerpt,price:n.whitelabelPrice||n.subscriptionPrice,featuredImage:n.featuredImage,url:`https://devskills.ee/${l}/webstore-single/${n.slug}`}),A=ts([{name:"Home",url:`https://devskills.ee/${l}`},{name:"Webstore",url:`https://devskills.ee/${l}/webstore`},{name:n.title,url:`https://devskills.ee/${l}/webstore-single/${n.slug}`}]),i=n.featuredImage?`http://localhost:4004/uploads/product-images/${n.featuredImage}`:"https://devskills.ee/webstore.jpg";return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:n.metaTitle||n.title,description:n.metaDescription||n.excerpt,slug:`webstore-single/${n.slug}`,type:"product",image:i,imageAlt:n.featuredImageAlt||n.title,schema:[h,A],keywords:n.metaKeywords?n.metaKeywords.split(",").map(p=>p.trim()):["software","business","devskills"],publishedAt:n.publishedAt,modifiedAt:n.updatedAt}),e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/3.jpg)",paddingBottom:"40px"},children:e.jsx("div",{className:"container position-relative pt-30 pt-sm-50",children:e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-md-8 offset-md-2",children:[e.jsx("h1",{className:"hs-title-1 mb-20",children:e.jsx("span",{className:"wow charsAnimIn","data-splitting":"chars",children:n.title})}),e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-8 offset-lg-2",children:e.jsx("p",{className:"section-descr mb-20 wow fadeIn","data-wow-delay":"0.2s",children:n.excerpt})})})]})})})})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",style:{paddingTop:"40px"},children:e.jsx("div",{className:"container position-relative",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-lg-8",children:[e.jsx(Me,{images:n.images,productTitle:n.title}),e.jsx("div",{className:"blog-item-body",children:e.jsx("div",{className:"blog-item-content",dangerouslySetInnerHTML:{__html:n.content}})}),e.jsxs("div",{className:"blog-item-footer pt-4 mt-4 border-top",children:[n.categories&&n.categories.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("strong",{children:"Categories: "}),n.categories.map((p,t)=>e.jsx("span",{children:e.jsx("span",{className:"badge bg-primary me-2",children:p.category.name})},p.category.id))]}),n.tags&&n.tags.length>0&&e.jsxs("div",{children:[e.jsx("strong",{children:"Tags: "}),n.tags.map((p,t)=>e.jsx("span",{children:e.jsx("span",{className:"badge bg-secondary me-2",children:p.tag.name})},p.tag.id))]})]})]}),e.jsx("div",{className:"col-lg-4",children:e.jsxs("div",{className:"blog-sidebar ps-lg-4",children:[n.whitelabelPrice&&e.jsx("div",{className:"widget mb-4",children:e.jsxs("div",{className:"pricing-option mb-4 p-4 rounded-3",style:{background:"rgba(255, 255, 255, 0.02)",border:"1px solid rgba(255, 255, 255, 0.15)",transition:"all 0.3s ease"},children:[e.jsx("h6",{className:"text-white mb-3 text-center",children:"Whitelabel License"}),e.jsxs("div",{className:"text-center mb-3",children:[e.jsx("div",{className:"price text-primary",style:{fontSize:"2.5rem",fontWeight:"700"},children:T(n.whitelabelPrice)}),e.jsx("small",{className:"text-gray",children:"One-time payment"})]}),e.jsx("p",{className:"text-gray small mb-4 text-center",children:"Get the complete source code with commercial license"}),e.jsx("div",{className:"text-center",children:e.jsx("button",{className:"btn btn-mod btn-medium btn-circle btn-hover-anim btn-color",onClick:()=>C("whitelabel"),style:{minWidth:"180px"},children:e.jsx("span",{children:"Buy Whitelabel"})})})]})}),n.subscriptionPrice&&e.jsx("div",{className:"widget mb-4",children:e.jsxs("div",{className:"pricing-option mb-4 p-4 rounded-3",style:{background:"rgba(255, 255, 255, 0.02)",border:"1px solid rgba(255, 255, 255, 0.15)",transition:"all 0.3s ease"},children:[e.jsx("h6",{className:"text-white mb-3 text-center",children:"Subscription"}),e.jsxs("div",{className:"text-center mb-3",children:[e.jsxs("div",{className:"price text-success",style:{fontSize:"2.5rem",fontWeight:"700"},children:[T(n.subscriptionPrice),e.jsx("small",{style:{fontSize:"1rem",fontWeight:"400"},children:"/mo"})]}),e.jsx("small",{className:"text-gray",children:"Monthly billing"})]}),e.jsx("p",{className:"text-gray small mb-4 text-center",children:"Use the software as a service without source code"}),e.jsx("div",{className:"text-center",children:e.jsx("button",{className:"btn btn-mod btn-medium btn-circle btn-hover-anim",onClick:()=>C("subscription"),style:{minWidth:"180px",background:"#22c55e",borderColor:"#22c55e",color:"#fff"},children:e.jsx("span",{children:"Start Subscription"})})})]})}),n.demoUrl&&e.jsx("div",{className:"widget mb-4",children:e.jsx("div",{className:"text-center mt-4",children:e.jsx("a",{href:n.demoUrl,target:"_blank",rel:"noopener noreferrer",className:"opacity-1 no-hover",onClick:S,style:{cursor:"pointer"},children:e.jsx("span",{className:"btn btn-mod btn-small btn-border-w btn-circle","data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:"View Live Demo"}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:"View Live Demo"})]})})})})}),e.jsxs("div",{className:"widget",children:[e.jsx("h5",{className:"widget-title",children:"Product Information"}),e.jsxs("ul",{className:"list-unstyled",children:[e.jsxs("li",{className:"mb-2",children:[e.jsx("strong",{children:"Published:"})," ",new Date(n.publishedAt).toLocaleDateString()]}),e.jsxs("li",{className:"mb-2",children:[e.jsx("strong",{children:"Last Updated:"})," ",new Date(n.updatedAt).toLocaleDateString()]}),e.jsxs("li",{className:"mb-2",children:[e.jsx("strong",{children:"Views:"})," ",n.viewCount]})]})]})]})})]})})})]}),e.jsx("footer",{className:"footer-1 bg-dark-2 light-content",children:e.jsx(ie,{})})]})})})]})}const Os=Object.freeze(Object.defineProperty({__proto__:null,default:ds},Symbol.toStringTag,{value:"Module"}));function ms(){var S,C,T,h,A,i;let s=pe();const{t:m,i18n:N}=se(),f=N.language||"et";le();const[l,n]=c.useState(null),[b,u]=c.useState(!0),[w,g]=c.useState("");c.useEffect(()=>{const p=async()=>{try{u(!0);const t=await me.getPost(s.id);t.response.ok&&t.data?(console.log("Blog single API response:",t.data),n(t.data.data||t.data)):(console.error("Failed to fetch blog post:",t.response.status),g("Blog post not found"))}catch(t){console.error("Error fetching blog post:",t),g("Failed to load blog post")}finally{u(!1)}};s.id&&p()},[s.id]),c.useEffect(()=>{l&&!b&&setTimeout(async()=>{try{const{highlightCodeBlocks:p}=await Ze(async()=>{const{highlightCodeBlocks:t}=await import("./syntaxHighlighting-DKW5YAVq.js");return{highlightCodeBlocks:t}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12]));await p(".blog-content pre code","github-dark")}catch(p){console.warn("Failed to load syntax highlighting:",p)}},100)},[l,b]);const y=(p,t)=>{var a;if(!p||!p.translations)return"";const r=p.translations.find(j=>j.language===f);return(r==null?void 0:r[t])||((a=p.translations.find(j=>j.language==="en"))==null?void 0:a[t])||""};if(b)return e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{children:"Loading..."}),e.jsx("p",{children:"Please wait while we load the blog post."})]})})})})})]})})});if(!l||w)return e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("h1",{children:"Blog Post Not Found"}),e.jsx("p",{children:"The blog post you're looking for doesn't exist."}),e.jsx("a",{href:"/blog",className:"btn btn-mod btn-border btn-large btn-round",children:"Back to Blog"})]})})})})})]})})});const v=ss({title:y(l,"title"),description:y(l,"excerpt"),excerpt:y(l,"excerpt"),featuredImage:l.featuredImage,author:l.author||"DevSkills",publishedAt:l.publishedAt,modifiedAt:l.updatedAt,url:`https://devskills.ee/${f}/blog-single/${l.slug}`});return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:y(l,"title"),description:y(l,"excerpt"),slug:`blog-single/${l.slug}`,type:"article",image:l.featuredImage||"https://devskills.ee/blog.jpg",imageAlt:y(l,"title"),author:l.author||"DevSkills",publishedAt:l.publishedAt,modifiedAt:l.updatedAt,schema:[v],keywords:y(l,"keywords")||["blog","software development","devskills"]}),e.jsx("div",{className:"theme-elegant",children:e.jsxs("div",{className:"dark-mode",children:[e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content blog-hero-minimal",style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-10 offset-lg-1",children:e.jsx("h1",{className:"hs-title-3a mb-0 wow fadeInUpShort","data-wow-duration":"0.6s",children:y(l,"title")})})}),e.jsxs("div",{className:"blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn","data-wow-delay":"0.2s",children:[e.jsx("div",{className:"d-inline-block me-3",children:e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"mi-clock size-16"}),e.jsx("span",{className:"visually-hidden",children:"Date:"})," ",new Date(l.publishedAt||l.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})}),e.jsx("div",{className:"d-inline-block me-3",children:e.jsxs("a",{href:"#",children:[e.jsx("i",{className:"mi-user size-16"}),e.jsx("span",{className:"visually-hidden",children:"Author:"})," ",((S=l.author)==null?void 0:S.name)||"DevSkills Team"]})}),l.categories&&l.categories.length>0&&e.jsxs("div",{className:"d-inline-block me-3",children:[e.jsx("i",{className:"mi-folder size-16"}),e.jsx("span",{className:"visually-hidden",children:"Category:"}),e.jsx("a",{href:"#",children:l.categories[0].name})]}),e.jsxs("div",{className:"d-inline-block me-3",children:[e.jsx("i",{className:"mi-time size-16"}),e.jsx("span",{className:"visually-hidden",children:"Read time:"})," ",l.readTime||5," min"]})]}),e.jsx("div",{className:"blog-nav-minimal",children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{className:"d-flex gap-3",children:[e.jsxs(G,{to:`/${f}/blog`,className:"link-hover-anim link-circle-1 align-middle","data-link-animate":"y",children:[e.jsxs("span",{className:"link-strong link-strong-unhovered",children:[e.jsx("i",{className:"mi-arrow-left size-18 align-middle"})," ",m("blog.back_to_blog")||"Back to Blog"]}),e.jsxs("span",{className:"link-strong link-strong-hovered","aria-hidden":"true",children:[e.jsx("i",{className:"mi-arrow-left size-18 align-middle"})," ",m("blog.back_to_blog")||"Back to Blog"]})]}),((C=l==null?void 0:l.navigation)==null?void 0:C.previous)&&e.jsxs(G,{to:`/${f}/blog-single/${l.navigation.previous.slug}`,className:"link-hover-anim link-circle-1 align-middle","data-link-animate":"y",title:l.navigation.previous.title,children:[e.jsxs("span",{className:"link-strong link-strong-unhovered",children:[e.jsx("i",{className:"mi-chevron-left size-18 align-middle"})," ",m("blog.previous")||"Previous"]}),e.jsxs("span",{className:"link-strong link-strong-hovered","aria-hidden":"true",children:[e.jsx("i",{className:"mi-chevron-left size-18 align-middle"})," ",m("blog.previous")||"Previous"]})]})]}),e.jsx("div",{children:((T=l==null?void 0:l.navigation)==null?void 0:T.next)&&e.jsxs(G,{to:`/${f}/blog-single/${l.navigation.next.slug}`,className:"link-hover-anim link-circle-1 align-middle","data-link-animate":"y",title:l.navigation.next.title,children:[e.jsxs("span",{className:"link-strong link-strong-unhovered",children:[m("blog.next")||"Next"," ",e.jsx("i",{className:"mi-chevron-right size-18 align-middle"})]}),e.jsxs("span",{className:"link-strong link-strong-hovered","aria-hidden":"true",children:[m("blog.next")||"Next"," ",e.jsx("i",{className:"mi-chevron-right size-18 align-middle"})]})]})})]})})]})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container relative",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last",children:[e.jsx("div",{className:"blog-item mb-80 mb-xs-40",children:e.jsxs("div",{className:"blog-item-body",children:[l.featuredImage&&e.jsx("div",{className:"mb-40 mb-xs-30",children:e.jsx("img",{src:l.featuredImage,alt:y(l,"title"),width:1350,height:796})}),e.jsx("div",{className:"lead mb-40",children:y(l,"excerpt")}),e.jsx("div",{className:"blog-content",style:{lineHeight:"1.8",fontSize:"16px"},dangerouslySetInnerHTML:{__html:y(l,"content")}})]})}),e.jsxs("div",{className:"blog-comments-minimal mb-xs-40",children:[e.jsxs("h4",{className:"blog-page-title",children:[m("blog.comments.title")," ",e.jsxs("small",{className:"number",children:["(",((h=l.comments)==null?void 0:h.length)||0,")"]})]}),e.jsx("ul",{className:"media-list comment-list clearlist",children:e.jsx(Be,{comments:l.comments||[]})})]}),e.jsxs("div",{className:"blog-comments-minimal mb-xs-40",children:[e.jsx("h4",{className:"blog-page-title",children:m("blog.comments.add_comment")}),e.jsx(ze,{blogSlug:l.slug})]}),e.jsxs("div",{className:"clearfix mt-40",children:[((A=l==null?void 0:l.navigation)==null?void 0:A.previous)&&e.jsxs(G,{to:`/${f}/blog-single/${l.navigation.previous.slug}`,className:"blog-item-more left",children:[e.jsx("i",{className:"mi-chevron-left"})," ",m("blog.previous")||"Previous"]}),((i=l==null?void 0:l.navigation)==null?void 0:i.next)&&e.jsxs(G,{to:`/${f}/blog-single/${l.navigation.next.slug}`,className:"blog-item-more right",children:[m("blog.next")||"Next"," ",e.jsx("i",{className:"mi-chevron-right"})]})]})]}),e.jsx("div",{className:"col-lg-4 col-xl-3",children:e.jsx(Re,{searchInputClass:"form-control input-lg search-field round"})})]})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})," "]})})]})}const Ms=Object.freeze(Object.defineProperty({__proto__:null,default:ms},Symbol.toStringTag,{value:"Module"}));function hs(){const{t:s,i18n:m}=se(),N=m.language||"et";return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:"Privacy Policy",description:"DevSkills Privacy Policy - Learn how we collect, use, and protect your personal information when you use our services.",slug:"privacy-policy",type:"website",keywords:["privacy policy","data protection","devskills","gdpr"]}),e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:s("privacy.title")}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsxs("p",{className:"section-title-tiny mb-0 opacity-075",children:[s("privacy.lastUpdated"),":"," ",new Date().toLocaleDateString(N,{year:"numeric",month:"long",day:"numeric"})]})})}),e.jsx("div",{className:"spacer-small"})]})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container position-relative",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-lg-8 offset-lg-2",children:[e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.intro.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.intro.text1")}),e.jsx("p",{className:"text-gray",children:s("privacy.intro.text2")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.collect.title")}),e.jsx("h3",{className:"h4 mb-20 text-white",children:s("privacy.collect.personal.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.collect.personal.text")}),e.jsxs("ul",{className:"text-gray mb-30",children:[e.jsx("li",{children:s("privacy.collect.personal.item1")}),e.jsx("li",{children:s("privacy.collect.personal.item2")}),e.jsx("li",{children:s("privacy.collect.personal.item3")}),e.jsx("li",{children:s("privacy.collect.personal.item4")})]}),e.jsx("h3",{className:"h4 mb-20 text-white",children:s("privacy.collect.usage.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.collect.usage.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("privacy.collect.usage.item1")}),e.jsx("li",{children:s("privacy.collect.usage.item2")}),e.jsx("li",{children:s("privacy.collect.usage.item3")}),e.jsx("li",{children:s("privacy.collect.usage.item4")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.use.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.use.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("privacy.use.item1")}),e.jsx("li",{children:s("privacy.use.item2")}),e.jsx("li",{children:s("privacy.use.item3")}),e.jsx("li",{children:s("privacy.use.item4")}),e.jsx("li",{children:s("privacy.use.item5")}),e.jsx("li",{children:s("privacy.use.item6")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.sharing.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.sharing.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("privacy.sharing.item1")}),e.jsx("li",{children:s("privacy.sharing.item2")}),e.jsx("li",{children:s("privacy.sharing.item3")}),e.jsx("li",{children:s("privacy.sharing.item4")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.security.title")}),e.jsx("p",{className:"text-gray",children:s("privacy.security.text")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.rights.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.rights.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("privacy.rights.item1")}),e.jsx("li",{children:s("privacy.rights.item2")}),e.jsx("li",{children:s("privacy.rights.item3")}),e.jsx("li",{children:s("privacy.rights.item4")}),e.jsx("li",{children:s("privacy.rights.item5")}),e.jsx("li",{children:s("privacy.rights.item6")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.cookies.title")}),e.jsx("p",{className:"text-gray",children:s("privacy.cookies.text")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.contact.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("privacy.contact.text")}),e.jsxs("div",{className:"text-gray",children:[e.jsx("p",{children:e.jsx("strong",{children:"DevSkills OÜ"})}),e.jsx("p",{children:s("privacy.contact.address")}),e.jsx("p",{children:s("privacy.contact.email")}),e.jsx("p",{children:s("privacy.contact.phone")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("privacy.updates.title")}),e.jsx("p",{className:"text-gray",children:s("privacy.updates.text")})]})]})})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})})})]})}const Bs=Object.freeze(Object.defineProperty({__proto__:null,default:hs},Symbol.toStringTag,{value:"Module"}));function gs(){const{t:s,i18n:m}=se(),N=m.language||"en";return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:"Terms and Conditions",description:"DevSkills Terms and Conditions - Legal terms governing the use of our software development services and Business Comanager platform.",slug:"terms-conditions",type:"website",keywords:["terms and conditions","legal","devskills","service agreement"]}),e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark dark-mode transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-3 mb-10 wow fadeInUpShort","data-wow-duration":"0.6s",children:s("terms.title")}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsxs("p",{className:"section-title-tiny mb-0 opacity-075",children:[s("terms.lastUpdated"),":"," ",new Date().toLocaleDateString(N,{year:"numeric",month:"long",day:"numeric"})]})})}),e.jsx("div",{className:"spacer-small"})]})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container position-relative",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-lg-8 offset-lg-2",children:[e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.agreement.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.agreement.text1")}),e.jsx("p",{className:"text-gray",children:s("terms.agreement.text2")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.services.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.services.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("terms.services.item1")}),e.jsx("li",{children:s("terms.services.item2")}),e.jsx("li",{children:s("terms.services.item3")}),e.jsx("li",{children:s("terms.services.item4")}),e.jsx("li",{children:s("terms.services.item5")}),e.jsx("li",{children:s("terms.services.item6")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.responsibilities.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.responsibilities.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("terms.responsibilities.item1")}),e.jsx("li",{children:s("terms.responsibilities.item2")}),e.jsx("li",{children:s("terms.responsibilities.item3")}),e.jsx("li",{children:s("terms.responsibilities.item4")}),e.jsx("li",{children:s("terms.responsibilities.item5")}),e.jsx("li",{children:s("terms.responsibilities.item6")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.payment.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.payment.text")}),e.jsxs("ul",{className:"text-gray mb-20",children:[e.jsx("li",{children:s("terms.payment.item1")}),e.jsx("li",{children:s("terms.payment.item2")}),e.jsx("li",{children:s("terms.payment.item3")}),e.jsx("li",{children:s("terms.payment.item4")}),e.jsx("li",{children:s("terms.payment.item5")})]}),e.jsx("p",{className:"text-gray",children:s("terms.payment.text2")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.ip.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.ip.our")}),e.jsxs("p",{className:"text-gray mb-20",children:[s("terms.ip.custom"),"will be specified in individual project agreements."]}),e.jsx("p",{className:"text-gray",children:s("terms.ip.client")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.confidentiality.title")}),e.jsx("p",{className:"text-gray",children:s("terms.confidentiality.text")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.liability.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.liability.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("terms.liability.item1")}),e.jsx("li",{children:s("terms.liability.item2")}),e.jsx("li",{children:s("terms.liability.item3")}),e.jsx("li",{children:s("terms.liability.item4")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.warranties.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.warranties.text")}),e.jsxs("ul",{className:"text-gray",children:[e.jsx("li",{children:s("terms.warranties.item1")}),e.jsx("li",{children:s("terms.warranties.item2")}),e.jsx("li",{children:s("terms.warranties.item3")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.termination.title")}),e.jsx("p",{className:"text-gray",children:s("terms.termination.text")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.governing.title")}),e.jsx("p",{className:"text-gray",children:s("terms.governing.text")})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.contact.title")}),e.jsx("p",{className:"text-gray mb-20",children:s("terms.contact.text")}),e.jsxs("div",{className:"text-gray",children:[e.jsx("p",{children:e.jsx("strong",{children:"DevSkills OÜ"})}),e.jsx("p",{children:s("terms.contact.address")}),e.jsx("p",{children:s("terms.contact.email")}),e.jsx("p",{children:s("terms.contact.phone")})]})]}),e.jsxs("div",{className:"mb-50",children:[e.jsx("h2",{className:"section-title-small mb-30",children:s("terms.changes.title")}),e.jsx("p",{className:"text-gray",children:s("terms.changes.text")})]})]})})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})})})]})}const zs=Object.freeze(Object.defineProperty({__proto__:null,default:gs},Symbol.toStringTag,{value:"Module"}));function xs(){const{t:s}=se();return e.jsxs(e.Fragment,{children:[e.jsx(ce,{title:"Page Not Found - 404",description:"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.",slug:"404",type:"website",image:"https://devskills.ee/404.jpg",keywords:["404","page not found","error","devskills"]}),e.jsx("div",{className:"theme-elegant",children:e.jsx("div",{className:"dark-mode",children:e.jsxs("div",{className:"page bg-dark-1",id:"top",children:[e.jsx("nav",{className:"main-nav dark transparent stick-fixed wow-menubar",children:e.jsx(J,{links:K})}),e.jsxs("main",{id:"main",children:[e.jsx("section",{className:"page-section bg-dark-alpha-50 light-content",style:{backgroundImage:"url(/assets/images/demo-elegant/7.jpg)"},id:"home",children:e.jsxs("div",{className:"container position-relative pt-20 pt-sm-20 text-center",children:[e.jsx("h1",{className:"hs-title-1 mb-20 wow fadeInUpShort","data-wow-duration":"0.6s",style:{fontSize:"8rem",fontWeight:"700"},children:"404"}),e.jsx("div",{className:"row wow fadeIn","data-wow-delay":"0.2s",children:e.jsx("div",{className:"col-md-8 offset-md-2 col-lg-6 offset-lg-3",children:e.jsx("p",{className:"section-title-small mb-0 opacity-075",style:{fontSize:"2rem"},children:"Page Not Found"})})}),e.jsx("div",{className:"spacer-small"})]})}),e.jsx("section",{className:"page-section bg-dark-1 light-content",children:e.jsx("div",{className:"container position-relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-8 offset-lg-2 text-center",children:e.jsxs("div",{className:"wow fadeInUp","data-wow-delay":"0.1s",children:[e.jsxs("h2",{className:"section-title mb-30 mb-sm-20",children:[e.jsx("span",{className:"text-gray",children:"Oops!"})," Something went wrong",e.jsx("span",{className:"text-gray",children:"."})]}),e.jsxs("div",{className:"text-gray mb-40 mb-sm-30",children:[e.jsx("p",{className:"mb-20",children:"The page you were looking for could not be found. It might have been removed, had its name changed, or is temporarily unavailable."}),e.jsx("p",{className:"mb-0",children:"Don't worry, you can navigate back to our homepage or explore our services."})]}),e.jsxs("div",{className:"local-scroll",children:[e.jsx(G,{to:"/",className:"btn btn-mod btn-w btn-circle btn-medium me-3 mb-xs-10","data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsxs("span",{className:"btn-animate-y-1",children:[e.jsx("i",{className:"mi-home size-18 align-center me-2"}),"Back to Home"]}),e.jsxs("span",{className:"btn-animate-y-2","aria-hidden":"true",children:[e.jsx("i",{className:"mi-home size-18 align-center me-2"}),"Back to Home"]})]})}),e.jsx(G,{to:"/services",className:"btn btn-mod btn-border-w btn-circle btn-medium","data-btn-animate":"y",children:e.jsxs("span",{className:"btn-animate-y",children:[e.jsx("span",{className:"btn-animate-y-1",children:"Our Services"}),e.jsx("span",{className:"btn-animate-y-2","aria-hidden":"true",children:"Our Services"})]})})]})]})})})})})]}),e.jsx("footer",{className:"bg-dark-2 light-content footer z-index-1 position-relative",children:e.jsx(ie,{})})]})})})]})}const Rs=Object.freeze(Object.defineProperty({__proto__:null,default:xs},Symbol.toStringTag,{value:"Module"})),us=()=>{const{t:s}=se(),m=le(),[N,f]=c.useState({email:"",password:""}),[l,n]=c.useState(!1),[b,u]=c.useState(""),w=y=>{f({...N,[y.target.name]:y.target.value}),b&&u("")},g=async y=>{y.preventDefault(),n(!0),u("");try{const{response:v,data:S}=await Se.login(N);S.success?(localStorage.setItem("adminToken",S.token),localStorage.setItem("adminUser",JSON.stringify(S.user)),m("/admin/dashboard")):u(S.message||"Login failed")}catch(v){console.error("Login error:",v),u("Network error. Please try again.")}finally{n(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:"Admin Login - DevSkills",description:"Admin login page for DevSkills content management",noIndex:!0}),e.jsx("div",{id:"page",className:"page",children:e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section",id:"admin-login",children:e.jsx("div",{className:"container relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-md-6 offset-md-3 col-lg-4 offset-lg-4",children:e.jsxs("div",{className:"form-container",children:[e.jsxs("div",{className:"text-center mb-60 mb-sm-40",children:[e.jsxs("div",{className:"hs-line-4 font-alt black mb-20 mb-xs-10",children:[e.jsx("span",{className:"color-primary-1",children:"DevSkills"})," Admin"]}),e.jsx("p",{className:"section-descr mb-0",children:"Sign in to access the admin dashboard"})]}),b&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("i",{className:"mi-warning"}),b]}),e.jsxs("form",{className:"form contact-form",onSubmit:g,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email Address"}),e.jsx("input",{type:"email",name:"email",id:"email",className:"input-lg round form-control",placeholder:"Email Address",value:N.email,onChange:w,required:!0,autoComplete:"email"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsx("input",{type:"password",name:"password",id:"password",className:"input-lg round form-control",placeholder:"Password",value:N.password,onChange:w,required:!0,autoComplete:"current-password"})]}),e.jsx("div",{className:"form-group",children:e.jsx("button",{type:"submit",className:"btn btn-mod btn-color btn-large btn-round btn-full-width",disabled:l,children:l?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"fa fa-spinner fa-spin me-2"}),"Signing in..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-lock me-2"}),"Sign In"]})})})]}),e.jsx("div",{className:"text-center mt-40",children:e.jsx("p",{className:"small opacity-07",children:"© 2024 DevSkills. All rights reserved."})})]})})})})})})})]})},Ws=Object.freeze(Object.defineProperty({__proto__:null,default:us},Symbol.toStringTag,{value:"Module"})),ps=()=>{const s=le(),[m,N]=c.useState(null),[f,l]=c.useState(!0);return c.useEffect(()=>{(async()=>{const b=localStorage.getItem("adminToken"),u=localStorage.getItem("adminUser");if(!b||!u){s("/admin");return}try{const{response:w,data:g}=await Se.getMe();w.ok&&g.success?N(g.user):(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),s("/admin"))}catch(w){console.error("Auth check failed:",w),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),s("/admin")}finally{l(!1)}})()},[s]),f?e.jsx("div",{id:"page",className:"page",children:e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section",children:e.jsx("div",{className:"container relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 text-center",children:e.jsxs("div",{className:"loading-animation",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"color-primary-1",style:{fontSize:"3rem",animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"mt-20",children:e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading..."})})]})})})})})})}):e.jsxs(e.Fragment,{children:[e.jsx(te,{title:"Admin Dashboard - DevSkills",description:"DevSkills admin dashboard for content management",noIndex:!0}),e.jsxs(ee,{title:"Dashboard",children:[e.jsxs("div",{className:"row mb-40",children:[e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:document-text-bold"})}),e.jsx("div",{className:"number-2-title",children:"Total Posts"}),e.jsx("div",{className:"number-2-number",children:"0"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:folder-bold"})}),e.jsx("div",{className:"number-2-title",children:"Categories"}),e.jsx("div",{className:"number-2-number",children:"5"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:chat-round-bold"})}),e.jsx("div",{className:"number-2-title",children:"Comments"}),e.jsx("div",{className:"number-2-number",children:"0"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:eye-bold"})}),e.jsx("div",{className:"number-2-title",children:"Page Views"}),e.jsx("div",{className:"number-2-number",children:"-"})]})})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsx("div",{className:"mb-20",children:e.jsx("h3",{className:"hs-line-4 font-alt black mb-20 mb-xs-10",children:"Quick Actions"})}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:add-circle-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"New Blog Post"}),e.jsx("div",{className:"alt-features-descr",children:"Create a new multilingual blog post with rich content and scheduling."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>s("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:"Create Post"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:documents-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Manage Posts"}),e.jsx("div",{className:"alt-features-descr",children:"Edit, publish, schedule, and organize your existing blog posts."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>s("/admin/posts"),className:"btn btn-mod btn-color btn-round",children:"Manage Posts"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:folder-with-files-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Categories & Tags"}),e.jsx("div",{className:"alt-features-descr",children:"Organize your content with categories and tags for better navigation."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>s("/admin/categories"),className:"btn btn-mod btn-color btn-round",children:"Organize Content"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:chat-round-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Comment Management"}),e.jsx("div",{className:"alt-features-descr",children:"Review, approve, and manage comments from your blog readers."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>s("/admin/comments"),className:"btn btn-mod btn-color btn-round",children:"Manage Comments"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:chart-2-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Analytics"}),e.jsx("div",{className:"alt-features-descr",children:"View detailed analytics and insights about your blog performance."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>s("/admin/analytics"),className:"btn btn-mod btn-color btn-round",children:"View Analytics"})})]})})]})]})})]})]})},Hs=Object.freeze(Object.defineProperty({__proto__:null,default:ps},Symbol.toStringTag,{value:"Module"})),js=()=>{const s=le(),[m,N]=c.useState([]),[f,l]=c.useState(!0),[n,b]=c.useState(""),[u,w]=c.useState({page:1,limit:10,status:"all",search:""}),[g,y]=c.useState({});c.useEffect(()=>{v()},[u]);const v=async()=>{try{l(!0);const i={};Object.entries(u).forEach(([r,a])=>{a&&a!=="all"&&(i[r]=a)});const{response:p,data:t}=await z.getPosts(i);t.success?(N(t.data.posts),y(t.data.pagination)):b(t.message||"Failed to load posts")}catch(i){console.error("Load posts error:",i),b("Network error. Please try again.")}finally{l(!1)}},S=async i=>{if(confirm("Are you sure you want to delete this blog post? This action cannot be undone."))try{const{response:p,data:t}=await me.deletePost(i);t.success?v():b(t.message||"Failed to delete post")}catch(p){console.error("Delete error:",p),b("Network error. Please try again.")}},C=async i=>{try{const{response:p,data:t}=await me.toggleVisibility(i);t.success?v():b(t.message||"Failed to toggle visibility")}catch(p){console.error("Toggle visibility error:",p),b("Network error. Please try again.")}},T=i=>new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),h=i=>i?i.startsWith("http")?i:`${je.replace("/api","")}/uploads/blog-images/${i}`:null,A=i=>i.published?i.scheduledAt&&new Date(i.scheduledAt)>new Date?e.jsxs("span",{className:"badge bg-warning",children:[e.jsx("iconify-icon",{icon:"solar:clock-circle-bold",className:"me-1"}),"Scheduled"]}):e.jsxs("span",{className:"badge bg-success",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]}):e.jsxs("span",{className:"badge bg-secondary",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-1"}),"Draft"]});return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:"Manage Blog Posts - Admin",description:"Manage blog posts in the admin panel",noIndex:!0}),e.jsxs(ee,{title:"Blog Posts",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Manage your blog posts, create new content, and organize your articles."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:()=>s("/admin/blog/new"),className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Post"]})})]})}),e.jsx("div",{className:"admin-table mb-30",style:{padding:"15px 20px"},children:e.jsxs("div",{className:"row g-3",children:[e.jsxs("div",{className:"col-12 col-md-6 col-lg-4",children:[e.jsx("label",{className:"form-label",children:"Search Posts"}),e.jsx("input",{type:"text",value:u.search,onChange:i=>w(p=>({...p,search:i.target.value,page:1})),className:"form-control",placeholder:"Search by title..."})]}),e.jsxs("div",{className:"col-6 col-md-3 col-lg-3",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:u.status,onChange:i=>w(p=>({...p,status:i.target.value,page:1})),className:"form-control",children:[e.jsx("option",{value:"all",children:"All Posts"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"draft",children:"Drafts"})]})]}),e.jsxs("div",{className:"col-6 col-md-3 col-lg-2",children:[e.jsx("label",{className:"form-label",children:"Per Page"}),e.jsxs("select",{value:u.limit,onChange:i=>w(p=>({...p,limit:parseInt(i.target.value),page:1})),className:"form-control",children:[e.jsx("option",{value:10,children:"10"}),e.jsx("option",{value:25,children:"25"}),e.jsx("option",{value:50,children:"50"})]})]})]})}),n&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),n]}),e.jsx("div",{className:"admin-table",children:f?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading posts..."})]}):m.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No blog posts found"}),e.jsx("p",{className:"section-descr mb-30",children:u.search||u.status!=="all"?"Try adjusting your search filters or create your first blog post.":"Get started by creating your first blog post."}),e.jsxs("button",{onClick:()=>s("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Post"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Title"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Author"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:m.map(i=>{const p=i.translations.find(t=>t.language==="en")||i.translations[0];return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[i.featuredImage&&e.jsx("img",{className:"rounded me-3",src:h(i.featuredImage),alt:"",style:{width:"50px",height:"50px",objectFit:"cover"},onError:t=>{t.target.style.display="none"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:(p==null?void 0:p.title)||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",i.slug]})]})]})}),e.jsxs("td",{children:[A(i),i.featured&&e.jsxs("span",{className:"badge bg-primary ms-2",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured"]})]}),e.jsx("td",{children:i.author.name||i.author.email}),e.jsx("td",{children:T(i.createdAt)}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>s(`/admin/blog/edit/${i.id}`),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>C(i.id),className:`btn btn-sm ${i.published?"btn-outline-warning":"btn-outline-success"}`,title:i.published?"Unpublish":"Publish",children:e.jsx("iconify-icon",{icon:i.published?"solar:eye-closed-bold":"solar:eye-bold"})}),e.jsx("button",{onClick:()=>S(i.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},i.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:m.map(i=>{const p=i.translations.find(t=>t.language==="en")||i.translations[0];return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[i.featuredImage&&e.jsx("img",{className:"rounded me-3",src:h(i.featuredImage),alt:"",style:{width:"40px",height:"40px",objectFit:"cover"},onError:t=>{t.target.style.display="none"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:(p==null?void 0:p.title)||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",i.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Status"}),e.jsxs("div",{children:[A(i),i.featured&&e.jsxs("span",{className:"badge bg-primary ms-1",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured"]})]})]}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Author"}),e.jsx("small",{children:i.author.name||i.author.email})]}),e.jsxs("div",{className:"col-12 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:T(i.createdAt)})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>s(`/admin/blog/edit/${i.id}`),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>C(i.id),className:`btn btn-sm flex-fill ${i.published?"btn-outline-warning":"btn-outline-success"}`,title:i.published?"Unpublish":"Publish",children:[e.jsx("iconify-icon",{icon:i.published?"solar:eye-closed-bold":"solar:eye-bold",className:"me-1"}),i.published?"Hide":"Show"]}),e.jsxs("button",{onClick:()=>S(i.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},i.id)})})})]})}),g.pages>1&&e.jsxs("div",{className:"row mt-30 align-items-center",children:[e.jsx("div",{className:"col-12 col-md-6 mb-3 mb-md-0",children:e.jsxs("p",{className:"small text-muted mb-0 text-center text-md-start",children:["Showing ",(g.page-1)*g.limit+1," to"," ",Math.min(g.page*g.limit,g.total)," ","of ",g.total," results"]})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx("nav",{"aria-label":"Blog posts pagination",children:e.jsxs("ul",{className:"pagination pagination-sm justify-content-center justify-content-md-end mb-0",children:[e.jsx("li",{className:`page-item ${g.page<=1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>w(i=>({...i,page:i.page-1})),disabled:g.page<=1,children:"Previous"})}),e.jsx("li",{className:"page-item active",children:e.jsxs("span",{className:"page-link",children:["Page ",g.page," of ",g.pages]})}),e.jsx("li",{className:`page-item ${g.page>=g.pages?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>w(i=>({...i,page:i.page+1})),disabled:g.page>=g.pages,children:"Next"})})]})})})]})]})]})},qs=Object.freeze(Object.defineProperty({__proto__:null,default:js},Symbol.toStringTag,{value:"Module"})),bs=()=>{var k,E,Y,V,H;const{t:s,i18n:m}=se(),N=le(),{id:f}=pe(),l=!!f,n=d=>d?d.startsWith("http")?d:`${je.replace("/api","")}/uploads/blog-images/${d}`:null,[b,u]=c.useState(!1),[w,g]=c.useState(!1),[y,v]=c.useState(""),[S,C]=c.useState(""),[T]=c.useState(()=>Object.keys(m.store.data)),[h,A]=c.useState(()=>{const d={};return T.forEach(_=>{d[_]={title:"",excerpt:"",content:"",metaTitle:"",metaDesc:"",keywords:[]}}),{slug:"",featured:!1,published:!1,scheduledAt:"",featuredImage:null,featuredImageAlt:"",readTime:"",categoryIds:[],tagIds:[],translations:d}}),[i,p]=c.useState("en"),[t,r]=c.useState([]),[a,j]=c.useState([]),[o,F]=c.useState(null);c.useEffect(()=>{(async()=>{try{if(u(!0),v(""),!localStorage.getItem("adminToken")){v("Authentication required. Please log in to access this page."),u(!1);return}const[D,B]=await Promise.all([z.getCategories(),z.getTags()]);if(D.response.ok&&D.data)r(D.data.data||[]);else{if(console.error("Categories API failed:",D.response.status,D.response.statusText),D.response.status===401||D.response.status===403){v("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}r([])}if(B.response.ok&&B.data)j(B.data.data||[]);else{if(console.error("Tags API failed:",B.response.status,B.response.statusText),B.response.status===401||B.response.status===403){v("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}j([])}if(l){const{response:ae,data:Q}=await z.getPost(f);if(ae.ok&&Q.success)try{const U=Q.data,x={};U.translations&&Array.isArray(U.translations)&&U.translations.forEach(I=>{x[I.language]=I}),A(I=>({...I,slug:U.slug||"",featured:U.featured||!1,published:U.published||!1,scheduledAt:U.scheduledAt?new Date(U.scheduledAt).toISOString().slice(0,16):"",featuredImage:null,featuredImageAlt:U.featuredImageAlt||"",readTime:U.readTime||"",categoryIds:U.categories?U.categories.map(P=>P.id):[],tagIds:U.tags?U.tags.map(P=>P.id):[],translations:{...I.translations,...x}})),U.featuredImage&&F(n(U.featuredImage))}catch(U){console.error("Failed to parse post response:",U),v("Failed to load post data - invalid response format")}else console.error("Post API failed:",ae.status,ae.statusText),v(Q.message||`Failed to load post: ${ae.status} ${ae.statusText}`)}}catch(_){console.error("Error loading data:",_),_.message&&_.message.includes("fetch")?v("Failed to connect to the server. Please check if the backend is running on localhost:4004"):v("Failed to load data. Please try again.")}finally{u(!1)}})()},[f,l]);const M=(d,_)=>{A(D=>({...D,[d]:_}))},W=(d,_,D)=>{A(B=>({...B,translations:{...B.translations,[d]:{...B.translations[d],[_]:D}}}))},he=d=>{const _=d.target.files[0];if(_){A(B=>({...B,featuredImage:_}));const D=new FileReader;D.onload=B=>{F(B.target.result)},D.readAsDataURL(_)}},ne=async d=>{d.preventDefault(),g(!0),v(""),C("");try{const _=localStorage.getItem("adminToken"),D=new FormData;D.append("slug",h.slug),D.append("featured",h.featured),D.append("published",h.published),h.scheduledAt&&D.append("scheduledAt",h.scheduledAt),D.append("featuredImageAlt",h.featuredImageAlt),h.readTime&&D.append("readTime",h.readTime),D.append("categoryIds",JSON.stringify(h.categoryIds)),D.append("tagIds",JSON.stringify(h.tagIds)),D.append("translations",JSON.stringify(h.translations)),h.featuredImage&&D.append("featuredImage",h.featuredImage);let B;l?B=await me.updatePost(f,D):B=await me.createPost(D);const{response:ae,data:Q}=B;if(ae.ok&&Q&&Q.success)C(`Blog post ${l?"updated":"created"} successfully!`),setTimeout(()=>{N("/admin/posts")},2e3);else{const U=(Q==null?void 0:Q.message)||`Failed to ${l?"update":"create"} blog post`;v(U)}}catch(_){console.error("Save error:",_),v("Network error. Please try again.")}finally{g(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:`${l?"Edit":"Create"} Blog Post - Admin`,description:"Create or edit blog posts in the admin panel",noIndex:!0}),e.jsx(ee,{title:l?"Edit Blog Post":"Create New Blog Post",children:e.jsxs("form",{onSubmit:ne,className:"admin-form",children:[y&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),y]}),S&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),S]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:settings-bold",className:"me-2 color-primary-1"}),"Basic Settings"]}),e.jsx("p",{className:"section-descr mb-0",children:"Configure the basic properties of your blog post"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"me-2"}),"Slug (URL)"]}),e.jsx("input",{type:"text",value:h.slug,onChange:d=>M("slug",d.target.value),className:"form-control",placeholder:"blog-post-url"}),e.jsx("small",{className:"form-text text-muted",children:"This will be the URL path for your blog post (e.g., /blog/your-slug)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:clock-circle-bold",className:"me-2"}),"Read Time (minutes)"]}),e.jsx("input",{type:"number",value:h.readTime,onChange:d=>M("readTime",d.target.value),className:"form-control",placeholder:"5",min:"1",max:"60"}),e.jsx("small",{className:"form-text text-muted",children:"Estimated reading time for this post"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:calendar-bold",className:"me-2"}),"Schedule Publication"]}),e.jsx("input",{type:"datetime-local",value:h.scheduledAt,onChange:d=>M("scheduledAt",d.target.value),className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Leave empty to publish immediately when published is checked"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-2"}),"Post Options"]}),e.jsxs("div",{className:"d-flex flex-column gap-2",children:[e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"featured",checked:h.featured,onChange:d=>M("featured",d.target.checked),className:"form-check-input"}),e.jsxs("label",{className:"form-check-label",htmlFor:"featured",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured Post"]}),e.jsx("small",{className:"form-text text-muted d-block",children:"Show this post prominently on the homepage"})]}),e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"published",checked:h.published,onChange:d=>M("published",d.target.checked),className:"form-check-input"}),e.jsxs("label",{className:"form-check-label",htmlFor:"published",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]}),e.jsx("small",{className:"form-text text-muted d-block",children:"Make this post visible to the public"})]})]})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:gallery-bold",className:"me-2 color-primary-1"}),"Featured Image"]}),e.jsx("p",{className:"section-descr mb-0",children:"Upload a featured image that will be displayed with your blog post"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:upload-bold",className:"me-2"}),"Upload Image"]}),e.jsx("input",{type:"file",accept:"image/*",onChange:he,className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Recommended size: 1200x630px. Supported formats: JPG, PNG, WebP"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:eye-bold",className:"me-2"}),"Alt Text"]}),e.jsx("input",{type:"text",value:h.featuredImageAlt,onChange:d=>M("featuredImageAlt",d.target.value),className:"form-control",placeholder:"Describe the image for accessibility"}),e.jsx("small",{className:"form-text text-muted",children:"Describe the image for screen readers and SEO"})]}),o&&e.jsxs("div",{className:"col-12",children:[e.jsx("div",{className:"mb-20",children:e.jsx("label",{className:"form-label",children:"Image Preview"})}),e.jsx("div",{className:"text-center",children:e.jsx("img",{src:o,alt:"Preview",className:"image-preview",style:{maxWidth:"400px",height:"auto"}})})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("i",{className:"mi-globe me-2 color-primary-1"}),"Content (Multi-language)"]}),e.jsx("p",{className:"section-descr mb-0",children:"Create content in multiple languages. At least English content is required."})]})}),e.jsx("div",{className:"language-tabs mb-30",children:T.map(d=>e.jsxs("button",{type:"button",onClick:()=>p(d),className:`language-tab ${i===d?"active":""}`,children:[e.jsx("i",{className:"mi-globe me-2"}),d.toUpperCase(),d==="en"&&e.jsx("span",{className:"ms-1 small",children:"(Required)"})]},d))}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Title (",i.toUpperCase(),")",i==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx("input",{type:"text",value:((k=h.translations[i])==null?void 0:k.title)||"",onChange:d=>W(i,"title",d.target.value),className:"form-control",placeholder:"Enter blog post title",required:i==="en"}),e.jsxs("small",{className:"form-text text-muted",children:["The main title of your blog post in"," ",i.toUpperCase()]})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-text me-2"}),"Excerpt (",i.toUpperCase(),")"]}),e.jsx("textarea",{value:((E=h.translations[i])==null?void 0:E.excerpt)||"",onChange:d=>W(i,"excerpt",d.target.value),rows:3,className:"form-control",placeholder:"Brief description of the blog post"}),e.jsx("small",{className:"form-text text-muted",children:"A short summary that will appear in blog listings and social media previews"})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-2"}),"Content (",i.toUpperCase(),")",i==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx(Pe,{content:((Y=h.translations[i])==null?void 0:Y.content)||"",onChange:d=>W(i,"content",d),placeholder:"Write your blog post content here. You can paste formatted text and code snippets with syntax highlighting."}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("iconify-icon",{icon:"solar:info-circle-bold",className:"me-1"}),"Rich text editor with syntax highlighting. Paste code snippets and they will be automatically highlighted. Use the toolbar for formatting options."]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Title (",i.toUpperCase(),")"]}),e.jsx("input",{type:"text",value:((V=h.translations[i])==null?void 0:V.metaTitle)||"",onChange:d=>W(i,"metaTitle",d.target.value),className:"form-control",placeholder:"SEO title (optional)",maxLength:"60"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-search me-1"}),"Title that appears in search engine results (max 60 characters)"]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-description me-2"}),"Meta Description (",i.toUpperCase(),")"]}),e.jsx("textarea",{value:((H=h.translations[i])==null?void 0:H.metaDesc)||"",onChange:d=>W(i,"metaDesc",d.target.value),rows:3,className:"form-control",placeholder:"SEO description (optional)",maxLength:"160"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-search me-1"}),"Description that appears in search engine results (max 160 characters)"]})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-2 color-primary-1"}),"Categories & Tags"]}),e.jsx("p",{className:"section-descr mb-0",children:"Organize your blog post with categories and tags"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"me-2"}),"Categories"]}),e.jsx("div",{className:"categories-grid",children:t&&t.length>0?t.map(d=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`category-${d.id}`,checked:h.categoryIds.includes(d.id),onChange:()=>{const _=h.categoryIds.includes(d.id)?h.categoryIds.filter(D=>D!==d.id):[...h.categoryIds,d.id];A(D=>({...D,categoryIds:_}))}}),e.jsx("label",{className:"form-check-label",htmlFor:`category-${d.id}`,children:d.name})]},d.id)):e.jsx("p",{className:"text-muted",children:"No categories available"})})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:hashtag-bold",className:"me-2"}),"Tags"]}),e.jsx("div",{className:"tags-grid",children:a&&a.length>0?a.map(d=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`tag-${d.id}`,checked:h.tagIds.includes(d.id),onChange:()=>{const _=h.tagIds.includes(d.id)?h.tagIds.filter(D=>D!==d.id):[...h.tagIds,d.id];A(D=>({...D,tagIds:_}))}}),e.jsx("label",{className:"form-check-label",htmlFor:`tag-${d.id}`,children:d.name})]},d.id)):e.jsx("p",{className:"text-muted",children:"No tags available"})})]})]})]}),e.jsx("div",{className:"row mt-40",children:e.jsxs("div",{className:"col-12 text-end",children:[e.jsx("button",{type:"button",onClick:()=>N("/admin/posts"),className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:w,className:"btn btn-mod btn-color btn-round",children:w?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"fa fa-spinner fa-spin me-2"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-check me-2"}),l?"Update Post":"Create Post"]})})]})})]})})]})},Gs=Object.freeze(Object.defineProperty({__proto__:null,default:bs},Symbol.toStringTag,{value:"Module"})),Ns=()=>{const s=le(),[m,N]=c.useState([]),[f,l]=c.useState(!0),[n,b]=c.useState(""),[u,w]=c.useState({page:1,limit:10,status:"all",search:""}),[g,y]=c.useState({});c.useEffect(()=>{v()},[u]);const v=async()=>{var t,r;try{l(!0);const a={};Object.entries(u).forEach(([F,M])=>{M&&M!=="all"&&(a[F]=M)});const{response:j,data:o}=await z.getProducts(a);o.success?(N(((t=o.data)==null?void 0:t.products)||o.products||[]),y(((r=o.data)==null?void 0:r.pagination)||{})):b(o.message||"Failed to load products")}catch(a){console.error("Load products error:",a),b("Network error. Please try again.")}finally{l(!1)}},S=async t=>{if(window.confirm("Are you sure you want to delete this product?"))try{const{response:r,data:a}=await z.deleteProduct(t);r.ok&&a.success?N(m.filter(j=>j.id!==t)):b(a.message||"Failed to delete product")}catch(r){console.error("Delete product error:",r),b("Failed to delete product")}},C=async t=>{try{const a=m.find(F=>F.id===t).status==="published"?"draft":"published",{response:j,data:o}=await z.updateProduct(t,{status:a});j.ok&&o.success?N(m.map(F=>F.id===t?{...F,status:a}:F)):b(o.message||"Failed to update product status")}catch(r){console.error("Toggle visibility error:",r),b("Failed to update product status")}},T=(t,r)=>{w(a=>({...a,[t]:r,page:1}))},h=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),A=t=>t?t.startsWith("http")?t:`${je.replace("/api","")}/uploads/product-images/${t}`:null,i=t=>t.images&&t.images.length>0?(t.images.find(a=>a.isDisplay)||t.images[0]).filename:t.featuredImage,p=t=>t.status==="draft"?e.jsxs("span",{className:"badge bg-secondary",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-1"}),"Draft"]}):e.jsxs("span",{className:"badge bg-success",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]});return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:"Manage Products - Admin",description:"Manage products in the admin panel",noIndex:!0}),e.jsxs(ee,{title:"Products",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Manage your webstore products, create new items, and organize your catalog."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:()=>s("/admin/products/new"),className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Product"]})})]})}),e.jsx("div",{className:"admin-table mb-30",style:{padding:"15px 20px"},children:e.jsxs("div",{className:"row g-3",children:[e.jsxs("div",{className:"col-12 col-md-6 col-lg-4",children:[e.jsx("label",{className:"form-label",children:"Search Products"}),e.jsx("input",{type:"text",className:"form-control",placeholder:"Search by title or slug...",value:u.search,onChange:t=>T("search",t.target.value)})]}),e.jsxs("div",{className:"col-12 col-md-6 col-lg-4",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{className:"form-control",value:u.status,onChange:t=>T("status",t.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"draft",children:"Draft"})]})]}),e.jsx("div",{className:"col-12 col-lg-4 d-flex align-items-end",children:e.jsxs("button",{onClick:()=>w({page:1,limit:10,status:"all",search:""}),className:"btn btn-mod btn-border btn-round w-100",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"me-2"}),"Reset Filters"]})})]})}),n&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),n]}),e.jsx("div",{className:"admin-table",children:f?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading products..."})]}):m.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:shop-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No products found"}),e.jsx("p",{className:"section-descr mb-30",children:u.search||u.status!=="all"?"Try adjusting your search filters or create your first product.":"Get started by creating your first product."}),e.jsxs("button",{onClick:()=>s("/admin/products/new"),className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Product"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Product"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Pricing"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:m.map(t=>{var a,j;const r=((a=t.translations)==null?void 0:a.find(o=>o.language==="en"))||((j=t.translations)==null?void 0:j[0]);return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[i(t)&&e.jsx("img",{className:"rounded me-3",src:A(i(t)),alt:"",style:{width:"50px",height:"50px",objectFit:"cover"},onError:o=>{o.target.style.display="none"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:(r==null?void 0:r.title)||t.title||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",t.slug]})]})]})}),e.jsx("td",{children:p(t)}),e.jsx("td",{children:e.jsxs("div",{children:[t.whitelabelPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"Whitelabel:"})," €",t.whitelabelPrice]}),t.subscriptionPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"Subscription:"})," €",t.subscriptionPrice,"/mo"]}),!t.whitelabelPrice&&!t.subscriptionPrice&&e.jsx("span",{className:"text-muted",children:"No pricing set"})]})}),e.jsx("td",{children:h(t.createdAt)}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>s(`/admin/products/edit/${t.id}`),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>C(t.id),className:`btn btn-sm ${t.status==="published"?"btn-outline-warning":"btn-outline-success"}`,title:t.status==="published"?"Unpublish":"Publish",children:e.jsx("iconify-icon",{icon:t.status==="published"?"solar:eye-closed-bold":"solar:eye-bold"})}),e.jsx("button",{onClick:()=>S(t.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},t.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:m.map(t=>{var a,j;const r=((a=t.translations)==null?void 0:a.find(o=>o.language==="en"))||((j=t.translations)==null?void 0:j[0]);return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[i(t)&&e.jsx("img",{className:"rounded me-3",src:A(i(t)),alt:"",style:{width:"40px",height:"40px",objectFit:"cover"},onError:o=>{o.target.style.display="none"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:(r==null?void 0:r.title)||t.title||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",t.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Status"}),e.jsx("div",{children:p(t)})]}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Pricing"}),e.jsxs("div",{children:[t.whitelabelPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"WL:"})," €",t.whitelabelPrice]}),t.subscriptionPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"Sub:"})," €",t.subscriptionPrice,"/mo"]}),!t.whitelabelPrice&&!t.subscriptionPrice&&e.jsx("span",{className:"text-muted small",children:"No pricing"})]})]}),e.jsxs("div",{className:"col-12 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:h(t.createdAt)})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>s(`/admin/products/edit/${t.id}`),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>C(t.id),className:`btn btn-sm flex-fill ${t.status==="published"?"btn-outline-warning":"btn-outline-success"}`,title:t.status==="published"?"Unpublish":"Publish",children:[e.jsx("iconify-icon",{icon:t.status==="published"?"solar:eye-closed-bold":"solar:eye-bold",className:"me-1"}),t.status==="published"?"Hide":"Show"]}),e.jsxs("button",{onClick:()=>S(t.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},t.id)})})})]})}),g.pages>1&&e.jsxs("div",{className:"row mt-30 align-items-center",children:[e.jsx("div",{className:"col-12 col-md-6 mb-3 mb-md-0",children:e.jsxs("p",{className:"small text-muted mb-0 text-center text-md-start",children:["Showing ",(g.page-1)*g.limit+1," to"," ",Math.min(g.page*g.limit,g.total)," ","of ",g.total," results"]})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx("nav",{"aria-label":"Products pagination",children:e.jsxs("ul",{className:"pagination pagination-sm justify-content-center justify-content-md-end mb-0",children:[e.jsx("li",{className:`page-item ${g.page<=1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>w(t=>({...t,page:t.page-1})),disabled:g.page<=1,children:"Previous"})}),e.jsx("li",{className:"page-item active",children:e.jsxs("span",{className:"page-link",children:["Page ",g.page," of ",g.pages]})}),e.jsx("li",{className:`page-item ${g.page>=g.pages?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>w(t=>({...t,page:t.page+1})),disabled:g.page>=g.pages,children:"Next"})})]})})})]})]})]})},Vs=Object.freeze(Object.defineProperty({__proto__:null,default:Ns},Symbol.toStringTag,{value:"Module"})),fs=()=>{var D,B,ae,Q,U;const{t:s,i18n:m}=se(),N=le(),{id:f}=pe(),l=!!f,n=x=>x?x.startsWith("http")?x:`${je.replace("/api","")}/uploads/product-images/${x}`:null,[b,u]=c.useState(!1),[w,g]=c.useState(!1),[y,v]=c.useState(""),[S,C]=c.useState(""),[T]=c.useState(()=>Object.keys(m.store.data)),[h,A]=c.useState(()=>{const x={};return T.forEach(I=>{x[I]={title:"",excerpt:"",content:"",metaTitle:"",metaDesc:"",keywords:[]}}),{slug:"",whitelabelPrice:"",subscriptionPrice:"",demoUrl:"",status:"draft",featuredImage:null,featuredImageAlt:"",images:[],categoryIds:[],tagIds:[],translations:x}}),[i,p]=c.useState([]),[t,r]=c.useState([]),[a,j]=c.useState("en"),[o,F]=c.useState(null),[M,W]=c.useState([]),[he,ne]=c.useState(0);c.useEffect(()=>{(async()=>{var I,P,O;try{u(!0);const[X,re]=await Promise.all([z.getCategories(),z.getTags()]);if((I=X.data)!=null&&I.success&&p(X.data.data),(P=re.data)!=null&&P.success&&r(re.data.data),l&&f){console.log("Loading product with ID:",f),console.log("Calling adminAPI.getProduct...");const de=await z.getProduct(f);if(console.log("Product API response:",de),(O=de.data)!=null&&O.success){const L=de.data.product;console.log("Product data:",L),A(q=>{var ge,Z;return{...q,slug:L.slug||"",whitelabelPrice:L.whitelabelPrice||"",subscriptionPrice:L.subscriptionPrice||"",demoUrl:L.demoUrl||"",status:L.status||"draft",featuredImageAlt:L.featuredImageAlt||"",categoryIds:((ge=L.categories)==null?void 0:ge.map(xe=>xe.categoryId))||[],tagIds:((Z=L.tags)==null?void 0:Z.map(xe=>xe.tagId))||[]}});const R={...h.translations};if(L.translations&&Array.isArray(L.translations)&&L.translations.forEach(q=>{R[q.language]={title:q.title||"",excerpt:q.excerpt||"",content:q.content||"",metaTitle:q.metaTitle||"",metaDesc:q.metaDesc||"",keywords:q.keywords||[]}}),A(q=>({...q,translations:R})),L.images&&Array.isArray(L.images)){console.log("Loading existing images:",L.images);const q=L.images.map((Z,xe)=>{const fe=n(Z.filename);return console.log(`Image ${xe}: ${Z.filename} -> ${fe}`),{id:Z.id,file:null,preview:fe,alt:Z.alt||"",isDisplay:Z.isDisplay,filename:Z.filename,sortOrder:Z.sortOrder}});console.log("Processed existing images:",q),W(q);const ge=q.findIndex(Z=>Z.isDisplay);ge!==-1&&ne(ge)}else console.log("No images found in product:",L.images);L.featuredImage&&(!L.images||L.images.length===0)&&F(n(L.featuredImage))}else console.error("Failed to load product:",de),v("Failed to load product data")}}catch(X){console.error("Error loading data:",X),v("Failed to load data")}finally{u(!1)}})()},[f,l]);const k=(x,I)=>{A(P=>({...P,[x]:I}))},E=(x,I,P)=>{A(O=>({...O,translations:{...O.translations,[x]:{...O.translations[x],[I]:P}}}))},Y=x=>{const I=Array.from(x.target.files);if(I.length===0)return;if(M.length+I.length>10){v("Maximum 10 images allowed per product");return}const P=I.map((O,X)=>({file:O,preview:URL.createObjectURL(O),alt:"",isDisplay:M.length===0&&X===0}));W(O=>[...O,...P]),v("")},V=x=>{W(I=>{var O;const P=I.filter((X,re)=>re!==x);return(O=I[x])!=null&&O.isDisplay&&P.length>0&&(P[0].isDisplay=!0,ne(0)),P})},H=x=>{W(I=>I.map((P,O)=>({...P,isDisplay:O===x}))),ne(x)},d=(x,I)=>{W(P=>P.map((O,X)=>X===x?{...O,alt:I}:O))},_=async x=>{x.preventDefault(),g(!0),v(""),C("");try{const I=localStorage.getItem("adminToken"),P=new FormData;P.append("slug",h.slug),P.append("whitelabelPrice",h.whitelabelPrice),P.append("subscriptionPrice",h.subscriptionPrice),P.append("demoUrl",h.demoUrl),P.append("status",h.status),P.append("featuredImageAlt",h.featuredImageAlt),P.append("categoryIds",JSON.stringify(h.categoryIds)),P.append("tagIds",JSON.stringify(h.tagIds)),P.append("translations",JSON.stringify(h.translations));let O=0;M.forEach(R=>{R.file&&(P.append("images",R.file),P.append(`imageAlt_${O}`,R.alt),P.append(`isDisplay_${O}`,R.isDisplay),O++)});const X=M.filter(R=>!R.file&&R.id).map(R=>({id:R.id,alt:R.alt,isDisplay:R.isDisplay,sortOrder:R.sortOrder}));X.length>0&&P.append("existingImages",JSON.stringify(X));let re;l?re=await z.updateProduct(f,P):re=await z.createProduct(P);const{response:de,data:L}=re;if(de.ok&&L&&L.success)C(`Product ${l?"updated":"created"} successfully!`),setTimeout(()=>{N("/admin/products")},2e3);else{const R=(L==null?void 0:L.message)||`Failed to ${l?"update":"create"} product`;v(R)}}catch(I){console.error("Error saving product:",I),v(`Failed to ${l?"update":"create"} product`)}finally{g(!1)}};return b?e.jsx(ee,{children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"spinner-border text-primary mb-3",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"text-muted",children:"Loading product data..."})]})})}):e.jsxs(e.Fragment,{children:[e.jsx(te,{title:`${l?"Edit":"Create"} Product | Admin`,description:"Create and manage products in the webstore"}),e.jsx(ee,{title:l?"Edit Product":"Create New Product",children:e.jsxs("form",{onSubmit:_,className:"admin-form",children:[y&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),y]}),S&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),S]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:settings-bold",className:"me-2 color-primary-1"}),"Basic Settings"]}),e.jsx("p",{className:"section-descr mb-0",children:"Configure the basic properties of your product"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"me-2"}),"Slug (URL)"]}),e.jsx("input",{type:"text",value:h.slug,onChange:x=>k("slug",x.target.value),className:"form-control",placeholder:"product-url-slug"}),e.jsx("small",{className:"form-text text-muted",children:"This will be the URL path for your product (e.g., /webstore/your-slug)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),"Publication Status"]}),e.jsxs("select",{value:h.status,onChange:x=>k("status",x.target.value),className:"form-control",children:[e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"published",children:"Published"})]}),e.jsx("small",{className:"form-text text-muted",children:"Draft products are not visible to the public"})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:dollar-minimalistic-bold",className:"me-2 color-primary-1"}),"Pricing & Demo"]}),e.jsx("p",{className:"section-descr mb-0",children:"Set pricing options and demo URL for your product"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-4 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:code-bold",className:"me-2"}),"Whitelabel Price (EUR)"]}),e.jsx("input",{type:"number",step:"0.01",value:h.whitelabelPrice,onChange:x=>k("whitelabelPrice",x.target.value),className:"form-control",placeholder:"2999.99"}),e.jsx("small",{className:"form-text text-muted",children:"Price for purchasing the source code with commercial license"})]}),e.jsxs("div",{className:"col-md-4 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"me-2"}),"Subscription Price (EUR/month)"]}),e.jsx("input",{type:"number",step:"0.01",value:h.subscriptionPrice,onChange:x=>k("subscriptionPrice",x.target.value),className:"form-control",placeholder:"99.99"}),e.jsx("small",{className:"form-text text-muted",children:"Monthly subscription price for SaaS access"})]}),e.jsxs("div",{className:"col-md-4 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:eye-bold",className:"me-2"}),"Demo URL"]}),e.jsx("input",{type:"url",value:h.demoUrl,onChange:x=>k("demoUrl",x.target.value),className:"form-control",placeholder:"https://demo.example.com"}),e.jsx("small",{className:"form-text text-muted",children:"Link to live demo of the product"})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:gallery-bold",className:"me-2 color-primary-1"}),"Featured Image"]}),e.jsx("p",{className:"section-descr mb-0",children:"Upload a featured image that will be displayed with your product"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:upload-bold",className:"me-2"}),"Upload Images (Max 10)"]}),e.jsx("input",{type:"file",accept:"image/*",multiple:!0,onChange:Y,className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Recommended size: 800x600px. Max file size: 5MB per image. Maximum 10 images."})]}),M.length>0&&e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("h6",{className:"mb-3",children:["Product Images (",M.length,"/10)"]}),e.jsx("div",{className:"row",children:M.map((x,I)=>e.jsx("div",{className:"col-md-4 mb-3",children:e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"position-relative",children:[e.jsx("img",{src:x.preview,alt:`Preview ${I+1}`,className:"card-img-top",style:{height:"200px",objectFit:"cover"},onError:P=>{console.error("Failed to load image:",x.preview),P.target.style.display="none"},onLoad:()=>{console.log("Successfully loaded image:",x.preview)}}),e.jsx("button",{type:"button",className:"btn btn-danger btn-sm position-absolute top-0 end-0 m-2",onClick:()=>V(I),children:"×"}),x.isDisplay&&e.jsx("span",{className:"badge bg-primary position-absolute top-0 start-0 m-2",children:"Display Image"})]}),e.jsxs("div",{className:"card-body",children:[e.jsx("div",{className:"mb-2",children:e.jsx("input",{type:"text",className:"form-control form-control-sm",placeholder:"Alt text",value:x.alt,onChange:P=>d(I,P.target.value)})}),e.jsx("div",{className:"d-flex gap-2",children:!x.isDisplay&&e.jsx("button",{type:"button",className:"btn btn-outline-primary btn-sm",onClick:()=>H(I),children:"Set as Display"})})]})]})},I))})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("i",{className:"mi-globe me-2 color-primary-1"}),"Content (Multi-language)"]}),e.jsx("p",{className:"section-descr mb-0",children:"Create content in multiple languages. At least English content is required."})]})}),e.jsx("div",{className:"language-tabs mb-30",children:T.map(x=>e.jsxs("button",{type:"button",onClick:()=>j(x),className:`language-tab ${a===x?"active":""}`,children:[e.jsx("i",{className:"mi-globe me-2"}),x.toUpperCase(),x==="en"&&e.jsx("span",{className:"ms-1 small",children:"(Required)"})]},x))}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Title (",a.toUpperCase(),")",a==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx("input",{type:"text",value:((D=h.translations[a])==null?void 0:D.title)||"",onChange:x=>E(a,"title",x.target.value),className:"form-control",placeholder:"Enter product title",required:a==="en"}),e.jsxs("small",{className:"form-text text-muted",children:["The main title of your product in"," ",a.toUpperCase()]})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-text me-2"}),"Excerpt (",a.toUpperCase(),")"]}),e.jsx("textarea",{value:((B=h.translations[a])==null?void 0:B.excerpt)||"",onChange:x=>E(a,"excerpt",x.target.value),rows:3,className:"form-control",placeholder:"Brief description of the product"}),e.jsx("small",{className:"form-text text-muted",children:"A short summary that will appear in product listings and social media previews"})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-2"}),"Content (",a.toUpperCase(),")",a==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx(Pe,{content:((ae=h.translations[a])==null?void 0:ae.content)||"",onChange:x=>E(a,"content",x),placeholder:"Write your product description here. You can paste formatted text and code snippets with syntax highlighting."}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("iconify-icon",{icon:"solar:info-circle-bold",className:"me-1"}),"Rich text editor with syntax highlighting. Paste code snippets and they will be automatically highlighted. Use the toolbar for formatting options."]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Title (",a.toUpperCase(),")"]}),e.jsx("input",{type:"text",value:((Q=h.translations[a])==null?void 0:Q.metaTitle)||"",onChange:x=>E(a,"metaTitle",x.target.value),className:"form-control",placeholder:"SEO title (optional)",maxLength:"60"}),e.jsx("small",{className:"form-text text-muted",children:"SEO title for search engines (max 60 characters)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Description (",a.toUpperCase(),")"]}),e.jsx("textarea",{value:((U=h.translations[a])==null?void 0:U.metaDesc)||"",onChange:x=>E(a,"metaDesc",x.target.value),rows:3,className:"form-control",placeholder:"SEO description (optional)",maxLength:"160"}),e.jsx("small",{className:"form-text text-muted",children:"SEO description for search engines (max 160 characters)"})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-2 color-primary-1"}),"Categories & Tags"]}),e.jsx("p",{className:"section-descr mb-0",children:"Organize your product with categories and tags"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"me-2"}),"Categories"]}),e.jsx("div",{className:"categories-grid",children:i&&i.length>0?i.map(x=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`category-${x.id}`,checked:h.categoryIds.includes(x.id),onChange:()=>{const I=h.categoryIds.includes(x.id)?h.categoryIds.filter(P=>P!==x.id):[...h.categoryIds,x.id];k("categoryIds",I)}}),e.jsx("label",{className:"form-check-label",htmlFor:`category-${x.id}`,children:x.name})]},x.id)):e.jsx("p",{className:"text-muted",children:"No categories available"})})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:hashtag-bold",className:"me-2"}),"Tags"]}),e.jsx("div",{className:"tags-grid",children:t&&t.length>0?t.map(x=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`tag-${x.id}`,checked:h.tagIds.includes(x.id),onChange:()=>{const I=h.tagIds.includes(x.id)?h.tagIds.filter(P=>P!==x.id):[...h.tagIds,x.id];k("tagIds",I)}}),e.jsx("label",{className:"form-check-label",htmlFor:`tag-${x.id}`,children:x.name})]},x.id)):e.jsx("p",{className:"text-muted",children:"No tags available"})})]})]})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("button",{type:"submit",className:"btn btn-mod btn-color btn-large btn-round",disabled:w,children:w?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"spinner-border spinner-border-sm me-2"}),l?"Updating...":"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-check me-2"}),l?"Update Product":"Create Product"]})}),e.jsx("div",{className:"mt-3",children:e.jsxs("small",{className:"text-muted",children:[e.jsx("i",{className:"mi-info me-1"}),l?"Changes will be saved and the product will be updated":"The product will be created and added to your webstore"]})})]})})]})})]})},Js=Object.freeze(Object.defineProperty({__proto__:null,default:fs},Symbol.toStringTag,{value:"Module"})),vs=()=>{var i;const{t:s}=se(),[m,N]=c.useState(!0),[f,l]=c.useState(""),[n,b]=c.useState("last30days"),[u,w]=c.useState("all"),[g,y]=c.useState(null),[v,S]=c.useState([]),C=[{value:"lastday",label:"Last day"},{value:"lastweek",label:"Last week"},{value:"last14days",label:"Last 14 days"},{value:"last30days",label:"Last 30 days"},{value:"last2months",label:"Last 2 months"},{value:"last4months",label:"Last 4 months"},{value:"last6months",label:"Last 6 months"}],T=[{value:"all",label:"All languages",flag:"🌐"},{value:"en",label:"English",flag:"🇬🇧"},{value:"et",label:"Estonian",flag:"🇪🇪"},{value:"fi",label:"Finnish",flag:"🇫🇮"},{value:"de",label:"German",flag:"🇩🇪"},{value:"sv",label:"Swedish",flag:"🇸🇪"}];c.useEffect(()=>{(async()=>{try{if(N(!0),l(""),!localStorage.getItem("adminToken")){l("Authentication required. Please log in to access this page."),N(!1);return}const[r,a]=await Promise.all([z.getBlogAnalytics(n,u),z.getBlogPostsAnalytics(n,u)]);if(r.response.ok&&r.data)y(r.data.data||r.data);else{if(console.error("Analytics API failed:",r.response.status,r.response.statusText),r.response.status===401||r.response.status===403){l("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}l("Failed to load analytics data")}a.response.ok&&a.data?S(a.data.data||a.data):(console.error("Posts analytics API failed:",a.response.status,a.response.statusText),S([]))}catch(t){console.error("Error loading analytics data:",t),t.message&&t.message.includes("fetch")?l("Failed to connect to the server. Please check if the backend is running."):l("Failed to load analytics data. Please try again.")}finally{N(!1)}})()},[n,u]);const h=p=>{b(p)},A=p=>{w(p)};return m?e.jsxs(ee,{title:"Blog Analytics",children:[e.jsx(te,{title:"Blog Analytics - Admin",description:"Blog analytics and performance metrics"}),e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})})]}):e.jsxs(ee,{title:"Analytics Dashboard",children:[e.jsx(te,{title:"Analytics Dashboard - Admin",description:"Blog analytics, conversion tracking, and page performance metrics"}),e.jsxs("div",{className:"admin-content",children:[e.jsx("div",{className:"admin-header mb-4",children:e.jsx("div",{className:"d-flex justify-content-between align-items-center",children:e.jsxs("div",{children:[e.jsx("h1",{className:"admin-title mb-2",children:"Analytics Dashboard"}),e.jsxs("p",{className:"admin-subtitle text-muted mb-0",children:["Track and analyze your blog performance, conversions, and page analytics."," ",e.jsx("a",{href:"https://developers.google.com/analytics/devguides/collection/ga4",target:"_blank",rel:"noopener noreferrer",className:"text-primary",children:"Learn more"})]})]})})}),f&&e.jsxs("div",{className:"alert alert-danger mb-4",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-bold",className:"me-2"}),f]}),e.jsxs("div",{className:"row mb-4",children:[e.jsx("div",{className:"col-md-6",children:e.jsx(We,{options:C,value:n,onChange:h,comparedPeriod:g==null?void 0:g.comparedPeriod})}),e.jsx("div",{className:"col-md-6",children:e.jsx(He,{options:T,value:u,onChange:A})})]}),g&&e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsx(qe,{data:g.overview,selectedLanguage:u})})}),e.jsxs("div",{className:"row mb-4",children:[e.jsx("div",{className:"col-lg-8 col-12 mb-4",children:g&&e.jsx(Ge,{data:g.chartData,timeRange:n,selectedLanguage:u})}),e.jsx("div",{className:"col-lg-4 col-12 mb-4",children:g&&e.jsx(Ve,{data:g.heatmapData,title:`Post views by time of day${u!=="all"?` (${(i=T.find(p=>p.value===u))==null?void 0:i.label})`:""}`,selectedLanguage:u})})]}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsx(Je,{data:v,loading:m,timeRange:n,selectedLanguage:u})})}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"card-header",children:[e.jsxs("h3",{className:"card-title mb-0",children:[e.jsx("iconify-icon",{icon:"solar:target-bold",className:"me-2"}),"Conversion Analytics"]}),e.jsx("p",{className:"text-muted mb-0 mt-1",children:"Track Business Comanager CTA performance and conversion metrics"})]}),e.jsx("div",{className:"card-body",children:e.jsx(Ke,{timeRange:n,selectedLanguage:u})})]})})}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"card-header",children:[e.jsxs("h3",{className:"card-title mb-0",children:[e.jsx("iconify-icon",{icon:"solar:document-bold",className:"me-2"}),"Static Pages Analytics"]}),e.jsx("p",{className:"text-muted mb-0 mt-1",children:"Performance metrics for all application pages"})]}),e.jsx("div",{className:"card-body",children:e.jsx(Ye,{timeRange:n,selectedLanguage:u})})]})})})]})]})},Ks=Object.freeze(Object.defineProperty({__proto__:null,default:vs},Symbol.toStringTag,{value:"Module"})),ys=()=>{le();const[s,m]=c.useState([]),[N,f]=c.useState(!0),[l,n]=c.useState(""),[b,u]=c.useState(""),[w,g]=c.useState(!1),[y,v]=c.useState(null),[S,C]=c.useState({name:"",description:"",color:"#4567e7"});c.useEffect(()=>{T()},[]);const T=async()=>{try{f(!0);const{response:a,data:j}=await z.getCategories();j.success?m(j.data||[]):n(j.message||"Failed to load categories")}catch(a){console.error("Load categories error:",a),n("Network error. Please try again.")}finally{f(!1)}},h=(a,j)=>{C(o=>({...o,[a]:j}))},A=async a=>{a.preventDefault(),n(""),u("");try{let j,o;y?{response:j,data:o}=await z.updateCategory(y.id,S):{response:j,data:o}=await z.createCategory(S),o.success?(u(y?"Category updated successfully!":"Category created successfully!"),g(!1),v(null),C({name:"",description:"",color:"#4567e7"}),T()):n(o.message||"Failed to save category")}catch(j){console.error("Save category error:",j),n("Network error. Please try again.")}},i=a=>{v(a),C({name:a.name,description:a.description||"",color:a.color||"#4567e7"}),g(!0)},p=async a=>{if(window.confirm("Are you sure you want to delete this category? This action cannot be undone."))try{const{response:j,data:o}=await z.deleteCategory(a);o.success?(u("Category deleted successfully!"),T()):n(o.message||"Failed to delete category")}catch(j){console.error("Delete category error:",j),n("Network error. Please try again.")}},t=()=>{v(null),C({name:"",description:"",color:"#4567e7"}),g(!0)},r=()=>{g(!1),v(null),C({name:"",description:"",color:"#4567e7"}),n("")};return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:"Manage Categories - Admin",description:"Manage blog categories in the admin panel",noIndex:!0}),e.jsxs(ee,{title:"Categories",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Organize your blog posts with categories. Create, edit, and manage content categories."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:t,className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Category"]})})]})}),l&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),l]}),b&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),b]}),e.jsx("div",{className:"admin-table",children:N?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading categories..."})]}):s.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No categories found"}),e.jsx("p",{className:"section-descr mb-30",children:"Create your first category to start organizing your blog posts."}),e.jsxs("button",{onClick:t,className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Category"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Category"}),e.jsx("th",{children:"Description"}),e.jsx("th",{children:"Posts"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:s.map(a=>{var j;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"rounded me-3",style:{width:"20px",height:"20px",backgroundColor:a.color||"#4567e7"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:a.name}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsx("td",{children:e.jsx("span",{className:"text-muted",children:a.description||"No description"})}),e.jsx("td",{children:e.jsxs("span",{className:"badge bg-secondary",children:[((j=a._count)==null?void 0:j.posts)||0," posts"]})}),e.jsx("td",{children:new Date(a.createdAt).toLocaleDateString()}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>i(a),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>p(a.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},a.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:s.map(a=>{var j;return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"rounded me-3",style:{width:"30px",height:"30px",backgroundColor:a.color||"#4567e7"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:a.name}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Description"}),e.jsx("small",{children:a.description||"No description"})]}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Posts"}),e.jsxs("span",{className:"badge bg-secondary",children:[((j=a._count)==null?void 0:j.posts)||0," posts"]})]}),e.jsxs("div",{className:"col-12 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:new Date(a.createdAt).toLocaleDateString()})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>i(a),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>p(a.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},a.id)})})})]})}),w&&e.jsx("div",{className:"modal-overlay",onClick:r,children:e.jsxs("div",{className:"modal-content",onClick:a=>a.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h4",{className:"modal-title",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"me-2"}),y?"Edit Category":"Create New Category"]}),e.jsx("button",{type:"button",className:"modal-close",onClick:r,children:e.jsx("iconify-icon",{icon:"solar:close-circle-bold"})})]}),e.jsxs("form",{onSubmit:A,children:[e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-2"}),"Category Name *"]}),e.jsx("input",{type:"text",value:S.name,onChange:a=>h("name",a.target.value),className:"form-control",placeholder:"Enter category name",required:!0})]}),e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:text-bold",className:"me-2"}),"Description"]}),e.jsx("textarea",{value:S.description,onChange:a=>h("description",a.target.value),className:"form-control",rows:3,placeholder:"Brief description of this category"})]}),e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:palette-bold",className:"me-2"}),"Color"]}),e.jsxs("div",{className:"d-flex align-items-center gap-3",children:[e.jsx("input",{type:"color",value:S.color,onChange:a=>h("color",a.target.value),className:"form-control form-control-color",style:{width:"60px",height:"40px"}}),e.jsx("input",{type:"text",value:S.color,onChange:a=>h("color",a.target.value),className:"form-control",placeholder:"#4567e7"})]}),e.jsx("small",{className:"form-text text-muted",children:"Choose a color to represent this category"})]})]})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",onClick:r,className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsxs("button",{type:"submit",className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),y?"Update Category":"Create Category"]})]})]})]})})]})]})},Ys=Object.freeze(Object.defineProperty({__proto__:null,default:ys},Symbol.toStringTag,{value:"Module"})),ws=()=>{le();const[s,m]=c.useState([]),[N,f]=c.useState(!0),[l,n]=c.useState(""),[b,u]=c.useState(""),[w,g]=c.useState(!1),[y,v]=c.useState(null),[S,C]=c.useState({name:""});c.useEffect(()=>{T()},[]);const T=async()=>{try{f(!0);const{response:a,data:j}=await z.getTags();j.success?m(j.data||[]):n(j.message||"Failed to load tags")}catch(a){console.error("Load tags error:",a),n("Network error. Please try again.")}finally{f(!1)}},h=(a,j)=>{C(o=>({...o,[a]:j}))},A=async a=>{a.preventDefault(),n(""),u("");try{let j,o;y?{response:j,data:o}=await z.updateTag(y.id,S):{response:j,data:o}=await z.createTag(S),o.success?(u(y?"Tag updated successfully!":"Tag created successfully!"),g(!1),v(null),C({name:""}),T()):n(o.message||"Failed to save tag")}catch(j){console.error("Save tag error:",j),n("Network error. Please try again.")}},i=a=>{v(a),C({name:a.name}),g(!0)},p=async a=>{if(window.confirm("Are you sure you want to delete this tag? This action cannot be undone."))try{const{response:j,data:o}=await z.deleteTag(a);o.success?(u("Tag deleted successfully!"),T()):n(o.message||"Failed to delete tag")}catch(j){console.error("Delete tag error:",j),n("Network error. Please try again.")}},t=()=>{v(null),C({name:""}),g(!0)},r=()=>{g(!1),v(null),C({name:""}),n("")};return e.jsxs(e.Fragment,{children:[e.jsx(te,{title:"Manage Tags - Admin",description:"Manage blog tags in the admin panel",noIndex:!0}),e.jsxs(ee,{title:"Tags",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Tag your blog posts for better organization and discoverability. Create and manage content tags."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:t,className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Tag"]})})]})}),l&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),l]}),b&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),b]}),e.jsx("div",{className:"admin-table",children:N?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading tags..."})]}):s.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No tags found"}),e.jsx("p",{className:"section-descr mb-30",children:"Create your first tag to start organizing your blog posts."}),e.jsxs("button",{onClick:t,className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Tag"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Tag Name"}),e.jsx("th",{children:"Posts"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:s.map(a=>{var j;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-3 color-primary-1"}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:a.name}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsx("td",{children:e.jsxs("span",{className:"badge bg-secondary",children:[((j=a._count)==null?void 0:j.posts)||0," posts"]})}),e.jsx("td",{children:new Date(a.createdAt).toLocaleDateString()}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>i(a),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>p(a.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},a.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:s.map(a=>{var j;return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-3 color-primary-1",style:{fontSize:"1.5rem"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:a.name}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-6 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Posts"}),e.jsxs("span",{className:"badge bg-secondary",children:[((j=a._count)==null?void 0:j.posts)||0," posts"]})]}),e.jsxs("div",{className:"col-6 col-sm-6 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:new Date(a.createdAt).toLocaleDateString()})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>i(a),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>p(a.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},a.id)})})})]})}),w&&e.jsx("div",{className:"modal-overlay",onClick:r,children:e.jsxs("div",{className:"modal-content",onClick:a=>a.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h4",{className:"modal-title",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-2"}),y?"Edit Tag":"Create New Tag"]}),e.jsx("button",{type:"button",className:"modal-close",onClick:r,children:e.jsx("iconify-icon",{icon:"solar:close-circle-bold"})})]}),e.jsxs("form",{onSubmit:A,children:[e.jsx("div",{className:"modal-body",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-2"}),"Tag Name *"]}),e.jsx("input",{type:"text",value:S.name,onChange:a=>h("name",a.target.value),className:"form-control",placeholder:"Enter tag name",required:!0}),e.jsx("small",{className:"form-text text-muted",children:'Keep it short and descriptive (e.g., "JavaScript", "Tutorial", "News")'})]})})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",onClick:r,className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsxs("button",{type:"submit",className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),y?"Update Tag":"Create Tag"]})]})]})]})})]})]})},Qs=Object.freeze(Object.defineProperty({__proto__:null,default:ws},Symbol.toStringTag,{value:"Module"})),ks=async(s={})=>{try{const m=new URLSearchParams;s.page&&m.append("page",s.page),s.limit&&m.append("limit",s.limit),s.status&&m.append("status",s.status),s.search&&m.append("search",s.search),s.blogPostId&&m.append("blogPostId",s.blogPostId);const{response:N,data:f}=await be(`/admin/comments?${m}`);if(!N.ok)throw new Error(`HTTP error! status: ${N.status}`);return f}catch(m){throw console.error("Get admin comments error:",m),m}},Ss=async s=>{try{const{response:m,data:N}=await be(`/admin/comments/${s}/approve`,{method:"PATCH"});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);return N}catch(m){throw console.error("Approve comment error:",m),m}},Ps=async s=>{try{const{response:m,data:N}=await be(`/admin/comments/${s}/reject`,{method:"PATCH"});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);return N}catch(m){throw console.error("Reject comment error:",m),m}},Cs=async s=>{try{const{response:m,data:N}=await be(`/admin/comments/${s}`,{method:"DELETE"});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);return N}catch(m){throw console.error("Delete comment error:",m),m}};function Is(){const[s,m]=c.useState([]),[N,f]=c.useState(!0),[l,n]=c.useState(""),[b,u]=c.useState(1),[w,g]=c.useState(1),[y,v]=c.useState("all"),[S,C]=c.useState(""),[T,h]=c.useState({total:0,pending:0,approved:0}),A=async()=>{try{f(!0);const o=await ks({page:b,limit:10,status:y,search:S});if(o.success){m(o.data.comments),g(o.data.pagination.pages);const F=o.data.pagination.total;h(M=>({...M,total:F}))}}catch(o){n("Failed to fetch comments"),console.error("Fetch comments error:",o)}finally{f(!1)}};c.useEffect(()=>{A()},[b,y,S]);const i=async o=>{try{await Ss(o),A()}catch{n("Failed to approve comment")}},p=async o=>{try{await Ps(o),A()}catch{n("Failed to reject comment")}},t=async o=>{if(window.confirm("Are you sure you want to delete this comment? This action cannot be undone."))try{await Cs(o),A()}catch{n("Failed to delete comment")}},r=o=>{o.preventDefault(),u(1),A()},a=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),j=(o,F=100)=>o.length>F?o.substring(0,F)+"...":o;return e.jsx(ee,{children:e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[e.jsx("h1",{className:"h3 mb-0",children:"Comment Management"}),e.jsxs("div",{className:"d-flex gap-3",children:[e.jsxs("div",{className:"badge bg-primary",children:["Total: ",T.total]}),e.jsxs("div",{className:"badge bg-warning",children:["Pending: ",T.pending]}),e.jsxs("div",{className:"badge bg-success",children:["Approved: ",T.approved]})]})]}),l&&e.jsx("div",{className:"alert alert-danger",role:"alert",children:l}),e.jsx("div",{className:"card mb-4",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"row g-3",children:[e.jsxs("div",{className:"col-md-4",children:[e.jsx("label",{className:"form-label",children:"Status Filter"}),e.jsxs("select",{className:"form-select",value:y,onChange:o=>{v(o.target.value),u(1)},children:[e.jsx("option",{value:"all",children:"All Comments"}),e.jsx("option",{value:"pending",children:"Pending Approval"}),e.jsx("option",{value:"approved",children:"Approved"})]})]}),e.jsxs("div",{className:"col-md-8",children:[e.jsx("label",{className:"form-label",children:"Search"}),e.jsxs("form",{onSubmit:r,className:"d-flex",children:[e.jsx("input",{type:"text",className:"form-control",placeholder:"Search by author name, email, or content...",value:S,onChange:o=>C(o.target.value)}),e.jsx("button",{type:"submit",className:"btn btn-primary ms-2",children:"Search"})]})]})]})})}),e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[N?e.jsx("div",{className:"text-center py-4",children:e.jsx("div",{className:"spinner-border",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})}):s.length===0?e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-muted",children:"No comments found."})}):e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table table-hover",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Author"}),e.jsx("th",{children:"Email"}),e.jsx("th",{children:"Content"}),e.jsx("th",{children:"Blog Post"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:s.map(o=>{var F;return e.jsxs("tr",{children:[e.jsxs("td",{children:[e.jsx("strong",{children:o.author}),o.website&&e.jsx("div",{children:e.jsx("small",{children:e.jsx("a",{href:o.website,target:"_blank",rel:"noopener noreferrer",children:o.website})})})]}),e.jsx("td",{children:e.jsx("small",{className:"text-muted",children:o.email})}),e.jsxs("td",{children:[e.jsx("div",{title:o.content,children:j(o.content)}),o.parent&&e.jsxs("small",{className:"text-muted",children:["Reply to: ",o.parent.author]})]}),e.jsx("td",{children:e.jsx(G,{to:`/blog-single/${o.blogPost.slug}`,className:"text-decoration-none",target:"_blank",children:((F=o.blogPost.translations[0])==null?void 0:F.title)||"Untitled"})}),e.jsx("td",{children:e.jsx("span",{className:`badge ${o.approved?"bg-success":"bg-warning"}`,children:o.approved?"Approved":"Pending"})}),e.jsx("td",{children:e.jsx("small",{children:a(o.createdAt)})}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group btn-group-sm",children:[o.approved?e.jsx("button",{className:"btn btn-warning",onClick:()=>p(o.id),title:"Hide",children:e.jsx("i",{className:"mi-eye-off"})}):e.jsx("button",{className:"btn btn-success",onClick:()=>i(o.id),title:"Approve",children:e.jsx("i",{className:"mi-check"})}),e.jsx("button",{className:"btn btn-danger",onClick:()=>t(o.id),title:"Delete",children:e.jsx("i",{className:"mi-trash"})})]})})]},o.id)})})]})}),w>1&&e.jsx("nav",{className:"mt-4",children:e.jsxs("ul",{className:"pagination justify-content-center",children:[e.jsx("li",{className:`page-item ${b===1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>u(b-1),disabled:b===1,children:"Previous"})}),[...Array(w)].map((o,F)=>e.jsx("li",{className:`page-item ${b===F+1?"active":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>u(F+1),children:F+1})},F+1)),e.jsx("li",{className:`page-item ${b===w?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>u(b+1),disabled:b===w,children:"Next"})})]})})]})})]})})})})}const Xs=Object.freeze(Object.defineProperty({__proto__:null,default:Is},Symbol.toStringTag,{value:"Module"}));export{Ws as A,_s as H,Ze as _,Ls as a,$s as b,Us as c,Os as d,Ms as e,Bs as f,Ne as g,zs as h,Rs as i,Hs as j,qs as k,Gs as l,K as m,Vs as n,Js as o,Fs as p,Ks as q,Ys as r,Qs as s,Xs as t};
//# sourceMappingURL=pages-other-BXFHPpbx.js.map
