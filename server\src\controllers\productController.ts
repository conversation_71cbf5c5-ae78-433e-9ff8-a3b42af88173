import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import slugify from "slugify";

const prisma = new PrismaClient();

// @desc    Get all products
// @route   GET /api/products
// @access  Public
export const getProducts = async (req: Request, res: Response) => {
  try {
    const {
      language = "en",
      status = "published",
      category,
      tag,
      limit = "10",
      offset = "0",
    } = req.query;

    const whereClause: any = {
      status: status as string,
    };

    // Filter by language through translations
    whereClause.translations = {
      some: {
        language: language as string,
      },
    };

    if (category) {
      whereClause.categories = {
        some: {
          category: {
            slug: category as string,
          },
        },
      };
    }

    if (tag) {
      whereClause.tags = {
        some: {
          tag: {
            slug: tag as string,
          },
        },
      };
    }

    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        translations: {
          where: {
            language: language as string,
          },
        },
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
        images: {
          orderBy: { sortOrder: "asc" },
        },
      },
      orderBy: {
        publishedAt: "desc",
      },
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    });

    const totalCount = await prisma.product.count({
      where: whereClause,
    });

    // Format products with the translation for the requested language
    const formattedProducts = products.map((product) => {
      const translation = product.translations[0]; // Should only be one for the requested language
      const displayImage =
        product.images?.find((img) => img.isDisplay) || product.images?.[0];

      return {
        id: product.id,
        slug: product.slug,
        title: translation?.title || "",
        excerpt: translation?.excerpt || "",
        content: translation?.content || "",
        metaTitle: translation?.metaTitle || "",
        metaDesc: translation?.metaDesc || "",
        keywords: translation?.keywords || [],
        featuredImage: displayImage?.filename || product.featuredImage,
        featuredImageAlt: displayImage?.alt || product.featuredImageAlt,
        whitelabelPrice: product.whitelabelPrice,
        subscriptionPrice: product.subscriptionPrice,
        demoUrl: product.demoUrl,
        status: product.status,
        readingTime: product.readingTime,
        viewCount: product.viewCount,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        publishedAt: product.publishedAt,
        categories: product.categories,
        tags: product.tags,
        images: product.images,
        language: language as string,
      };
    });

    res.json({
      success: true,
      products: formattedProducts,
      pagination: {
        total: totalCount,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore:
          parseInt(offset as string) + parseInt(limit as string) < totalCount,
      },
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch products",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// @desc    Get single product by slug
// @route   GET /api/products/:slug
// @access  Public
export const getProductBySlug = async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const { language = "en" } = req.query;

    const product = await prisma.product.findFirst({
      where: {
        slug: slug,
        status: "published",
        translations: {
          some: {
            language: language as string,
          },
        },
      },
      include: {
        translations: {
          where: {
            language: language as string,
          },
        },
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
        images: {
          orderBy: { sortOrder: "asc" },
        },
      },
    });

    if (!product || product.translations.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    // Increment view count
    await prisma.product.update({
      where: { id: product.id },
      data: { viewCount: { increment: 1 } },
    });

    // Format product with the translation for the requested language
    const translation = product.translations[0];
    const displayImage =
      product.images?.find((img) => img.isDisplay) || product.images?.[0];

    const formattedProduct = {
      id: product.id,
      slug: product.slug,
      title: translation.title,
      excerpt: translation.excerpt,
      content: translation.content,
      metaTitle: translation.metaTitle,
      metaDesc: translation.metaDesc,
      keywords: translation.keywords,
      featuredImage: displayImage?.filename || product.featuredImage,
      featuredImageAlt: displayImage?.alt || product.featuredImageAlt,
      whitelabelPrice: product.whitelabelPrice,
      subscriptionPrice: product.subscriptionPrice,
      demoUrl: product.demoUrl,
      status: product.status,
      readingTime: product.readingTime,
      viewCount: product.viewCount,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      publishedAt: product.publishedAt,
      categories: product.categories,
      tags: product.tags,
      images: product.images,
      language: language as string,
    };

    res.json({
      success: true,
      product: formattedProduct,
    });
  } catch (error) {
    console.error("Error fetching product:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch product",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// @desc    Create new product
// @route   POST /api/admin/products
// @access  Private (Admin)
export const createProduct = async (req: any, res: Response) => {
  try {
    // Parse FormData fields (multer puts form fields in req.body)
    const formData = { ...req.body };

    // Parse JSON strings from FormData
    try {
      if (formData.categoryIds) {
        formData.categoryIds = JSON.parse(formData.categoryIds);
      }
      if (formData.tagIds) {
        formData.tagIds = JSON.parse(formData.tagIds);
      }
      if (formData.translations) {
        formData.translations = JSON.parse(formData.translations);
      }

      // Convert price strings to numbers
      if (formData.whitelabelPrice) {
        formData.whitelabelPrice = parseFloat(formData.whitelabelPrice);
      }
      if (formData.subscriptionPrice) {
        formData.subscriptionPrice = parseFloat(formData.subscriptionPrice);
      }
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: "Invalid JSON in form data",
      });
    }

    const {
      slug: providedSlug,
      whitelabelPrice,
      subscriptionPrice,
      demoUrl,
      status = "draft",
      featuredImageAlt,
      categoryIds = [],
      tagIds = [],
      translations,
    } = formData;

    // Custom validation: Ensure English content is provided
    if (
      !translations?.en ||
      !translations.en.title ||
      !translations.en.content
    ) {
      return res.status(400).json({
        success: false,
        message: "English title and content are required",
      });
    }

    // Filter out empty translations (keep only languages with content)
    const validTranslations = Object.entries(translations).reduce(
      (acc, [lang, translation]) => {
        const typedTranslation = translation as any;
        if (typedTranslation.title && typedTranslation.content) {
          acc[lang] = translation;
        }
        return acc;
      },
      {} as any
    );

    // Generate slug from provided slug or English title, ensure no leading slashes
    const baseSlug =
      providedSlug ||
      slugify(translations.en.title, { lower: true, strict: true });
    const slug = baseSlug.replace(/^\/+/, ""); // Remove leading slashes

    // Check if slug already exists
    const existingProduct = await prisma.product.findFirst({
      where: { slug },
    });

    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: "A product with this slug already exists",
      });
    }

    // Handle multiple images from request
    const uploadedImages = [];
    if (req.files && Array.isArray(req.files)) {
      req.files.forEach((file, index) => {
        const isDisplay =
          formData[`isDisplay_${index}`] === "true" ||
          (index === 0 &&
            !req.files.some((_, i) => formData[`isDisplay_${i}`] === "true"));
        uploadedImages.push({
          filename: file.filename,
          alt: formData[`imageAlt_${index}`] || "",
          sortOrder: index,
          isDisplay,
        });
        console.log(
          `📸 Uploaded product image: ${file.filename} (Display: ${isDisplay})`
        );
      });
    }

    // Handle featured image (for backward compatibility)
    let featuredImage = null;
    if (req.file) {
      featuredImage = req.file.filename;
      console.log(`📸 Uploaded featured image: ${req.file.filename}`);
    }

    // Calculate reading time for English content (rough estimate: 200 words per minute)
    const wordCount = translations.en.content
      .replace(/<[^>]*>/g, "")
      .split(/\s+/).length;
    const readingTime = Math.max(1, Math.ceil(wordCount / 200));

    // Create product with translations
    const product = await prisma.product.create({
      data: {
        slug,
        whitelabelPrice,
        subscriptionPrice,
        demoUrl,
        status,
        featuredImage,
        featuredImageAlt,
        readingTime,
        publishedAt: status === "published" ? new Date() : null,
        translations: {
          create: Object.entries(validTranslations).map(
            ([language, translation]: [string, any]) => ({
              language,
              title: translation.title,
              excerpt: translation.excerpt || "",
              content: translation.content,
              metaTitle: translation.metaTitle || translation.title,
              metaDesc: translation.metaDesc || translation.excerpt || "",
              keywords: translation.keywords || [],
            })
          ),
        },
        categories: {
          create: categoryIds.map((categoryId: string) => ({
            categoryId,
          })),
        },
        tags: {
          create: tagIds.map((tagId: string) => ({
            tagId,
          })),
        },
        images: {
          create: uploadedImages,
        },
      },
      include: {
        translations: true,
        categories: {
          include: { category: true },
        },
        tags: {
          include: { tag: true },
        },
        images: {
          orderBy: { sortOrder: "asc" },
        },
      },
    });

    res.status(201).json({
      success: true,
      message: "Product created successfully",
      product,
    });
  } catch (error) {
    console.error("Error creating product:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create product",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// @desc    Update product
// @route   PUT /api/admin/products/:id
// @access  Private (Admin)
export const updateProduct = async (req: any, res: Response) => {
  try {
    const { id } = req.params;

    // Parse FormData fields (multer puts form fields in req.body)
    const formData = { ...req.body, id };

    // Parse JSON strings from FormData
    try {
      if (formData.categoryIds) {
        formData.categoryIds = JSON.parse(formData.categoryIds);
      }
      if (formData.tagIds) {
        formData.tagIds = JSON.parse(formData.tagIds);
      }
      if (formData.translations) {
        formData.translations = JSON.parse(formData.translations);
      }

      // Convert price strings to numbers
      if (formData.whitelabelPrice) {
        formData.whitelabelPrice = parseFloat(formData.whitelabelPrice);
      }
      if (formData.subscriptionPrice) {
        formData.subscriptionPrice = parseFloat(formData.subscriptionPrice);
      }
    } catch (parseError) {
      return res.status(400).json({
        success: false,
        message: "Invalid JSON in form data",
      });
    }

    const {
      slug: providedSlug,
      whitelabelPrice,
      subscriptionPrice,
      demoUrl,
      status,
      featuredImageAlt,
      categoryIds = [],
      tagIds = [],
      translations,
    } = formData;

    // Custom validation: Ensure English content is provided
    if (
      !translations?.en ||
      !translations.en.title ||
      !translations.en.content
    ) {
      return res.status(400).json({
        success: false,
        message: "English title and content are required",
      });
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
      include: { translations: true },
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    // Filter out empty translations (keep only languages with content)
    const validTranslations = Object.entries(translations).reduce(
      (acc, [lang, translation]) => {
        const typedTranslation = translation as any;
        if (typedTranslation.title && typedTranslation.content) {
          acc[lang] = translation;
        }
        return acc;
      },
      {} as any
    );

    // Generate slug if provided or keep existing
    let slug = existingProduct.slug;
    if (providedSlug && providedSlug !== existingProduct.slug) {
      const baseSlug = slugify(providedSlug || translations.en.title, {
        lower: true,
        strict: true,
      });
      slug = baseSlug.replace(/^\/+/, ""); // Remove leading slashes

      // Check if new slug already exists
      const slugExists = await prisma.product.findFirst({
        where: { slug, id: { not: id } },
      });

      if (slugExists) {
        return res.status(400).json({
          success: false,
          message: "A product with this slug already exists",
        });
      }
    }

    // Handle multiple images from request
    const uploadedImages = [];
    if (req.files && Array.isArray(req.files)) {
      req.files.forEach((file, index) => {
        const isDisplay =
          formData[`isDisplay_${index}`] === "true" ||
          (index === 0 &&
            !req.files.some((_, i) => formData[`isDisplay_${i}`] === "true"));
        uploadedImages.push({
          filename: file.filename,
          alt: formData[`imageAlt_${index}`] || "",
          sortOrder: index,
          isDisplay,
        });
        console.log(
          `📸 Uploaded product image: ${file.filename} (Display: ${isDisplay})`
        );
      });
    }

    // Handle existing images
    let existingImages = [];
    if (formData.existingImages) {
      try {
        existingImages = JSON.parse(formData.existingImages);
      } catch (e) {
        console.error("Failed to parse existing images:", e);
      }
    }

    // Handle featured image (for backward compatibility)
    let featuredImage = existingProduct.featuredImage;
    if (req.file) {
      featuredImage = req.file.filename;
      console.log(`📸 Updated featured image: ${req.file.filename}`);
    }

    // Calculate reading time for English content
    const wordCount = translations.en.content
      .replace(/<[^>]*>/g, "")
      .split(/\s+/).length;
    const readingTime = Math.max(1, Math.ceil(wordCount / 200));

    // Update product
    const updatedProduct = await prisma.$transaction(async (tx) => {
      // Delete existing translations, categories, tags, and images
      await tx.productTranslation.deleteMany({
        where: { productId: id },
      });
      await tx.productCategory.deleteMany({
        where: { productId: id },
      });
      await tx.productTag.deleteMany({
        where: { productId: id },
      });
      // Handle images separately - don't delete all, update existing ones
      const currentImages = await tx.productImage.findMany({
        where: { productId: id },
      });

      // Delete images that are no longer in existingImages
      const existingImageIds = existingImages
        .map((img: any) => img.id)
        .filter(Boolean);
      await tx.productImage.deleteMany({
        where: {
          productId: id,
          id: { notIn: existingImageIds },
        },
      });

      // Update existing images
      for (const existingImg of existingImages) {
        if (existingImg.id) {
          await tx.productImage.update({
            where: { id: existingImg.id },
            data: {
              alt: existingImg.alt || "",
              isDisplay: existingImg.isDisplay || false,
              sortOrder: existingImg.sortOrder || 0,
            },
          });
        }
      }

      // Update the product
      return await tx.product.update({
        where: { id },
        data: {
          slug,
          whitelabelPrice,
          subscriptionPrice,
          demoUrl,
          status,
          featuredImage,
          featuredImageAlt,
          readingTime,
          publishedAt:
            status === "published" && !existingProduct.publishedAt
              ? new Date()
              : existingProduct.publishedAt,
          translations: {
            create: Object.entries(validTranslations).map(
              ([language, translation]: [string, any]) => ({
                language,
                title: translation.title,
                excerpt: translation.excerpt || "",
                content: translation.content,
                metaTitle: translation.metaTitle || translation.title,
                metaDesc: translation.metaDesc || translation.excerpt || "",
                keywords: translation.keywords || [],
              })
            ),
          },
          categories: {
            create: categoryIds.map((categoryId: string) => ({
              categoryId,
            })),
          },
          tags: {
            create: tagIds.map((tagId: string) => ({
              tagId,
            })),
          },
          images: {
            create: uploadedImages, // Only create new uploaded images
          },
        },
        include: {
          translations: true,
          categories: {
            include: { category: true },
          },
          tags: {
            include: { tag: true },
          },
          images: {
            orderBy: { sortOrder: "asc" },
          },
        },
      });
    });

    res.json({
      success: true,
      message: "Product updated successfully",
      product: updatedProduct,
    });
  } catch (error) {
    console.error("Error updating product:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update product",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// @desc    Delete product
// @route   DELETE /api/admin/products/:id
// @access  Private (Admin)
export const deleteProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const product = await prisma.product.findUnique({
      where: { id },
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    await prisma.product.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: "Product deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting product:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete product",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// @desc    Get all products for admin
// @route   GET /api/admin/products
// @access  Private (Admin)
export const getAdminProducts = async (req: Request, res: Response) => {
  try {
    const { status, limit = "20", offset = "0", search } = req.query;

    const whereClause: any = {};

    if (status && status !== "all") {
      whereClause.status = status as string;
    }

    if (search) {
      whereClause.translations = {
        some: {
          OR: [
            { title: { contains: search as string, mode: "insensitive" } },
            { excerpt: { contains: search as string, mode: "insensitive" } },
            { content: { contains: search as string, mode: "insensitive" } },
          ],
        },
      };
    }

    const products = await prisma.product.findMany({
      where: whereClause,
      include: {
        translations: true,
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
        images: {
          orderBy: { sortOrder: "asc" },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: parseInt(limit as string),
      skip: parseInt(offset as string),
    });

    const totalCount = await prisma.product.count({
      where: whereClause,
    });

    res.json({
      success: true,
      products,
      pagination: {
        total: totalCount,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore:
          parseInt(offset as string) + parseInt(limit as string) < totalCount,
      },
    });
  } catch (error) {
    console.error("Error fetching admin products:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch products",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
