import React from "react";
import PropTypes from "prop-types";

export default function Pagination({
  className,
  currentPage = 1,
  totalPages = 1,
  onPageChange = () => {},
}) {
  // Function to handle page change
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  // Don't render pagination if there's only one page or no pages
  if (totalPages <= 1) {
    return null;
  }

  // Generate page numbers array
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show smart pagination with ellipsis
      if (currentPage <= 3) {
        // Show first pages
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Show last pages
        pages.push(1);
        pages.push("...");
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Show middle pages
        pages.push(1);
        pages.push("...");
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div
      className={className ? className : "pagination justify-content-center"}
    >
      {/* Previous Page Button */}
      <a
        onClick={() => handlePageChange(currentPage - 1)}
        className={currentPage === 1 ? "disabled" : ""}
        style={{ cursor: currentPage === 1 ? "not-allowed" : "pointer" }}
      >
        <i className="mi-chevron-left" />
        <span className="visually-hidden">Previous page</span>
      </a>

      {/* Dynamic Page Numbers */}
      {pageNumbers.map((page, index) => {
        if (page === "...") {
          return (
            <span key={`ellipsis-${index}`} className="no-active">
              ...
            </span>
          );
        }

        return (
          <a
            key={page}
            onClick={() => handlePageChange(page)}
            className={currentPage === page ? "active" : ""}
            style={{ cursor: "pointer" }}
          >
            {page}
          </a>
        );
      })}

      {/* Next Page Button */}
      <a
        onClick={() => handlePageChange(currentPage + 1)}
        className={currentPage === totalPages ? "disabled" : ""}
        style={{
          cursor: currentPage === totalPages ? "not-allowed" : "pointer",
        }}
      >
        <i className="mi-chevron-right" />
        <span className="visually-hidden">Next page</span>
      </a>
    </div>
  );
}

Pagination.propTypes = {
  className: PropTypes.string,
  currentPage: PropTypes.number,
  totalPages: PropTypes.number,
  onPageChange: PropTypes.func,
};
