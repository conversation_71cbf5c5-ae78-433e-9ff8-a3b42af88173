import { Request, Response } from "express";
import nodemailer from "nodemailer";
import <PERSON><PERSON> from "joi";

// Validation schema
const contactSchema = Joi.object({
  name: Joi.string().trim().min(2).max(100).required(),
  email: Joi.string().email().required(),
  message: Joi.string().trim().min(5).max(5000).required(),
});

// Create nodemailer transporter
const createTransporter = () => {
  const isProduction = process.env.NODE_ENV === "production";

  const config = {
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT || "587"),
    secure: process.env.EMAIL_SECURE === "true",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    // Production-specific settings
    ...(isProduction && {
      connectionTimeout: 60000,
      greetingTimeout: 30000,
      socketTimeout: 60000,
      dnsTimeout: 30000,
      tls: {
        rejectUnauthorized: false,
        minVersion: "TLSv1.2" as const,
        ciphers: "HIGH:!aNULL:!MD5:!RC4:!3DES",
      },
      requireTLS: true,
      pool: true,
      maxConnections: 5,
      maxMessages: 100,
    }),
    // Development settings
    ...(!isProduction && {
      tls: {
        ciphers: "SSLv3",
        rejectUnauthorized: false,
      },
      requireTLS: true,
    }),
  };

  console.log(
    `🔧 Creating transporter for ${isProduction ? "production" : "development"}`
  );
  return nodemailer.createTransport(config);
};

// @desc    Send contact form email
// @route   POST /api/contact
// @access  Public
export const sendContactForm = async (
  req: Request,
  res: Response
): Promise<Response | void> => {
  try {
    console.log("\n🔍 CONTACT FORM SUBMISSION:");
    console.log("📝 Raw form data:", req.body);

    // Validate input
    const { error, value } = contactSchema.validate(req.body);
    if (error) {
      console.log("❌ VALIDATION FAILED:", error.details);
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    console.log("✅ VALIDATION PASSED");

    const { name, email, message } = value;

    console.log("\n📧 EMAIL PREPARATION:");
    console.log(`   From: ${name} <${email}>`);
    console.log(`   Message length: ${message.length} characters`);
    console.log(`   To: ${process.env.EMAIL_TO}`);

    // Create transporter
    console.log("\n🔧 CREATING TRANSPORTER:");
    console.log(`   Host: ${process.env.EMAIL_HOST}`);
    console.log(`   Port: ${process.env.EMAIL_PORT}`);
    console.log(`   User: ${process.env.EMAIL_USER}`);

    const transporter = createTransporter();

    // Verify transporter configuration
    console.log("\n🔍 VERIFYING SMTP CONNECTION...");
    try {
      await transporter.verify();
      console.log("✅ SMTP connection verified successfully");
    } catch (verifyError: any) {
      console.log("❌ SMTP verification failed:", verifyError.message);
      throw verifyError;
    }

    // Email content
    const mailOptions = {
      from: `"DevSkills Contact Form" <${process.env.EMAIL_FROM}>`,
      to: process.env.EMAIL_TO,
      replyTo: email,
      subject: `New Contact Form Submission from ${name}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            New Contact Form Submission
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #495057; margin-top: 0;">Contact Details:</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString(
              "en-EE",
              {
                timeZone: "Europe/Tallinn",
              }
            )}</p>
          </div>
          
          <div style="background-color: #ffffff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px;">
            <h3 style="color: #495057; margin-top: 0;">Message:</h3>
            <p style="line-height: 1.6; color: #212529;">${message.replace(
              /\n/g,
              "<br>"
            )}</p>
          </div>
          
          <div style="margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 5px; font-size: 12px; color: #6c757d;">
            <p style="margin: 0;"><strong>Note:</strong> This email was sent from the DevSkills website contact form.</p>
            <p style="margin: 5px 0 0 0;">To reply, simply respond to this email or contact ${email} directly.</p>
          </div>
        </div>
      `,
      text: `
New Contact Form Submission

Name: ${name}
Email: ${email}
Submitted: ${new Date().toLocaleString("en-EE", { timeZone: "Europe/Tallinn" })}

Message:
${message}

---
This email was sent from the DevSkills website contact form.
To reply, contact ${email} directly.
      `,
    };

    // Send email
    console.log("\n📤 SENDING EMAIL...");
    const info = await transporter.sendMail(mailOptions);

    console.log("✅ EMAIL SENT SUCCESSFULLY!");
    console.log(`   Message ID: ${info.messageId}`);
    console.log(`   Response: ${info.response}`);

    res.json({
      success: true,
      message: "Your message has been sent successfully!",
      messageId: info.messageId,
    });
  } catch (error: any) {
    console.log("\n❌ EMAIL SENDING FAILED:");
    console.log(`   Error Type: ${error.constructor.name}`);
    console.log(`   Error Message: ${error.message}`);
    console.log(`   Error Code: ${error.code || "N/A"}`);
    console.log(`   Error Response: ${error.response || "N/A"}`);

    res.status(500).json({
      success: false,
      message:
        "Failed to send message. Please try again later or contact us directly.",
      details:
        process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};
