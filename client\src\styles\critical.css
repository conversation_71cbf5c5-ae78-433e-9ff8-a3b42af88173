/* Critical CSS - Above the fold content only */
/* This file contains ONLY essential styles for first paint */
/* NO IMPORTS - All styles are inlined to prevent loading heavy CSS files */

/* CSS Variables - Essential */
:root {
  --font-global: "DM Sans", sans-serif;
  --font-alt: "DM Sans", sans-serif;
  --container-width: 1350px;
  --section-padding-y: 120px;
  --menu-bar-height: 85px;
  --color-dark-1: #010101;
  --color-dark-2: #171717;
  --color-gray-1: #757575;
}

.theme-elegant {
  --font-global: "Poppins", sans-serif;
  --container-width: 1230px;
  --section-padding-y: 120px;
  --color-dark-1: #111;
  --color-gray-1: #777;
}

/* Essential Reset */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  background-color: #000000 !important;
  background: #000000 !important;
}

body {
  font-family: var(--font-global);
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-dark-1);
}

.theme-elegant body {
  color: var(--color-dark-1);
  font-family: var(--font-global);
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 2;
}

/* Container System */
.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 15px;
}

/* Page Section - Critical for headers */
.page-section,
.small-section,
.bg-image {
  width: 100%;
  display: block;
  position: relative;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding-top: var(--section-padding-y);
  padding-bottom: var(--section-padding-y);
}

.small-section {
  padding: 100px 0;
}

/* Essential Grid */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 0 15px;
}
.col-lg-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 15px;
}
.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 15px;
}
.col-lg-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 15px;
}
.col-md-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
  padding: 0 15px;
}
.col-lg-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 15px;
}

/* Responsive grid */
@media (max-width: 991px) {
  .col-lg-6,
  .col-lg-4,
  .col-lg-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (max-width: 767px) {
  .col-md-6,
  .col-md-8 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Offset classes */
.offset-md-2 {
  margin-left: 16.666667%;
}
.offset-lg-3 {
  margin-left: 25%;
}

@media (max-width: 991px) {
  .offset-lg-3 {
    margin-left: 0;
  }
}

@media (max-width: 767px) {
  .offset-md-2 {
    margin-left: 0;
  }
}

/* Page Structure */
.page {
  background-color: var(--color-dark-1);
  min-height: 100vh;
}

.bg-dark-1 {
  background-color: var(--color-dark-1) !important;
}

/* Navigation - Critical - FOUC Prevention */
.main-nav {
  display: flex !important;
  width: 100%;
  height: var(--menu-bar-height);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  z-index: 1030;
  transition: all 0.2s ease;
  visibility: visible !important;
  opacity: 1 !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.main-nav.dark {
  background-color: rgba(10, 10, 10, 0.905);
  box-shadow: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-nav.dark-mode {
  background-color: rgba(27, 27, 27, 0.905);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-nav.transparent {
  background: transparent;
  box-shadow: none;
  border-bottom: none;
  backdrop-filter: none;
}

.main-nav-sub {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 30px;
  height: 100%;
}

/* Navbar menu items */
.main-nav .navbar-nav {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-nav .nav-item {
  margin: 0 15px;
}

.main-nav .nav-link {
  color: rgba(0, 0, 0, 0.8);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.main-nav.dark .nav-link {
  color: rgba(255, 255, 255, 0.9);
}

.main-nav .nav-link:hover {
  color: rgba(0, 0, 0, 1);
}

.main-nav.dark .nav-link:hover {
  color: #ffffff;
}

/* Logo - Critical */
.nav-logo-wrap {
  display: flex;
  align-items: center;
}

.logo {
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif !important;
  font-size: 18px;
  font-weight: 600 !important;
  text-decoration: none;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo .mt-1 {
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif !important;
  font-weight: 600 !important;
}

/* Hero Section - Critical */
/* Hero Section - EXACT 100vh height */
.home-section {
  width: 100%;
  display: block;
  position: relative;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center center;
  background-size: cover;
  min-height: 100vh;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.home-content {
  width: 100%;
  position: relative;
  text-align: center;
  z-index: 2;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-bottom: 80px; /* Leave space for scroll indicator */
}

/* Container inside hero - must fit within 100vh including padding */
.min-height-100vh {
  min-height: 100vh !important;
  height: 100vh !important;
  max-height: 100vh !important;
  box-sizing: border-box !important;
}

/* Hero container with padding - total height = 100vh */
.container.min-height-100vh.d-flex.align-items-center.pt-100.pb-100 {
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  padding-top: 100px !important;
  padding-bottom: 100px !important;
  box-sizing: border-box !important;
  flex-direction: column !important;
  justify-content: center !important;
  position: relative !important;
}

/* Mobile responsive hero */
@media (max-width: 768px) {
  .container.min-height-100vh.d-flex.align-items-center.pt-sm-120.pb-sm-120 {
    padding-top: 120px !important;
    padding-bottom: 120px !important;
  }
}

.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.pt-100 {
  padding-top: 100px !important;
}
.pb-100 {
  padding-bottom: 100px !important;
}

/* Typography - Critical */
.section-title-tiny {
  font-size: 19px !important;
  font-weight: 500 !important;
  letter-spacing: -0.01em !important;
  margin-bottom: 50px;
  opacity: 0.8;
  /* NO text-transform: uppercase - should be normal case */
}

.hs-title-3 {
  margin-top: 0;
  font-size: 38px !important;
  font-weight: 400 !important;
  line-height: 1.3 !important;
  letter-spacing: 0.3em !important;
  text-transform: uppercase !important;
}

/* Section titles for page headers */
.section-title {
  font-size: 2.5rem;
  font-weight: 400;
  line-height: 1.2;
  margin-bottom: 30px;
  margin-top: 0;
  /* NO text-transform - should be normal case */
}

.theme-elegant .section-title {
  font-size: 2.5rem;
  font-weight: 400;
  line-height: 1.2;
  letter-spacing: 0.05em;
  /* NO text-transform - should be normal case */
}

/* Text utilities */
.text-gray {
  color: var(--color-gray-1) !important;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 0;
}

.light-content .text-gray {
  color: rgba(255, 255, 255, 0.7) !important;
}

.theme-elegant .text-gray {
  color: var(--color-gray-1) !important;
  font-size: 1rem;
  line-height: 2;
}

/* Animation Base - Critical - FOUC Prevention */
.wow {
  visibility: hidden;
  opacity: 0.001;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated {
  visibility: visible !important;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  opacity: 1;
  transform: scale(1);
}

/* Essential Animations */
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Additional Critical Animations for FOUC Prevention */
@keyframes fadeInDownShort {
  0% {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUpShort {
  0% {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes charsAnimInLong {
  0% {
    opacity: 0;
    transform: translateY(100%) rotateX(-90deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

/* Animation Classes */
.fadeInDown {
  animation-name: fadeInDown;
  animation-duration: 0.8s;
}

.fadeInDownShort {
  animation-name: fadeInDownShort;
  animation-duration: 0.6s;
}

.fadeInUp {
  animation-name: fadeInUp;
  animation-duration: 0.8s;
}

.fadeInUpShort {
  animation-name: fadeInUpShort;
  animation-duration: 0.6s;
}

.charsAnimInLong {
  animation-name: charsAnimInLong;
  animation-duration: 1.2s;
  animation-delay: 0.1s;
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.position-relative {
  position: relative;
}
.light-content {
  color: #ffffff !important;
}

.light-content .section-title-tiny {
  color: #ffffff !important;
  opacity: 0.8;
}

.light-content .hs-title-3 {
  color: #ffffff !important;
}

/* Critical Button Styles - Exact from original CSS */
.link-hover-anim {
  display: inline-block;
  text-decoration: none;
  color: #ffffff !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.link-hover-anim:hover {
  color: inherit;
  text-decoration: none;
}

.link-circle-1 {
  position: relative;
  display: inline-block;
  margin-left: -7px;
  padding-left: 27px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.link-circle-1:before {
  content: "";
  position: absolute;
  top: calc(50%);
  left: 7px;
  transform: translate3d(0, -50%, 0.001px);
  display: block;
  width: 60px;
  height: 60px;
  border: 2px solid var(--color-dark-1);
  border-radius: 50%;
  opacity: 0.15;
  transition: all 0.5s ease;
}

.light-content .link-circle-1:before {
  border-color: #fff;
  opacity: 0.25;
}

.link-circle-1:hover:before {
  transform: translate3d(0, -50%, 0.001px) scale(0.88);
}

.link-circle-1 .link-strong {
  padding: 24px 0;
}

.link-strong {
  position: relative;
  display: block;
  transition: all 0.35s ease;
}

/* Button animation states */
.link-strong-hovered {
  display: block;
  position: absolute;
  top: 6px;
  left: 0;
  opacity: 0;
  transform: translateY(150%) translateZ(0.001px) skewY(10deg);
  transition: all 0.35s ease;
}

.link-strong-unhovered {
  display: block;
  transition: all 0.35s ease;
}

.link-hover-anim:hover .link-strong-unhovered {
  opacity: 0;
  transform: translateY(-150%) translateZ(0.001px) skewY(-10deg);
}

.link-hover-anim:hover .link-strong-hovered {
  opacity: 1;
  transform: translateY(0) translateZ(0.001px) skewY(0);
}

.link-circle-1 .link-strong-hovered {
  left: 27px;
}

.light-content .link-strong,
.light-content .link-hover-anim {
  color: #ffffff !important;
}

.light-content .link-circle-1 {
  border-color: rgba(255, 255, 255, 0.25);
}

.light-content .link-circle-1:hover {
  border-color: rgba(255, 255, 255, 0.6);
}

/* Scroll Down Indicator - Fixed positioning */
.scroll-down-3-wrap {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  pointer-events: none;
}

.scroll-down-3-wrap .scroll-down-3 {
  pointer-events: auto;
}

.scroll-down-3 {
  position: relative;
  color: var(--color-dark-1);
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  text-decoration: none;
  letter-spacing: 0.1em;
  transition: letter-spacing 0.27s ease;
}

.scroll-down-3:hover {
  color: unset;
  text-decoration: none;
  letter-spacing: 0.2em;
}

.scroll-down-3:after {
  content: "";
  display: block;
  width: 1px;
  height: 106px;
  margin: 6px auto -53px;
  background: var(--color-dark-1);
}

.light-content .scroll-down-3 {
  color: #fff;
}

.light-content .scroll-down-3:after {
  background: rgba(193, 193, 193, 0.9);
}

/* Spacing */
.mb-50 {
  margin-bottom: 50px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-120 {
  margin-bottom: 120px;
}
.mb-sm-30 {
  margin-bottom: 30px;
}
.mb-sm-20 {
  margin-bottom: 20px;
}
.mb-sm-80 {
  margin-bottom: 80px;
}
.mb-xs-140 {
  margin-bottom: 140px;
}
.pt-sm-120 {
  padding-top: 120px;
}
.pb-sm-120 {
  padding-bottom: 120px;
}

/* Responsive spacing */
@media (max-width: 768px) {
  .mb-sm-30 {
    margin-bottom: 30px;
  }
  .mb-sm-20 {
    margin-bottom: 20px;
  }
}

/* Additional utility classes */
.align-middle {
  vertical-align: middle;
}
.size-18 {
  font-size: 18px;
}
.local-scroll {
  position: relative;
}
.z-index-1 {
  z-index: 1;
}
.z-index-2 {
  z-index: 2;
}
.z-index-3 {
  z-index: 3;
}

/* Icon styles for CTA button */
.mi-arrow-right:before {
  content: "→";
  font-style: normal;
  font-weight: normal;
  display: inline-block;
}

/* Flex utilities */
.d-flex {
  display: flex !important;
}
.align-items-center {
  align-items: center !important;
}
.justify-content-center {
  justify-content: center !important;
}
.flex-column {
  flex-direction: column !important;
}

/* Parallax */
.parallax-5 {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-dark-alpha-30::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

/* Critical Service Card Styles - Exact from original */
.service-card-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card-container:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.08);
}

.service-card-img {
  width: 100%;
  height: 250px !important;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-card-image {
  width: 100% !important;
  height: 250px !important;
  object-fit: cover !important;
  object-position: center !important;
  transition: all 0.4s ease;
  filter: grayscale(100%);
  display: block;
}

.service-card-container:hover .service-card-image {
  filter: grayscale(0%);
  transform: scale(1.05);
}

.service-card-content {
  padding: 30px 25px;
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.service-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
  line-height: 1.3;
}

.service-card-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
}

/* Responsive service cards */
@media (max-width: 768px) {
  .service-card-img {
    height: 200px;
  }

  .service-card-content {
    padding: 25px 20px;
  }

  .service-card-title {
    font-size: 1.1rem;
  }

  .service-card-text {
    font-size: 0.9rem;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .hs-title-3 {
    font-size: calc(1.559rem + 0.96vw) !important;
  }
  .section-title-tiny {
    margin-bottom: 30px;
  }
  .main-nav-sub {
    padding: 0 15px;
  }
}
