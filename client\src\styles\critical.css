/* Critical CSS - Above the fold content only */
/* This file contains ONLY essential styles for first paint */
/* NO IMPORTS - All styles are inlined to prevent loading heavy CSS files */

/* CSS Variables - Essential */
:root {
  --font-global: "DM Sans", sans-serif;
  --font-alt: "DM Sans", sans-serif;
  --container-width: 1350px;
  --section-padding-y: 120px;
  --menu-bar-height: 85px;
  --color-dark-1: #010101;
  --color-dark-2: #171717;
  --color-gray-1: #757575;
}

.theme-elegant {
  --font-global: "Poppins", sans-serif;
  --container-width: 1230px;
  --section-padding-y: 120px;
  --color-dark-1: #111;
  --color-gray-1: #777;
}

/* Essential Reset */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  background-color: #000000 !important;
  background: #000000 !important;
}

body {
  font-family: var(--font-global);
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-dark-1);
}

.theme-elegant body {
  color: var(--color-dark-1);
  font-family: var(--font-global);
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 2;
}

/* Container System */
.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 15px;
}

/* Essential Grid */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
.col-lg-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

/* Page Structure */
.page {
  background-color: var(--color-dark-1);
  min-height: 100vh;
}

.bg-dark-1 {
  background-color: var(--color-dark-1) !important;
}

/* Navigation - Critical */
.main-nav {
  display: flex;
  width: 100%;
  height: var(--menu-bar-height);
  position: relative;
  background: rgba(255, 255, 255, 0.98);
  z-index: 1030;
  transition: all 0.2s ease;
  visibility: visible !important;
  opacity: 1 !important;
}

.main-nav.dark {
  background-color: rgba(10, 10, 10, 0.905);
  box-shadow: none;
}

.main-nav.dark-mode {
  background-color: rgba(27, 27, 27, 0.905);
}

.main-nav.transparent {
  background: transparent;
  box-shadow: none;
}

.main-nav-sub {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 30px;
}

/* Logo - Critical */
.nav-logo-wrap {
  display: flex;
  align-items: center;
}

.logo {
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif !important;
  font-size: 18px;
  font-weight: 600 !important;
  text-decoration: none;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo .mt-1 {
  font-family: "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif !important;
  font-weight: 600 !important;
}

/* Hero Section - Critical */
.home-section {
  width: 100%;
  display: block;
  position: relative;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center center;
  background-size: cover;
}

.home-content {
  width: 100%;
  position: relative;
  text-align: center;
}

.min-height-100vh {
  min-height: 100vh !important;
}

.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.pt-100 {
  padding-top: 100px !important;
}
.pb-100 {
  padding-bottom: 100px !important;
}

/* Typography - Critical */
.section-title-tiny {
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 50px;
  opacity: 0.8;
}

.hs-title-3 {
  margin-top: 0;
  font-size: 38px !important;
  font-weight: 400 !important;
  line-height: 1.3 !important;
  letter-spacing: 0.3em !important;
  text-transform: uppercase !important;
}

/* Animation Base - Critical */
.wow {
  opacity: 0.001;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  opacity: 1;
  transform: scale(1);
}

/* Essential Animations */
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fadeInDown {
  animation-name: fadeInDown;
}
.fadeInUp {
  animation-name: fadeInUp;
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.position-relative {
  position: relative;
}
.light-content {
  color: #ffffff;
}

/* Spacing */
.mb-50 {
  margin-bottom: 50px;
}
.mb-120 {
  margin-bottom: 120px;
}

/* Parallax */
.parallax-5 {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-dark-alpha-30::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .hs-title-3 {
    font-size: calc(1.559rem + 0.96vw) !important;
  }
  .section-title-tiny {
    margin-bottom: 30px;
  }
  .main-nav-sub {
    padding: 0 15px;
  }
}
