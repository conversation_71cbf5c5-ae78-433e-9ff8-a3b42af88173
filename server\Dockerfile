# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine

# Install OpenSSL and other required dependencies for Prisma 6
RUN apk add --no-cache openssl openssl-dev libc6-compat su-exec

# Install pnpm globally
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml* ./

# Install ALL dependencies (including devDependencies for build)
RUN pnpm install

# Copy application code
COPY . .

# Copy and set permissions for entrypoint script
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Generate Prisma client
RUN pnpm db:generate

# Build TypeScript
RUN pnpm build

# Remove devDependencies to reduce image size (optional)
RUN pnpm prune --prod

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Create uploads directory structure with proper permissions
RUN mkdir -p /app/uploads/blog-images /app/uploads/temp && \
    chown -R nodejs:nodejs /app && \
    chmod -R 755 /app/uploads

# Don't switch to nodejs user yet - we'll do it in the entrypoint script
# This allows us to fix volume mount permissions at runtime

# Expose port
EXPOSE 4004

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:4004/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Set entrypoint and default command
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["node", "dist/index.js"]
