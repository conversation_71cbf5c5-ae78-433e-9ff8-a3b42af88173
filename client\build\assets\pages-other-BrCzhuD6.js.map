{"version": 3, "mappings": ";+bAAY,MAACA,EAAY,CACvB,CAAE,KAAM,IAAK,KAAM,MAAQ,EAC3B,CAAE,KAAM,SAAU,KAAM,OAAS,EACjC,CAAE,KAAM,YAAa,KAAM,UAAY,EACvC,CAAE,KAAM,YAAa,KAAM,UAAY,EAEvC,CAAE,KAAM,QAAS,KAAM,MAAQ,EAC/B,CAAE,KAAM,WAAY,KAAM,SAAW,CACvC,ECAaC,EAAgB,CAC3B,KAAM,YACN,SAAU,eACV,cAAe,+BACf,YACE,kEACF,IAAK,uBACL,QAAS,CACP,cAAe,gBACf,gBAAiB,UACjB,WAAY,QACZ,eAAgB,IACjB,EACD,IAAK,CACH,SAAU,OACV,UAAW,OACZ,EACD,aAAc,CACZ,UAAW,iBACX,YAAa,mBACb,kBAAmB,CAAC,UAAW,WAAY,UAAW,SAAU,SAAS,CAC1E,EACD,aAAc,CACZ,UAAW,CAAC,SAAU,UAAW,YAAa,WAAY,QAAQ,EAClE,MAAO,QACP,OAAQ,OACT,EACD,SAAU,CACR,8BACA,kBACA,eACA,uBACA,yBACA,kCACA,sBACA,6BACD,EACD,YAAa,CACX,uCACA,gEACA,iCACD,CACH,EAKaC,EAA8B,KAAO,CAChD,QAAS,gBACT,MAAO,GAAGD,EAAc,GAAG,iBAC3B,KAAMA,EAAc,SACpB,cAAeA,EAAc,cAC7B,IAAKA,EAAc,IACnB,YAAaA,EAAc,YAC3B,QAAS,CACP,QAAS,gBACT,GAAGA,EAAc,OAClB,EACD,IAAK,CACH,QAAS,iBACT,GAAGA,EAAc,GAClB,EACD,aAAc,CACZ,QAAS,eACT,GAAGA,EAAc,YAClB,EACD,0BAA2B,CACzB,QAAS,4BACT,GAAGA,EAAc,YAClB,EACD,YAAa,CACX,QAAS,UACT,KAAM,SACP,EACD,gBAAiB,CACf,QAAS,eACT,KAAM,gCACN,gBAAiBA,EAAc,SAAS,IAAKE,IAAa,CACxD,QAAS,QACT,YAAa,CACX,QAAS,UACT,KAAMA,CACP,CACP,EAAM,CACH,EACD,KAAM,CACJ,QAAS,cACT,IAAK,GAAGF,EAAc,GAAG,YACzB,MAAO,MACP,OAAQ,IACT,EACD,OAAQA,EAAc,WACxB,GAKaG,GAAwB,KAAO,CAC1C,QAAS,UACT,MAAO,GAAGH,EAAc,GAAG,YAC3B,KAAMA,EAAc,KACpB,cAAeA,EAAc,cAC7B,IAAKA,EAAc,IACnB,YAAaA,EAAc,YAC3B,UAAW,CACT,MAAO,GAAGA,EAAc,GAAG,gBAC5B,EACD,gBAAiB,CACf,QAAS,eACT,OAAQ,GAAGA,EAAc,GAAG,iCAC5B,cAAe,kCAChB,EACD,WAAY,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,CAC3C,GAKaI,GAAyBC,IAAa,CACjD,QAAS,UACT,SAAUA,EAAQ,MAClB,YAAaA,EAAQ,SAAWA,EAAQ,YACxC,MAAOA,EAAQ,eAAiB,GAAGL,EAAc,GAAG,YACpD,OAAQ,CACN,QAAS,SACT,KAAMK,EAAQ,QAAUL,EAAc,IACvC,EACD,UAAW,CACT,QAAS,eACT,KAAMA,EAAc,KACpB,KAAM,CACJ,QAAS,cACT,IAAK,GAAGA,EAAc,GAAG,WAC1B,CACF,EACD,cAAeK,EAAQ,YACvB,aAAcA,EAAQ,YAAcA,EAAQ,YAC5C,iBAAkB,CAChB,QAAS,UACT,MAAOA,EAAQ,GAChB,CACH,GAKaC,GAAyBC,IAAa,CACjD,QAAS,sBACT,KAAMA,EAAQ,MACd,YAAaA,EAAQ,YACrB,oBAAqB,sBACrB,gBAAiB,MACjB,UAAW,CACT,MAAO,GAAGP,EAAc,GAAG,gBAC5B,EACD,OAAQ,CACN,QAAS,QACT,MAAOO,EAAQ,OAAS,sBACxB,cAAe,MACf,aAAc,4BACf,EACD,gBAAiBA,EAAQ,OACrB,CACE,QAAS,kBACT,YAAaA,EAAQ,OAAO,MAC5B,YAAaA,EAAQ,OAAO,KACpC,EACM,MACN,GAKaC,GAA4BC,IAAiB,CACxD,QAAS,iBACT,gBAAiBA,EAAY,IAAI,CAACC,EAAOC,KAAW,CAClD,QAAS,WACT,SAAUA,EAAQ,EAClB,KAAM,CACJ,MAAOD,EAAM,IACb,KAAMA,EAAM,IACb,CACL,EAAI,CACJ,GAKaE,EAAiB,CAACC,EAAUC,EAAU,GAAIC,EAAW,OAAS,CACzE,MAAMC,EAAe,CACnB,uBACA,kBACA,kBACA,eACA,UACA,SACD,EAEKC,EAAU,CACd,SAAU,CACR,MAAO,6CACP,YACE,wJACF,SAAU,CAAC,GAAGD,EAAc,uBAAwB,oBAAoB,EACxE,OAAQ,CAACf,IAA+BE,IAAuB,CAChE,EACD,MAAO,CACL,MAAO,kDACP,YACE,kJACF,SAAU,CAAC,GAAGa,EAAc,QAAS,OAAQ,SAAS,EACtD,OAAQ,CAACf,GAA6B,CACvC,EACD,SAAU,CACR,MAAO,mDACP,YACE,yJACF,SAAU,CACR,GAAGe,EACH,WACA,qBACA,aACA,aACD,EACD,OAAQ,CAACf,GAA6B,CACvC,EACD,SAAU,CACR,MAAO,sDACP,YACE,4HACF,SAAU,CACR,GAAGe,EACH,cACA,oBACA,UACD,EACD,OAAQ,CAACf,GAA6B,CACvC,EACD,KAAM,CACJ,MAAO,iDACP,YACE,wIACF,SAAU,CACR,GAAGe,EACH,OACA,YACA,WACA,iBACD,EACD,OAAQ,CAACf,GAA6B,CACvC,EACD,QAAS,CACP,MAAO,qDACP,YACE,mJACF,SAAU,CAAC,GAAGe,EAAc,UAAW,QAAS,cAAc,EAC9D,OAAQ,CAACf,GAA6B,CACvC,CACF,EAGD,OAAIa,EAAQ,OAASA,EAAQ,YACpB,CACL,MAAOA,EAAQ,MACf,YAAaA,EAAQ,YACrB,SAAUA,EAAQ,UAAYE,EAC9B,OAAQF,EAAQ,QAAU,CAACb,EAA2B,CAAE,CACzD,EAGIgB,EAAQJ,CAAQ,GAAKI,EAAQ,QACtC,ECpKA,SAAwBC,IAA6B,CAC7C,MAAAD,EAAUL,EAAe,UAAU,EAEzC,OAEIO,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAOL,EAAQ,MACf,YAAaA,EAAQ,YACrB,KAAK,GACL,KAAK,UACL,MAAM,gCACN,SAAS,mEACT,OAAQA,EAAQ,OAChB,SAAUA,EAAQ,SACpB,QACC,MAAI,WAAU,gBACb,SAACE,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,8DACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAACG,GAAA,CACC,UAAU,oFACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,eAACC,GAAK,IACR,EAEAJ,MAACK,GAAK,MAAI,EAAC,IACb,QACC,SAAO,WAAU,6DAChB,SAAAL,EAAA,IAACM,IAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,CChJA,SAAwBC,IAA0B,CAChD,KAAM,CAAE,EAAGC,EAAW,KAAAC,CAAA,EAASC,EAAe,EACxCC,EAAkBF,EAAK,UAAY,KAEnC,CAACG,EAAUC,CAAW,EAAIC,WAAS,EAAE,EACrC,CAACC,EAAkBC,CAAmB,EAAIF,WAAS,EAAE,EACrD,CAACG,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAACK,EAAkBC,CAAmB,EAAIN,WAAS,KAAK,EACxD,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAErCW,YAAU,IAAM,CACDC,EAAA,EACEC,EAAA,GACd,CAAChB,CAAe,CAAC,EAEpB,MAAMe,EAAe,SAAY,CAC3B,IACFJ,EAAW,EAAI,EACf,KAAM,CAAE,SAAAM,EAAU,KAAAC,CAAS,QAAMC,GAAY,YAAY,CACvD,SAAUnB,EACV,OAAQ,YACT,EAEGiB,EAAS,IAAMC,EAAK,SACd,YAAI,mBAAoBA,EAAK,QAAQ,EAC7ChB,EAAYgB,EAAK,QAAQ,EACzBb,EAAoBa,EAAK,QAAQ,GAEjCL,EAAS,yBAAyB,QAE7BO,EAAK,CACJ,cAAM,0BAA2BA,CAAG,EAC5CP,EAAS,wBAAwB,SACjC,CACAF,EAAW,EAAK,EAEpB,EAEMK,EAAiB,SAAY,CAC7B,IACF,KAAM,CAAE,SAAAC,EAAU,KAAAC,CAAS,QAAMG,EAAc,cAAc,EAEzDJ,EAAS,IAAMC,EAAK,SACRX,EAAAW,EAAK,MAAQA,EAAK,UAAU,QAErCE,EAAK,CACJ,cAAM,4BAA6BA,CAAG,EAElD,EAEME,EAAwBC,GAAiB,CAG7C,GAFAd,EAAoBc,CAAY,EAE5BA,IAAiB,MACnBlB,EAAoBJ,CAAQ,MACvB,CACL,MAAMuB,EAAWvB,EAAS,OAAQ1B,GAChCA,EAAQ,WAAW,KAAMkD,GAAQA,EAAI,SAAS,OAASF,CAAY,CACrE,EACAlB,EAAoBmB,CAAQ,EAEhC,EAEME,EAAsBnD,GAAY,CACtCoD,EAAW,eAAgB,CACzB,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,SAAUyB,EACV,OAAQ,mBACT,CACH,EAUMf,EAAUL,EAAe,UAAU,EAIrC,OAAAO,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAOO,EAAU,qBAAqB,GAAKZ,EAAQ,MACnD,YACEY,EAAU,2BAA2B,GAAKZ,EAAQ,YAEpD,KAAK,WACL,KAAK,UACL,MAAM,oCACN,OAAQA,EAAQ,OAChB,SACEY,EAAU,wBAAwB,EAC9BA,EAAU,wBAAwB,EAC/B,MAAM,GAAG,EACT,IAAK+B,GAAMA,EAAE,KAAK,CAAC,EACtB3C,EAAQ,SAEhB,QACC,QACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAaH,QAEC,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACI,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,EAECoB,OAAA,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,SAACA,EAAA,WAAI,UAAU,6CACb,eAAC,MAAI,WAAU,cACb,SAACA,MAAA,OAAI,UAAU,MACb,gBAAC,OAAI,UAAU,uBACb,UAACA,MAAA,MAAG,UAAU,mBACZ,SAAAA,EAAA,IAAC,QACC,UAAU,kBACV,iBAAe,QAEf,eAACwC,GAAA,CAAa,KAAMhC,EAAU,gBAAgB,CAAG,KAErD,QACC,MAAI,WAAU,MACb,SAACR,MAAA,OAAI,UAAU,uBACb,SAAAA,EAAA,IAAC,KACC,UAAU,gCACV,iBAAe,OAEd,WAAU,sBAAsB,EACnC,EACF,CACF,IACF,EACF,EACF,CACF,GACF,QAEC,UAAQ,WAAU,uCACjB,SAACF,EAAA,YAAI,UAAU,8BAEb,gBAAC,OAAI,UAAU,qBACb,SAACE,EAAA,WAAI,UAAU,YACb,SAAAF,OAAC,MAAI,WAAU,gDACb,UAAAE,EAAA,IAAC,KACC,QAAS,IAAMiC,EAAqB,KAAK,EACzC,UAAW,UACTd,IAAqB,MAAQ,SAAW,EAC1C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,qBAAqB,EAClC,EACCF,EAAW,IAAKwB,GACfzC,EAAA,IAAC,KAEC,QAAS,IAAMiC,EAAqBQ,EAAS,IAAI,EACjD,UAAW,UACTtB,IAAqBsB,EAAS,KAAO,SAAW,EAClD,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,SAASA,EAAA,MAPLA,EAAS,EASjB,GACH,EACF,GACF,EAGCpB,EACErB,EAAA,UAAI,WAAU,mBACb,SAAAA,EAAA,IAAC,OACC,UAAU,8BACV,KAAK,SAEL,eAAC,QAAK,UAAU,kBAAkB,SAAU,gBAEhD,GACEuB,EACFvB,EAAA,IAAC,OACC,UAAU,iCACV,KAAK,QAEJ,SAAAuB,CAAA,CAGH,EAAAvB,EAAA,IAAC,MAAI,WAAU,aACZ,SAAAe,EAAiB,OAAS,EACzBA,EAAiB,IAAI,CAAC7B,EAASI,IAC7BU,EAAA,IAAC,OAEC,UAAU,oCAEV,SAACF,EAAA,WAAI,WAAU,+CACb,UAACE,MAAA,OAAI,UAAU,gBACb,SAAAA,EAAA,IAAC0C,EAAA,CACC,GAAI,IAAI/B,CAAe,qBACrBzB,EAAQ,MAAQA,EAAQ,IACxB,QAAQ,OAAQ,EAAE,CAAC,GACrB,QAAS,IAAMmD,EAAmBnD,CAAO,EAEzC,SAAAc,EAAA,IAAC,OACC,IACEd,EAAQ,cACJ,gDAIEA,EAAQ,aACV,GACA,yCAEN,MAAO,IACP,OAAQ,IACR,IACEA,EAAQ,kBAAoBA,EAAQ,MAEtC,UAAU,iBACV,oBAAkB,QACpB,GAEJ,EAGCY,OAAA,MAAI,WAAU,iCACb,UAACE,MAAA,MAAG,UAAU,kBACZ,SAAAA,EAAA,IAAC0C,EAAA,CACC,GAAI,IAAI/B,CAAe,qBACrBzB,EAAQ,MAAQA,EAAQ,IACxB,QAAQ,OAAQ,EAAE,CAAC,GACrB,QAAS,IAAMmD,EAAmBnD,CAAO,EAExC,SAAQA,EAAA,QAEb,EAECc,EAAA,WAAI,UAAU,6BACZ,WAAQ,QACX,EAGCF,OAAA,MAAI,WAAU,UAEb,UAACA,OAAA,OAAI,UAAU,oCACZ,UAAAZ,EAAQ,iBACPY,OAAC,MAAI,WAAU,kBACb,UAACA,OAAA,QAAK,UAAU,YACb,UAAAU,EACC,2BACF,EAAE,IACA,KACJ,EACCV,OAAA,OAAK,WAAU,qCAAqC,cACjDZ,EAAQ,gBACZ,IACF,EAEDA,EAAQ,mBACNY,OAAA,OAAI,UAAU,kBACb,UAACA,OAAA,QAAK,UAAU,YACb,UAAAU,EACC,6BACF,EAAE,IACA,KACJ,EACCV,OAAA,OAAK,WAAU,qCAAqC,cACjDZ,EAAQ,kBAAkB,MAC9B,GACF,IAEJ,QAGC,OAAI,UAAU,cACZ,WAAQ,SACPc,EAAA,IAAC,KACC,KAAMd,EAAQ,QACd,OAAO,SACP,IAAI,sBACJ,UAAU,yDACV,QAAUyD,GAAM,CAEd,WAAW,IAAM,CACfA,EAAE,OAAO,KAAK,GACb,GAAG,EAENL,EAAW,aAAc,CACvB,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,SAAUyB,EACV,OAAQ,mBACT,CACH,EAEA,eAAC,QACE,SAAUH,EAAA,oBAAoB,CACjC,IAGN,GACF,GACF,GACF,IAhHKtB,EAAQ,GAkHhB,EAEAc,EAAA,WAAI,UAAU,qBACb,SAACA,MAAA,IAAE,WAAU,aACV,SAAAQ,EAAU,sBAAsB,EACnC,EACF,CAEJ,IAEJ,CACF,IACF,QAEC,SAAO,WAAU,mCAChB,SAAAR,EAAA,IAACM,IAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHC9VMsC,GAAW,CACf,MACE,yFACF,YAAa,8DACf,EACA,SAAwBC,IAA2B,CACjD,OAEI/C,EAAA,KAAAC,WAAA,WAACC,MAAA8C,EAAA,CAAc,KAAMF,EAAU,SAC9B,MAAI,WAAU,gBACb,SAAC9C,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OACnB,qBAED,QACC,MAAI,WAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,OAAI,UAAU,4CACb,eAAC,IAAE,WAAU,sCAAsC,qDAEnD,EACF,CACF,GACF,GACF,EACAA,EAAA,IAAC,WACC,UAAW,gEAGX,GAAG,YAEH,eAAC+C,GAAU,IACb,QACC,MAAI,WAAU,+BACb,SAAA/C,MAACgD,IAAY,GACf,EACChD,MAAA,WAAQ,UAAU,4CACjB,eAAC,MAAI,WAAU,8BAIb,SAAAA,EAAA,IAAC,OAAI,UAAU,+BACb,SAACF,OAAA,OAAI,UAAU,6CACb,UAACE,EAAA,SAAE,UAAU,+BAA+B,SAG5C,4FACAA,MAAC,MAAI,WAAU,eACb,SAAAA,EAAA,IAAC0C,EAAA,CACC,GAAI,mBACJ,UAAU,wDAEV,SAAA1C,MAAC,QAAK,SAAU,gBAEpB,IACF,EACF,EACF,CACF,IACF,QACC,SAAO,WAAU,6DAChB,SAAAA,EAAA,IAACM,IAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHC/EA,SAAwB2C,IAAsB,SAC5C,KAAM,CAAE,EAAAC,EAAG,KAAAzC,CAAK,EAAIC,EAAe,EAC7BC,EAAkBF,EAAK,UAAY,KACnC,CAAC0C,EAAcC,CAAe,EAAIC,GAAgB,EAClD,CAACC,EAAWC,CAAY,EAAIzC,WAAS,EAAE,EACvC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAAC0C,EAAaC,CAAc,EAAI3C,WAAS,CAAC,EAC1C,CAAC4C,EAAYC,CAAa,EAAI7C,WAAS,CAAC,EACxC,CAACG,EAAYC,CAAa,EAAIJ,WAAS,EAAE,EACzC,CAAC8C,EAAMC,CAAO,EAAI/C,WAAS,EAAE,EAC7B,CAACgD,EAAaC,CAAc,EAAIjD,WAAS,EAAE,EAG3CkD,EAAkBb,EAAa,IAAI,UAAU,EAC7Cc,EAAad,EAAa,IAAI,KAAK,EACnCe,EAAgBf,EAAa,IAAI,QAAQ,EAE/C1B,YAAU,IAAM,EACI,SAAY,SACxB,IACFH,EAAW,EAAI,EAGf,MAAM6C,EAAS,CACb,SAAUxD,EACV,KAAM6C,EACN,MAAO,CACT,EAEIQ,MAAwB,SAAWA,GACnCC,MAAmB,IAAMA,GACzBC,MAAsB,OAASA,GAEnC,MAAME,EAAa,MAAMC,EAAQ,aAAaF,CAAM,EAEpD,GAAIC,EAAW,SAAS,IAAMA,EAAW,KAAM,CAEvC,MAAAE,IACJC,EAAAH,EAAW,KAAK,OAAhB,YAAAG,EAAsB,OAAQH,EAAW,KAAK,MAAQ,CAAC,EACnDI,IACJC,EAAAL,EAAW,KAAK,OAAhB,YAAAK,EAAsB,aAAcL,EAAW,KAAK,WAC9C,YAAI,6BAA8BA,EAAW,IAAI,EACjD,YAAI,eAAgBE,CAAK,EACzB,YAAI,cAAeE,CAAU,EACrCjB,EAAa,MAAM,QAAQe,CAAK,EAAIA,EAAQ,EAAE,EAChCX,GAAAa,GAAA,YAAAA,EAAY,aAAc,CAAC,OAEjC,cACN,8BACAJ,EAAW,SAAS,MACtB,EACAb,EAAa,EAAE,EAIb,IACI,MAAAmB,EAAmB,MAAM1C,EAAc,cAAc,EACvD0C,EAAiB,SAAS,IAAMA,EAAiB,MACnDxD,EAAcwD,EAAiB,KAAK,MAAQ,EAAE,QAEzCnD,EAAO,CACN,cAAM,6BAA8BA,CAAK,EAI/C,IACI,MAAAoD,EAAa,MAAMC,GAAQ,QAAQ,EACrCD,EAAW,SAAS,IAAMA,EAAW,MACvCd,EAAQc,EAAW,KAAK,MAAQ,EAAE,QAE7BpD,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAIzC,IACI,MAAAsD,EAAgB,MAAMC,GAAW,WAAW,EAC9CD,EAAc,SAAS,IAAMA,EAAc,MAC7Cd,EAAec,EAAc,KAAK,SAAW,EAAE,QAE1CtD,EAAO,CACN,cAAM,0BAA2BA,CAAK,SAEzCA,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAC3CgC,EAAa,EAAE,SACf,CACAjC,EAAW,EAAK,EAEpB,GAEU,GACT,CACDX,EACA6C,EACAQ,EACAC,EACAC,CAAA,CACD,EAGK,MAAAa,EAAiB,CAACC,EAAMC,IAAU,WAChC,MAAAC,GAAcX,EAAAS,EAAK,eAAL,YAAAT,EAAmB,KACpCrB,GAAMA,EAAE,WAAavC,GAExB,OACEuE,GAAA,YAAAA,EAAcD,OACdE,GAAAV,EAAAO,EAAK,eAAL,YAAAP,EAAmB,KAAMvB,GAAMA,EAAE,WAAa,QAA9C,YAAAiC,EAAsDF,KACtD,EAEJ,EAGMhD,EAAwBC,GAAiB,CACvC,MAAAkD,EAAY,IAAI,gBAAgBjC,CAAY,EAC9CjB,EACQkD,EAAA,IAAI,WAAYlD,CAAY,EAEtCkD,EAAU,OAAO,UAAU,EAE7BA,EAAU,OAAO,MAAM,EACvBhC,EAAgBgC,CAAS,EACzB3B,EAAe,CAAC,CAClB,EAEM4B,EAAmBC,GAAY,CAC7B,MAAAF,EAAY,IAAI,gBAAgBjC,CAAY,EAC9CmC,EACQF,EAAA,IAAI,MAAOE,CAAO,EAE5BF,EAAU,OAAO,KAAK,EAExBA,EAAU,OAAO,MAAM,EACvBhC,EAAgBgC,CAAS,EACzB3B,EAAe,CAAC,CAClB,EAEM8B,EAAe,IAAM,CACzBnC,EAAgB,EAAE,EAClBK,EAAe,CAAC,CAClB,EAEM7D,EAAUL,EAAe,MAAM,EAErC,OAEIO,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAOiD,EAAE,iBAAiB,GAAKtD,EAAQ,MACvC,YAAasD,EAAE,uBAAuB,GAAKtD,EAAQ,YACnD,KAAK,OACL,KAAK,UACL,MAAM,gCACN,OAAQA,EAAQ,OAChB,SACEsD,EAAE,qBAAsB,CAAE,cAAe,EAAK,CAAC,GAAKtD,EAAQ,SAEhE,QACC,MAAI,WAAU,gBACb,SAACE,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAW,+CACTgE,GAAmBC,GAAcC,EAC7B,oBACA,EACN,GACA,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAApE,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,YAAY,EACjB,QACC,MAAI,WAAU,iBAAiB,iBAAe,OAC7C,eAAC,MAAI,WAAU,4CACb,SAAAA,MAAC,KAAE,UAAU,sCACV,WAAE,eAAe,EACpB,EACF,CACF,IAGEgE,GAAmBC,GAAcC,IAChClE,MAAA,OAAI,UAAU,wBACb,SAAAF,EAAA,KAAC,MAAI,WAAU,mEACb,UAACE,EAAA,YAAK,UAAU,qBAAqB,SAAY,iBAChDgE,GACClE,EAAA,KAAC,OAAK,WAAU,mBAAmB,sBACvB,KAERyE,EAAAtD,EAAW,KAAMuE,GAAMA,EAAE,OAASxB,CAAe,IAAjD,YAAAO,EACI,MAER,EAEDN,GACCnE,EAAA,KAAC,OAAK,WAAU,qBAAqB,mBAC7B2E,EAAAb,EAAK,KAAMV,GAAMA,EAAE,OAASe,CAAU,IAAtC,YAAAQ,EAAyC,MACjD,EAEDP,GACCpE,EAAA,KAAC,OAAK,WAAU,gBAAgB,sBACpBoE,EAAc,KAC1B,EAEFpE,EAAA,KAAC,UACC,QAASyF,EACT,UAAU,6CACV,oBAAkB,IAElB,UAACvF,EAAA,YAAK,UAAU,oCAAoC,SAEpD,kBACAA,EAAA,IAAC,QACC,UAAU,kCACV,cAAY,OACb,0BAED,GACF,EACF,CACF,GAEJ,GACF,EAEEF,OAAAC,WAAA,WAAAC,EAAA,IAAC,WACC,UAAU,uCACV,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,YAEb,UAAAA,EAAA,KAAC,OACC,UAAU,gCACV,kBAAiB,EAGhB,UACCuB,GAAArB,EAAA,IAAC,MAAI,WAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,WAAU,YAAa,SAAAkD,EAAE,cAAc,CAAE,GAChD,EAID,CAAC7B,GAAWiC,EAAU,SAAW,SAC/B,MAAI,WAAU,qBACb,SAAAtD,MAAC,OAAI,UAAU,YAAa,SAAEkD,EAAA,YAAY,CAAE,GAC9C,EAID,CAAC7B,GACA,MAAM,QAAQiC,CAAS,GACvBA,EAAU,IAAK0B,UACbhF,SAAA,IAAC,OAEC,UAAU,oCAEV,SAAAF,EAAA,KAAC,MAAI,WAAU,sBACb,UAACE,EAAA,WAAI,UAAU,gBACb,SAAAA,MAAC0C,GAAK,GAAI,gBAAgBsC,EAAK,IAAI,GACjC,SAAAhF,EAAA,IAAC,OACC,IACEgF,EAAK,eACL,yCAEF,MAAO,IACP,OAAQ,IACR,IAAKD,EAAeC,EAAM,OAAO,IAErC,CACF,GACChF,MAAA,MAAG,UAAU,kBACZ,eAAC0C,EAAK,IAAI,gBAAgBsC,EAAK,IAAI,GAChC,SAAAD,EAAeC,EAAM,OAAO,CAC/B,GACF,QACC,MAAI,WAAU,iBACZ,SAAeD,EAAAC,EAAM,SAAS,EACjC,EACAlF,OAAC,MAAI,WAAU,0BACb,UAACA,OAAA,OAAI,UAAU,cACb,UAACE,MAAA,KAAE,KAAK,IAAI,UAAU,cACpB,SAACA,MAAA,KAAE,UAAU,+BAA+B,CAC9C,SACC,IAAE,MAAK,IACL,WAAKuE,EAAAS,EAAA,qBAAAT,EAAQ,OAAQ,gBACxB,IACF,EACAzE,OAAC,MAAI,WAAU,YACb,UAACE,MAAA,KAAE,UAAU,kCAAmC,GAC/CA,EAAA,SAAE,KAAK,IACL,SAAI,SACHgF,EAAK,aAAeA,EAAK,SAC3B,EAAE,oBACJ,GACF,GACF,GACF,IA3CKA,EAAK,EA6Cb,KAIL,EAGAhF,EAAA,IAACyF,GAAA,CACC,YAAAjC,EACA,WAAAE,EACA,aAAcD,CAAA,EAChB,CAEF,GACF,EAGAzD,MAAC,KAAG,WAAU,iBAAkB,GAGhCA,EAAA,IAAC,UAAQ,WAAU,uCACjB,SAAAA,EAAA,IAAC,MAAI,WAAU,qBACb,SAAAF,OAAC,MAAI,WAAU,aACb,UAAAE,MAAC,OAAI,UAAU,0BAEb,SAACF,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eACX,SAAAkD,EAAE,iBAAiB,EACtB,EACClD,EAAA,WAAI,UAAU,cACb,SAACA,MAAA,MAAG,UAAU,wBACX,SAAWiB,EAAA,IAAKwB,GAAA,qBACd,KACC,WAAAzC,EAAA,IAAC,KACC,KAAK,IACL,MAAM,GACN,QAAU2C,GAAM,CACdA,EAAE,eAAe,EACjBV,EAAqBQ,EAAS,IAAI,CACpC,EACA,UACEuB,IAAoBvB,EAAS,KACzB,SACA,GAGL,SAASA,EAAA,KACZ,SACC,QACE,eAAI,OACF8B,EAAA9B,EAAS,SAAT,YAAA8B,EAAiB,YAAa,EAAG,IACtC,KAnBO9B,EAAS,EAoBlB,EACD,CACH,EACF,IACF,CAEF,SACC,MAAI,WAAU,0BAEb,SAAC3C,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eAAgB,SAAAkD,EAAE,WAAW,EAAE,EAC7ClD,EAAA,IAAC,MAAI,WAAU,cACb,SAAAA,MAAC,MAAI,WAAU,OACZ,SAAA4D,EAAK,IAAK8B,GACT1F,EAAA,IAAC,KACC,KAAK,IAEL,QAAU2C,GAAM,CACdA,EAAE,eAAe,EACjB0C,EAAgBK,EAAI,IAAI,CAC1B,EACA,UACEzB,IAAeyB,EAAI,KAAO,SAAW,GAGtC,SAAIA,EAAA,MATAA,EAAI,EAWZ,EACH,EACF,IACF,CAEF,SACC,MAAI,WAAU,0BAEb,SAAC5F,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eAAgB,SAAAkD,EAAE,cAAc,EAAE,EAC/ClD,MAAA,OAAI,UAAU,cACb,eAAC,KAAG,WAAU,wBACX,SAAA8D,EAAY,IAAI,CAAC6B,EAASrG,WACxB,KACC,WAAAQ,EAAA,KAAC,IAAE,MAAK,IAAI,MAAM,GACf,UAAQ6F,EAAA,UAAU,IAAEA,EAAQ,MAC/B,SACC,QAAM,iBAAIA,EAAQ,MAAM,IAAC,KAJnBrG,CAKT,CACD,EACH,CACF,IACF,CAEF,SACC,MAAI,WAAU,0BAEb,SAACQ,EAAA,YAAI,UAAU,cACb,UAAAE,MAAC,KAAG,WAAU,eACX,SAAAkD,EAAE,yBAAyB,EAC9B,QACC,MAAI,WAAU,cACb,SAACpD,EAAA,YAAI,UAAU,uBACb,UAAAE,EAAA,IAAC,OACC,IAAI,4BACJ,IAAI,iBACJ,OAAQ,GACR,MAAO,GACP,UAAU,gBACV,MAAO,CAAE,aAAc,KAAM,EAC/B,EACCkD,EAAE,wBAAwB,GAC7B,CACF,IACF,CAEF,GACF,EACF,EACF,GACF,IACF,QACC,SAAO,WAAU,6DAChB,SAAAlD,EAAA,IAACM,IAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHCtcMsC,GAAW,CACf,MACE,gGACF,YAAa,8DACf,EACA,SAAwBgD,IAAiC,CACvD,IAAIzB,EAAS0B,EAAU,EACvB,MAAMC,EACJC,EAAc,OAAQC,GAAQA,EAAI,IAAM7B,EAAO,EAAE,EAAE,CAAC,GAAK4B,EAAc,CAAC,EAC1E,OAEIjG,EAAA,KAAAC,WAAA,WAACC,MAAA8C,EAAA,CAAc,KAAMF,EAAU,SAC9B,MAAI,WAAU,gBACb,SAAC9C,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBACE,mDACJ,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,SAAc8F,EAAA,MACjB,QAEC,MAAI,WAAU,iBAAiB,iBAAe,OAC7C,SAAC9F,MAAA,OAAI,UAAU,4CACb,eAAC,IAAE,WAAU,sCAAsC,uDAEnD,EACF,CACF,GACF,GACF,EAGEF,OAAAC,WAAA,WAAAC,MAAC,WAAQ,UAAU,uCACjB,SAACF,EAAA,YAAI,UAAU,qBACb,UAACA,OAAA,OAAI,UAAU,qBAEb,UAACA,OAAA,OAAI,UAAU,oBACb,UAACE,EAAA,UAAG,UAAU,4BAA4B,SAE1C,oBACAA,MAAC,KAAG,WAAU,OAAQ,GACtBF,OAAC,MAAI,WAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,WACb,SAACA,MAAA,KAAE,iBAAK,CACV,GACCA,EAAA,WAAI,UAAU,WAAW,SAAa,mBACzC,EACAA,MAAC,KAAG,WAAU,OAAQ,GACtBF,OAAC,MAAI,WAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,WACb,SAACA,MAAA,KAAE,mBAAO,CACZ,GACCA,EAAA,WAAI,UAAU,WAAW,SAAY,kBACxC,EACAA,MAAC,KAAG,WAAU,OAAQ,GACtBF,OAAC,MAAI,WAAU,gBACb,UAAAE,MAAC,OAAI,UAAU,WACb,SAACA,MAAA,KAAE,qBAAS,CACd,GACCA,EAAA,WAAI,UAAU,WAAW,SAG1B,yEACF,EACAA,MAAC,KAAG,WAAU,OAAQ,IACxB,EAGAF,OAAC,MAAI,WAAU,WACb,UAACE,EAAA,UAAG,UAAU,4BAA4B,SAE1C,gBACAA,MAAC,KAAG,WAAU,OAAQ,GACrBA,EAAA,SAAE,UAAU,iBAAiB,SAU9B,idACF,IAEF,EACAF,OAAC,MAAI,WAAU,aAEb,UAACE,MAAA,OAAI,UAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,OAEZ,EAGAA,MAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,OAEZ,EAGAA,MAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,OAEZ,EAGAA,MAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OACC,IAAI,oDACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,MAEZ,GAEF,IACF,CACF,GAGAA,MAAC,KAAG,WAAU,iBAAkB,IAElC,QACC,UAAQ,WAAU,uCACjB,SAAAA,MAACiG,IAAgB,GACnB,EAGEnG,OAAAC,WAAA,WAACC,MAAA,MAAG,UAAU,iBAAkB,GAGhCF,OAAC,MAAI,WAAU,+EACb,UAAAE,MAAC0C,GAAK,GAAI,6BAA8B,UAAU,YAChD,gBAAC,OACC,WAAC1C,MAAA,KAAE,UAAU,oCAAqC,GAAG,IAAI,YAE3D,CACF,SACC,IAAE,MAAK,IAAI,UAAU,WACpB,gBAAC,OACC,WAACA,MAAA,KAAE,UAAU,+BAAgC,GAAE,cACjD,CACF,SACC0C,EAAK,IAAI,6BAA8B,UAAU,YAChD,gBAAC,OAAK,mBACC1C,MAAC,IAAE,WAAU,qCAAsC,IAC1D,CACF,GACF,GAEF,IACF,QACC,SAAO,WAAU,6DAChB,SAAAA,EAAA,IAACM,IAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHCtLA,SAAwB4F,IAAgC,CAChD,MAAE,GAAAC,CAAG,EAAIN,EAAU,EACnBO,EAAWC,EAAY,EACvB,CAAE,EAAG7F,EAAW,KAAAC,CAAA,EAASC,EAAe,EACxCC,EAAkBF,EAAK,UAAY,KAEnC,CAACvB,EAASoH,CAAU,EAAIxF,WAAS,IAAI,EACrC,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAErCW,YAAU,IAAM,CACF8E,EAAA,GACX,CAACJ,EAAIxF,CAAe,CAAC,EAExB,MAAM4F,EAAc,SAAY,CAC1B,IACFjF,EAAW,EAAI,EAIT,MAAAO,EAAO,MAHI,MAAM,MACrB,iBAAiBsE,CAAE,aAAaxF,CAAe,EACjD,GAC4B,KAAK,EAE7BkB,EAAK,SACPyE,EAAWzE,EAAK,OAAO,EAGvBS,EAAW,sBAAuB,CAChC,WAAYT,EAAK,QAAQ,GACzB,cAAeA,EAAK,QAAQ,MAC5B,SAAUlB,EACV,OAAQ,cACT,GAEDa,EAAS,mBAAmB,QAEvBO,EAAK,CACJ,cAAM,yBAA0BA,CAAG,EAC3CP,EAAS,uBAAuB,SAChC,CACAF,EAAW,EAAK,EAEpB,EAEMkF,EAAkB,IAAM,CAC5BlE,EAAW,aAAc,CACvB,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,SAAUyB,EACV,OAAQ,iBACT,CACH,EAEM8F,EAAuBC,GAAS,CACpCpE,EAAW,kBAAmB,CAC5B,WAAYpD,EAAQ,GACpB,cAAeA,EAAQ,MACvB,cAAewH,EACf,SAAU/F,EACV,OAAQ,iBACT,CACH,EAEMgG,EAAeC,GACdA,EACE,IAAI,KAAK,aAAa,QAAS,CACpC,MAAO,WACP,SAAU,MACX,EAAE,OAAOA,CAAK,EAJI,KAOrB,GAAIvF,EACF,aACG,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACrB,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,QACC,OAAK,IAAG,OACP,eAAC,UAAQ,WAAU,uCACjB,SAACsB,MAAA,MAAI,WAAU,6BACb,SAACA,MAAA,OAAI,UAAU,8BAA8B,KAAK,SAChD,SAACA,MAAA,QAAK,UAAU,kBAAkB,SAAU,eAC9C,EACF,EACF,CACF,GACF,EACF,GACF,EAIA,GAAAuB,GAAS,CAACrC,EACZ,aACG,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACc,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,QACC,OAAK,IAAG,OACP,SAACsB,EAAA,cAAQ,WAAU,uCACjB,SAAAF,OAAC,MAAI,WAAU,6BACb,UAACE,MAAA,MAAG,SAAiB,sBACpBA,MAAA,KAAE,SAA6C,kDAChDA,EAAA,IAAC,UACC,QAAS,IAAMoG,EAAS,IAAIzF,CAAe,WAAW,EACtD,UAAU,wBACX,6BAED,CACF,EACF,EACF,GACF,EACF,GACF,EAKJ,MAAMkG,EAAgB5H,GAAsB,CAC1C,MAAOC,EAAQ,MACf,YAAaA,EAAQ,QACrB,MAAOA,EAAQ,iBAAmBA,EAAQ,kBAC1C,cAAeA,EAAQ,cACvB,IAAK,wBAAwByB,CAAe,oBAAoBzB,EAAQ,IAAI,GAC7E,EAGK4H,EAAmB3H,GAAyB,CAChD,CAAE,KAAM,OAAQ,IAAK,wBAAwBwB,CAAe,EAAG,EAC/D,CACE,KAAM,WACN,IAAK,wBAAwBA,CAAe,WAC9C,EACA,CACE,KAAMzB,EAAQ,MACd,IAAK,wBAAwByB,CAAe,oBAAoBzB,EAAQ,IAAI,GAC9E,CACD,EAEK6H,EAAe7H,EAAQ,cACzB,gDAE2BA,EAAQ,aAAa,GAChD,oCAIA,OAAAY,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAOf,EAAQ,WAAaA,EAAQ,MACpC,YAAaA,EAAQ,iBAAmBA,EAAQ,QAChD,KAAM,mBAAmBA,EAAQ,IAAI,GACrC,KAAK,UACL,MAAO6H,EACP,SAAU7H,EAAQ,kBAAoBA,EAAQ,MAC9C,OAAQ,CAAC2H,EAAeC,CAAgB,EACxC,SACE5H,EAAQ,aACJA,EAAQ,aAAa,MAAM,GAAG,EAAE,IAAKqD,GAAMA,EAAE,KAAM,GACnD,CAAC,WAAY,WAAY,WAAW,EAE1C,YAAarD,EAAQ,YACrB,WAAYA,EAAQ,UACtB,QAEC,OAAI,UAAU,gBACb,eAAC,OAAI,UAAU,YACb,gBAAC,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAACc,MAAA,OAAI,UAAU,8DACb,SAACA,MAAAE,EAAO,OAAOxB,EAAW,EAC5B,EAECoB,OAAA,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,yCACjB,cAAe,MACjB,EAEA,SAACA,EAAA,WAAI,UAAU,6CACb,eAAC,MAAI,WAAU,cACb,SAACA,MAAA,OAAI,UAAU,MACb,gBAAC,OAAI,UAAU,uBACb,UAACA,MAAA,MAAG,UAAU,mBACZ,SAAAA,EAAA,IAAC,QACC,UAAU,kBACV,iBAAe,QAEd,SAAQd,EAAA,QAEb,QACC,MAAI,WAAU,MACb,SAACc,MAAA,OAAI,UAAU,uBACb,SAAAA,EAAA,IAAC,KACC,UAAU,iCACV,iBAAe,OAEd,SAAQd,EAAA,QACX,EACF,CACF,IACF,EACF,EACF,CACF,GACF,EAGAc,EAAA,IAAC,WACC,UAAU,uCACV,MAAO,CAAE,WAAY,MAAO,EAE5B,eAAC,MAAI,WAAU,8BAEb,SAACF,OAAA,OAAI,UAAU,MACb,UAACA,OAAA,OAAI,UAAU,WAEb,UAAAE,EAAA,IAACgH,GAAA,CACC,OAAQ9H,EAAQ,OAChB,aAAcA,EAAQ,MACxB,EAGCc,MAAA,MAAI,WAAU,iBACb,SAAAA,EAAA,IAAC,OACC,UAAU,oBACV,wBAAyB,CAAE,OAAQd,EAAQ,OAAQ,IAEvD,EAGCY,OAAA,MAAI,WAAU,wCACZ,UAAQZ,EAAA,YACPA,EAAQ,WAAW,OAAS,GACzBY,EAAA,YAAI,UAAU,OACb,UAACE,MAAA,UAAO,SAAY,iBACnBd,EAAQ,WAAW,IAAI,CAACkD,EAAK9C,IAC3BU,MAAA,OACC,UAACA,MAAA,QAAK,UAAU,wBACb,WAAI,SAAS,IAChB,IAHSoC,EAAI,SAAS,EAIxB,CACD,GACH,EAGHlD,EAAQ,MAAQA,EAAQ,KAAK,OAAS,UACpC,MACC,WAACc,MAAA,UAAO,SAAM,WACbd,EAAQ,KAAK,IAAI,CAACwG,EAAKpG,IACrBU,MAAA,OACC,UAACA,MAAA,QAAK,UAAU,0BACb,WAAI,IAAI,IACX,IAHS0F,EAAI,IAAI,EAInB,CACD,EACH,GAEJ,IACF,QAGC,MAAI,WAAU,WACb,SAAC5F,EAAA,YAAI,UAAU,uBAEZ,UAAAZ,EAAQ,iBACPc,MAAC,MAAI,WAAU,cACb,SAAAF,EAAA,KAAC,OACC,UAAU,oCACV,MAAO,CACL,WAAY,4BACZ,OAAQ,sCACR,WAAY,eACd,EAEA,gBAAC,MAAG,UAAU,8BAA8B,SAE5C,uBACCA,OAAA,MAAI,WAAU,mBACb,UAAAE,EAAA,IAAC,OACC,UAAU,qBACV,MAAO,CACL,SAAU,SACV,WAAY,KACd,EAEC,SAAA2G,EAAYzH,EAAQ,eAAe,EACtC,QACC,SAAM,UAAU,YAAY,SAE7B,sBACF,QACC,KAAE,UAAU,mCAAmC,SAGhD,yDACCc,MAAA,MAAI,WAAU,cACb,SAAAA,EAAA,IAAC,UACC,UAAU,6DACV,QAAS,IACPyG,EAAoB,YAAY,EAElC,MAAO,CAAE,SAAU,OAAQ,EAE3B,SAACzG,MAAA,QAAK,SAAc,oBAExB,MAEJ,EAGDd,EAAQ,mBACNc,MAAA,OAAI,UAAU,cACb,SAAAF,EAAA,KAAC,OACC,UAAU,oCACV,MAAO,CACL,WAAY,4BACZ,OAAQ,sCACR,WAAY,eACd,EAEA,gBAAC,MAAG,UAAU,8BAA8B,SAE5C,iBACCA,OAAA,MAAI,WAAU,mBACb,UAAAA,EAAA,KAAC,OACC,UAAU,qBACV,MAAO,CACL,SAAU,SACV,WAAY,KACd,EAEC,UAAA6G,EAAYzH,EAAQ,iBAAiB,EACtCc,EAAA,IAAC,SACC,MAAO,CACL,SAAU,OACV,WAAY,KACd,EACD,gBAED,EACF,QACC,SAAM,UAAU,YAAY,SAE7B,qBACF,QACC,KAAE,UAAU,mCAAmC,SAGhD,sDACCA,MAAA,MAAI,WAAU,cACb,SAAAA,EAAA,IAAC,UACC,UAAU,mDACV,QAAS,IACPyG,EAAoB,cAAc,EAEpC,MAAO,CACL,SAAU,QACV,WAAY,UACZ,YAAa,UACb,MAAO,MACT,EAEA,SAACzG,MAAA,QAAK,SAAkB,wBAE5B,MAEJ,EAGDd,EAAQ,SACNc,MAAA,OAAI,UAAU,cACb,SAACA,MAAA,MAAI,WAAU,mBACb,SAAAA,EAAA,IAAC,KACC,KAAMd,EAAQ,QACd,OAAO,SACP,IAAI,sBACJ,UAAU,qBACV,QAASsH,EACT,MAAO,CAAE,OAAQ,SAAU,EAE3B,SAAAxG,EAAA,IAAC,QACC,UAAU,gDACV,mBAAiB,IAEjB,SAACF,EAAA,YAAK,WAAU,gBACd,gBAAC,QAAK,UAAU,kBAAkB,SAElC,mBACAE,EAAA,IAAC,QACC,UAAU,kBACV,cAAY,OACb,2BAED,CACF,IACF,CACF,EACF,EACF,EAIDF,OAAA,MAAI,WAAU,SACb,gBAAC,MAAG,UAAU,eAAe,SAAmB,wBAC/CA,OAAA,KAAG,WAAU,gBACZ,UAACA,OAAA,MAAG,UAAU,OACZ,UAACE,MAAA,UAAO,SAAU,eAAU,IAC3B,IAAI,KACHd,EAAQ,aACR,mBAAmB,GACvB,EACCY,OAAA,KAAG,WAAU,OACZ,UAACE,MAAA,UAAO,SAAa,kBAAU,IAC9B,IAAI,KAAKd,EAAQ,SAAS,EAAE,mBAAmB,GAClD,EACCY,OAAA,KAAG,WAAU,OACZ,UAACE,MAAA,UAAO,SAAM,WAAS,IAAEd,EAAQ,UACnC,GACF,GACF,IACF,CACF,IACF,CACF,IACF,EACF,QAEC,SAAO,WAAU,mCAChB,SAAAc,EAAA,IAACM,IAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHCrbA,SAAwB2G,IAA4B,iBAClD,IAAI9C,EAAS0B,EAAU,EACvB,KAAM,CAAE,EAAA3C,EAAG,KAAAzC,CAAK,EAAIC,EAAe,EAC7BC,EAAkBF,EAAK,UAAY,KACxB4F,EAAY,EAC7B,KAAM,CAACa,EAAMC,CAAO,EAAIrG,WAAS,IAAI,EAC/B,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAACS,EAAOC,CAAQ,EAAIV,WAAS,EAAE,EAErCW,YAAU,IAAM,CACd,MAAM2F,EAAgB,SAAY,CAC5B,IACF9F,EAAW,EAAI,EACf,MAAM+F,EAAS,MAAMhD,EAAQ,QAAQF,EAAO,EAAE,EAE1CkD,EAAO,SAAS,IAAMA,EAAO,MACvB,YAAI,4BAA6BA,EAAO,IAAI,EACpDF,EAAQE,EAAO,KAAK,MAAQA,EAAO,IAAI,IAEvC,QAAQ,MAAM,6BAA8BA,EAAO,SAAS,MAAM,EAClE7F,EAAS,qBAAqB,SAEzBD,EAAO,CACN,cAAM,4BAA6BA,CAAK,EAChDC,EAAS,0BAA0B,SACnC,CACAF,EAAW,EAAK,EAEpB,EAEI6C,EAAO,IACKiD,EAAA,CAChB,EACC,CAACjD,EAAO,EAAE,CAAC,EAGd1C,YAAU,IAAM,CACVyF,GAAQ,CAAC7F,GAEX,WAAW,SAAY,CACjB,IAEF,KAAM,CAAE,oBAAAiG,CAAA,EAAwB,MAAAC,GAAA,oCAAAD,GAAA,KAAM,QACpC,kCACF,qEACM,MAAAA,EAAoB,yBAA0B,aAAa,QAC1D/F,EAAO,CACN,aAAK,sCAAuCA,CAAK,IAE1D,GAAG,CACR,EACC,CAAC2F,EAAM7F,CAAO,CAAC,EAGZ,MAAA0D,EAAiB,CAACC,EAAMC,IAAU,OACtC,GAAI,CAACD,GAAQ,CAACA,EAAK,aAAqB,SAClC,MAAAE,EAAcF,EAAK,aAAa,KACnC9B,GAAMA,EAAE,WAAavC,CACxB,EACA,OACEuE,GAAA,YAAAA,EAAcD,OACdV,EAAAS,EAAK,aAAa,KAAM9B,GAAMA,EAAE,WAAa,IAAI,IAAjD,YAAAqB,EAAqDU,KACrD,EAEJ,EAGA,GAAI5D,EACF,OACGrB,EAAA,WAAI,UAAU,gBACb,SAACA,MAAA,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,SACC,OAAK,IAAG,OACP,SAACsB,MAAA,WAAQ,UAAU,uCACjB,SAAAA,MAAC,OAAI,UAAU,YACb,eAAC,MAAI,WAAU,MACb,SAACF,EAAA,YAAI,UAAU,qBACb,UAAAE,MAAC,MAAG,SAAU,eACdA,MAAC,KAAE,SAAwC,4CAC7C,GACF,GACF,EACF,CACF,GACF,EACF,GACF,EAKA,IAACkH,GAAQ3F,EACX,OACGvB,EAAA,WAAI,UAAU,gBACb,SAACA,MAAA,OAAI,UAAU,YACb,SAACF,EAAA,YAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,SACC,OAAK,IAAG,OACP,SAACsB,MAAA,WAAQ,UAAU,uCACjB,SAAAA,MAAC,OAAI,UAAU,YACb,eAAC,MAAI,WAAU,MACb,SAACF,EAAA,YAAI,UAAU,qBACb,UAAAE,MAAC,MAAG,SAAmB,wBACvBA,MAAC,KAAE,SAGH,oDACAA,EAAA,IAAC,KACC,KAAK,QACL,UAAU,6CACX,yBAGH,GACF,GACF,EACF,CACF,GACF,EACF,GACF,EAKJ,MAAMwH,EAAgBzI,GAAsB,CAC1C,MAAOgG,EAAemC,EAAM,OAAO,EACnC,YAAanC,EAAemC,EAAM,SAAS,EAC3C,QAASnC,EAAemC,EAAM,SAAS,EACvC,cAAeA,EAAK,cACpB,OAAQA,EAAK,QAAU,YACvB,YAAaA,EAAK,YAClB,WAAYA,EAAK,UACjB,IAAK,wBAAwBvG,CAAe,gBAAgBuG,EAAK,IAAI,GACtE,EAED,OAEIpH,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAO8E,EAAemC,EAAM,OAAO,EACnC,YAAanC,EAAemC,EAAM,SAAS,EAC3C,KAAM,eAAeA,EAAK,IAAI,GAC9B,KAAK,UACL,MAAOA,EAAK,eAAiB,gCAC7B,SAAUnC,EAAemC,EAAM,OAAO,EACtC,OAAQA,EAAK,QAAU,YACvB,YAAaA,EAAK,YAClB,WAAYA,EAAK,UACjB,OAAQ,CAACM,CAAa,EACtB,SACEzC,EAAemC,EAAM,UAAU,GAAK,CAClC,OACA,uBACA,YACF,CAEJ,QACC,MAAI,WAAU,gBACb,SAACpH,EAAA,YAAI,UAAU,YACb,UAAAA,EAAA,KAAC,MAAI,WAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OACP,UAAAE,EAAA,IAAC,WACC,UAAU,gEACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,MAAC,OAAI,UAAU,MACb,SAACA,MAAA,OAAI,UAAU,wBACb,SAAAA,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,SAAA+E,EAAemC,EAAM,OAAO,IAEjC,CACF,GAEApH,EAAA,KAAC,OACC,UAAU,gDACV,iBAAe,OAEf,UAAAE,MAAC,OAAI,UAAU,sBACb,SAACF,EAAA,UAAE,KAAK,IACN,UAACE,MAAA,KAAE,UAAU,kBAAmB,GAC/BA,EAAA,YAAK,UAAU,kBAAkB,SAAK,UAAQ,IAC9C,IAAI,KACHkH,EAAK,aAAeA,EAAK,WACzB,mBAAmB,QAAS,CAC5B,KAAM,UACN,MAAO,OACP,IAAK,SACN,IACH,CACF,SACC,MAAI,WAAU,sBACb,SAACpH,EAAA,UAAE,KAAK,IACN,UAACE,MAAA,KAAE,UAAU,iBAAkB,GAC9BA,EAAA,YAAK,UAAU,kBAAkB,SAAO,YAAQ,MAChDuE,EAAA2C,EAAK,SAAL,YAAA3C,EAAa,OAAQ,kBACxB,CACF,GACC2C,EAAK,YAAcA,EAAK,WAAW,OAAS,GAC3CpH,EAAA,KAAC,MAAI,WAAU,sBACb,UAACE,MAAA,KAAE,UAAU,mBAAoB,GAChCA,EAAA,YAAK,UAAU,kBAAkB,SAAS,cAC3CA,MAAC,KAAE,KAAK,IAAK,WAAK,WAAW,CAAC,EAAE,IAAK,IACvC,EAEFF,OAAC,MAAI,WAAU,sBACb,UAACE,MAAA,KAAE,UAAU,iBAAkB,GAC9BA,EAAA,YAAK,UAAU,kBAAkB,SAAU,eAAQ,IACnDkH,EAAK,UAAY,EAAE,OACtB,IACF,QAIC,MAAI,WAAU,mBACb,SAACpH,EAAA,YAAI,UAAU,oDAEb,UAACA,OAAA,OAAI,UAAU,eACb,UAAAA,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,QACvB,UAAU,6CACV,oBAAkB,IAElB,UAACb,OAAA,QAAK,UAAU,oCACd,UAACE,MAAA,KAAE,UAAU,oCAAqC,GAAE,IAC7CkD,EAAE,mBAAmB,GAAK,gBACnC,EACApD,EAAA,KAAC,QACC,UAAU,kCACV,cAAY,OAEZ,UAACE,MAAA,KAAE,UAAU,oCAAqC,GAAE,IAC7CkD,EAAE,mBAAmB,GAAK,iBACnC,EACF,IAECuB,EAAAyC,GAAA,YAAAA,EAAM,aAAN,YAAAzC,EAAkB,WACjB3E,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,SAAS,IAAI,GACpE,UAAU,6CACV,oBAAkB,IAClB,MAAOA,EAAK,WAAW,SAAS,MAEhC,UAACpH,OAAA,QAAK,UAAU,oCACd,UAACE,MAAA,KAAE,UAAU,sCAAuC,GAAE,IAC/CkD,EAAE,eAAe,GAAK,YAC/B,EACApD,EAAA,KAAC,QACC,UAAU,kCACV,cAAY,OAEZ,UAACE,MAAA,KAAE,UAAU,sCAAuC,GAAE,IAC/CkD,EAAE,eAAe,GAAK,aAC/B,GACF,EAEJ,EAGClD,EAAA,WACE,WAAMmF,EAAA+B,GAAA,YAAAA,EAAA,yBAAA/B,EAAY,OACjBrF,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,KAAK,IAAI,GAChE,UAAU,6CACV,oBAAkB,IAClB,MAAOA,EAAK,WAAW,KAAK,MAE5B,UAACpH,OAAA,QAAK,UAAU,oCACb,UAAAoD,EAAE,WAAW,GAAK,OAAO,IAC1BlD,MAAC,IAAE,WAAU,uCAAwC,IACvD,EACAF,EAAA,KAAC,QACC,UAAU,kCACV,cAAY,OAEX,UAAAoD,EAAE,WAAW,GAAK,OAAO,IAC1BlD,MAAC,IAAE,WAAU,uCAAwC,KACvD,GAGN,IACF,CACF,GACF,GACF,EACAA,EAAA,IAAC,UAAQ,WAAU,uCACjB,SAAAA,EAAA,IAAC,MAAI,WAAU,qBACb,SAAAF,OAAC,MAAI,WAAU,MAEb,UAACA,OAAA,OAAI,UAAU,0DAEb,UAAAE,MAAC,OAAI,UAAU,2BACb,SAACF,EAAA,YAAI,UAAU,iBACZ,UAAAoH,EAAK,eACJlH,MAAC,MAAI,WAAU,iBACb,SAAAA,EAAA,IAAC,OACC,IAAKkH,EAAK,cACV,IAAKnC,EAAemC,EAAM,OAAO,EACjC,MAAO,KACP,OAAQ,MAEZ,QAID,MAAI,WAAU,aACZ,SAAenC,EAAAmC,EAAM,SAAS,EACjC,EAGAlH,EAAA,IAAC,OACC,UAAU,eACV,MAAO,CACL,WAAY,MACZ,SAAU,MACZ,EACA,wBAAyB,CACvB,OAAQ+E,EAAemC,EAAM,SAAS,EACxC,EACF,EACF,CACF,GAGApH,OAAC,MAAI,WAAU,iCACb,UAACA,OAAA,MAAG,UAAU,kBACX,UAAAoD,EAAE,qBAAqB,EAAG,IAC3BpD,OAAC,QAAM,WAAU,SAAS,gBACtB2H,EAAAP,EAAK,WAAL,YAAAO,EAAe,SAAU,EAAE,IAC/B,IACF,EACAzH,MAAC,KAAG,WAAU,oCACZ,SAAAA,MAAC0H,GAAS,UAAUR,EAAK,UAAY,EAAI,EAC3C,IACF,EAGApH,OAAC,MAAI,WAAU,iCACb,UAAAE,MAAC,KAAG,WAAU,kBACX,SAAAkD,EAAE,2BAA2B,EAChC,EAEClD,MAAA2H,GAAA,CAAK,SAAUT,EAAK,IAAM,IAE7B,EAGApH,OAAC,MAAI,WAAU,iBACZ,YAAA8H,EAAAV,GAAA,YAAAA,EAAM,aAAN,YAAAU,EAAkB,WACjB9H,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,SAAS,IAAI,GACpE,UAAU,sBAEV,UAAClH,MAAA,KAAE,UAAU,iBAAkB,GAAE,IAC1BkD,EAAE,eAAe,GAAK,YAC/B,IAED2E,EAAAX,GAAA,YAAAA,EAAM,aAAN,YAAAW,EAAkB,OACjB/H,EAAA,KAAC4C,EAAA,CACC,GAAI,IAAI/B,CAAe,gBAAgBuG,EAAK,WAAW,KAAK,IAAI,GAChE,UAAU,uBAET,UAAAhE,EAAE,WAAW,GAAK,OAAO,IAC1BlD,MAAC,IAAE,WAAU,kBAAmB,KAClC,CAEJ,IAEF,EAGAA,MAAC,OAAI,UAAU,oBACb,eAAC8H,GAAQ,kBAAiB,0CAA2C,EAEvE,GAEF,EACF,EACF,IACF,QACC,SAAO,WAAU,6DAChB,SAAA9H,EAAA,IAACM,IAAO,CACV,IACF,EAAO,KACT,CACF,IACF,CAEJ,gHC5ZA,SAAwByH,IAAoB,CAC1C,KAAM,CAAE,EAAA7E,EAAG,KAAAzC,CAAK,EAAIC,EAAe,EAC7BC,EAAkBF,EAAK,UAAY,KAEzC,OAEIX,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAM,iBACN,YAAY,yHACZ,KAAK,iBACL,KAAK,UACL,SAAU,CAAC,iBAAkB,kBAAmB,YAAa,MAAM,EACrE,EAECD,MAAA,OAAI,UAAU,gBACb,SAACA,EAAA,WAAI,UAAU,YACb,SAACF,OAAA,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,8DACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GAEAoB,OAAC,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,eAAe,EACpB,EACCA,MAAA,OAAI,UAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,OAAI,UAAU,4CACb,SAACF,EAAA,UAAE,UAAU,sCACV,UAAAoD,EAAE,qBAAqB,EAAE,IAAE,IACvB,WAAO,mBAAmBvC,EAAiB,CAC9C,KAAM,UACN,MAAO,OACP,IAAK,SACN,GACH,EACF,GACF,EACAX,MAAC,MAAI,WAAU,cAAe,GAChC,GACF,EAGCA,MAAA,WAAQ,UAAU,uCACjB,eAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,OAAI,UAAU,uBAEb,UAACA,OAAA,OAAI,UAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,qBAAqB,CAAE,IACrD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QAEC,KAAG,WAAU,sBACX,SAAAA,EAAE,gCAAgC,EACrC,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,+BAA+B,EACpC,EACApD,OAAC,KAAG,WAAU,kBACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,gCAAgC,CAAE,GACxClD,EAAA,UAAI,SAAEkD,EAAA,gCAAgC,CAAE,GACxClD,EAAA,UAAI,SAAEkD,EAAA,gCAAgC,CAAE,GACxClD,MAAA,MAAI,SAAEkD,EAAA,gCAAgC,CAAE,IAC3C,QAEC,KAAG,WAAU,sBACX,SAAAA,EAAE,6BAA6B,EAClC,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,4BAA4B,EACjC,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACrClD,EAAA,UAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACrClD,EAAA,UAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACrClD,MAAA,MAAI,SAAEkD,EAAA,6BAA6B,CAAE,GACxC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,mBAAmB,EACxB,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,kBAAkB,EACvB,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,EAAA,UAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC3BlD,MAAA,MAAI,SAAEkD,EAAA,mBAAmB,CAAE,GAC9B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,sBAAsB,EAC3B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,MAAA,MAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAClC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,wBAAwB,EAC7B,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,uBAAuB,CAC5B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,sBAAsB,EAC3B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,qBAAqB,EAC1B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,MAAA,MAAI,SAAEkD,EAAA,sBAAsB,CAAE,GACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,IACtD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,sBAAsB,EAC3B,EACApD,OAAC,MAAI,WAAU,YACb,UAAAE,MAAC,IACC,UAAAA,EAAA,IAAC,SAAO,wBAAY,GACtB,EACCA,EAAA,SAAG,SAAEkD,EAAA,yBAAyB,CAAE,GAChClD,EAAA,SAAG,SAAEkD,EAAA,uBAAuB,CAAE,GAC9BlD,MAAA,KAAG,SAAEkD,EAAA,uBAAuB,CAAE,GACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,GACtD,IACF,EACF,EACF,CACF,IACF,QAEC,SAAO,WAAU,6DAChB,SAAAlD,EAAA,IAACM,IAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHCjNA,SAAwB0H,IAAsB,CAC5C,KAAM,CAAE,EAAA9E,EAAG,KAAAzC,CAAK,EAAIC,EAAe,EAC7BC,EAAkBF,EAAK,UAAY,KAEzC,OAEIX,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAM,uBACN,YAAY,uIACZ,KAAK,mBACL,KAAK,UACL,SAAU,CACR,uBACA,QACA,YACA,oBACF,CACF,EAECD,MAAA,OAAI,UAAU,gBACb,SAACA,EAAA,WAAI,UAAU,YACb,SAACF,OAAA,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,8DACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GAEAoB,OAAC,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,aAAa,EAClB,EACCA,MAAA,OAAI,UAAU,iBAAiB,iBAAe,OAC7C,SAACA,MAAA,OAAI,UAAU,4CACb,SAACF,EAAA,UAAE,UAAU,sCACV,UAAAoD,EAAE,mBAAmB,EAAE,IAAE,IACrB,WAAO,mBAAmBvC,EAAiB,CAC9C,KAAM,UACN,MAAO,OACP,IAAK,SACN,GACH,EACF,GACF,EACAX,MAAC,MAAI,WAAU,cAAe,GAChC,GACF,EAGCA,MAAA,WAAQ,UAAU,uCACjB,eAAC,MAAI,WAAU,8BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,OAAI,UAAU,uBAEb,UAACA,OAAA,OAAI,UAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,uBAAuB,CAC5B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,sBAAsB,EAC3B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,qBAAqB,EAC1B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,EAAA,UAAI,SAAEkD,EAAA,sBAAsB,CAAE,GAC9BlD,MAAA,MAAI,SAAEkD,EAAA,sBAAsB,CAAE,GACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,8BAA8B,EACnC,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,6BAA6B,EAClC,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,EAAA,UAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACtClD,MAAA,MAAI,SAAEkD,EAAA,8BAA8B,CAAE,GACzC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,oBAAoB,EACzB,EACApD,OAAC,KAAG,WAAU,kBACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,EAAA,UAAI,SAAEkD,EAAA,qBAAqB,CAAE,GAC7BlD,MAAA,MAAI,SAAEkD,EAAA,qBAAqB,CAAE,IAChC,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,qBAAqB,CAAE,IACrD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,gBAAgB,EACrB,QACC,IAAE,WAAU,kBAAmB,SAAAA,EAAE,cAAc,EAAE,EAClDpD,OAAC,IAAE,WAAU,kBACV,UAAAoD,EAAE,iBAAiB,EAAE,uDAExB,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,iBAAiB,CAAE,IACjD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,6BAA6B,EAClC,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,4BAA4B,CACjC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,sBAAsB,EAC3B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,EAAA,UAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAC/BlD,MAAA,MAAI,SAAEkD,EAAA,uBAAuB,CAAE,GAClC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,wBAAwB,EAC7B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,uBAAuB,EAC5B,EACApD,OAAC,KAAG,WAAU,YACZ,UAACE,EAAA,UAAI,SAAEkD,EAAA,wBAAwB,CAAE,GAChClD,EAAA,UAAI,SAAEkD,EAAA,wBAAwB,CAAE,GAChClD,MAAA,MAAI,SAAEkD,EAAA,wBAAwB,CAAE,GACnC,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,yBAAyB,EAC9B,QACC,IAAE,WAAU,YACV,SAAAA,EAAE,wBAAwB,CAC7B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,uBAAuB,EAC5B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,IACtD,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,kBACV,SAAAA,EAAE,oBAAoB,EACzB,EACApD,OAAC,MAAI,WAAU,YACb,UAAAE,MAAC,IACC,UAAAA,EAAA,IAAC,SAAO,wBAAY,GACtB,EACCA,EAAA,SAAG,SAAEkD,EAAA,uBAAuB,CAAE,GAC9BlD,EAAA,SAAG,SAAEkD,EAAA,qBAAqB,CAAE,GAC5BlD,MAAA,KAAG,SAAEkD,EAAA,qBAAqB,CAAE,GAC/B,IACF,EAGApD,OAAC,MAAI,WAAU,QACb,UAAAE,MAAC,KAAG,WAAU,4BACX,SAAAkD,EAAE,qBAAqB,EAC1B,QACC,IAAE,WAAU,YAAa,SAAAA,EAAE,oBAAoB,CAAE,GACpD,IACF,EACF,EACF,CACF,IACF,QAEC,SAAO,WAAU,6DAChB,SAAAlD,EAAA,IAACM,IAAO,CACV,GACF,EACF,EACF,IACF,CAEJ,gHCpPA,SAAwB2H,IAAmB,CACnC,MAAE,EAAA/E,CAAE,EAAIxC,EAAe,EAE7B,OAEIZ,EAAA,KAAAC,WAAA,WAAAC,EAAA,IAACC,EAAA,CACC,MAAM,uBACN,YAAY,6GACZ,KAAK,MACL,KAAK,UACL,MAAM,+BACN,SAAU,CAAC,MAAO,iBAAkB,QAAS,WAAW,EAC1D,EACCD,MAAA,OAAI,UAAU,gBACb,SAACA,EAAA,WAAI,UAAU,YACb,SAACF,OAAA,OAAI,UAAU,iBAAiB,GAAG,MACjC,UAAAE,MAAC,OAAI,UAAU,oDACb,eAACE,EAAO,OAAOxB,EAAW,CAC5B,GACAoB,OAAC,OAAK,IAAG,OAEP,UAAAE,EAAA,IAAC,WACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,WAAU,yDACb,UAAAE,EAAA,IAAC,MACC,UAAU,qCACV,oBAAkB,OAClB,MAAO,CAAE,SAAU,OAAQ,WAAY,KAAM,EAC9C,eAED,EACAA,MAAC,OAAI,UAAU,iBAAiB,iBAAe,OAC7C,SAAAA,MAAC,MAAI,WAAU,4CACb,SAAAA,EAAA,IAAC,KACC,UAAU,uCACV,MAAO,CAAE,SAAU,MAAO,EAC3B,4BAGH,CACF,GACAA,MAAC,MAAI,WAAU,cAAe,GAChC,GACF,EAGAA,MAAC,WAAQ,UAAU,uCACjB,eAAC,MAAI,WAAU,8BACb,SAACA,MAAA,OAAI,UAAU,MACb,SAAAA,MAAC,OAAI,UAAU,mCACb,gBAAC,MAAI,WAAU,eAAe,iBAAe,OAC3C,UAACF,OAAA,MAAG,UAAU,+BACZ,UAACE,EAAA,YAAK,UAAU,YAAY,SAAK,UAAO,wBAEvCA,EAAA,YAAK,UAAU,YAAY,SAAC,OAC/B,EACAF,OAAC,MAAI,WAAU,2BACb,UAACE,EAAA,SAAE,UAAU,QAAQ,SAIrB,uIACCA,EAAA,SAAE,UAAU,OAAO,SAGpB,iFACF,EACAF,OAAC,MAAI,WAAU,eACb,UAAAE,EAAA,IAAC0C,EAAA,CACC,GAAG,IACH,UAAU,wDACV,mBAAiB,IAEjB,SAAA5C,EAAA,KAAC,OAAK,WAAU,gBACd,UAACA,OAAA,QAAK,UAAU,kBACd,UAACE,MAAA,KAAE,UAAU,mCAAoC,GAAE,gBAErD,EACAF,EAAA,KAAC,QACC,UAAU,kBACV,cAAY,OAEZ,UAACE,MAAA,KAAE,UAAU,mCAAoC,GAAE,iBAErD,CACF,GACF,EACAA,EAAA,IAAC0C,EAAA,CACC,GAAG,YACH,UAAU,iDACV,mBAAiB,IAEjB,SAAA5C,EAAA,KAAC,OAAK,WAAU,gBACd,UAACE,EAAA,YAAK,UAAU,kBAAkB,SAElC,iBACAA,EAAA,IAAC,QACC,UAAU,kBACV,cAAY,OACb,yBAED,CACF,IACF,CACF,EACF,GACF,GACF,EACF,CACF,IACF,QAEC,SAAO,WAAU,6DAChB,SAAAA,EAAA,IAACM,IAAO,CACV,GACF,EACF,EACF,IACF,CAEJ", "names": ["menuItems", "BUSINESS_INFO", "generateLocalBusinessSchema", "service", "generateWebsiteSchema", "generateArticleSchema", "article", "generateProductSchema", "product", "generateBreadcrumbSchema", "breadcrumbs", "crumb", "index", "getPageSEOData", "pageType", "content", "language", "baseKeywords", "seoData", "Home5MainDemoMultiPageDark", "jsxs", "Fragment", "jsx", "UnifiedSEO", "Header", "ParallaxContainer", "Hero", "Home", "Footer", "ElegantWebstorePageDark", "translate", "i18n", "useTranslation", "currentLanguage", "products", "setProducts", "useState", "filteredProducts", "setFilteredProducts", "categories", "setCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "loading", "setLoading", "error", "setError", "useEffect", "loadProducts", "loadCategories", "response", "data", "productsAPI", "err", "categoriesAPI", "handleCategoryFilter", "categorySlug", "filtered", "cat", "handleProductClick", "trackEvent", "k", "AnimatedText", "category", "Link", "e", "metadata", "ElegantPortfolioPageDark", "MetaComponent", "Portfolio", "MarqueeDark", "ElegantBlogPageDark", "t", "searchParams", "setSearchParams", "useSearchParams", "blogPosts", "setBlogPosts", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "tags", "setTags", "archiveData", "setArchiveData", "currentCategory", "currentTag", "currentSearch", "params", "blogResult", "blogAPI", "posts", "_a", "pagination", "_b", "categoriesResult", "tagsResult", "tagsAPI", "archiveResult", "archiveAPI", "getTranslation", "post", "field", "translation", "_c", "newParams", "handleTagFilter", "tagSlug", "clearFilters", "c", "Pagination", "tag", "archive", "ElegantPortfolioSinglePageDark", "useParams", "portfolioItem", "allPortfolios", "elm", "RelatedProjects", "ElegantWebstoreSinglePageDark", "id", "navigate", "useNavigate", "setProduct", "loadProduct", "handleDemoClick", "handlePurchaseClick", "type", "formatPrice", "price", "productSchema", "breadcrumbSchema", "productImage", "ProductGallery", "ElegantBlogSinglePageDark", "blog", "setBlog", "fetchBlogPost", "result", "highlightCodeBlocks", "__vitePreload", "articleSchema", "_d", "Comments", "Form", "_e", "_f", "Widget1", "PrivacyPolicyPage", "TermsConditionsPage", "MainPageNotFound"], "ignoreList": [], "sources": ["../../src/data/menu.js", "../../src/utils/seoHelpers.js", "../../src/pages/home/<USER>", "../../src/pages/webstore/page.jsx", "../../src/pages/portfolio/page.jsx", "../../src/pages/blogs/page.jsx", "../../src/pages/portfolio-single/page.jsx", "../../src/pages/webstore-single/page.jsx", "../../src/pages/blog-single/page.jsx", "../../src/pages/privacy-policy/page.jsx", "../../src/pages/terms-conditions/page.jsx", "../../src/pages/otherPages/page.jsx"], "sourcesContent": ["export const menuItems = [\n  { href: \"/\", text: \"Home\" },\n  { href: \"/about\", text: \"About\" },\n  { href: \"/webstore\", text: \"Webstore\" },\n  { href: \"/services\", text: \"Services\" },\n  // { href: \"/portfolio\", text: \"Portfolio\" },\n  { href: \"/blog\", text: \"Blog\" },\n  { href: \"/contact\", text: \"Contact\" },\n];\n", "// client/src/utils/seoHelpers.js\n\n/**\n * SEO Helper functions for generating page-specific metadata and schema\n * Following 2025 SEO best practices\n */\n\n// Business information constants\nexport const BUSINESS_INFO = {\n  name: \"DevSkills\",\n  fullName: \"DevSkills OÜ\",\n  alternateName: \"DevSkills Development Studio\",\n  description:\n    \"Professional software development services and custom solutions\",\n  url: \"https://devskills.ee\",\n  address: {\n    streetAddress: \"Tornimäe tn 7\",\n    addressLocality: \"Tallinn\",\n    postalCode: \"10145\",\n    addressCountry: \"EE\",\n  },\n  geo: {\n    latitude: 59.437,\n    longitude: 24.7536,\n  },\n  contactPoint: {\n    telephone: \"+372 5628 2038\",\n    contactType: \"customer service\",\n    availableLanguage: [\"English\", \"Estonian\", \"Finnish\", \"German\", \"Swedish\"],\n  },\n  openingHours: {\n    dayOfWeek: [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\"],\n    opens: \"08:00\",\n    closes: \"17:00\",\n  },\n  services: [\n    \"Custom Software Development\",\n    \"Web Development\",\n    \"AI Solutions\",\n    \"White Label Software\",\n    \"Blockchain Development\",\n    \"Mobile Applications Development\",\n    \"Backend Development\",\n    \"Business Management Systems\",\n  ],\n  socialMedia: [\n    \"https://www.facebook.com/devskillsee\",\n    \"https://www.linkedin.com/company/devskills-development-studio\",\n    \"https://twitter.com/DevSkillsEE\",\n  ],\n};\n\n/**\n * Generate local business schema markup\n */\nexport const generateLocalBusinessSchema = () => ({\n  \"@type\": \"LocalBusiness\",\n  \"@id\": `${BUSINESS_INFO.url}/#organization`,\n  name: BUSINESS_INFO.fullName,\n  alternateName: BUSINESS_INFO.alternateName,\n  url: BUSINESS_INFO.url,\n  description: BUSINESS_INFO.description,\n  address: {\n    \"@type\": \"PostalAddress\",\n    ...BUSINESS_INFO.address,\n  },\n  geo: {\n    \"@type\": \"GeoCoordinates\",\n    ...BUSINESS_INFO.geo,\n  },\n  contactPoint: {\n    \"@type\": \"ContactPoint\",\n    ...BUSINESS_INFO.contactPoint,\n  },\n  openingHoursSpecification: {\n    \"@type\": \"OpeningHoursSpecification\",\n    ...BUSINESS_INFO.openingHours,\n  },\n  serviceArea: {\n    \"@type\": \"Country\",\n    name: \"Estonia\",\n  },\n  hasOfferCatalog: {\n    \"@type\": \"OfferCatalog\",\n    name: \"Software Development Services\",\n    itemListElement: BUSINESS_INFO.services.map((service) => ({\n      \"@type\": \"Offer\",\n      itemOffered: {\n        \"@type\": \"Service\",\n        name: service,\n      },\n    })),\n  },\n  logo: {\n    \"@type\": \"ImageObject\",\n    url: `${BUSINESS_INFO.url}/logo.png`,\n    width: \"180\",\n    height: \"60\",\n  },\n  sameAs: BUSINESS_INFO.socialMedia,\n});\n\n/**\n * Generate website schema markup\n */\nexport const generateWebsiteSchema = () => ({\n  \"@type\": \"WebSite\",\n  \"@id\": `${BUSINESS_INFO.url}/#website`,\n  name: BUSINESS_INFO.name,\n  alternateName: BUSINESS_INFO.alternateName,\n  url: BUSINESS_INFO.url,\n  description: BUSINESS_INFO.description,\n  publisher: {\n    \"@id\": `${BUSINESS_INFO.url}/#organization`,\n  },\n  potentialAction: {\n    \"@type\": \"SearchAction\",\n    target: `${BUSINESS_INFO.url}/search?q={search_term_string}`,\n    \"query-input\": \"required name=search_term_string\",\n  },\n  inLanguage: [\"en\", \"et\", \"fi\", \"de\", \"sv\"],\n});\n\n/**\n * Generate article schema for blog posts\n */\nexport const generateArticleSchema = (article) => ({\n  \"@type\": \"Article\",\n  headline: article.title,\n  description: article.excerpt || article.description,\n  image: article.featuredImage || `${BUSINESS_INFO.url}/home.jpg`,\n  author: {\n    \"@type\": \"Person\",\n    name: article.author || BUSINESS_INFO.name,\n  },\n  publisher: {\n    \"@type\": \"Organization\",\n    name: BUSINESS_INFO.name,\n    logo: {\n      \"@type\": \"ImageObject\",\n      url: `${BUSINESS_INFO.url}/logo.png`,\n    },\n  },\n  datePublished: article.publishedAt,\n  dateModified: article.modifiedAt || article.publishedAt,\n  mainEntityOfPage: {\n    \"@type\": \"WebPage\",\n    \"@id\": article.url,\n  },\n});\n\n/**\n * Generate product schema for webstore items\n */\nexport const generateProductSchema = (product) => ({\n  \"@type\": \"SoftwareApplication\",\n  name: product.title,\n  description: product.description,\n  applicationCategory: \"BusinessApplication\",\n  operatingSystem: \"Web\",\n  publisher: {\n    \"@id\": `${BUSINESS_INFO.url}/#organization`,\n  },\n  offers: {\n    \"@type\": \"Offer\",\n    price: product.price || \"Contact for pricing\",\n    priceCurrency: \"EUR\",\n    availability: \"https://schema.org/InStock\",\n  },\n  aggregateRating: product.rating\n    ? {\n        \"@type\": \"AggregateRating\",\n        ratingValue: product.rating.value,\n        ratingCount: product.rating.count,\n      }\n    : undefined,\n});\n\n/**\n * Generate breadcrumb schema\n */\nexport const generateBreadcrumbSchema = (breadcrumbs) => ({\n  \"@type\": \"BreadcrumbList\",\n  itemListElement: breadcrumbs.map((crumb, index) => ({\n    \"@type\": \"ListItem\",\n    position: index + 1,\n    item: {\n      \"@id\": crumb.url,\n      name: crumb.name,\n    },\n  })),\n});\n\n/**\n * Get page-specific SEO data based on page type and content\n */\nexport const getPageSEOData = (pageType, content = {}, language = \"en\") => {\n  const baseKeywords = [\n    \"software development\",\n    \"custom software\",\n    \"web development\",\n    \"AI solutions\",\n    \"estonia\",\n    \"tallinn\",\n  ];\n\n  const seoData = {\n    homepage: {\n      title: \"Professional Software Development Services\",\n      description:\n        \"DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.\",\n      keywords: [...baseKeywords, \"white label software\", \"business comanager\"],\n      schema: [generateLocalBusinessSchema(), generateWebsiteSchema()],\n    },\n    about: {\n      title: \"About DevSkills - Professional Development Team\",\n      description:\n        \"Learn about DevSkills, our mission, values, and the professional team behind our innovative software development services and custom solutions.\",\n      keywords: [...baseKeywords, \"about\", \"team\", \"company\"],\n      schema: [generateLocalBusinessSchema()],\n    },\n    services: {\n      title: \"Software Development Services - Custom Solutions\",\n      description:\n        \"Comprehensive software development services including custom software, web development, AI solutions, blockchain development, and mobile applications.\",\n      keywords: [\n        ...baseKeywords,\n        \"services\",\n        \"custom development\",\n        \"blockchain\",\n        \"mobile apps\",\n      ],\n      schema: [generateLocalBusinessSchema()],\n    },\n    webstore: {\n      title: \"White Label Software Solutions - DevSkills Webstore\",\n      description:\n        \"Explore our white-label software solutions including Business Comanager and other professional business management tools.\",\n      keywords: [\n        ...baseKeywords,\n        \"white label\",\n        \"business software\",\n        \"webstore\",\n      ],\n      schema: [generateLocalBusinessSchema()],\n    },\n    blog: {\n      title: \"Software Development Blog - DevSkills Insights\",\n      description:\n        \"Latest insights, tutorials, and news about software development, AI, web technologies, and business solutions from DevSkills experts.\",\n      keywords: [\n        ...baseKeywords,\n        \"blog\",\n        \"tutorials\",\n        \"insights\",\n        \"technology news\",\n      ],\n      schema: [generateLocalBusinessSchema()],\n    },\n    contact: {\n      title: \"Contact DevSkills - Get Your Custom Software Quote\",\n      description:\n        \"Contact DevSkills for professional software development services. Get a quote for your custom software, web development, or AI solution project.\",\n      keywords: [...baseKeywords, \"contact\", \"quote\", \"consultation\"],\n      schema: [generateLocalBusinessSchema()],\n    },\n  };\n\n  // Handle dynamic content\n  if (content.title && content.description) {\n    return {\n      title: content.title,\n      description: content.description,\n      keywords: content.keywords || baseKeywords,\n      schema: content.schema || [generateLocalBusinessSchema()],\n    };\n  }\n\n  return seoData[pageType] || seoData.homepage;\n};\n\n/**\n * Generate language-specific keywords\n */\nexport const getLanguageSpecificKeywords = (baseKeywords, language) => {\n  const languageKeywords = {\n    et: [\n      \"tarkvara arendus\",\n      \"kohandatud tarkvara\",\n      \"veebiarendus\",\n      \"AI lahendused\",\n      \"eesti\",\n    ],\n    fi: [\n      \"ohjelmistokehitys\",\n      \"mukautettu ohjelmisto\",\n      \"web-kehitys\",\n      \"AI-ratkaisut\",\n      \"suomi\",\n    ],\n    de: [\n      \"softwareentwicklung\",\n      \"maßgeschneiderte software\",\n      \"webentwicklung\",\n      \"AI-lösungen\",\n      \"deutschland\",\n    ],\n    sv: [\n      \"mjukvaruutveckling\",\n      \"anpassad mjukvara\",\n      \"webbutveckling\",\n      \"AI-lösningar\",\n      \"sverige\",\n    ],\n  };\n\n  return [...baseKeywords, ...(languageKeywords[language] || [])];\n};\n\nexport default {\n  BUSINESS_INFO,\n  generateLocalBusinessSchema,\n  generateWebsiteSchema,\n  generateArticleSchema,\n  generateProductSchema,\n  generateBreadcrumbSchema,\n  getPageSEOData,\n  getLanguageSpecificKeywords,\n};\n", "import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport Home from \"@/components/home\";\nimport Hero from \"@/components/home/<USER>\";\nimport { menuItems } from \"@/data/menu\";\nimport ParallaxContainer from \"@/components/common/ParallaxContainer\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\n// JSON-LD structured data for the homepage\nconst homeSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"LocalBusiness\",\n  \"@id\": \"https://devskills.ee/#organization\",\n  name: \"DevSkills OÜ\",\n  alternateName: \"DevSkills Development Studio\",\n  url: \"https://devskills.ee\",\n  logo: {\n    \"@type\": \"ImageObject\",\n    url: \"https://devskills.ee/logo.png\",\n    width: \"180\",\n    height: \"60\",\n  },\n  description:\n    \"DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.\",\n  address: {\n    \"@type\": \"PostalAddress\",\n    streetAddress: \"Tornimäe tn 7\",\n    addressLocality: \"Tallinn\",\n    postalCode: \"10145\",\n    addressCountry: \"EE\",\n  },\n  geo: {\n    \"@type\": \"GeoCoordinates\",\n    latitude: 59.437,\n    longitude: 24.7536,\n  },\n  contactPoint: {\n    \"@type\": \"ContactPoint\",\n    telephone: \"+372 5628 2038\",\n    contactType: \"customer service\",\n    availableLanguage: [\"English\", \"Estonian\", \"Finnish\", \"German\", \"Swedish\"],\n  },\n  openingHoursSpecification: {\n    \"@type\": \"OpeningHoursSpecification\",\n    dayOfWeek: [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\"],\n    opens: \"08:00\",\n    closes: \"17:00\",\n  },\n  serviceArea: {\n    \"@type\": \"Country\",\n    name: \"Estonia\",\n  },\n  hasOfferCatalog: {\n    \"@type\": \"OfferCatalog\",\n    name: \"Software Development Services\",\n    itemListElement: [\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"Custom Software Development\",\n        },\n      },\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"Web Development\",\n        },\n      },\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"AI Solutions\",\n        },\n      },\n      {\n        \"@type\": \"Offer\",\n        itemOffered: {\n          \"@type\": \"Service\",\n          name: \"White Label Software\",\n        },\n      },\n    ],\n  },\n  sameAs: [\n    \"https://www.facebook.com/devskillsee\",\n    \"https://www.linkedin.com/company/devskills-ee\",\n    \"https://twitter.com/DevSkillsEE\",\n  ],\n};\n\n// Additional WebSite schema\nconst websiteSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"WebSite\",\n  \"@id\": \"https://devskills.ee/#website\",\n  name: \"DevSkills\",\n  alternateName: \"DevSkills Development Studio\",\n  url: \"https://devskills.ee\",\n  description:\n    \"Professional software development services and custom solutions\",\n  publisher: {\n    \"@id\": \"https://devskills.ee/#organization\",\n  },\n  potentialAction: {\n    \"@type\": \"SearchAction\",\n    target: \"https://devskills.ee/search?q={search_term_string}\",\n    \"query-input\": \"required name=search_term_string\",\n  },\n  inLanguage: [\"en\", \"et\", \"fi\", \"de\", \"sv\"],\n};\nexport default function Home5MainDemoMultiPageDark() {\n  const seoData = getPageSEOData(\"homepage\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={seoData.title}\n        description={seoData.description}\n        slug=\"\"\n        type=\"website\"\n        image=\"https://devskills.ee/home.jpg\"\n        imageAlt=\"DevSkills Development Studio - Professional Software Development\"\n        schema={seoData.schema}\n        keywords={seoData.keywords}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <ParallaxContainer\n                className=\"home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <Hero />\n              </ParallaxContainer>\n\n              <Home dark />\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React, { useState, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link } from \"react-router-dom\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\nimport Header from \"@/components/headers/Header\";\nimport AnimatedText from \"@/components/common/AnimatedText\";\nimport Footer from \"@/components/footers/Footer\";\nimport { trackEvent } from \"@/utils/analytics\";\nimport { menuItems } from \"@/data/menu\";\nimport { productsAPI, categoriesAPI } from \"@/utils/api\";\n\nexport default function ElegantWebstorePageDark() {\n  const { t: translate, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  const [products, setProducts] = useState([]);\n  const [filteredProducts, setFilteredProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadProducts();\n    loadCategories();\n  }, [currentLanguage]);\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await productsAPI.getProducts({\n        language: currentLanguage,\n        status: \"published\",\n      });\n\n      if (response.ok && data.success) {\n        console.log(\"Loaded products:\", data.products); // Debug log\n        setProducts(data.products);\n        setFilteredProducts(data.products);\n      } else {\n        setError(\"Failed to load products\");\n      }\n    } catch (err) {\n      console.error(\"Error loading products:\", err);\n      setError(\"Error loading products\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCategories = async () => {\n    try {\n      const { response, data } = await categoriesAPI.getCategories();\n\n      if (response.ok && data.success) {\n        setCategories(data.data || data.categories);\n      }\n    } catch (err) {\n      console.error(\"Error loading categories:\", err);\n    }\n  };\n\n  const handleCategoryFilter = (categorySlug) => {\n    setSelectedCategory(categorySlug);\n\n    if (categorySlug === \"all\") {\n      setFilteredProducts(products);\n    } else {\n      const filtered = products.filter((product) =>\n        product.categories.some((cat) => cat.category.slug === categorySlug)\n      );\n      setFilteredProducts(filtered);\n    }\n  };\n\n  const handleProductClick = (product) => {\n    trackEvent(\"product_view\", {\n      product_id: product.id,\n      product_title: product.title,\n      language: currentLanguage,\n      source: \"webstore_listing\",\n    });\n  };\n\n  const formatPrice = (price) => {\n    if (!price) return null;\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"EUR\",\n    }).format(price);\n  };\n\n  const seoData = getPageSEOData(\"webstore\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={translate(\"webstore.meta.title\") || seoData.title}\n        description={\n          translate(\"webstore.meta.description\") || seoData.description\n        }\n        slug=\"webstore\"\n        type=\"website\"\n        image=\"https://devskills.ee/webstore.jpg\"\n        schema={seoData.schema}\n        keywords={\n          translate(\"webstore.meta.keywords\")\n            ? translate(\"webstore.meta.keywords\")\n                .split(\",\")\n                .map((k) => k.trim())\n            : seoData.keywords\n        }\n      />\n      <style>\n        {`\n          .btn-mod:focus,\n          .btn-mod:active {\n            outline: none !important;\n            box-shadow: none !important;\n          }\n          .btn-mod.btn-w:focus,\n          .btn-mod.btn-w:active {\n            background: #fff !important;\n            color: var(--color-dark-1) !important;\n            border-color: #fff !important;\n          }\n        `}\n      </style>\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/3.jpg)\",\n                }}\n              >\n                <div className=\"container position-relative pt-30 pt-sm-50\">\n                  <div className=\"text-center\">\n                    <div className=\"row\">\n                      <div className=\"col-md-8 offset-md-2\">\n                        <h1 className=\"hs-title-1 mb-20\">\n                          <span\n                            className=\"wow charsAnimIn\"\n                            data-splitting=\"chars\"\n                          >\n                            <AnimatedText text={translate(\"webstore.title\")} />\n                          </span>\n                        </h1>\n                        <div className=\"row\">\n                          <div className=\"col-lg-8 offset-lg-2\">\n                            <p\n                              className=\"section-descr mb-0 wow fadeIn\"\n                              data-wow-delay=\"0.2s\"\n                            >\n                              {translate(\"webstore.description\")}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  {/* Category Filter */}\n                  <div className=\"row mb-60 mb-xs-40\">\n                    <div className=\"col-md-12\">\n                      <div className=\"works-filter works-filter-elegant text-center\">\n                        <a\n                          onClick={() => handleCategoryFilter(\"all\")}\n                          className={`filter ${\n                            selectedCategory === \"all\" ? \"active\" : \"\"\n                          }`}\n                          style={{ cursor: \"pointer\" }}\n                        >\n                          {translate(\"webstore.filter.all\")}\n                        </a>\n                        {categories.map((category) => (\n                          <a\n                            key={category.id}\n                            onClick={() => handleCategoryFilter(category.slug)}\n                            className={`filter ${\n                              selectedCategory === category.slug ? \"active\" : \"\"\n                            }`}\n                            style={{ cursor: \"pointer\" }}\n                          >\n                            {category.name}\n                          </a>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Products Grid */}\n                  {loading ? (\n                    <div className=\"text-center py-5\">\n                      <div\n                        className=\"spinner-border text-primary\"\n                        role=\"status\"\n                      >\n                        <span className=\"visually-hidden\">Loading...</span>\n                      </div>\n                    </div>\n                  ) : error ? (\n                    <div\n                      className=\"alert alert-danger text-center\"\n                      role=\"alert\"\n                    >\n                      {error}\n                    </div>\n                  ) : (\n                    <div className=\"row mt-n50\">\n                      {filteredProducts.length > 0 ? (\n                        filteredProducts.map((product, index) => (\n                          <div\n                            key={product.id}\n                            className=\"post-prev col-md-6 col-lg-4 mt-50\"\n                          >\n                            <div className=\"post-prev-container d-flex flex-column h-100\">\n                              <div className=\"post-prev-img\">\n                                <Link\n                                  to={`/${currentLanguage}/webstore-single/${(\n                                    product.slug || product.id\n                                  ).replace(/^\\/+/, \"\")}`}\n                                  onClick={() => handleProductClick(product)}\n                                >\n                                  <img\n                                    src={\n                                      product.featuredImage\n                                        ? `${\n                                            import.meta.env.VITE_API_BASE_URL ||\n                                            \"http://localhost:4004\"\n                                          }/uploads/product-images/${\n                                            product.featuredImage\n                                          }`\n                                        : \"/assets/images/demo-elegant/blog/1.jpg\"\n                                    }\n                                    width={650}\n                                    height={412}\n                                    alt={\n                                      product.featuredImageAlt || product.title\n                                    }\n                                    className=\"wow scaleOutIn\"\n                                    data-wow-duration=\"1.2s\"\n                                  />\n                                </Link>\n                              </div>\n\n                              {/* Content area that can grow */}\n                              <div className=\"flex-grow-1 d-flex flex-column\">\n                                <h4 className=\"post-prev-title\">\n                                  <Link\n                                    to={`/${currentLanguage}/webstore-single/${(\n                                      product.slug || product.id\n                                    ).replace(/^\\/+/, \"\")}`}\n                                    onClick={() => handleProductClick(product)}\n                                  >\n                                    {product.title}\n                                  </Link>\n                                </h4>\n\n                                <div className=\"post-prev-text flex-grow-1\">\n                                  {product.excerpt}\n                                </div>\n\n                                {/* Pricing and button section - always at bottom */}\n                                <div className=\"mt-auto\">\n                                  {/* Pricing Information */}\n                                  <div className=\"product-pricing mb-30 text-center\">\n                                    {product.whitelabelPrice && (\n                                      <div className=\"price-row mb-10\">\n                                        <span className=\"text-gray\">\n                                          {translate(\n                                            \"webstore.whitelabel_price\"\n                                          )}\n                                          :{\" \"}\n                                        </span>\n                                        <span className=\"price-value text-white h5 d-inline\">\n                                          €{product.whitelabelPrice}\n                                        </span>\n                                      </div>\n                                    )}\n                                    {product.subscriptionPrice && (\n                                      <div className=\"price-row mb-10\">\n                                        <span className=\"text-gray\">\n                                          {translate(\n                                            \"webstore.subscription_price\"\n                                          )}\n                                          :{\" \"}\n                                        </span>\n                                        <span className=\"price-value text-white h5 d-inline\">\n                                          €{product.subscriptionPrice}/mo\n                                        </span>\n                                      </div>\n                                    )}\n                                  </div>\n\n                                  {/* Demo Button */}\n                                  <div className=\"text-center\">\n                                    {product.demoUrl && (\n                                      <a\n                                        href={product.demoUrl}\n                                        target=\"_blank\"\n                                        rel=\"noopener noreferrer\"\n                                        className=\"btn btn-mod btn-medium btn-circle btn-hover-anim btn-w\"\n                                        onClick={(e) => {\n                                          // Remove focus after click to prevent focus state styling\n                                          setTimeout(() => {\n                                            e.target.blur();\n                                          }, 100);\n\n                                          trackEvent(\"demo_click\", {\n                                            product_id: product.id,\n                                            product_title: product.title,\n                                            language: currentLanguage,\n                                            source: \"webstore_listing\",\n                                          });\n                                        }}\n                                      >\n                                        <span>\n                                          {translate(\"webstore.view_demo\")}\n                                        </span>\n                                      </a>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ))\n                      ) : (\n                        <div className=\"col-12 text-center\">\n                          <p className=\"text-muted\">\n                            {translate(\"webstore.no_products\")}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"footer-1 bg-dark-2 light-content\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nconst dark = true;\nimport { menuItems } from \"@/data/menu\";\nimport Portfolio from \"@/components/home/<USER>\";\nimport MarqueeDark from \"@/components/home/<USER>\";\n\nimport MetaComponent from \"@/components/common/MetaComponent\";\nconst metadata = {\n  title:\n    \"Elegant Portfolio Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n  description: \"Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n};\nexport default function ElegantPortfolioPageDark() {\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    PORTFOLIO\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        Explore captivating web design solutions.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section\n                className={`page-section pb-0  scrollSpysection  ${\n                  dark ? \"bg-dark-1 light-content\" : \"\"\n                } `}\n                id=\"portfolio\"\n              >\n                <Portfolio />\n              </section>\n              <div className=\"page-section overflow-hidden\">\n                <MarqueeDark />\n              </div>\n              <section className=\"page-section bg-dark-1 light-content pt-0\">\n                <div className=\"container position-relative\">\n                  {/* Decorative Waves */}\n\n                  {/* End Decorative Waves */}\n                  <div className=\"row text-center wow fadeInUp\">\n                    <div className=\"col-md-10 offset-md-1 col-lg-6 offset-lg-3\">\n                      <p className=\"section-descr mb-50 mb-sm-30\">\n                        The power of design help us to solve complex problems\n                        and cultivate business solutions.\n                      </p>\n                      <div className=\"local-scroll\">\n                        <Link\n                          to={`/elegant-contact`}\n                          className=\"btn btn-mod btn-large btn-w btn-circle btn-hover-anim\"\n                        >\n                          <span>Contact us</span>\n                        </Link>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/blogs/page.jsx\n\nimport React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport { Link, useSearchParams } from \"react-router-dom\";\nimport { useState, useEffect } from \"react\";\nimport { menuItems } from \"@/data/menu\";\nimport Pagination from \"@/components/common/Pagination\";\nimport { blogAPI, categoriesAPI, tagsAPI, archiveAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\nexport default function ElegantBlogPageDark() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [blogPosts, setBlogPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [archiveData, setArchiveData] = useState([]);\n\n  // Get current filters from URL\n  const currentCategory = searchParams.get(\"category\");\n  const currentTag = searchParams.get(\"tag\");\n  const currentSearch = searchParams.get(\"search\");\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch blog posts with filters\n        const params = {\n          language: currentLanguage,\n          page: currentPage,\n          limit: 9,\n        };\n\n        if (currentCategory) params.category = currentCategory;\n        if (currentTag) params.tag = currentTag;\n        if (currentSearch) params.search = currentSearch;\n\n        const blogResult = await blogAPI.getBlogPosts(params);\n\n        if (blogResult.response.ok && blogResult.data) {\n          // Extract the posts array from the nested response structure\n          const posts =\n            blogResult.data.data?.data || blogResult.data.data || [];\n          const pagination =\n            blogResult.data.data?.pagination || blogResult.data.pagination;\n          console.log(\"Blog listing API response:\", blogResult.data);\n          console.log(\"Posts array:\", posts);\n          console.log(\"Pagination:\", pagination);\n          setBlogPosts(Array.isArray(posts) ? posts : []);\n          setTotalPages(pagination?.totalPages || 1);\n        } else {\n          console.error(\n            \"Failed to fetch blog posts:\",\n            blogResult.response.status\n          );\n          setBlogPosts([]);\n        }\n\n        // Fetch categories\n        try {\n          const categoriesResult = await categoriesAPI.getCategories();\n          if (categoriesResult.response.ok && categoriesResult.data) {\n            setCategories(categoriesResult.data.data || []);\n          }\n        } catch (error) {\n          console.error(\"Error fetching categories:\", error);\n        }\n\n        // Fetch tags\n        try {\n          const tagsResult = await tagsAPI.getTags();\n          if (tagsResult.response.ok && tagsResult.data) {\n            setTags(tagsResult.data.data || []);\n          }\n        } catch (error) {\n          console.error(\"Error fetching tags:\", error);\n        }\n\n        // Fetch archive data\n        try {\n          const archiveResult = await archiveAPI.getArchive();\n          if (archiveResult.response.ok && archiveResult.data) {\n            setArchiveData(archiveResult.data.archive || []);\n          }\n        } catch (error) {\n          console.error(\"Error fetching archive:\", error);\n        }\n      } catch (error) {\n        console.error(\"Error fetching data:\", error);\n        setBlogPosts([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [\n    currentLanguage,\n    currentPage,\n    currentCategory,\n    currentTag,\n    currentSearch,\n  ]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    const translation = post.translations?.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations?.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Filter handlers\n  const handleCategoryFilter = (categorySlug) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (categorySlug) {\n      newParams.set(\"category\", categorySlug);\n    } else {\n      newParams.delete(\"category\");\n    }\n    newParams.delete(\"page\"); // Reset to first page when filtering\n    setSearchParams(newParams);\n    setCurrentPage(1);\n  };\n\n  const handleTagFilter = (tagSlug) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (tagSlug) {\n      newParams.set(\"tag\", tagSlug);\n    } else {\n      newParams.delete(\"tag\");\n    }\n    newParams.delete(\"page\"); // Reset to first page when filtering\n    setSearchParams(newParams);\n    setCurrentPage(1);\n  };\n\n  const clearFilters = () => {\n    setSearchParams({});\n    setCurrentPage(1);\n  };\n\n  const seoData = getPageSEOData(\"blog\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={t(\"blog.page.title\") || seoData.title}\n        description={t(\"blog.page.description\") || seoData.description}\n        slug=\"blog\"\n        type=\"website\"\n        image=\"https://devskills.ee/blog.jpg\"\n        schema={seoData.schema}\n        keywords={\n          t(\"blog.page.keywords\", { returnObjects: true }) || seoData.keywords\n        }\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className={`page-section bg-dark-alpha-50 light-content ${\n                  currentCategory || currentTag || currentSearch\n                    ? \"blog-hero-minimal\"\n                    : \"\"\n                }`}\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"blog.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"blog.subtitle\")}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Filter Status */}\n                  {(currentCategory || currentTag || currentSearch) && (\n                    <div className=\"filter-status-minimal\">\n                      <div className=\"d-flex flex-wrap justify-content-center align-items-center gap-3\">\n                        <span className=\"text-white-opacity\">Filtered by:</span>\n                        {currentCategory && (\n                          <span className=\"badge bg-primary\">\n                            Category:{\" \"}\n                            {\n                              categories.find((c) => c.slug === currentCategory)\n                                ?.name\n                            }\n                          </span>\n                        )}\n                        {currentTag && (\n                          <span className=\"badge bg-secondary\">\n                            Tag: {tags.find((t) => t.slug === currentTag)?.name}\n                          </span>\n                        )}\n                        {currentSearch && (\n                          <span className=\"badge bg-info\">\n                            Search: \"{currentSearch}\"\n                          </span>\n                        )}\n                        <button\n                          onClick={clearFilters}\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            Clear Filters\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            Clear Filters\n                          </span>\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </section>\n              <>\n                <section\n                  className=\"page-section bg-dark-1 light-content\"\n                  id=\"blog\"\n                >\n                  <div className=\"container\">\n                    {/* Blog Posts Grid */}\n                    <div\n                      className=\"row mt-n50 mb-50 wow fadeInUp\"\n                      data-wow-offset={0}\n                    >\n                      {/* Loading State */}\n                      {loading && (\n                        <div className=\"col-12 text-center\">\n                          <div className=\"text-gray\">{t(\"blog.loading\")}</div>\n                        </div>\n                      )}\n\n                      {/* Empty State */}\n                      {!loading && blogPosts.length === 0 && (\n                        <div className=\"col-12 text-center\">\n                          <div className=\"text-gray\">{t(\"blog.empty\")}</div>\n                        </div>\n                      )}\n\n                      {/* Post Items */}\n                      {!loading &&\n                        Array.isArray(blogPosts) &&\n                        blogPosts.map((post) => (\n                          <div\n                            key={post.id}\n                            className=\"post-prev col-md-6 col-lg-4 mt-50\"\n                          >\n                            <div className=\"post-prev-container\">\n                              <div className=\"post-prev-img\">\n                                <Link to={`/blog-single/${post.slug}`}>\n                                  <img\n                                    src={\n                                      post.featuredImage ||\n                                      \"/assets/images/demo-elegant/blog/1.jpg\"\n                                    }\n                                    width={607}\n                                    height={358}\n                                    alt={getTranslation(post, \"title\")}\n                                  />\n                                </Link>\n                              </div>\n                              <h3 className=\"post-prev-title\">\n                                <Link to={`/blog-single/${post.slug}`}>\n                                  {getTranslation(post, \"title\")}\n                                </Link>\n                              </h3>\n                              <div className=\"post-prev-text\">\n                                {getTranslation(post, \"excerpt\")}\n                              </div>\n                              <div className=\"post-prev-info clearfix\">\n                                <div className=\"float-start\">\n                                  <a href=\"#\" className=\"icon-author\">\n                                    <i className=\"mi-user size-14 align-middle\" />\n                                  </a>\n                                  <a href=\"#\">\n                                    {post.author?.name || \"DevSkills Team\"}\n                                  </a>\n                                </div>\n                                <div className=\"float-end\">\n                                  <i className=\"mi-calendar size-14 align-middle\" />\n                                  <a href=\"#\">\n                                    {new Date(\n                                      post.publishedAt || post.createdAt\n                                    ).toLocaleDateString()}\n                                  </a>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      {/* End Post Item */}\n\n                      {/* End Post Item */}\n                    </div>\n                    {/* End Blog Posts Grid */}\n                    {/* Pagination */}\n                    <Pagination\n                      currentPage={currentPage}\n                      totalPages={totalPages}\n                      onPageChange={setCurrentPage}\n                    />\n                    {/* End Pagination */}\n                  </div>\n                </section>\n                {/* End Blog Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Section */}\n                <section className=\"page-section bg-dark-1 light-content\">\n                  <div className=\"container relative\">\n                    <div className=\"row mt-n60\">\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">\n                            {t(\"blog.categories\")}\n                          </h3>\n                          <div className=\"widget-body\">\n                            <ul className=\"clearlist widget-menu\">\n                              {categories.map((category) => (\n                                <li key={category.id}>\n                                  <a\n                                    href=\"#\"\n                                    title=\"\"\n                                    onClick={(e) => {\n                                      e.preventDefault();\n                                      handleCategoryFilter(category.slug);\n                                    }}\n                                    className={\n                                      currentCategory === category.slug\n                                        ? \"active\"\n                                        : \"\"\n                                    }\n                                  >\n                                    {category.name}\n                                  </a>\n                                  <small>\n                                    {\" \"}\n                                    - {category._count?.blogPosts || 0}{\" \"}\n                                  </small>\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">{t(\"blog.tags\")}</h3>\n                          <div className=\"widget-body\">\n                            <div className=\"tags\">\n                              {tags.map((tag) => (\n                                <a\n                                  href=\"#\"\n                                  key={tag.id}\n                                  onClick={(e) => {\n                                    e.preventDefault();\n                                    handleTagFilter(tag.slug);\n                                  }}\n                                  className={\n                                    currentTag === tag.slug ? \"active\" : \"\"\n                                  }\n                                >\n                                  {tag.name}\n                                </a>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">{t(\"blog.archive\")}</h3>\n                          <div className=\"widget-body\">\n                            <ul className=\"clearlist widget-menu\">\n                              {archiveData.map((archive, index) => (\n                                <li key={index}>\n                                  <a href=\"#\" title=\"\">\n                                    {archive.monthName} {archive.year}\n                                  </a>\n                                  <small> - {archive.count} </small>\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                      <div className=\"col-sm-6 col-lg-3 mt-60\">\n                        {/* Widget */}\n                        <div className=\"widget mb-0\">\n                          <h3 className=\"widget-title\">\n                            {t(\"blog.about_widget.title\")}\n                          </h3>\n                          <div className=\"widget-body\">\n                            <div className=\"widget-text clearfix\">\n                              <img\n                                src=\"/assets/img/power-128.png\"\n                                alt=\"DevSkills Logo\"\n                                height={40}\n                                width={40}\n                                className=\"left img-left\"\n                                style={{ borderRadius: \"8px\" }}\n                              />\n                              {t(\"blog.about_widget.text\")}\n                            </div>\n                          </div>\n                        </div>\n                        {/* End Widget */}\n                      </div>\n                    </div>\n                  </div>\n                </section>\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\nimport React from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport { Link } from \"react-router-dom\";\nimport RelatedProjects from \"@/components/portfolio/RelatedProjects\";\nimport { allPortfolios } from \"@/data/portfolio\";\nimport MetaComponent from \"@/components/common/MetaComponent\";\nconst metadata = {\n  title:\n    \"Elegant Portfolio Single Dark || Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n  description: \"Resonance &mdash; One & Multi Page Reactjs Creative Template\",\n};\nexport default function ElegantPortfolioSinglePageDark() {\n  let params = useParams();\n  const portfolioItem =\n    allPortfolios.filter((elm) => elm.id == params.id)[0] || allPortfolios[0];\n  return (\n    <>\n      <MetaComponent meta={metadata} />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/section-bg-1.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {portfolioItem.title}\n                  </h1>\n\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        Branding, UI/UX Design, No-code Development\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <>\n                {/* Section */}\n                <section className=\"page-section bg-dark-1 light-content\">\n                  <div className=\"container relative\">\n                    <div className=\"row mb-80 mb-sm-40\">\n                      {/* Project Details */}\n                      <div className=\"col-md-6 mb-sm-40\">\n                        <h2 className=\"section-title-small mb-20\">\n                          Project Details\n                        </h2>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Date:</b>\n                          </div>\n                          <div className=\"col-sm-8\">May 1th, 2023</div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Client:</b>\n                          </div>\n                          <div className=\"col-sm-8\">Envato Users</div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                        <div className=\"row text-gray\">\n                          <div className=\"col-sm-4\">\n                            <b>Services:</b>\n                          </div>\n                          <div className=\"col-sm-8\">\n                            Branding, UI/UX Design, Front-end Development,\n                            Back-end Development\n                          </div>\n                        </div>\n                        <hr className=\"mb-20\" />\n                      </div>\n                      {/* End Project Details */}\n                      {/* Project Description */}\n                      <div className=\"col-md-6\">\n                        <h2 className=\"section-title-small mb-20\">\n                          Description\n                        </h2>\n                        <hr className=\"mb-20\" />\n                        <p className=\"text-gray mb-0\">\n                          Lorem ipsum dolor sit amet conseur adipisci inerene\n                          maximus ligula sempe metuse pelente mattis. Maecenas\n                          volutpat, diam eni sagittis quam porta quam. Sed id\n                          dolor consectetur fermentum volutpat accumsan purus\n                          iaculis libero. Donec vel ultricies purus iaculis\n                          libero. Etiam sit amet fringilla lacus susantebe sit\n                          ullamcorper pulvinar neque porttitor. Integere lectus.\n                          Praesent sede nisi eleifend fermum orci amet, iaculis\n                          libero. Donec vel ultricies purus quam.\n                        </p>\n                      </div>\n                      {/* End Project Description */}\n                    </div>\n                    <div className=\"row mb-n30\">\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/1-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/6-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/8-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                      {/* Photo Item */}\n                      <div className=\"col-md-6 mb-30 wow fadeInUp\">\n                        <img\n                          src=\"/assets/images/demo-elegant/portfolio/3-large.jpg\"\n                          alt=\"Image Description\"\n                          width={970}\n                          height={1136}\n                        />\n                      </div>\n                      {/* End Photo Item */}\n                    </div>\n                  </div>\n                </section>\n                {/* End Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n              </>\n              <section className=\"page-section bg-dark-1 light-content\">\n                <RelatedProjects />\n              </section>\n              <>\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Work Navigation */}\n                <div className=\"work-navigation bg-dark-1 light-content clearfix z-index-1 position-relative\">\n                  <Link to={`/main-portfolio-single-1/1`} className=\"work-prev\">\n                    <span>\n                      <i className=\"mi-arrow-left size-24 align-middle\" />{\" \"}\n                      Previous\n                    </span>\n                  </Link>\n                  <a href=\"#\" className=\"work-all\">\n                    <span>\n                      <i className=\"mi-close size-24 align-middle\" /> All works\n                    </span>\n                  </a>\n                  <Link to={`/main-portfolio-single-3/1`} className=\"work-next\">\n                    <span>\n                      Next <i className=\"mi-arrow-right size-24 align-middle\" />\n                    </span>\n                  </Link>\n                </div>\n                {/* End Work Navigation */}\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React, { useState, useEffect } from \"react\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport {\n  generateProductSchema,\n  generateBreadcrumbSchema,\n} from \"@/utils/seoHelpers\";\n\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport { trackEvent } from \"@/utils/analytics\";\nimport { menuItems } from \"@/data/menu\";\nimport ProductGallery from \"@/components/ProductGallery\";\n\nexport default function ElegantWebstoreSinglePageDark() {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { t: translate, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  const [product, setProduct] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    loadProduct();\n  }, [id, currentLanguage]);\n\n  const loadProduct = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(\n        `/api/products/${id}?language=${currentLanguage}`\n      );\n      const data = await response.json();\n\n      if (data.success) {\n        setProduct(data.product);\n\n        // Track product view\n        trackEvent(\"product_detail_view\", {\n          product_id: data.product.id,\n          product_title: data.product.title,\n          language: currentLanguage,\n          source: \"direct_link\",\n        });\n      } else {\n        setError(\"Product not found\");\n      }\n    } catch (err) {\n      console.error(\"Error loading product:\", err);\n      setError(\"Error loading product\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDemoClick = () => {\n    trackEvent(\"demo_click\", {\n      product_id: product.id,\n      product_title: product.title,\n      language: currentLanguage,\n      source: \"product_detail\",\n    });\n  };\n\n  const handlePurchaseClick = (type) => {\n    trackEvent(\"purchase_intent\", {\n      product_id: product.id,\n      product_title: product.title,\n      purchase_type: type, // 'whitelabel' or 'subscription'\n      language: currentLanguage,\n      source: \"product_detail\",\n    });\n  };\n\n  const formatPrice = (price) => {\n    if (!price) return null;\n    return new Intl.NumberFormat(\"en-US\", {\n      style: \"currency\",\n      currency: \"EUR\",\n    }).format(price);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container text-center py-5\">\n                  <div className=\"spinner-border text-primary\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading...</span>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container text-center py-5\">\n                  <h1>Product Not Found</h1>\n                  <p>The product you're looking for doesn't exist.</p>\n                  <button\n                    onClick={() => navigate(`/${currentLanguage}/webstore`)}\n                    className=\"btn btn-mod btn-round\"\n                  >\n                    Back to Webstore\n                  </button>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Generate product schema\n  const productSchema = generateProductSchema({\n    title: product.title,\n    description: product.excerpt,\n    price: product.whitelabelPrice || product.subscriptionPrice,\n    featuredImage: product.featuredImage,\n    url: `https://devskills.ee/${currentLanguage}/webstore-single/${product.slug}`,\n  });\n\n  // Generate breadcrumb schema\n  const breadcrumbSchema = generateBreadcrumbSchema([\n    { name: \"Home\", url: `https://devskills.ee/${currentLanguage}` },\n    {\n      name: \"Webstore\",\n      url: `https://devskills.ee/${currentLanguage}/webstore`,\n    },\n    {\n      name: product.title,\n      url: `https://devskills.ee/${currentLanguage}/webstore-single/${product.slug}`,\n    },\n  ]);\n\n  const productImage = product.featuredImage\n    ? `${\n        import.meta.env.VITE_API_BASE_URL || \"http://localhost:4004\"\n      }/uploads/product-images/${product.featuredImage}`\n    : \"https://devskills.ee/webstore.jpg\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title={product.metaTitle || product.title}\n        description={product.metaDescription || product.excerpt}\n        slug={`webstore-single/${product.slug}`}\n        type=\"product\"\n        image={productImage}\n        imageAlt={product.featuredImageAlt || product.title}\n        schema={[productSchema, breadcrumbSchema]}\n        keywords={\n          product.metaKeywords\n            ? product.metaKeywords.split(\",\").map((k) => k.trim())\n            : [\"software\", \"business\", \"devskills\"]\n        }\n        publishedAt={product.publishedAt}\n        modifiedAt={product.updatedAt}\n      />\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              {/* Hero Section with Background */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/3.jpg)\",\n                  paddingBottom: \"40px\",\n                }}\n              >\n                <div className=\"container position-relative pt-30 pt-sm-50\">\n                  <div className=\"text-center\">\n                    <div className=\"row\">\n                      <div className=\"col-md-8 offset-md-2\">\n                        <h1 className=\"hs-title-1 mb-20\">\n                          <span\n                            className=\"wow charsAnimIn\"\n                            data-splitting=\"chars\"\n                          >\n                            {product.title}\n                          </span>\n                        </h1>\n                        <div className=\"row\">\n                          <div className=\"col-lg-8 offset-lg-2\">\n                            <p\n                              className=\"section-descr mb-20 wow fadeIn\"\n                              data-wow-delay=\"0.2s\"\n                            >\n                              {product.excerpt}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n\n              {/* Product Content Section */}\n              <section\n                className=\"page-section bg-dark-1 light-content\"\n                style={{ paddingTop: \"40px\" }}\n              >\n                <div className=\"container position-relative\">\n                  {/* Product Content */}\n                  <div className=\"row\">\n                    <div className=\"col-lg-8\">\n                      {/* Product Gallery */}\n                      <ProductGallery\n                        images={product.images}\n                        productTitle={product.title}\n                      />\n\n                      {/* Product Content */}\n                      <div className=\"blog-item-body\">\n                        <div\n                          className=\"blog-item-content\"\n                          dangerouslySetInnerHTML={{ __html: product.content }}\n                        />\n                      </div>\n\n                      {/* Categories and Tags */}\n                      <div className=\"blog-item-footer pt-4 mt-4 border-top\">\n                        {product.categories &&\n                          product.categories.length > 0 && (\n                            <div className=\"mb-3\">\n                              <strong>Categories: </strong>\n                              {product.categories.map((cat, index) => (\n                                <span key={cat.category.id}>\n                                  <span className=\"badge bg-primary me-2\">\n                                    {cat.category.name}\n                                  </span>\n                                </span>\n                              ))}\n                            </div>\n                          )}\n\n                        {product.tags && product.tags.length > 0 && (\n                          <div>\n                            <strong>Tags: </strong>\n                            {product.tags.map((tag, index) => (\n                              <span key={tag.tag.id}>\n                                <span className=\"badge bg-secondary me-2\">\n                                  {tag.tag.name}\n                                </span>\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Sidebar */}\n                    <div className=\"col-lg-4\">\n                      <div className=\"blog-sidebar ps-lg-4\">\n                        {/* Pricing Options */}\n                        {product.whitelabelPrice && (\n                          <div className=\"widget mb-4\">\n                            <div\n                              className=\"pricing-option mb-4 p-4 rounded-3\"\n                              style={{\n                                background: \"rgba(255, 255, 255, 0.02)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.15)\",\n                                transition: \"all 0.3s ease\",\n                              }}\n                            >\n                              <h6 className=\"text-white mb-3 text-center\">\n                                Whitelabel License\n                              </h6>\n                              <div className=\"text-center mb-3\">\n                                <div\n                                  className=\"price text-primary\"\n                                  style={{\n                                    fontSize: \"2.5rem\",\n                                    fontWeight: \"700\",\n                                  }}\n                                >\n                                  {formatPrice(product.whitelabelPrice)}\n                                </div>\n                                <small className=\"text-gray\">\n                                  One-time payment\n                                </small>\n                              </div>\n                              <p className=\"text-gray small mb-4 text-center\">\n                                Get the complete source code with commercial\n                                license\n                              </p>\n                              <div className=\"text-center\">\n                                <button\n                                  className=\"btn btn-mod btn-medium btn-circle btn-hover-anim btn-color\"\n                                  onClick={() =>\n                                    handlePurchaseClick(\"whitelabel\")\n                                  }\n                                  style={{ minWidth: \"180px\" }}\n                                >\n                                  <span>Buy Whitelabel</span>\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        {product.subscriptionPrice && (\n                          <div className=\"widget mb-4\">\n                            <div\n                              className=\"pricing-option mb-4 p-4 rounded-3\"\n                              style={{\n                                background: \"rgba(255, 255, 255, 0.02)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.15)\",\n                                transition: \"all 0.3s ease\",\n                              }}\n                            >\n                              <h6 className=\"text-white mb-3 text-center\">\n                                Subscription\n                              </h6>\n                              <div className=\"text-center mb-3\">\n                                <div\n                                  className=\"price text-success\"\n                                  style={{\n                                    fontSize: \"2.5rem\",\n                                    fontWeight: \"700\",\n                                  }}\n                                >\n                                  {formatPrice(product.subscriptionPrice)}\n                                  <small\n                                    style={{\n                                      fontSize: \"1rem\",\n                                      fontWeight: \"400\",\n                                    }}\n                                  >\n                                    /mo\n                                  </small>\n                                </div>\n                                <small className=\"text-gray\">\n                                  Monthly billing\n                                </small>\n                              </div>\n                              <p className=\"text-gray small mb-4 text-center\">\n                                Use the software as a service without source\n                                code\n                              </p>\n                              <div className=\"text-center\">\n                                <button\n                                  className=\"btn btn-mod btn-medium btn-circle btn-hover-anim\"\n                                  onClick={() =>\n                                    handlePurchaseClick(\"subscription\")\n                                  }\n                                  style={{\n                                    minWidth: \"180px\",\n                                    background: \"#22c55e\",\n                                    borderColor: \"#22c55e\",\n                                    color: \"#fff\",\n                                  }}\n                                >\n                                  <span>Start Subscription</span>\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n\n                        {product.demoUrl && (\n                          <div className=\"widget mb-4\">\n                            <div className=\"text-center mt-4\">\n                              <a\n                                href={product.demoUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"opacity-1 no-hover\"\n                                onClick={handleDemoClick}\n                                style={{ cursor: \"pointer\" }}\n                              >\n                                <span\n                                  className=\"btn btn-mod btn-small btn-border-w btn-circle\"\n                                  data-btn-animate=\"y\"\n                                >\n                                  <span className=\"btn-animate-y\">\n                                    <span className=\"btn-animate-y-1\">\n                                      View Live Demo\n                                    </span>\n                                    <span\n                                      className=\"btn-animate-y-2\"\n                                      aria-hidden=\"true\"\n                                    >\n                                      View Live Demo\n                                    </span>\n                                  </span>\n                                </span>\n                              </a>\n                            </div>\n                          </div>\n                        )}\n\n                        {/* Product Info */}\n                        <div className=\"widget\">\n                          <h5 className=\"widget-title\">Product Information</h5>\n                          <ul className=\"list-unstyled\">\n                            <li className=\"mb-2\">\n                              <strong>Published:</strong>{\" \"}\n                              {new Date(\n                                product.publishedAt\n                              ).toLocaleDateString()}\n                            </li>\n                            <li className=\"mb-2\">\n                              <strong>Last Updated:</strong>{\" \"}\n                              {new Date(product.updatedAt).toLocaleDateString()}\n                            </li>\n                            <li className=\"mb-2\">\n                              <strong>Views:</strong> {product.viewCount}\n                            </li>\n                          </ul>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"footer-1 bg-dark-2 light-content\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/blog-single/page.jsx\n\nimport Footer from \"@/components/footers/Footer\";\n\nimport Header from \"@/components/headers/Header\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { menuItems } from \"@/data/menu\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { Link } from \"react-router-dom\";\nimport Comments from \"@/components/blog/Comments\";\nimport Form from \"@/components/blog/commentForm/Form\";\nimport Widget1 from \"@/components/blog/widgets/Widget1\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { generateArticleSchema } from \"@/utils/seoHelpers\";\n// highlight.js will be lazy loaded when needed\n\nexport default function ElegantBlogSinglePageDark() {\n  let params = useParams();\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const navigate = useNavigate();\n  const [blog, setBlog] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    const fetchBlogPost = async () => {\n      try {\n        setLoading(true);\n        const result = await blogAPI.getPost(params.id);\n\n        if (result.response.ok && result.data) {\n          console.log(\"Blog single API response:\", result.data);\n          setBlog(result.data.data || result.data);\n        } else {\n          console.error(\"Failed to fetch blog post:\", result.response.status);\n          setError(\"Blog post not found\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching blog post:\", error);\n        setError(\"Failed to load blog post\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (params.id) {\n      fetchBlogPost();\n    }\n  }, [params.id]);\n\n  // Highlight code blocks when blog content is loaded using Shiki\n  useEffect(() => {\n    if (blog && !loading) {\n      // Small delay to ensure DOM is updated\n      setTimeout(async () => {\n        try {\n          // Dynamically import the syntax highlighting service\n          const { highlightCodeBlocks } = await import(\n            \"@/utils/syntaxHighlighting\"\n          );\n          await highlightCodeBlocks(\".blog-content pre code\", \"github-dark\");\n        } catch (error) {\n          console.warn(\"Failed to load syntax highlighting:\", error);\n        }\n      }, 100);\n    }\n  }, [blog, loading]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    if (!post || !post.translations) return \"\";\n    const translation = post.translations.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-12 text-center\">\n                      <h1>Loading...</h1>\n                      <p>Please wait while we load the blog post.</p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // If no blog post found or error, show 404 or redirect\n  if (!blog || error) {\n    return (\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-12 text-center\">\n                      <h1>Blog Post Not Found</h1>\n                      <p>\n                        The blog post you&apos;re looking for doesn&apos;t\n                        exist.\n                      </p>\n                      <a\n                        href=\"/blog\"\n                        className=\"btn btn-mod btn-border btn-large btn-round\"\n                      >\n                        Back to Blog\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Generate article schema for the blog post\n  const articleSchema = generateArticleSchema({\n    title: getTranslation(blog, \"title\"),\n    description: getTranslation(blog, \"excerpt\"),\n    excerpt: getTranslation(blog, \"excerpt\"),\n    featuredImage: blog.featuredImage,\n    author: blog.author || \"DevSkills\",\n    publishedAt: blog.publishedAt,\n    modifiedAt: blog.updatedAt,\n    url: `https://devskills.ee/${currentLanguage}/blog-single/${blog.slug}`,\n  });\n\n  return (\n    <>\n      <UnifiedSEO\n        title={getTranslation(blog, \"title\")}\n        description={getTranslation(blog, \"excerpt\")}\n        slug={`blog-single/${blog.slug}`}\n        type=\"article\"\n        image={blog.featuredImage || \"https://devskills.ee/blog.jpg\"}\n        imageAlt={getTranslation(blog, \"title\")}\n        author={blog.author || \"DevSkills\"}\n        publishedAt={blog.publishedAt}\n        modifiedAt={blog.updatedAt}\n        schema={[articleSchema]}\n        keywords={\n          getTranslation(blog, \"keywords\") || [\n            \"blog\",\n            \"software development\",\n            \"devskills\",\n          ]\n        }\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content blog-hero-minimal\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-10 offset-lg-1\">\n                      <h1\n                        className=\"hs-title-3a mb-0 wow fadeInUpShort\"\n                        data-wow-duration=\"0.6s\"\n                      >\n                        {getTranslation(blog, \"title\")}\n                      </h1>\n                    </div>\n                  </div>\n                  {/* Author, Categories, Comments */}\n                  <div\n                    className=\"blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn\"\n                    data-wow-delay=\"0.2s\"\n                  >\n                    <div className=\"d-inline-block me-3\">\n                      <a href=\"#\">\n                        <i className=\"mi-clock size-16\" />\n                        <span className=\"visually-hidden\">Date:</span>{\" \"}\n                        {new Date(\n                          blog.publishedAt || blog.createdAt\n                        ).toLocaleDateString(\"en-US\", {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </a>\n                    </div>\n                    <div className=\"d-inline-block me-3\">\n                      <a href=\"#\">\n                        <i className=\"mi-user size-16\" />\n                        <span className=\"visually-hidden\">Author:</span>{\" \"}\n                        {blog.author?.name || \"DevSkills Team\"}\n                      </a>\n                    </div>\n                    {blog.categories && blog.categories.length > 0 && (\n                      <div className=\"d-inline-block me-3\">\n                        <i className=\"mi-folder size-16\" />\n                        <span className=\"visually-hidden\">Category:</span>\n                        <a href=\"#\">{blog.categories[0].name}</a>\n                      </div>\n                    )}\n                    <div className=\"d-inline-block me-3\">\n                      <i className=\"mi-time size-16\" />\n                      <span className=\"visually-hidden\">Read time:</span>{\" \"}\n                      {blog.readTime || 5} min\n                    </div>\n                  </div>\n                  {/* End Author, Categories, Comments */}\n\n                  {/* Navigation Buttons */}\n                  <div className=\"blog-nav-minimal\">\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      {/* Left side - Back to Blog and Previous */}\n                      <div className=\"d-flex gap-3\">\n                        <Link\n                          to={`/${currentLanguage}/blog`}\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            <i className=\"mi-arrow-left size-18 align-middle\" />\n                            &nbsp;{t(\"blog.back_to_blog\") || \"Back to Blog\"}\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            <i className=\"mi-arrow-left size-18 align-middle\" />\n                            &nbsp;{t(\"blog.back_to_blog\") || \"Back to Blog\"}\n                          </span>\n                        </Link>\n\n                        {blog?.navigation?.previous && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.previous.slug}`}\n                            className=\"link-hover-anim link-circle-1 align-middle\"\n                            data-link-animate=\"y\"\n                            title={blog.navigation.previous.title}\n                          >\n                            <span className=\"link-strong link-strong-unhovered\">\n                              <i className=\"mi-chevron-left size-18 align-middle\" />\n                              &nbsp;{t(\"blog.previous\") || \"Previous\"}\n                            </span>\n                            <span\n                              className=\"link-strong link-strong-hovered\"\n                              aria-hidden=\"true\"\n                            >\n                              <i className=\"mi-chevron-left size-18 align-middle\" />\n                              &nbsp;{t(\"blog.previous\") || \"Previous\"}\n                            </span>\n                          </Link>\n                        )}\n                      </div>\n\n                      {/* Right side - Next */}\n                      <div>\n                        {blog?.navigation?.next && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.next.slug}`}\n                            className=\"link-hover-anim link-circle-1 align-middle\"\n                            data-link-animate=\"y\"\n                            title={blog.navigation.next.title}\n                          >\n                            <span className=\"link-strong link-strong-unhovered\">\n                              {t(\"blog.next\") || \"Next\"}&nbsp;\n                              <i className=\"mi-chevron-right size-18 align-middle\" />\n                            </span>\n                            <span\n                              className=\"link-strong link-strong-hovered\"\n                              aria-hidden=\"true\"\n                            >\n                              {t(\"blog.next\") || \"Next\"}&nbsp;\n                              <i className=\"mi-chevron-right size-18 align-middle\" />\n                            </span>\n                          </Link>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container relative\">\n                  <div className=\"row\">\n                    {/* Content */}\n                    <div className=\"col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last\">\n                      {/* Post */}\n                      <div className=\"blog-item mb-80 mb-xs-40\">\n                        <div className=\"blog-item-body\">\n                          {blog.featuredImage && (\n                            <div className=\"mb-40 mb-xs-30\">\n                              <img\n                                src={blog.featuredImage}\n                                alt={getTranslation(blog, \"title\")}\n                                width={1350}\n                                height={796}\n                              />\n                            </div>\n                          )}\n\n                          {/* Blog excerpt */}\n                          <div className=\"lead mb-40\">\n                            {getTranslation(blog, \"excerpt\")}\n                          </div>\n\n                          {/* Blog content */}\n                          <div\n                            className=\"blog-content\"\n                            style={{\n                              lineHeight: \"1.8\",\n                              fontSize: \"16px\",\n                            }}\n                            dangerouslySetInnerHTML={{\n                              __html: getTranslation(blog, \"content\"),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/* End Post */}\n                      {/* Comments */}\n                      <div className=\"blog-comments-minimal mb-xs-40\">\n                        <h4 className=\"blog-page-title\">\n                          {t(\"blog.comments.title\")}{\" \"}\n                          <small className=\"number\">\n                            ({blog.comments?.length || 0})\n                          </small>\n                        </h4>\n                        <ul className=\"media-list comment-list clearlist\">\n                          <Comments comments={blog.comments || []} />\n                        </ul>\n                      </div>\n                      {/* End Comments */}\n                      {/* Add Comment */}\n                      <div className=\"blog-comments-minimal mb-xs-40\">\n                        <h4 className=\"blog-page-title\">\n                          {t(\"blog.comments.add_comment\")}\n                        </h4>\n                        {/* Form */}\n                        <Form blogSlug={blog.slug} />\n                        {/* End Form */}\n                      </div>\n                      {/* End Add Comment */}\n                      {/* Prev/Next Post */}\n                      <div className=\"clearfix mt-40\">\n                        {blog?.navigation?.previous && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.previous.slug}`}\n                            className=\"blog-item-more left\"\n                          >\n                            <i className=\"mi-chevron-left\" />\n                            &nbsp;{t(\"blog.previous\") || \"Previous\"}\n                          </Link>\n                        )}\n                        {blog?.navigation?.next && (\n                          <Link\n                            to={`/${currentLanguage}/blog-single/${blog.navigation.next.slug}`}\n                            className=\"blog-item-more right\"\n                          >\n                            {t(\"blog.next\") || \"Next\"}&nbsp;\n                            <i className=\"mi-chevron-right\" />\n                          </Link>\n                        )}\n                      </div>\n                      {/* End Prev/Next Post */}\n                    </div>\n                    {/* End Content */}\n                    {/* Sidebar */}\n                    <div className=\"col-lg-4 col-xl-3\">\n                      <Widget1 searchInputClass=\"form-control input-lg search-field round\" />\n                      {/* End Widget */}\n                    </div>\n                    {/* End Sidebar */}\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/privacy-policy/page.jsx\n\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { menuItems } from \"@/data/menu\";\n\nexport default function PrivacyPolicyPage() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title=\"Privacy Policy\"\n        description=\"DevSkills Privacy Policy - Learn how we collect, use, and protect your personal information when you use our services.\"\n        slug=\"privacy-policy\"\n        type=\"website\"\n        keywords={[\"privacy policy\", \"data protection\", \"devskills\", \"gdpr\"]}\n      />\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              {/* Page Header */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"privacy.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"privacy.lastUpdated\")}:{\" \"}\n                        {new Date().toLocaleDateString(currentLanguage, {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n\n              {/* Privacy Policy Content */}\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2\">\n                      {/* Introduction */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.intro.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.intro.text1\")}\n                        </p>\n                        <p className=\"text-gray\">{t(\"privacy.intro.text2\")}</p>\n                      </div>\n\n                      {/* Information We Collect */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.collect.title\")}\n                        </h2>\n\n                        <h3 className=\"h4 mb-20 text-white\">\n                          {t(\"privacy.collect.personal.title\")}\n                        </h3>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.collect.personal.text\")}\n                        </p>\n                        <ul className=\"text-gray mb-30\">\n                          <li>{t(\"privacy.collect.personal.item1\")}</li>\n                          <li>{t(\"privacy.collect.personal.item2\")}</li>\n                          <li>{t(\"privacy.collect.personal.item3\")}</li>\n                          <li>{t(\"privacy.collect.personal.item4\")}</li>\n                        </ul>\n\n                        <h3 className=\"h4 mb-20 text-white\">\n                          {t(\"privacy.collect.usage.title\")}\n                        </h3>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.collect.usage.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.collect.usage.item1\")}</li>\n                          <li>{t(\"privacy.collect.usage.item2\")}</li>\n                          <li>{t(\"privacy.collect.usage.item3\")}</li>\n                          <li>{t(\"privacy.collect.usage.item4\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* How We Use Information */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.use.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.use.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.use.item1\")}</li>\n                          <li>{t(\"privacy.use.item2\")}</li>\n                          <li>{t(\"privacy.use.item3\")}</li>\n                          <li>{t(\"privacy.use.item4\")}</li>\n                          <li>{t(\"privacy.use.item5\")}</li>\n                          <li>{t(\"privacy.use.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Information Sharing */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.sharing.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.sharing.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.sharing.item1\")}</li>\n                          <li>{t(\"privacy.sharing.item2\")}</li>\n                          <li>{t(\"privacy.sharing.item3\")}</li>\n                          <li>{t(\"privacy.sharing.item4\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Data Security */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.security.title\")}\n                        </h2>\n                        <p className=\"text-gray\">\n                          {t(\"privacy.security.text\")}\n                        </p>\n                      </div>\n\n                      {/* Your Rights */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.rights.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.rights.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"privacy.rights.item1\")}</li>\n                          <li>{t(\"privacy.rights.item2\")}</li>\n                          <li>{t(\"privacy.rights.item3\")}</li>\n                          <li>{t(\"privacy.rights.item4\")}</li>\n                          <li>{t(\"privacy.rights.item5\")}</li>\n                          <li>{t(\"privacy.rights.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Cookies */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.cookies.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"privacy.cookies.text\")}</p>\n                      </div>\n\n                      {/* Contact Information */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.contact.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"privacy.contact.text\")}\n                        </p>\n                        <div className=\"text-gray\">\n                          <p>\n                            <strong>DevSkills OÜ</strong>\n                          </p>\n                          <p>{t(\"privacy.contact.address\")}</p>\n                          <p>{t(\"privacy.contact.email\")}</p>\n                          <p>{t(\"privacy.contact.phone\")}</p>\n                        </div>\n                      </div>\n\n                      {/* Updates */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"privacy.updates.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"privacy.updates.text\")}</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client/src/pages/terms-conditions/page.jsx\n\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport Header from \"@/components/headers/Header\";\nimport Footer from \"@/components/footers/Footer\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { menuItems } from \"@/data/menu\";\n\nconst dark = true;\n\nexport default function TermsConditionsPage() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"en\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title=\"Terms and Conditions\"\n        description=\"DevSkills Terms and Conditions - Legal terms governing the use of our software development services and Business Comanager platform.\"\n        slug=\"terms-conditions\"\n        type=\"website\"\n        keywords={[\n          \"terms and conditions\",\n          \"legal\",\n          \"devskills\",\n          \"service agreement\",\n        ]}\n      />\n\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark dark-mode transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n\n            <main id=\"main\">\n              {/* Page Header */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"terms.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"terms.lastUpdated\")}:{\" \"}\n                        {new Date().toLocaleDateString(currentLanguage, {\n                          year: \"numeric\",\n                          month: \"long\",\n                          day: \"numeric\",\n                        })}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n\n              {/* Terms Content */}\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2\">\n                      {/* Introduction */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.agreement.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.agreement.text1\")}\n                        </p>\n                        <p className=\"text-gray\">\n                          {t(\"terms.agreement.text2\")}\n                        </p>\n                      </div>\n\n                      {/* Services */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.services.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.services.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.services.item1\")}</li>\n                          <li>{t(\"terms.services.item2\")}</li>\n                          <li>{t(\"terms.services.item3\")}</li>\n                          <li>{t(\"terms.services.item4\")}</li>\n                          <li>{t(\"terms.services.item5\")}</li>\n                          <li>{t(\"terms.services.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* User Responsibilities */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.responsibilities.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.responsibilities.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.responsibilities.item1\")}</li>\n                          <li>{t(\"terms.responsibilities.item2\")}</li>\n                          <li>{t(\"terms.responsibilities.item3\")}</li>\n                          <li>{t(\"terms.responsibilities.item4\")}</li>\n                          <li>{t(\"terms.responsibilities.item5\")}</li>\n                          <li>{t(\"terms.responsibilities.item6\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Payment Terms */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.payment.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.payment.text\")}\n                        </p>\n                        <ul className=\"text-gray mb-20\">\n                          <li>{t(\"terms.payment.item1\")}</li>\n                          <li>{t(\"terms.payment.item2\")}</li>\n                          <li>{t(\"terms.payment.item3\")}</li>\n                          <li>{t(\"terms.payment.item4\")}</li>\n                          <li>{t(\"terms.payment.item5\")}</li>\n                        </ul>\n                        <p className=\"text-gray\">{t(\"terms.payment.text2\")}</p>\n                      </div>\n\n                      {/* Intellectual Property */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.ip.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">{t(\"terms.ip.our\")}</p>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.ip.custom\")}\n                          will be specified in individual project agreements.\n                        </p>\n                        <p className=\"text-gray\">{t(\"terms.ip.client\")}</p>\n                      </div>\n\n                      {/* Confidentiality */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.confidentiality.title\")}\n                        </h2>\n                        <p className=\"text-gray\">\n                          {t(\"terms.confidentiality.text\")}\n                        </p>\n                      </div>\n\n                      {/* Limitation of Liability */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.liability.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.liability.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.liability.item1\")}</li>\n                          <li>{t(\"terms.liability.item2\")}</li>\n                          <li>{t(\"terms.liability.item3\")}</li>\n                          <li>{t(\"terms.liability.item4\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Warranties */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.warranties.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.warranties.text\")}\n                        </p>\n                        <ul className=\"text-gray\">\n                          <li>{t(\"terms.warranties.item1\")}</li>\n                          <li>{t(\"terms.warranties.item2\")}</li>\n                          <li>{t(\"terms.warranties.item3\")}</li>\n                        </ul>\n                      </div>\n\n                      {/* Termination */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.termination.title\")}\n                        </h2>\n                        <p className=\"text-gray\">\n                          {t(\"terms.termination.text\")}\n                        </p>\n                      </div>\n\n                      {/* Governing Law */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.governing.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"terms.governing.text\")}</p>\n                      </div>\n\n                      {/* Contact Information */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.contact.title\")}\n                        </h2>\n                        <p className=\"text-gray mb-20\">\n                          {t(\"terms.contact.text\")}\n                        </p>\n                        <div className=\"text-gray\">\n                          <p>\n                            <strong>DevSkills OÜ</strong>\n                          </p>\n                          <p>{t(\"terms.contact.address\")}</p>\n                          <p>{t(\"terms.contact.email\")}</p>\n                          <p>{t(\"terms.contact.phone\")}</p>\n                        </div>\n                      </div>\n\n                      {/* Changes to Terms */}\n                      <div className=\"mb-50\">\n                        <h2 className=\"section-title-small mb-30\">\n                          {t(\"terms.changes.title\")}\n                        </h2>\n                        <p className=\"text-gray\">{t(\"terms.changes.text\")}</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport { Link } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function MainPageNotFound() {\n  const { t } = useTranslation();\n\n  return (\n    <>\n      <UnifiedSEO\n        title=\"Page Not Found - 404\"\n        description=\"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.\"\n        slug=\"404\"\n        type=\"website\"\n        image=\"https://devskills.ee/404.jpg\"\n        keywords={[\"404\", \"page not found\", \"error\", \"devskills\"]}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              {/* Hero Section */}\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-1 mb-20 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                    style={{ fontSize: \"8rem\", fontWeight: \"700\" }}\n                  >\n                    404\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p\n                        className=\"section-title-small mb-0 opacity-075\"\n                        style={{ fontSize: \"2rem\" }}\n                      >\n                        Page Not Found\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n\n              {/* Content Section */}\n              <section className=\"page-section bg-dark-1 light-content\">\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2 text-center\">\n                      <div className=\"wow fadeInUp\" data-wow-delay=\"0.1s\">\n                        <h2 className=\"section-title mb-30 mb-sm-20\">\n                          <span className=\"text-gray\">Oops!</span> Something\n                          went wrong\n                          <span className=\"text-gray\">.</span>\n                        </h2>\n                        <div className=\"text-gray mb-40 mb-sm-30\">\n                          <p className=\"mb-20\">\n                            The page you were looking for could not be found. It\n                            might have been removed, had its name changed, or is\n                            temporarily unavailable.\n                          </p>\n                          <p className=\"mb-0\">\n                            Don't worry, you can navigate back to our homepage\n                            or explore our services.\n                          </p>\n                        </div>\n                        <div className=\"local-scroll\">\n                          <Link\n                            to=\"/\"\n                            className=\"btn btn-mod btn-w btn-circle btn-medium me-3 mb-xs-10\"\n                            data-btn-animate=\"y\"\n                          >\n                            <span className=\"btn-animate-y\">\n                              <span className=\"btn-animate-y-1\">\n                                <i className=\"mi-home size-18 align-center me-2\" />\n                                Back to Home\n                              </span>\n                              <span\n                                className=\"btn-animate-y-2\"\n                                aria-hidden=\"true\"\n                              >\n                                <i className=\"mi-home size-18 align-center me-2\" />\n                                Back to Home\n                              </span>\n                            </span>\n                          </Link>\n                          <Link\n                            to=\"/services\"\n                            className=\"btn btn-mod btn-border-w btn-circle btn-medium\"\n                            data-btn-animate=\"y\"\n                          >\n                            <span className=\"btn-animate-y\">\n                              <span className=\"btn-animate-y-1\">\n                                Our Services\n                              </span>\n                              <span\n                                className=\"btn-animate-y-2\"\n                                aria-hidden=\"true\"\n                              >\n                                Our Services\n                              </span>\n                            </span>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "file": "assets/pages-other-BrCzhuD6.js"}