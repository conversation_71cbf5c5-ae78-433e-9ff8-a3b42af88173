import React from "react";
import { useTranslation } from "react-i18next";

export default function TranslationTest() {
  const { t, i18n, ready } = useTranslation();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div
      style={{
        position: "fixed",
        bottom: "10px",
        right: "10px",
        background: "rgba(0,0,0,0.9)",
        color: "white",
        padding: "15px",
        borderRadius: "8px",
        fontSize: "12px",
        zIndex: 10000,
        maxWidth: "350px",
        fontFamily: "monospace",
      }}
    >
      <h4 style={{ margin: "0 0 10px 0", color: "#00ff00" }}>🔧 Translation Test</h4>
      
      <div style={{ marginBottom: "8px" }}>
        <strong>Ready:</strong> {ready ? "✅ Yes" : "❌ No"}
      </div>
      
      <div style={{ marginBottom: "8px" }}>
        <strong>Current Language:</strong> {i18n.language || 'undefined'}
      </div>
      
      <div style={{ marginBottom: "8px" }}>
        <strong>Resources Loaded:</strong> {i18n.hasResourceBundle(i18n.language, 'translation') ? "✅ Yes" : "❌ No"}
      </div>
      
      <div style={{ marginBottom: "8px" }}>
        <strong>Test Translations:</strong>
      </div>
      
      <div style={{ marginLeft: "10px", fontSize: "11px" }}>
        <div>nav.home: "{t('nav.home')}"</div>
        <div>blog.title: "{t('blog.title')}"</div>
        <div>common.loading: "{t('common.loading')}"</div>
      </div>
      
      <div style={{ marginTop: "10px" }}>
        <strong>Quick Language Test:</strong>
        <div style={{ display: "flex", gap: "5px", marginTop: "5px" }}>
          {['en', 'et', 'fi', 'de', 'sv'].map((lang) => (
            <button
              key={lang}
              onClick={() => i18n.changeLanguage(lang)}
              style={{
                padding: "2px 6px",
                fontSize: "10px",
                background: i18n.language === lang ? "#007bff" : "#333",
                color: "white",
                border: "none",
                borderRadius: "3px",
                cursor: "pointer",
              }}
            >
              {lang.toUpperCase()}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
