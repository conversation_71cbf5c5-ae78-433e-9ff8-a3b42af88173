import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { trackPageView, trackTimeOnPage, trackScrollDepth } from '@/utils/analytics';

export const usePageAnalytics = (pageTitle) => {
  const location = useLocation();
  const startTimeRef = useRef(null);
  const scrollDepthRef = useRef(0);
  const scrollMilestonesRef = useRef(new Set());

  useEffect(() => {
    // Track page view
    const fullPageTitle = pageTitle || document.title;
    const pageLocation = window.location.href;
    
    trackPageView(fullPageTitle, pageLocation);
    startTimeRef.current = Date.now();

    // Reset scroll tracking for new page
    scrollDepthRef.current = 0;
    scrollMilestonesRef.current.clear();

    // Scroll depth tracking
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);

      // Update max scroll depth
      if (scrollPercentage > scrollDepthRef.current) {
        scrollDepthRef.current = scrollPercentage;
      }

      // Track milestone percentages (25%, 50%, 75%, 90%, 100%)
      const milestones = [25, 50, 75, 90, 100];
      milestones.forEach(milestone => {
        if (scrollPercentage >= milestone && !scrollMilestonesRef.current.has(milestone)) {
          scrollMilestonesRef.current.add(milestone);
          trackScrollDepth(milestone, pageLocation);
        }
      });
    };

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Track time on page when user leaves
    const handleBeforeUnload = () => {
      if (startTimeRef.current) {
        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);
        trackTimeOnPage(timeOnPage, pageLocation);
      }
    };

    // Track time on page when component unmounts (route change)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && startTimeRef.current) {
        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);
        trackTimeOnPage(timeOnPage, pageLocation);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      // Track final time on page
      if (startTimeRef.current) {
        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);
        trackTimeOnPage(timeOnPage, pageLocation);
      }
    };
  }, [location.pathname, pageTitle]);

  // Return analytics functions for manual tracking
  return {
    trackCustomEvent: (eventName, category, label, value, additionalParams) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, {
          event_category: category,
          event_label: label,
          value: value,
          page_location: window.location.href,
          ...additionalParams,
          send_to: 'G-8NEGL4LL8Q'
        });
      }
    },
    getCurrentScrollDepth: () => scrollDepthRef.current,
    getTimeOnPage: () => startTimeRef.current ? Math.round((Date.now() - startTimeRef.current) / 1000) : 0
  };
};
