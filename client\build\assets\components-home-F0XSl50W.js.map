{"version": 3, "file": "components-home-F0XSl50W.js", "sources": ["../../src/utils/analytics.js", "../../src/components/home/<USER>", "../../src/hooks/usePageAnalytics.js", "../../src/data/services.js", "../../src/components/home/<USER>", "../../src/data/bms.js", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/data/contact.js", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/components/home/<USER>", "../../src/data/team.js", "../../src/components/home/<USER>", "../../src/components/home/<USER>"], "sourcesContent": ["// Google Analytics 4 utility functions\n\n// Extract language from URL path\nconst extractLanguageFromPath = (path) => {\n  const match = path.match(/\\/([a-z]{2})\\//);\n  return match ? match[1] : \"en\";\n};\n\n// Get page type from URL\nconst getPageType = (path) => {\n  if (path.includes(\"/blog-single/\")) return \"blog_post\";\n  if (path.includes(\"/blog\")) return \"blog_list\";\n  if (path.includes(\"/products/\")) return \"product\";\n  if (path.includes(\"/about\")) return \"about\";\n  if (path.includes(\"/contact\")) return \"contact\";\n  if (path.includes(\"/services\")) return \"services\";\n  if (path.includes(\"/portfolio\")) return \"portfolio\";\n  return \"home\";\n};\n\n// Track custom events to our backend\n// Get API base URL - same pattern as api.jsx\nconst getApiBaseUrl = () => {\n  // In production, both frontend and backend are served from the same domain\n  if (import.meta.env.PROD) {\n    return `${window.location.protocol}//${window.location.host}`;\n  }\n\n  // In development, backend runs on port 4004\n  return \"http://localhost:4004\";\n};\n\nexport const trackCustomEvent = async (\n  eventName,\n  eventCategory,\n  eventLabel,\n  eventValue = null,\n  metadata = {}\n) => {\n  try {\n    const language = extractLanguageFromPath(window.location.pathname);\n    const path = window.location.pathname;\n\n    // Extract blog post slug if on a blog post page\n    let blogPostId = null;\n    const blogPostMatch = path.match(/\\/blog-single\\/([^\\/]+)/);\n    if (blogPostMatch) {\n      const slug = blogPostMatch[1];\n      // The backend will resolve the slug to blogPostId\n      blogPostId = slug;\n    }\n\n    const apiBaseUrl = getApiBaseUrl();\n    await fetch(`${apiBaseUrl}/api/analytics/event`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        eventName,\n        eventCategory,\n        eventLabel,\n        eventValue,\n        path,\n        language,\n        blogPostId,\n        metadata: {\n          ...metadata,\n          timestamp: new Date().toISOString(),\n          userAgent: navigator.userAgent,\n          referrer: document.referrer,\n        },\n      }),\n    });\n  } catch (error) {\n    console.error(\"Error tracking custom event:\", error);\n  }\n};\n\n// Track page views (non-blocking)\nexport const trackPageView = (page_title, page_location) => {\n  // Use requestIdleCallback for better performance, fallback to setTimeout\n  const trackFn = () => {\n    if (typeof window !== \"undefined\" && window.gtag) {\n      const language = extractLanguageFromPath(page_location);\n\n      window.gtag(\"event\", \"page_view\", {\n        page_title: page_title,\n        page_location: page_location,\n        language: language,\n        page_type: getPageType(page_location),\n        send_to: \"G-8NEGL4LL8Q\",\n      });\n\n      // Also track in our custom analytics\n      trackCustomEvent(\"page_view\", \"navigation\", page_title, null, {\n        language: language,\n        page_type: getPageType(page_location),\n      });\n    }\n  };\n\n  if (typeof window !== \"undefined\" && window.requestIdleCallback) {\n    window.requestIdleCallback(trackFn);\n  } else {\n    setTimeout(trackFn, 0);\n  }\n};\n\n// Track button clicks\nexport const trackButtonClick = (\n  button_name,\n  button_location,\n  additional_params = {}\n) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    const language = extractLanguageFromPath(window.location.pathname);\n\n    window.gtag(\"event\", \"click\", {\n      event_category: \"Button\",\n      event_label: button_name,\n      button_location: button_location,\n      language: language,\n      page_type: getPageType(window.location.pathname),\n      ...additional_params,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n\n    // Also track in our custom analytics\n    trackCustomEvent(\"click\", \"Button\", button_name, null, {\n      button_location,\n      language,\n      page_type: getPageType(window.location.pathname),\n      ...additional_params,\n    });\n  }\n};\n\n// Track form submissions\nexport const trackFormSubmission = (\n  form_name,\n  form_location,\n  success = true\n) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", success ? \"form_submit\" : \"form_error\", {\n      event_category: \"Form\",\n      event_label: form_name,\n      form_location: form_location,\n      success: success,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track contact form submissions (conversion)\nexport const trackContactFormSubmission = (email, message_length) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    // Track as conversion\n    window.gtag(\"event\", \"conversion\", {\n      send_to: \"G-8NEGL4LL8Q\",\n      event_category: \"Contact\",\n      event_label: \"Contact Form Submission\",\n      value: 1,\n      currency: \"EUR\",\n      user_email: email,\n      message_length: message_length,\n    });\n\n    // Also track as generate_lead\n    window.gtag(\"event\", \"generate_lead\", {\n      send_to: \"G-8NEGL4LL8Q\",\n      event_category: \"Lead Generation\",\n      event_label: \"Contact Form\",\n      value: 1,\n      currency: \"EUR\",\n    });\n  }\n};\n\n// Track language changes\nexport const trackLanguageChange = (from_language, to_language) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"language_change\", {\n      event_category: \"User Interaction\",\n      event_label: `${from_language} to ${to_language}`,\n      from_language: from_language,\n      to_language: to_language,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track navigation menu interactions\nexport const trackNavigation = (menu_item, menu_location) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"navigation_click\", {\n      event_category: \"Navigation\",\n      event_label: menu_item,\n      menu_location: menu_location,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track scroll depth\nexport const trackScrollDepth = (scroll_percentage, page_location) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"scroll\", {\n      event_category: \"User Engagement\",\n      event_label: `${scroll_percentage}% scrolled`,\n      scroll_percentage: scroll_percentage,\n      page_location: page_location,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track time on page\nexport const trackTimeOnPage = (time_seconds, page_location) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"timing_complete\", {\n      name: \"page_view_time\",\n      value: time_seconds,\n      event_category: \"User Engagement\",\n      event_label: page_location,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n\n    // Also track in our custom analytics\n    trackCustomEvent(\n      \"timing_complete\",\n      \"User Engagement\",\n      page_location,\n      time_seconds,\n      {\n        page_view_time: time_seconds,\n      }\n    );\n  }\n};\n\n// Track file downloads\nexport const trackFileDownload = (file_name, file_type, download_location) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"file_download\", {\n      event_category: \"Downloads\",\n      event_label: file_name,\n      file_type: file_type,\n      download_location: download_location,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track external link clicks\nexport const trackExternalLink = (link_url, link_text, link_location) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"click\", {\n      event_category: \"External Link\",\n      event_label: link_text,\n      link_url: link_url,\n      link_location: link_location,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track search queries (if you have search functionality)\nexport const trackSearch = (search_term, search_results_count) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"search\", {\n      search_term: search_term,\n      event_category: \"Search\",\n      event_label: search_term,\n      search_results: search_results_count,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Track Business Comanager conversion events\nexport const trackComanagerConversion = (\n  source_location,\n  additional_params = {}\n) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    const language = extractLanguageFromPath(window.location.pathname);\n\n    // Track as conversion event\n    window.gtag(\"event\", \"comanager_conversion\", {\n      event_category: \"Conversion\",\n      event_label: \"Business Comanager CTA\",\n      source_location: source_location,\n      language: language,\n      page_type: getPageType(window.location.pathname),\n      value: 100, // Assign conversion value\n      currency: \"EUR\",\n      ...additional_params,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n\n    // Also track as purchase event for GA4 conversion tracking\n    window.gtag(\"event\", \"purchase\", {\n      transaction_id: `comanager_${Date.now()}`,\n      value: 100,\n      currency: \"EUR\",\n      items: [\n        {\n          item_id: \"comanager_cta\",\n          item_name: \"Business Comanager CTA Click\",\n          item_category: \"Conversion\",\n          item_variant: source_location,\n          quantity: 1,\n          price: 100,\n        },\n      ],\n      source_location: source_location,\n      language: language,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n\n    // Track in our custom analytics\n    trackCustomEvent(\n      \"comanager_conversion\",\n      \"Conversion\",\n      source_location,\n      100,\n      {\n        language,\n        page_type: getPageType(window.location.pathname),\n        conversion_type: \"comanager_cta\",\n        ...additional_params,\n      }\n    );\n  }\n};\n\n// Track video interactions\nexport const trackVideoInteraction = (\n  video_title,\n  action,\n  video_progress = null\n) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    const eventData = {\n      event_category: \"Video\",\n      event_label: video_title,\n      video_action: action,\n      send_to: \"G-8NEGL4LL8Q\",\n    };\n\n    if (video_progress !== null) {\n      eventData.video_progress = video_progress;\n    }\n\n    window.gtag(\"event\", `video_${action}`, eventData);\n  }\n};\n\n// Track user engagement milestones\nexport const trackEngagementMilestone = (\n  milestone_name,\n  page_location,\n  additional_data = {}\n) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    window.gtag(\"event\", \"engagement_milestone\", {\n      event_category: \"User Engagement\",\n      event_label: milestone_name,\n      page_location: page_location,\n      ...additional_data,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n  }\n};\n\n// Generic track event function for webstore and other components\nexport const trackEvent = (eventName, eventData = {}) => {\n  if (typeof window !== \"undefined\" && window.gtag) {\n    const language = extractLanguageFromPath(window.location.pathname);\n\n    window.gtag(\"event\", eventName, {\n      event_category: eventData.category || \"User Interaction\",\n      event_label: eventData.label || eventName,\n      language: language,\n      page_type: getPageType(window.location.pathname),\n      ...eventData,\n      send_to: \"G-8NEGL4LL8Q\",\n    });\n\n    // Also track in our custom analytics\n    trackCustomEvent(\n      eventName,\n      eventData.category || \"User Interaction\",\n      eventData.label || eventName,\n      eventData.value || null,\n      {\n        language,\n        page_type: getPageType(window.location.pathname),\n        ...eventData,\n      }\n    );\n  }\n};\n\n// Debug function to check if analytics is working\nexport const debugAnalytics = () => {\n  if (typeof window !== \"undefined\") {\n    console.log(\"Google Analytics Debug Info:\");\n    console.log(\"gtag available:\", typeof window.gtag !== \"undefined\");\n    console.log(\"dataLayer:\", window.dataLayer);\n\n    if (window.gtag) {\n      // Test event\n      window.gtag(\"event\", \"debug_test\", {\n        event_category: \"Debug\",\n        event_label: \"Analytics Test\",\n        send_to: \"G-8NEGL4LL8Q\",\n      });\n      console.log(\"Test event sent\");\n    }\n  }\n};\n", "import React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function About() {\n  const { t } = useTranslation();\n  return (\n    <div className=\"col-lg-6 offset-lg-1\">\n      <div className=\"row\">\n        <div\n          className=\"col-sm-6 pt-60 pt-xs-0 mb-xs-40\"\n          data-rellax-y=\"\"\n          data-rellax-speed=\"-0.5\"\n          data-rellax-percentage=\"0.5\"\n        >\n          <div className=\"spot-box clearfix mb-30\">\n            <div className=\"spot-box-icon float-end ms-3\" />\n            <div className=\"spot-box-text text-end\">\n              {/* <span className=\"text-gray\">{t(\"hero.brands\")}</span> */}\n            </div>\n          </div>\n          <img\n            src=\"/assets/img/2.jpg\"\n            width={400}\n            height={489}\n            className=\"w-100 round grayscale\"\n            alt=\"Image Description\"\n          />\n        </div>\n        <div\n          className=\"col-sm-6\"\n          data-rellax-y=\"\"\n          data-rellax-speed=\"0.5\"\n          data-rellax-percentage=\"0.5\"\n        >\n          <img\n            src=\"/assets/img/3.jpg\"\n            width={400}\n            height={489}\n            className=\"w-100 round grayscale\"\n            alt=\"Image Description\"\n          />\n          <div className=\"spot-box clearfix mt-30\">\n            <div className=\"spot-box-icon float-start me-3\" />\n            <div className=\"spot-box-text\">\n              {/* <span className=\"text-gray\">{t(\"hero.interfaces\")}</span> */}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import { useEffect, useRef } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport { trackPageView, trackTimeOnPage, trackScrollDepth } from '@/utils/analytics';\n\nexport const usePageAnalytics = (pageTitle) => {\n  const location = useLocation();\n  const startTimeRef = useRef(null);\n  const scrollDepthRef = useRef(0);\n  const scrollMilestonesRef = useRef(new Set());\n\n  useEffect(() => {\n    // Track page view\n    const fullPageTitle = pageTitle || document.title;\n    const pageLocation = window.location.href;\n    \n    trackPageView(fullPageTitle, pageLocation);\n    startTimeRef.current = Date.now();\n\n    // Reset scroll tracking for new page\n    scrollDepthRef.current = 0;\n    scrollMilestonesRef.current.clear();\n\n    // Scroll depth tracking\n    const handleScroll = () => {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);\n\n      // Update max scroll depth\n      if (scrollPercentage > scrollDepthRef.current) {\n        scrollDepthRef.current = scrollPercentage;\n      }\n\n      // Track milestone percentages (25%, 50%, 75%, 90%, 100%)\n      const milestones = [25, 50, 75, 90, 100];\n      milestones.forEach(milestone => {\n        if (scrollPercentage >= milestone && !scrollMilestonesRef.current.has(milestone)) {\n          scrollMilestonesRef.current.add(milestone);\n          trackScrollDepth(milestone, pageLocation);\n        }\n      });\n    };\n\n    // Add scroll listener\n    window.addEventListener('scroll', handleScroll, { passive: true });\n\n    // Track time on page when user leaves\n    const handleBeforeUnload = () => {\n      if (startTimeRef.current) {\n        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);\n        trackTimeOnPage(timeOnPage, pageLocation);\n      }\n    };\n\n    // Track time on page when component unmounts (route change)\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === 'hidden' && startTimeRef.current) {\n        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);\n        trackTimeOnPage(timeOnPage, pageLocation);\n      }\n    };\n\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n\n    // Cleanup function\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n\n      // Track final time on page\n      if (startTimeRef.current) {\n        const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);\n        trackTimeOnPage(timeOnPage, pageLocation);\n      }\n    };\n  }, [location.pathname, pageTitle]);\n\n  // Return analytics functions for manual tracking\n  return {\n    trackCustomEvent: (eventName, category, label, value, additionalParams) => {\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', eventName, {\n          event_category: category,\n          event_label: label,\n          value: value,\n          page_location: window.location.href,\n          ...additionalParams,\n          send_to: 'G-8NEGL4LL8Q'\n        });\n      }\n    },\n    getCurrentScrollDepth: () => scrollDepthRef.current,\n    getTimeOnPage: () => startTimeRef.current ? Math.round((Date.now() - startTimeRef.current) / 1000) : 0\n  };\n};\n", "export const services6 = [\n  {\n    width: 48,\n    height: 64,\n    path: \"M47.7 23.5h-10V5.8c0-1.6-1.3-2.8-2.8-2.8H5.8C4.3 3 3 4.3 3 5.8v29.7c0 1.6 1.3 2.8 2.8 2.8h10v10c0 1.6 1.3 2.8 2.8 2.8h29.1c1.6 0 2.8-1.3 2.8-2.8V26.4c.1-1.6-1.2-2.9-2.8-2.9zm-34.7-9.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm11.1-16.6h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm11.1-16.6h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm0 8.3h-4.2v-4.2h4.2v4.2zm11.1 16.7h-4.2v-4.2h4.2v4.2zm0-8.4h-4.2v-4.2h4.2v4.2z\",\n    key: \"web\",\n    image: \"/assets/img/webdesign.jpg\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M36 0H12C5.4 0 0 5.4 0 12v40c0 6.6 5.4 12 12 12h24c6.6 0 12-5.4 12-12V12c0-6.6-5.4-12-12-12zm-24 4h24c4.4 0 8 3.6 8 8v40c0 4.4-3.6 8-8 8H12c-4.4 0-8-3.6-8-8V12c0-4.4 3.6-8 8-8z M24 56c2.2 0 4 1.8 4 4s-1.8 4-4 4-4-1.8-4-4 1.8-4 4-4z\",\n    key: \"mobile\",\n    image: \"/assets/img/mobiledesign.jpg\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M44 0H4C1.8 0 0 1.8 0 4v56c0 2.2 1.8 4 4 4h40c2.2 0 4-1.8 4-4V4c0-2.2-1.8-4-4-4zm-4 56H8V8h32v48z M16 16h16v4H16z M16 24h16v4H16z M16 32h16v4H16z\",\n    key: \"backend\",\n    image: \"/assets/img/server.jpg\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M24 0C10.7 0 0 10.7 0 24s10.7 24 24 24 24-10.7 24-24S37.3 0 24 0zm0 44c-11 0-20-9-20-20S13 4 24 4s20 9 20 20-9 20-20 20zm8-22c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z\",\n    key: \"ai\",\n    image: \"/assets/img/aichat.jpg\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M44 16L24 0 4 16v32l20 16 20-16V16zm-20 4c2.2 0 4 1.8 4 4s-1.8 4-4 4-4-1.8-4-4 1.8-4 4-4zm0 24c-6.6 0-12-5.4-12-12s5.4-12 12-12 12 5.4 12 12-5.4 12-12 12z\",\n    key: \"blockchain\",\n    image: \"/assets/img/blockchain.jpg\",\n  },\n  {\n    width: 48,\n    height: 64,\n    path: \"M42 48h-4V24c0-1.1-.9-2-2-2H24v-4h4c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h4v4H12c-1.1 0-2 .9-2 2v24H6c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2h-4V26h20v22h-4c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2z\",\n    key: \"bms\",\n    image: \"/assets/img/business_software.jpg\",\n  },\n];\n", "import { services6 } from \"@/data/services\";\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Service() {\n  const { t } = useTranslation();\n  return (\n    <>\n      <div\n        className=\"page-section bg-dark-1 bg-dark-alpha-80 light-content parallax-7 pb-140\"\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container position-relative\">\n          <div className=\"row mb-70 mb-sm-50\">\n            <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n              <h2 className=\"section-title mb-30 mb-sm-20\">\n                {t(\"services.title\")}\n              </h2>\n              <div className=\"text-gray\">{t(\"services.description\")}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"container mt-n140 position-relative z-index-1\">\n        <div className=\"row mb-n30\">\n          {services6.map((elm, i) => (\n            <div\n              key={i}\n              className=\"col-md-6 col-lg-4 d-flex align-items-stretch mb-30\"\n            >\n              <div className=\"service-card-container\">\n                <div className=\"service-card-img\">\n                  <img\n                    src={elm.image}\n                    width={400}\n                    height={250}\n                    alt={t(`services.${elm.key}.title`)}\n                    className=\"service-card-image\"\n                  />\n                </div>\n                <div className=\"service-card-content\">\n                  <h3 className=\"service-card-title\">\n                    {t(`services.${elm.key}.title`)}\n                  </h3>\n                  <div className=\"service-card-text\">\n                    {t(`services.${elm.key}.text`)}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </>\n  );\n}\n", "export const bmsItems = [\n  {\n    id: 1,\n    imageSrc: \"transparent\",\n    title: \"Core Module\",\n    type: \"Lightbox\",\n    categories: [\"core\"],\n    description: {\n      heading: \"Core Module\",\n      subheading: \"The Foundation of Your Business\",\n      text: \"The Core Module is the foundation of the Business Comanager platform. It provides the essential framework for managing your entire business ecosystem, including user management, permissions, system settings, and integration capabilities. With the Core Module, you can configure your business rules, policies, and strategies, while the AI assistant helps you manage tasks, monitor compliance, and track performance metrics.\",\n    },\n  },\n  {\n    id: 2,\n    imageSrc: \"transparent\",\n    title: \"Accounting Module\",\n    type: \"External Page\",\n    categories: [\"accounting\"],\n    description: {\n      heading: \"Accounting Module\",\n      subheading: \"Financial Management Simplified\",\n      text: \"The Accounting Module provides a comprehensive solution for managing your organization's financial operations. From journal entries and reconciliation to financial reporting and tax management, this module streamlines your accounting processes. With features like chart of accounts, balance sheets, income statements, and cash flow analysis, you'll have complete visibility into your financial health and can make informed decisions to drive your business forward.\",\n    },\n  },\n  {\n    id: 3,\n    imageSrc: \"transparent\",\n    title: \"Budget Module\",\n    type: \"External Page\",\n    categories: [\"budget\"],\n    description: {\n      heading: \"Budget Module\",\n      subheading: \"Strategic Financial Planning\",\n      text: \"Take control of your finances with our Budget Module. This powerful tool enables you to create, manage, and track budgets across your organization. Monitor income and expenses, analyze cash flow, and manage assets with ease. The forecasting capabilities help you plan for the future, while detailed reports provide insights into spending patterns and financial performance. With the Budget Module, you can make data-driven decisions to optimize your financial resources.\",\n    },\n  },\n  {\n    id: 4,\n    imageSrc: \"transparent\",\n    title: \"Human Resources\",\n    type: \"External Page\",\n    categories: [\"hr\", \"management\"],\n    description: {\n      heading: \"Human Resources Module\",\n      subheading: \"Empowering Your Workforce\",\n      text: \"The Human Resources Module helps you manage your most valuable asset - your people. From employee records and department structures to leave management and attendance tracking, this module streamlines HR operations. Handle compensation, benefits, and payroll with ease, while fostering employee development through performance management and training programs. The HR Module provides the tools you need to build and maintain a productive, engaged workforce.\",\n    },\n  },\n  {\n    id: 5,\n    imageSrc: \"transparent\",\n    title: \"Recruitment Module\",\n    type: \"External Page\",\n    categories: [\"hr\"],\n    description: {\n      heading: \"Recruitment Module\",\n      subheading: \"Talent Acquisition Reimagined\",\n      text: \"Transform your hiring process with our Recruitment Module. This comprehensive solution helps you manage job postings, track applications, and streamline candidate evaluations. The AI-powered matching system connects the right candidates with the right positions, while the customizable form builder allows you to create tailored application forms. With features for workforce intermediation and detailed analytics, you can optimize your recruitment strategy and find the best talent for your organization.\",\n    },\n  },\n  {\n    id: 6,\n    imageSrc: \"transparent\",\n    title: \"Production Module\",\n    type: \"Lightbox\",\n    categories: [\"production\", \"management\"],\n    description: {\n      heading: \"Production Module\",\n      subheading: \"Streamlining Operations\",\n      text: \"The Production Module optimizes your manufacturing and project management processes. Track projects from inception to completion, manage tasks and assignments, and ensure quality control at every step. With features for resource management, equipment tracking, and materials planning, you can maximize efficiency and minimize waste. The AI insights provide valuable recommendations for process improvements, while detailed reports help you monitor performance and make data-driven decisions.\",\n    },\n  },\n  {\n    id: 7,\n    imageSrc: \"transparent\",\n    title: \"Sales Module\",\n    type: \"External Page\",\n    categories: [\"sales\"],\n    description: {\n      heading: \"Sales Module\",\n      subheading: \"Revenue Growth Engine\",\n      text: \"Accelerate your sales cycle and boost revenue with our Sales Module. This powerful tool helps you manage leads, track opportunities, and close deals more efficiently. Monitor sales performance, analyze trends, and forecast future revenue with intuitive dashboards and reports. The customer relationship management features enable you to build stronger connections with clients, while the integration with other modules ensures seamless order processing and financial tracking.\",\n    },\n  },\n  {\n    id: 8,\n    imageSrc: \"transparent\",\n    title: \"Quality Control\",\n    type: \"Lightbox\",\n    categories: [\"production\"],\n    description: {\n      heading: \"Quality Control Module\",\n      subheading: \"Excellence in Every Detail\",\n      text: \"Ensure consistent quality across all your products and services with our Quality Control Module. Create and manage comprehensive checklists, document inspection results, and track quality metrics over time. The photo evidence feature allows you to visually document quality issues and resolutions, while the integration with the Production Module ensures that quality control is embedded in your manufacturing process. With detailed analytics and reporting, you can identify trends and continuously improve your quality standards.\",\n    },\n  },\n  {\n    id: 9,\n    imageSrc: \"transparent\",\n    title: \"Communication Module\",\n    type: \"External Page\",\n    categories: [\"communication\", \"management\"],\n    description: {\n      heading: \"Communication Module\",\n      subheading: \"Seamless Connectivity\",\n      text: \"The Communication Module enhances collaboration and information sharing across your organization. Manage emails, chat messages, and customer inquiries from a centralized dashboard. Create and manage custom API endpoints to integrate with external systems and services. With real-time notifications and message tracking, you can ensure that important communications are never missed. The module's intuitive interface makes it easy to stay connected with team members, partners, and customers.\",\n    },\n  },\n  {\n    id: 10,\n    imageSrc: \"transparent\",\n    title: \"Companies Module\",\n    type: \"External Page\",\n    categories: [\"companies\", \"management\"],\n    description: {\n      heading: \"Companies Module\",\n      subheading: \"Partner Relationship Management\",\n      text: \"The Companies Module helps you manage relationships with clients, suppliers, and other business partners. Track company details, monitor interactions, and assess risks with comprehensive tools for partner relationship management. The workforce needs assessment feature helps you plan resource allocation, while the compliance tracking ensures that all partnerships meet regulatory requirements. With detailed analytics and reporting, you can optimize your business relationships and drive mutual growth.\",\n    },\n  },\n  {\n    id: 11,\n    imageSrc: \"transparent\",\n    title: \"All Modules\",\n    type: \"External Page\",\n    categories: [\"all\"],\n    description: {\n      heading: \"Business Comanager\",\n      subheading: \"The Ultimate Business Management System\",\n      text: \"Business Comanager is our next-generation business management system that combines AI-driven automation with modular flexibility. It features a smart core that manages your business rules, policies, and strategies, while the AI assistant handles tasks on command, monitoring compliance and performance. Choose from essential modules including accounting, HR, recruiting, production, and quality control - all designed to grow with your business needs. Experience the future of business management, where complexity meets simplicity.\",\n    },\n  },\n];\n", "// client\\src\\components\\home\\DevskillsBMS.jsx\n\nimport { bmsItems } from \"@/data/bms\";\nimport React, { useEffect, useState } from \"react\";\nimport { Gallery, Item } from \"react-photoswipe-gallery\";\nimport { useTranslation } from \"react-i18next\";\nimport { trackComanagerConversion } from \"@/utils/analytics\";\n\nexport default function DevskillsBMS() {\n  // Use reactive translation hook\n  const { t: translate, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n\n  const [currentCategory, setCurrentCategory] = useState(\"all\");\n  const [filtered, setFiltered] = useState(bmsItems);\n  const [activeModule, setActiveModule] = useState(\"all\");\n\n  // Store the current module translation keys\n  const [moduleKeys, setModuleKeys] = useState({\n    heading: \"comanager.all.heading\",\n    subheading: \"comanager.all.subheading\",\n    text: \"comanager.all.text\",\n  });\n\n  useEffect(() => {\n    if (currentCategory === \"all\") {\n      setFiltered(\n        [...bmsItems].filter((item) => !item.categories.includes(\"all\"))\n      );\n    } else {\n      setFiltered(\n        [...bmsItems].filter(\n          (elm) =>\n            elm.categories.includes(currentCategory) &&\n            !elm.categories.includes(\"all\")\n        )\n      );\n    }\n  }, [currentCategory, currentLanguage]);\n\n  // Function to handle module button click\n  const handleModuleClick = (moduleCategory) => {\n    setActiveModule(moduleCategory);\n\n    // Update the module keys based on the selected module\n    setModuleKeys({\n      heading: `comanager.${moduleCategory}.heading`,\n      subheading: `comanager.${moduleCategory}.subheading`,\n      text: `comanager.${moduleCategory}.text`,\n    });\n  };\n\n  // Function to handle CTA click\n  const handleCTAClick = () => {\n    trackComanagerConversion(\"landing_page_bms_section\", {\n      cta_type: \"main_cta\",\n      section: \"devskills_bms\",\n    });\n    window.open(\"https://comanager.ee\", \"_blank\");\n  };\n\n  // Function to handle link box clicks\n  const handleLinkBoxClick = (e) => {\n    e.preventDefault();\n    trackComanagerConversion(\"landing_page_bms_section\", {\n      cta_type: \"image_overlay\",\n      section: \"devskills_bms\",\n    });\n    window.open(\"https://comanager.ee\", \"_blank\");\n  };\n\n  return (\n    <>\n      <div className=\"container\">\n        {/* Module Description */}\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h3 className=\"text-white opacity-05 mb-3 fs-5 text-uppercase\">\n              {translate(\"introducing\")}\n            </h3>\n            <h1 className=\"section-title mb-3\">\n              <span className=\"text-white opacity-06 fs-1\">DEVSKILLS</span>\n              <span className=\"text-white opacity-09 fs-1\">\n                {\" \"}\n                {translate(moduleKeys.heading)}\n              </span>\n            </h1>\n            <h3 className=\"text-white opacity-07 mb-4 fs-5 text-uppercase\">\n              {translate(moduleKeys.subheading)}\n            </h3>\n            <div className=\"text-white opacity-07\">\n              {translate(moduleKeys.text)}\n            </div>\n          </div>\n        </div>\n\n        {/* Works Filter - Original Rounded Buttons */}\n        <div className=\"works-filter works-filter-elegant text-center mb-50\">\n          <a\n            onClick={() => {\n              handleModuleClick(\"all\");\n              setCurrentCategory(\"all\");\n            }}\n            className={`filter ${activeModule === \"all\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.all\")}\n          </a>\n\n          {/* Core Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"core\");\n              setCurrentCategory(\"core\");\n            }}\n            className={`filter ${activeModule === \"core\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.core\")}\n          </a>\n\n          {/* Accounting Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"accounting\");\n              setCurrentCategory(\"accounting\");\n            }}\n            className={`filter ${\n              activeModule === \"accounting\" ? \"active\" : \"\"\n            }`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.accounting\")}\n          </a>\n\n          {/* Budget Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"budget\");\n              setCurrentCategory(\"budget\");\n            }}\n            className={`filter ${activeModule === \"budget\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.budget\")}\n          </a>\n\n          {/* HR Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"hr\");\n              setCurrentCategory(\"hr\");\n            }}\n            className={`filter ${activeModule === \"hr\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.hr\")}\n          </a>\n\n          {/* Recruitment Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"recruiting\");\n              setCurrentCategory(\"recruiting\");\n            }}\n            className={`filter ${\n              activeModule === \"recruiting\" ? \"active\" : \"\"\n            }`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.recruiting\")}\n          </a>\n\n          {/* Production Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"production\");\n              setCurrentCategory(\"production\");\n            }}\n            className={`filter ${\n              activeModule === \"production\" ? \"active\" : \"\"\n            }`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.production\")}\n          </a>\n\n          {/* Sales Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"sales\");\n              setCurrentCategory(\"sales\");\n            }}\n            className={`filter ${activeModule === \"sales\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.sales\")}\n          </a>\n\n          {/* Quality Control Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"quality\");\n              setCurrentCategory(\"quality\");\n            }}\n            className={`filter ${activeModule === \"quality\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.quality\")}\n          </a>\n\n          {/* Communication Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"communication\");\n              setCurrentCategory(\"communication\");\n            }}\n            className={`filter ${\n              activeModule === \"communication\" ? \"active\" : \"\"\n            }`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.communication\")}\n          </a>\n\n          {/* Companies Module */}\n          <a\n            onClick={() => {\n              handleModuleClick(\"companies\");\n              setCurrentCategory(\"companies\");\n            }}\n            className={`filter ${activeModule === \"companies\" ? \"active\" : \"\"}`}\n            style={{ cursor: \"pointer\" }}\n          >\n            {translate(\"comanager.module.companies\")}\n          </a>\n        </div>\n        {/* End Works Filter */}\n      </div>\n      <div\n        className=\"position-relative\"\n        style={{\n          width: \"100%\",\n          maxWidth: \"1600px\",\n          margin: \"0 auto\",\n        }}\n      >\n        <div className=\"bms-image-container\">\n          <img\n            src=\"/assets/img/devskills-bms.jpg\"\n            alt=\"Business Comanager Background\"\n          />\n          <ul\n            className=\"works-grid work-grid-gut-sm hide-titles\"\n            id=\"work-grid\"\n            style={{\n              position: \"absolute\",\n              top: 0,\n              left: 0,\n              width: \"100%\",\n              height: \"100%\",\n              display: \"grid\",\n              gridTemplateColumns: \"repeat(4, 1fr)\",\n              gap: \"5px\",\n              background: \"transparent\",\n              borderRadius: \"20px 20px 0 0\",\n              overflow: \"hidden\",\n              margin: 0,\n              padding: 0,\n            }}\n          >\n            <Gallery>\n              {filtered.map((item, index) => (\n                <li\n                  key={index}\n                  className={`work-item mix ${item.categories.join(\" \")}`}\n                  style={{\n                    background: \"transparent\",\n                    width: \"100%\",\n                    height: \"100%\",\n                  }}\n                >\n                  {item.type === \"Lightbox\" ? (\n                    <Item\n                      original={item.imgUrl}\n                      thumbnail={item.imgUrl}\n                      width={400}\n                      height={400}\n                    >\n                      {({ open }) => (\n                        <a\n                          onClick={handleLinkBoxClick}\n                          className=\"work-lightbox-link mfp-image\"\n                          style={{\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            width: \"100%\",\n                            height: \"100%\",\n                            borderRadius: \"10px\",\n                            overflow: \"hidden\",\n                            cursor: \"pointer\",\n                          }}\n                        >\n                          <div\n                            className=\"work-img\"\n                            style={{\n                              flex: 1,\n                              background: \"transparent\",\n                            }}\n                          />\n                          <div\n                            className=\"work-intro\"\n                            style={{\n                              padding: \"15px\",\n                            }}\n                          >\n                            <h3 className=\"work-title\">\n                              {translate(\n                                `comanager.module.${item.title\n                                  .split(\" \")[0]\n                                  .toLowerCase()}`\n                              )}\n                            </h3>\n                            <div className=\"work-descr\">{item.type}</div>\n                          </div>\n                        </a>\n                      )}\n                    </Item>\n                  ) : (\n                    <a\n                      onClick={handleLinkBoxClick}\n                      className=\"work-ext-link\"\n                      style={{ background: \"transparent\", cursor: \"pointer\" }}\n                    >\n                      <div\n                        className=\"work-img\"\n                        style={{\n                          height: \"400px\", // Square dimensions\n                          background: \"transparent\",\n                        }}\n                      >\n                        <div\n                          className=\"work-img-bg\"\n                          style={{ background: \"transparent\" }}\n                        />\n                      </div>\n                      <div className=\"work-intro\">\n                        <h3 className=\"work-title\">\n                          {translate(\n                            `comanager.module.${item.title\n                              .split(\" \")[0]\n                              .toLowerCase()}`\n                          )}\n                        </h3>\n                        <div className=\"work-descr\">{item.type}</div>\n                      </div>\n                    </a>\n                  )}\n                </li>\n              ))}\n            </Gallery>\n          </ul>\n        </div>\n      </div>\n\n      {/* CTA Section */}\n      <section\n        className=\"small-section bg-dark-2 light-content bg-dark-alpha-80 parallax-6\"\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container\">\n          <div className=\"row mb-n10\">\n            <div className=\"col-md-6 offset-md-1 col-lg-5 offset-lg-2 mb-sm-30 text-center text-md-start\">\n              <h2 className=\"section-title-small mb-0 opacity-08\">\n                {translate(\"home.comanager.cta.title\")}\n              </h2>\n            </div>\n            <div className=\"col-md-4 col-lg-3 text-center text-md-end\">\n              <div className=\"mt-n20\">\n                <a\n                  onClick={handleCTAClick}\n                  className=\"link-hover-anim link-circle-1 align-middle\"\n                  data-link-animate=\"y\"\n                  style={{ cursor: \"pointer\" }}\n                >\n                  <span className=\"link-strong link-strong-unhovered\">\n                    {translate(\"home.comanager.cta.button\")}\n                  </span>\n                  <span\n                    className=\"link-strong link-strong-hovered\"\n                    aria-hidden=\"true\"\n                  >\n                    {translate(\"home.comanager.cta.button\")}\n                  </span>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </>\n  );\n}\n", "import React, { useState, useEffect } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { blogAPI } from \"@/utils/api\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Blog() {\n  const { i18n, t } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  const [blogPosts, setBlogPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        setLoading(true);\n        const result = await blogAPI.getFeaturedPosts(currentLanguage, 3);\n\n        if (result.response.ok && result.data) {\n          // Extract the posts array from the nested response structure\n          const posts = result.data.data?.data || result.data.data || [];\n          console.log(\"Blog API response:\", result.data);\n          console.log(\"Posts array:\", posts);\n          setBlogPosts(Array.isArray(posts) ? posts : []);\n        } else {\n          console.error(\"Failed to fetch blog posts:\", result.response.status);\n          setBlogPosts([]);\n        }\n      } catch (error) {\n        console.error(\"Error fetching blog posts:\", error);\n        setError(\"Failed to load blog posts\");\n        setBlogPosts([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, [currentLanguage]);\n\n  // Helper function to get translation for current language\n  const getTranslation = (post, field) => {\n    const translation = post.translations?.find(\n      (t) => t.language === currentLanguage\n    );\n    return (\n      translation?.[field] ||\n      post.translations?.find((t) => t.language === \"en\")?.[field] ||\n      \"\"\n    );\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h2 className=\"section-title mb-30 mb-sm-20\">\n              <span className=\"text-gray\">{t(\"blog.title\")}</span>\n              <span className=\"text-gray\">.</span>\n            </h2>\n            <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n          </div>\n        </div>\n        <div className=\"row mt-n30\">\n          <div className=\"col-12 text-center\">\n            <div className=\"text-gray\">Loading blog posts...</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"container\">\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h2 className=\"section-title mb-30 mb-sm-20\">\n              <span className=\"text-gray\">{t(\"blog.title\")}</span>\n              <span className=\"text-gray\">.</span>\n            </h2>\n            <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n          </div>\n        </div>\n        <div className=\"row mt-n30\">\n          <div className=\"col-12 text-center\">\n            <div className=\"text-gray\">{error}</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Show empty state if no posts\n  if (blogPosts.length === 0) {\n    return (\n      <div className=\"container\">\n        <div className=\"row mb-70 mb-sm-50\">\n          <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n            <h2 className=\"section-title mb-30 mb-sm-20\">\n              <span className=\"text-gray\">{t(\"blog.title\")}</span>\n              <span className=\"text-gray\">.</span>\n            </h2>\n            <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n          </div>\n        </div>\n        <div className=\"row mt-n30\">\n          <div className=\"col-12 text-center\">\n            <div className=\"text-gray\">No blog posts available yet.</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n  return (\n    <div className=\"container\">\n      <div className=\"row mb-70 mb-sm-50\">\n        <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n          <h2 className=\"section-title mb-30 mb-sm-20\">\n            <span className=\"text-gray\">{t(\"blog.title\")}</span>\n            <span className=\"text-gray\">.</span>\n          </h2>\n          <div className=\"text-gray\">{t(\"blog.subtitle\")}</div>\n        </div>\n        <div className=\"col-md-2 col-lg-3 text-center text-md-end mt-10 mt-sm-30\">\n          <Link to={`/blog`} className=\"section-more\">\n            {t(\"blog.all_posts\")} <i className=\"mi-chevron-right size-14\" />\n          </Link>\n        </div>\n      </div>\n      <div className=\"row mt-n30\">\n        {/* Post Item */}\n        {Array.isArray(blogPosts) &&\n          blogPosts.map((post, index) => (\n            <div\n              key={post.id}\n              className={`post-prev col-md-6 col-lg-4 mt-30 wow fadeInLeft`}\n              data-wow-delay={`${index * 0.1}s`}\n            >\n              <div className=\"post-prev-container\">\n                <div className=\"post-prev-img\">\n                  <Link to={`/blog-single/${post.slug}`}>\n                    <img\n                      src={\n                        post.featuredImage ||\n                        \"/assets/images/demo-elegant/blog/1.jpg\"\n                      }\n                      width={607}\n                      height={358}\n                      alt={getTranslation(post, \"title\")}\n                    />\n                  </Link>\n                </div>\n                <h3 className=\"post-prev-title\">\n                  <Link to={`/blog-single/${post.slug}`}>\n                    {getTranslation(post, \"title\")}\n                  </Link>\n                </h3>\n                <div className=\"post-prev-text\">\n                  {getTranslation(post, \"excerpt\")}\n                </div>\n                <div className=\"post-prev-info clearfix\">\n                  <div className=\"float-start\">\n                    <a href=\"#\" className=\"icon-author\">\n                      <i className=\"mi-user size-14 align-middle\" />\n                    </a>\n                    <a href=\"#\">{post.author?.name || \"DevSkills Team\"}</a>\n                  </div>\n                  <div className=\"float-end\">\n                    <i className=\"mi-calendar size-14 align-middle\" />\n                    <a href=\"#\">\n                      {new Date(\n                        post.publishedAt || post.createdAt\n                      ).toLocaleDateString()}\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        {/* End Post Item */}\n      </div>\n    </div>\n  );\n}\n", "import { useTranslation } from \"react-i18next\";\n\nexport const useContactItems = () => {\n  const { t } = useTranslation();\n\n  return [\n    {\n      iconClass: \"mi-location\",\n      title: t(\"contact.address.title\"),\n      text: t(\"contact.address.text\"),\n      link: {\n        url: \"https://maps.app.goo.gl/kjvsp1j2f15nff1B8\",\n        text: t(\"contact.address.link\"),\n        rel: \"nofollow noopener\",\n        target: \"_blank\",\n      },\n    },\n    {\n      iconClass: \"mi-email\",\n      title: t(\"contact.email.title\"),\n      text: t(\"contact.email.text\"),\n      link: {\n        url: \"mailto:<EMAIL>\",\n        text: t(\"contact.email.link\"),\n      },\n    },\n    {\n      iconClass: \"mi-mobile\",\n      title: t(\"contact.phone.title\"),\n      text: t(\"contact.phone.text\"),\n      link: {\n        url: \"tel:+37256282038\",\n        text: t(\"contact.phone.link\"),\n      },\n    },\n  ];\n};\n\n// For backward compatibility\nexport const contactItems = [\n  {\n    iconClass: \"mi-location\",\n    title: \"Address\",\n    text: \"Devskills OÜ, Tornimäe tn 7, 10145 Tallinn\",\n    link: {\n      url: \"https://maps.app.goo.gl/kjvsp1j2f15nff1B8\",\n      text: \"See Map\",\n      rel: \"nofollow noopener\",\n      target: \"_blank\",\n    },\n  },\n  {\n    iconClass: \"mi-email\",\n    title: \"Email\",\n    text: \"<EMAIL>\",\n    link: {\n      url: \"mailto:<EMAIL>\",\n      text: \"Say Hello\",\n    },\n  },\n  {\n    iconClass: \"mi-mobile\",\n    title: \"Phone\",\n    text: \"+372 5628 2038\",\n    link: {\n      url: \"tel:+37256282038\",\n      text: \"Call now\",\n    },\n  },\n];\n", "import { useContactItems } from \"@/data/contact\";\nimport React, { useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport axios from \"axios\";\nimport {\n  trackContactFormSubmission,\n  trackFormSubmission,\n} from \"@/utils/analytics\";\n\nexport default function Contact() {\n  const { t } = useTranslation();\n  const contactItems = useContactItems();\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\",\n  });\n  const [formStatus, setFormStatus] = useState({\n    submitting: false,\n    success: false,\n    error: false,\n    message: \"\",\n  });\n  // Handle form input changes\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value,\n    });\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Set submitting state\n    setFormStatus({\n      submitting: true,\n      success: false,\n      error: false,\n      message: \"\",\n    });\n\n    try {\n      // Use environment-based URL (same pattern as other API calls)\n      const isDevelopment = window.location.hostname === \"localhost\";\n      const endpointUrl = isDevelopment\n        ? \"http://localhost:4004/api/contact\" // Updated to use the main contact route\n        : \"https://devskills.ee/api/contact\"; // Updated to use the main contact route\n      const apiKey = \"9afe34d2134b43e19163c50924df6714\";\n\n      // Send the form data to the endpoint\n      await axios.post(endpointUrl, formData, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"X-API-Key\": apiKey,\n        },\n      });\n\n      // Track successful form submission\n      trackContactFormSubmission(formData.email, formData.message.length);\n      trackFormSubmission(\"Contact Form\", window.location.pathname, true);\n\n      // Handle success\n      setFormStatus({\n        submitting: false,\n        success: true,\n        error: false,\n        message:\n          t(\"contact.success\") ||\n          \"Thank you! Your message has been sent successfully.\",\n      });\n\n      // Reset form\n      setFormData({\n        name: \"\",\n        email: \"\",\n        message: \"\",\n      });\n    } catch (error) {\n      console.error(\"Contact form error:\", error);\n\n      // Track failed form submission\n      trackFormSubmission(\"Contact Form\", window.location.pathname, false);\n\n      // Handle different types of errors\n      let errorMessage =\n        t(\"contact.error\") ||\n        \"Sorry, there was an error sending your message. Please try again.\";\n\n      if (error.response?.status === 429) {\n        errorMessage =\n          t(\"contact.rateLimit\") ||\n          \"Too many requests. Please wait a moment before trying again.\";\n      } else if (error.response?.status === 400) {\n        errorMessage =\n          t(\"contact.validation\") || \"Please check your input and try again.\";\n      }\n\n      setFormStatus({\n        submitting: false,\n        success: false,\n        error: true,\n        message: errorMessage,\n      });\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <div className=\"row mt-n10 mb-60 mb-xs-40\">\n        <div className=\"col-md-10 offset-md-1\">\n          <div className=\"row\">\n            {/* Phone */}\n            {contactItems.map((item, index) => (\n              <React.Fragment key={index}>\n                <div className={`col-md-6 col-lg-4 mb-md-30 `}>\n                  <div className=\"contact-item wow fadeScaleIn\">\n                    <div className=\"ci-icon\">\n                      <i className={item.iconClass} />\n                    </div>\n                    <h4 className=\"ci-title\">{item.title}</h4>\n                    <div className=\"ci-text large\">{item.text}</div>\n                    <div className=\"ci-link\">\n                      <a\n                        href={item.link.url}\n                        target={item.link.target}\n                        rel={item.link.rel}\n                      >\n                        {item.link.text}\n                      </a>\n                    </div>{\" \"}\n                  </div>\n                </div>{\" \"}\n              </React.Fragment>\n            ))}\n\n            {/* End Email */}\n          </div>\n        </div>\n      </div>\n      {/* Contact Form */}\n      <div className=\"row\">\n        <div className=\"col-md-10 offset-md-1\">\n          <form\n            onSubmit={handleSubmit}\n            className=\"form contact-form wow fadeInUp wch-unset\"\n            data-wow-delay=\".5s\"\n            id=\"contact_form\"\n          >\n            <div className=\"row\">\n              <div className=\"col-md-6\">\n                {/* Name */}\n                <div className=\"form-group\">\n                  <label htmlFor=\"name\">{t(\"contact.name\")}</label>\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    id=\"name\"\n                    className=\"input-lg round form-control\"\n                    placeholder={t(\"contact.name.placeholder\")}\n                    pattern=\".{3,100}\"\n                    required\n                    aria-required=\"true\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n              <div className=\"col-md-6\">\n                {/* Email */}\n                <div className=\"form-group\">\n                  <label htmlFor=\"email\">{t(\"contact.email\")}</label>\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    id=\"email\"\n                    className=\"input-lg round form-control\"\n                    placeholder={t(\"contact.email.placeholder\")}\n                    pattern=\".{5,100}\"\n                    required\n                    aria-required=\"true\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                  />\n                </div>\n              </div>\n            </div>\n            {/* Message */}\n            <div className=\"form-group\">\n              <label htmlFor=\"message\">{t(\"contact.message\")}</label>\n              <textarea\n                name=\"message\"\n                id=\"message\"\n                className=\"input-lg round form-control\"\n                style={{ height: 200 }}\n                placeholder={t(\"contact.message.placeholder\")}\n                value={formData.message}\n                onChange={handleInputChange}\n                minLength={5}\n                maxLength={5000}\n                required\n              />\n              <div className=\"form-text text-muted small\">\n                {formData.message.length}/5000 characters\n              </div>\n            </div>\n            <div className=\"row\">\n              <div className=\"col-sm-6\">\n                {/* Inform Tip */}\n                <div className=\"form-tip pt-20 pt-sm-0\">\n                  <i className=\"icon-info size-16\" />\n                  {t(\"contact.terms\")}\n                </div>\n              </div>\n              <div className=\"col-sm-6\">\n                {/* Send Button */}\n                <div className=\"text-end pt-10\">\n                  <button\n                    type=\"submit\"\n                    id=\"submit_btn\"\n                    aria-controls=\"result\"\n                    className=\"submit_btn link-hover-anim link-circle-1 align-middle\"\n                    data-link-animate=\"y\"\n                    disabled={formStatus.submitting}\n                  >\n                    <span className=\"link-strong link-strong-unhovered\">\n                      {formStatus.submitting ? \"Sending...\" : t(\"contact.send\")}\n                      <i\n                        className=\"mi-arrow-right size-18 align-middle\"\n                        aria-hidden=\"true\"\n                      ></i>\n                    </span>\n                    <span\n                      className=\"link-strong link-strong-hovered\"\n                      aria-hidden=\"true\"\n                    >\n                      {formStatus.submitting ? \"Sending...\" : t(\"contact.send\")}\n                      <i\n                        className=\"mi-arrow-right size-18 align-middle\"\n                        aria-hidden=\"true\"\n                      ></i>\n                    </span>\n                  </button>\n                </div>\n              </div>\n            </div>\n            <div\n              id=\"result\"\n              role=\"region\"\n              aria-live=\"polite\"\n              aria-atomic=\"true\"\n              className={`mt-4 p-3 rounded ${\n                formStatus.success\n                  ? \"bg-success-100 text-success-700\"\n                  : formStatus.error\n                  ? \"bg-danger-100 text-danger-700\"\n                  : \"\"\n              }`}\n              style={{ display: formStatus.message ? \"block\" : \"none\" }}\n            >\n              {formStatus.message}\n            </div>\n          </form>\n        </div>\n      </div>\n      {/* End Contact Form */}\n    </div>\n  );\n}\n", "import React from \"react\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function MarqueeDark() {\n  const { t } = useTranslation();\n  return (\n    <>\n      <div className=\"marquee marquee-style-1 bg-dark-2 mb-30\">\n        <div className=\"marquee-track marquee-animation\">\n          <div>{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.development6\")}</div>\n        </div>\n      </div>\n      {/* End Marquee Text Line */}\n      {/* Marquee Text Line */}\n      <div className=\"marquee marquee-style-1 bg-dark-2\">\n        <div className=\"marquee-track marquee-animation\">\n          <div>{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values1\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values2\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values3\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values4\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values5\")}</div>\n          <div aria-hidden=\"true\">{t(\"marquee.values6\")}</div>\n        </div>\n      </div>\n    </>\n  );\n}\n", "// client\\src\\components\\home\\index.jsx\n\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport About from \"./About\";\nimport { useTranslation } from \"react-i18next\";\nimport { usePageAnalytics } from \"@/hooks/usePageAnalytics\";\n\n// Keeping imports for potential future use\n// import Team from \"./Team\";\nimport Service from \"./Service\";\n// import Portfolio from \"./Portfolio\";\nimport DevskillsBMS from \"./DevskillsBMS\";\n\nimport Blog from \"./Blog\";\n// import NewsLetter from \"./NewsLetter\";\nimport Contact from \"./Contact\";\nimport { Link } from \"react-router-dom\";\nimport MarqueeDark from \"./MarqueeDark\";\n\nexport default function Home({ onePage = false, dark = false }) {\n  const { t } = useTranslation();\n\n  // Add page analytics tracking\n  usePageAnalytics(\"DevSkills - Business Management Solutions\");\n  return (\n    <>\n      <section\n        className={`page-section  scrollSpysection pb-40 ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"about\"\n      >\n        <div className=\"container position-relative\">\n          <div className=\"row\">\n            <div className=\"col-lg-5 d-flex align-items-center mb-md-50\">\n              <div>\n                <div className=\"wow linesAnimIn\" data-splitting=\"lines\">\n                  <h2 className=\"section-title mb-30 mb-sm-20\">\n                    <span className=\"text-gray\">{t(\"menu.about\")}</span>{\" \"}\n                    DEVSKILLS STUDIO\n                    <span className=\"text-gray\">.</span>\n                  </h2>\n                  <div className=\"text-gray mb-30 mb-sm-20\">\n                    <p className=\"mb-0\">{t(\"home.about.description\")}</p>\n                  </div>\n                </div>\n                <div className=\"local-scroll wow fadeInUpShort wch-unset\">\n                  {onePage ? (\n                    <>\n                      {\" \"}\n                      <a\n                        href=\"/about\"\n                        className=\"link-hover-anim link-circle-1 align-middle\"\n                        data-link-animate=\"y\"\n                      >\n                        <span className=\"link-strong link-strong-unhovered\">\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                        <span\n                          className=\"link-strong link-strong-hovered\"\n                          aria-hidden=\"true\"\n                        >\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                      </a>\n                    </>\n                  ) : (\n                    <>\n                      {\" \"}\n                      <Link\n                        to={`/about`}\n                        className=\"link-hover-anim link-circle-1 align-middle\"\n                        data-link-animate=\"y\"\n                      >\n                        <span className=\"link-strong link-strong-unhovered\">\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                        <span\n                          className=\"link-strong link-strong-hovered\"\n                          aria-hidden=\"true\"\n                        >\n                          {t(\"home.about.learnMore\")}{\" \"}\n                          <span className=\"visually-hidden\">\n                            {t(\"home.about.visually-hidden\")}\n                          </span>{\" \"}\n                          <i\n                            className=\"mi-arrow-right size-18 align-middle\"\n                            aria-hidden=\"true\"\n                          ></i>\n                        </span>\n                      </Link>\n                    </>\n                  )}\n                </div>\n              </div>\n            </div>\n            <About />\n          </div>\n        </div>\n      </section>\n\n      <MarqueeDark />\n\n      <section\n        className={`page-section px-4 sm:px-0 pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content scrollSpysection ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        }`}\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n        id=\"bms\"\n      >\n        <DevskillsBMS />\n      </section>\n\n      <div className=\"page-section overflow-hidden\">\n        <MarqueeDark />\n      </div>\n\n      <section\n        className=\"page-section pt-0 pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content\"\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container position-relative\">\n          <div className=\"row\">\n            <div className=\"col-md-6 col-xl-5\">\n              <div className=\"call-action-1-images pb-60 pb-md-0 mt-n30 mt-md-70 mb-n30 mb-md-70 mb-sm-0\">\n                <div className=\"call-action-1-image-1 round grayscale\">\n                  <img\n                    src=\"/assets/images/demo-elegant/code2.jpg\"\n                    width={678}\n                    height={840}\n                    alt=\"Image Description\"\n                  />\n                </div>\n                <div className=\"call-action-1-image-2\">\n                  <div\n                    className=\"call-action-1-image-2-inner\"\n                    data-rellax-y=\"\"\n                    data-rellax-speed=\"0.7\"\n                    data-rellax-percentage=\"0.427\"\n                  >\n                    <img\n                      src=\"/assets/images/demo-elegant/code3.jpg\"\n                      alt=\"Image Description\"\n                      width={300}\n                      height={409}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"col-md-6 offset-xl-1 d-flex align-items-center\">\n              <div className=\"row small-section\">\n                <div className=\"col-xl-11\">\n                  <h2 className=\"section-title mb-30 mb-sm-20\">\n                    {t(\"home.services.title\")}\n                  </h2>\n                  <div className=\"text-gray mb-30 mb-sm-20\">\n                    <p className=\"mb-0\">{t(\"home.services.description\")}</p>\n                  </div>\n                  <div className=\"local-scroll\">\n                    {onePage ? (\n                      <>\n                        {\" \"}\n                        <a\n                          href=\"/services\"\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                        </a>\n                      </>\n                    ) : (\n                      <>\n                        {\" \"}\n                        <Link\n                          to={`/services`}\n                          className=\"link-hover-anim link-circle-1 align-middle\"\n                          data-link-animate=\"y\"\n                        >\n                          <span className=\"link-strong link-strong-unhovered\">\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                          <span\n                            className=\"link-strong link-strong-hovered\"\n                            aria-hidden=\"true\"\n                          >\n                            {t(\"home.services.viewServices\")}{\" \"}\n                            <i\n                              className=\"mi-arrow-right size-18 align-middle\"\n                              aria-hidden=\"true\"\n                            ></i>\n                          </span>\n                        </Link>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      {/* Commenting out Team section */}\n\n      {/* <section\n        className={`page-section pb-0  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"team\"\n      >\n        <Team />\n      </section> */}\n\n      <div className=\"page-section overflow-hidden\">\n        <MarqueeDark />\n      </div>\n      <section\n        className={`page-section pt-0  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"services\"\n      >\n        <Service />\n      </section>\n      <hr className=\"mt-0 mb-0\" />\n\n      {/* Commenting out Portfolio section */}\n      {/*\n      <section\n        className={`page-section pb-0  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"portfolio\"\n      >\n        <div className=\"container\">\n          <div className=\"row mb-70 mb-sm-50\">\n            <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n              <h2 className=\"section-title mb-30 mb-sm-20\">\n                <span className=\"text-gray\">Our</span> Portfolio\n                <span className=\"text-gray\">.</span>\n              </h2>\n              <div className=\"text-gray\">\n                The action centric perspective is a label given to a collection\n                of concepts, which are antithetical to the rational model.\n              </div>\n            </div>\n          </div>\n        </div>\n        <Portfolio />\n      </section>\n      */}\n      <section\n        className={`small-section ${\n          dark ? \"bg-dark-2 light-content\" : \"bg-dark-1 light-content\"\n        } bg-dark-alpha-80 parallax-6`}\n        style={{\n          backgroundImage: \"url(/assets/images/demo-elegant/5.jpg)\",\n        }}\n      >\n        <div className=\"container\">\n          <div className=\"row mb-n10\">\n            <div className=\"col-md-6 offset-md-1 col-lg-5 offset-lg-2 mb-sm-30 text-center text-md-start\">\n              <h2 className=\"section-title-small mb-0 opacity-08\">\n                {t(\"home.cta.title\")}\n              </h2>\n            </div>\n            <div className=\"col-md-4 col-lg-3 text-center text-md-end\">\n              <div className=\"mt-n20\">\n                {onePage ? (\n                  <>\n                    {\" \"}\n                    <a\n                      href=\"/contact\"\n                      className=\"link-hover-anim link-circle-1 align-middle\"\n                      data-link-animate=\"y\"\n                    >\n                      <span className=\"link-strong link-strong-unhovered\">\n                        {t(\"home.cta.button\")}\n                      </span>\n                      <span\n                        className=\"link-strong link-strong-hovered\"\n                        aria-hidden=\"true\"\n                      >\n                        {t(\"home.cta.button\")}\n                      </span>\n                    </a>\n                  </>\n                ) : (\n                  <>\n                    {\" \"}\n                    <Link\n                      to={`/contact`}\n                      className=\"link-hover-anim link-circle-1 align-middle\"\n                      data-link-animate=\"y\"\n                    >\n                      <span className=\"link-strong link-strong-unhovered\">\n                        {t(\"home.cta.button\")}\n                      </span>\n                      <span\n                        className=\"link-strong link-strong-hovered\"\n                        aria-hidden=\"true\"\n                      >\n                        {t(\"home.cta.button\")}\n                      </span>\n                    </Link>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n      {/* Commenting out Newsletter section */}\n      {/*\n      <section\n        className={`small-section pb-0 ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n      >\n        <NewsLetter />\n      </section>\n      */}\n\n      {/* Blog Section */}\n      <section\n        className={`page-section scrollSpysection ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        }`}\n        id=\"blog\"\n      >\n        <Blog />\n      </section>\n\n      <section\n        className={`page-section  scrollSpysection  ${\n          dark ? \"bg-dark-1 light-content\" : \"\"\n        } `}\n        id=\"contact\"\n      >\n        <div className=\"container\">\n          <div className=\"row mb-70 mb-sm-50\">\n            <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n              <h2 className=\"section-title mb-30 mb-sm-20\">\n                <span className=\"text-gray\">{t(\"home.contact.title\")}</span>\n                <span className=\"text-gray\">.</span>\n              </h2>\n            </div>\n          </div>\n        </div>\n        <Contact />\n      </section>\n    </>\n  );\n}\n\nHome.propTypes = {\n  onePage: PropTypes.bool,\n  dark: PropTypes.bool,\n};\n", "import AnimatedText from \"@/components/common/AnimatedText\";\nimport React from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { trackButtonClick } from \"@/utils/analytics\";\n\nexport default function Hero() {\n  const { t } = useTranslation();\n\n  const handleDiscoverClick = () => {\n    trackButtonClick(\"Discover Now\", \"Hero Section\", {\n      button_type: \"cta\",\n      section: \"hero\",\n    });\n  };\n  return (\n    <div className=\"container min-height-100vh d-flex align-items-center pt-100 pb-100 pt-sm-120 pb-sm-120\">\n      {/* Home Section Content */}\n      <div className=\"home-content text-center\">\n        <h2 className=\"section-title-tiny mb-50 mb-sm-30 wow fadeInDownShort\">\n          {t(\"hero.welcome\")}\n        </h2>\n        <h1 className=\"hs-title-3 mb-120 mb-sm-80 mb-xs-140\">\n          <span className=\"wow charsAnimInLong\" data-splitting=\"chars\">\n            <AnimatedText text={t(\"hero.studio\")} />\n          </span>\n        </h1>\n        <div className=\"local-scroll wow fadeInUpShort\" data-wow-delay=\"0.57s\">\n          <a\n            href=\"#about\"\n            className=\"link-hover-anim link-circle-1 align-middle\"\n            data-link-animate=\"y\"\n            onClick={handleDiscoverClick}\n          >\n            <span className=\"link-strong link-strong-unhovered\">\n              {t(\"hero.discover\")}{\" \"}\n              <i\n                className=\"mi-arrow-right size-18 align-middle\"\n                aria-hidden=\"true\"\n              ></i>\n            </span>\n            <span\n              className=\"link-strong link-strong-hovered\"\n              aria-hidden=\"true\"\n            >\n              {t(\"hero.discover\")}{\" \"}\n              <i\n                className=\"mi-arrow-right size-18 align-middle\"\n                aria-hidden=\"true\"\n              ></i>\n            </span>\n          </a>\n        </div>\n      </div>\n      {/* End Home Section Content */}\n      {/* Scroll Down */}\n      <div\n        className=\"local-scroll scroll-down-3-wrap wow fadeInUp\"\n        data-wow-offset={0}\n      >\n        <a href=\"#about\" className=\"scroll-down-3\">\n          {t(\"hero.scrollDown\") || \"Scroll Down\"}\n        </a>\n      </div>\n      {/* End Scroll Down */}\n    </div>\n  );\n}\n", "export const teamMembers2 = [\n  {\n    name: \"<PERSON><PERSON>\",\n    role: \"Founder & CEO\",\n    image: \"/assets/img/timo.jpg\",\n    socials: [\n      {\n        name: \"LinkedIn\",\n        url: \"https://www.linkedin.com/in/timo-lambing-178a49153\",\n      },\n      { name: \"Gith<PERSON>\", url: \"https://github.com/TimoLambing\" },\n    ],\n  },\n  {\n    name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    role: \"Chief of Marketing\",\n    image: \"/assets/img/ann-kristiin.jpg\",\n    socials: [\n      {\n        name: \"LinkedIn\",\n        url: \"https://www.linkedin.com/in/ann-kristiin-reimann-349506216/\",\n      },\n      { name: \"Instagram\", url: \"https://www.instagram.com/ak47kristiin/\" },\n    ],\n  },\n];\n", "import React from \"react\";\nimport { teamMembers2 } from \"@/data/team\";\nimport { useTranslation } from \"react-i18next\";\n\nexport default function Team() {\n  const { t } = useTranslation();\n  return (\n    <div className=\"container\">\n      <div className=\"row mb-70 mb-sm-50\">\n        <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center\">\n          <h2 className=\"section-title mb-30 mb-sm-20\">\n            <span className=\"text-gray\">\n              {t(\"about.team.title\").split(\" \")[0]}\n            </span>{\" \"}\n            {t(\"about.team.title\").split(\" \")[1]}\n            <span className=\"text-gray\">.</span>\n          </h2>\n          <div className=\"text-gray\">{t(\"about.team.description\")}</div>\n        </div>\n      </div>\n      <div className=\"row justify-content-center mt-n40\">\n        {/* Team item */}\n        {teamMembers2.map((member, index) => (\n          <div key={index} className=\"col-md-5 col-lg-4 mt-40 mx-md-4\">\n            <div className=\"team-item\">\n              <div\n                className={`team-item-image ${\n                  member.name === \"Timo Lambing\" ? \"timo-image-container\" : \"\"\n                }`}\n              >\n                <img\n                  src={member.image}\n                  width={625}\n                  height={767}\n                  className=\"wow scaleOutIn grayscale-effect team-image\"\n                  data-wow-duration=\"1.2s\"\n                  alt={`Image of ${member.name}`}\n                  style={{ objectFit: \"cover\", width: \"100%\", height: \"100%\" }}\n                />\n                <div className=\"team-item-detail\">\n                  <div className=\"team-social-links\">\n                    {member.socials.map((social, idx) => (\n                      <a\n                        key={idx}\n                        href={social.url}\n                        target=\"_blank\"\n                        rel=\"noreferrer nofollow\"\n                      >\n                        <div className=\"visually-hidden\">{social.name}</div>\n                        <i className={`fa-${social.name.toLowerCase()}`} />\n                      </a>\n                    ))}\n                  </div>\n                </div>\n              </div>\n              <div className=\"team-item-descr\">\n                <div className=\"team-item-name\">{member.name}</div>\n                <div className=\"team-item-role\">{member.role}</div>\n              </div>\n            </div>\n          </div>\n        ))}\n        {/* End Team item */}\n      </div>\n    </div>\n  );\n}\n", "import { portfolios5 } from \"@/data/portfolio\";\nimport React, { useEffect, useState } from \"react\";\n\nimport { Link } from \"react-router-dom\";\nimport { Gallery, Item } from \"react-photoswipe-gallery\";\nconst filters = [\n  { name: \"All works\", category: \"all\" },\n  { name: \"Branding\", category: \"branding\" },\n  { name: \"Design\", category: \"design\" },\n  { name: \"Development\", category: \"development\" },\n];\nexport default function Portfolio() {\n  const [currentCategory, setCurrentCategory] = useState(\"all\");\n  const [filtered, setFiltered] = useState(portfolios5);\n  useEffect(() => {\n    if (currentCategory == \"all\") {\n      setFiltered(portfolios5);\n    } else {\n      setFiltered(\n        [...portfolios5].filter((elm) =>\n          elm.categories.includes(currentCategory)\n        )\n      );\n    }\n  }, [currentCategory]);\n  return (\n    <>\n      <div className=\"container\">\n        {/* Works Filter */}\n        <div className=\"works-filter works-filter-elegant text-center mb-50\">\n          {filters.map((elm, i) => (\n            <a\n              onClick={() => setCurrentCategory(elm.category)}\n              key={i}\n              className={`filter ${currentCategory == elm.category ? \"active\" : \"\"\n                }`}\n            >\n              {elm.name}\n            </a>\n          ))}\n        </div>\n        {/* End Works Filter */}\n      </div>\n      <div className=\"position-relative\">\n        {/* Works Grid */}\n        <ul\n          className=\"works-grid work-grid-4 work-grid-gut-sm hide-titles\"\n          id=\"work-grid\"\n        >\n          <Gallery>\n            {/* Work Item (Lightbox) */}\n            {filtered.map((item, index) => (\n              <li\n                key={index}\n                className={`work-item mix ${item.categories.join(\" \")}`}\n              >\n                {item.type === \"Lightbox\" ? (\n                  <Item\n                    original={item.imageSrc}\n                    thumbnail={item.imageSrc}\n                    width={650}\n                    height={773}\n                  >\n                    {({ ref, open }) => (\n                      <a\n                        onClick={open}\n                        className=\"work-lightbox-link mfp-image\"\n                      >\n                        <div className=\"work-img\">\n                          <div className=\"work-img-bg wow-p scalexIn\" />\n\n                          <img\n                            src={item.imageSrc}\n                            ref={ref}\n                            width={650}\n                            height={761}\n                            alt=\"Work Description\"\n                          />\n                        </div>\n                        <div className=\"work-intro\">\n                          <h3 className=\"work-title\">{item.title}</h3>\n                          <div className=\"work-descr\">{item.type}</div>\n                        </div>\n                      </a>\n                    )}\n                  </Item>\n                ) : (\n                  <Link\n                    to={`/portfolio-single/${item.id}`}\n                    className=\"work-ext-link\"\n                  >\n                    <div className=\"work-img\">\n                      <div className=\"work-img-bg\" />\n                      <img\n                        src={item.imageSrc}\n                        width={650}\n                        height={761}\n                        alt=\"Work Description\"\n                      />\n                    </div>\n                    <div className=\"work-intro\">\n                      <h3 className=\"work-title\">{item.title}</h3>\n                      <div className=\"work-descr\">{item.type}</div>\n                    </div>\n                  </Link>\n                )}\n              </li>\n            ))}{\" \"}\n          </Gallery>\n          {/* End Work Item */}\n        </ul>\n        {/* End Works Grid */}\n      </div>\n    </>\n  );\n}\n"], "names": ["extractLanguageFromPath", "path", "match", "getPageType", "getApiBaseUrl", "trackCustomEvent", "eventName", "eventCategory", "eventLabel", "eventValue", "metadata", "language", "blogPostId", "blogPostMatch", "apiBaseUrl", "error", "trackPageView", "page_title", "page_location", "trackFn", "trackButtonClick", "button_name", "button_location", "additional_params", "trackFormSubmission", "form_name", "form_location", "success", "trackContactFormSubmission", "email", "message_length", "trackScrollDepth", "scroll_percentage", "trackTimeOnPage", "time_seconds", "trackComanagerConversion", "source_location", "trackEvent", "eventData", "About", "t", "useTranslation", "jsxs", "jsx", "usePageAnalytics", "pageTitle", "location", "useLocation", "startTimeRef", "useRef", "scrollDepthRef", "scrollMilestonesRef", "useEffect", "fullPageTitle", "pageLocation", "handleScroll", "scrollTop", "documentHeight", "scrollPercentage", "milestone", "handleBeforeUnload", "timeOnPage", "handleVisibilityChange", "category", "label", "value", "additionalParams", "services6", "Service", "Fragment", "elm", "bmsItems", "DevskillsBMS", "translate", "i18n", "currentLanguage", "currentCategory", "setCurrentCategory", "useState", "filtered", "setFiltered", "activeModule", "setActiveModule", "moduleKeys", "setModuleKeys", "item", "handleModuleClick", "moduleCategory", "handleCTAClick", "handleLinkBoxClick", "e", "Gallery", "index", "<PERSON><PERSON>", "open", "Blog", "blogPosts", "setBlogPosts", "loading", "setLoading", "setError", "result", "blogAPI", "posts", "_a", "getTranslation", "post", "field", "translation", "_c", "_b", "Link", "useContactItems", "Contact", "contactItems", "formData", "setFormData", "formStatus", "setFormStatus", "handleInputChange", "name", "handleSubmit", "endpointUrl", "axios", "errorMessage", "React", "MarqueeDark", "Home", "onePage", "dark", "PropTypes", "Hero", "handleDiscoverClick", "AnimatedText", "teamMembers2", "Team", "member", "social", "idx", "filters", "Portfolio", "portfolios5", "i", "ref"], "mappings": "+PAGA,MAAMA,EAA2BC,GAAS,CAClC,MAAAC,EAAQD,EAAK,MAAM,gBAAgB,EAClC,OAAAC,EAAQA,EAAM,CAAC,EAAI,IAC5B,EAGMC,EAAeF,GACfA,EAAK,SAAS,eAAe,EAAU,YACvCA,EAAK,SAAS,OAAO,EAAU,YAC/BA,EAAK,SAAS,YAAY,EAAU,UACpCA,EAAK,SAAS,QAAQ,EAAU,QAChCA,EAAK,SAAS,UAAU,EAAU,UAClCA,EAAK,SAAS,WAAW,EAAU,WACnCA,EAAK,SAAS,YAAY,EAAU,YACjC,OAKHG,EAAgB,IAGX,GAAG,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,IAAI,GAOlDC,EAAmB,MAC9BC,EACAC,EACAC,EACAC,EAAa,KACbC,EAAW,KACR,CACC,GAAA,CACF,MAAMC,EAAWX,EAAwB,OAAO,SAAS,QAAQ,EAC3DC,EAAO,OAAO,SAAS,SAG7B,IAAIW,EAAa,KACX,MAAAC,EAAgBZ,EAAK,MAAM,yBAAyB,EACtDY,IAGWD,EAFAC,EAAc,CAAC,GAK9B,MAAMC,EAAaV,EAAc,EAC3B,MAAA,MAAM,GAAGU,CAAU,uBAAwB,CAC/C,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CACnB,UAAAR,EACA,cAAAC,EACA,WAAAC,EACA,WAAAC,EACA,KAAAR,EACA,SAAAU,EACA,WAAAC,EACA,SAAU,CACR,GAAGF,EACH,UAAW,IAAI,KAAK,EAAE,YAAY,EAClC,UAAW,UAAU,UACrB,SAAU,SAAS,QAAA,CAEtB,CAAA,CAAA,CACF,QACMK,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,CAAA,CAEvD,EAGaC,EAAgB,CAACC,EAAYC,IAAkB,CAE1D,MAAMC,EAAU,IAAM,CACpB,GAAI,OAAO,OAAW,KAAe,OAAO,KAAM,CAC1C,MAAAR,EAAWX,EAAwBkB,CAAa,EAE/C,OAAA,KAAK,QAAS,YAAa,CAChC,WAAAD,EACA,cAAAC,EACA,SAAAP,EACA,UAAWR,EAAYe,CAAa,EACpC,QAAS,cAAA,CACV,EAGgBb,EAAA,YAAa,aAAcY,EAAY,KAAM,CAC5D,SAAAN,EACA,UAAWR,EAAYe,CAAa,CAAA,CACrC,CAAA,CAEL,EAEI,OAAO,OAAW,KAAe,OAAO,oBAC1C,OAAO,oBAAoBC,CAAO,EAElC,WAAWA,EAAS,CAAC,CAEzB,EAGaC,EAAmB,CAC9BC,EACAC,EACAC,EAAoB,CAAA,IACjB,CACH,GAAI,OAAO,OAAW,KAAe,OAAO,KAAM,CAChD,MAAMZ,EAAWX,EAAwB,OAAO,SAAS,QAAQ,EAE1D,OAAA,KAAK,QAAS,QAAS,CAC5B,eAAgB,SAChB,YAAaqB,EACb,gBAAAC,EACA,SAAAX,EACA,UAAWR,EAAY,OAAO,SAAS,QAAQ,EAC/C,GAAGoB,EACH,QAAS,cAAA,CACV,EAGgBlB,EAAA,QAAS,SAAUgB,EAAa,KAAM,CACrD,gBAAAC,EACA,SAAAX,EACA,UAAWR,EAAY,OAAO,SAAS,QAAQ,EAC/C,GAAGoB,CAAA,CACJ,CAAA,CAEL,EAGaC,EAAsB,CACjCC,EACAC,EACAC,EAAU,KACP,CACC,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAASA,EAAU,cAAgB,aAAc,CAC3D,eAAgB,OAChB,YAAaF,EACb,cAAAC,EACA,QAAAC,EACA,QAAS,cAAA,CACV,CAEL,EAGaC,EAA6B,CAACC,EAAOC,IAAmB,CAC/D,OAAO,OAAW,KAAe,OAAO,OAEnC,OAAA,KAAK,QAAS,aAAc,CACjC,QAAS,eACT,eAAgB,UAChB,YAAa,0BACb,MAAO,EACP,SAAU,MACV,WAAYD,EACZ,eAAAC,CAAA,CACD,EAGM,OAAA,KAAK,QAAS,gBAAiB,CACpC,QAAS,eACT,eAAgB,kBAChB,YAAa,eACb,MAAO,EACP,SAAU,KAAA,CACX,EAEL,EA4BaC,EAAmB,CAACC,EAAmBd,IAAkB,CAChE,OAAO,OAAW,KAAe,OAAO,MACnC,OAAA,KAAK,QAAS,SAAU,CAC7B,eAAgB,kBAChB,YAAa,GAAGc,CAAiB,aACjC,kBAAAA,EACA,cAAAd,EACA,QAAS,cAAA,CACV,CAEL,EAGae,EAAkB,CAACC,EAAchB,IAAkB,CAC1D,OAAO,OAAW,KAAe,OAAO,OACnC,OAAA,KAAK,QAAS,kBAAmB,CACtC,KAAM,iBACN,MAAOgB,EACP,eAAgB,kBAChB,YAAahB,EACb,QAAS,cAAA,CACV,EAGDb,EACE,kBACA,kBACAa,EACAgB,EACA,CACE,eAAgBA,CAAA,CAEpB,EAEJ,EA0CaC,EAA2B,CACtCC,EACAb,EAAoB,KACjB,CACH,GAAI,OAAO,OAAW,KAAe,OAAO,KAAM,CAChD,MAAMZ,EAAWX,EAAwB,OAAO,SAAS,QAAQ,EAG1D,OAAA,KAAK,QAAS,uBAAwB,CAC3C,eAAgB,aAChB,YAAa,yBACb,gBAAAoC,EACA,SAAAzB,EACA,UAAWR,EAAY,OAAO,SAAS,QAAQ,EAC/C,MAAO,IACP,SAAU,MACV,GAAGoB,EACH,QAAS,cAAA,CACV,EAGM,OAAA,KAAK,QAAS,WAAY,CAC/B,eAAgB,aAAa,KAAK,IAAK,CAAA,GACvC,MAAO,IACP,SAAU,MACV,MAAO,CACL,CACE,QAAS,gBACT,UAAW,+BACX,cAAe,aACf,aAAca,EACd,SAAU,EACV,MAAO,GAAA,CAEX,EACA,gBAAAA,EACA,SAAAzB,EACA,QAAS,cAAA,CACV,EAGDN,EACE,uBACA,aACA+B,EACA,IACA,CACE,SAAAzB,EACA,UAAWR,EAAY,OAAO,SAAS,QAAQ,EAC/C,gBAAiB,gBACjB,GAAGoB,CAAA,CAEP,CAAA,CAEJ,EA0Cac,GAAa,CAAC/B,EAAWgC,EAAY,KAAO,CACvD,GAAI,OAAO,OAAW,KAAe,OAAO,KAAM,CAChD,MAAM3B,EAAWX,EAAwB,OAAO,SAAS,QAAQ,EAE1D,OAAA,KAAK,QAASM,EAAW,CAC9B,eAAgBgC,EAAU,UAAY,mBACtC,YAAaA,EAAU,OAAShC,EAChC,SAAAK,EACA,UAAWR,EAAY,OAAO,SAAS,QAAQ,EAC/C,GAAGmC,EACH,QAAS,cAAA,CACV,EAGDjC,EACEC,EACAgC,EAAU,UAAY,mBACtBA,EAAU,OAAShC,EACnBgC,EAAU,OAAS,KACnB,CACE,SAAA3B,EACA,UAAWR,EAAY,OAAO,SAAS,QAAQ,EAC/C,GAAGmC,CAAA,CAEP,CAAA,CAEJ,ECjZA,SAAwBC,GAAQ,CACxB,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EAC7B,aACG,MAAI,CAAA,UAAU,uBACb,SAACC,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAU,kCACV,gBAAc,GACd,oBAAkB,OAClB,yBAAuB,MAEvB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,8BAA+B,CAAA,EAC9CA,EAAAA,IAAC,MAAI,CAAA,UAAU,wBAEf,CAAA,CAAA,EACF,EACAA,EAAA,IAAC,MAAA,CACC,IAAI,oBACJ,MAAO,IACP,OAAQ,IACR,UAAU,wBACV,IAAI,mBAAA,CAAA,CACN,CAAA,CACF,EACAD,EAAA,KAAC,MAAA,CACC,UAAU,WACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,MAEvB,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,IAAI,oBACJ,MAAO,IACP,OAAQ,IACR,UAAU,wBACV,IAAI,mBAAA,CACN,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gCAAiC,CAAA,EAChDA,EAAAA,IAAC,MAAI,CAAA,UAAU,eAEf,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAEJ,CC/CO,MAAMC,EAAoBC,GAAc,CAC7C,MAAMC,EAAWC,EAAa,EACxBC,EAAeC,EAAM,OAAC,IAAI,EAC1BC,EAAiBD,EAAM,OAAC,CAAC,EACzBE,EAAsBF,EAAAA,OAAO,IAAI,GAAK,EAE5CG,OAAAA,EAAAA,UAAU,IAAM,CAEd,MAAMC,EAAgBR,EAChBS,EAAe,OAAO,SAAS,KAErCtC,EAAcqC,EAAeC,CAAY,EACzCN,EAAa,QAAU,KAAK,IAAK,EAGjCE,EAAe,QAAU,EACzBC,EAAoB,QAAQ,MAAO,EAGnC,MAAMI,EAAe,IAAM,CACzB,MAAMC,EAAY,OAAO,aAAe,SAAS,gBAAgB,UAC3DC,EAAiB,SAAS,gBAAgB,aAAe,OAAO,YAChEC,EAAmB,KAAK,MAAOF,EAAYC,EAAkB,GAAG,EAGlEC,EAAmBR,EAAe,UACpCA,EAAe,QAAUQ,GAIR,CAAC,GAAI,GAAI,GAAI,GAAI,GAAG,EAC5B,QAAQC,GAAa,CAC1BD,GAAoBC,GAAa,CAACR,EAAoB,QAAQ,IAAIQ,CAAS,IAC7ER,EAAoB,QAAQ,IAAIQ,CAAS,EACzC5B,EAAiB4B,EAAWL,CAAY,EAElD,CAAO,CACF,EAGD,OAAO,iBAAiB,SAAUC,EAAc,CAAE,QAAS,GAAM,EAGjE,MAAMK,EAAqB,IAAM,CAC/B,GAAIZ,EAAa,QAAS,CACxB,MAAMa,EAAa,KAAK,OAAO,KAAK,MAAQb,EAAa,SAAW,GAAI,EACxEf,EAAgB4B,EAAYP,CAAY,CAChD,CACK,EAGKQ,EAAyB,IAAM,CACnC,GAAI,SAAS,kBAAoB,UAAYd,EAAa,QAAS,CACjE,MAAMa,EAAa,KAAK,OAAO,KAAK,MAAQb,EAAa,SAAW,GAAI,EACxEf,EAAgB4B,EAAYP,CAAY,CAChD,CACK,EAED,cAAO,iBAAiB,eAAgBM,CAAkB,EAC1D,SAAS,iBAAiB,mBAAoBE,CAAsB,EAG7D,IAAM,CAMX,GALA,OAAO,oBAAoB,SAAUP,CAAY,EACjD,OAAO,oBAAoB,eAAgBK,CAAkB,EAC7D,SAAS,oBAAoB,mBAAoBE,CAAsB,EAGnEd,EAAa,QAAS,CACxB,MAAMa,EAAa,KAAK,OAAO,KAAK,MAAQb,EAAa,SAAW,GAAI,EACxEf,EAAgB4B,EAAYP,CAAY,CAChD,CACK,CACF,EAAE,CAACR,EAAS,SAAUD,CAAS,CAAC,EAG1B,CACL,iBAAkB,CAACvC,EAAWyD,EAAUC,EAAOC,EAAOC,IAAqB,CACrE,OAAO,OAAW,KAAe,OAAO,MAC1C,OAAO,KAAK,QAAS5D,EAAW,CAC9B,eAAgByD,EAChB,YAAaC,EACb,MAAOC,EACP,cAAe,OAAO,SAAS,KAC/B,GAAGC,EACH,QAAS,cACnB,CAAS,CAEJ,EACD,sBAAuB,IAAMhB,EAAe,QAC5C,cAAe,IAAMF,EAAa,QAAU,KAAK,OAAO,KAAK,IAAG,EAAKA,EAAa,SAAW,GAAI,EAAI,CACtG,CACH,EChGamB,EAAY,CACvB,CACE,MAAO,GACP,OAAQ,GACR,KAAM,mdACN,IAAK,MACL,MAAO,2BACR,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,0OACN,IAAK,SACL,MAAO,8BACR,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,oJACN,IAAK,UACL,MAAO,wBACR,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,gLACN,IAAK,KACL,MAAO,wBACR,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,6JACN,IAAK,aACL,MAAO,4BACR,EACD,CACE,MAAO,GACP,OAAQ,GACR,KAAM,2RACN,IAAK,MACL,MAAO,mCACR,CACH,ECvCA,SAAwBC,GAAU,CAC1B,KAAA,CAAE,EAAA5B,CAAE,EAAIC,EAAe,EAC7B,OAEIC,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAA1B,EAAA,IAAC,MAAA,CACC,UAAU,0EACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,wDACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,+BACX,SAAAH,EAAE,gBAAgB,EACrB,QACC,MAAI,CAAA,UAAU,YAAa,SAAAA,EAAE,sBAAsB,CAAE,CAAA,CAAA,CACxD,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,EACCG,EAAA,IAAA,MAAA,CAAI,UAAU,gDACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,aACZ,SAAUwB,EAAA,IAAI,CAACG,EAAK,IACnB3B,EAAA,IAAC,MAAA,CAEC,UAAU,qDAEV,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAK2B,EAAI,MACT,MAAO,IACP,OAAQ,IACR,IAAK9B,EAAE,YAAY8B,EAAI,GAAG,QAAQ,EAClC,UAAU,oBAAA,CAAA,EAEd,EACA5B,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,qBACX,SAAAH,EAAE,YAAY8B,EAAI,GAAG,QAAQ,CAChC,CAAA,EACA3B,EAAAA,IAAC,OAAI,UAAU,oBACZ,WAAE,YAAY2B,EAAI,GAAG,OAAO,CAC/B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EArBK,CAuBR,CAAA,CACH,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,CCzDO,MAAMC,EAAW,CACtB,CACE,GAAI,EACJ,SAAU,cACV,MAAO,cACP,KAAM,WACN,WAAY,CAAC,MAAM,EACnB,YAAa,CACX,QAAS,cACT,WAAY,kCACZ,KAAM,waACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,oBACP,KAAM,gBACN,WAAY,CAAC,YAAY,EACzB,YAAa,CACX,QAAS,oBACT,WAAY,kCACZ,KAAM,kdACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,gBACP,KAAM,gBACN,WAAY,CAAC,QAAQ,EACrB,YAAa,CACX,QAAS,gBACT,WAAY,+BACZ,KAAM,wdACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,kBACP,KAAM,gBACN,WAAY,CAAC,KAAM,YAAY,EAC/B,YAAa,CACX,QAAS,yBACT,WAAY,4BACZ,KAAM,2cACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,qBACP,KAAM,gBACN,WAAY,CAAC,IAAI,EACjB,YAAa,CACX,QAAS,qBACT,WAAY,gCACZ,KAAM,2fACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,oBACP,KAAM,WACN,WAAY,CAAC,aAAc,YAAY,EACvC,YAAa,CACX,QAAS,oBACT,WAAY,0BACZ,KAAM,6eACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,eACP,KAAM,gBACN,WAAY,CAAC,OAAO,EACpB,YAAa,CACX,QAAS,eACT,WAAY,wBACZ,KAAM,8dACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,kBACP,KAAM,WACN,WAAY,CAAC,YAAY,EACzB,YAAa,CACX,QAAS,yBACT,WAAY,6BACZ,KAAM,ohBACP,CACF,EACD,CACE,GAAI,EACJ,SAAU,cACV,MAAO,uBACP,KAAM,gBACN,WAAY,CAAC,gBAAiB,YAAY,EAC1C,YAAa,CACX,QAAS,uBACT,WAAY,wBACZ,KAAM,6eACP,CACF,EACD,CACE,GAAI,GACJ,SAAU,cACV,MAAO,mBACP,KAAM,gBACN,WAAY,CAAC,YAAa,YAAY,EACtC,YAAa,CACX,QAAS,mBACT,WAAY,kCACZ,KAAM,yfACP,CACF,EACD,CACE,GAAI,GACJ,SAAU,cACV,MAAO,cACP,KAAM,gBACN,WAAY,CAAC,KAAK,EAClB,YAAa,CACX,QAAS,qBACT,WAAY,0CACZ,KAAM,shBACP,CACF,CACH,EC7HA,SAAwBC,GAAe,CAErC,KAAM,CAAE,EAAGC,EAAW,KAAAC,CAAA,EAASjC,EAAe,EACxCkC,EAAkBD,EAAK,UAAY,KAEnC,CAACE,EAAiBC,CAAkB,EAAIC,EAAAA,SAAS,KAAK,EACtD,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAASP,CAAQ,EAC3C,CAACU,EAAcC,CAAe,EAAIJ,EAAAA,SAAS,KAAK,EAGhD,CAACK,EAAYC,CAAa,EAAIN,WAAS,CAC3C,QAAS,wBACT,WAAY,2BACZ,KAAM,oBAAA,CACP,EAED1B,EAAAA,UAAU,IAAM,CAEZ4B,EADEJ,IAAoB,MAEpB,CAAC,GAAGL,CAAQ,EAAE,OAAQc,GAAS,CAACA,EAAK,WAAW,SAAS,KAAK,CAAC,EAI/D,CAAC,GAAGd,CAAQ,EAAE,OACXD,GACCA,EAAI,WAAW,SAASM,CAAe,GACvC,CAACN,EAAI,WAAW,SAAS,KAAK,CAAA,CANpC,CASF,EACC,CAACM,EAAiBD,CAAe,CAAC,EAG/B,MAAAW,EAAqBC,GAAmB,CAC5CL,EAAgBK,CAAc,EAGhBH,EAAA,CACZ,QAAS,aAAaG,CAAc,WACpC,WAAY,aAAaA,CAAc,cACvC,KAAM,aAAaA,CAAc,OAAA,CAClC,CACH,EAGMC,EAAiB,IAAM,CAC3BrD,EAAyB,2BAA4B,CACnD,SAAU,WACV,QAAS,eAAA,CACV,EACM,OAAA,KAAK,uBAAwB,QAAQ,CAC9C,EAGMsD,EAAsBC,GAAM,CAChCA,EAAE,eAAe,EACjBvD,EAAyB,2BAA4B,CACnD,SAAU,gBACV,QAAS,eAAA,CACV,EACM,OAAA,KAAK,uBAAwB,QAAQ,CAC9C,EAEA,OAEIO,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAC3B,EAAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,iDACX,SAAA8B,EAAU,aAAa,EAC1B,EACA/B,EAAAA,KAAC,KAAG,CAAA,UAAU,qBACZ,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,6BAA6B,SAAS,YAAA,EACtDD,EAAAA,KAAC,OAAK,CAAA,UAAU,6BACb,SAAA,CAAA,IACA+B,EAAUU,EAAW,OAAO,CAAA,CAC/B,CAAA,CAAA,EACF,QACC,KAAG,CAAA,UAAU,iDACX,SAAUV,EAAAU,EAAW,UAAU,EAClC,QACC,MAAI,CAAA,UAAU,wBACZ,SAAUV,EAAAU,EAAW,IAAI,CAC5B,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAzC,EAAAA,KAAC,MAAI,CAAA,UAAU,sDACb,SAAA,CAAAC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,KAAK,EACvBT,EAAmB,KAAK,CAC1B,EACA,UAAW,UAAUI,IAAiB,MAAQ,SAAW,EAAE,GAC3D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,sBAAsB,CAAA,CACnC,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,MAAM,EACxBT,EAAmB,MAAM,CAC3B,EACA,UAAW,UAAUI,IAAiB,OAAS,SAAW,EAAE,GAC5D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,uBAAuB,CAAA,CACpC,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,YAAY,EAC9BT,EAAmB,YAAY,CACjC,EACA,UAAW,UACTI,IAAiB,aAAe,SAAW,EAC7C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,6BAA6B,CAAA,CAC1C,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,QAAQ,EAC1BT,EAAmB,QAAQ,CAC7B,EACA,UAAW,UAAUI,IAAiB,SAAW,SAAW,EAAE,GAC9D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,yBAAyB,CAAA,CACtC,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,IAAI,EACtBT,EAAmB,IAAI,CACzB,EACA,UAAW,UAAUI,IAAiB,KAAO,SAAW,EAAE,GAC1D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,qBAAqB,CAAA,CAClC,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,YAAY,EAC9BT,EAAmB,YAAY,CACjC,EACA,UAAW,UACTI,IAAiB,aAAe,SAAW,EAC7C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,6BAA6B,CAAA,CAC1C,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,YAAY,EAC9BT,EAAmB,YAAY,CACjC,EACA,UAAW,UACTI,IAAiB,aAAe,SAAW,EAC7C,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,6BAA6B,CAAA,CAC1C,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,OAAO,EACzBT,EAAmB,OAAO,CAC5B,EACA,UAAW,UAAUI,IAAiB,QAAU,SAAW,EAAE,GAC7D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,wBAAwB,CAAA,CACrC,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,SAAS,EAC3BT,EAAmB,SAAS,CAC9B,EACA,UAAW,UAAUI,IAAiB,UAAY,SAAW,EAAE,GAC/D,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,0BAA0B,CAAA,CACvC,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,eAAe,EACjCT,EAAmB,eAAe,CACpC,EACA,UAAW,UACTI,IAAiB,gBAAkB,SAAW,EAChD,GACA,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,gCAAgC,CAAA,CAC7C,EAGAtC,EAAA,IAAC,IAAA,CACC,QAAS,IAAM,CACb2C,EAAkB,WAAW,EAC7BT,EAAmB,WAAW,CAChC,EACA,UAAW,UAAUI,IAAiB,YAAc,SAAW,EAAE,GACjE,MAAO,CAAE,OAAQ,SAAU,EAE1B,WAAU,4BAA4B,CAAA,CAAA,CACzC,CACF,CAAA,CAAA,EAEF,EACAtC,EAAA,IAAC,MAAA,CACC,UAAU,oBACV,MAAO,CACL,MAAO,OACP,SAAU,SACV,OAAQ,QACV,EAEA,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,IAAI,gCACJ,IAAI,+BAAA,CACN,EACAA,EAAA,IAAC,KAAA,CACC,UAAU,0CACV,GAAG,YACH,MAAO,CACL,SAAU,WACV,IAAK,EACL,KAAM,EACN,MAAO,OACP,OAAQ,OACR,QAAS,OACT,oBAAqB,iBACrB,IAAK,MACL,WAAY,cACZ,aAAc,gBACd,SAAU,SACV,OAAQ,EACR,QAAS,CACX,EAEA,eAACgD,EACE,CAAA,SAAAZ,EAAS,IAAI,CAACM,EAAMO,IACnBjD,EAAA,IAAC,KAAA,CAEC,UAAW,iBAAiB0C,EAAK,WAAW,KAAK,GAAG,CAAC,GACrD,MAAO,CACL,WAAY,cACZ,MAAO,OACP,OAAQ,MACV,EAEC,SAAAA,EAAK,OAAS,WACb1C,EAAA,IAACkD,EAAA,CACC,SAAUR,EAAK,OACf,UAAWA,EAAK,OAChB,MAAO,IACP,OAAQ,IAEP,SAAA,CAAC,CAAE,KAAAS,CAAA,IACFpD,EAAA,KAAC,IAAA,CACC,QAAS+C,EACT,UAAU,+BACV,MAAO,CACL,QAAS,OACT,cAAe,SACf,MAAO,OACP,OAAQ,OACR,aAAc,OACd,SAAU,SACV,OAAQ,SACV,EAEA,SAAA,CAAA9C,EAAA,IAAC,MAAA,CACC,UAAU,WACV,MAAO,CACL,KAAM,EACN,WAAY,aAAA,CACd,CACF,EACAD,EAAA,KAAC,MAAA,CACC,UAAU,aACV,MAAO,CACL,QAAS,MACX,EAEA,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,aACX,SAAA8B,EACC,oBAAoBY,EAAK,MACtB,MAAM,GAAG,EAAE,CAAC,EACZ,aAAa,EAAA,EAEpB,EACC1C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CAAA,CAAA,CACzC,CAAA,CAAA,CACF,CAAA,EAIJD,EAAA,KAAC,IAAA,CACC,QAAS+C,EACT,UAAU,gBACV,MAAO,CAAE,WAAY,cAAe,OAAQ,SAAU,EAEtD,SAAA,CAAA9C,EAAA,IAAC,MAAA,CACC,UAAU,WACV,MAAO,CACL,OAAQ,QACR,WAAY,aACd,EAEA,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,cACV,MAAO,CAAE,WAAY,aAAc,CAAA,CAAA,CACrC,CACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACC,EAAAA,IAAA,KAAA,CAAG,UAAU,aACX,SAAA8B,EACC,oBAAoBY,EAAK,MACtB,MAAM,GAAG,EAAE,CAAC,EACZ,aAAa,EAAA,EAEpB,EACC1C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,EAlFGiD,CAAA,CAqFR,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,EAGAjD,EAAA,IAAC,UAAA,CACC,UAAU,oEACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,eAAC,MAAI,CAAA,UAAU,YACb,SAACD,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,+EACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,sCACX,SAAA8B,EAAU,0BAA0B,CACvC,CAAA,EACF,QACC,MAAI,CAAA,UAAU,4CACb,SAAC9B,MAAA,MAAA,CAAI,UAAU,SACb,SAAAD,EAAA,KAAC,IAAA,CACC,QAAS8C,EACT,UAAU,6CACV,oBAAkB,IAClB,MAAO,CAAE,OAAQ,SAAU,EAE3B,SAAA,CAAA7C,MAAC,OAAK,CAAA,UAAU,oCACb,SAAA8B,EAAU,2BAA2B,EACxC,EACA9B,EAAA,IAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,WAAU,2BAA2B,CAAA,CAAA,CACxC,CAAA,GAEJ,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,EACF,CAEJ,CC/YA,SAAwBoD,GAAO,CAC7B,KAAM,CAAE,KAAArB,EAAM,EAAAlC,CAAE,EAAIC,EAAe,EAC7BkC,EAAkBD,EAAK,UAAY,KACnC,CAACsB,EAAWC,CAAY,EAAInB,EAAAA,SAAS,CAAA,CAAE,EACvC,CAACoB,EAASC,CAAU,EAAIrB,EAAAA,SAAS,EAAI,EACrC,CAAC/D,EAAOqF,CAAQ,EAAItB,EAAAA,SAAS,EAAE,EAErC1B,EAAAA,UAAU,IAAM,EACS,SAAY,OAC7B,GAAA,CACF+C,EAAW,EAAI,EACf,MAAME,EAAS,MAAMC,EAAQ,iBAAiB3B,EAAiB,CAAC,EAEhE,GAAI0B,EAAO,SAAS,IAAMA,EAAO,KAAM,CAE/B,MAAAE,IAAQC,EAAAH,EAAO,KAAK,OAAZ,YAAAG,EAAkB,OAAQH,EAAO,KAAK,MAAQ,CAAC,EACrD,QAAA,IAAI,qBAAsBA,EAAO,IAAI,EACrC,QAAA,IAAI,eAAgBE,CAAK,EACjCN,EAAa,MAAM,QAAQM,CAAK,EAAIA,EAAQ,CAAA,CAAE,CAAA,MAE9C,QAAQ,MAAM,8BAA+BF,EAAO,SAAS,MAAM,EACnEJ,EAAa,CAAA,CAAE,QAEVlF,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjDqF,EAAS,2BAA2B,EACpCH,EAAa,CAAA,CAAE,CAAA,QACf,CACAE,EAAW,EAAK,CAAA,CAEpB,GAEe,CAAA,EACd,CAACxB,CAAe,CAAC,EAGd,MAAA8B,EAAiB,CAACC,EAAMC,IAAU,WAChC,MAAAC,GAAcJ,EAAAE,EAAK,eAAL,YAAAF,EAAmB,KACpChE,GAAMA,EAAE,WAAamC,GAExB,OACEiC,GAAA,YAAAA,EAAcD,OACdE,GAAAC,EAAAJ,EAAK,eAAL,YAAAI,EAAmB,KAAMtE,GAAMA,EAAE,WAAa,QAA9C,YAAAqE,EAAsDF,KACtD,EAEJ,EAGA,OAAIT,EAEAxD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,EACCG,MAAA,MAAA,CAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YAAY,SAAA,wBAAqB,EAClD,CACF,CAAA,CAAA,EACF,EAKA5B,EAEA2B,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,EACCG,MAAA,MAAA,CAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YAAa,SAAA5B,EAAM,EACpC,CACF,CAAA,CAAA,EACF,EAKAiF,EAAU,SAAW,EAErBtD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,CAAA,CACjD,CACF,CAAA,EACCG,MAAA,MAAA,CAAI,UAAU,aACb,eAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YAAY,SAAA,+BAA4B,EACzD,CACF,CAAA,CAAA,EACF,EAIFD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAC5CG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,eAAe,CAAE,CAAA,CAAA,EACjD,EACAG,EAAAA,IAAC,OAAI,UAAU,2DACb,gBAACoE,EAAK,CAAA,GAAI,QAAS,UAAU,eAC1B,SAAA,CAAAvE,EAAE,gBAAgB,EAAE,IAACG,EAAAA,IAAC,IAAE,CAAA,UAAU,0BAA2B,CAAA,CAAA,CAAA,CAChE,CACF,CAAA,CAAA,EACF,EACCA,EAAAA,IAAA,MAAA,CAAI,UAAU,aAEZ,SAAM,MAAA,QAAQqD,CAAS,GACtBA,EAAU,IAAI,CAACU,EAAMd,IACnB,OAAAjD,OAAAA,EAAA,IAAC,MAAA,CAEC,UAAW,mDACX,iBAAgB,GAAGiD,EAAQ,EAAG,IAE9B,SAAAlD,EAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAAAA,EAAAA,IAACoE,GAAK,GAAI,gBAAgBL,EAAK,IAAI,GACjC,SAAA/D,EAAA,IAAC,MAAA,CACC,IACE+D,EAAK,eACL,yCAEF,MAAO,IACP,OAAQ,IACR,IAAKD,EAAeC,EAAM,OAAO,CAAA,GAErC,CACF,CAAA,EACC/D,MAAA,KAAA,CAAG,UAAU,kBACZ,eAACoE,EAAK,CAAA,GAAI,gBAAgBL,EAAK,IAAI,GAChC,SAAAD,EAAeC,EAAM,OAAO,CAC/B,CAAA,EACF,QACC,MAAI,CAAA,UAAU,iBACZ,SAAeD,EAAAC,EAAM,SAAS,EACjC,EACAhE,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,KAAK,IAAI,UAAU,cACpB,SAACA,EAAAA,IAAA,IAAA,CAAE,UAAU,8BAAA,CAA+B,CAC9C,CAAA,QACC,IAAE,CAAA,KAAK,IAAK,WAAK6D,EAAAE,EAAA,SAAA,YAAAF,EAAQ,OAAQ,gBAAiB,CAAA,CAAA,EACrD,EACA9D,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,kCAAmC,CAAA,EAC/CA,EAAA,IAAA,IAAA,CAAE,KAAK,IACL,SAAI,IAAA,KACH+D,EAAK,aAAeA,EAAK,SAC3B,EAAE,oBACJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EA1CKA,EAAK,EAAA,EA4Cb,CAEL,CAAA,CAAA,EACF,CAEJ,CCzLO,MAAMM,EAAkB,IAAM,CACnC,KAAM,CAAE,EAAAxE,CAAG,EAAGC,EAAgB,EAE9B,MAAO,CACL,CACE,UAAW,cACX,MAAOD,EAAE,uBAAuB,EAChC,KAAMA,EAAE,sBAAsB,EAC9B,KAAM,CACJ,IAAK,4CACL,KAAMA,EAAE,sBAAsB,EAC9B,IAAK,oBACL,OAAQ,QACT,CACF,EACD,CACE,UAAW,WACX,MAAOA,EAAE,qBAAqB,EAC9B,KAAMA,EAAE,oBAAoB,EAC5B,KAAM,CACJ,IAAK,2BACL,KAAMA,EAAE,oBAAoB,CAC7B,CACF,EACD,CACE,UAAW,YACX,MAAOA,EAAE,qBAAqB,EAC9B,KAAMA,EAAE,oBAAoB,EAC5B,KAAM,CACJ,IAAK,mBACL,KAAMA,EAAE,oBAAoB,CAC7B,CACF,CACF,CACH,EC3BA,SAAwByE,GAAU,CAC1B,KAAA,CAAE,EAAAzE,CAAE,EAAIC,EAAe,EACvByE,EAAeF,EAAgB,EAC/B,CAACG,EAAUC,CAAW,EAAItC,WAAS,CACvC,KAAM,GACN,MAAO,GACP,QAAS,EAAA,CACV,EACK,CAACuC,EAAYC,CAAa,EAAIxC,WAAS,CAC3C,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAAS,EAAA,CACV,EAEKyC,EAAqB7B,GAAM,CAC/B,KAAM,CAAE,KAAA8B,EAAM,MAAAvD,CAAM,EAAIyB,EAAE,OACd0B,EAAA,CACV,GAAGD,EACH,CAACK,CAAI,EAAGvD,CAAA,CACT,CACH,EAGMwD,EAAe,MAAO/B,GAAM,SAChCA,EAAE,eAAe,EAGH4B,EAAA,CACZ,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAAS,EAAA,CACV,EAEG,GAAA,CAGI,MAAAI,EADgB,OAAO,SAAS,WAAa,YAE/C,oCACA,mCAIE,MAAAC,EAAM,KAAKD,EAAaP,EAAU,CACtC,QAAS,CACP,eAAgB,mBAChB,YANW,kCAME,CACf,CACD,EAGDvF,EAA2BuF,EAAS,MAAOA,EAAS,QAAQ,MAAM,EAClE3F,EAAoB,eAAgB,OAAO,SAAS,SAAU,EAAI,EAGpD8F,EAAA,CACZ,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QACE9E,EAAE,iBAAiB,GACnB,qDAAA,CACH,EAGW4E,EAAA,CACV,KAAM,GACN,MAAO,GACP,QAAS,EAAA,CACV,QACMrG,EAAO,CACN,QAAA,MAAM,sBAAuBA,CAAK,EAG1CS,EAAoB,eAAgB,OAAO,SAAS,SAAU,EAAK,EAG/D,IAAAoG,EACFpF,EAAE,eAAe,GACjB,sEAEEgE,EAAAzF,EAAM,WAAN,YAAAyF,EAAgB,UAAW,IAE3BoB,EAAApF,EAAE,mBAAmB,GACrB,iEACOsE,EAAA/F,EAAM,WAAN,YAAA+F,EAAgB,UAAW,MAElCc,EAAApF,EAAE,oBAAoB,GAAK,0CAGjB8E,EAAA,CACZ,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAASM,CAAA,CACV,CAAA,CAEL,EAGE,OAAAlF,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,4BACb,eAAC,MAAI,CAAA,UAAU,wBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MAEZ,SAAAuE,EAAa,IAAI,CAAC7B,EAAMO,IACtBlD,EAAAA,KAAAmF,EAAM,SAAN,CACC,SAAA,CAAAlF,EAAAA,IAAC,OAAI,UAAW,8BACd,SAACD,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,UACb,SAAAA,EAAAA,IAAC,KAAE,UAAW0C,EAAK,UAAW,CAChC,CAAA,EACC1C,EAAA,IAAA,KAAA,CAAG,UAAU,WAAY,WAAK,MAAM,EACpCA,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAiB,WAAK,KAAK,EAC1CA,EAAAA,IAAC,MAAI,CAAA,UAAU,UACb,SAAAA,EAAA,IAAC,IAAA,CACC,KAAM0C,EAAK,KAAK,IAChB,OAAQA,EAAK,KAAK,OAClB,IAAKA,EAAK,KAAK,IAEd,WAAK,KAAK,IAAA,CAAA,EAEf,EAAO,GAAA,CAAA,CACT,CACF,CAAA,EAAO,GAAA,CAAA,EAlBYO,CAmBrB,CACD,EAGH,CAAA,CACF,CACF,CAAA,QAEC,MAAI,CAAA,UAAU,MACb,SAACjD,MAAA,MAAA,CAAI,UAAU,wBACb,SAAAD,EAAA,KAAC,OAAA,CACC,SAAU+E,EACV,UAAU,2CACV,iBAAe,MACf,GAAG,eAEH,SAAA,CAAC/E,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,WAEb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAC,MAAC,QAAM,CAAA,QAAQ,OAAQ,SAAAH,EAAE,cAAc,EAAE,EACzCG,EAAA,IAAC,QAAA,CACC,KAAK,OACL,KAAK,OACL,GAAG,OACH,UAAU,8BACV,YAAaH,EAAE,0BAA0B,EACzC,QAAQ,WACR,SAAQ,GACR,gBAAc,OACd,MAAO2E,EAAS,KAChB,SAAUI,CAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,QACC,MAAI,CAAA,UAAU,WAEb,SAAC7E,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAC,MAAC,QAAM,CAAA,QAAQ,QAAS,SAAAH,EAAE,eAAe,EAAE,EAC3CG,EAAA,IAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAaH,EAAE,2BAA2B,EAC1C,QAAQ,WACR,SAAQ,GACR,gBAAc,OACd,MAAO2E,EAAS,MAChB,SAAUI,CAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAEA7E,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,MAAC,QAAM,CAAA,QAAQ,UAAW,SAAAH,EAAE,iBAAiB,EAAE,EAC/CG,EAAA,IAAC,WAAA,CACC,KAAK,UACL,GAAG,UACH,UAAU,8BACV,MAAO,CAAE,OAAQ,GAAI,EACrB,YAAaH,EAAE,6BAA6B,EAC5C,MAAO2E,EAAS,QAChB,SAAUI,EACV,UAAW,EACX,UAAW,IACX,SAAQ,EAAA,CACV,EACA7E,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACZ,SAAA,CAAAyE,EAAS,QAAQ,OAAO,kBAAA,CAC3B,CAAA,CAAA,EACF,EACAzE,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,WAEb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,mBAAoB,CAAA,EAChCH,EAAE,eAAe,CAAA,CAAA,CACpB,CACF,CAAA,QACC,MAAI,CAAA,UAAU,WAEb,SAACG,MAAA,MAAA,CAAI,UAAU,iBACb,SAAAD,EAAA,KAAC,SAAA,CACC,KAAK,SACL,GAAG,aACH,gBAAc,SACd,UAAU,wDACV,oBAAkB,IAClB,SAAU2E,EAAW,WAErB,SAAA,CAAC3E,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAW2E,EAAA,WAAa,aAAe7E,EAAE,cAAc,EACxDG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAW2E,EAAA,WAAa,aAAe7E,EAAE,cAAc,EACxDG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,GAEJ,CACF,CAAA,CAAA,EACF,EACAA,EAAA,IAAC,MAAA,CACC,GAAG,SACH,KAAK,SACL,YAAU,SACV,cAAY,OACZ,UAAW,oBACT0E,EAAW,QACP,kCACAA,EAAW,MACX,gCACA,EACN,GACA,MAAO,CAAE,QAASA,EAAW,QAAU,QAAU,MAAO,EAEvD,SAAWA,EAAA,OAAA,CAAA,CACd,CAAA,GAEJ,CACF,CAAA,CAAA,EAEF,CAEJ,CC3QA,SAAwBS,GAAc,CAC9B,KAAA,CAAE,EAAAtF,CAAE,EAAIC,EAAe,EAC7B,OAEIC,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAA1B,EAAAA,IAAC,OAAI,UAAU,0CACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAK,SAAEH,EAAA,sBAAsB,CAAE,CAAA,QAC/B,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,EAAE,QAClD,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,sBAAsB,CAAE,CAAA,CAAA,CAAA,CACrD,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,oCACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,kCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAK,SAAEH,EAAA,iBAAiB,CAAE,CAAA,QAC1B,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,EAAE,QAC7C,MAAI,CAAA,cAAY,OAAQ,SAAAA,EAAE,iBAAiB,CAAE,CAAA,CAAA,CAAA,CAChD,CACF,CAAA,CAAA,EACF,CAEJ,CC/CA,SAAwBuF,EAAK,CAAE,QAAAC,EAAU,GAAO,KAAAC,EAAO,IAAS,CACxD,KAAA,CAAE,EAAAzF,CAAE,EAAIC,EAAe,EAG7B,OAAAG,EAAiB,2CAA2C,EAGxDF,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAA1B,EAAA,IAAC,UAAA,CACC,UAAW,wCACTsF,EAAO,0BAA4B,EACrC,IACA,GAAG,QAEH,eAAC,MAAI,CAAA,UAAU,8BACb,SAACvF,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,8CACb,SAAAD,EAAA,KAAC,MACC,CAAA,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,kBAAkB,iBAAe,QAC9C,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,YAAY,EAAE,EAAQ,IAAI,mBAExDG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,2BACb,SAAAA,EAAAA,IAAC,IAAE,CAAA,UAAU,OAAQ,SAAAH,EAAE,wBAAwB,CAAE,CAAA,CACnD,CAAA,CAAA,EACF,EACCG,MAAA,MAAA,CAAI,UAAU,2CACZ,WAEID,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAA,IACD3B,EAAA,KAAC,IAAA,CACC,KAAK,SACL,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,EAGGD,EAAAA,KAAA2B,EAAA,SAAA,CAAA,SAAA,CAAA,IACD3B,EAAA,KAACqE,EAAA,CACC,GAAI,SACJ,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACrE,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,sBAAsB,EAAG,UAC3B,OAAK,CAAA,UAAU,kBACb,SAAAA,EAAE,4BAA4B,EACjC,EAAQ,IACRG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QACCJ,EAAM,CAAA,CAAA,CAAA,CAAA,CACT,CACF,CAAA,CAAA,CACF,QAECuF,EAAY,EAAA,EAEbnF,EAAA,IAAC,UAAA,CACC,UAAW,uGACTsF,EAAO,0BAA4B,EACrC,GACA,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,MAEH,eAACzD,EAAa,CAAA,CAAA,CAAA,CAChB,QAEC,MAAI,CAAA,UAAU,+BACb,SAAA7B,MAACmF,GAAY,CAAA,EACf,EAEAnF,EAAA,IAAC,UAAA,CACC,UAAU,6EACV,MAAO,CACL,gBAAiB,wCACnB,EAEA,eAAC,MAAI,CAAA,UAAU,8BACb,SAACD,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,oBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,6EACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,wCACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,wCACJ,MAAO,IACP,OAAQ,IACR,IAAI,mBAAA,CAAA,EAER,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,wBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,QAEvB,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,wCACJ,IAAI,oBACJ,MAAO,IACP,OAAQ,GAAA,CAAA,CACV,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,iDACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,oBACb,SAAAD,OAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,+BACX,SAAAH,EAAE,qBAAqB,EAC1B,EACAG,EAAA,IAAC,MAAI,CAAA,UAAU,2BACb,SAAAA,EAAAA,IAAC,IAAE,CAAA,UAAU,OAAQ,SAAAH,EAAE,2BAA2B,CAAE,CAAA,EACtD,EACCG,MAAA,MAAA,CAAI,UAAU,eACZ,WAEID,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAA,IACD3B,EAAA,KAAC,IAAA,CACC,KAAK,YACL,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,EAGGD,EAAAA,KAAA2B,EAAA,SAAA,CAAA,SAAA,CAAA,IACD3B,EAAA,KAACqE,EAAA,CACC,GAAI,YACJ,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACrE,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,4BAA4B,EAAG,IAClCG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,QAYC,MAAI,CAAA,UAAU,+BACb,SAAAA,MAACmF,GAAY,CAAA,EACf,EACAnF,EAAA,IAAC,UAAA,CACC,UAAW,wCACTsF,EAAO,0BAA4B,EACrC,IACA,GAAG,WAEH,eAAC7D,EAAQ,CAAA,CAAA,CAAA,CACX,EACAzB,EAAAA,IAAC,KAAG,CAAA,UAAU,WAAY,CAAA,EA2B1BA,EAAA,IAAC,UAAA,CACC,UAAW,iBACTsF,EAAO,0BAA4B,yBACrC,+BACA,MAAO,CACL,gBAAiB,wCACnB,EAEA,eAAC,MAAI,CAAA,UAAU,YACb,SAACvF,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,+EACb,SAACA,EAAAA,IAAA,KAAA,CAAG,UAAU,sCACX,SAAAH,EAAE,gBAAgB,CACrB,CAAA,EACF,EACAG,EAAA,IAAC,OAAI,UAAU,4CACb,eAAC,MAAI,CAAA,UAAU,SACZ,SAAAqF,EAEItF,EAAAA,KAAA2B,EAAAA,SAAA,CAAA,SAAA,CAAA,IACD3B,EAAA,KAAC,IAAA,CACC,KAAK,WACL,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,oCACb,SAAAH,EAAE,iBAAiB,EACtB,EACAG,EAAA,IAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,WAAE,iBAAiB,CAAA,CAAA,CACtB,CAAA,CAAA,CACF,CAAA,CACF,EAGGD,EAAAA,KAAA2B,EAAA,SAAA,CAAA,SAAA,CAAA,IACD3B,EAAA,KAACqE,EAAA,CACC,GAAI,WACJ,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAAApE,MAAC,OAAK,CAAA,UAAU,oCACb,SAAAH,EAAE,iBAAiB,EACtB,EACAG,EAAA,IAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,WAAE,iBAAiB,CAAA,CAAA,CACtB,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,EAaAA,EAAA,IAAC,UAAA,CACC,UAAW,iCACTsF,EAAO,0BAA4B,EACrC,GACA,GAAG,OAEH,eAAClC,EAAK,CAAA,CAAA,CAAA,CACR,EAEArD,EAAA,KAAC,UAAA,CACC,UAAW,mCACTuF,EAAO,0BAA4B,EACrC,IACA,GAAG,UAEH,SAAA,CAAAtF,MAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wDACb,SAACD,OAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAAAC,MAAC,OAAK,CAAA,UAAU,YAAa,SAAAH,EAAE,oBAAoB,EAAE,EACpDG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,CAAA,CACF,CACF,CAAA,EACF,QACCsE,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CACX,EACF,CAEJ,CAEAc,EAAK,UAAY,CACf,QAASG,EAAU,KACnB,KAAMA,EAAU,IAClB,EC/YA,SAAwBC,IAAO,CACvB,KAAA,CAAE,EAAA3F,CAAE,EAAIC,EAAe,EAEvB2F,EAAsB,IAAM,CAChChH,EAAiB,eAAgB,eAAgB,CAC/C,YAAa,MACb,QAAS,MAAA,CACV,CACH,EAEE,OAAAsB,EAAA,KAAC,MAAI,CAAA,UAAU,yFAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAAAC,MAAC,KAAG,CAAA,UAAU,wDACX,SAAAH,EAAE,cAAc,EACnB,QACC,KAAG,CAAA,UAAU,uCACZ,SAAAG,EAAAA,IAAC,QAAK,UAAU,sBAAsB,iBAAe,QACnD,eAAC0F,EAAa,CAAA,KAAM7F,EAAE,aAAa,CAAA,CAAG,CACxC,CAAA,EACF,EACCG,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,iBAAe,QAC7D,SAAAD,EAAA,KAAC,IAAA,CACC,KAAK,SACL,UAAU,6CACV,oBAAkB,IAClB,QAAS0F,EAET,SAAA,CAAC1F,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAF,EAAE,eAAe,EAAG,IACrBG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAD,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAF,EAAE,eAAe,EAAG,IACrBG,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAA,EAAA,IAAC,MAAA,CACC,UAAU,+CACV,kBAAiB,EAEjB,SAAAA,EAAA,IAAC,KAAE,KAAK,SAAS,UAAU,gBACxB,SAAAH,EAAE,iBAAiB,GAAK,aAC3B,CAAA,CAAA,CAAA,CACF,EAEF,CAEJ,CClEO,MAAM8F,EAAe,CAC1B,CACE,KAAM,eACN,KAAM,gBACN,MAAO,uBACP,QAAS,CACP,CACE,KAAM,WACN,IAAK,oDACN,EACD,CAAE,KAAM,SAAU,IAAK,gCAAkC,CAC1D,CACF,EACD,CACE,KAAM,uBACN,KAAM,qBACN,MAAO,+BACP,QAAS,CACP,CACE,KAAM,WACN,IAAK,6DACN,EACD,CAAE,KAAM,YAAa,IAAK,yCAA2C,CACtE,CACF,CACH,ECrBA,SAAwBC,IAAO,CACvB,KAAA,CAAE,EAAA/F,CAAE,EAAIC,EAAe,EAE3B,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,qBACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,wDACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,UAAU,YACb,SAAEH,EAAA,kBAAkB,EAAE,MAAM,GAAG,EAAE,CAAC,CACrC,CAAA,EAAQ,IACPA,EAAE,kBAAkB,EAAE,MAAM,GAAG,EAAE,CAAC,EAClCG,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,QACC,MAAI,CAAA,UAAU,YAAa,SAAAH,EAAE,wBAAwB,CAAE,CAAA,CAAA,CAAA,CAC1D,CACF,CAAA,QACC,MAAI,CAAA,UAAU,oCAEZ,SAAA8F,EAAa,IAAI,CAACE,EAAQ5C,IACzBjD,EAAA,IAAC,OAAgB,UAAU,kCACzB,SAACD,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAW,mBACT8F,EAAO,OAAS,eAAiB,uBAAyB,EAC5D,GAEA,SAAA,CAAA7F,EAAA,IAAC,MAAA,CACC,IAAK6F,EAAO,MACZ,MAAO,IACP,OAAQ,IACR,UAAU,6CACV,oBAAkB,OAClB,IAAK,YAAYA,EAAO,IAAI,GAC5B,MAAO,CAAE,UAAW,QAAS,MAAO,OAAQ,OAAQ,MAAO,CAAA,CAC7D,EACC7F,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,oBACZ,SAAO6F,EAAA,QAAQ,IAAI,CAACC,EAAQC,IAC3BhG,EAAA,KAAC,IAAA,CAEC,KAAM+F,EAAO,IACb,OAAO,SACP,IAAI,sBAEJ,SAAA,CAAA9F,EAAA,IAAC,MAAI,CAAA,UAAU,kBAAmB,SAAA8F,EAAO,KAAK,EAC9C9F,MAAC,KAAE,UAAW,MAAM8F,EAAO,KAAK,YAAa,CAAA,EAAI,CAAA,CAAA,CAAA,EAN5CC,CAQR,CAAA,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACAhG,EAAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,iBAAkB,SAAA6F,EAAO,KAAK,EAC5C7F,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAkB,WAAO,IAAK,CAAA,CAAA,CAC/C,CAAA,CAAA,EACF,CAAA,EApCQiD,CAqCV,CACD,CAEH,CAAA,CAAA,EACF,CAEJ,CC7DA,MAAM+C,EAAU,CACd,CAAE,KAAM,YAAa,SAAU,KAAM,EACrC,CAAE,KAAM,WAAY,SAAU,UAAW,EACzC,CAAE,KAAM,SAAU,SAAU,QAAS,EACrC,CAAE,KAAM,cAAe,SAAU,aAAc,CACjD,EACA,SAAwBC,IAAY,CAClC,KAAM,CAAChE,EAAiBC,CAAkB,EAAIC,EAAAA,SAAS,KAAK,EACtD,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAAS+D,CAAW,EACpDzF,OAAAA,EAAAA,UAAU,IAAM,CACVwB,GAAmB,MACrBI,EAAY6D,CAAW,EAEvB7D,EACE,CAAC,GAAG6D,CAAW,EAAE,OAAQvE,GACvBA,EAAI,WAAW,SAASM,CAAe,CAAA,CAE3C,CACF,EACC,CAACA,CAAe,CAAC,EAGhBlC,EAAA,KAAA2B,WAAA,CAAA,SAAA,CAAC1B,EAAA,IAAA,MAAA,CAAI,UAAU,YAEb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,sDACZ,SAAQgG,EAAA,IAAI,CAACrE,EAAKwE,IACjBnG,EAAA,IAAC,IAAA,CACC,QAAS,IAAMkC,EAAmBP,EAAI,QAAQ,EAE9C,UAAW,UAAUM,GAAmBN,EAAI,SAAW,SAAW,EAChE,GAED,SAAIA,EAAA,IAAA,EAJAwE,CAAA,CAMR,EACH,CAEF,CAAA,EACAnG,EAAAA,IAAC,MAAI,CAAA,UAAU,oBAEb,SAAAA,EAAA,IAAC,KAAA,CACC,UAAU,sDACV,GAAG,YAEH,gBAACgD,EAEE,CAAA,SAAA,CAASZ,EAAA,IAAI,CAACM,EAAMO,IACnBjD,EAAA,IAAC,KAAA,CAEC,UAAW,iBAAiB0C,EAAK,WAAW,KAAK,GAAG,CAAC,GAEpD,SAAAA,EAAK,OAAS,WACb1C,EAAA,IAACkD,EAAA,CACC,SAAUR,EAAK,SACf,UAAWA,EAAK,SAChB,MAAO,IACP,OAAQ,IAEP,SAAC,CAAA,CAAE,IAAA0D,EAAK,KAAAjD,CACP,IAAApD,EAAA,KAAC,IAAA,CACC,QAASoD,EACT,UAAU,+BAEV,SAAA,CAACpD,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,CAAA,EAE5CA,EAAA,IAAC,MAAA,CACC,IAAK0C,EAAK,SACV,IAAA0D,EACA,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACArG,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAA0C,EAAK,MAAM,EACtC1C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,EAIJD,EAAA,KAACqE,EAAA,CACC,GAAI,qBAAqB1B,EAAK,EAAE,GAChC,UAAU,gBAEV,SAAA,CAAC3C,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,aAAc,CAAA,EAC7BA,EAAA,IAAC,MAAA,CACC,IAAK0C,EAAK,SACV,MAAO,IACP,OAAQ,IACR,IAAI,kBAAA,CAAA,CACN,EACF,EACA3C,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,aAAc,SAAA0C,EAAK,MAAM,EACtC1C,EAAA,IAAA,MAAA,CAAI,UAAU,aAAc,WAAK,IAAK,CAAA,CAAA,CACzC,CAAA,CAAA,CAAA,CAAA,CACF,EAnDGiD,CAAA,CAsDR,EAAG,GAAA,CACN,CAAA,CAAA,CAAA,CAIJ,CAAA,CAAA,EACF,CAEJ"}