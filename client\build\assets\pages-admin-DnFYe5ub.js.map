{"version": 3, "file": "pages-admin-DnFYe5ub.js", "sources": ["../../src/pages/AdminLogin.jsx", "../../src/pages/AdminDashboard.jsx", "../../src/pages/AdminBlogPosts.jsx", "../../src/pages/AdminBlogEditor.jsx", "../../src/pages/AdminProducts.jsx", "../../src/pages/AdminProductEditor.jsx", "../../src/pages/AdminBlogAnalytics.jsx", "../../src/pages/AdminCategories.jsx", "../../src/pages/AdminTags.jsx", "../../src/utils/commentAPI.js", "../../src/pages/admin/comments/page.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport { authAPI } from \"../utils/api\";\n\nconst AdminLogin = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    email: \"\",\n    password: \"\",\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n    // Clear error when user starts typing\n    if (error) setError(\"\");\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(\"\");\n\n    try {\n      const { response, data } = await authAPI.login(formData);\n\n      if (data.success) {\n        // Store token in localStorage\n        localStorage.setItem(\"adminToken\", data.token);\n        localStorage.setItem(\"adminUser\", JSON.stringify(data.user));\n\n        // Redirect to admin dashboard\n        navigate(\"/admin/dashboard\");\n      } else {\n        setError(data.message || \"Login failed\");\n      }\n    } catch (err) {\n      console.error(\"Login error:\", err);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Admin Login - DevSkills\"\n        description=\"Admin login page for DevSkills content management\"\n        noIndex={true}\n      />\n\n      {/* Page Wrapper */}\n      <div id=\"page\" className=\"page\">\n        {/* Main Content */}\n        <main id=\"main\">\n          {/* Admin Login Section */}\n          <section\n            className=\"page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section\"\n            id=\"admin-login\"\n          >\n            <div className=\"container relative\">\n              <div className=\"row\">\n                <div className=\"col-md-6 offset-md-3 col-lg-4 offset-lg-4\">\n                  {/* Login Form */}\n                  <div className=\"form-container\">\n                    {/* Header */}\n                    <div className=\"text-center mb-60 mb-sm-40\">\n                      <div className=\"hs-line-4 font-alt black mb-20 mb-xs-10\">\n                        <span className=\"color-primary-1\">DevSkills</span> Admin\n                      </div>\n                      <p className=\"section-descr mb-0\">\n                        Sign in to access the admin dashboard\n                      </p>\n                    </div>\n\n                    {/* Error Message */}\n                    {error && (\n                      <div className=\"alert alert-danger mb-30\" role=\"alert\">\n                        <i className=\"mi-warning\"></i>\n                        {error}\n                      </div>\n                    )}\n\n                    {/* Login Form */}\n                    <form className=\"form contact-form\" onSubmit={handleSubmit}>\n                      {/* Email Field */}\n                      <div className=\"form-group\">\n                        <label htmlFor=\"email\" className=\"sr-only\">\n                          Email Address\n                        </label>\n                        <input\n                          type=\"email\"\n                          name=\"email\"\n                          id=\"email\"\n                          className=\"input-lg round form-control\"\n                          placeholder=\"Email Address\"\n                          value={formData.email}\n                          onChange={handleChange}\n                          required\n                          autoComplete=\"email\"\n                        />\n                      </div>\n\n                      {/* Password Field */}\n                      <div className=\"form-group\">\n                        <label htmlFor=\"password\" className=\"sr-only\">\n                          Password\n                        </label>\n                        <input\n                          type=\"password\"\n                          name=\"password\"\n                          id=\"password\"\n                          className=\"input-lg round form-control\"\n                          placeholder=\"Password\"\n                          value={formData.password}\n                          onChange={handleChange}\n                          required\n                          autoComplete=\"current-password\"\n                        />\n                      </div>\n\n                      {/* Submit Button */}\n                      <div className=\"form-group\">\n                        <button\n                          type=\"submit\"\n                          className=\"btn btn-mod btn-color btn-large btn-round btn-full-width\"\n                          disabled={loading}\n                        >\n                          {loading ? (\n                            <>\n                              <i className=\"fa fa-spinner fa-spin me-2\"></i>\n                              Signing in...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"mi-lock me-2\"></i>\n                              Sign In\n                            </>\n                          )}\n                        </button>\n                      </div>\n                    </form>\n\n                    {/* Footer */}\n                    <div className=\"text-center mt-40\">\n                      <p className=\"small opacity-07\">\n                        © 2024 DevSkills. All rights reserved.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n        </main>\n      </div>\n    </>\n  );\n};\n\nexport default AdminLogin;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { authAPI, adminAPI } from \"../utils/api\";\n\nconst AdminDashboard = () => {\n  const navigate = useNavigate();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem(\"adminToken\");\n      const userData = localStorage.getItem(\"adminUser\");\n\n      if (!token || !userData) {\n        navigate(\"/admin\");\n        return;\n      }\n\n      try {\n        // Verify token with backend\n        const { response, data } = await authAPI.getMe();\n\n        if (response.ok && data.success) {\n          setUser(data.user);\n        } else {\n          // Token invalid, redirect to login\n          localStorage.removeItem(\"adminToken\");\n          localStorage.removeItem(\"adminUser\");\n          navigate(\"/admin\");\n        }\n      } catch (error) {\n        console.error(\"Auth check failed:\", error);\n        localStorage.removeItem(\"adminToken\");\n        localStorage.removeItem(\"adminUser\");\n        navigate(\"/admin\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [navigate]);\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"adminToken\");\n    localStorage.removeItem(\"adminUser\");\n    navigate(\"/admin\");\n  };\n\n  if (loading) {\n    return (\n      <div id=\"page\" className=\"page\">\n        <main id=\"main\">\n          <section className=\"page-section\">\n            <div className=\"container relative\">\n              <div className=\"row\">\n                <div className=\"col-12 text-center\">\n                  <div className=\"loading-animation\">\n                    <iconify-icon\n                      icon=\"solar:refresh-bold\"\n                      className=\"color-primary-1\"\n                      style={{\n                        fontSize: \"3rem\",\n                        animation: \"spin 1s linear infinite\",\n                      }}\n                    ></iconify-icon>\n                    <div className=\"mt-20\">\n                      <div className=\"hs-line-4 font-alt black\">Loading...</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </section>\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <SEO\n        title=\"Admin Dashboard - DevSkills\"\n        description=\"DevSkills admin dashboard for content management\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Dashboard\">\n        {/* Stats Cards */}\n        <div className=\"row mb-40\">\n          {/* Total Posts */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:document-text-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Total Posts</div>\n              <div className=\"number-2-number\">0</div>\n            </div>\n          </div>\n\n          {/* Categories */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:folder-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Categories</div>\n              <div className=\"number-2-number\">5</div>\n            </div>\n          </div>\n\n          {/* Comments */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:chat-round-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Comments</div>\n              <div className=\"number-2-number\">0</div>\n            </div>\n          </div>\n\n          {/* Page Views */}\n          <div className=\"col-sm-6 col-lg-3 mb-md-50\">\n            <div className=\"number-2-item\">\n              <div className=\"number-2-icon\">\n                <iconify-icon icon=\"solar:eye-bold\"></iconify-icon>\n              </div>\n              <div className=\"number-2-title\">Page Views</div>\n              <div className=\"number-2-number\">-</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"mb-20\">\n              <h3 className=\"hs-line-4 font-alt black mb-20 mb-xs-10\">\n                Quick Actions\n              </h3>\n            </div>\n\n            <div className=\"row\">\n              {/* New Blog Post */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:add-circle-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">New Blog Post</h3>\n                  <div className=\"alt-features-descr\">\n                    Create a new multilingual blog post with rich content and\n                    scheduling.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/blog/new\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Create Post\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Manage Posts */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:documents-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">Manage Posts</h3>\n                  <div className=\"alt-features-descr\">\n                    Edit, publish, schedule, and organize your existing blog\n                    posts.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/posts\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Manage Posts\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Categories & Tags */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:folder-with-files-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">\n                    Categories & Tags\n                  </h3>\n                  <div className=\"alt-features-descr\">\n                    Organize your content with categories and tags for better\n                    navigation.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/categories\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Organize Content\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Comment Management */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:chat-round-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">\n                    Comment Management\n                  </h3>\n                  <div className=\"alt-features-descr\">\n                    Review, approve, and manage comments from your blog readers.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/comments\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      Manage Comments\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Analytics */}\n              <div className=\"col-sm-6 col-lg-4 mb-md-50\">\n                <div className=\"alt-features-item align-center\">\n                  <div className=\"alt-features-icon\">\n                    <iconify-icon icon=\"solar:chart-2-bold\"></iconify-icon>\n                  </div>\n                  <h3 className=\"alt-features-title font-alt\">Analytics</h3>\n                  <div className=\"alt-features-descr\">\n                    View detailed analytics and insights about your blog\n                    performance.\n                  </div>\n                  <div className=\"local-scroll mt-20\">\n                    <button\n                      onClick={() => navigate(\"/admin/analytics\")}\n                      className=\"btn btn-mod btn-color btn-round\"\n                    >\n                      View Analytics\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminDashboard;\n", "// client/src/pages/AdminBlogPosts.jsx\n\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI, blogAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminBlogPosts = () => {\n  const navigate = useNavigate();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 10,\n    status: \"all\",\n    search: \"\",\n  });\n  const [pagination, setPagination] = useState({});\n\n  useEffect(() => {\n    loadPosts();\n  }, [filters]);\n\n  const loadPosts = async () => {\n    try {\n      setLoading(true);\n\n      const params = {};\n      Object.entries(filters).forEach(([key, value]) => {\n        if (value && value !== \"all\") {\n          params[key] = value;\n        }\n      });\n\n      const { response, data } = await adminAPI.getPosts(params);\n\n      if (data.success) {\n        setPosts(data.data.posts);\n        setPagination(data.data.pagination);\n      } else {\n        setError(data.message || \"Failed to load posts\");\n      }\n    } catch (error) {\n      console.error(\"Load posts error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (postId) => {\n    if (\n      !confirm(\n        \"Are you sure you want to delete this blog post? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const { response, data } = await blogAPI.deletePost(postId);\n\n      if (data.success) {\n        loadPosts(); // Reload the list\n      } else {\n        setError(data.message || \"Failed to delete post\");\n      }\n    } catch (error) {\n      console.error(\"Delete error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleToggleVisibility = async (postId) => {\n    try {\n      const { response, data } = await blogAPI.toggleVisibility(postId);\n\n      if (data.success) {\n        loadPosts(); // Reload the list\n      } else {\n        setError(data.message || \"Failed to toggle visibility\");\n      }\n    } catch (error) {\n      console.error(\"Toggle visibility error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/blog-images/${filename}`;\n  };\n\n  const getStatusBadge = (post) => {\n    if (!post.published) {\n      return (\n        <span className=\"badge bg-secondary\">\n          <iconify-icon\n            icon=\"solar:document-text-bold\"\n            className=\"me-1\"\n          ></iconify-icon>\n          Draft\n        </span>\n      );\n    }\n\n    if (post.scheduledAt && new Date(post.scheduledAt) > new Date()) {\n      return (\n        <span className=\"badge bg-warning\">\n          <iconify-icon\n            icon=\"solar:clock-circle-bold\"\n            className=\"me-1\"\n          ></iconify-icon>\n          Scheduled\n        </span>\n      );\n    }\n\n    return (\n      <span className=\"badge bg-success\">\n        <iconify-icon\n          icon=\"solar:check-circle-bold\"\n          className=\"me-1\"\n        ></iconify-icon>\n        Published\n      </span>\n    );\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Blog Posts - Admin\"\n        description=\"Manage blog posts in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Blog Posts\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Manage your blog posts, create new content, and organize your\n                articles.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={() => navigate(\"/admin/blog/new\")}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Post\n              </button>\n            </div>\n          </div>\n        </div>\n        {/* Filters */}\n        <div className=\"admin-table mb-30\" style={{ padding: \"15px 20px\" }}>\n          <div className=\"row g-3\">\n            <div className=\"col-12 col-md-6 col-lg-4\">\n              <label className=\"form-label\">Search Posts</label>\n              <input\n                type=\"text\"\n                value={filters.search}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    search: e.target.value,\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n                placeholder=\"Search by title...\"\n              />\n            </div>\n\n            <div className=\"col-6 col-md-3 col-lg-3\">\n              <label className=\"form-label\">Status</label>\n              <select\n                value={filters.status}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    status: e.target.value,\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n              >\n                <option value=\"all\">All Posts</option>\n                <option value=\"published\">Published</option>\n                <option value=\"draft\">Drafts</option>\n              </select>\n            </div>\n\n            <div className=\"col-6 col-md-3 col-lg-2\">\n              <label className=\"form-label\">Per Page</label>\n              <select\n                value={filters.limit}\n                onChange={(e) =>\n                  setFilters((prev) => ({\n                    ...prev,\n                    limit: parseInt(e.target.value),\n                    page: 1,\n                  }))\n                }\n                className=\"form-control\"\n              >\n                <option value={10}>10</option>\n                <option value={25}>25</option>\n                <option value={50}>50</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {/* Posts Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">Loading posts...</div>\n            </div>\n          ) : posts.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:document-text-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No blog posts found\n              </div>\n              <p className=\"section-descr mb-30\">\n                {filters.search || filters.status !== \"all\"\n                  ? \"Try adjusting your search filters or create your first blog post.\"\n                  : \"Get started by creating your first blog post.\"}\n              </p>\n              <button\n                onClick={() => navigate(\"/admin/blog/new\")}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Post\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Title</th>\n                        <th>Status</th>\n                        <th>Author</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {posts.map((post) => {\n                        const englishTranslation =\n                          post.translations.find((t) => t.language === \"en\") ||\n                          post.translations[0];\n\n                        return (\n                          <tr key={post.id}>\n                            <td>\n                              <div className=\"d-flex align-items-center\">\n                                {post.featuredImage && (\n                                  <img\n                                    className=\"rounded me-3\"\n                                    src={getImageUrl(post.featuredImage)}\n                                    alt=\"\"\n                                    style={{\n                                      width: \"50px\",\n                                      height: \"50px\",\n                                      objectFit: \"cover\",\n                                    }}\n                                    onError={(e) => {\n                                      e.target.style.display = \"none\";\n                                    }}\n                                  />\n                                )}\n                                <div>\n                                  <div className=\"fw-bold\">\n                                    {englishTranslation?.title || \"Untitled\"}\n                                  </div>\n                                  <small className=\"text-muted\">\n                                    /{post.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </td>\n                            <td>\n                              {getStatusBadge(post)}\n                              {post.featured && (\n                                <span className=\"badge bg-primary ms-2\">\n                                  <iconify-icon\n                                    icon=\"solar:star-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Featured\n                                </span>\n                              )}\n                            </td>\n                            <td>{post.author.name || post.author.email}</td>\n                            <td>{formatDate(post.createdAt)}</td>\n                            <td>\n                              <div className=\"btn-group\" role=\"group\">\n                                <button\n                                  onClick={() =>\n                                    navigate(`/admin/blog/edit/${post.id}`)\n                                  }\n                                  className=\"btn btn-sm btn-outline-primary\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() =>\n                                    handleToggleVisibility(post.id)\n                                  }\n                                  className={`btn btn-sm ${\n                                    post.published\n                                      ? \"btn-outline-warning\"\n                                      : \"btn-outline-success\"\n                                  }`}\n                                  title={\n                                    post.published ? \"Unpublish\" : \"Publish\"\n                                  }\n                                >\n                                  <iconify-icon\n                                    icon={\n                                      post.published\n                                        ? \"solar:eye-closed-bold\"\n                                        : \"solar:eye-bold\"\n                                    }\n                                  ></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(post.id)}\n                                  className=\"btn btn-sm btn-outline-danger\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {posts.map((post) => {\n                    const englishTranslation =\n                      post.translations.find((t) => t.language === \"en\") ||\n                      post.translations[0];\n\n                    return (\n                      <div key={post.id} className=\"col-12\">\n                        <div className=\"card border-0 shadow-sm\">\n                          <div className=\"card-body p-3\">\n                            <div className=\"row align-items-center\">\n                              <div className=\"col-12 mb-2\">\n                                <div className=\"d-flex align-items-center\">\n                                  {post.featuredImage && (\n                                    <img\n                                      className=\"rounded me-3\"\n                                      src={getImageUrl(post.featuredImage)}\n                                      alt=\"\"\n                                      style={{\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        objectFit: \"cover\",\n                                      }}\n                                      onError={(e) => {\n                                        e.target.style.display = \"none\";\n                                      }}\n                                    />\n                                  )}\n                                  <div className=\"flex-grow-1\">\n                                    <h6 className=\"mb-1 fw-bold\">\n                                      {englishTranslation?.title || \"Untitled\"}\n                                    </h6>\n                                    <small className=\"text-muted\">\n                                      /{post.slug}\n                                    </small>\n                                  </div>\n                                </div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Status\n                                </small>\n                                <div>\n                                  {getStatusBadge(post)}\n                                  {post.featured && (\n                                    <span className=\"badge bg-primary ms-1\">\n                                      <iconify-icon\n                                        icon=\"solar:star-bold\"\n                                        className=\"me-1\"\n                                      ></iconify-icon>\n                                      Featured\n                                    </span>\n                                  )}\n                                </div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Author\n                                </small>\n                                <small>\n                                  {post.author.name || post.author.email}\n                                </small>\n                              </div>\n\n                              <div className=\"col-12 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Created\n                                </small>\n                                <small>{formatDate(post.createdAt)}</small>\n                              </div>\n\n                              <div className=\"col-12\">\n                                <div className=\"d-flex gap-2 flex-wrap\">\n                                  <button\n                                    onClick={() =>\n                                      navigate(`/admin/blog/edit/${post.id}`)\n                                    }\n                                    className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                    title=\"Edit\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:pen-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Edit\n                                  </button>\n\n                                  <button\n                                    onClick={() =>\n                                      handleToggleVisibility(post.id)\n                                    }\n                                    className={`btn btn-sm flex-fill ${\n                                      post.published\n                                        ? \"btn-outline-warning\"\n                                        : \"btn-outline-success\"\n                                    }`}\n                                    title={\n                                      post.published ? \"Unpublish\" : \"Publish\"\n                                    }\n                                  >\n                                    <iconify-icon\n                                      icon={\n                                        post.published\n                                          ? \"solar:eye-closed-bold\"\n                                          : \"solar:eye-bold\"\n                                      }\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    {post.published ? \"Hide\" : \"Show\"}\n                                  </button>\n\n                                  <button\n                                    onClick={() => handleDelete(post.id)}\n                                    className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                    title=\"Delete\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:trash-bin-trash-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Delete\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {pagination.pages > 1 && (\n          <div className=\"row mt-30 align-items-center\">\n            <div className=\"col-12 col-md-6 mb-3 mb-md-0\">\n              <p className=\"small text-muted mb-0 text-center text-md-start\">\n                Showing {(pagination.page - 1) * pagination.limit + 1} to{\" \"}\n                {Math.min(pagination.page * pagination.limit, pagination.total)}{\" \"}\n                of {pagination.total} results\n              </p>\n            </div>\n            <div className=\"col-12 col-md-6\">\n              <nav aria-label=\"Blog posts pagination\">\n                <ul className=\"pagination pagination-sm justify-content-center justify-content-md-end mb-0\">\n                  <li\n                    className={`page-item ${\n                      pagination.page <= 1 ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))\n                      }\n                      disabled={pagination.page <= 1}\n                    >\n                      Previous\n                    </button>\n                  </li>\n\n                  <li className=\"page-item active\">\n                    <span className=\"page-link\">\n                      Page {pagination.page} of {pagination.pages}\n                    </span>\n                  </li>\n\n                  <li\n                    className={`page-item ${\n                      pagination.page >= pagination.pages ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))\n                      }\n                      disabled={pagination.page >= pagination.pages}\n                    >\n                      Next\n                    </button>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminBlogPosts;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport TipTapEditor from \"../components/editor/TipTapEditor\";\nimport { adminAPI, blogAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminBlogEditor = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { id } = useParams(); // For editing existing posts\n  const isEditing = Boolean(id);\n\n  // Utility function to construct image URLs\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/blog-images/${filename}`;\n  };\n\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  // Available languages from i18n (memoized to prevent infinite re-renders)\n  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));\n\n  // Form state\n  const [formData, setFormData] = useState(() => {\n    // Initialize translations for all available languages\n    const initialTranslations = {};\n    availableLanguages.forEach((lang) => {\n      initialTranslations[lang] = {\n        title: \"\",\n        excerpt: \"\",\n        content: \"\",\n        metaTitle: \"\",\n        metaDesc: \"\",\n        keywords: [],\n      };\n    });\n\n    return {\n      slug: \"\",\n      featured: false,\n      published: false,\n      scheduledAt: \"\",\n      featuredImage: null,\n      featuredImageAlt: \"\",\n      readTime: \"\",\n      categoryIds: [],\n      tagIds: [],\n      translations: initialTranslations,\n    };\n  });\n\n  const [activeLanguage, setActiveLanguage] = useState(\"en\");\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [imagePreview, setImagePreview] = useState(null);\n\n  // Load categories and tags\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n\n        // Check if user is authenticated\n        const token = localStorage.getItem(\"adminToken\");\n        if (!token) {\n          setError(\n            \"Authentication required. Please log in to access this page.\"\n          );\n          setLoading(false);\n          return;\n        }\n\n        // Load categories and tags using the API utility\n        const [categoriesResult, tagsResult] = await Promise.all([\n          adminAPI.getCategories(),\n          adminAPI.getTags(),\n        ]);\n\n        // Handle categories response\n        if (categoriesResult.response.ok && categoriesResult.data) {\n          setCategories(categoriesResult.data.data || []);\n        } else {\n          console.error(\n            \"Categories API failed:\",\n            categoriesResult.response.status,\n            categoriesResult.response.statusText\n          );\n          if (\n            categoriesResult.response.status === 401 ||\n            categoriesResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setCategories([]);\n        }\n\n        // Handle tags response\n        if (tagsResult.response.ok && tagsResult.data) {\n          setTags(tagsResult.data.data || []);\n        } else {\n          console.error(\n            \"Tags API failed:\",\n            tagsResult.response.status,\n            tagsResult.response.statusText\n          );\n          if (\n            tagsResult.response.status === 401 ||\n            tagsResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setTags([]);\n        }\n\n        // Load existing post if editing\n        if (isEditing) {\n          const { response: postRes, data: postData } = await adminAPI.getPost(\n            id\n          );\n\n          if (postRes.ok && postData.success) {\n            try {\n              const post = postData.data;\n\n              // Convert translations array to object\n              const translationsObj = {};\n              if (post.translations && Array.isArray(post.translations)) {\n                post.translations.forEach((t) => {\n                  translationsObj[t.language] = t;\n                });\n              }\n\n              setFormData((prev) => ({\n                ...prev,\n                slug: post.slug || \"\",\n                featured: post.featured || false,\n                published: post.published || false,\n                scheduledAt: post.scheduledAt\n                  ? new Date(post.scheduledAt).toISOString().slice(0, 16)\n                  : \"\",\n                featuredImage: null,\n                featuredImageAlt: post.featuredImageAlt || \"\",\n                readTime: post.readTime || \"\",\n                categoryIds: post.categories\n                  ? post.categories.map((c) => c.id)\n                  : [],\n                tagIds: post.tags ? post.tags.map((t) => t.id) : [],\n                translations: { ...prev.translations, ...translationsObj },\n              }));\n\n              if (post.featuredImage) {\n                setImagePreview(getImageUrl(post.featuredImage));\n              }\n            } catch (jsonError) {\n              console.error(\"Failed to parse post response:\", jsonError);\n              setError(\"Failed to load post data - invalid response format\");\n            }\n          } else {\n            console.error(\n              \"Post API failed:\",\n              postRes.status,\n              postRes.statusText\n            );\n            setError(\n              postData.message ||\n                `Failed to load post: ${postRes.status} ${postRes.statusText}`\n            );\n          }\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n        if (error.message && error.message.includes(\"fetch\")) {\n          setError(\n            \"Failed to connect to the server. Please check if the backend is running on localhost:4004\"\n          );\n        } else {\n          setError(\"Failed to load data. Please try again.\");\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [id, isEditing]);\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleTranslationChange = (language, field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      translations: {\n        ...prev.translations,\n        [language]: {\n          ...prev.translations[language],\n          [field]: value,\n        },\n      },\n    }));\n  };\n\n  const handleImageChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData((prev) => ({\n        ...prev,\n        featuredImage: file,\n      }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreview(e.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const formDataToSend = new FormData();\n\n      // Add basic fields\n      formDataToSend.append(\"slug\", formData.slug);\n      formDataToSend.append(\"featured\", formData.featured);\n      formDataToSend.append(\"published\", formData.published);\n      if (formData.scheduledAt) {\n        formDataToSend.append(\"scheduledAt\", formData.scheduledAt);\n      }\n      formDataToSend.append(\"featuredImageAlt\", formData.featuredImageAlt);\n      if (formData.readTime) {\n        formDataToSend.append(\"readTime\", formData.readTime);\n      }\n\n      // Add arrays\n      formDataToSend.append(\n        \"categoryIds\",\n        JSON.stringify(formData.categoryIds)\n      );\n      formDataToSend.append(\"tagIds\", JSON.stringify(formData.tagIds));\n      formDataToSend.append(\n        \"translations\",\n        JSON.stringify(formData.translations)\n      );\n\n      // Add image if selected\n      if (formData.featuredImage) {\n        formDataToSend.append(\"featuredImage\", formData.featuredImage);\n      }\n\n      // Use the proper API utility\n      let result;\n      if (isEditing) {\n        result = await blogAPI.updatePost(id, formDataToSend);\n      } else {\n        result = await blogAPI.createPost(formDataToSend);\n      }\n\n      const { response, data } = result;\n\n      if (response.ok && data && data.success) {\n        setSuccess(\n          `Blog post ${isEditing ? \"updated\" : \"created\"} successfully!`\n        );\n        setTimeout(() => {\n          navigate(\"/admin/posts\");\n        }, 2000);\n      } else {\n        const errorMessage =\n          data?.message ||\n          `Failed to ${isEditing ? \"update\" : \"create\"} blog post`;\n        setError(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Save error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  return (\n    <>\n      <SEO\n        title={`${isEditing ? \"Edit\" : \"Create\"} Blog Post - Admin`}\n        description=\"Create or edit blog posts in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout\n        title={isEditing ? \"Edit Blog Post\" : \"Create New Blog Post\"}\n      >\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:danger-triangle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"alert alert-success mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:check-circle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {success}\n            </div>\n          )}\n\n          {/* Basic Settings */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:settings-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Basic Settings\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Configure the basic properties of your blog post\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Slug (URL)\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.slug}\n                  onChange={(e) => handleInputChange(\"slug\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"blog-post-url\"\n                />\n                <small className=\"form-text text-muted\">\n                  This will be the URL path for your blog post (e.g.,\n                  /blog/your-slug)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:clock-circle-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Read Time (minutes)\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.readTime}\n                  onChange={(e) =>\n                    handleInputChange(\"readTime\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"5\"\n                  min=\"1\"\n                  max=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  Estimated reading time for this post\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:calendar-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Schedule Publication\n                </label>\n                <input\n                  type=\"datetime-local\"\n                  value={formData.scheduledAt}\n                  onChange={(e) =>\n                    handleInputChange(\"scheduledAt\", e.target.value)\n                  }\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Leave empty to publish immediately when published is checked\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:star-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Post Options\n                </label>\n                <div className=\"d-flex flex-column gap-2\">\n                  <div className=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"featured\"\n                      checked={formData.featured}\n                      onChange={(e) =>\n                        handleInputChange(\"featured\", e.target.checked)\n                      }\n                      className=\"form-check-input\"\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"featured\">\n                      <iconify-icon\n                        icon=\"solar:star-bold\"\n                        className=\"me-1\"\n                      ></iconify-icon>\n                      Featured Post\n                    </label>\n                    <small className=\"form-text text-muted d-block\">\n                      Show this post prominently on the homepage\n                    </small>\n                  </div>\n\n                  <div className=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"published\"\n                      checked={formData.published}\n                      onChange={(e) =>\n                        handleInputChange(\"published\", e.target.checked)\n                      }\n                      className=\"form-check-input\"\n                    />\n                    <label className=\"form-check-label\" htmlFor=\"published\">\n                      <iconify-icon\n                        icon=\"solar:check-circle-bold\"\n                        className=\"me-1\"\n                      ></iconify-icon>\n                      Published\n                    </label>\n                    <small className=\"form-text text-muted d-block\">\n                      Make this post visible to the public\n                    </small>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Featured Image */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:gallery-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Featured Image\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Upload a featured image that will be displayed with your blog\n                  post\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:upload-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Upload Image\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleImageChange}\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Recommended size: 1200x630px. Supported formats: JPG, PNG,\n                  WebP\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:eye-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Alt Text\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.featuredImageAlt}\n                  onChange={(e) =>\n                    handleInputChange(\"featuredImageAlt\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Describe the image for accessibility\"\n                />\n                <small className=\"form-text text-muted\">\n                  Describe the image for screen readers and SEO\n                </small>\n              </div>\n\n              {imagePreview && (\n                <div className=\"col-12\">\n                  <div className=\"mb-20\">\n                    <label className=\"form-label\">Image Preview</label>\n                  </div>\n                  <div className=\"text-center\">\n                    <img\n                      src={imagePreview}\n                      alt=\"Preview\"\n                      className=\"image-preview\"\n                      style={{ maxWidth: \"400px\", height: \"auto\" }}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Language Tabs */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <i className=\"mi-globe me-2 color-primary-1\"></i>\n                  Content (Multi-language)\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Create content in multiple languages. At least English content\n                  is required.\n                </p>\n              </div>\n            </div>\n\n            {/* Language Selector */}\n            <div className=\"language-tabs mb-30\">\n              {availableLanguages.map((lang) => (\n                <button\n                  key={lang}\n                  type=\"button\"\n                  onClick={() => setActiveLanguage(lang)}\n                  className={`language-tab ${\n                    activeLanguage === lang ? \"active\" : \"\"\n                  }`}\n                >\n                  <i className=\"mi-globe me-2\"></i>\n                  {lang.toUpperCase()}\n                  {lang === \"en\" && (\n                    <span className=\"ms-1 small\">(Required)</span>\n                  )}\n                </button>\n              ))}\n            </div>\n\n            {/* Content for Active Language */}\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-edit me-2\"></i>\n                  Title ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.title || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"title\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Enter blog post title\"\n                  required={activeLanguage === \"en\"}\n                />\n                <small className=\"form-text text-muted\">\n                  The main title of your blog post in{\" \"}\n                  {activeLanguage.toUpperCase()}\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-text me-2\"></i>\n                  Excerpt ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.excerpt || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"excerpt\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"Brief description of the blog post\"\n                />\n                <small className=\"form-text text-muted\">\n                  A short summary that will appear in blog listings and social\n                  media previews\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:document-text-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Content ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <TipTapEditor\n                  content={formData.translations[activeLanguage]?.content || \"\"}\n                  onChange={(html) =>\n                    handleTranslationChange(activeLanguage, \"content\", html)\n                  }\n                  placeholder=\"Write your blog post content here. You can paste formatted text and code snippets with syntax highlighting.\"\n                />\n                <small className=\"form-text text-muted\">\n                  <iconify-icon\n                    icon=\"solar:info-circle-bold\"\n                    className=\"me-1\"\n                  ></iconify-icon>\n                  Rich text editor with syntax highlighting. Paste code snippets\n                  and they will be automatically highlighted. Use the toolbar\n                  for formatting options.\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Title ({activeLanguage.toUpperCase()})\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.metaTitle || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaTitle\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"SEO title (optional)\"\n                  maxLength=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-search me-1\"></i>\n                  Title that appears in search engine results (max 60\n                  characters)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-description me-2\"></i>\n                  Meta Description ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.metaDesc || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaDesc\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"SEO description (optional)\"\n                  maxLength=\"160\"\n                />\n                <small className=\"form-text text-muted\">\n                  <i className=\"mi-search me-1\"></i>\n                  Description that appears in search engine results (max 160\n                  characters)\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Categories and Tags */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:tag-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Categories & Tags\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Organize your blog post with categories and tags\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:folder-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Categories\n                </label>\n                <div className=\"categories-grid\">\n                  {categories && categories.length > 0 ? (\n                    categories.map((category) => (\n                      <div key={category.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`category-${category.id}`}\n                          checked={formData.categoryIds.includes(category.id)}\n                          onChange={() => {\n                            const newCategoryIds =\n                              formData.categoryIds.includes(category.id)\n                                ? formData.categoryIds.filter(\n                                    (id) => id !== category.id\n                                  )\n                                : [...formData.categoryIds, category.id];\n                            setFormData((prev) => ({\n                              ...prev,\n                              categoryIds: newCategoryIds,\n                            }));\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`category-${category.id}`}\n                        >\n                          {category.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No categories available</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:hashtag-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Tags\n                </label>\n                <div className=\"tags-grid\">\n                  {tags && tags.length > 0 ? (\n                    tags.map((tag) => (\n                      <div key={tag.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`tag-${tag.id}`}\n                          checked={formData.tagIds.includes(tag.id)}\n                          onChange={() => {\n                            const newTagIds = formData.tagIds.includes(tag.id)\n                              ? formData.tagIds.filter((id) => id !== tag.id)\n                              : [...formData.tagIds, tag.id];\n                            setFormData((prev) => ({\n                              ...prev,\n                              tagIds: newTagIds,\n                            }));\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`tag-${tag.id}`}\n                        >\n                          {tag.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No tags available</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"row mt-40\">\n            <div className=\"col-12 text-end\">\n              <button\n                type=\"button\"\n                onClick={() => navigate(\"/admin/posts\")}\n                className=\"btn btn-mod btn-gray btn-round me-3\"\n              >\n                Cancel\n              </button>\n\n              <button\n                type=\"submit\"\n                disabled={saving}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                {saving ? (\n                  <>\n                    <i className=\"fa fa-spinner fa-spin me-2\"></i>\n                    Saving...\n                  </>\n                ) : (\n                  <>\n                    <i className=\"mi-check me-2\"></i>\n                    {isEditing ? \"Update Post\" : \"Create Post\"}\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </form>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminBlogEditor;\n", "// client/src/pages/AdminProducts.jsx\n\nimport React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminProducts = () => {\n  const navigate = useNavigate();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [filters, setFilters] = useState({\n    page: 1,\n    limit: 10,\n    status: \"all\",\n    search: \"\",\n  });\n  const [pagination, setPagination] = useState({});\n\n  useEffect(() => {\n    loadProducts();\n  }, [filters]);\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n\n      const params = {};\n      Object.entries(filters).forEach(([key, value]) => {\n        if (value && value !== \"all\") {\n          params[key] = value;\n        }\n      });\n\n      const { response, data } = await adminAPI.getProducts(params);\n\n      if (data.success) {\n        setProducts(data.data?.products || data.products || []);\n        setPagination(data.data?.pagination || {});\n      } else {\n        setError(data.message || \"Failed to load products\");\n      }\n    } catch (error) {\n      console.error(\"Load products error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(\"Are you sure you want to delete this product?\")) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteProduct(id);\n\n      if (response.ok && data.success) {\n        setProducts(products.filter((product) => product.id !== id));\n      } else {\n        setError(data.message || \"Failed to delete product\");\n      }\n    } catch (error) {\n      console.error(\"Delete product error:\", error);\n      setError(\"Failed to delete product\");\n    }\n  };\n\n  const handleToggleVisibility = async (id) => {\n    try {\n      const product = products.find((p) => p.id === id);\n      const newStatus = product.status === \"published\" ? \"draft\" : \"published\";\n\n      const { response, data } = await adminAPI.updateProduct(id, {\n        status: newStatus,\n      });\n\n      if (response.ok && data.success) {\n        setProducts(\n          products.map((p) => (p.id === id ? { ...p, status: newStatus } : p))\n        );\n      } else {\n        setError(data.message || \"Failed to update product status\");\n      }\n    } catch (error) {\n      console.error(\"Toggle visibility error:\", error);\n      setError(\"Failed to update product status\");\n    }\n  };\n\n  const handleFilterChange = (key, value) => {\n    setFilters((prev) => ({\n      ...prev,\n      [key]: value,\n      page: 1, // Reset to first page when filtering\n    }));\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n    });\n  };\n\n  // Utility function to construct image URLs\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/product-images/${filename}`;\n  };\n\n  // Get display image from product images array\n  const getDisplayImage = (product) => {\n    if (product.images && product.images.length > 0) {\n      const displayImage =\n        product.images.find((img) => img.isDisplay) || product.images[0];\n      return displayImage.filename;\n    }\n    return product.featuredImage;\n  };\n\n  const getStatusBadge = (product) => {\n    if (product.status === \"draft\") {\n      return (\n        <span className=\"badge bg-secondary\">\n          <iconify-icon\n            icon=\"solar:document-text-bold\"\n            className=\"me-1\"\n          ></iconify-icon>\n          Draft\n        </span>\n      );\n    }\n\n    return (\n      <span className=\"badge bg-success\">\n        <iconify-icon\n          icon=\"solar:check-circle-bold\"\n          className=\"me-1\"\n        ></iconify-icon>\n        Published\n      </span>\n    );\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Products - Admin\"\n        description=\"Manage products in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Products\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Manage your webstore products, create new items, and organize\n                your catalog.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={() => navigate(\"/admin/products/new\")}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Product\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"admin-table mb-30\" style={{ padding: \"15px 20px\" }}>\n          <div className=\"row g-3\">\n            <div className=\"col-12 col-md-6 col-lg-4\">\n              <label className=\"form-label\">Search Products</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                placeholder=\"Search by title or slug...\"\n                value={filters.search}\n                onChange={(e) => handleFilterChange(\"search\", e.target.value)}\n              />\n            </div>\n\n            <div className=\"col-12 col-md-6 col-lg-4\">\n              <label className=\"form-label\">Status</label>\n              <select\n                className=\"form-control\"\n                value={filters.status}\n                onChange={(e) => handleFilterChange(\"status\", e.target.value)}\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"published\">Published</option>\n                <option value=\"draft\">Draft</option>\n              </select>\n            </div>\n\n            <div className=\"col-12 col-lg-4 d-flex align-items-end\">\n              <button\n                onClick={() =>\n                  setFilters({\n                    page: 1,\n                    limit: 10,\n                    status: \"all\",\n                    search: \"\",\n                  })\n                }\n                className=\"btn btn-mod btn-border btn-round w-100\"\n              >\n                <iconify-icon\n                  icon=\"solar:refresh-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Reset Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {/* Products Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">\n                Loading products...\n              </div>\n            </div>\n          ) : products.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:shop-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No products found\n              </div>\n              <p className=\"section-descr mb-30\">\n                {filters.search || filters.status !== \"all\"\n                  ? \"Try adjusting your search filters or create your first product.\"\n                  : \"Get started by creating your first product.\"}\n              </p>\n              <button\n                onClick={() => navigate(\"/admin/products/new\")}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Product\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Product</th>\n                        <th>Status</th>\n                        <th>Pricing</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {products.map((product) => {\n                        const englishTranslation =\n                          product.translations?.find(\n                            (t) => t.language === \"en\"\n                          ) || product.translations?.[0];\n\n                        return (\n                          <tr key={product.id}>\n                            <td>\n                              <div className=\"d-flex align-items-center\">\n                                {getDisplayImage(product) && (\n                                  <img\n                                    className=\"rounded me-3\"\n                                    src={getImageUrl(getDisplayImage(product))}\n                                    alt=\"\"\n                                    style={{\n                                      width: \"50px\",\n                                      height: \"50px\",\n                                      objectFit: \"cover\",\n                                    }}\n                                    onError={(e) => {\n                                      e.target.style.display = \"none\";\n                                    }}\n                                  />\n                                )}\n                                <div>\n                                  <div className=\"fw-bold\">\n                                    {englishTranslation?.title ||\n                                      product.title ||\n                                      \"Untitled\"}\n                                  </div>\n                                  <small className=\"text-muted\">\n                                    /{product.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </td>\n                            <td>{getStatusBadge(product)}</td>\n                            <td>\n                              <div>\n                                {product.whitelabelPrice && (\n                                  <div className=\"small\">\n                                    <strong>Whitelabel:</strong> €\n                                    {product.whitelabelPrice}\n                                  </div>\n                                )}\n                                {product.subscriptionPrice && (\n                                  <div className=\"small\">\n                                    <strong>Subscription:</strong> €\n                                    {product.subscriptionPrice}/mo\n                                  </div>\n                                )}\n                                {!product.whitelabelPrice &&\n                                  !product.subscriptionPrice && (\n                                    <span className=\"text-muted\">\n                                      No pricing set\n                                    </span>\n                                  )}\n                              </div>\n                            </td>\n                            <td>{formatDate(product.createdAt)}</td>\n                            <td>\n                              <div className=\"btn-group\" role=\"group\">\n                                <button\n                                  onClick={() =>\n                                    navigate(\n                                      `/admin/products/edit/${product.id}`\n                                    )\n                                  }\n                                  className=\"btn btn-sm btn-outline-primary\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() =>\n                                    handleToggleVisibility(product.id)\n                                  }\n                                  className={`btn btn-sm ${\n                                    product.status === \"published\"\n                                      ? \"btn-outline-warning\"\n                                      : \"btn-outline-success\"\n                                  }`}\n                                  title={\n                                    product.status === \"published\"\n                                      ? \"Unpublish\"\n                                      : \"Publish\"\n                                  }\n                                >\n                                  <iconify-icon\n                                    icon={\n                                      product.status === \"published\"\n                                        ? \"solar:eye-closed-bold\"\n                                        : \"solar:eye-bold\"\n                                    }\n                                  ></iconify-icon>\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(product.id)}\n                                  className=\"btn btn-sm btn-outline-danger\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {products.map((product) => {\n                    const englishTranslation =\n                      product.translations?.find((t) => t.language === \"en\") ||\n                      product.translations?.[0];\n\n                    return (\n                      <div key={product.id} className=\"col-12\">\n                        <div className=\"card border-0 shadow-sm\">\n                          <div className=\"card-body p-3\">\n                            <div className=\"row align-items-center\">\n                              <div className=\"col-12 mb-2\">\n                                <div className=\"d-flex align-items-center\">\n                                  {getDisplayImage(product) && (\n                                    <img\n                                      className=\"rounded me-3\"\n                                      src={getImageUrl(\n                                        getDisplayImage(product)\n                                      )}\n                                      alt=\"\"\n                                      style={{\n                                        width: \"40px\",\n                                        height: \"40px\",\n                                        objectFit: \"cover\",\n                                      }}\n                                      onError={(e) => {\n                                        e.target.style.display = \"none\";\n                                      }}\n                                    />\n                                  )}\n                                  <div className=\"flex-grow-1\">\n                                    <h6 className=\"mb-1 fw-bold\">\n                                      {englishTranslation?.title ||\n                                        product.title ||\n                                        \"Untitled\"}\n                                    </h6>\n                                    <small className=\"text-muted\">\n                                      /{product.slug}\n                                    </small>\n                                  </div>\n                                </div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Status\n                                </small>\n                                <div>{getStatusBadge(product)}</div>\n                              </div>\n\n                              <div className=\"col-6 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Pricing\n                                </small>\n                                <div>\n                                  {product.whitelabelPrice && (\n                                    <div className=\"small\">\n                                      <strong>WL:</strong> €\n                                      {product.whitelabelPrice}\n                                    </div>\n                                  )}\n                                  {product.subscriptionPrice && (\n                                    <div className=\"small\">\n                                      <strong>Sub:</strong> €\n                                      {product.subscriptionPrice}/mo\n                                    </div>\n                                  )}\n                                  {!product.whitelabelPrice &&\n                                    !product.subscriptionPrice && (\n                                      <span className=\"text-muted small\">\n                                        No pricing\n                                      </span>\n                                    )}\n                                </div>\n                              </div>\n\n                              <div className=\"col-12 col-sm-4 mb-2\">\n                                <small className=\"text-muted d-block\">\n                                  Created\n                                </small>\n                                <small>{formatDate(product.createdAt)}</small>\n                              </div>\n\n                              <div className=\"col-12\">\n                                <div className=\"d-flex gap-2 flex-wrap\">\n                                  <button\n                                    onClick={() =>\n                                      navigate(\n                                        `/admin/products/edit/${product.id}`\n                                      )\n                                    }\n                                    className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                    title=\"Edit\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:pen-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Edit\n                                  </button>\n\n                                  <button\n                                    onClick={() =>\n                                      handleToggleVisibility(product.id)\n                                    }\n                                    className={`btn btn-sm flex-fill ${\n                                      product.status === \"published\"\n                                        ? \"btn-outline-warning\"\n                                        : \"btn-outline-success\"\n                                    }`}\n                                    title={\n                                      product.status === \"published\"\n                                        ? \"Unpublish\"\n                                        : \"Publish\"\n                                    }\n                                  >\n                                    <iconify-icon\n                                      icon={\n                                        product.status === \"published\"\n                                          ? \"solar:eye-closed-bold\"\n                                          : \"solar:eye-bold\"\n                                      }\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    {product.status === \"published\"\n                                      ? \"Hide\"\n                                      : \"Show\"}\n                                  </button>\n\n                                  <button\n                                    onClick={() => handleDelete(product.id)}\n                                    className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                    title=\"Delete\"\n                                  >\n                                    <iconify-icon\n                                      icon=\"solar:trash-bin-trash-bold\"\n                                      className=\"me-1\"\n                                    ></iconify-icon>\n                                    Delete\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {pagination.pages > 1 && (\n          <div className=\"row mt-30 align-items-center\">\n            <div className=\"col-12 col-md-6 mb-3 mb-md-0\">\n              <p className=\"small text-muted mb-0 text-center text-md-start\">\n                Showing {(pagination.page - 1) * pagination.limit + 1} to{\" \"}\n                {Math.min(pagination.page * pagination.limit, pagination.total)}{\" \"}\n                of {pagination.total} results\n              </p>\n            </div>\n            <div className=\"col-12 col-md-6\">\n              <nav aria-label=\"Products pagination\">\n                <ul className=\"pagination pagination-sm justify-content-center justify-content-md-end mb-0\">\n                  <li\n                    className={`page-item ${\n                      pagination.page <= 1 ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))\n                      }\n                      disabled={pagination.page <= 1}\n                    >\n                      Previous\n                    </button>\n                  </li>\n\n                  <li className=\"page-item active\">\n                    <span className=\"page-link\">\n                      Page {pagination.page} of {pagination.pages}\n                    </span>\n                  </li>\n\n                  <li\n                    className={`page-item ${\n                      pagination.page >= pagination.pages ? \"disabled\" : \"\"\n                    }`}\n                  >\n                    <button\n                      className=\"page-link\"\n                      onClick={() =>\n                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))\n                      }\n                      disabled={pagination.page >= pagination.pages}\n                    >\n                      Next\n                    </button>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminProducts;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport TipTapEditor from \"../components/editor/TipTapEditor\";\nimport { adminAPI, API_BASE_URL } from \"../utils/api\";\n\nconst AdminProductEditor = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { id } = useParams(); // For editing existing products\n  const isEditing = Boolean(id);\n\n  // Utility function to construct image URLs\n  const getImageUrl = (filename) => {\n    if (!filename) return null;\n\n    // If it's already a full URL, return as is\n    if (filename.startsWith(\"http\")) {\n      return filename;\n    }\n\n    // Construct the full URL for uploaded images\n    const baseUrl = API_BASE_URL.replace(\"/api\", \"\");\n    return `${baseUrl}/uploads/product-images/${filename}`;\n  };\n\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n\n  // Available languages from i18n (memoized to prevent infinite re-renders)\n  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));\n\n  // Form state\n  const [formData, setFormData] = useState(() => {\n    // Initialize translations for all available languages\n    const initialTranslations = {};\n    availableLanguages.forEach((lang) => {\n      initialTranslations[lang] = {\n        title: \"\",\n        excerpt: \"\",\n        content: \"\",\n        metaTitle: \"\",\n        metaDesc: \"\",\n        keywords: [],\n      };\n    });\n\n    return {\n      slug: \"\",\n      whitelabelPrice: \"\",\n      subscriptionPrice: \"\",\n      demoUrl: \"\",\n      status: \"draft\",\n      featuredImage: null,\n      featuredImageAlt: \"\",\n      images: [], // Array of image objects\n      categoryIds: [],\n      tagIds: [],\n      translations: initialTranslations,\n    };\n  });\n\n  const [categories, setCategories] = useState([]);\n  const [tags, setTags] = useState([]);\n  const [activeLanguage, setActiveLanguage] = useState(\"en\");\n  const [imagePreview, setImagePreview] = useState(null);\n  const [selectedImages, setSelectedImages] = useState([]);\n  const [displayImageIndex, setDisplayImageIndex] = useState(0);\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Load categories and tags\n        const [categoriesResult, tagsResult] = await Promise.all([\n          adminAPI.getCategories(),\n          adminAPI.getTags(),\n        ]);\n\n        if (categoriesResult.data?.success) {\n          setCategories(categoriesResult.data.data);\n        }\n\n        if (tagsResult.data?.success) {\n          setTags(tagsResult.data.data);\n        }\n\n        // Load existing product if editing\n        if (isEditing && id) {\n          console.log(\"Loading product with ID:\", id);\n          console.log(\"Calling adminAPI.getProduct...\");\n          const productResult = await adminAPI.getProduct(id);\n          console.log(\"Product API response:\", productResult);\n\n          if (productResult.data?.success) {\n            const product = productResult.data.product;\n            console.log(\"Product data:\", product);\n\n            // Set basic fields\n            setFormData((prev) => ({\n              ...prev,\n              slug: product.slug || \"\",\n              whitelabelPrice: product.whitelabelPrice || \"\",\n              subscriptionPrice: product.subscriptionPrice || \"\",\n              demoUrl: product.demoUrl || \"\",\n              status: product.status || \"draft\",\n              featuredImageAlt: product.featuredImageAlt || \"\",\n              categoryIds: product.categories?.map((c) => c.categoryId) || [],\n              tagIds: product.tags?.map((t) => t.tagId) || [],\n            }));\n\n            // Handle translations - products have translations array, not single language\n            const updatedTranslations = { ...formData.translations };\n            if (product.translations && Array.isArray(product.translations)) {\n              product.translations.forEach((translation) => {\n                updatedTranslations[translation.language] = {\n                  title: translation.title || \"\",\n                  excerpt: translation.excerpt || \"\",\n                  content: translation.content || \"\",\n                  metaTitle: translation.metaTitle || \"\",\n                  metaDesc: translation.metaDesc || \"\",\n                  keywords: translation.keywords || [],\n                };\n              });\n            }\n\n            setFormData((prev) => ({\n              ...prev,\n              translations: updatedTranslations,\n            }));\n\n            // Load existing images\n            if (product.images && Array.isArray(product.images)) {\n              console.log(\"Loading existing images:\", product.images);\n              const existingImages = product.images.map((img, index) => {\n                const imageUrl = getImageUrl(img.filename);\n                console.log(`Image ${index}: ${img.filename} -> ${imageUrl}`);\n                return {\n                  id: img.id,\n                  file: null, // No file for existing images\n                  preview: imageUrl,\n                  alt: img.alt || \"\",\n                  isDisplay: img.isDisplay,\n                  filename: img.filename, // Store filename for existing images\n                  sortOrder: img.sortOrder,\n                };\n              });\n              console.log(\"Processed existing images:\", existingImages);\n              setSelectedImages(existingImages);\n\n              // Set display image index\n              const displayIndex = existingImages.findIndex(\n                (img) => img.isDisplay\n              );\n              if (displayIndex !== -1) {\n                setDisplayImageIndex(displayIndex);\n              }\n            } else {\n              console.log(\"No images found in product:\", product.images);\n            }\n\n            // Set legacy featured image preview if exists (fallback)\n            if (\n              product.featuredImage &&\n              (!product.images || product.images.length === 0)\n            ) {\n              setImagePreview(getImageUrl(product.featuredImage));\n            }\n          } else {\n            console.error(\"Failed to load product:\", productResult);\n            setError(\"Failed to load product data\");\n          }\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n        setError(\"Failed to load data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, [id, isEditing]);\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleTranslationChange = (language, field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      translations: {\n        ...prev.translations,\n        [language]: {\n          ...prev.translations[language],\n          [field]: value,\n        },\n      },\n    }));\n  };\n\n  const handleImagesChange = (e) => {\n    const files = Array.from(e.target.files);\n    if (files.length === 0) return;\n\n    // Check if total images would exceed 10\n    if (selectedImages.length + files.length > 10) {\n      setError(\"Maximum 10 images allowed per product\");\n      return;\n    }\n\n    const newImages = files.map((file, index) => ({\n      file,\n      preview: URL.createObjectURL(file),\n      alt: \"\",\n      isDisplay: selectedImages.length === 0 && index === 0, // First image of first upload is display\n    }));\n\n    setSelectedImages((prev) => [...prev, ...newImages]);\n    setError(\"\"); // Clear any previous errors\n  };\n\n  const removeImage = (index) => {\n    setSelectedImages((prev) => {\n      const updated = prev.filter((_, i) => i !== index);\n      // If we removed the display image, make the first image the display\n      if (prev[index]?.isDisplay && updated.length > 0) {\n        updated[0].isDisplay = true;\n        setDisplayImageIndex(0);\n      }\n      return updated;\n    });\n  };\n\n  const setDisplayImage = (index) => {\n    setSelectedImages((prev) =>\n      prev.map((img, i) => ({\n        ...img,\n        isDisplay: i === index,\n      }))\n    );\n    setDisplayImageIndex(index);\n  };\n\n  const updateImageAlt = (index, alt) => {\n    setSelectedImages((prev) =>\n      prev.map((img, i) => (i === index ? { ...img, alt } : img))\n    );\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      const token = localStorage.getItem(\"adminToken\");\n      const formDataToSend = new FormData();\n\n      // Add basic fields\n      formDataToSend.append(\"slug\", formData.slug);\n      formDataToSend.append(\"whitelabelPrice\", formData.whitelabelPrice);\n      formDataToSend.append(\"subscriptionPrice\", formData.subscriptionPrice);\n      formDataToSend.append(\"demoUrl\", formData.demoUrl);\n      formDataToSend.append(\"status\", formData.status);\n      formDataToSend.append(\"featuredImageAlt\", formData.featuredImageAlt);\n\n      // Add arrays\n      formDataToSend.append(\n        \"categoryIds\",\n        JSON.stringify(formData.categoryIds)\n      );\n      formDataToSend.append(\"tagIds\", JSON.stringify(formData.tagIds));\n      formDataToSend.append(\n        \"translations\",\n        JSON.stringify(formData.translations)\n      );\n\n      // Add images if selected\n      let imageIndex = 0;\n      selectedImages.forEach((imageData) => {\n        // Only append new images (with file), existing images are handled separately\n        if (imageData.file) {\n          formDataToSend.append(\"images\", imageData.file);\n          formDataToSend.append(`imageAlt_${imageIndex}`, imageData.alt);\n          formDataToSend.append(`isDisplay_${imageIndex}`, imageData.isDisplay);\n          imageIndex++;\n        }\n      });\n\n      // Send existing images data separately\n      const existingImages = selectedImages\n        .filter((img) => !img.file && img.id)\n        .map((img) => ({\n          id: img.id,\n          alt: img.alt,\n          isDisplay: img.isDisplay,\n          sortOrder: img.sortOrder,\n        }));\n\n      if (existingImages.length > 0) {\n        formDataToSend.append(\"existingImages\", JSON.stringify(existingImages));\n      }\n\n      // Use the proper API utility\n      let result;\n      if (isEditing) {\n        result = await adminAPI.updateProduct(id, formDataToSend);\n      } else {\n        result = await adminAPI.createProduct(formDataToSend);\n      }\n\n      const { response, data } = result;\n\n      if (response.ok && data && data.success) {\n        setSuccess(\n          `Product ${isEditing ? \"updated\" : \"created\"} successfully!`\n        );\n        setTimeout(() => {\n          navigate(\"/admin/products\");\n        }, 2000);\n      } else {\n        const errorMessage =\n          data?.message ||\n          `Failed to ${isEditing ? \"update\" : \"create\"} product`;\n        setError(errorMessage);\n      }\n    } catch (error) {\n      console.error(\"Error saving product:\", error);\n      setError(`Failed to ${isEditing ? \"update\" : \"create\"} product`);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div\n          className=\"d-flex justify-content-center align-items-center\"\n          style={{ minHeight: \"400px\" }}\n        >\n          <div className=\"text-center\">\n            <div className=\"spinner-border text-primary mb-3\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            <p className=\"text-muted\">Loading product data...</p>\n          </div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <>\n      <SEO\n        title={`${isEditing ? \"Edit\" : \"Create\"} Product | Admin`}\n        description=\"Create and manage products in the webstore\"\n      />\n      <AdminLayout title={isEditing ? \"Edit Product\" : \"Create New Product\"}>\n        <form onSubmit={handleSubmit} className=\"admin-form\">\n          {/* Messages */}\n          {error && (\n            <div className=\"alert alert-danger mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:danger-triangle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {error}\n            </div>\n          )}\n\n          {success && (\n            <div className=\"alert alert-success mb-30\" role=\"alert\">\n              <iconify-icon\n                icon=\"solar:check-circle-bold\"\n                className=\"me-2\"\n              ></iconify-icon>\n              {success}\n            </div>\n          )}\n\n          {/* Basic Settings */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:settings-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Basic Settings\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Configure the basic properties of your product\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:link-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Slug (URL)\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.slug}\n                  onChange={(e) => handleInputChange(\"slug\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"product-url-slug\"\n                />\n                <small className=\"form-text text-muted\">\n                  This will be the URL path for your product (e.g.,\n                  /webstore/your-slug)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:check-circle-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Publication Status\n                </label>\n                <select\n                  value={formData.status}\n                  onChange={(e) => handleInputChange(\"status\", e.target.value)}\n                  className=\"form-control\"\n                >\n                  <option value=\"draft\">Draft</option>\n                  <option value=\"published\">Published</option>\n                </select>\n                <small className=\"form-text text-muted\">\n                  Draft products are not visible to the public\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Pricing Information */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:dollar-minimalistic-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Pricing & Demo\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Set pricing options and demo URL for your product\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-4 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:code-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Whitelabel Price (EUR)\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={formData.whitelabelPrice}\n                  onChange={(e) =>\n                    handleInputChange(\"whitelabelPrice\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"2999.99\"\n                />\n                <small className=\"form-text text-muted\">\n                  Price for purchasing the source code with commercial license\n                </small>\n              </div>\n\n              <div className=\"col-md-4 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:refresh-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Subscription Price (EUR/month)\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={formData.subscriptionPrice}\n                  onChange={(e) =>\n                    handleInputChange(\"subscriptionPrice\", e.target.value)\n                  }\n                  className=\"form-control\"\n                  placeholder=\"99.99\"\n                />\n                <small className=\"form-text text-muted\">\n                  Monthly subscription price for SaaS access\n                </small>\n              </div>\n\n              <div className=\"col-md-4 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:eye-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Demo URL\n                </label>\n                <input\n                  type=\"url\"\n                  value={formData.demoUrl}\n                  onChange={(e) => handleInputChange(\"demoUrl\", e.target.value)}\n                  className=\"form-control\"\n                  placeholder=\"https://demo.example.com\"\n                />\n                <small className=\"form-text text-muted\">\n                  Link to live demo of the product\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Featured Image */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:gallery-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Featured Image\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Upload a featured image that will be displayed with your\n                  product\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:upload-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Upload Images (Max 10)\n                </label>\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  multiple\n                  onChange={handleImagesChange}\n                  className=\"form-control\"\n                />\n                <small className=\"form-text text-muted\">\n                  Recommended size: 800x600px. Max file size: 5MB per image.\n                  Maximum 10 images.\n                </small>\n              </div>\n\n              {selectedImages.length > 0 && (\n                <div className=\"col-12 mb-30\">\n                  <h6 className=\"mb-3\">\n                    Product Images ({selectedImages.length}/10)\n                  </h6>\n                  <div className=\"row\">\n                    {selectedImages.map((imageData, index) => (\n                      <div key={index} className=\"col-md-4 mb-3\">\n                        <div className=\"card\">\n                          <div className=\"position-relative\">\n                            <img\n                              src={imageData.preview}\n                              alt={`Preview ${index + 1}`}\n                              className=\"card-img-top\"\n                              style={{ height: \"200px\", objectFit: \"cover\" }}\n                              onError={(e) => {\n                                console.error(\n                                  \"Failed to load image:\",\n                                  imageData.preview\n                                );\n                                e.target.style.display = \"none\";\n                              }}\n                              onLoad={() => {\n                                console.log(\n                                  \"Successfully loaded image:\",\n                                  imageData.preview\n                                );\n                              }}\n                            />\n                            <button\n                              type=\"button\"\n                              className=\"btn btn-danger btn-sm position-absolute top-0 end-0 m-2\"\n                              onClick={() => removeImage(index)}\n                            >\n                              ×\n                            </button>\n                            {imageData.isDisplay && (\n                              <span className=\"badge bg-primary position-absolute top-0 start-0 m-2\">\n                                Display Image\n                              </span>\n                            )}\n                          </div>\n                          <div className=\"card-body\">\n                            <div className=\"mb-2\">\n                              <input\n                                type=\"text\"\n                                className=\"form-control form-control-sm\"\n                                placeholder=\"Alt text\"\n                                value={imageData.alt}\n                                onChange={(e) =>\n                                  updateImageAlt(index, e.target.value)\n                                }\n                              />\n                            </div>\n                            <div className=\"d-flex gap-2\">\n                              {!imageData.isDisplay && (\n                                <button\n                                  type=\"button\"\n                                  className=\"btn btn-outline-primary btn-sm\"\n                                  onClick={() => setDisplayImage(index)}\n                                >\n                                  Set as Display\n                                </button>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Language Tabs */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <i className=\"mi-globe me-2 color-primary-1\"></i>\n                  Content (Multi-language)\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Create content in multiple languages. At least English content\n                  is required.\n                </p>\n              </div>\n            </div>\n\n            {/* Language Selector */}\n            <div className=\"language-tabs mb-30\">\n              {availableLanguages.map((lang) => (\n                <button\n                  key={lang}\n                  type=\"button\"\n                  onClick={() => setActiveLanguage(lang)}\n                  className={`language-tab ${\n                    activeLanguage === lang ? \"active\" : \"\"\n                  }`}\n                >\n                  <i className=\"mi-globe me-2\"></i>\n                  {lang.toUpperCase()}\n                  {lang === \"en\" && (\n                    <span className=\"ms-1 small\">(Required)</span>\n                  )}\n                </button>\n              ))}\n            </div>\n\n            {/* Content for Active Language */}\n            <div className=\"row\">\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-edit me-2\"></i>\n                  Title ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.title || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"title\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"Enter product title\"\n                  required={activeLanguage === \"en\"}\n                />\n                <small className=\"form-text text-muted\">\n                  The main title of your product in{\" \"}\n                  {activeLanguage.toUpperCase()}\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-text me-2\"></i>\n                  Excerpt ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.excerpt || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"excerpt\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"Brief description of the product\"\n                />\n                <small className=\"form-text text-muted\">\n                  A short summary that will appear in product listings and\n                  social media previews\n                </small>\n              </div>\n\n              <div className=\"col-12 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:document-text-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Content ({activeLanguage.toUpperCase()})\n                  {activeLanguage === \"en\" && (\n                    <span className=\"text-danger ms-1\">*</span>\n                  )}\n                </label>\n                <TipTapEditor\n                  content={formData.translations[activeLanguage]?.content || \"\"}\n                  onChange={(html) =>\n                    handleTranslationChange(activeLanguage, \"content\", html)\n                  }\n                  placeholder=\"Write your product description here. You can paste formatted text and code snippets with syntax highlighting.\"\n                />\n                <small className=\"form-text text-muted\">\n                  <iconify-icon\n                    icon=\"solar:info-circle-bold\"\n                    className=\"me-1\"\n                  ></iconify-icon>\n                  Rich text editor with syntax highlighting. Paste code snippets\n                  and they will be automatically highlighted. Use the toolbar\n                  for formatting options.\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Title ({activeLanguage.toUpperCase()})\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.translations[activeLanguage]?.metaTitle || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaTitle\",\n                      e.target.value\n                    )\n                  }\n                  className=\"form-control\"\n                  placeholder=\"SEO title (optional)\"\n                  maxLength=\"60\"\n                />\n                <small className=\"form-text text-muted\">\n                  SEO title for search engines (max 60 characters)\n                </small>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <i className=\"mi-seo me-2\"></i>\n                  Meta Description ({activeLanguage.toUpperCase()})\n                </label>\n                <textarea\n                  value={formData.translations[activeLanguage]?.metaDesc || \"\"}\n                  onChange={(e) =>\n                    handleTranslationChange(\n                      activeLanguage,\n                      \"metaDesc\",\n                      e.target.value\n                    )\n                  }\n                  rows={3}\n                  className=\"form-control\"\n                  placeholder=\"SEO description (optional)\"\n                  maxLength=\"160\"\n                />\n                <small className=\"form-text text-muted\">\n                  SEO description for search engines (max 160 characters)\n                </small>\n              </div>\n            </div>\n          </div>\n\n          {/* Categories and Tags */}\n          <div className=\"admin-table mb-40\">\n            <div className=\"row mb-30\">\n              <div className=\"col-12\">\n                <h3 className=\"hs-line-4 font-alt black mb-0\">\n                  <iconify-icon\n                    icon=\"solar:tag-bold\"\n                    className=\"me-2 color-primary-1\"\n                  ></iconify-icon>\n                  Categories & Tags\n                </h3>\n                <p className=\"section-descr mb-0\">\n                  Organize your product with categories and tags\n                </p>\n              </div>\n            </div>\n\n            <div className=\"row\">\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:folder-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Categories\n                </label>\n                <div className=\"categories-grid\">\n                  {categories && categories.length > 0 ? (\n                    categories.map((category) => (\n                      <div key={category.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`category-${category.id}`}\n                          checked={formData.categoryIds.includes(category.id)}\n                          onChange={() => {\n                            const newCategoryIds =\n                              formData.categoryIds.includes(category.id)\n                                ? formData.categoryIds.filter(\n                                    (id) => id !== category.id\n                                  )\n                                : [...formData.categoryIds, category.id];\n                            handleInputChange(\"categoryIds\", newCategoryIds);\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`category-${category.id}`}\n                        >\n                          {category.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No categories available</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"col-md-6 mb-30\">\n                <label className=\"form-label\">\n                  <iconify-icon\n                    icon=\"solar:hashtag-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Tags\n                </label>\n                <div className=\"tags-grid\">\n                  {tags && tags.length > 0 ? (\n                    tags.map((tag) => (\n                      <div key={tag.id} className=\"form-check mb-2\">\n                        <input\n                          className=\"form-check-input\"\n                          type=\"checkbox\"\n                          id={`tag-${tag.id}`}\n                          checked={formData.tagIds.includes(tag.id)}\n                          onChange={() => {\n                            const newTagIds = formData.tagIds.includes(tag.id)\n                              ? formData.tagIds.filter((id) => id !== tag.id)\n                              : [...formData.tagIds, tag.id];\n                            handleInputChange(\"tagIds\", newTagIds);\n                          }}\n                        />\n                        <label\n                          className=\"form-check-label\"\n                          htmlFor={`tag-${tag.id}`}\n                        >\n                          {tag.name}\n                        </label>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-muted\">No tags available</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"row\">\n            <div className=\"col-12 text-center\">\n              <button\n                type=\"submit\"\n                className=\"btn btn-mod btn-color btn-large btn-round\"\n                disabled={saving}\n              >\n                {saving ? (\n                  <>\n                    <span className=\"spinner-border spinner-border-sm me-2\"></span>\n                    {isEditing ? \"Updating...\" : \"Creating...\"}\n                  </>\n                ) : (\n                  <>\n                    <i className=\"mi-check me-2\"></i>\n                    {isEditing ? \"Update Product\" : \"Create Product\"}\n                  </>\n                )}\n              </button>\n              <div className=\"mt-3\">\n                <small className=\"text-muted\">\n                  <i className=\"mi-info me-1\"></i>\n                  {isEditing\n                    ? \"Changes will be saved and the product will be updated\"\n                    : \"The product will be created and added to your webstore\"}\n                </small>\n              </div>\n            </div>\n          </div>\n        </form>\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminProductEditor;\n", "// client/src/pages/AdminBlogAnalytics.jsx\n\nimport React, { useState, useEffect } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport TimeRangeSelector from \"../components/analytics/TimeRangeSelector\";\nimport LanguageSelector from \"../components/analytics/LanguageSelector\";\nimport AnalyticsOverview from \"../components/analytics/AnalyticsOverview\";\nimport AnalyticsChart from \"../components/analytics/AnalyticsChart\";\nimport HeatmapChart from \"../components/analytics/HeatmapChart\";\nimport PostsTable from \"../components/analytics/PostsTable\";\nimport ConversionAnalytics from \"../components/analytics/ConversionAnalytics\";\nimport StaticPagesAnalytics from \"../components/analytics/StaticPagesAnalytics\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminBlogAnalytics = () => {\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [timeRange, setTimeRange] = useState(\"last30days\");\n  const [selectedLanguage, setSelectedLanguage] = useState(\"all\");\n  const [analyticsData, setAnalyticsData] = useState(null);\n  const [postsData, setPostsData] = useState([]);\n\n  // Time range options\n  const timeRangeOptions = [\n    { value: \"lastday\", label: \"Last day\" },\n    { value: \"lastweek\", label: \"Last week\" },\n    { value: \"last14days\", label: \"Last 14 days\" },\n    { value: \"last30days\", label: \"Last 30 days\" },\n    { value: \"last2months\", label: \"Last 2 months\" },\n    { value: \"last4months\", label: \"Last 4 months\" },\n    { value: \"last6months\", label: \"Last 6 months\" },\n  ];\n\n  // Language options for analytics\n  const languageOptions = [\n    { value: \"all\", label: \"All languages\", flag: \"🌐\" },\n    { value: \"en\", label: \"English\", flag: \"🇬🇧\" },\n    { value: \"et\", label: \"Estonian\", flag: \"🇪🇪\" },\n    { value: \"fi\", label: \"Finnish\", flag: \"🇫🇮\" },\n    { value: \"de\", label: \"German\", flag: \"🇩🇪\" },\n    { value: \"sv\", label: \"Swedish\", flag: \"🇸🇪\" },\n  ];\n\n  // Load analytics data\n  useEffect(() => {\n    const loadAnalyticsData = async () => {\n      try {\n        setLoading(true);\n        setError(\"\");\n\n        // Check if user is authenticated\n        const token = localStorage.getItem(\"adminToken\");\n        if (!token) {\n          setError(\n            \"Authentication required. Please log in to access this page.\"\n          );\n          setLoading(false);\n          return;\n        }\n\n        // Load analytics data\n        const [analyticsResult, postsResult] = await Promise.all([\n          adminAPI.getBlogAnalytics(timeRange, selectedLanguage),\n          adminAPI.getBlogPostsAnalytics(timeRange, selectedLanguage),\n        ]);\n\n        // Handle analytics response\n        if (analyticsResult.response.ok && analyticsResult.data) {\n          setAnalyticsData(analyticsResult.data.data || analyticsResult.data);\n        } else {\n          console.error(\n            \"Analytics API failed:\",\n            analyticsResult.response.status,\n            analyticsResult.response.statusText\n          );\n          if (\n            analyticsResult.response.status === 401 ||\n            analyticsResult.response.status === 403\n          ) {\n            setError(\"Authentication failed. Please log in again.\");\n            localStorage.removeItem(\"adminToken\");\n            return;\n          }\n          setError(\"Failed to load analytics data\");\n        }\n\n        // Handle posts response\n        if (postsResult.response.ok && postsResult.data) {\n          setPostsData(postsResult.data.data || postsResult.data);\n        } else {\n          console.error(\n            \"Posts analytics API failed:\",\n            postsResult.response.status,\n            postsResult.response.statusText\n          );\n          setPostsData([]);\n        }\n      } catch (error) {\n        console.error(\"Error loading analytics data:\", error);\n        if (error.message && error.message.includes(\"fetch\")) {\n          setError(\n            \"Failed to connect to the server. Please check if the backend is running.\"\n          );\n        } else {\n          setError(\"Failed to load analytics data. Please try again.\");\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadAnalyticsData();\n  }, [timeRange, selectedLanguage]);\n\n  const handleTimeRangeChange = (newTimeRange) => {\n    setTimeRange(newTimeRange);\n  };\n\n  const handleLanguageChange = (newLanguage) => {\n    setSelectedLanguage(newLanguage);\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout title=\"Blog Analytics\">\n        <SEO\n          title=\"Blog Analytics - Admin\"\n          description=\"Blog analytics and performance metrics\"\n        />\n        <div\n          className=\"d-flex justify-content-center align-items-center\"\n          style={{ minHeight: \"400px\" }}\n        >\n          <div className=\"spinner-border text-primary\" role=\"status\">\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout title=\"Analytics Dashboard\">\n      <SEO\n        title=\"Analytics Dashboard - Admin\"\n        description=\"Blog analytics, conversion tracking, and page performance metrics\"\n      />\n\n      <div className=\"admin-content\">\n        <div className=\"admin-header mb-4\">\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h1 className=\"admin-title mb-2\">Analytics Dashboard</h1>\n              <p className=\"admin-subtitle text-muted mb-0\">\n                Track and analyze your blog performance, conversions, and page\n                analytics.{\" \"}\n                <a\n                  href=\"https://developers.google.com/analytics/devguides/collection/ga4\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-primary\"\n                >\n                  Learn more\n                </a>\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"alert alert-danger mb-4\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {/* Time Range and Language Selectors */}\n        <div className=\"row mb-4\">\n          <div className=\"col-md-6\">\n            <TimeRangeSelector\n              options={timeRangeOptions}\n              value={timeRange}\n              onChange={handleTimeRangeChange}\n              comparedPeriod={analyticsData?.comparedPeriod}\n            />\n          </div>\n          <div className=\"col-md-6\">\n            <LanguageSelector\n              options={languageOptions}\n              value={selectedLanguage}\n              onChange={handleLanguageChange}\n            />\n          </div>\n        </div>\n\n        {/* Analytics Overview Cards */}\n        {analyticsData && (\n          <div className=\"row mb-4\">\n            <div className=\"col-12\">\n              <AnalyticsOverview\n                data={analyticsData.overview}\n                selectedLanguage={selectedLanguage}\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Charts Row */}\n        <div className=\"row mb-4\">\n          {/* Main Analytics Chart */}\n          <div className=\"col-lg-8 col-12 mb-4\">\n            {analyticsData && (\n              <AnalyticsChart\n                data={analyticsData.chartData}\n                timeRange={timeRange}\n                selectedLanguage={selectedLanguage}\n              />\n            )}\n          </div>\n\n          {/* Heatmap Chart */}\n          <div className=\"col-lg-4 col-12 mb-4\">\n            {analyticsData && (\n              <HeatmapChart\n                data={analyticsData.heatmapData}\n                title={`Post views by time of day${\n                  selectedLanguage !== \"all\"\n                    ? ` (${\n                        languageOptions.find(\n                          (l) => l.value === selectedLanguage\n                        )?.label\n                      })`\n                    : \"\"\n                }`}\n                selectedLanguage={selectedLanguage}\n              />\n            )}\n          </div>\n        </div>\n\n        {/* Posts Analytics Table */}\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <PostsTable\n              data={postsData}\n              loading={loading}\n              timeRange={timeRange}\n              selectedLanguage={selectedLanguage}\n            />\n          </div>\n        </div>\n\n        {/* Conversion Analytics Section */}\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title mb-0\">\n                  <iconify-icon\n                    icon=\"solar:target-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Conversion Analytics\n                </h3>\n                <p className=\"text-muted mb-0 mt-1\">\n                  Track Business Comanager CTA performance and conversion\n                  metrics\n                </p>\n              </div>\n              <div className=\"card-body\">\n                <ConversionAnalytics\n                  timeRange={timeRange}\n                  selectedLanguage={selectedLanguage}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Static Pages Analytics Section */}\n        <div className=\"row mb-4\">\n          <div className=\"col-12\">\n            <div className=\"card\">\n              <div className=\"card-header\">\n                <h3 className=\"card-title mb-0\">\n                  <iconify-icon\n                    icon=\"solar:document-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  Static Pages Analytics\n                </h3>\n                <p className=\"text-muted mb-0 mt-1\">\n                  Performance metrics for all application pages\n                </p>\n              </div>\n              <div className=\"card-body\">\n                <StaticPagesAnalytics\n                  timeRange={timeRange}\n                  selectedLanguage={selectedLanguage}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminBlogAnalytics;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminCategories = () => {\n  const navigate = useNavigate();\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [showModal, setShowModal] = useState(false);\n  const [editingCategory, setEditingCategory] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n    color: \"#4567e7\",\n  });\n\n  // Load categories on component mount\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await adminAPI.getCategories();\n\n      if (data.success) {\n        setCategories(data.data || []);\n      } else {\n        setError(data.message || \"Failed to load categories\");\n      }\n    } catch (error) {\n      console.error(\"Load categories error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      let response, data;\n\n      if (editingCategory) {\n        ({ response, data } = await adminAPI.updateCategory(\n          editingCategory.id,\n          formData\n        ));\n      } else {\n        ({ response, data } = await adminAPI.createCategory(formData));\n      }\n\n      if (data.success) {\n        setSuccess(\n          editingCategory\n            ? \"Category updated successfully!\"\n            : \"Category created successfully!\"\n        );\n        setShowModal(false);\n        setEditingCategory(null);\n        setFormData({ name: \"\", description: \"\", color: \"#4567e7\" });\n        loadCategories();\n      } else {\n        setError(data.message || \"Failed to save category\");\n      }\n    } catch (error) {\n      console.error(\"Save category error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleEdit = (category) => {\n    setEditingCategory(category);\n    setFormData({\n      name: category.name,\n      description: category.description || \"\",\n      color: category.color || \"#4567e7\",\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (categoryId) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this category? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteCategory(categoryId);\n\n      if (data.success) {\n        setSuccess(\"Category deleted successfully!\");\n        loadCategories();\n      } else {\n        setError(data.message || \"Failed to delete category\");\n      }\n    } catch (error) {\n      console.error(\"Delete category error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const openCreateModal = () => {\n    setEditingCategory(null);\n    setFormData({ name: \"\", description: \"\", color: \"#4567e7\" });\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    setEditingCategory(null);\n    setFormData({ name: \"\", description: \"\", color: \"#4567e7\" });\n    setError(\"\");\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Categories - Admin\"\n        description=\"Manage blog categories in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Categories\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Organize your blog posts with categories. Create, edit, and\n                manage content categories.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Category\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"alert alert-success mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:check-circle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {success}\n          </div>\n        )}\n\n        {/* Categories Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">\n                Loading categories...\n              </div>\n            </div>\n          ) : categories.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:folder-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No categories found\n              </div>\n              <p className=\"section-descr mb-30\">\n                Create your first category to start organizing your blog posts.\n              </p>\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Category\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Category</th>\n                        <th>Description</th>\n                        <th>Posts</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {categories.map((category) => (\n                        <tr key={category.id}>\n                          <td>\n                            <div className=\"d-flex align-items-center\">\n                              <div\n                                className=\"rounded me-3\"\n                                style={{\n                                  width: \"20px\",\n                                  height: \"20px\",\n                                  backgroundColor: category.color || \"#4567e7\",\n                                }}\n                              ></div>\n                              <div>\n                                <div className=\"fw-bold\">{category.name}</div>\n                                <small className=\"text-muted\">\n                                  /{category.slug}\n                                </small>\n                              </div>\n                            </div>\n                          </td>\n                          <td>\n                            <span className=\"text-muted\">\n                              {category.description || \"No description\"}\n                            </span>\n                          </td>\n                          <td>\n                            <span className=\"badge bg-secondary\">\n                              {category._count?.posts || 0} posts\n                            </span>\n                          </td>\n                          <td>\n                            {new Date(category.createdAt).toLocaleDateString()}\n                          </td>\n                          <td>\n                            <div className=\"btn-group\" role=\"group\">\n                              <button\n                                onClick={() => handleEdit(category)}\n                                className=\"btn btn-sm btn-outline-primary\"\n                                title=\"Edit\"\n                              >\n                                <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                              </button>\n                              <button\n                                onClick={() => handleDelete(category.id)}\n                                className=\"btn btn-sm btn-outline-danger\"\n                                title=\"Delete\"\n                              >\n                                <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {categories.map((category) => (\n                    <div key={category.id} className=\"col-12\">\n                      <div className=\"card border-0 shadow-sm\">\n                        <div className=\"card-body p-3\">\n                          <div className=\"row align-items-center\">\n                            <div className=\"col-12 mb-2\">\n                              <div className=\"d-flex align-items-center\">\n                                <div\n                                  className=\"rounded me-3\"\n                                  style={{\n                                    width: \"30px\",\n                                    height: \"30px\",\n                                    backgroundColor:\n                                      category.color || \"#4567e7\",\n                                  }}\n                                ></div>\n                                <div className=\"flex-grow-1\">\n                                  <h6 className=\"mb-1 fw-bold\">\n                                    {category.name}\n                                  </h6>\n                                  <small className=\"text-muted\">\n                                    /{category.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </div>\n\n                            <div className=\"col-6 col-sm-4 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Description\n                              </small>\n                              <small>\n                                {category.description || \"No description\"}\n                              </small>\n                            </div>\n\n                            <div className=\"col-6 col-sm-4 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Posts\n                              </small>\n                              <span className=\"badge bg-secondary\">\n                                {category._count?.posts || 0} posts\n                              </span>\n                            </div>\n\n                            <div className=\"col-12 col-sm-4 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Created\n                              </small>\n                              <small>\n                                {new Date(\n                                  category.createdAt\n                                ).toLocaleDateString()}\n                              </small>\n                            </div>\n\n                            <div className=\"col-12\">\n                              <div className=\"d-flex gap-2 flex-wrap\">\n                                <button\n                                  onClick={() => handleEdit(category)}\n                                  className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:pen-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Edit\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(category.id)}\n                                  className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:trash-bin-trash-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Delete\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Modal */}\n        {showModal && (\n          <div className=\"modal-overlay\" onClick={closeModal}>\n            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"modal-header\">\n                <h4 className=\"modal-title\">\n                  <iconify-icon\n                    icon=\"solar:folder-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  {editingCategory ? \"Edit Category\" : \"Create New Category\"}\n                </h4>\n                <button\n                  type=\"button\"\n                  className=\"modal-close\"\n                  onClick={closeModal}\n                >\n                  <iconify-icon icon=\"solar:close-circle-bold\"></iconify-icon>\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div className=\"modal-body\">\n                  <div className=\"row\">\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:pen-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Category Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) =>\n                          handleInputChange(\"name\", e.target.value)\n                        }\n                        className=\"form-control\"\n                        placeholder=\"Enter category name\"\n                        required\n                      />\n                    </div>\n\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:text-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Description\n                      </label>\n                      <textarea\n                        value={formData.description}\n                        onChange={(e) =>\n                          handleInputChange(\"description\", e.target.value)\n                        }\n                        className=\"form-control\"\n                        rows={3}\n                        placeholder=\"Brief description of this category\"\n                      />\n                    </div>\n\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:palette-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Color\n                      </label>\n                      <div className=\"d-flex align-items-center gap-3\">\n                        <input\n                          type=\"color\"\n                          value={formData.color}\n                          onChange={(e) =>\n                            handleInputChange(\"color\", e.target.value)\n                          }\n                          className=\"form-control form-control-color\"\n                          style={{ width: \"60px\", height: \"40px\" }}\n                        />\n                        <input\n                          type=\"text\"\n                          value={formData.color}\n                          onChange={(e) =>\n                            handleInputChange(\"color\", e.target.value)\n                          }\n                          className=\"form-control\"\n                          placeholder=\"#4567e7\"\n                        />\n                      </div>\n                      <small className=\"form-text text-muted\">\n                        Choose a color to represent this category\n                      </small>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-mod btn-gray btn-round me-3\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-mod btn-color btn-round\"\n                  >\n                    <iconify-icon\n                      icon=\"solar:check-circle-bold\"\n                      className=\"me-2\"\n                    ></iconify-icon>\n                    {editingCategory ? \"Update Category\" : \"Create Category\"}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminCategories;\n", "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport SEO from \"../components/common/SEO\";\nimport AdminLayout from \"../components/admin/AdminLayout\";\nimport { adminAPI } from \"../utils/api\";\n\nconst AdminTags = () => {\n  const navigate = useNavigate();\n  const [tags, setTags] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(\"\");\n  const [showModal, setShowModal] = useState(false);\n  const [editingTag, setEditingTag] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n  });\n\n  // Load tags on component mount\n  useEffect(() => {\n    loadTags();\n  }, []);\n\n  const loadTags = async () => {\n    try {\n      setLoading(true);\n      const { response, data } = await adminAPI.getTags();\n\n      if (data.success) {\n        setTags(data.data || []);\n      } else {\n        setError(data.message || \"Failed to load tags\");\n      }\n    } catch (error) {\n      console.error(\"Load tags error:\", error);\n      setError(\"Network error. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData((prev) => ({\n      ...prev,\n      [field]: value,\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError(\"\");\n    setSuccess(\"\");\n\n    try {\n      let response, data;\n\n      if (editingTag) {\n        ({ response, data } = await adminAPI.updateTag(\n          editingTag.id,\n          formData\n        ));\n      } else {\n        ({ response, data } = await adminAPI.createTag(formData));\n      }\n\n      if (data.success) {\n        setSuccess(\n          editingTag ? \"Tag updated successfully!\" : \"Tag created successfully!\"\n        );\n        setShowModal(false);\n        setEditingTag(null);\n        setFormData({ name: \"\" });\n        loadTags();\n      } else {\n        setError(data.message || \"Failed to save tag\");\n      }\n    } catch (error) {\n      console.error(\"Save tag error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const handleEdit = (tag) => {\n    setEditingTag(tag);\n    setFormData({\n      name: tag.name,\n    });\n    setShowModal(true);\n  };\n\n  const handleDelete = async (tagId) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this tag? This action cannot be undone.\"\n      )\n    ) {\n      return;\n    }\n\n    try {\n      const { response, data } = await adminAPI.deleteTag(tagId);\n\n      if (data.success) {\n        setSuccess(\"Tag deleted successfully!\");\n        loadTags();\n      } else {\n        setError(data.message || \"Failed to delete tag\");\n      }\n    } catch (error) {\n      console.error(\"Delete tag error:\", error);\n      setError(\"Network error. Please try again.\");\n    }\n  };\n\n  const openCreateModal = () => {\n    setEditingTag(null);\n    setFormData({ name: \"\" });\n    setShowModal(true);\n  };\n\n  const closeModal = () => {\n    setShowModal(false);\n    setEditingTag(null);\n    setFormData({ name: \"\" });\n    setError(\"\");\n  };\n\n  return (\n    <>\n      <SEO\n        title=\"Manage Tags - Admin\"\n        description=\"Manage blog tags in the admin panel\"\n        noIndex={true}\n      />\n\n      <AdminLayout title=\"Tags\">\n        {/* Action Bar */}\n        <div className=\"mb-30\">\n          <div className=\"row align-items-center\">\n            <div className=\"col-12 col-lg-6 mb-3 mb-lg-0\">\n              <p className=\"section-descr mb-0\">\n                Tag your blog posts for better organization and discoverability.\n                Create and manage content tags.\n              </p>\n            </div>\n            <div className=\"col-12 col-lg-6 text-lg-end\">\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round w-100 w-lg-auto\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                New Tag\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        {error && (\n          <div className=\"alert alert-danger mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:danger-triangle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"alert alert-success mb-30\" role=\"alert\">\n            <iconify-icon\n              icon=\"solar:check-circle-bold\"\n              className=\"me-2\"\n            ></iconify-icon>\n            {success}\n          </div>\n        )}\n\n        {/* Tags Table */}\n        <div className=\"admin-table\">\n          {loading ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:refresh-bold\"\n                className=\"fa-2x color-primary-1 mb-20\"\n                style={{ animation: \"spin 1s linear infinite\" }}\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black\">Loading tags...</div>\n            </div>\n          ) : tags.length === 0 ? (\n            <div className=\"text-center py-60\" style={{ padding: \"40px 20px\" }}>\n              <iconify-icon\n                icon=\"solar:tag-bold\"\n                className=\"fa-3x color-gray-light-1 mb-20\"\n              ></iconify-icon>\n              <div className=\"hs-line-4 font-alt black mb-10\">\n                No tags found\n              </div>\n              <p className=\"section-descr mb-30\">\n                Create your first tag to start organizing your blog posts.\n              </p>\n              <button\n                onClick={openCreateModal}\n                className=\"btn btn-mod btn-color btn-round\"\n              >\n                <iconify-icon\n                  icon=\"solar:add-circle-bold\"\n                  className=\"me-2\"\n                ></iconify-icon>\n                Create First Tag\n              </button>\n            </div>\n          ) : (\n            <>\n              {/* Desktop Table View */}\n              <div className=\"d-none d-lg-block\">\n                <div className=\"table-responsive\">\n                  <table className=\"table\">\n                    <thead>\n                      <tr>\n                        <th>Tag Name</th>\n                        <th>Posts</th>\n                        <th>Created</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {tags.map((tag) => (\n                        <tr key={tag.id}>\n                          <td>\n                            <div className=\"d-flex align-items-center\">\n                              <iconify-icon\n                                icon=\"solar:tag-bold\"\n                                className=\"me-3 color-primary-1\"\n                              ></iconify-icon>\n                              <div>\n                                <div className=\"fw-bold\">{tag.name}</div>\n                                <small className=\"text-muted\">\n                                  /{tag.slug}\n                                </small>\n                              </div>\n                            </div>\n                          </td>\n                          <td>\n                            <span className=\"badge bg-secondary\">\n                              {tag._count?.posts || 0} posts\n                            </span>\n                          </td>\n                          <td>\n                            {new Date(tag.createdAt).toLocaleDateString()}\n                          </td>\n                          <td>\n                            <div className=\"btn-group\" role=\"group\">\n                              <button\n                                onClick={() => handleEdit(tag)}\n                                className=\"btn btn-sm btn-outline-primary\"\n                                title=\"Edit\"\n                              >\n                                <iconify-icon icon=\"solar:pen-bold\"></iconify-icon>\n                              </button>\n                              <button\n                                onClick={() => handleDelete(tag.id)}\n                                className=\"btn btn-sm btn-outline-danger\"\n                                title=\"Delete\"\n                              >\n                                <iconify-icon icon=\"solar:trash-bin-trash-bold\"></iconify-icon>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n\n              {/* Mobile Card View */}\n              <div className=\"d-lg-none\">\n                <div className=\"row g-3\">\n                  {tags.map((tag) => (\n                    <div key={tag.id} className=\"col-12\">\n                      <div className=\"card border-0 shadow-sm\">\n                        <div className=\"card-body p-3\">\n                          <div className=\"row align-items-center\">\n                            <div className=\"col-12 mb-2\">\n                              <div className=\"d-flex align-items-center\">\n                                <iconify-icon\n                                  icon=\"solar:tag-bold\"\n                                  className=\"me-3 color-primary-1\"\n                                  style={{ fontSize: \"1.5rem\" }}\n                                ></iconify-icon>\n                                <div className=\"flex-grow-1\">\n                                  <h6 className=\"mb-1 fw-bold\">{tag.name}</h6>\n                                  <small className=\"text-muted\">\n                                    /{tag.slug}\n                                  </small>\n                                </div>\n                              </div>\n                            </div>\n\n                            <div className=\"col-6 col-sm-6 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Posts\n                              </small>\n                              <span className=\"badge bg-secondary\">\n                                {tag._count?.posts || 0} posts\n                              </span>\n                            </div>\n\n                            <div className=\"col-6 col-sm-6 mb-2\">\n                              <small className=\"text-muted d-block\">\n                                Created\n                              </small>\n                              <small>\n                                {new Date(tag.createdAt).toLocaleDateString()}\n                              </small>\n                            </div>\n\n                            <div className=\"col-12\">\n                              <div className=\"d-flex gap-2 flex-wrap\">\n                                <button\n                                  onClick={() => handleEdit(tag)}\n                                  className=\"btn btn-sm btn-outline-primary flex-fill\"\n                                  title=\"Edit\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:pen-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Edit\n                                </button>\n\n                                <button\n                                  onClick={() => handleDelete(tag.id)}\n                                  className=\"btn btn-sm btn-outline-danger flex-fill\"\n                                  title=\"Delete\"\n                                >\n                                  <iconify-icon\n                                    icon=\"solar:trash-bin-trash-bold\"\n                                    className=\"me-1\"\n                                  ></iconify-icon>\n                                  Delete\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Modal */}\n        {showModal && (\n          <div className=\"modal-overlay\" onClick={closeModal}>\n            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n              <div className=\"modal-header\">\n                <h4 className=\"modal-title\">\n                  <iconify-icon\n                    icon=\"solar:tag-bold\"\n                    className=\"me-2\"\n                  ></iconify-icon>\n                  {editingTag ? \"Edit Tag\" : \"Create New Tag\"}\n                </h4>\n                <button\n                  type=\"button\"\n                  className=\"modal-close\"\n                  onClick={closeModal}\n                >\n                  <iconify-icon icon=\"solar:close-circle-bold\"></iconify-icon>\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit}>\n                <div className=\"modal-body\">\n                  <div className=\"row\">\n                    <div className=\"col-12 mb-20\">\n                      <label className=\"form-label\">\n                        <iconify-icon\n                          icon=\"solar:pen-bold\"\n                          className=\"me-2\"\n                        ></iconify-icon>\n                        Tag Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.name}\n                        onChange={(e) =>\n                          handleInputChange(\"name\", e.target.value)\n                        }\n                        className=\"form-control\"\n                        placeholder=\"Enter tag name\"\n                        required\n                      />\n                      <small className=\"form-text text-muted\">\n                        Keep it short and descriptive (e.g., \"JavaScript\",\n                        \"Tutorial\", \"News\")\n                      </small>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"modal-footer\">\n                  <button\n                    type=\"button\"\n                    onClick={closeModal}\n                    className=\"btn btn-mod btn-gray btn-round me-3\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"btn btn-mod btn-color btn-round\"\n                  >\n                    <iconify-icon\n                      icon=\"solar:check-circle-bold\"\n                      className=\"me-2\"\n                    ></iconify-icon>\n                    {editingTag ? \"Update Tag\" : \"Create Tag\"}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        )}\n      </AdminLayout>\n    </>\n  );\n};\n\nexport default AdminTags;\n", "import { apiCall } from \"./api\";\n\n// Get comments for admin\nexport const getAdminComments = async (params = {}) => {\n  try {\n    const queryParams = new URLSearchParams();\n\n    if (params.page) queryParams.append(\"page\", params.page);\n    if (params.limit) queryParams.append(\"limit\", params.limit);\n    if (params.status) queryParams.append(\"status\", params.status);\n    if (params.search) queryParams.append(\"search\", params.search);\n    if (params.blogPostId) queryParams.append(\"blogPostId\", params.blogPostId);\n\n    const { response, data } = await apiCall(`/admin/comments?${queryParams}`);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Get admin comments error:\", error);\n    throw error;\n  }\n};\n\n// Approve comment\nexport const approveComment = async (commentId) => {\n  try {\n    const { response, data } = await apiCall(\n      `/admin/comments/${commentId}/approve`,\n      {\n        method: \"PATCH\",\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Approve comment error:\", error);\n    throw error;\n  }\n};\n\n// Reject/Hide comment\nexport const rejectComment = async (commentId) => {\n  try {\n    const { response, data } = await apiCall(\n      `/admin/comments/${commentId}/reject`,\n      {\n        method: \"PATCH\",\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Reject comment error:\", error);\n    throw error;\n  }\n};\n\n// Delete comment\nexport const deleteComment = async (commentId) => {\n  try {\n    const { response, data } = await apiCall(`/admin/comments/${commentId}`, {\n      method: \"DELETE\",\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error(\"Delete comment error:\", error);\n    throw error;\n  }\n};\n\n// Get comment statistics\nexport const getCommentStats = async () => {\n  try {\n    const { response, data: result } = await apiCall(\"/admin/comments?limit=1\");\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    // Get counts for different statuses\n    const [pendingResult, approvedResult] = await Promise.all([\n      apiCall(\"/admin/comments?status=pending&limit=1\"),\n      apiCall(\"/admin/comments?status=approved&limit=1\"),\n    ]);\n\n    return {\n      total: result.data.pagination.total,\n      pending: pendingResult.data.data.pagination.total,\n      approved: approvedResult.data.data.pagination.total,\n    };\n  } catch (error) {\n    console.error(\"Get comment stats error:\", error);\n    throw error;\n  }\n};\n", "import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport AdminLayout from '@/components/admin/AdminLayout';\nimport { getAdminComments, approveComment, rejectComment, deleteComment } from '@/utils/commentAPI';\n\nexport default function AdminCommentsPage() {\n  const [comments, setComments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [stats, setStats] = useState({ total: 0, pending: 0, approved: 0 });\n\n  const fetchComments = async () => {\n    try {\n      setLoading(true);\n      const response = await getAdminComments({\n        page: currentPage,\n        limit: 10,\n        status: statusFilter,\n        search: searchTerm,\n      });\n\n      if (response.success) {\n        setComments(response.data.comments);\n        setTotalPages(response.data.pagination.pages);\n        \n        // Update stats\n        const totalComments = response.data.pagination.total;\n        setStats(prev => ({ ...prev, total: totalComments }));\n      }\n    } catch (err) {\n      setError('Failed to fetch comments');\n      console.error('Fetch comments error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchComments();\n  }, [currentPage, statusFilter, searchTerm]);\n\n  const handleApprove = async (commentId) => {\n    try {\n      await approveComment(commentId);\n      fetchComments(); // Refresh the list\n    } catch (err) {\n      setError('Failed to approve comment');\n    }\n  };\n\n  const handleReject = async (commentId) => {\n    try {\n      await rejectComment(commentId);\n      fetchComments(); // Refresh the list\n    } catch (err) {\n      setError('Failed to reject comment');\n    }\n  };\n\n  const handleDelete = async (commentId) => {\n    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {\n      try {\n        await deleteComment(commentId);\n        fetchComments(); // Refresh the list\n      } catch (err) {\n        setError('Failed to delete comment');\n      }\n    }\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    setCurrentPage(1);\n    fetchComments();\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const truncateText = (text, maxLength = 100) => {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  };\n\n  return (\n    <AdminLayout>\n      <div className=\"container-fluid\">\n        <div className=\"row\">\n          <div className=\"col-12\">\n            <div className=\"d-flex justify-content-between align-items-center mb-4\">\n              <h1 className=\"h3 mb-0\">Comment Management</h1>\n              <div className=\"d-flex gap-3\">\n                <div className=\"badge bg-primary\">Total: {stats.total}</div>\n                <div className=\"badge bg-warning\">Pending: {stats.pending}</div>\n                <div className=\"badge bg-success\">Approved: {stats.approved}</div>\n              </div>\n            </div>\n\n            {error && (\n              <div className=\"alert alert-danger\" role=\"alert\">\n                {error}\n              </div>\n            )}\n\n            {/* Filters */}\n            <div className=\"card mb-4\">\n              <div className=\"card-body\">\n                <div className=\"row g-3\">\n                  <div className=\"col-md-4\">\n                    <label className=\"form-label\">Status Filter</label>\n                    <select\n                      className=\"form-select\"\n                      value={statusFilter}\n                      onChange={(e) => {\n                        setStatusFilter(e.target.value);\n                        setCurrentPage(1);\n                      }}\n                    >\n                      <option value=\"all\">All Comments</option>\n                      <option value=\"pending\">Pending Approval</option>\n                      <option value=\"approved\">Approved</option>\n                    </select>\n                  </div>\n                  <div className=\"col-md-8\">\n                    <label className=\"form-label\">Search</label>\n                    <form onSubmit={handleSearch} className=\"d-flex\">\n                      <input\n                        type=\"text\"\n                        className=\"form-control\"\n                        placeholder=\"Search by author name, email, or content...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                      />\n                      <button type=\"submit\" className=\"btn btn-primary ms-2\">\n                        Search\n                      </button>\n                    </form>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Comments Table */}\n            <div className=\"card\">\n              <div className=\"card-body\">\n                {loading ? (\n                  <div className=\"text-center py-4\">\n                    <div className=\"spinner-border\" role=\"status\">\n                      <span className=\"visually-hidden\">Loading...</span>\n                    </div>\n                  </div>\n                ) : comments.length === 0 ? (\n                  <div className=\"text-center py-4\">\n                    <p className=\"text-muted\">No comments found.</p>\n                  </div>\n                ) : (\n                  <div className=\"table-responsive\">\n                    <table className=\"table table-hover\">\n                      <thead>\n                        <tr>\n                          <th>Author</th>\n                          <th>Email</th>\n                          <th>Content</th>\n                          <th>Blog Post</th>\n                          <th>Status</th>\n                          <th>Date</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {comments.map((comment) => (\n                          <tr key={comment.id}>\n                            <td>\n                              <strong>{comment.author}</strong>\n                              {comment.website && (\n                                <div>\n                                  <small>\n                                    <a href={comment.website} target=\"_blank\" rel=\"noopener noreferrer\">\n                                      {comment.website}\n                                    </a>\n                                  </small>\n                                </div>\n                              )}\n                            </td>\n                            <td>\n                              <small className=\"text-muted\">{comment.email}</small>\n                            </td>\n                            <td>\n                              <div title={comment.content}>\n                                {truncateText(comment.content)}\n                              </div>\n                              {comment.parent && (\n                                <small className=\"text-muted\">\n                                  Reply to: {comment.parent.author}\n                                </small>\n                              )}\n                            </td>\n                            <td>\n                              <Link \n                                to={`/blog-single/${comment.blogPost.slug}`}\n                                className=\"text-decoration-none\"\n                                target=\"_blank\"\n                              >\n                                {comment.blogPost.translations[0]?.title || 'Untitled'}\n                              </Link>\n                            </td>\n                            <td>\n                              <span className={`badge ${comment.approved ? 'bg-success' : 'bg-warning'}`}>\n                                {comment.approved ? 'Approved' : 'Pending'}\n                              </span>\n                            </td>\n                            <td>\n                              <small>{formatDate(comment.createdAt)}</small>\n                            </td>\n                            <td>\n                              <div className=\"btn-group btn-group-sm\">\n                                {!comment.approved ? (\n                                  <button\n                                    className=\"btn btn-success\"\n                                    onClick={() => handleApprove(comment.id)}\n                                    title=\"Approve\"\n                                  >\n                                    <i className=\"mi-check\"></i>\n                                  </button>\n                                ) : (\n                                  <button\n                                    className=\"btn btn-warning\"\n                                    onClick={() => handleReject(comment.id)}\n                                    title=\"Hide\"\n                                  >\n                                    <i className=\"mi-eye-off\"></i>\n                                  </button>\n                                )}\n                                <button\n                                  className=\"btn btn-danger\"\n                                  onClick={() => handleDelete(comment.id)}\n                                  title=\"Delete\"\n                                >\n                                  <i className=\"mi-trash\"></i>\n                                </button>\n                              </div>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                )}\n\n                {/* Pagination */}\n                {totalPages > 1 && (\n                  <nav className=\"mt-4\">\n                    <ul className=\"pagination justify-content-center\">\n                      <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>\n                        <button\n                          className=\"page-link\"\n                          onClick={() => setCurrentPage(currentPage - 1)}\n                          disabled={currentPage === 1}\n                        >\n                          Previous\n                        </button>\n                      </li>\n                      {[...Array(totalPages)].map((_, index) => (\n                        <li key={index + 1} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>\n                          <button\n                            className=\"page-link\"\n                            onClick={() => setCurrentPage(index + 1)}\n                          >\n                            {index + 1}\n                          </button>\n                        </li>\n                      ))}\n                      <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>\n                        <button\n                          className=\"page-link\"\n                          onClick={() => setCurrentPage(currentPage + 1)}\n                          disabled={currentPage === totalPages}\n                        >\n                          Next\n                        </button>\n                      </li>\n                    </ul>\n                  </nav>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n}\n"], "names": ["AdminLogin", "t", "useTranslation", "navigate", "useNavigate", "formData", "setFormData", "useState", "loading", "setLoading", "error", "setError", "handleChange", "e", "handleSubmit", "response", "data", "authAPI", "err", "jsxs", "Fragment", "jsx", "SEO", "AdminDashboard", "user", "setUser", "useEffect", "token", "userData", "AdminLayout", "AdminBlogPosts", "posts", "setPosts", "filters", "setFilters", "pagination", "setPagination", "loadPosts", "params", "key", "value", "adminAPI", "handleDelete", "postId", "blogAPI", "handleToggleVisibility", "formatDate", "dateString", "getImageUrl", "filename", "API_BASE_URL", "getStatusBadge", "post", "prev", "englishTranslation", "AdminBlogEditor", "i18n", "id", "useParams", "isEditing", "saving", "setSaving", "success", "setSuccess", "availableLanguages", "initialTranslations", "lang", "activeLanguage", "setActiveLanguage", "categories", "setCategories", "tags", "setTags", "imagePreview", "setImagePreview", "categoriesResult", "tagsResult", "postRes", "postData", "translationsObj", "c", "jsonError", "handleInputChange", "field", "handleTranslationChange", "language", "handleImageChange", "file", "reader", "formDataToSend", "result", "errorMessage", "_a", "_b", "TipTapEditor", "_c", "html", "_d", "_e", "category", "newCategoryIds", "tag", "newTagIds", "AdminProducts", "products", "setProducts", "loadProducts", "product", "newStatus", "p", "handleFilterChange", "getDisplayImage", "img", "AdminProductEditor", "selectedImages", "setSelectedImages", "displayImageIndex", "setDisplayImageIndex", "productResult", "updatedTranslations", "translation", "existingImages", "index", "imageUrl", "displayIndex", "handleImagesChange", "files", "newImages", "removeImage", "updated", "_", "i", "setDisplayImage", "updateImageAlt", "alt", "imageIndex", "imageData", "AdminBlogAnalytics", "timeRange", "setTimeRange", "selectedLanguage", "setSelectedLanguage", "analyticsData", "setAnalyticsData", "postsData", "setPostsData", "timeRangeOptions", "languageOptions", "analyticsResult", "postsResult", "handleTimeRangeChange", "newTimeRange", "handleLanguageChange", "newLanguage", "TimeRangeSelector", "LanguageSelector", "AnalyticsOverview", "AnalyticsChart", "HeatmapChart", "l", "PostsTable", "ConversionAnalytics", "StaticPagesAnalytics", "AdminCategories", "showModal", "setShowModal", "editingCategory", "setEditingCategory", "loadCategories", "handleEdit", "categoryId", "openCreateModal", "closeModal", "AdminTags", "editingTag", "setEditingTag", "loadTags", "tagId", "getAdminComments", "queryParams", "apiCall", "approveComment", "commentId", "rejectComment", "deleteComment", "AdminCommentsPage", "comments", "setComments", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "statusFilter", "setStatus<PERSON>ilter", "searchTerm", "setSearchTerm", "stats", "setStats", "fetchComments", "totalComments", "handleApprove", "handleReject", "handleSearch", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "comment", "Link"], "mappings": "+PAMA,MAAMA,GAAa,IAAM,CACjB,KAAA,CAAE,EAAAC,CAAE,EAAIC,GAAe,EACvBC,EAAWC,EAAY,EACvB,CAACC,EAAUC,CAAW,EAAIC,WAAS,CACvC,MAAO,GACP,SAAU,EAAA,CACX,EACK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAE/BK,EAAgBC,GAAM,CACdP,EAAA,CACV,GAAGD,EACH,CAACQ,EAAE,OAAO,IAAI,EAAGA,EAAE,OAAO,KAAA,CAC3B,EAEGH,KAAgB,EAAE,CACxB,EAEMI,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBJ,EAAW,EAAI,EACfE,EAAS,EAAE,EAEP,GAAA,CACF,KAAM,CAAE,SAAAI,EAAU,KAAAC,CAAA,EAAS,MAAMC,GAAQ,MAAMZ,CAAQ,EAEnDW,EAAK,SAEM,aAAA,QAAQ,aAAcA,EAAK,KAAK,EAC7C,aAAa,QAAQ,YAAa,KAAK,UAAUA,EAAK,IAAI,CAAC,EAG3Db,EAAS,kBAAkB,GAElBQ,EAAAK,EAAK,SAAW,cAAc,QAElCE,EAAK,CACJ,QAAA,MAAM,eAAgBA,CAAG,EACjCP,EAAS,kCAAkC,CAAA,QAC3C,CACAF,EAAW,EAAK,CAAA,CAEpB,EAEA,OAEIU,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,0BACN,YAAY,oDACZ,QAAS,EAAA,CACX,EAGAD,EAAAA,IAAC,OAAI,GAAG,OAAO,UAAU,OAEvB,SAAAA,EAAAA,IAAC,OAAK,CAAA,GAAG,OAEP,SAAAA,EAAA,IAAC,UAAA,CACC,UAAU,4EACV,GAAG,cAEH,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,qBACb,eAAC,MAAI,CAAA,UAAU,MACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,4CAEb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0CACb,SAAA,CAACE,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAS,YAAA,EAAO,QAAA,EACpD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,uCAAA,CAAA,CAAA,EACF,EAGCX,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,YAAa,CAAA,EACzBX,CAAA,EACH,EAIDS,EAAA,KAAA,OAAA,CAAK,UAAU,oBAAoB,SAAUL,EAE5C,SAAA,CAACK,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAE,MAAC,QAAM,CAAA,QAAQ,QAAQ,UAAU,UAAU,SAE3C,gBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,QACL,KAAK,QACL,GAAG,QACH,UAAU,8BACV,YAAY,gBACZ,MAAOhB,EAAS,MAChB,SAAUO,EACV,SAAQ,GACR,aAAa,OAAA,CAAA,CACf,EACF,EAGAO,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,MAAC,QAAM,CAAA,QAAQ,WAAW,UAAU,UAAU,SAE9C,WAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,WACL,KAAK,WACL,GAAG,WACH,UAAU,8BACV,YAAY,WACZ,MAAOhB,EAAS,SAChB,SAAUO,EACV,SAAQ,GACR,aAAa,kBAAA,CAAA,CACf,EACF,EAGAS,EAAAA,IAAC,MAAI,CAAA,UAAU,aACb,SAAAA,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,2DACV,SAAUb,EAET,WAEGW,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,4BAA6B,CAAA,EAAI,eAAA,CAAA,CAEhD,EAGEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,SAAA,CAElC,CAAA,CAAA,CAAA,CAGN,CAAA,CAAA,EACF,EAGAA,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,IAAE,CAAA,UAAU,mBAAmB,SAAA,wCAEhC,CAAA,CACF,CAAA,CAAA,EACF,CAAA,CACF,EACF,CACF,CAAA,CAAA,GAEJ,CACF,CAAA,CAAA,EACF,CAEJ,2GChKME,GAAiB,IAAM,CAC3B,MAAMpB,EAAWC,EAAY,EACvB,CAACoB,EAAMC,CAAO,EAAIlB,EAAAA,SAAS,IAAI,EAC/B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EA2C3C,OAzCAmB,EAAAA,UAAU,IAAM,EACI,SAAY,CACtB,MAAAC,EAAQ,aAAa,QAAQ,YAAY,EACzCC,EAAW,aAAa,QAAQ,WAAW,EAE7C,GAAA,CAACD,GAAS,CAACC,EAAU,CACvBzB,EAAS,QAAQ,EACjB,MAAA,CAGE,GAAA,CAEF,KAAM,CAAE,SAAAY,EAAU,KAAAC,CAAS,EAAA,MAAMC,GAAQ,MAAM,EAE3CF,EAAS,IAAMC,EAAK,QACtBS,EAAQT,EAAK,IAAI,GAGjB,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnCb,EAAS,QAAQ,SAEZO,EAAO,CACN,QAAA,MAAM,qBAAsBA,CAAK,EACzC,aAAa,WAAW,YAAY,EACpC,aAAa,WAAW,WAAW,EACnCP,EAAS,QAAQ,CAAA,QACjB,CACAM,EAAW,EAAK,CAAA,CAEpB,GAEU,CAAA,EACT,CAACN,CAAQ,CAAC,EAQTK,EAECa,EAAA,IAAA,MAAA,CAAI,GAAG,OAAO,UAAU,OACvB,SAACA,MAAA,OAAA,CAAK,GAAG,OACP,SAACA,EAAA,IAAA,UAAA,CAAQ,UAAU,eACjB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,qBACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,kBACV,MAAO,CACL,SAAU,OACV,UAAW,yBAAA,CACb,CACD,EACDA,EAAAA,IAAC,OAAI,UAAU,QACb,eAAC,MAAI,CAAA,UAAU,2BAA2B,SAAA,YAAU,CAAA,CACtD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAMAF,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,8BACN,YAAY,mDACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACU,EAAY,CAAA,MAAM,YAEjB,SAAA,CAACV,EAAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,2BAA2B,CAChD,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAW,cAAA,EAC1CA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,oBAAoB,CACzC,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAU,aAAA,EACzCA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,wBAAwB,CAC7C,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAQ,WAAA,EACvCA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,gBACb,eAAC,eAAa,CAAA,KAAK,iBAAiB,CACtC,CAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,iBAAiB,SAAU,aAAA,EACzCA,EAAA,IAAA,MAAA,CAAI,UAAU,kBAAkB,SAAC,GAAA,CAAA,CAAA,CAAA,CACpC,CACF,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,MACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,QACb,SAAAA,EAAA,IAAC,MAAG,UAAU,0CAA0C,yBAExD,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MAEb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,6BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,wBAAwB,CAC7C,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAa,gBAAA,EACxDA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,wEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlB,EAAS,iBAAiB,EACzC,UAAU,kCACX,SAAA,aAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACgB,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,uBAAuB,CAC5C,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAY,eAAA,EACvDA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,kEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlB,EAAS,cAAc,EACtC,UAAU,kCACX,SAAA,cAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACgB,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,+BAA+B,CACpD,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAE5C,oBAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,wEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlB,EAAS,mBAAmB,EAC3C,UAAU,kCACX,SAAA,kBAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACgB,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,wBAAwB,CAC7C,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAE5C,qBAAA,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAEpC,+DAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlB,EAAS,iBAAiB,EACzC,UAAU,kCACX,SAAA,iBAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,6BACb,SAACgB,EAAA,KAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,eAAC,eAAa,CAAA,KAAK,qBAAqB,CAC1C,CAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAAS,YAAA,EACpDA,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAGpC,oEAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,qBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMlB,EAAS,kBAAkB,EAC1C,UAAU,kCACX,SAAA,gBAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,2GClQM2B,GAAiB,IAAM,CAC3B,MAAM3B,EAAWC,EAAY,EACvB,CAAC2B,EAAOC,CAAQ,EAAIzB,EAAAA,SAAS,CAAA,CAAE,EAC/B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAAC0B,EAASC,CAAU,EAAI3B,WAAS,CACrC,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,EAAA,CACT,EACK,CAAC4B,EAAYC,CAAa,EAAI7B,EAAAA,SAAS,CAAA,CAAE,EAE/CmB,EAAAA,UAAU,IAAM,CACJW,EAAA,CAAA,EACT,CAACJ,CAAO,CAAC,EAEZ,MAAMI,EAAY,SAAY,CACxB,GAAA,CACF5B,EAAW,EAAI,EAEf,MAAM6B,EAAS,CAAC,EACT,OAAA,QAAQL,CAAO,EAAE,QAAQ,CAAC,CAACM,EAAKC,CAAK,IAAM,CAC5CA,GAASA,IAAU,QACrBF,EAAOC,CAAG,EAAIC,EAChB,CACD,EAED,KAAM,CAAE,SAAAzB,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,SAASH,CAAM,EAErDtB,EAAK,SACEgB,EAAAhB,EAAK,KAAK,KAAK,EACVoB,EAAApB,EAAK,KAAK,UAAU,GAEzBL,EAAAK,EAAK,SAAW,sBAAsB,QAE1CN,EAAO,CACN,QAAA,MAAM,oBAAqBA,CAAK,EACxCC,EAAS,kCAAkC,CAAA,QAC3C,CACAF,EAAW,EAAK,CAAA,CAEpB,EAEMiC,EAAe,MAAOC,GAAW,CACrC,GACG,QACC,+EAAA,EAMA,GAAA,CACF,KAAM,CAAE,SAAA5B,EAAU,KAAAC,CAAA,EAAS,MAAM4B,GAAQ,WAAWD,CAAM,EAEtD3B,EAAK,QACGqB,EAAA,EAED1B,EAAAK,EAAK,SAAW,uBAAuB,QAE3CN,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EACpCC,EAAS,kCAAkC,CAAA,CAE/C,EAEMkC,EAAyB,MAAOF,GAAW,CAC3C,GAAA,CACF,KAAM,CAAE,SAAA5B,EAAU,KAAAC,CAAA,EAAS,MAAM4B,GAAQ,iBAAiBD,CAAM,EAE5D3B,EAAK,QACGqB,EAAA,EAED1B,EAAAK,EAAK,SAAW,6BAA6B,QAEjDN,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,EAC/CC,EAAS,kCAAkC,CAAA,CAE/C,EAEMmC,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,SAAA,CACT,EAGGC,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,wBAAwBD,CAAQ,GAT3B,KAYlBE,EAAkBC,GACjBA,EAAK,UAYNA,EAAK,aAAe,IAAI,KAAKA,EAAK,WAAW,EAAQ,IAAA,KAErDjC,EAAA,KAAC,OAAK,CAAA,UAAU,mBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,WAAA,EAElB,EAKFF,EAAA,KAAC,OAAK,CAAA,UAAU,mBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,WAAA,EAElB,EA7BEF,EAAA,KAAC,OAAK,CAAA,UAAU,qBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,2BACL,UAAU,MAAA,CACX,EAAe,OAAA,EAElB,EA2BN,OAEIF,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,4BACN,YAAY,uCACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACU,EAAY,CAAA,MAAM,aAEjB,SAAA,CAAAR,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,mFAGlC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMhB,EAAS,iBAAiB,EACzC,UAAU,kDAEV,SAAA,CAAAkB,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,UAAA,CAAA,CAAA,CAGpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAECA,EAAA,IAAA,MAAA,CAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,WAAA,EACnD,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,UACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAY,eAAA,EAC1CA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOY,EAAQ,OACf,SAAWpB,GACTqB,EAAYmB,IAAU,CACpB,GAAGA,EACH,OAAQxC,EAAE,OAAO,MACjB,KAAM,CAAA,EACN,EAEJ,UAAU,eACV,YAAY,oBAAA,CAAA,CACd,EACF,EAEAM,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAM,SAAA,EACpCF,EAAA,KAAC,SAAA,CACC,MAAOc,EAAQ,OACf,SAAWpB,GACTqB,EAAYmB,IAAU,CACpB,GAAGA,EACH,OAAQxC,EAAE,OAAO,MACjB,KAAM,CAAA,EACN,EAEJ,UAAU,eAEV,SAAA,CAACQ,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAS,YAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAS,YAAA,EAClCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAM,QAAA,CAAA,CAAA,CAAA,CAAA,CAC9B,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAQ,WAAA,EACtCF,EAAA,KAAC,SAAA,CACC,MAAOc,EAAQ,MACf,SAAWpB,GACTqB,EAAYmB,IAAU,CACpB,GAAGA,EACH,MAAO,SAASxC,EAAE,OAAO,KAAK,EAC9B,KAAM,CAAA,EACN,EAEJ,UAAU,eAEV,SAAA,CAACQ,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAE,KAAA,EACpBA,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAE,KAAA,EACpBA,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAE,IAAA,CAAA,CAAA,CAAA,CAAA,CACvB,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCX,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAIDW,EAAA,IAAA,MAAA,CAAI,UAAU,cACZ,SACCb,EAAAW,EAAA,KAAC,MAAI,CAAA,UAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,EAAA,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,CAAA,CAC/C,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAAgB,kBAAA,CAAA,CAAA,CAAA,CAC5D,EACEU,EAAM,SAAW,EAClBZ,EAAA,KAAA,MAAA,CAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,WAAA,EACnD,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,2BACL,UAAU,gCAAA,CACX,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,sBAAA,EACAA,EAAAA,IAAC,IAAE,CAAA,UAAU,sBACV,SAAAY,EAAQ,QAAUA,EAAQ,SAAW,MAClC,oEACA,+CACN,CAAA,EACAd,EAAA,KAAC,SAAA,CACC,QAAS,IAAMhB,EAAS,iBAAiB,EACzC,UAAU,kCAEV,SAAA,CAAAkB,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,mBAAA,CAAA,CAAA,CAElB,CAAA,CACF,EAIEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,CAAA,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAMU,EAAA,IAAKqB,GAAS,CACnB,MAAME,EACJF,EAAK,aAAa,KAAMnD,GAAMA,EAAE,WAAa,IAAI,GACjDmD,EAAK,aAAa,CAAC,EAErB,cACG,KACC,CAAA,SAAA,CAAA/B,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACZ,SAAA,CAAAiC,EAAK,eACJ/B,EAAA,IAAC,MAAA,CACC,UAAU,eACV,IAAK2B,EAAYI,EAAK,aAAa,EACnC,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAUvC,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,MAAA,CAC3B,CACF,SAED,MACC,CAAA,SAAA,CAAAQ,MAAC,MAAI,CAAA,UAAU,UACZ,UAAAiC,GAAA,YAAAA,EAAoB,QAAS,WAChC,EACAnC,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BiC,EAAK,IAAA,CACT,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,SACC,KACE,CAAA,SAAA,CAAAD,EAAeC,CAAI,EACnBA,EAAK,UACHjC,OAAA,OAAA,CAAK,UAAU,wBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,UAAA,CAElB,CAAA,CAAA,EAEJ,QACC,KAAI,CAAA,SAAA+B,EAAK,OAAO,MAAQA,EAAK,OAAO,MAAM,EAC1C/B,EAAA,IAAA,KAAA,CAAI,SAAWyB,EAAAM,EAAK,SAAS,EAAE,QAC/B,KACC,CAAA,SAAAjC,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IACPlB,EAAS,oBAAoBiD,EAAK,EAAE,EAAE,EAExC,UAAU,iCACV,MAAM,OAEN,SAAA/B,EAAAA,IAAC,eAAa,CAAA,KAAK,gBAAiB,CAAA,CAAA,CACtC,EAEAA,EAAA,IAAC,SAAA,CACC,QAAS,IACPwB,EAAuBO,EAAK,EAAE,EAEhC,UAAW,cACTA,EAAK,UACD,sBACA,qBACN,GACA,MACEA,EAAK,UAAY,YAAc,UAGjC,SAAA/B,EAAA,IAAC,eAAA,CACC,KACE+B,EAAK,UACD,wBACA,gBAAA,CAAA,CAEP,CACH,EAEA/B,EAAA,IAAC,SAAA,CACC,QAAS,IAAMqB,EAAaU,EAAK,EAAE,EACnC,UAAU,gCACV,MAAM,SAEN,SAAA/B,EAAAA,IAAC,eAAa,CAAA,KAAK,4BAA6B,CAAA,CAAA,CAAA,CAClD,CAAA,CACF,CACF,CAAA,CAAA,CAAA,EApFO+B,EAAK,EAqFd,CAAA,CAEH,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGA/B,EAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,UACZ,SAAAU,EAAM,IAAKqB,GAAS,CACnB,MAAME,EACJF,EAAK,aAAa,KAAMnD,GAAMA,EAAE,WAAa,IAAI,GACjDmD,EAAK,aAAa,CAAC,EAErB,OACG/B,MAAA,MAAA,CAAkB,UAAU,SAC3B,eAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,gBACb,SAACF,OAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,cACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,4BACZ,SAAA,CAAAiC,EAAK,eACJ/B,EAAA,IAAC,MAAA,CACC,UAAU,eACV,IAAK2B,EAAYI,EAAK,aAAa,EACnC,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAUvC,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,MAAA,CAC3B,CACF,EAEFM,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,eACX,UAAAiC,GAAA,YAAAA,EAAoB,QAAS,WAChC,EACAnC,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BiC,EAAK,IAAA,CACT,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAjC,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,SAAA,SACC,MACE,CAAA,SAAA,CAAA8B,EAAeC,CAAI,EACnBA,EAAK,UACHjC,OAAA,OAAA,CAAK,UAAU,wBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,UAAA,CAElB,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,SAAA,QACC,QACE,CAAA,SAAA+B,EAAK,OAAO,MAAQA,EAAK,OAAO,KACnC,CAAA,CAAA,EACF,EAEAjC,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,UAAA,EACCA,EAAA,IAAA,QAAA,CAAO,SAAWyB,EAAAM,EAAK,SAAS,CAAE,CAAA,CAAA,EACrC,QAEC,MAAI,CAAA,UAAU,SACb,SAACjC,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAAS,IACPhB,EAAS,oBAAoBiD,EAAK,EAAE,EAAE,EAExC,UAAU,2CACV,MAAM,OAEN,SAAA,CAAA/B,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,MAAA,CAAA,CAElB,EAEAF,EAAA,KAAC,SAAA,CACC,QAAS,IACP0B,EAAuBO,EAAK,EAAE,EAEhC,UAAW,wBACTA,EAAK,UACD,sBACA,qBACN,GACA,MACEA,EAAK,UAAY,YAAc,UAGjC,SAAA,CAAA/B,EAAA,IAAC,eAAA,CACC,KACE+B,EAAK,UACD,wBACA,iBAEN,UAAU,MAAA,CACX,EACAA,EAAK,UAAY,OAAS,MAAA,CAAA,CAC7B,EAEAjC,EAAA,KAAC,SAAA,CACC,QAAS,IAAMuB,EAAaU,EAAK,EAAE,EACnC,UAAU,0CACV,MAAM,SAEN,SAAA,CAAA/B,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EAAe,QAAA,CAAA,CAAA,CAElB,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAzHQ,EAAA+B,EAAK,EA0Hf,CAEH,CAAA,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EAGCjB,EAAW,MAAQ,GACjBhB,EAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,+BACb,SAACF,EAAA,KAAA,IAAA,CAAE,UAAU,kDAAkD,SAAA,CAAA,YACnDgB,EAAW,KAAO,GAAKA,EAAW,MAAQ,EAAE,MAAI,IACzD,KAAK,IAAIA,EAAW,KAAOA,EAAW,MAAOA,EAAW,KAAK,EAAG,IAAI,MACjEA,EAAW,MAAM,UAAA,CAAA,CACvB,CACF,CAAA,EACAd,EAAA,IAAC,MAAI,CAAA,UAAU,kBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,aAAW,wBACd,SAAAF,OAAC,KAAG,CAAA,UAAU,8EACZ,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAW,aACTc,EAAW,MAAQ,EAAI,WAAa,EACtC,GAEA,SAAAd,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IACPa,EAAYmB,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,EAAA,EAEzD,SAAUlB,EAAW,MAAQ,EAC9B,SAAA,UAAA,CAAA,CAED,CACF,QAEC,KAAG,CAAA,UAAU,mBACZ,SAAChB,EAAA,KAAA,OAAA,CAAK,UAAU,YAAY,SAAA,CAAA,QACpBgB,EAAW,KAAK,OAAKA,EAAW,KAAA,CAAA,CACxC,CACF,CAAA,EAEAd,EAAA,IAAC,KAAA,CACC,UAAW,aACTc,EAAW,MAAQA,EAAW,MAAQ,WAAa,EACrD,GAEA,SAAAd,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IACPa,EAAYmB,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,EAAA,EAEzD,SAAUlB,EAAW,MAAQA,EAAW,MACzC,SAAA,MAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,2GCjlBMoB,GAAkB,IAAM,kBAC5B,KAAM,CAAE,EAAAtD,EAAG,KAAAuD,CAAK,EAAItD,GAAe,EAC7BC,EAAWC,EAAY,EACvB,CAAE,GAAAqD,CAAG,EAAIC,GAAU,EACnBC,EAAY,EAAQF,EAGpBT,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,wBAAwBD,CAAQ,GAT3B,KAYlB,CAACzC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACqD,EAAQC,CAAS,EAAItD,EAAAA,SAAS,EAAK,EACpC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACuD,EAASC,CAAU,EAAIxD,EAAAA,SAAS,EAAE,EAGnC,CAACyD,CAAkB,EAAIzD,EAAAA,SAAS,IAAM,OAAO,KAAKiD,EAAK,MAAM,IAAI,CAAC,EAGlE,CAACnD,EAAUC,CAAW,EAAIC,WAAS,IAAM,CAE7C,MAAM0D,EAAsB,CAAC,EACV,OAAAD,EAAA,QAASE,GAAS,CACnCD,EAAoBC,CAAI,EAAI,CAC1B,MAAO,GACP,QAAS,GACT,QAAS,GACT,UAAW,GACX,SAAU,GACV,SAAU,CAAA,CACZ,CAAA,CACD,EAEM,CACL,KAAM,GACN,SAAU,GACV,UAAW,GACX,YAAa,GACb,cAAe,KACf,iBAAkB,GAClB,SAAU,GACV,YAAa,CAAC,EACd,OAAQ,CAAC,EACT,aAAcD,CAChB,CAAA,CACD,EAEK,CAACE,EAAgBC,CAAiB,EAAI7D,EAAAA,SAAS,IAAI,EACnD,CAAC8D,EAAYC,CAAa,EAAI/D,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACgE,EAAMC,CAAO,EAAIjE,EAAAA,SAAS,CAAA,CAAE,EAC7B,CAACkE,EAAcC,CAAe,EAAInE,EAAAA,SAAS,IAAI,EAGrDmB,EAAAA,UAAU,IAAM,EACG,SAAY,CACvB,GAAA,CAMF,GALAjB,EAAW,EAAI,EACfE,EAAS,EAAE,EAIP,CADU,aAAa,QAAQ,YAAY,EACnC,CACVA,EACE,6DACF,EACAF,EAAW,EAAK,EAChB,MAAA,CAIF,KAAM,CAACkE,EAAkBC,CAAU,EAAI,MAAM,QAAQ,IAAI,CACvDnC,EAAS,cAAc,EACvBA,EAAS,QAAQ,CAAA,CAClB,EAGD,GAAIkC,EAAiB,SAAS,IAAMA,EAAiB,KACnDL,EAAcK,EAAiB,KAAK,MAAQ,CAAA,CAAE,MACzC,CAML,GALQ,QAAA,MACN,yBACAA,EAAiB,SAAS,OAC1BA,EAAiB,SAAS,UAC5B,EAEEA,EAAiB,SAAS,SAAW,KACrCA,EAAiB,SAAS,SAAW,IACrC,CACAhE,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEF2D,EAAc,CAAA,CAAE,CAAA,CAIlB,GAAIM,EAAW,SAAS,IAAMA,EAAW,KACvCJ,EAAQI,EAAW,KAAK,MAAQ,CAAA,CAAE,MAC7B,CAML,GALQ,QAAA,MACN,mBACAA,EAAW,SAAS,OACpBA,EAAW,SAAS,UACtB,EAEEA,EAAW,SAAS,SAAW,KAC/BA,EAAW,SAAS,SAAW,IAC/B,CACAjE,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEF6D,EAAQ,CAAA,CAAE,CAAA,CAIZ,GAAIb,EAAW,CACb,KAAM,CAAE,SAAUkB,EAAS,KAAMC,CAAS,EAAI,MAAMrC,EAAS,QAC3DgB,CACF,EAEI,GAAAoB,EAAQ,IAAMC,EAAS,QACrB,GAAA,CACF,MAAM1B,EAAO0B,EAAS,KAGhBC,EAAkB,CAAC,EACrB3B,EAAK,cAAgB,MAAM,QAAQA,EAAK,YAAY,GACjDA,EAAA,aAAa,QAASnD,GAAM,CACfA,EAAAA,EAAE,QAAQ,EAAIA,CAAA,CAC/B,EAGHK,EAAa+C,IAAU,CACrB,GAAGA,EACH,KAAMD,EAAK,MAAQ,GACnB,SAAUA,EAAK,UAAY,GAC3B,UAAWA,EAAK,WAAa,GAC7B,YAAaA,EAAK,YACd,IAAI,KAAKA,EAAK,WAAW,EAAE,YAAY,EAAE,MAAM,EAAG,EAAE,EACpD,GACJ,cAAe,KACf,iBAAkBA,EAAK,kBAAoB,GAC3C,SAAUA,EAAK,UAAY,GAC3B,YAAaA,EAAK,WACdA,EAAK,WAAW,IAAK4B,GAAMA,EAAE,EAAE,EAC/B,CAAC,EACL,OAAQ5B,EAAK,KAAOA,EAAK,KAAK,IAAKnD,GAAMA,EAAE,EAAE,EAAI,CAAC,EAClD,aAAc,CAAE,GAAGoD,EAAK,aAAc,GAAG0B,CAAgB,CAAA,EACzD,EAEE3B,EAAK,eACSsB,EAAA1B,EAAYI,EAAK,aAAa,CAAC,QAE1C6B,EAAW,CACV,QAAA,MAAM,iCAAkCA,CAAS,EACzDtE,EAAS,oDAAoD,CAAA,MAGvD,QAAA,MACN,mBACAkE,EAAQ,OACRA,EAAQ,UACV,EACAlE,EACEmE,EAAS,SACP,wBAAwBD,EAAQ,MAAM,IAAIA,EAAQ,UAAU,EAChE,CACF,QAEKnE,EAAO,CACN,QAAA,MAAM,sBAAuBA,CAAK,EACtCA,EAAM,SAAWA,EAAM,QAAQ,SAAS,OAAO,EACjDC,EACE,2FACF,EAEAA,EAAS,wCAAwC,CACnD,QACA,CACAF,EAAW,EAAK,CAAA,CAEpB,GAES,CAAA,EACR,CAACgD,EAAIE,CAAS,CAAC,EAEZ,MAAAuB,EAAoB,CAACC,EAAO3C,IAAU,CAC1ClC,EAAa+C,IAAU,CACrB,GAAGA,EACH,CAAC8B,CAAK,EAAG3C,CAAA,EACT,CACJ,EAEM4C,EAA0B,CAACC,EAAUF,EAAO3C,IAAU,CAC1DlC,EAAa+C,IAAU,CACrB,GAAGA,EACH,aAAc,CACZ,GAAGA,EAAK,aACR,CAACgC,CAAQ,EAAG,CACV,GAAGhC,EAAK,aAAagC,CAAQ,EAC7B,CAACF,CAAK,EAAG3C,CAAA,CACX,CACF,EACA,CACJ,EAEM8C,GAAqBzE,GAAM,CAC/B,MAAM0E,EAAO1E,EAAE,OAAO,MAAM,CAAC,EAC7B,GAAI0E,EAAM,CACRjF,EAAa+C,IAAU,CACrB,GAAGA,EACH,cAAekC,CAAA,EACf,EAGI,MAAAC,EAAS,IAAI,WACZA,EAAA,OAAU3E,GAAM,CACLA,EAAAA,EAAE,OAAO,MAAM,CACjC,EACA2E,EAAO,cAAcD,CAAI,CAAA,CAE7B,EAEMzE,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBgD,EAAU,EAAI,EACdlD,EAAS,EAAE,EACXoD,EAAW,EAAE,EAET,GAAA,CACI,MAAApC,EAAQ,aAAa,QAAQ,YAAY,EACzC8D,EAAiB,IAAI,SAGZA,EAAA,OAAO,OAAQpF,EAAS,IAAI,EAC5BoF,EAAA,OAAO,WAAYpF,EAAS,QAAQ,EACpCoF,EAAA,OAAO,YAAapF,EAAS,SAAS,EACjDA,EAAS,aACIoF,EAAA,OAAO,cAAepF,EAAS,WAAW,EAE5CoF,EAAA,OAAO,mBAAoBpF,EAAS,gBAAgB,EAC/DA,EAAS,UACIoF,EAAA,OAAO,WAAYpF,EAAS,QAAQ,EAItCoF,EAAA,OACb,cACA,KAAK,UAAUpF,EAAS,WAAW,CACrC,EACAoF,EAAe,OAAO,SAAU,KAAK,UAAUpF,EAAS,MAAM,CAAC,EAChDoF,EAAA,OACb,eACA,KAAK,UAAUpF,EAAS,YAAY,CACtC,EAGIA,EAAS,eACIoF,EAAA,OAAO,gBAAiBpF,EAAS,aAAa,EAI3D,IAAAqF,EACA/B,EACF+B,EAAS,MAAM9C,GAAQ,WAAWa,EAAIgC,CAAc,EAE3CC,EAAA,MAAM9C,GAAQ,WAAW6C,CAAc,EAG5C,KAAA,CAAE,SAAA1E,EAAU,KAAAC,CAAA,EAAS0E,EAE3B,GAAI3E,EAAS,IAAMC,GAAQA,EAAK,QAC9B+C,EACE,aAAaJ,EAAY,UAAY,SAAS,gBAChD,EACA,WAAW,IAAM,CACfxD,EAAS,cAAc,GACtB,GAAI,MACF,CACL,MAAMwF,GACJ3E,GAAA,YAAAA,EAAM,UACN,aAAa2C,EAAY,SAAW,QAAQ,aAC9ChD,EAASgF,CAAY,CAAA,QAEhBjF,EAAO,CACN,QAAA,MAAM,cAAeA,CAAK,EAClCC,EAAS,kCAAkC,CAAA,QAC3C,CACAkD,EAAU,EAAK,CAAA,CAEnB,EAEA,OAEI1C,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAO,GAAGqC,EAAY,OAAS,QAAQ,qBACvC,YAAY,+CACZ,QAAS,EAAA,CACX,EAEAtC,EAAA,IAACQ,EAAA,CACC,MAAO8B,EAAY,iBAAmB,uBAEtC,SAACxC,EAAAA,KAAA,OAAA,CAAK,SAAUL,EAAc,UAAU,aAErC,SAAA,CAAAJ,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAGDoD,GACE3C,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAyC,CAAA,EACH,EAIF3C,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,sBAAA,CACX,EAAe,gBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,kDAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,YAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhB,EAAS,KAChB,SAAWQ,GAAMqE,EAAkB,OAAQrE,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,eAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,sEAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,qBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAOhB,EAAS,SAChB,SAAWQ,GACTqE,EAAkB,WAAYrE,EAAE,OAAO,KAAK,EAE9C,UAAU,eACV,YAAY,IACZ,IAAI,IACJ,IAAI,IAAA,CACN,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,sCAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,MAAA,CACX,EAAe,sBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,iBACL,MAAOhB,EAAS,YAChB,SAAWQ,GACTqE,EAAkB,cAAerE,EAAE,OAAO,KAAK,EAEjD,UAAU,cAAA,CACZ,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,8DAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,cAAA,EAElB,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,WACH,QAAShB,EAAS,SAClB,SAAWQ,GACTqE,EAAkB,WAAYrE,EAAE,OAAO,OAAO,EAEhD,UAAU,kBAAA,CACZ,EACCM,EAAA,KAAA,QAAA,CAAM,UAAU,mBAAmB,QAAQ,WAC1C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,eAAA,EAElB,EACCA,EAAA,IAAA,QAAA,CAAM,UAAU,+BAA+B,SAEhD,4CAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,GAAG,YACH,QAAShB,EAAS,UAClB,SAAWQ,GACTqE,EAAkB,YAAarE,EAAE,OAAO,OAAO,EAEjD,UAAU,kBAAA,CACZ,EACCM,EAAA,KAAA,QAAA,CAAM,UAAU,mBAAmB,QAAQ,YAC1C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,WAAA,EAElB,EACCA,EAAA,IAAA,QAAA,CAAM,UAAU,+BAA+B,SAEhD,sCAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,sBAAA,CACX,EAAe,gBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,oEAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EAAe,cAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,OAAO,UACP,SAAUiE,GACV,UAAU,cAAA,CACZ,EACCjE,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,iEAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,UAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhB,EAAS,iBAChB,SAAWQ,GACTqE,EAAkB,mBAAoBrE,EAAE,OAAO,KAAK,EAEtD,UAAU,eACV,YAAY,sCAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,+CAAA,CAAA,CAAA,EACF,EAECoD,GACCtD,EAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,QACb,SAAAA,EAAA,IAAC,SAAM,UAAU,aAAa,yBAAa,CAC7C,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,cACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAKoD,EACL,IAAI,UACJ,UAAU,gBACV,MAAO,CAAE,SAAU,QAAS,OAAQ,MAAO,CAAA,CAAA,CAE/C,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAtD,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,+BAAgC,CAAA,EAAI,0BAAA,EAEnD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,6EAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,sBACZ,SAAmB2C,EAAA,IAAKE,GACvB/C,EAAA,KAAC,SAAA,CAEC,KAAK,SACL,QAAS,IAAMiD,EAAkBF,CAAI,EACrC,UAAW,gBACTC,IAAmBD,EAAO,SAAW,EACvC,GAEA,SAAA,CAAC7C,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5B6C,EAAK,YAAY,EACjBA,IAAS,MACR7C,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAa,SAAU,YAAA,CAAA,CAAA,CAAA,EAVpC6C,CAaR,CAAA,EACH,EAGA/C,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,UACxB8C,EAAe,YAAY,EAAE,IACpCA,IAAmB,MAClB9C,EAAAA,IAAC,OAAK,CAAA,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAExC,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAOuE,EAAAvF,EAAS,aAAa8D,CAAc,IAApC,YAAAyB,EAAuC,QAAS,GACvD,SAAW/E,GACTuE,EACEjB,EACA,QACAtD,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,wBACZ,SAAUsD,IAAmB,IAAA,CAC/B,EACAhD,EAAAA,KAAC,QAAM,CAAA,UAAU,uBAAuB,SAAA,CAAA,sCACF,IACnCgD,EAAe,YAAY,CAAA,CAC9B,CAAA,CAAA,EACF,EAEAhD,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,YACtB8C,EAAe,YAAY,EAAE,GAAA,EACzC,EACA9C,EAAA,IAAC,WAAA,CACC,QAAOwE,EAAAxF,EAAS,aAAa8D,CAAc,IAApC,YAAA0B,EAAuC,UAAW,GACzD,SAAWhF,GACTuE,EACEjB,EACA,UACAtD,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,oCAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,6EAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,2BACL,UAAU,MAAA,CACX,EAAe,YACN8C,EAAe,YAAY,EAAE,IACtCA,IAAmB,MAClB9C,EAAAA,IAAC,OAAK,CAAA,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAExC,EACAA,EAAA,IAACyE,GAAA,CACC,UAASC,GAAA1F,EAAS,aAAa8D,CAAc,IAApC,YAAA4B,GAAuC,UAAW,GAC3D,SAAWC,GACTZ,EAAwBjB,EAAgB,UAAW6B,CAAI,EAEzD,YAAY,6GAAA,CACd,EACA7E,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,yBACL,UAAU,MAAA,CACX,EAAe,oJAAA,CAIlB,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,aAAc,CAAA,EAAI,eAClB8C,EAAe,YAAY,EAAE,GAAA,EAC5C,EACA9C,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAO4E,GAAA5F,EAAS,aAAa8D,CAAc,IAApC,YAAA8B,GAAuC,YAAa,GAC3D,SAAWpF,GACTuE,EACEjB,EACA,YACAtD,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,uBACZ,UAAU,IAAA,CACZ,EACAM,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,gBAAiB,CAAA,EAAI,iEAAA,CAGpC,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,qBAAsB,CAAA,EAAI,qBACpB8C,EAAe,YAAY,EAAE,GAAA,EAClD,EACA9C,EAAA,IAAC,WAAA,CACC,QAAO6E,GAAA7F,EAAS,aAAa8D,CAAc,IAApC,YAAA+B,GAAuC,WAAY,GAC1D,SAAWrF,GACTuE,EACEjB,EACA,WACAtD,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,6BACZ,UAAU,KAAA,CACZ,EACAM,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,gBAAiB,CAAA,EAAI,wEAAA,CAGpC,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,sBAAA,CACX,EAAe,mBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,kDAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EAAe,YAAA,EAElB,EACCA,MAAA,MAAA,CAAI,UAAU,kBACZ,YAAcgD,EAAW,OAAS,EACjCA,EAAW,IAAK8B,GACbhF,EAAA,KAAA,MAAA,CAAsB,UAAU,kBAC/B,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,KAAK,WACL,GAAI,YAAY8E,EAAS,EAAE,GAC3B,QAAS9F,EAAS,YAAY,SAAS8F,EAAS,EAAE,EAClD,SAAU,IAAM,CACR,MAAAC,EACJ/F,EAAS,YAAY,SAAS8F,EAAS,EAAE,EACrC9F,EAAS,YAAY,OAClBoD,GAAOA,IAAO0C,EAAS,IAE1B,CAAC,GAAG9F,EAAS,YAAa8F,EAAS,EAAE,EAC3C7F,EAAa+C,IAAU,CACrB,GAAGA,EACH,YAAa+C,CAAA,EACb,CAAA,CACJ,CACF,EACA/E,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,QAAS,YAAY8E,EAAS,EAAE,GAE/B,SAASA,EAAA,IAAA,CAAA,CAvBJ,CAAA,EAAAA,EAAS,EAyBnB,CACD,QAEA,IAAE,CAAA,UAAU,aAAa,SAAA,yBAAuB,CAAA,CAErD,CAAA,CAAA,EACF,EAEAhF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,MAAA,CACX,EAAe,MAAA,EAElB,EACCA,MAAA,MAAA,CAAI,UAAU,YACZ,YAAQkD,EAAK,OAAS,EACrBA,EAAK,IAAK8B,GACPlF,EAAA,KAAA,MAAA,CAAiB,UAAU,kBAC1B,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,KAAK,WACL,GAAI,OAAOgF,EAAI,EAAE,GACjB,QAAShG,EAAS,OAAO,SAASgG,EAAI,EAAE,EACxC,SAAU,IAAM,CACR,MAAAC,EAAYjG,EAAS,OAAO,SAASgG,EAAI,EAAE,EAC7ChG,EAAS,OAAO,OAAQoD,GAAOA,IAAO4C,EAAI,EAAE,EAC5C,CAAC,GAAGhG,EAAS,OAAQgG,EAAI,EAAE,EAC/B/F,EAAa+C,IAAU,CACrB,GAAGA,EACH,OAAQiD,CAAA,EACR,CAAA,CACJ,CACF,EACAjF,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,QAAS,OAAOgF,EAAI,EAAE,GAErB,SAAIA,EAAA,IAAA,CAAA,CApBC,CAAA,EAAAA,EAAI,EAsBd,CACD,QAEA,IAAE,CAAA,UAAU,aAAa,SAAA,mBAAiB,CAAA,CAE/C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,YACb,SAAClF,EAAA,KAAA,MAAA,CAAI,UAAU,kBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMlB,EAAS,cAAc,EACtC,UAAU,sCACX,SAAA,QAAA,CAED,EAEAkB,EAAA,IAAC,SAAA,CACC,KAAK,SACL,SAAUuC,EACV,UAAU,kCAET,WAEGzC,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,4BAA6B,CAAA,EAAI,WAAA,CAAA,CAEhD,EAGEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5BsC,EAAY,cAAgB,aAAA,CAC/B,CAAA,CAAA,CAAA,CAEJ,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACF,CAEJ,2GCn2BM4C,GAAgB,IAAM,CAC1B,MAAMpG,EAAWC,EAAY,EACvB,CAACoG,EAAUC,CAAW,EAAIlG,EAAAA,SAAS,CAAA,CAAE,EACrC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAAC0B,EAASC,CAAU,EAAI3B,WAAS,CACrC,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,EAAA,CACT,EACK,CAAC4B,EAAYC,CAAa,EAAI7B,EAAAA,SAAS,CAAA,CAAE,EAE/CmB,EAAAA,UAAU,IAAM,CACDgF,EAAA,CAAA,EACZ,CAACzE,CAAO,CAAC,EAEZ,MAAMyE,EAAe,SAAY,SAC3B,GAAA,CACFjG,EAAW,EAAI,EAEf,MAAM6B,EAAS,CAAC,EACT,OAAA,QAAQL,CAAO,EAAE,QAAQ,CAAC,CAACM,EAAKC,CAAK,IAAM,CAC5CA,GAASA,IAAU,QACrBF,EAAOC,CAAG,EAAIC,EAChB,CACD,EAED,KAAM,CAAE,SAAAzB,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,YAAYH,CAAM,EAExDtB,EAAK,SACPyF,IAAYb,EAAA5E,EAAK,OAAL,YAAA4E,EAAW,WAAY5E,EAAK,UAAY,EAAE,EACtDoB,IAAcyD,EAAA7E,EAAK,OAAL,YAAA6E,EAAW,aAAc,CAAA,CAAE,GAEhClF,EAAAK,EAAK,SAAW,yBAAyB,QAE7CN,EAAO,CACN,QAAA,MAAM,uBAAwBA,CAAK,EAC3CC,EAAS,kCAAkC,CAAA,QAC3C,CACAF,EAAW,EAAK,CAAA,CAEpB,EAEMiC,EAAe,MAAOe,GAAO,CACjC,GAAK,OAAO,QAAQ,+CAA+C,EAI/D,GAAA,CACF,KAAM,CAAE,SAAA1C,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,cAAcgB,CAAE,EAEtD1C,EAAS,IAAMC,EAAK,QACtByF,EAAYD,EAAS,OAAQG,GAAYA,EAAQ,KAAOlD,CAAE,CAAC,EAElD9C,EAAAK,EAAK,SAAW,0BAA0B,QAE9CN,EAAO,CACN,QAAA,MAAM,wBAAyBA,CAAK,EAC5CC,EAAS,0BAA0B,CAAA,CAEvC,EAEMkC,EAAyB,MAAOY,GAAO,CACvC,GAAA,CAEF,MAAMmD,EADUJ,EAAS,KAAMK,GAAMA,EAAE,KAAOpD,CAAE,EACtB,SAAW,YAAc,QAAU,YAEvD,CAAE,SAAA1C,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,cAAcgB,EAAI,CAC1D,OAAQmD,CAAA,CACT,EAEG7F,EAAS,IAAMC,EAAK,QACtByF,EACED,EAAS,IAAKK,GAAOA,EAAE,KAAOpD,EAAK,CAAE,GAAGoD,EAAG,OAAQD,GAAcC,CAAE,CACrE,EAESlG,EAAAK,EAAK,SAAW,iCAAiC,QAErDN,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,EAC/CC,EAAS,iCAAiC,CAAA,CAE9C,EAEMmG,EAAqB,CAACvE,EAAKC,IAAU,CACzCN,EAAYmB,IAAU,CACpB,GAAGA,EACH,CAACd,CAAG,EAAGC,EACP,KAAM,CAAA,EACN,CACJ,EAEMM,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,SAAA,CACN,EAIGC,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,2BAA2BD,CAAQ,GAT9B,KAalB8D,EAAmBJ,GACnBA,EAAQ,QAAUA,EAAQ,OAAO,OAAS,GAE1CA,EAAQ,OAAO,KAAMK,GAAQA,EAAI,SAAS,GAAKL,EAAQ,OAAO,CAAC,GAC7C,SAEfA,EAAQ,cAGXxD,EAAkBwD,GAClBA,EAAQ,SAAW,QAEnBxF,EAAA,KAAC,OAAK,CAAA,UAAU,qBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,2BACL,UAAU,MAAA,CACX,EAAe,OAAA,EAElB,EAKFF,EAAA,KAAC,OAAK,CAAA,UAAU,mBACd,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,WAAA,EAElB,EAIJ,OAEIF,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,0BACN,YAAY,qCACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACU,EAAY,CAAA,MAAM,WAEjB,SAAA,CAAAR,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,uFAGlC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMhB,EAAS,qBAAqB,EAC7C,UAAU,kDAEV,SAAA,CAAAkB,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,aAAA,CAAA,CAAA,CAGpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCA,EAAA,IAAA,MAAA,CAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,WAAA,EACnD,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,UACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2BACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAe,kBAAA,EAC7CA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAU,eACV,YAAY,6BACZ,MAAOY,EAAQ,OACf,SAAWpB,GAAMiG,EAAmB,SAAUjG,EAAE,OAAO,KAAK,CAAA,CAAA,CAC9D,EACF,EAEAM,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAM,SAAA,EACpCF,EAAA,KAAC,SAAA,CACC,UAAU,eACV,MAAOc,EAAQ,OACf,SAAWpB,GAAMiG,EAAmB,SAAUjG,EAAE,OAAO,KAAK,EAE5D,SAAA,CAACQ,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAU,aAAA,EAC7BA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAS,YAAA,EAClCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,OAAA,CAAA,CAAA,CAAA,CAAA,CAC7B,EACF,EAEAA,EAAAA,IAAC,MAAI,CAAA,UAAU,yCACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAAS,IACPe,EAAW,CACT,KAAM,EACN,MAAO,GACP,OAAQ,MACR,OAAQ,EAAA,CACT,EAEH,UAAU,yCAEV,SAAA,CAAAb,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,MAAA,CACX,EAAe,eAAA,CAAA,CAAA,CAGpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCX,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAIDW,EAAA,IAAA,MAAA,CAAI,UAAU,cACZ,SACCb,EAAAW,EAAA,KAAC,MAAI,CAAA,UAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,EAAA,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,CAAA,CAC/C,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAE1C,qBAAA,CAAA,CAAA,CAAA,CACF,EACEmF,EAAS,SAAW,EACrBrF,EAAA,KAAA,MAAA,CAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,WAAA,EACnD,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,gCAAA,CACX,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,oBAAA,EACAA,EAAAA,IAAC,IAAE,CAAA,UAAU,sBACV,SAAAY,EAAQ,QAAUA,EAAQ,SAAW,MAClC,kEACA,6CACN,CAAA,EACAd,EAAA,KAAC,SAAA,CACC,QAAS,IAAMhB,EAAS,qBAAqB,EAC7C,UAAU,kCAEV,SAAA,CAAAkB,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,sBAAA,CAAA,CAAA,CAElB,CAAA,CACF,EAIEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,CAAA,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAASmF,EAAA,IAAKG,GAAY,SACnB,MAAArD,IACJsC,EAAAe,EAAQ,eAAR,YAAAf,EAAsB,KACnB,GAAM,EAAE,WAAa,UACnBC,EAAAc,EAAQ,eAAR,YAAAd,EAAuB,IAE9B,cACG,KACC,CAAA,SAAA,CAAAxE,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACZ,SAAA,CAAA4F,EAAgBJ,CAAO,GACtBtF,EAAA,IAAC,MAAA,CACC,UAAU,eACV,IAAK2B,EAAY+D,EAAgBJ,CAAO,CAAC,EACzC,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAU9F,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,MAAA,CAC3B,CACF,SAED,MACC,CAAA,SAAA,CAAAQ,EAAAA,IAAC,OAAI,UAAU,UACZ,2BAAoB,QACnBsF,EAAQ,OACR,UACJ,CAAA,EACAxF,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BwF,EAAQ,IAAA,CACZ,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACCtF,EAAA,IAAA,KAAA,CAAI,SAAe8B,EAAAwD,CAAO,CAAE,CAAA,EAC7BtF,EAAA,IAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MACE,CAAA,SAAA,CAAAwF,EAAQ,iBACPxF,OAAC,MAAI,CAAA,UAAU,QACb,SAAA,CAAAE,EAAAA,IAAC,UAAO,SAAW,aAAA,CAAA,EAAS,KAC3BsF,EAAQ,eAAA,EACX,EAEDA,EAAQ,mBACNxF,OAAA,MAAA,CAAI,UAAU,QACb,SAAA,CAAAE,EAAAA,IAAC,UAAO,SAAa,eAAA,CAAA,EAAS,KAC7BsF,EAAQ,kBAAkB,KAAA,EAC7B,EAED,CAACA,EAAQ,iBACR,CAACA,EAAQ,mBACNtF,EAAAA,IAAA,OAAA,CAAK,UAAU,aAAa,SAE7B,gBAAA,CAAA,CAAA,CAAA,CAEN,CACF,CAAA,EACCA,EAAA,IAAA,KAAA,CAAI,SAAWyB,EAAA6D,EAAQ,SAAS,EAAE,QAClC,KACC,CAAA,SAAAxF,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IACPlB,EACE,wBAAwBwG,EAAQ,EAAE,EACpC,EAEF,UAAU,iCACV,MAAM,OAEN,SAAAtF,EAAAA,IAAC,eAAa,CAAA,KAAK,gBAAiB,CAAA,CAAA,CACtC,EAEAA,EAAA,IAAC,SAAA,CACC,QAAS,IACPwB,EAAuB8D,EAAQ,EAAE,EAEnC,UAAW,cACTA,EAAQ,SAAW,YACf,sBACA,qBACN,GACA,MACEA,EAAQ,SAAW,YACf,YACA,UAGN,SAAAtF,EAAA,IAAC,eAAA,CACC,KACEsF,EAAQ,SAAW,YACf,wBACA,gBAAA,CAAA,CAEP,CACH,EAEAtF,EAAA,IAAC,SAAA,CACC,QAAS,IAAMqB,EAAaiE,EAAQ,EAAE,EACtC,UAAU,gCACV,MAAM,SAEN,SAAAtF,EAAAA,IAAC,eAAa,CAAA,KAAK,4BAA6B,CAAA,CAAA,CAAA,CAClD,CAAA,CACF,CACF,CAAA,CAAA,CAAA,EApGOsF,EAAQ,EAqGjB,CAAA,CAEH,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAtF,EAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,UACZ,SAAAmF,EAAS,IAAKG,GAAY,SACzB,MAAMrD,IACJsC,EAAAe,EAAQ,eAAR,YAAAf,EAAsB,KAAM,GAAM,EAAE,WAAa,UACjDC,EAAAc,EAAQ,eAAR,YAAAd,EAAuB,IAEzB,OACGxE,MAAA,MAAA,CAAqB,UAAU,SAC9B,eAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAA,IAAC,OAAI,UAAU,gBACb,SAACF,OAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,cACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,4BACZ,SAAA,CAAA4F,EAAgBJ,CAAO,GACtBtF,EAAA,IAAC,MAAA,CACC,UAAU,eACV,IAAK2B,EACH+D,EAAgBJ,CAAO,CACzB,EACA,IAAI,GACJ,MAAO,CACL,MAAO,OACP,OAAQ,OACR,UAAW,OACb,EACA,QAAU9F,GAAM,CACZA,EAAA,OAAO,MAAM,QAAU,MAAA,CAC3B,CACF,EAEFM,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAE,EAAAA,IAAC,MAAG,UAAU,eACX,2BAAoB,QACnBsF,EAAQ,OACR,UACJ,CAAA,EACAxF,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BwF,EAAQ,IAAA,CACZ,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAxF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,SAAA,EACCA,EAAAA,IAAA,MAAA,CAAK,SAAe8B,EAAAwD,CAAO,CAAE,CAAA,CAAA,EAChC,EAEAxF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,UAAA,SACC,MACE,CAAA,SAAA,CAAAsF,EAAQ,iBACPxF,OAAC,MAAI,CAAA,UAAU,QACb,SAAA,CAAAE,EAAAA,IAAC,UAAO,SAAG,KAAA,CAAA,EAAS,KACnBsF,EAAQ,eAAA,EACX,EAEDA,EAAQ,mBACNxF,OAAA,MAAA,CAAI,UAAU,QACb,SAAA,CAAAE,EAAAA,IAAC,UAAO,SAAI,MAAA,CAAA,EAAS,KACpBsF,EAAQ,kBAAkB,KAAA,EAC7B,EAED,CAACA,EAAQ,iBACR,CAACA,EAAQ,mBACNtF,EAAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,SAEnC,YAAA,CAAA,CAAA,CAEN,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,UAAA,EACCA,EAAA,IAAA,QAAA,CAAO,SAAWyB,EAAA6D,EAAQ,SAAS,CAAE,CAAA,CAAA,EACxC,QAEC,MAAI,CAAA,UAAU,SACb,SAACxF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAAS,IACPhB,EACE,wBAAwBwG,EAAQ,EAAE,EACpC,EAEF,UAAU,2CACV,MAAM,OAEN,SAAA,CAAAtF,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,MAAA,CAAA,CAElB,EAEAF,EAAA,KAAC,SAAA,CACC,QAAS,IACP0B,EAAuB8D,EAAQ,EAAE,EAEnC,UAAW,wBACTA,EAAQ,SAAW,YACf,sBACA,qBACN,GACA,MACEA,EAAQ,SAAW,YACf,YACA,UAGN,SAAA,CAAAtF,EAAA,IAAC,eAAA,CACC,KACEsF,EAAQ,SAAW,YACf,wBACA,iBAEN,UAAU,MAAA,CACX,EACAA,EAAQ,SAAW,YAChB,OACA,MAAA,CAAA,CACN,EAEAxF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMuB,EAAaiE,EAAQ,EAAE,EACtC,UAAU,0CACV,MAAM,SAEN,SAAA,CAAAtF,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EAAe,QAAA,CAAA,CAAA,CAElB,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAzIQ,EAAAsF,EAAQ,EA0IlB,CAEH,CAAA,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EAGCxE,EAAW,MAAQ,GACjBhB,EAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,+BACb,SAACF,EAAA,KAAA,IAAA,CAAE,UAAU,kDAAkD,SAAA,CAAA,YACnDgB,EAAW,KAAO,GAAKA,EAAW,MAAQ,EAAE,MAAI,IACzD,KAAK,IAAIA,EAAW,KAAOA,EAAW,MAAOA,EAAW,KAAK,EAAG,IAAI,MACjEA,EAAW,MAAM,UAAA,CAAA,CACvB,CACF,CAAA,EACAd,EAAA,IAAC,MAAI,CAAA,UAAU,kBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,aAAW,sBACd,SAAAF,OAAC,KAAG,CAAA,UAAU,8EACZ,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAW,aACTc,EAAW,MAAQ,EAAI,WAAa,EACtC,GAEA,SAAAd,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IACPa,EAAYmB,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,EAAA,EAEzD,SAAUlB,EAAW,MAAQ,EAC9B,SAAA,UAAA,CAAA,CAED,CACF,QAEC,KAAG,CAAA,UAAU,mBACZ,SAAChB,EAAA,KAAA,OAAA,CAAK,UAAU,YAAY,SAAA,CAAA,QACpBgB,EAAW,KAAK,OAAKA,EAAW,KAAA,CAAA,CACxC,CACF,CAAA,EAEAd,EAAA,IAAC,KAAA,CACC,UAAW,aACTc,EAAW,MAAQA,EAAW,MAAQ,WAAa,EACrD,GAEA,SAAAd,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IACPa,EAAYmB,IAAU,CAAE,GAAGA,EAAM,KAAMA,EAAK,KAAO,CAAI,EAAA,EAEzD,SAAUlB,EAAW,MAAQA,EAAW,MACzC,SAAA,MAAA,CAAA,CAED,CAAA,CACF,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,2GClnBM8E,GAAqB,IAAM,eAC/B,KAAM,CAAE,EAAAhH,EAAG,KAAAuD,CAAK,EAAItD,GAAe,EAC7BC,EAAWC,EAAY,EACvB,CAAE,GAAAqD,CAAG,EAAIC,GAAU,EACnBC,EAAY,EAAQF,EAGpBT,EAAeC,GACdA,EAGDA,EAAS,WAAW,MAAM,EACrBA,EAKF,GADSC,GAAa,QAAQ,OAAQ,EAAE,CAC9B,2BAA2BD,CAAQ,GAT9B,KAYlB,CAACzC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAK,EACtC,CAACqD,EAAQC,CAAS,EAAItD,EAAAA,SAAS,EAAK,EACpC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACuD,EAASC,CAAU,EAAIxD,EAAAA,SAAS,EAAE,EAGnC,CAACyD,CAAkB,EAAIzD,EAAAA,SAAS,IAAM,OAAO,KAAKiD,EAAK,MAAM,IAAI,CAAC,EAGlE,CAACnD,EAAUC,CAAW,EAAIC,WAAS,IAAM,CAE7C,MAAM0D,EAAsB,CAAC,EACV,OAAAD,EAAA,QAASE,GAAS,CACnCD,EAAoBC,CAAI,EAAI,CAC1B,MAAO,GACP,QAAS,GACT,QAAS,GACT,UAAW,GACX,SAAU,GACV,SAAU,CAAA,CACZ,CAAA,CACD,EAEM,CACL,KAAM,GACN,gBAAiB,GACjB,kBAAmB,GACnB,QAAS,GACT,OAAQ,QACR,cAAe,KACf,iBAAkB,GAClB,OAAQ,CAAC,EACT,YAAa,CAAC,EACd,OAAQ,CAAC,EACT,aAAcD,CAChB,CAAA,CACD,EAEK,CAACI,EAAYC,CAAa,EAAI/D,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACgE,EAAMC,CAAO,EAAIjE,EAAAA,SAAS,CAAA,CAAE,EAC7B,CAAC4D,EAAgBC,CAAiB,EAAI7D,EAAAA,SAAS,IAAI,EACnD,CAACkE,EAAcC,CAAe,EAAInE,EAAAA,SAAS,IAAI,EAC/C,CAAC2G,EAAgBC,CAAiB,EAAI5G,EAAAA,SAAS,CAAA,CAAE,EACjD,CAAC6G,GAAmBC,CAAoB,EAAI9G,EAAAA,SAAS,CAAC,EAE5DmB,EAAAA,UAAU,IAAM,EACG,SAAY,WACvB,GAAA,CACFjB,EAAW,EAAI,EAGf,KAAM,CAACkE,EAAkBC,CAAU,EAAI,MAAM,QAAQ,IAAI,CACvDnC,EAAS,cAAc,EACvBA,EAAS,QAAQ,CAAA,CAClB,EAWD,IATImD,EAAAjB,EAAiB,OAAjB,MAAAiB,EAAuB,SACXtB,EAAAK,EAAiB,KAAK,IAAI,GAGtCkB,EAAAjB,EAAW,OAAX,MAAAiB,EAAiB,SACXrB,EAAAI,EAAW,KAAK,IAAI,EAI1BjB,GAAaF,EAAI,CACX,QAAA,IAAI,2BAA4BA,CAAE,EAC1C,QAAQ,IAAI,gCAAgC,EAC5C,MAAM6D,EAAgB,MAAM7E,EAAS,WAAWgB,CAAE,EAG9C,GAFI,QAAA,IAAI,wBAAyB6D,CAAa,GAE9CvB,EAAAuB,EAAc,OAAd,MAAAvB,EAAoB,QAAS,CACzB,MAAAY,EAAUW,EAAc,KAAK,QAC3B,QAAA,IAAI,gBAAiBX,CAAO,EAGpCrG,EAAa+C,GAAU,UAAA,OACrB,GAAGA,EACH,KAAMsD,EAAQ,MAAQ,GACtB,gBAAiBA,EAAQ,iBAAmB,GAC5C,kBAAmBA,EAAQ,mBAAqB,GAChD,QAASA,EAAQ,SAAW,GAC5B,OAAQA,EAAQ,QAAU,QAC1B,iBAAkBA,EAAQ,kBAAoB,GAC9C,cAAaf,GAAAe,EAAQ,aAAR,YAAAf,GAAoB,IAAKZ,IAAMA,GAAE,cAAe,CAAC,EAC9D,SAAQa,EAAAc,EAAQ,OAAR,YAAAd,EAAc,IAAK5F,IAAMA,GAAE,SAAU,CAAA,CAAC,EAC9C,EAGF,MAAMsH,EAAsB,CAAE,GAAGlH,EAAS,YAAa,EAoBvD,GAnBIsG,EAAQ,cAAgB,MAAM,QAAQA,EAAQ,YAAY,GACpDA,EAAA,aAAa,QAASa,GAAgB,CACxBD,EAAAC,EAAY,QAAQ,EAAI,CAC1C,MAAOA,EAAY,OAAS,GAC5B,QAASA,EAAY,SAAW,GAChC,QAASA,EAAY,SAAW,GAChC,UAAWA,EAAY,WAAa,GACpC,SAAUA,EAAY,UAAY,GAClC,SAAUA,EAAY,UAAY,CAAA,CACpC,CAAA,CACD,EAGHlH,EAAa+C,IAAU,CACrB,GAAGA,EACH,aAAckE,CAAA,EACd,EAGEZ,EAAQ,QAAU,MAAM,QAAQA,EAAQ,MAAM,EAAG,CAC3C,QAAA,IAAI,2BAA4BA,EAAQ,MAAM,EACtD,MAAMc,EAAiBd,EAAQ,OAAO,IAAI,CAACK,EAAKU,KAAU,CAClD,MAAAC,GAAW3E,EAAYgE,EAAI,QAAQ,EACjC,eAAA,IAAI,SAASU,EAAK,KAAKV,EAAI,QAAQ,OAAOW,EAAQ,EAAE,EACrD,CACL,GAAIX,EAAI,GACR,KAAM,KACN,QAASW,GACT,IAAKX,EAAI,KAAO,GAChB,UAAWA,EAAI,UACf,SAAUA,EAAI,SACd,UAAWA,EAAI,SACjB,CAAA,CACD,EACO,QAAA,IAAI,6BAA8BS,CAAc,EACxDN,EAAkBM,CAAc,EAGhC,MAAMG,GAAeH,EAAe,UACjCT,GAAQA,EAAI,SACf,EACIY,KAAiB,IACnBP,EAAqBO,EAAY,CACnC,MAEQ,QAAA,IAAI,8BAA+BjB,EAAQ,MAAM,EAKzDA,EAAQ,gBACP,CAACA,EAAQ,QAAUA,EAAQ,OAAO,SAAW,IAE9BjC,EAAA1B,EAAY2D,EAAQ,aAAa,CAAC,CACpD,MAEQ,QAAA,MAAM,0BAA2BW,CAAa,EACtD3G,EAAS,6BAA6B,CACxC,QAEKD,EAAO,CACN,QAAA,MAAM,sBAAuBA,CAAK,EAC1CC,EAAS,qBAAqB,CAAA,QAC9B,CACAF,EAAW,EAAK,CAAA,CAEpB,GAES,CAAA,EACR,CAACgD,EAAIE,CAAS,CAAC,EAEZ,MAAAuB,EAAoB,CAACC,EAAO3C,IAAU,CAC1ClC,EAAa+C,IAAU,CACrB,GAAGA,EACH,CAAC8B,CAAK,EAAG3C,CAAA,EACT,CACJ,EAEM4C,EAA0B,CAACC,EAAUF,EAAO3C,IAAU,CAC1DlC,EAAa+C,IAAU,CACrB,GAAGA,EACH,aAAc,CACZ,GAAGA,EAAK,aACR,CAACgC,CAAQ,EAAG,CACV,GAAGhC,EAAK,aAAagC,CAAQ,EAC7B,CAACF,CAAK,EAAG3C,CAAA,CACX,CACF,EACA,CACJ,EAEMqF,GAAsBhH,GAAM,CAChC,MAAMiH,EAAQ,MAAM,KAAKjH,EAAE,OAAO,KAAK,EACnC,GAAAiH,EAAM,SAAW,EAAG,OAGxB,GAAIZ,EAAe,OAASY,EAAM,OAAS,GAAI,CAC7CnH,EAAS,uCAAuC,EAChD,MAAA,CAGF,MAAMoH,EAAYD,EAAM,IAAI,CAACvC,EAAMmC,KAAW,CAC5C,KAAAnC,EACA,QAAS,IAAI,gBAAgBA,CAAI,EACjC,IAAK,GACL,UAAW2B,EAAe,SAAW,GAAKQ,IAAU,CAAA,EACpD,EAEFP,EAAmB9D,GAAS,CAAC,GAAGA,EAAM,GAAG0E,CAAS,CAAC,EACnDpH,EAAS,EAAE,CACb,EAEMqH,GAAeN,GAAU,CAC7BP,EAAmB9D,GAAS,OAC1B,MAAM4E,EAAU5E,EAAK,OAAO,CAAC6E,EAAGC,IAAMA,IAAMT,CAAK,EAEjD,OAAI9B,EAAAvC,EAAKqE,CAAK,IAAV,MAAA9B,EAAa,WAAaqC,EAAQ,OAAS,IACrCA,EAAA,CAAC,EAAE,UAAY,GACvBZ,EAAqB,CAAC,GAEjBY,CAAA,CACR,CACH,EAEMG,GAAmBV,GAAU,CACjCP,EAAmB9D,GACjBA,EAAK,IAAI,CAAC2D,EAAKmB,KAAO,CACpB,GAAGnB,EACH,UAAWmB,IAAMT,CAAA,EACjB,CACJ,EACAL,EAAqBK,CAAK,CAC5B,EAEMW,EAAiB,CAACX,EAAOY,IAAQ,CACrCnB,EAAmB9D,GACjBA,EAAK,IAAI,CAAC2D,EAAKmB,IAAOA,IAAMT,EAAQ,CAAE,GAAGV,EAAK,IAAAsB,GAAQtB,CAAI,CAC5D,CACF,EAEMlG,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBgD,EAAU,EAAI,EACdlD,EAAS,EAAE,EACXoD,EAAW,EAAE,EAET,GAAA,CACI,MAAApC,EAAQ,aAAa,QAAQ,YAAY,EACzC8D,EAAiB,IAAI,SAGZA,EAAA,OAAO,OAAQpF,EAAS,IAAI,EAC5BoF,EAAA,OAAO,kBAAmBpF,EAAS,eAAe,EAClDoF,EAAA,OAAO,oBAAqBpF,EAAS,iBAAiB,EACtDoF,EAAA,OAAO,UAAWpF,EAAS,OAAO,EAClCoF,EAAA,OAAO,SAAUpF,EAAS,MAAM,EAChCoF,EAAA,OAAO,mBAAoBpF,EAAS,gBAAgB,EAGpDoF,EAAA,OACb,cACA,KAAK,UAAUpF,EAAS,WAAW,CACrC,EACAoF,EAAe,OAAO,SAAU,KAAK,UAAUpF,EAAS,MAAM,CAAC,EAChDoF,EAAA,OACb,eACA,KAAK,UAAUpF,EAAS,YAAY,CACtC,EAGA,IAAIkI,EAAa,EACFrB,EAAA,QAASsB,GAAc,CAEhCA,EAAU,OACG/C,EAAA,OAAO,SAAU+C,EAAU,IAAI,EAC9C/C,EAAe,OAAO,YAAY8C,CAAU,GAAIC,EAAU,GAAG,EAC7D/C,EAAe,OAAO,aAAa8C,CAAU,GAAIC,EAAU,SAAS,EACpED,IACF,CACD,EAGD,MAAMd,EAAiBP,EACpB,OAAQF,GAAQ,CAACA,EAAI,MAAQA,EAAI,EAAE,EACnC,IAAKA,IAAS,CACb,GAAIA,EAAI,GACR,IAAKA,EAAI,IACT,UAAWA,EAAI,UACf,UAAWA,EAAI,SAAA,EACf,EAEAS,EAAe,OAAS,GAC1BhC,EAAe,OAAO,iBAAkB,KAAK,UAAUgC,CAAc,CAAC,EAIpE,IAAA/B,EACA/B,EACF+B,EAAS,MAAMjD,EAAS,cAAcgB,EAAIgC,CAAc,EAE/CC,EAAA,MAAMjD,EAAS,cAAcgD,CAAc,EAGhD,KAAA,CAAE,SAAA1E,EAAU,KAAAC,CAAA,EAAS0E,EAE3B,GAAI3E,EAAS,IAAMC,GAAQA,EAAK,QAC9B+C,EACE,WAAWJ,EAAY,UAAY,SAAS,gBAC9C,EACA,WAAW,IAAM,CACfxD,EAAS,iBAAiB,GACzB,GAAI,MACF,CACL,MAAMwF,GACJ3E,GAAA,YAAAA,EAAM,UACN,aAAa2C,EAAY,SAAW,QAAQ,WAC9ChD,EAASgF,CAAY,CAAA,QAEhBjF,EAAO,CACN,QAAA,MAAM,wBAAyBA,CAAK,EAC5CC,EAAS,aAAagD,EAAY,SAAW,QAAQ,UAAU,CAAA,QAC/D,CACAE,EAAU,EAAK,CAAA,CAEnB,EAEA,OAAIrD,QAECqB,EACC,CAAA,SAAAR,EAAA,IAAC,MAAA,CACC,UAAU,mDACV,MAAO,CAAE,UAAW,OAAQ,EAE5B,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,mCAAmC,KAAK,SACrD,eAAC,OAAK,CAAA,UAAU,kBAAkB,SAAA,YAAU,CAAA,EAC9C,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,aAAa,SAAuB,yBAAA,CAAA,CAAA,CACnD,CAAA,CAAA,CAAA,EAEJ,EAMAF,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAO,GAAGqC,EAAY,OAAS,QAAQ,mBACvC,YAAY,4CAAA,CACd,EACAtC,EAAA,IAACQ,EAAY,CAAA,MAAO8B,EAAY,eAAiB,qBAC/C,SAAAxC,EAAAA,KAAC,OAAK,CAAA,SAAUL,EAAc,UAAU,aAErC,SAAA,CAAAJ,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAGDoD,GACE3C,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAyC,CAAA,EACH,EAIF3C,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,sBAAA,CACX,EAAe,gBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,gDAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,YAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhB,EAAS,KAChB,SAAWQ,GAAMqE,EAAkB,OAAQrE,EAAE,OAAO,KAAK,EACzD,UAAU,eACV,YAAY,kBAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,wEAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EAAe,oBAAA,EAElB,EACAF,EAAA,KAAC,SAAA,CACC,MAAOd,EAAS,OAChB,SAAWQ,GAAMqE,EAAkB,SAAUrE,EAAE,OAAO,KAAK,EAC3D,UAAU,eAEV,SAAA,CAACQ,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAS,WAAA,CAAA,CAAA,CAAA,CACrC,EACCA,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,8CAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iCACL,UAAU,sBAAA,CACX,EAAe,gBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,mDAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,wBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,KAAK,OACL,MAAOhB,EAAS,gBAChB,SAAWQ,GACTqE,EAAkB,kBAAmBrE,EAAE,OAAO,KAAK,EAErD,UAAU,eACV,YAAY,SAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,8DAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,MAAA,CACX,EAAe,gCAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,KAAK,OACL,MAAOhB,EAAS,kBAChB,SAAWQ,GACTqE,EAAkB,oBAAqBrE,EAAE,OAAO,KAAK,EAEvD,UAAU,eACV,YAAY,OAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,4CAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,UAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,MACL,MAAOhB,EAAS,QAChB,SAAWQ,GAAMqE,EAAkB,UAAWrE,EAAE,OAAO,KAAK,EAC5D,UAAU,eACV,YAAY,0BAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,kCAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,sBAAA,CACX,EAAe,gBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,kEAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EAAe,wBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,OAAO,UACP,SAAQ,GACR,SAAUwG,GACV,UAAU,cAAA,CACZ,EACCxG,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,+EAAA,CAAA,CAAA,EACF,EAEC6F,EAAe,OAAS,GACtB/F,EAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,OAAO,SAAA,CAAA,mBACF+F,EAAe,OAAO,MAAA,EACzC,QACC,MAAI,CAAA,UAAU,MACZ,SAAAA,EAAe,IAAI,CAACsB,EAAWd,IAC9BrG,EAAA,IAAC,OAAgB,UAAU,gBACzB,SAACF,OAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,IAAKmH,EAAU,QACf,IAAK,WAAWd,EAAQ,CAAC,GACzB,UAAU,eACV,MAAO,CAAE,OAAQ,QAAS,UAAW,OAAQ,EAC7C,QAAU7G,GAAM,CACN,QAAA,MACN,wBACA2H,EAAU,OACZ,EACE3H,EAAA,OAAO,MAAM,QAAU,MAC3B,EACA,OAAQ,IAAM,CACJ,QAAA,IACN,6BACA2H,EAAU,OACZ,CAAA,CACF,CACF,EACAnH,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,0DACV,QAAS,IAAM2G,GAAYN,CAAK,EACjC,SAAA,GAAA,CAED,EACCc,EAAU,WACTnH,EAAAA,IAAC,OAAK,CAAA,UAAU,uDAAuD,SAEvE,eAAA,CAAA,CAAA,EAEJ,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,OACb,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAU,+BACV,YAAY,WACZ,MAAOmH,EAAU,IACjB,SAAW3H,GACTwH,EAAeX,EAAO7G,EAAE,OAAO,KAAK,CAAA,CAAA,EAG1C,QACC,MAAI,CAAA,UAAU,eACZ,SAAA,CAAC2H,EAAU,WACVnH,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,iCACV,QAAS,IAAM+G,GAAgBV,CAAK,EACrC,SAAA,gBAAA,CAAA,CAIL,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAAA,EA3DQA,CA4DV,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,EAGAvG,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,+BAAgC,CAAA,EAAI,0BAAA,EAEnD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAGlC,6EAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,sBACZ,SAAmB2C,EAAA,IAAKE,GACvB/C,EAAA,KAAC,SAAA,CAEC,KAAK,SACL,QAAS,IAAMiD,EAAkBF,CAAI,EACrC,UAAW,gBACTC,IAAmBD,EAAO,SAAW,EACvC,GAEA,SAAA,CAAC7C,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5B6C,EAAK,YAAY,EACjBA,IAAS,MACR7C,EAAAA,IAAC,OAAK,CAAA,UAAU,aAAa,SAAU,YAAA,CAAA,CAAA,CAAA,EAVpC6C,CAaR,CAAA,EACH,EAGA/C,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,UACxB8C,EAAe,YAAY,EAAE,IACpCA,IAAmB,MAClB9C,EAAAA,IAAC,OAAK,CAAA,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAExC,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAOuE,EAAAvF,EAAS,aAAa8D,CAAc,IAApC,YAAAyB,EAAuC,QAAS,GACvD,SAAW/E,GACTuE,EACEjB,EACA,QACAtD,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,sBACZ,SAAUsD,IAAmB,IAAA,CAC/B,EACAhD,EAAAA,KAAC,QAAM,CAAA,UAAU,uBAAuB,SAAA,CAAA,oCACJ,IACjCgD,EAAe,YAAY,CAAA,CAC9B,CAAA,CAAA,EACF,EAEAhD,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAAI,YACtB8C,EAAe,YAAY,EAAE,GAAA,EACzC,EACA9C,EAAA,IAAC,WAAA,CACC,QAAOwE,EAAAxF,EAAS,aAAa8D,CAAc,IAApC,YAAA0B,EAAuC,UAAW,GACzD,SAAWhF,GACTuE,EACEjB,EACA,UACAtD,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,kCAAA,CACd,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,gFAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,2BACL,UAAU,MAAA,CACX,EAAe,YACN8C,EAAe,YAAY,EAAE,IACtCA,IAAmB,MAClB9C,EAAAA,IAAC,OAAK,CAAA,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAExC,EACAA,EAAA,IAACyE,GAAA,CACC,UAASC,EAAA1F,EAAS,aAAa8D,CAAc,IAApC,YAAA4B,EAAuC,UAAW,GAC3D,SAAWC,GACTZ,EAAwBjB,EAAgB,UAAW6B,CAAI,EAEzD,YAAY,+GAAA,CACd,EACA7E,EAAAA,KAAC,QAAM,CAAA,UAAU,uBACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,yBACL,UAAU,MAAA,CACX,EAAe,oJAAA,CAIlB,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,aAAc,CAAA,EAAI,eAClB8C,EAAe,YAAY,EAAE,GAAA,EAC5C,EACA9C,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAO4E,EAAA5F,EAAS,aAAa8D,CAAc,IAApC,YAAA8B,EAAuC,YAAa,GAC3D,SAAWpF,GACTuE,EACEjB,EACA,YACAtD,EAAE,OAAO,KACX,EAEF,UAAU,eACV,YAAY,uBACZ,UAAU,IAAA,CACZ,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,kDAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,aAAc,CAAA,EAAI,qBACZ8C,EAAe,YAAY,EAAE,GAAA,EAClD,EACA9C,EAAA,IAAC,WAAA,CACC,QAAO6E,EAAA7F,EAAS,aAAa8D,CAAc,IAApC,YAAA+B,EAAuC,WAAY,GAC1D,SAAWrF,GACTuE,EACEjB,EACA,WACAtD,EAAE,OAAO,KACX,EAEF,KAAM,EACN,UAAU,eACV,YAAY,6BACZ,UAAU,KAAA,CACZ,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,yDAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,YACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,gCACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,sBAAA,CACX,EAAe,mBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,gDAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EAAe,YAAA,EAElB,EACCA,MAAA,MAAA,CAAI,UAAU,kBACZ,YAAcgD,EAAW,OAAS,EACjCA,EAAW,IAAK8B,GACbhF,EAAA,KAAA,MAAA,CAAsB,UAAU,kBAC/B,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,KAAK,WACL,GAAI,YAAY8E,EAAS,EAAE,GAC3B,QAAS9F,EAAS,YAAY,SAAS8F,EAAS,EAAE,EAClD,SAAU,IAAM,CACR,MAAAC,EACJ/F,EAAS,YAAY,SAAS8F,EAAS,EAAE,EACrC9F,EAAS,YAAY,OAClBoD,GAAOA,IAAO0C,EAAS,IAE1B,CAAC,GAAG9F,EAAS,YAAa8F,EAAS,EAAE,EAC3CjB,EAAkB,cAAekB,CAAc,CAAA,CACjD,CACF,EACA/E,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,QAAS,YAAY8E,EAAS,EAAE,GAE/B,SAASA,EAAA,IAAA,CAAA,CApBJ,CAAA,EAAAA,EAAS,EAsBnB,CACD,QAEA,IAAE,CAAA,UAAU,aAAa,SAAA,yBAAuB,CAAA,CAErD,CAAA,CAAA,EACF,EAEAhF,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,MAAA,CACX,EAAe,MAAA,EAElB,EACCA,MAAA,MAAA,CAAI,UAAU,YACZ,YAAQkD,EAAK,OAAS,EACrBA,EAAK,IAAK8B,GACPlF,EAAA,KAAA,MAAA,CAAiB,UAAU,kBAC1B,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,KAAK,WACL,GAAI,OAAOgF,EAAI,EAAE,GACjB,QAAShG,EAAS,OAAO,SAASgG,EAAI,EAAE,EACxC,SAAU,IAAM,CACR,MAAAC,EAAYjG,EAAS,OAAO,SAASgG,EAAI,EAAE,EAC7ChG,EAAS,OAAO,OAAQoD,GAAOA,IAAO4C,EAAI,EAAE,EAC5C,CAAC,GAAGhG,EAAS,OAAQgG,EAAI,EAAE,EAC/BnB,EAAkB,SAAUoB,CAAS,CAAA,CACvC,CACF,EACAjF,EAAA,IAAC,QAAA,CACC,UAAU,mBACV,QAAS,OAAOgF,EAAI,EAAE,GAErB,SAAIA,EAAA,IAAA,CAAA,CAjBC,CAAA,EAAAA,EAAI,EAmBd,CACD,QAEA,IAAE,CAAA,UAAU,aAAa,SAAA,mBAAiB,CAAA,CAE/C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,MACb,SAAClF,EAAA,KAAA,MAAA,CAAI,UAAU,qBACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,4CACV,SAAUuC,EAET,WAEGzC,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,UAAU,uCAAwC,CAAA,EACvDsC,EAAY,cAAgB,aAAA,CAAA,CAC/B,EAGExC,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAAA,IAAA,IAAA,CAAE,UAAU,eAAgB,CAAA,EAC5BsC,EAAY,iBAAmB,gBAAA,CAClC,CAAA,CAAA,CAEJ,QACC,MAAI,CAAA,UAAU,OACb,SAACxC,EAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAACE,EAAAA,IAAA,IAAA,CAAE,UAAU,cAAe,CAAA,EAC3BsC,EACG,wDACA,wDAAA,CAAA,CACN,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,2GC36BM8E,GAAqB,IAAM,OACzB,KAAA,CAAE,EAAAxI,CAAE,EAAIC,GAAe,EACvB,CAACM,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACmI,EAAWC,CAAY,EAAIpI,EAAAA,SAAS,YAAY,EACjD,CAACqI,EAAkBC,CAAmB,EAAItI,EAAAA,SAAS,KAAK,EACxD,CAACuI,EAAeC,CAAgB,EAAIxI,EAAAA,SAAS,IAAI,EACjD,CAACyI,EAAWC,CAAY,EAAI1I,EAAAA,SAAS,CAAA,CAAE,EAGvC2I,EAAmB,CACvB,CAAE,MAAO,UAAW,MAAO,UAAW,EACtC,CAAE,MAAO,WAAY,MAAO,WAAY,EACxC,CAAE,MAAO,aAAc,MAAO,cAAe,EAC7C,CAAE,MAAO,aAAc,MAAO,cAAe,EAC7C,CAAE,MAAO,cAAe,MAAO,eAAgB,EAC/C,CAAE,MAAO,cAAe,MAAO,eAAgB,EAC/C,CAAE,MAAO,cAAe,MAAO,eAAgB,CACjD,EAGMC,EAAkB,CACtB,CAAE,MAAO,MAAO,MAAO,gBAAiB,KAAM,IAAK,EACnD,CAAE,MAAO,KAAM,MAAO,UAAW,KAAM,MAAO,EAC9C,CAAE,MAAO,KAAM,MAAO,WAAY,KAAM,MAAO,EAC/C,CAAE,MAAO,KAAM,MAAO,UAAW,KAAM,MAAO,EAC9C,CAAE,MAAO,KAAM,MAAO,SAAU,KAAM,MAAO,EAC7C,CAAE,MAAO,KAAM,MAAO,UAAW,KAAM,MAAO,CAChD,EAGAzH,EAAAA,UAAU,IAAM,EACY,SAAY,CAChC,GAAA,CAMF,GALAjB,EAAW,EAAI,EACfE,EAAS,EAAE,EAIP,CADU,aAAa,QAAQ,YAAY,EACnC,CACVA,EACE,6DACF,EACAF,EAAW,EAAK,EAChB,MAAA,CAIF,KAAM,CAAC2I,EAAiBC,CAAW,EAAI,MAAM,QAAQ,IAAI,CACvD5G,EAAS,iBAAiBiG,EAAWE,CAAgB,EACrDnG,EAAS,sBAAsBiG,EAAWE,CAAgB,CAAA,CAC3D,EAGD,GAAIQ,EAAgB,SAAS,IAAMA,EAAgB,KACjDL,EAAiBK,EAAgB,KAAK,MAAQA,EAAgB,IAAI,MAC7D,CAML,GALQ,QAAA,MACN,wBACAA,EAAgB,SAAS,OACzBA,EAAgB,SAAS,UAC3B,EAEEA,EAAgB,SAAS,SAAW,KACpCA,EAAgB,SAAS,SAAW,IACpC,CACAzI,EAAS,6CAA6C,EACtD,aAAa,WAAW,YAAY,EACpC,MAAA,CAEFA,EAAS,+BAA+B,CAAA,CAItC0I,EAAY,SAAS,IAAMA,EAAY,KACzCJ,EAAaI,EAAY,KAAK,MAAQA,EAAY,IAAI,GAE9C,QAAA,MACN,8BACAA,EAAY,SAAS,OACrBA,EAAY,SAAS,UACvB,EACAJ,EAAa,CAAA,CAAE,SAEVvI,EAAO,CACN,QAAA,MAAM,gCAAiCA,CAAK,EAChDA,EAAM,SAAWA,EAAM,QAAQ,SAAS,OAAO,EACjDC,EACE,0EACF,EAEAA,EAAS,kDAAkD,CAC7D,QACA,CACAF,EAAW,EAAK,CAAA,CAEpB,GAEkB,CAAA,EACjB,CAACiI,EAAWE,CAAgB,CAAC,EAE1B,MAAAU,EAAyBC,GAAiB,CAC9CZ,EAAaY,CAAY,CAC3B,EAEMC,EAAwBC,GAAgB,CAC5CZ,EAAoBY,CAAW,CACjC,EAEA,OAAIjJ,EAEAW,EAAA,KAACU,EAAY,CAAA,MAAM,iBACjB,SAAA,CAAAR,EAAA,IAACC,EAAA,CACC,MAAM,yBACN,YAAY,wCAAA,CACd,EACAD,EAAA,IAAC,MAAA,CACC,UAAU,mDACV,MAAO,CAAE,UAAW,OAAQ,EAE5B,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,8BAA8B,KAAK,SAChD,SAAAA,EAAA,IAAC,OAAK,CAAA,UAAU,kBAAkB,SAAA,YAAA,CAAU,CAC9C,CAAA,CAAA,CAAA,CACF,EACF,EAKFF,EAAA,KAACU,EAAY,CAAA,MAAM,sBACjB,SAAA,CAAAR,EAAA,IAACC,EAAA,CACC,MAAM,8BACN,YAAY,mEAAA,CACd,EAEAH,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAAAA,EAAAA,IAAC,OAAI,UAAU,oDACb,gBAAC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAmB,sBAAA,EACpDF,EAAAA,KAAC,IAAE,CAAA,UAAU,iCAAiC,SAAA,CAAA,4EAEjC,IACXE,EAAA,IAAC,IAAA,CACC,KAAK,mEACL,OAAO,SACP,IAAI,sBACJ,UAAU,eACX,SAAA,YAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAECX,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,0BAA0B,KAAK,QAC5C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAIFS,EAAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,WACb,SAAAA,EAAA,IAACqI,GAAA,CACC,QAASR,EACT,MAAOR,EACP,SAAUY,EACV,eAAgBR,GAAA,YAAAA,EAAe,cAAA,CAAA,EAEnC,EACAzH,EAAAA,IAAC,MAAI,CAAA,UAAU,WACb,SAAAA,EAAA,IAACsI,GAAA,CACC,QAASR,EACT,MAAOP,EACP,SAAUY,CAAA,CAAA,CAEd,CAAA,CAAA,EACF,EAGCV,SACE,MAAI,CAAA,UAAU,WACb,SAACzH,EAAA,IAAA,MAAA,CAAI,UAAU,SACb,SAAAA,EAAA,IAACuI,GAAA,CACC,KAAMd,EAAc,SACpB,iBAAAF,CAAA,GAEJ,CACF,CAAA,EAIFzH,EAAAA,KAAC,MAAI,CAAA,UAAU,WAEb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,uBACZ,SACCyH,GAAAzH,EAAA,IAACwI,GAAA,CACC,KAAMf,EAAc,UACpB,UAAAJ,EACA,iBAAAE,CAAA,CAAA,EAGN,EAGCvH,EAAA,IAAA,MAAA,CAAI,UAAU,uBACZ,SACCyH,GAAAzH,EAAA,IAACyI,GAAA,CACC,KAAMhB,EAAc,YACpB,MAAO,4BACLF,IAAqB,MACjB,MACEhD,EAAAuD,EAAgB,KACbY,GAAMA,EAAE,QAAUnB,CACrB,IAFA,YAAAhD,EAEG,KACL,IACA,EACN,GACA,iBAAAgD,CAAA,CAAA,CAGN,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,WACb,SAACvH,MAAA,MAAA,CAAI,UAAU,SACb,SAAAA,EAAA,IAAC2I,GAAA,CACC,KAAMhB,EACN,QAAAxI,EACA,UAAAkI,EACA,iBAAAE,CAAA,GAEJ,CACF,CAAA,EAGAvH,EAAA,IAAC,MAAI,CAAA,UAAU,WACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,SACb,SAAAF,OAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,kBACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EAAe,sBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAGpC,iEAAA,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAA,IAAC4I,GAAA,CACC,UAAAvB,EACA,iBAAAE,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAvH,EAAA,IAAC,MAAI,CAAA,UAAU,WACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,SACb,SAAAF,OAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,kBACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,sBACL,UAAU,MAAA,CACX,EAAe,wBAAA,EAElB,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAEpC,+CAAA,CAAA,CAAA,EACF,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAA,IAAC6I,GAAA,CACC,UAAAxB,EACA,iBAAAE,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,2GCnTMuB,GAAkB,IAAM,CACX/J,EAAY,EAC7B,KAAM,CAACiE,EAAYC,CAAa,EAAI/D,EAAAA,SAAS,CAAA,CAAE,EACzC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACuD,EAASC,CAAU,EAAIxD,EAAAA,SAAS,EAAE,EACnC,CAAC6J,EAAWC,CAAY,EAAI9J,EAAAA,SAAS,EAAK,EAC1C,CAAC+J,EAAiBC,CAAkB,EAAIhK,EAAAA,SAAS,IAAI,EACrD,CAACF,EAAUC,CAAW,EAAIC,WAAS,CACvC,KAAM,GACN,YAAa,GACb,MAAO,SAAA,CACR,EAGDmB,EAAAA,UAAU,IAAM,CACC8I,EAAA,CACjB,EAAG,EAAE,EAEL,MAAMA,EAAiB,SAAY,CAC7B,GAAA,CACF/J,EAAW,EAAI,EACf,KAAM,CAAE,SAAAM,EAAU,KAAAC,CAAS,EAAA,MAAMyB,EAAS,cAAc,EAEpDzB,EAAK,QACOsD,EAAAtD,EAAK,MAAQ,EAAE,EAEpBL,EAAAK,EAAK,SAAW,2BAA2B,QAE/CN,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,EAC7CC,EAAS,kCAAkC,CAAA,QAC3C,CACAF,EAAW,EAAK,CAAA,CAEpB,EAEMyE,EAAoB,CAACC,EAAO3C,IAAU,CAC1ClC,EAAa+C,IAAU,CACrB,GAAGA,EACH,CAAC8B,CAAK,EAAG3C,CAAA,EACT,CACJ,EAEM1B,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBF,EAAS,EAAE,EACXoD,EAAW,EAAE,EAET,GAAA,CACF,IAAIhD,EAAUC,EAEVsJ,EACD,CAAE,SAAAvJ,EAAU,KAAAC,GAAS,MAAMyB,EAAS,eACnC6H,EAAgB,GAChBjK,CACF,EAEC,CAAE,SAAAU,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,eAAepC,CAAQ,EAG1DW,EAAK,SACP+C,EACEuG,EACI,iCACA,gCACN,EACAD,EAAa,EAAK,EAClBE,EAAmB,IAAI,EACvBjK,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC5CkK,EAAA,GAEN7J,EAAAK,EAAK,SAAW,yBAAyB,QAE7CN,EAAO,CACN,QAAA,MAAM,uBAAwBA,CAAK,EAC3CC,EAAS,kCAAkC,CAAA,CAE/C,EAEM8J,EAActE,GAAa,CAC/BoE,EAAmBpE,CAAQ,EACf7F,EAAA,CACV,KAAM6F,EAAS,KACf,YAAaA,EAAS,aAAe,GACrC,MAAOA,EAAS,OAAS,SAAA,CAC1B,EACDkE,EAAa,EAAI,CACnB,EAEM3H,EAAe,MAAOgI,GAAe,CACzC,GACG,OAAO,QACN,8EAAA,EAMA,GAAA,CACF,KAAM,CAAE,SAAA3J,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,eAAeiI,CAAU,EAE/D1J,EAAK,SACP+C,EAAW,gCAAgC,EAC5ByG,EAAA,GAEN7J,EAAAK,EAAK,SAAW,2BAA2B,QAE/CN,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,EAC7CC,EAAS,kCAAkC,CAAA,CAE/C,EAEMgK,EAAkB,IAAM,CAC5BJ,EAAmB,IAAI,EACvBjK,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC3D+J,EAAa,EAAI,CACnB,EAEMO,EAAa,IAAM,CACvBP,EAAa,EAAK,EAClBE,EAAmB,IAAI,EACvBjK,EAAY,CAAE,KAAM,GAAI,YAAa,GAAI,MAAO,UAAW,EAC3DK,EAAS,EAAE,CACb,EAEA,OAEIQ,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,4BACN,YAAY,4CACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACU,EAAY,CAAA,MAAM,aAEjB,SAAA,CAAAR,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,kGAGlC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAASwJ,EACT,UAAU,kDAEV,SAAA,CAAAtJ,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,cAAA,CAAA,CAAA,CAGpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCX,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAGDoD,GACE3C,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAyC,CAAA,EACH,EAIDzC,EAAA,IAAA,MAAA,CAAI,UAAU,cACZ,SACCb,EAAAW,EAAA,KAAC,MAAI,CAAA,UAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,EAAA,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,CAAA,CAC/C,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAE1C,uBAAA,CAAA,CAAA,CAAA,CACF,EACEgD,EAAW,SAAW,EACvBlD,EAAA,KAAA,MAAA,CAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,WAAA,EACnD,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,gCAAA,CACX,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,sBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,kEAAA,EACAF,EAAA,KAAC,SAAA,CACC,QAASwJ,EACT,UAAU,kCAEV,SAAA,CAAAtJ,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,uBAAA,CAAA,CAAA,CAElB,CAAA,CACF,EAIEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,CAAA,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAQ,UAAA,CAAA,EACZA,EAAAA,IAAC,MAAG,SAAW,aAAA,CAAA,EACfA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,QACC,QACE,CAAA,SAAAgD,EAAW,IAAK8B,wBACd,KACC,CAAA,SAAA,CAAA9E,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,UAAU,eACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,gBAAiB8E,EAAS,OAAS,SAAA,CACrC,CACD,SACA,MACC,CAAA,SAAA,CAAA9E,EAAA,IAAC,MAAI,CAAA,UAAU,UAAW,SAAA8E,EAAS,KAAK,EACxChF,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BgF,EAAS,IAAA,CACb,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACA9E,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,OAAA,CAAK,UAAU,aACb,SAAA8E,EAAS,aAAe,gBAAA,CAC3B,CACF,CAAA,EACC9E,MAAA,KAAA,CACC,SAACF,EAAAA,KAAA,OAAA,CAAK,UAAU,qBACb,SAAA,GAAAyE,EAAAO,EAAS,SAAT,YAAAP,EAAiB,QAAS,EAAE,QAAA,CAAA,CAC/B,CACF,CAAA,EACAvE,MAAC,MACE,SAAI,IAAA,KAAK8E,EAAS,SAAS,EAAE,qBAChC,QACC,KACC,CAAA,SAAAhF,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IAAMoJ,EAAWtE,CAAQ,EAClC,UAAU,iCACV,MAAM,OAEN,SAAA9E,EAAAA,IAAC,eAAa,CAAA,KAAK,gBAAiB,CAAA,CAAA,CACtC,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMqB,EAAayD,EAAS,EAAE,EACvC,UAAU,gCACV,MAAM,SAEN,SAAA9E,EAAAA,IAAC,eAAa,CAAA,KAAK,4BAA6B,CAAA,CAAA,CAAA,CAClD,CAAA,CACF,CACF,CAAA,CAAA,GAjDO8E,EAAS,EAkDlB,EACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGA9E,EAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,UACZ,SAAAgD,EAAW,IAAK8B,UACf9E,OAAAA,EAAA,IAAC,MAAsB,CAAA,UAAU,SAC/B,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,cACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,UAAU,eACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,gBACE8E,EAAS,OAAS,SAAA,CACtB,CACD,EACDhF,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAE,EAAA,IAAC,KAAG,CAAA,UAAU,eACX,SAAA8E,EAAS,KACZ,EACAhF,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BgF,EAAS,IAAA,CACb,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAhF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,cAAA,EACCA,EAAA,IAAA,QAAA,CACE,SAAS8E,EAAA,aAAe,gBAC3B,CAAA,CAAA,EACF,EAEAhF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,QAAA,EACAF,EAAAA,KAAC,OAAK,CAAA,UAAU,qBACb,SAAA,GAAAyE,EAAAO,EAAS,SAAT,YAAAP,EAAiB,QAAS,EAAE,QAAA,CAC/B,CAAA,CAAA,EACF,EAEAzE,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,UAAA,EACAA,EAAAA,IAAC,SACE,SAAI,IAAA,KACH8E,EAAS,SACX,EAAE,oBACJ,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,SACb,SAAChF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAAS,IAAMsJ,EAAWtE,CAAQ,EAClC,UAAU,2CACV,MAAM,OAEN,SAAA,CAAA9E,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,MAAA,CAAA,CAElB,EAEAF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMuB,EAAayD,EAAS,EAAE,EACvC,UAAU,0CACV,MAAM,SAEN,SAAA,CAAA9E,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EAAe,QAAA,CAAA,CAAA,CAElB,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,CAAA,EApFQ8E,EAAS,EAqFnB,EACD,EACH,CACF,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EAGCiE,GACE/I,EAAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,QAASuJ,EACtC,SAAAzJ,EAAAA,KAAC,MAAI,CAAA,UAAU,gBAAgB,QAAUN,GAAMA,EAAE,gBAC/C,EAAA,SAAA,CAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,cACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,oBACL,UAAU,MAAA,CACX,EACAiJ,EAAkB,gBAAkB,qBAAA,EACvC,EACAjJ,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,cACV,QAASuJ,EAET,SAAAvJ,EAAAA,IAAC,eAAa,CAAA,KAAK,yBAA0B,CAAA,CAAA,CAAA,CAC/C,EACF,EAEAF,EAAAA,KAAC,OAAK,CAAA,SAAUL,EACd,SAAA,CAAAO,EAAAA,IAAC,OAAI,UAAU,aACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,iBAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhB,EAAS,KAChB,SAAWQ,GACTqE,EAAkB,OAAQrE,EAAE,OAAO,KAAK,EAE1C,UAAU,eACV,YAAY,sBACZ,SAAQ,EAAA,CAAA,CACV,EACF,EAEAM,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,kBACL,UAAU,MAAA,CACX,EAAe,aAAA,EAElB,EACAA,EAAA,IAAC,WAAA,CACC,MAAOhB,EAAS,YAChB,SAAWQ,GACTqE,EAAkB,cAAerE,EAAE,OAAO,KAAK,EAEjD,UAAU,eACV,KAAM,EACN,YAAY,oCAAA,CAAA,CACd,EACF,EAEAM,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,MAAA,CACX,EAAe,OAAA,EAElB,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,QACL,MAAOhB,EAAS,MAChB,SAAWQ,GACTqE,EAAkB,QAASrE,EAAE,OAAO,KAAK,EAE3C,UAAU,kCACV,MAAO,CAAE,MAAO,OAAQ,OAAQ,MAAO,CAAA,CACzC,EACAQ,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhB,EAAS,MAChB,SAAWQ,GACTqE,EAAkB,QAASrE,EAAE,OAAO,KAAK,EAE3C,UAAU,eACV,YAAY,SAAA,CAAA,CACd,EACF,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAExC,2CAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAASuJ,EACT,UAAU,sCACX,SAAA,QAAA,CAED,EACAzJ,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAU,kCAEV,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAiJ,EAAkB,kBAAoB,iBAAA,CAAA,CAAA,CACzC,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,2GChgBMO,GAAY,IAAM,CACLzK,EAAY,EAC7B,KAAM,CAACmE,EAAMC,CAAO,EAAIjE,EAAAA,SAAS,CAAA,CAAE,EAC7B,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACuD,EAASC,CAAU,EAAIxD,EAAAA,SAAS,EAAE,EACnC,CAAC6J,EAAWC,CAAY,EAAI9J,EAAAA,SAAS,EAAK,EAC1C,CAACuK,EAAYC,CAAa,EAAIxK,EAAAA,SAAS,IAAI,EAC3C,CAACF,EAAUC,CAAW,EAAIC,WAAS,CACvC,KAAM,EAAA,CACP,EAGDmB,EAAAA,UAAU,IAAM,CACLsJ,EAAA,CACX,EAAG,EAAE,EAEL,MAAMA,EAAW,SAAY,CACvB,GAAA,CACFvK,EAAW,EAAI,EACf,KAAM,CAAE,SAAAM,EAAU,KAAAC,CAAS,EAAA,MAAMyB,EAAS,QAAQ,EAE9CzB,EAAK,QACCwD,EAAAxD,EAAK,MAAQ,EAAE,EAEdL,EAAAK,EAAK,SAAW,qBAAqB,QAEzCN,EAAO,CACN,QAAA,MAAM,mBAAoBA,CAAK,EACvCC,EAAS,kCAAkC,CAAA,QAC3C,CACAF,EAAW,EAAK,CAAA,CAEpB,EAEMyE,EAAoB,CAACC,EAAO3C,IAAU,CAC1ClC,EAAa+C,IAAU,CACrB,GAAGA,EACH,CAAC8B,CAAK,EAAG3C,CAAA,EACT,CACJ,EAEM1B,EAAe,MAAOD,GAAM,CAChCA,EAAE,eAAe,EACjBF,EAAS,EAAE,EACXoD,EAAW,EAAE,EAET,GAAA,CACF,IAAIhD,EAAUC,EAEV8J,EACD,CAAE,SAAA/J,EAAU,KAAAC,GAAS,MAAMyB,EAAS,UACnCqI,EAAW,GACXzK,CACF,EAEC,CAAE,SAAAU,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,UAAUpC,CAAQ,EAGrDW,EAAK,SACP+C,EACE+G,EAAa,4BAA8B,2BAC7C,EACAT,EAAa,EAAK,EAClBU,EAAc,IAAI,EACNzK,EAAA,CAAE,KAAM,GAAI,EACf0K,EAAA,GAEArK,EAAAK,EAAK,SAAW,oBAAoB,QAExCN,EAAO,CACN,QAAA,MAAM,kBAAmBA,CAAK,EACtCC,EAAS,kCAAkC,CAAA,CAE/C,EAEM8J,EAAcpE,GAAQ,CAC1B0E,EAAc1E,CAAG,EACL/F,EAAA,CACV,KAAM+F,EAAI,IAAA,CACX,EACDgE,EAAa,EAAI,CACnB,EAEM3H,EAAe,MAAOuI,GAAU,CACpC,GACG,OAAO,QACN,yEAAA,EAMA,GAAA,CACF,KAAM,CAAE,SAAAlK,EAAU,KAAAC,CAAA,EAAS,MAAMyB,EAAS,UAAUwI,CAAK,EAErDjK,EAAK,SACP+C,EAAW,2BAA2B,EAC7BiH,EAAA,GAEArK,EAAAK,EAAK,SAAW,sBAAsB,QAE1CN,EAAO,CACN,QAAA,MAAM,oBAAqBA,CAAK,EACxCC,EAAS,kCAAkC,CAAA,CAE/C,EAEMgK,EAAkB,IAAM,CAC5BI,EAAc,IAAI,EACNzK,EAAA,CAAE,KAAM,GAAI,EACxB+J,EAAa,EAAI,CACnB,EAEMO,EAAa,IAAM,CACvBP,EAAa,EAAK,EAClBU,EAAc,IAAI,EACNzK,EAAA,CAAE,KAAM,GAAI,EACxBK,EAAS,EAAE,CACb,EAEA,OAEIQ,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAM,sBACN,YAAY,sCACZ,QAAS,EAAA,CACX,EAEAH,EAAAA,KAACU,EAAY,CAAA,MAAM,OAEjB,SAAA,CAAAR,EAAAA,IAAC,OAAI,UAAU,QACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,+BACb,SAAAA,EAAA,IAAC,KAAE,UAAU,qBAAqB,4GAGlC,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAF,EAAA,KAAC,SAAA,CACC,QAASwJ,EACT,UAAU,kDAEV,SAAA,CAAAtJ,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,SAAA,CAAA,CAAA,CAGpB,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGCX,GACES,EAAAA,KAAA,MAAA,CAAI,UAAU,2BAA2B,KAAK,QAC7C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EACAX,CAAA,EACH,EAGDoD,GACE3C,EAAAA,KAAA,MAAA,CAAI,UAAU,4BAA4B,KAAK,QAC9C,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAyC,CAAA,EACH,EAIDzC,EAAA,IAAA,MAAA,CAAI,UAAU,cACZ,SACCb,EAAAW,EAAA,KAAC,MAAI,CAAA,UAAU,oBAAoB,MAAO,CAAE,QAAS,WACnD,EAAA,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,qBACL,UAAU,8BACV,MAAO,CAAE,UAAW,yBAA0B,CAAA,CAC/C,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,2BAA2B,SAAe,iBAAA,CAAA,CAAA,CAAA,CAC3D,EACEkD,EAAK,SAAW,EACjBpD,EAAA,KAAA,MAAA,CAAI,UAAU,oBAAoB,MAAO,CAAE,QAAS,WAAA,EACnD,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,gCAAA,CACX,EACAA,EAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,gBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAEnC,6DAAA,EACAF,EAAA,KAAC,SAAA,CACC,QAASwJ,EACT,UAAU,kCAEV,SAAA,CAAAtJ,EAAA,IAAC,eAAA,CACC,KAAK,wBACL,UAAU,MAAA,CACX,EAAe,kBAAA,CAAA,CAAA,CAElB,CAAA,CACF,EAIEF,EAAAA,KAAAC,EAAA,SAAA,CAAA,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAAAF,OAAC,QAAM,CAAA,UAAU,QACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAQ,UAAA,CAAA,EACZA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,QACC,QACE,CAAA,SAAAkD,EAAK,IAAK8B,wBACR,KACC,CAAA,SAAA,CAAAhF,MAAC,KACC,CAAA,SAAAF,EAAAA,KAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,sBAAA,CACX,SACA,MACC,CAAA,SAAA,CAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,UAAW,SAAAgF,EAAI,KAAK,EACnClF,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BkF,EAAI,IAAA,CACR,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACChF,MAAA,KAAA,CACC,SAACF,EAAAA,KAAA,OAAA,CAAK,UAAU,qBACb,SAAA,GAAAyE,EAAAS,EAAI,SAAJ,YAAAT,EAAY,QAAS,EAAE,QAAA,CAAA,CAC1B,CACF,CAAA,EACAvE,MAAC,MACE,SAAI,IAAA,KAAKgF,EAAI,SAAS,EAAE,qBAC3B,QACC,KACC,CAAA,SAAAlF,OAAC,OAAI,UAAU,YAAY,KAAK,QAC9B,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,QAAS,IAAMoJ,EAAWpE,CAAG,EAC7B,UAAU,iCACV,MAAM,OAEN,SAAAhF,EAAAA,IAAC,eAAa,CAAA,KAAK,gBAAiB,CAAA,CAAA,CACtC,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMqB,EAAa2D,EAAI,EAAE,EAClC,UAAU,gCACV,MAAM,SAEN,SAAAhF,EAAAA,IAAC,eAAa,CAAA,KAAK,4BAA6B,CAAA,CAAA,CAAA,CAClD,CAAA,CACF,CACF,CAAA,CAAA,GAxCOgF,EAAI,EAyCb,EACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAhF,EAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,UACZ,SAAAkD,EAAK,IAAK8B,UACThF,OAAAA,EAAA,IAAC,MAAiB,CAAA,UAAU,SAC1B,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,0BACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,gBACb,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,cACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,uBACV,MAAO,CAAE,SAAU,QAAS,CAAA,CAC7B,EACDF,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAE,EAAA,IAAC,KAAG,CAAA,UAAU,eAAgB,SAAAgF,EAAI,KAAK,EACvClF,EAAAA,KAAC,QAAM,CAAA,UAAU,aAAa,SAAA,CAAA,IAC1BkF,EAAI,IAAA,CACR,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAlF,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,QAAA,EACAF,EAAAA,KAAC,OAAK,CAAA,UAAU,qBACb,SAAA,GAAAyE,EAAAS,EAAI,SAAJ,YAAAT,EAAY,QAAS,EAAE,QAAA,CAC1B,CAAA,CAAA,EACF,EAEAzE,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,qBAAqB,SAEtC,UAAA,EACAA,MAAC,SACE,SAAI,IAAA,KAAKgF,EAAI,SAAS,EAAE,oBAC3B,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,SACb,SAAClF,EAAA,KAAA,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAAS,IAAMsJ,EAAWpE,CAAG,EAC7B,UAAU,2CACV,MAAM,OAEN,SAAA,CAAAhF,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,MAAA,CAAA,CAElB,EAEAF,EAAA,KAAC,SAAA,CACC,QAAS,IAAMuB,EAAa2D,EAAI,EAAE,EAClC,UAAU,0CACV,MAAM,SAEN,SAAA,CAAAhF,EAAA,IAAC,eAAA,CACC,KAAK,6BACL,UAAU,MAAA,CACX,EAAe,QAAA,CAAA,CAAA,CAElB,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,CAAA,EAnEQgF,EAAI,EAoEd,EACD,EACH,CACF,CAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EAGC+D,GACE/I,EAAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,QAASuJ,EACtC,SAAAzJ,EAAAA,KAAC,MAAI,CAAA,UAAU,gBAAgB,QAAUN,GAAMA,EAAE,gBAC/C,EAAA,SAAA,CAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,cACZ,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EACAyJ,EAAa,WAAa,gBAAA,EAC7B,EACAzJ,EAAA,IAAC,SAAA,CACC,KAAK,SACL,UAAU,cACV,QAASuJ,EAET,SAAAvJ,EAAAA,IAAC,eAAa,CAAA,KAAK,yBAA0B,CAAA,CAAA,CAAA,CAC/C,EACF,EAEAF,EAAAA,KAAC,OAAK,CAAA,SAAUL,EACd,SAAA,CAACO,EAAA,IAAA,MAAA,CAAI,UAAU,aACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAAAF,OAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,aACf,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,iBACL,UAAU,MAAA,CACX,EAAe,YAAA,EAElB,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhB,EAAS,KAChB,SAAWQ,GACTqE,EAAkB,OAAQrE,EAAE,OAAO,KAAK,EAE1C,UAAU,eACV,YAAY,iBACZ,SAAQ,EAAA,CACV,EACCQ,EAAA,IAAA,QAAA,CAAM,UAAU,uBAAuB,SAGxC,wEAAA,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAAAE,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAASuJ,EACT,UAAU,sCACX,SAAA,QAAA,CAED,EACAzJ,EAAA,KAAC,SAAA,CACC,KAAK,SACL,UAAU,kCAEV,SAAA,CAAAE,EAAA,IAAC,eAAA,CACC,KAAK,0BACL,UAAU,MAAA,CACX,EACAyJ,EAAa,aAAe,YAAA,CAAA,CAAA,CAC/B,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,CAEJ,2GChbaI,GAAmB,MAAO5I,EAAS,KAAO,CACrD,GAAI,CACF,MAAM6I,EAAc,IAAI,gBAEpB7I,EAAO,MAAM6I,EAAY,OAAO,OAAQ7I,EAAO,IAAI,EACnDA,EAAO,OAAO6I,EAAY,OAAO,QAAS7I,EAAO,KAAK,EACtDA,EAAO,QAAQ6I,EAAY,OAAO,SAAU7I,EAAO,MAAM,EACzDA,EAAO,QAAQ6I,EAAY,OAAO,SAAU7I,EAAO,MAAM,EACzDA,EAAO,YAAY6I,EAAY,OAAO,aAAc7I,EAAO,UAAU,EAEzE,KAAM,CAAE,SAAAvB,EAAU,KAAAC,GAAS,MAAMoK,GAAQ,mBAAmBD,CAAW,EAAE,EAEzE,GAAI,CAACpK,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACV,CACA,EAGa2K,GAAiB,MAAOC,GAAc,CACjD,GAAI,CACF,KAAM,CAAE,SAAAvK,EAAU,KAAAC,CAAM,EAAG,MAAMoK,GAC/B,mBAAmBE,CAAS,WAC5B,CACE,OAAQ,OAChB,CACK,EAED,GAAI,CAACvK,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,yBAA0BA,CAAK,EACvCA,CACV,CACA,EAGa6K,GAAgB,MAAOD,GAAc,CAChD,GAAI,CACF,KAAM,CAAE,SAAAvK,EAAU,KAAAC,CAAM,EAAG,MAAMoK,GAC/B,mBAAmBE,CAAS,UAC5B,CACE,OAAQ,OAChB,CACK,EAED,GAAI,CAACvK,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtCA,CACV,CACA,EAGa8K,GAAgB,MAAOF,GAAc,CAChD,GAAI,CACF,KAAM,CAAE,SAAAvK,EAAU,KAAAC,GAAS,MAAMoK,GAAQ,mBAAmBE,CAAS,GAAI,CACvE,OAAQ,QACd,CAAK,EAED,GAAI,CAACvK,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAG1D,OAAOC,CACR,OAAQN,EAAO,CACd,cAAQ,MAAM,wBAAyBA,CAAK,EACtCA,CACV,CACA,EC/EA,SAAwB+K,IAAoB,CAC1C,KAAM,CAACC,EAAUC,CAAW,EAAIpL,EAAAA,SAAS,CAAA,CAAE,EACrC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAS,EAAE,EAC/B,CAACqL,EAAaC,CAAc,EAAItL,EAAAA,SAAS,CAAC,EAC1C,CAACuL,EAAYC,CAAa,EAAIxL,EAAAA,SAAS,CAAC,EACxC,CAACyL,EAAcC,CAAe,EAAI1L,EAAAA,SAAS,KAAK,EAChD,CAAC2L,EAAYC,CAAa,EAAI5L,EAAAA,SAAS,EAAE,EACzC,CAAC6L,EAAOC,CAAQ,EAAI9L,EAAS,SAAA,CAAE,MAAO,EAAG,QAAS,EAAG,SAAU,CAAA,CAAG,EAElE+L,EAAgB,SAAY,CAC5B,GAAA,CACF7L,EAAW,EAAI,EACT,MAAAM,EAAW,MAAMmK,GAAiB,CACtC,KAAMU,EACN,MAAO,GACP,OAAQI,EACR,OAAQE,CAAA,CACT,EAED,GAAInL,EAAS,QAAS,CACR4K,EAAA5K,EAAS,KAAK,QAAQ,EACpBgL,EAAAhL,EAAS,KAAK,WAAW,KAAK,EAGtC,MAAAwL,EAAgBxL,EAAS,KAAK,WAAW,MAC/CsL,MAAkB,CAAE,GAAGhJ,EAAM,MAAOkJ,GAAgB,CAAA,QAE/CrL,EAAK,CACZP,EAAS,0BAA0B,EAC3B,QAAA,MAAM,wBAAyBO,CAAG,CAAA,QAC1C,CACAT,EAAW,EAAK,CAAA,CAEpB,EAEAiB,EAAAA,UAAU,IAAM,CACA4K,EAAA,CACb,EAAA,CAACV,EAAaI,EAAcE,CAAU,CAAC,EAEpC,MAAAM,EAAgB,MAAOlB,GAAc,CACrC,GAAA,CACF,MAAMD,GAAeC,CAAS,EAChBgB,EAAA,OACF,CACZ3L,EAAS,2BAA2B,CAAA,CAExC,EAEM8L,EAAe,MAAOnB,GAAc,CACpC,GAAA,CACF,MAAMC,GAAcD,CAAS,EACfgB,EAAA,OACF,CACZ3L,EAAS,0BAA0B,CAAA,CAEvC,EAEM+B,EAAe,MAAO4I,GAAc,CACpC,GAAA,OAAO,QAAQ,6EAA6E,EAC1F,GAAA,CACF,MAAME,GAAcF,CAAS,EACfgB,EAAA,OACF,CACZ3L,EAAS,0BAA0B,CAAA,CAGzC,EAEM+L,EAAgB7L,GAAM,CAC1BA,EAAE,eAAe,EACjBgL,EAAe,CAAC,EACFS,EAAA,CAChB,EAEMxJ,EAAcC,GACX,IAAI,KAAKA,CAAU,EAAE,mBAAmB,QAAS,CACtD,KAAM,UACN,MAAO,QACP,IAAK,UACL,KAAM,UACN,OAAQ,SAAA,CACT,EAGG4J,EAAe,CAACC,EAAMC,EAAY,MAC/BD,EAAK,OAASC,EAAYD,EAAK,UAAU,EAAGC,CAAS,EAAI,MAAQD,EAG1E,OACGvL,EAAA,IAAAQ,EAAA,CACC,SAACR,EAAAA,IAAA,MAAA,CAAI,UAAU,kBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,MACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yDACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,UAAU,SAAkB,qBAAA,EAC1CF,EAAAA,KAAC,MAAI,CAAA,UAAU,eACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mBAAmB,SAAA,CAAA,UAAQiL,EAAM,KAAA,EAAM,EACtDjL,EAAAA,KAAC,MAAI,CAAA,UAAU,mBAAmB,SAAA,CAAA,YAAUiL,EAAM,OAAA,EAAQ,EAC1DjL,EAAAA,KAAC,MAAI,CAAA,UAAU,mBAAmB,SAAA,CAAA,aAAWiL,EAAM,QAAA,CAAS,CAAA,CAAA,CAC9D,CAAA,CAAA,EACF,EAEC1L,GACEW,EAAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,KAAK,QACtC,SACHX,EAAA,EAIFW,EAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,YACb,SAAAF,OAAC,MAAI,CAAA,UAAU,UACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAa,gBAAA,EAC3CF,EAAA,KAAC,SAAA,CACC,UAAU,cACV,MAAO6K,EACP,SAAWnL,GAAM,CACCoL,EAAApL,EAAE,OAAO,KAAK,EAC9BgL,EAAe,CAAC,CAClB,EAEA,SAAA,CAACxK,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAY,eAAA,EAC/BA,EAAA,IAAA,SAAA,CAAO,MAAM,UAAU,SAAgB,mBAAA,EACvCA,EAAA,IAAA,SAAA,CAAO,MAAM,WAAW,SAAQ,UAAA,CAAA,CAAA,CAAA,CAAA,CACnC,EACF,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,aAAa,SAAM,SAAA,EACnCF,EAAA,KAAA,OAAA,CAAK,SAAUuL,EAAc,UAAU,SACtC,SAAA,CAAArL,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAU,eACV,YAAY,8CACZ,MAAO6K,EACP,SAAWrL,GAAMsL,EAActL,EAAE,OAAO,KAAK,CAAA,CAC/C,QACC,SAAO,CAAA,KAAK,SAAS,UAAU,uBAAuB,SAEvD,QAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,OACb,SAACM,EAAA,KAAA,MAAA,CAAI,UAAU,YACZ,SAAA,CAAAX,QACE,MAAI,CAAA,UAAU,mBACb,SAAAa,EAAAA,IAAC,OAAI,UAAU,iBAAiB,KAAK,SACnC,eAAC,OAAK,CAAA,UAAU,kBAAkB,SAAU,YAAA,CAAA,CAC9C,CAAA,EACF,EACEqK,EAAS,SAAW,QACrB,MAAI,CAAA,UAAU,mBACb,SAACrK,MAAA,IAAA,CAAE,UAAU,aAAa,SAAA,oBAAA,CAAkB,CAC9C,CAAA,QAEC,MAAI,CAAA,UAAU,mBACb,SAACF,EAAAA,KAAA,QAAA,CAAM,UAAU,oBACf,SAAA,CAACE,EAAA,IAAA,QAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAAA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAK,OAAA,CAAA,EACTA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,EACXA,EAAAA,IAAC,MAAG,SAAS,WAAA,CAAA,EACbA,EAAAA,IAAC,MAAG,SAAM,QAAA,CAAA,EACVA,EAAAA,IAAC,MAAG,SAAI,MAAA,CAAA,EACRA,EAAAA,IAAC,MAAG,SAAO,SAAA,CAAA,CAAA,CAAA,CACb,CACF,CAAA,QACC,QACE,CAAA,SAAAqK,EAAS,IAAKoB,wBACZ,KACC,CAAA,SAAA,CAAA3L,OAAC,KACC,CAAA,SAAA,CAACE,EAAAA,IAAA,SAAA,CAAQ,WAAQ,MAAO,CAAA,EACvByL,EAAQ,SACPzL,MAAC,OACC,SAACA,EAAA,IAAA,QAAA,CACC,eAAC,IAAE,CAAA,KAAMyL,EAAQ,QAAS,OAAO,SAAS,IAAI,sBAC3C,SAAQA,EAAA,OACX,CAAA,CACF,CAAA,CACF,CAAA,CAAA,EAEJ,EACAzL,EAAAA,IAAC,MACC,SAACA,EAAAA,IAAA,QAAA,CAAM,UAAU,aAAc,SAAAyL,EAAQ,MAAM,CAC/C,CAAA,SACC,KACC,CAAA,SAAA,CAAAzL,EAAAA,IAAC,OAAI,MAAOyL,EAAQ,QACjB,SAAaH,EAAAG,EAAQ,OAAO,EAC/B,EACCA,EAAQ,QACN3L,OAAA,QAAA,CAAM,UAAU,aAAa,SAAA,CAAA,aACjB2L,EAAQ,OAAO,MAAA,CAC5B,CAAA,CAAA,EAEJ,QACC,KACC,CAAA,SAAAzL,EAAA,IAAC0L,GAAA,CACC,GAAI,gBAAgBD,EAAQ,SAAS,IAAI,GACzC,UAAU,uBACV,OAAO,SAEN,WAAQlH,EAAAkH,EAAA,SAAS,aAAa,CAAC,IAAvB,YAAAlH,EAA0B,QAAS,UAAA,CAAA,EAEhD,EACCvE,MAAA,KAAA,CACC,SAACA,EAAAA,IAAA,OAAA,CAAK,UAAW,SAASyL,EAAQ,SAAW,aAAe,YAAY,GACrE,SAAAA,EAAQ,SAAW,WAAa,SACnC,CAAA,EACF,EACAzL,EAAAA,IAAC,MACC,SAACA,EAAA,IAAA,QAAA,CAAO,WAAWyL,EAAQ,SAAS,EAAE,CACxC,CAAA,EACCzL,MAAA,KAAA,CACC,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,yBACZ,SAAA,CAAC2L,EAAQ,SASRzL,EAAA,IAAC,SAAA,CACC,UAAU,kBACV,QAAS,IAAMoL,EAAaK,EAAQ,EAAE,EACtC,MAAM,OAEN,SAAAzL,EAAAA,IAAC,IAAE,CAAA,UAAU,YAAa,CAAA,CAAA,CAC5B,EAdAA,EAAA,IAAC,SAAA,CACC,UAAU,kBACV,QAAS,IAAMmL,EAAcM,EAAQ,EAAE,EACvC,MAAM,UAEN,SAAAzL,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,EAW5BA,EAAA,IAAC,SAAA,CACC,UAAU,iBACV,QAAS,IAAMqB,EAAaoK,EAAQ,EAAE,EACtC,MAAM,SAEN,SAAAzL,EAAAA,IAAC,IAAE,CAAA,UAAU,UAAW,CAAA,CAAA,CAAA,CAC1B,CAAA,CACF,CACF,CAAA,CAAA,GAtEOyL,EAAQ,EAuEjB,EACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAIDhB,EAAa,GACXzK,EAAAA,IAAA,MAAA,CAAI,UAAU,OACb,SAAAF,EAAA,KAAC,KAAG,CAAA,UAAU,oCACZ,SAAA,CAAAE,EAAAA,IAAC,MAAG,UAAW,aAAauK,IAAgB,EAAI,WAAa,EAAE,GAC7D,SAAAvK,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IAAMwK,EAAeD,EAAc,CAAC,EAC7C,SAAUA,IAAgB,EAC3B,SAAA,UAAA,CAAA,EAGH,EACC,CAAC,GAAG,MAAME,CAAU,CAAC,EAAE,IAAI,CAAC5D,EAAGR,IAC7BrG,EAAAA,IAAA,KAAA,CAAmB,UAAW,aAAauK,IAAgBlE,EAAQ,EAAI,SAAW,EAAE,GACnF,SAAArG,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IAAMwK,EAAenE,EAAQ,CAAC,EAEtC,SAAQA,EAAA,CAAA,CAAA,GALJA,EAAQ,CAOjB,CACD,EACDrG,EAAAA,IAAC,MAAG,UAAW,aAAauK,IAAgBE,EAAa,WAAa,EAAE,GACtE,SAAAzK,EAAA,IAAC,SAAA,CACC,UAAU,YACV,QAAS,IAAMwK,EAAeD,EAAc,CAAC,EAC7C,SAAUA,IAAgBE,EAC3B,SAAA,MAAA,CAAA,CAGH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,CAAA,CACF,CACF,CAAA,EACF,CAEJ"}