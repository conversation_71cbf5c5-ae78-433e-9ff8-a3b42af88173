const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/vendor-misc-BUjjPnRU.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css"])))=>i.map(i=>d[i]);
import{_ as _e}from"./pages-other-C6-deIaZ.js";const g=r=>typeof r=="string",_=()=>{let r,e;const t=new Promise((i,n)=>{r=i,e=n});return t.resolve=r,t.reject=e,t},ce=r=>r==null?"":""+r,qe=(r,e,t)=>{r.forEach(i=>{e[i]&&(t[i]=e[i])})},Be=/###/g,de=r=>r&&r.indexOf("###")>-1?r.replace(Be,"."):r,he=r=>!r||g(r),q=(r,e,t)=>{const i=g(e)?e.split("."):e;let n=0;for(;n<i.length-1;){if(he(r))return{};const s=de(i[n]);!r[s]&&t&&(r[s]=new t),Object.prototype.hasOwnProperty.call(r,s)?r=r[s]:r={},++n}return he(r)?{}:{obj:r,k:de(i[n])}},pe=(r,e,t)=>{const{obj:i,k:n}=q(r,e,Object);if(i!==void 0||e.length===1){i[n]=t;return}let s=e[e.length-1],o=e.slice(0,e.length-1),a=q(r,o,Object);for(;a.obj===void 0&&o.length;)s=`${o[o.length-1]}.${s}`,o=o.slice(0,o.length-1),a=q(r,o,Object),a!=null&&a.obj&&typeof a.obj[`${a.k}.${s}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${s}`]=t},ze=(r,e,t,i)=>{const{obj:n,k:s}=q(r,e,Object);n[s]=n[s]||[],n[s].push(t)},G=(r,e)=>{const{obj:t,k:i}=q(r,e);if(t&&Object.prototype.hasOwnProperty.call(t,i))return t[i]},Xe=(r,e,t)=>{const i=G(r,t);return i!==void 0?i:G(e,t)},De=(r,e,t)=>{for(const i in e)i!=="__proto__"&&i!=="constructor"&&(i in r?g(r[i])||r[i]instanceof String||g(e[i])||e[i]instanceof String?t&&(r[i]=e[i]):De(r[i],e[i],t):r[i]=e[i]);return r},I=r=>r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Je={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const We=r=>g(r)?r.replace(/[&<>"'\/]/g,e=>Je[e]):r;class Qe{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const i=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,i),this.regExpQueue.push(e),i}}const Ge=[" ",",","?","!",";"],Ye=new Qe(20),Ze=(r,e,t)=>{e=e||"",t=t||"";const i=Ge.filter(o=>e.indexOf(o)<0&&t.indexOf(o)<0);if(i.length===0)return!0;const n=Ye.getRegExp(`(${i.map(o=>o==="?"?"\\?":o).join("|")})`);let s=!n.test(r);if(!s){const o=r.indexOf(t);o>0&&!n.test(r.substring(0,o))&&(s=!0)}return s},se=(r,e,t=".")=>{if(!r)return;if(r[e])return Object.prototype.hasOwnProperty.call(r,e)?r[e]:void 0;const i=e.split(t);let n=r;for(let s=0;s<i.length;){if(!n||typeof n!="object")return;let o,a="";for(let l=s;l<i.length;++l)if(l!==s&&(a+=t),a+=i[l],o=n[a],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&l<i.length-1)continue;s+=l-s+1;break}n=o}return n},B=r=>r==null?void 0:r.replace("_","-"),et={type:"logger",log(r){this.output("log",r)},warn(r){this.output("warn",r)},error(r){this.output("error",r)},output(r,e){var t,i;(i=(t=console==null?void 0:console[r])==null?void 0:t.apply)==null||i.call(t,console,e)}};class Y{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||et,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,i,n){return n&&!this.debug?null:(g(e[0])&&(e[0]=`${i}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new Y(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Y(this.logger,e)}}var R=new Y;class te{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(i=>{this.observers[i]||(this.observers[i]=new Map);const n=this.observers[i].get(t)||0;this.observers[i].set(t,n+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([n,s])=>{for(let o=0;o<s;o++)n(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([n,s])=>{for(let o=0;o<s;o++)n.apply(n,[e,...t])})}}class ge extends te{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,i,n={}){var u,c;const s=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,o=n.ignoreJSONStructure!==void 0?n.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],i&&(Array.isArray(i)?a.push(...i):g(i)&&s?a.push(...i.split(s)):a.push(i)));const l=G(this.data,a);return!l&&!t&&!i&&e.indexOf(".")>-1&&(e=a[0],t=a[1],i=a.slice(2).join(".")),l||!o||!g(i)?l:se((c=(u=this.data)==null?void 0:u[e])==null?void 0:c[t],i,s)}addResource(e,t,i,n,s={silent:!1}){const o=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator;let a=[e,t];i&&(a=a.concat(o?i.split(o):i)),e.indexOf(".")>-1&&(a=e.split("."),n=t,t=a[1]),this.addNamespaces(t),pe(this.data,a,n),s.silent||this.emit("added",e,t,i,n)}addResources(e,t,i,n={silent:!1}){for(const s in i)(g(i[s])||Array.isArray(i[s]))&&this.addResource(e,t,s,i[s],{silent:!0});n.silent||this.emit("added",e,t,i)}addResourceBundle(e,t,i,n,s,o={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),n=i,i=t,t=a[1]),this.addNamespaces(t);let l=G(this.data,a)||{};o.skipCopy||(i=JSON.parse(JSON.stringify(i))),n?De(l,i,s):l={...l,...i},pe(this.data,a,l),o.silent||this.emit("added",e,t,i)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(n=>t[n]&&Object.keys(t[n]).length>0)}toJSON(){return this.data}}var Te={processors:{},addPostProcessor(r){this.processors[r.name]=r},handle(r,e,t,i,n){return r.forEach(s=>{var o;e=((o=this.processors[s])==null?void 0:o.process(e,t,i,n))??e}),e}};const me={},ye=r=>!g(r)&&typeof r!="boolean"&&typeof r!="number";class Z extends te{constructor(e,t={}){super(),qe(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=R.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const i={...t};if(e==null)return!1;const n=this.resolve(e,i);return(n==null?void 0:n.res)!==void 0}extractFromKey(e,t){let i=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;i===void 0&&(i=":");const n=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let s=t.ns||this.options.defaultNS||[];const o=i&&e.indexOf(i)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!Ze(e,i,n);if(o&&!a){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:g(s)?[s]:s};const u=e.split(i);(i!==n||i===n&&this.options.ns.indexOf(u[0])>-1)&&(s=u.shift()),e=u.join(n)}return{key:e,namespaces:g(s)?[s]:s}}translate(e,t,i){let n=typeof t=="object"?{...t}:t;if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(n={...n}),n||(n={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const s=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(e[e.length-1],n),u=l[l.length-1];let c=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;c===void 0&&(c=":");const f=n.lng||this.language,p=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((f==null?void 0:f.toLowerCase())==="cimode")return p?s?{res:`${u}${c}${a}`,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(n)}:`${u}${c}${a}`:s?{res:a,usedKey:a,exactUsedKey:a,usedLng:f,usedNS:u,usedParams:this.getUsedParamsDetails(n)}:a;const h=this.resolve(e,n);let d=h==null?void 0:h.res;const m=(h==null?void 0:h.usedKey)||a,y=(h==null?void 0:h.exactUsedKey)||a,L=["[object Number]","[object Function]","[object RegExp]"],O=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,N=!this.i18nFormat||this.i18nFormat.handleAsObject,S=n.count!==void 0&&!g(n.count),$=Z.hasDefaultValue(n),J=S?this.pluralResolver.getSuffix(f,n.count,n):"",j=n.ordinal&&S?this.pluralResolver.getSuffix(f,n.count,{ordinal:!1}):"",M=S&&!n.ordinal&&n.count===0,v=M&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${J}`]||n[`defaultValue${j}`]||n.defaultValue;let b=d;N&&!d&&$&&(b=v);const Ve=ye(b),Ke=Object.prototype.toString.apply(b);if(N&&b&&Ve&&L.indexOf(Ke)<0&&!(g(O)&&Array.isArray(b))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const k=this.options.returnedObjectHandler?this.options.returnedObjectHandler(m,b,{...n,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return s?(h.res=k,h.usedParams=this.getUsedParamsDetails(n),h):k}if(o){const k=Array.isArray(b),P=k?[]:{},ae=k?y:m;for(const C in b)if(Object.prototype.hasOwnProperty.call(b,C)){const E=`${ae}${o}${C}`;$&&!d?P[C]=this.translate(E,{...n,defaultValue:ye(v)?v[C]:void 0,joinArrays:!1,ns:l}):P[C]=this.translate(E,{...n,joinArrays:!1,ns:l}),P[C]===E&&(P[C]=b[C])}d=P}}else if(N&&g(O)&&Array.isArray(d))d=d.join(O),d&&(d=this.extendTranslation(d,e,n,i));else{let k=!1,P=!1;!this.isValidLookup(d)&&$&&(k=!0,d=v),this.isValidLookup(d)||(P=!0,d=a);const C=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&P?void 0:d,E=$&&v!==d&&this.options.updateMissing;if(P||k||E){if(this.logger.log(E?"updateKey":"missingKey",f,u,a,E?v:d),o){const w=this.resolve(a,{...n,keySeparator:!1});w&&w.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let V=[];const W=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&W&&W[0])for(let w=0;w<W.length;w++)V.push(W[w]);else this.options.saveMissingTo==="all"?V=this.languageUtils.toResolveHierarchy(n.lng||this.language):V.push(n.lng||this.language);const le=(w,D,K)=>{var fe;const ue=$&&K!==d?K:C;this.options.missingKeyHandler?this.options.missingKeyHandler(w,u,D,ue,E,n):(fe=this.backendConnector)!=null&&fe.saveMissing&&this.backendConnector.saveMissing(w,u,D,ue,E,n),this.emit("missingKey",w,u,D,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&S?V.forEach(w=>{const D=this.pluralResolver.getSuffixes(w,n);M&&n[`defaultValue${this.options.pluralSeparator}zero`]&&D.indexOf(`${this.options.pluralSeparator}zero`)<0&&D.push(`${this.options.pluralSeparator}zero`),D.forEach(K=>{le([w],a+K,n[`defaultValue${K}`]||v)})}):le(V,a,v))}d=this.extendTranslation(d,e,n,h,i),P&&d===a&&this.options.appendNamespaceToMissingKey&&(d=`${u}${c}${a}`),(P||k)&&this.options.parseMissingKeyHandler&&(d=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${c}${a}`:a,k?d:void 0,n))}return s?(h.res=d,h.usedParams=this.getUsedParamsDetails(n),h):d}extendTranslation(e,t,i,n,s){var l,u;if((l=this.i18nFormat)!=null&&l.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!i.skipInterpolation){i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});const c=g(e)&&(((u=i==null?void 0:i.interpolation)==null?void 0:u.skipOnVariables)!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let f;if(c){const h=e.match(this.interpolator.nestingRegexp);f=h&&h.length}let p=i.replace&&!g(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(p={...this.options.interpolation.defaultVariables,...p}),e=this.interpolator.interpolate(e,p,i.lng||this.language||n.usedLng,i),c){const h=e.match(this.interpolator.nestingRegexp),d=h&&h.length;f<d&&(i.nest=!1)}!i.lng&&n&&n.res&&(i.lng=this.language||n.usedLng),i.nest!==!1&&(e=this.interpolator.nest(e,(...h)=>(s==null?void 0:s[0])===h[0]&&!i.context?(this.logger.warn(`It seems you are nesting recursively key: ${h[0]} in key: ${t[0]}`),null):this.translate(...h,t),i)),i.interpolation&&this.interpolator.reset()}const o=i.postProcess||this.options.postProcess,a=g(o)?[o]:o;return e!=null&&(a!=null&&a.length)&&i.applyPostProcessor!==!1&&(e=Te.handle(a,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),e}resolve(e,t={}){let i,n,s,o,a;return g(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(i))return;const u=this.extractFromKey(l,t),c=u.key;n=c;let f=u.namespaces;this.options.fallbackNS&&(f=f.concat(this.options.fallbackNS));const p=t.count!==void 0&&!g(t.count),h=p&&!t.ordinal&&t.count===0,d=t.context!==void 0&&(g(t.context)||typeof t.context=="number")&&t.context!=="",m=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);f.forEach(y=>{var L,O;this.isValidLookup(i)||(a=y,!me[`${m[0]}-${y}`]&&((L=this.utils)!=null&&L.hasLoadedNamespace)&&!((O=this.utils)!=null&&O.hasLoadedNamespace(a))&&(me[`${m[0]}-${y}`]=!0,this.logger.warn(`key "${n}" for languages "${m.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),m.forEach(N=>{var J;if(this.isValidLookup(i))return;o=N;const S=[c];if((J=this.i18nFormat)!=null&&J.addLookupKeys)this.i18nFormat.addLookupKeys(S,c,N,y,t);else{let j;p&&(j=this.pluralResolver.getSuffix(N,t.count,t));const M=`${this.options.pluralSeparator}zero`,v=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(p&&(S.push(c+j),t.ordinal&&j.indexOf(v)===0&&S.push(c+j.replace(v,this.options.pluralSeparator)),h&&S.push(c+M)),d){const b=`${c}${this.options.contextSeparator}${t.context}`;S.push(b),p&&(S.push(b+j),t.ordinal&&j.indexOf(v)===0&&S.push(b+j.replace(v,this.options.pluralSeparator)),h&&S.push(b+M))}}let $;for(;$=S.pop();)this.isValidLookup(i)||(s=$,i=this.getResource(N,y,$,t))}))})}),{res:i,usedKey:n,exactUsedKey:s,usedLng:o,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,i,n={}){var s;return(s=this.i18nFormat)!=null&&s.getResource?this.i18nFormat.getResource(e,t,i,n):this.resourceStore.getResource(e,t,i,n)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],i=e.replace&&!g(e.replace);let n=i?e.replace:e;if(i&&typeof e.count<"u"&&(n.count=e.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!i){n={...n};for(const s of t)delete n[s]}return n}static hasDefaultValue(e){const t="defaultValue";for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&t===i.substring(0,t.length)&&e[i]!==void 0)return!0;return!1}}class be{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=R.create("languageUtils")}getScriptPartFromCode(e){if(e=B(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=B(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(g(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(i=>{if(t)return;const n=this.formatLanguageCode(i);(!this.options.supportedLngs||this.isSupportedCode(n))&&(t=n)}),!t&&this.options.supportedLngs&&e.forEach(i=>{if(t)return;const n=this.getScriptPartFromCode(i);if(this.isSupportedCode(n))return t=n;const s=this.getLanguagePartFromCode(i);if(this.isSupportedCode(s))return t=s;t=this.options.supportedLngs.find(o=>{if(o===s)return o;if(!(o.indexOf("-")<0&&s.indexOf("-")<0)&&(o.indexOf("-")>0&&s.indexOf("-")<0&&o.substring(0,o.indexOf("-"))===s||o.indexOf(s)===0&&s.length>1))return o})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),g(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let i=e[t];return i||(i=e[this.getScriptPartFromCode(t)]),i||(i=e[this.formatLanguageCode(t)]),i||(i=e[this.getLanguagePartFromCode(t)]),i||(i=e.default),i||[]}toResolveHierarchy(e,t){const i=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),n=[],s=o=>{o&&(this.isSupportedCode(o)?n.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return g(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&s(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&s(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&s(this.getLanguagePartFromCode(e))):g(e)&&s(this.formatLanguageCode(e)),i.forEach(o=>{n.indexOf(o)<0&&s(this.formatLanguageCode(o))}),n}}const xe={zero:0,one:1,two:2,few:3,many:4,other:5},Se={select:r=>r===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class tt{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=R.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const i=B(e==="dev"?"en":e),n=t.ordinal?"ordinal":"cardinal",s=JSON.stringify({cleanedCode:i,type:n});if(s in this.pluralRulesCache)return this.pluralRulesCache[s];let o;try{o=new Intl.PluralRules(i,{type:n})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Se;if(!e.match(/-|_/))return Se;const l=this.languageUtils.getLanguagePartFromCode(e);o=this.getRule(l,t)}return this.pluralRulesCache[s]=o,o}needsPlural(e,t={}){let i=this.getRule(e,t);return i||(i=this.getRule("dev",t)),(i==null?void 0:i.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t,i={}){return this.getSuffixes(e,i).map(n=>`${t}${n}`)}getSuffixes(e,t={}){let i=this.getRule(e,t);return i||(i=this.getRule("dev",t)),i?i.resolvedOptions().pluralCategories.sort((n,s)=>xe[n]-xe[s]).map(n=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${n}`):[]}getSuffix(e,t,i={}){const n=this.getRule(e,i);return n?`${this.options.prepend}${i.ordinal?`ordinal${this.options.prepend}`:""}${n.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,i))}}const ve=(r,e,t,i=".",n=!0)=>{let s=Xe(r,e,t);return!s&&n&&g(t)&&(s=se(r,t,i),s===void 0&&(s=se(e,t,i))),s},ie=r=>r.replace(/\$/g,"$$$$");class it{constructor(e={}){var t;this.logger=R.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(i=>i),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:i,useRawValueToEscape:n,prefix:s,prefixEscaped:o,suffix:a,suffixEscaped:l,formatSeparator:u,unescapeSuffix:c,unescapePrefix:f,nestingPrefix:p,nestingPrefixEscaped:h,nestingSuffix:d,nestingSuffixEscaped:m,nestingOptionsSeparator:y,maxReplaces:L,alwaysFormat:O}=e.interpolation;this.escape=t!==void 0?t:We,this.escapeValue=i!==void 0?i:!0,this.useRawValueToEscape=n!==void 0?n:!1,this.prefix=s?I(s):o||"{{",this.suffix=a?I(a):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=c?"":f||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=p?I(p):h||I("$t("),this.nestingSuffix=d?I(d):m||I(")"),this.nestingOptionsSeparator=y||",",this.maxReplaces=L||1e3,this.alwaysFormat=O!==void 0?O:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,i)=>(t==null?void 0:t.source)===i?(t.lastIndex=0,t):new RegExp(i,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,i,n){var h;let s,o,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=d=>{if(d.indexOf(this.formatSeparator)<0){const O=ve(t,l,d,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(O,void 0,i,{...n,...t,interpolationkey:d}):O}const m=d.split(this.formatSeparator),y=m.shift().trim(),L=m.join(this.formatSeparator).trim();return this.format(ve(t,l,y,this.options.keySeparator,this.options.ignoreJSONStructure),L,i,{...n,...t,interpolationkey:y})};this.resetRegExp();const c=(n==null?void 0:n.missingInterpolationHandler)||this.options.missingInterpolationHandler,f=((h=n==null?void 0:n.interpolation)==null?void 0:h.skipOnVariables)!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:d=>ie(d)},{regex:this.regexp,safeValue:d=>this.escapeValue?ie(this.escape(d)):ie(d)}].forEach(d=>{for(a=0;s=d.regex.exec(e);){const m=s[1].trim();if(o=u(m),o===void 0)if(typeof c=="function"){const L=c(e,s,n);o=g(L)?L:""}else if(n&&Object.prototype.hasOwnProperty.call(n,m))o="";else if(f){o=s[0];continue}else this.logger.warn(`missed to pass in variable ${m} for interpolating ${e}`),o="";else!g(o)&&!this.useRawValueToEscape&&(o=ce(o));const y=d.safeValue(o);if(e=e.replace(s[0],y),f?(d.regex.lastIndex+=o.length,d.regex.lastIndex-=s[0].length):d.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t,i={}){let n,s,o;const a=(l,u)=>{const c=this.nestingOptionsSeparator;if(l.indexOf(c)<0)return l;const f=l.split(new RegExp(`${c}[ ]*{`));let p=`{${f[1]}`;l=f[0],p=this.interpolate(p,o);const h=p.match(/'/g),d=p.match(/"/g);(((h==null?void 0:h.length)??0)%2===0&&!d||d.length%2!==0)&&(p=p.replace(/'/g,'"'));try{o=JSON.parse(p),u&&(o={...u,...o})}catch(m){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,m),`${l}${c}${p}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,l};for(;n=this.nestingRegexp.exec(e);){let l=[];o={...i},o=o.replace&&!g(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;let u=!1;if(n[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(n[1])){const c=n[1].split(this.formatSeparator).map(f=>f.trim());n[1]=c.shift(),l=c,u=!0}if(s=t(a.call(this,n[1].trim(),o),o),s&&n[0]===e&&!g(s))return s;g(s)||(s=ce(s)),s||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),s=""),u&&(s=l.reduce((c,f)=>this.format(c,f,i.lng,{...i,interpolationkey:n[1].trim()}),s.trim())),e=e.replace(n[0],s),this.regexp.lastIndex=0}return e}}const nt=r=>{let e=r.toLowerCase().trim();const t={};if(r.indexOf("(")>-1){const i=r.split("(");e=i[0].toLowerCase().trim();const n=i[1].substring(0,i[1].length-1);e==="currency"&&n.indexOf(":")<0?t.currency||(t.currency=n.trim()):e==="relativetime"&&n.indexOf(":")<0?t.range||(t.range=n.trim()):n.split(";").forEach(o=>{if(o){const[a,...l]=o.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),c=a.trim();t[c]||(t[c]=u),u==="false"&&(t[c]=!1),u==="true"&&(t[c]=!0),isNaN(u)||(t[c]=parseInt(u,10))}})}return{formatName:e,formatOptions:t}},we=r=>{const e={};return(t,i,n)=>{let s=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(s={...s,[n.interpolationkey]:void 0});const o=i+JSON.stringify(s);let a=e[o];return a||(a=r(B(i),n),e[o]=a),a(t)}},st=r=>(e,t,i)=>r(B(t),i)(e);class rt{constructor(e={}){this.logger=R.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const i=t.cacheInBuiltFormats?we:st;this.formats={number:i((n,s)=>{const o=new Intl.NumberFormat(n,{...s});return a=>o.format(a)}),currency:i((n,s)=>{const o=new Intl.NumberFormat(n,{...s,style:"currency"});return a=>o.format(a)}),datetime:i((n,s)=>{const o=new Intl.DateTimeFormat(n,{...s});return a=>o.format(a)}),relativetime:i((n,s)=>{const o=new Intl.RelativeTimeFormat(n,{...s});return a=>o.format(a,s.range||"day")}),list:i((n,s)=>{const o=new Intl.ListFormat(n,{...s});return a=>o.format(a)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=we(t)}format(e,t,i,n={}){const s=t.split(this.formatSeparator);if(s.length>1&&s[0].indexOf("(")>1&&s[0].indexOf(")")<0&&s.find(a=>a.indexOf(")")>-1)){const a=s.findIndex(l=>l.indexOf(")")>-1);s[0]=[s[0],...s.splice(1,a)].join(this.formatSeparator)}return s.reduce((a,l)=>{var f;const{formatName:u,formatOptions:c}=nt(l);if(this.formats[u]){let p=a;try{const h=((f=n==null?void 0:n.formatParams)==null?void 0:f[n.interpolationkey])||{},d=h.locale||h.lng||n.locale||n.lng||i;p=this.formats[u](a,d,{...c,...n,...h})}catch(h){this.logger.warn(h)}return p}else this.logger.warn(`there was no format function for ${u}`);return a},e)}}const ot=(r,e)=>{r.pending[e]!==void 0&&(delete r.pending[e],r.pendingCount--)};class at extends te{constructor(e,t,i,n={}){var s,o;super(),this.backend=e,this.store=t,this.services=i,this.languageUtils=i.languageUtils,this.options=n,this.logger=R.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],(o=(s=this.backend)==null?void 0:s.init)==null||o.call(s,i,n.backend,n)}queueLoad(e,t,i,n){const s={},o={},a={},l={};return e.forEach(u=>{let c=!0;t.forEach(f=>{const p=`${u}|${f}`;!i.reload&&this.store.hasResourceBundle(u,f)?this.state[p]=2:this.state[p]<0||(this.state[p]===1?o[p]===void 0&&(o[p]=!0):(this.state[p]=1,c=!1,o[p]===void 0&&(o[p]=!0),s[p]===void 0&&(s[p]=!0),l[f]===void 0&&(l[f]=!0)))}),c||(a[u]=!0)}),(Object.keys(s).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(s),pending:Object.keys(o),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(e,t,i){const n=e.split("|"),s=n[0],o=n[1];t&&this.emit("failedLoading",s,o,t),!t&&i&&this.store.addResourceBundle(s,o,i,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&i&&(this.state[e]=0);const a={};this.queue.forEach(l=>{ze(l.loaded,[s],o),ot(l,e),t&&l.errors.push(t),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{a[u]||(a[u]={});const c=l.loaded[u];c.length&&c.forEach(f=>{a[u][f]===void 0&&(a[u][f]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(e,t,i,n=0,s=this.retryTimeout,o){if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:i,tried:n,wait:s,callback:o});return}this.readingCalls++;const a=(u,c)=>{if(this.readingCalls--,this.waitingReads.length>0){const f=this.waitingReads.shift();this.read(f.lng,f.ns,f.fcName,f.tried,f.wait,f.callback)}if(u&&c&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,i,n+1,s*2,o)},s);return}o(u,c)},l=this.backend[i].bind(this.backend);if(l.length===2){try{const u=l(e,t);u&&typeof u.then=="function"?u.then(c=>a(null,c)).catch(a):a(null,u)}catch(u){a(u)}return}return l(e,t,a)}prepareLoading(e,t,i={},n){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();g(e)&&(e=this.languageUtils.toResolveHierarchy(e)),g(t)&&(t=[t]);const s=this.queueLoad(e,t,i,n);if(!s.toLoad.length)return s.pending.length||n(),null;s.toLoad.forEach(o=>{this.loadOne(o)})}load(e,t,i){this.prepareLoading(e,t,{},i)}reload(e,t,i){this.prepareLoading(e,t,{reload:!0},i)}loadOne(e,t=""){const i=e.split("|"),n=i[0],s=i[1];this.read(n,s,"read",void 0,void 0,(o,a)=>{o&&this.logger.warn(`${t}loading namespace ${s} for language ${n} failed`,o),!o&&a&&this.logger.log(`${t}loaded namespace ${s} for language ${n}`,a),this.loaded(e,o,a)})}saveMissing(e,t,i,n,s,o={},a=()=>{}){var l,u,c,f,p;if((u=(l=this.services)==null?void 0:l.utils)!=null&&u.hasLoadedNamespace&&!((f=(c=this.services)==null?void 0:c.utils)!=null&&f.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${i}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(i==null||i==="")){if((p=this.backend)!=null&&p.create){const h={...o,isUpdate:s},d=this.backend.create.bind(this.backend);if(d.length<6)try{let m;d.length===5?m=d(e,t,i,n,h):m=d(e,t,i,n),m&&typeof m.then=="function"?m.then(y=>a(null,y)).catch(a):a(null,m)}catch(m){a(m)}else d(e,t,i,n,a,h)}!e||!e[0]||this.store.addResource(e[0],t,i,n)}}}const Oe=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:r=>{let e={};if(typeof r[1]=="object"&&(e=r[1]),g(r[1])&&(e.defaultValue=r[1]),g(r[2])&&(e.tDescription=r[2]),typeof r[2]=="object"||typeof r[3]=="object"){const t=r[3]||r[2];Object.keys(t).forEach(i=>{e[i]=t[i]})}return e},interpolation:{escapeValue:!0,format:r=>r,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Le=r=>{var e,t;return g(r.ns)&&(r.ns=[r.ns]),g(r.fallbackLng)&&(r.fallbackLng=[r.fallbackLng]),g(r.fallbackNS)&&(r.fallbackNS=[r.fallbackNS]),((t=(e=r.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(r.supportedLngs=r.supportedLngs.concat(["cimode"])),typeof r.initImmediate=="boolean"&&(r.initAsync=r.initImmediate),r},Q=()=>{},lt=r=>{Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(t=>{typeof r[t]=="function"&&(r[t]=r[t].bind(r))})};class z extends te{constructor(e={},t){if(super(),this.options=Le(e),this.services={},this.logger=R,this.modules={external:[]},lt(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(g(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const i=Oe();this.options={...i,...this.options,...Le(e)},this.options.interpolation={...i.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const n=u=>u?typeof u=="function"?new u:u:null;if(!this.options.isClone){this.modules.logger?R.init(n(this.modules.logger),this.options):R.init(null,this.options);let u;this.modules.formatter?u=this.modules.formatter:u=rt;const c=new be(this.options);this.store=new ge(this.options.resources,this.options);const f=this.services;f.logger=R,f.resourceStore=this.store,f.languageUtils=c,f.pluralResolver=new tt(c,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),u&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(f.formatter=n(u),f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new it(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new at(n(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",(p,...h)=>{this.emit(p,...h)}),this.modules.languageDetector&&(f.languageDetector=n(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=n(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new Z(this.services,this.options),this.translator.on("*",(p,...h)=>{this.emit(p,...h)}),this.modules.external.forEach(p=>{p.init&&p.init(this)})}if(this.format=this.options.interpolation.format,t||(t=Q),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const u=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);u.length>0&&u[0]!=="dev"&&(this.options.lng=u[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(u=>{this[u]=(...c)=>this.store[u](...c)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(u=>{this[u]=(...c)=>(this.store[u](...c),this)});const a=_(),l=()=>{const u=(c,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(f),t(c,f)};if(this.languages&&!this.isInitialized)return u(null,this.t.bind(this));this.changeLanguage(this.options.lng,u)};return this.options.resources||!this.options.initAsync?l():setTimeout(l,0),a}loadResources(e,t=Q){var s,o;let i=t;const n=g(e)?e:this.language;if(typeof e=="function"&&(i=e),!this.options.resources||this.options.partialBundledLanguages){if((n==null?void 0:n.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return i();const a=[],l=u=>{if(!u||u==="cimode")return;this.services.languageUtils.toResolveHierarchy(u).forEach(f=>{f!=="cimode"&&a.indexOf(f)<0&&a.push(f)})};n?l(n):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(c=>l(c)),(o=(s=this.options.preload)==null?void 0:s.forEach)==null||o.call(s,u=>l(u)),this.services.backendConnector.load(a,this.options.ns,u=>{!u&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),i(u)})}else i(null)}reloadResources(e,t,i){const n=_();return typeof e=="function"&&(i=e,e=void 0),typeof t=="function"&&(i=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),i||(i=Q),this.services.backendConnector.reload(e,t,s=>{n.resolve(),i(s)}),n}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Te.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const i=this.languages[t];if(!(["cimode","dev"].indexOf(i)>-1)&&this.store.hasLanguageSomeTranslations(i)){this.resolvedLanguage=i;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const i=_();this.emit("languageChanging",e);const n=a=>{this.language=a,this.languages=this.services.languageUtils.toResolveHierarchy(a),this.resolvedLanguage=void 0,this.setResolvedLanguage(a)},s=(a,l)=>{l?this.isLanguageChangingTo===e&&(n(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,i.resolve((...u)=>this.t(...u)),t&&t(a,(...u)=>this.t(...u))},o=a=>{var c,f;!e&&!a&&this.services.languageDetector&&(a=[]);const l=g(a)?a:a&&a[0],u=this.store.hasLanguageSomeTranslations(l)?l:this.services.languageUtils.getBestMatchFromCodes(g(a)?[a]:a);u&&(this.language||n(u),this.translator.language||this.translator.changeLanguage(u),(f=(c=this.services.languageDetector)==null?void 0:c.cacheUserLanguage)==null||f.call(c,u)),this.loadResources(u,p=>{s(p,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),i}getFixedT(e,t,i){const n=(s,o,...a)=>{let l;typeof o!="object"?l=this.options.overloadTranslationOptionHandler([s,o].concat(a)):l={...o},l.lng=l.lng||n.lng,l.lngs=l.lngs||n.lngs,l.ns=l.ns||n.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||i||n.keyPrefix);const u=this.options.keySeparator||".";let c;return l.keyPrefix&&Array.isArray(s)?c=s.map(f=>`${l.keyPrefix}${u}${f}`):c=l.keyPrefix?`${l.keyPrefix}${u}${s}`:s,this.t(c,l)};return g(e)?n.lng=e:n.lngs=e,n.ns=t,n.keyPrefix=i,n}t(...e){var t;return(t=this.translator)==null?void 0:t.translate(...e)}exists(...e){var t;return(t=this.translator)==null?void 0:t.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const i=t.lng||this.resolvedLanguage||this.languages[0],n=this.options?this.options.fallbackLng:!1,s=this.languages[this.languages.length-1];if(i.toLowerCase()==="cimode")return!0;const o=(a,l)=>{const u=this.services.backendConnector.state[`${a}|${l}`];return u===-1||u===0||u===2};if(t.precheck){const a=t.precheck(this,o);if(a!==void 0)return a}return!!(this.hasResourceBundle(i,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(i,e)&&(!n||o(s,e)))}loadNamespaces(e,t){const i=_();return this.options.ns?(g(e)&&(e=[e]),e.forEach(n=>{this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}),this.loadResources(n=>{i.resolve(),t&&t(n)}),i):(t&&t(),Promise.resolve())}loadLanguages(e,t){const i=_();g(e)&&(e=[e]);const n=this.options.preload||[],s=e.filter(o=>n.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return s.length?(this.options.preload=n.concat(s),this.loadResources(o=>{i.resolve(),t&&t(o)}),i):(t&&t(),Promise.resolve())}dir(e){var n,s;if(e||(e=this.resolvedLanguage||(((n=this.languages)==null?void 0:n.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],i=((s=this.services)==null?void 0:s.languageUtils)||new be(Oe());return t.indexOf(i.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new z(e,t)}cloneInstance(e={},t=Q){const i=e.forkResourceStore;i&&delete e.forkResourceStore;const n={...this.options,...e,isClone:!0},s=new z(n);if((e.debug!==void 0||e.prefix!==void 0)&&(s.logger=s.logger.clone(e)),["store","services","language"].forEach(a=>{s[a]=this[a]}),s.services={...this.services},s.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},i){const a=Object.keys(this.store.data).reduce((l,u)=>(l[u]={...this.store.data[u]},l[u]=Object.keys(l[u]).reduce((c,f)=>(c[f]={...l[u][f]},c),l[u]),l),{});s.store=new ge(a,n),s.services.resourceStore=s.store}return s.translator=new Z(s.services,n),s.translator.on("*",(a,...l)=>{s.emit(a,...l)}),s.init(n,t),s.translator.options=n,s.translator.backendConnector.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},s}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const x=z.createInstance();x.createInstance=z.createInstance;x.createInstance;x.dir;x.init;x.loadResources;x.reloadResources;x.use;x.changeLanguage;x.getFixedT;x.t;x.exists;x.setDefaultNamespace;x.hasLoadedNamespace;x.loadNamespaces;x.loadLanguages;const{slice:ut,forEach:ft}=[];function ct(r){return ft.call(ut.call(arguments,1),e=>{if(e)for(const t in e)r[t]===void 0&&(r[t]=e[t])}),r}function dt(r){return typeof r!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(t=>t.test(r))}const Pe=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,ht=function(r,e){const i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},n=encodeURIComponent(e);let s=`${r}=${n}`;if(i.maxAge>0){const o=i.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");s+=`; Max-Age=${Math.floor(o)}`}if(i.domain){if(!Pe.test(i.domain))throw new TypeError("option domain is invalid");s+=`; Domain=${i.domain}`}if(i.path){if(!Pe.test(i.path))throw new TypeError("option path is invalid");s+=`; Path=${i.path}`}if(i.expires){if(typeof i.expires.toUTCString!="function")throw new TypeError("option expires is invalid");s+=`; Expires=${i.expires.toUTCString()}`}if(i.httpOnly&&(s+="; HttpOnly"),i.secure&&(s+="; Secure"),i.sameSite)switch(typeof i.sameSite=="string"?i.sameSite.toLowerCase():i.sameSite){case!0:s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"strict":s+="; SameSite=Strict";break;case"none":s+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return i.partitioned&&(s+="; Partitioned"),s},ke={create(r,e,t,i){let n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};t&&(n.expires=new Date,n.expires.setTime(n.expires.getTime()+t*60*1e3)),i&&(n.domain=i),document.cookie=ht(r,e,n)},read(r){const e=`${r}=`,t=document.cookie.split(";");for(let i=0;i<t.length;i++){let n=t[i];for(;n.charAt(0)===" ";)n=n.substring(1,n.length);if(n.indexOf(e)===0)return n.substring(e.length,n.length)}return null},remove(r,e){this.create(r,"",-1,e)}};var pt={name:"cookie",lookup(r){let{lookupCookie:e}=r;if(e&&typeof document<"u")return ke.read(e)||void 0},cacheUserLanguage(r,e){let{lookupCookie:t,cookieMinutes:i,cookieDomain:n,cookieOptions:s}=e;t&&typeof document<"u"&&ke.create(t,r,i,n,s)}},gt={name:"querystring",lookup(r){var i;let{lookupQuerystring:e}=r,t;if(typeof window<"u"){let{search:n}=window.location;!window.location.search&&((i=window.location.hash)==null?void 0:i.indexOf("?"))>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));const o=n.substring(1).split("&");for(let a=0;a<o.length;a++){const l=o[a].indexOf("=");l>0&&o[a].substring(0,l)===e&&(t=o[a].substring(l+1))}}return t}},mt={name:"hash",lookup(r){var n;let{lookupHash:e,lookupFromHashIndex:t}=r,i;if(typeof window<"u"){const{hash:s}=window.location;if(s&&s.length>2){const o=s.substring(1);if(e){const a=o.split("&");for(let l=0;l<a.length;l++){const u=a[l].indexOf("=");u>0&&a[l].substring(0,u)===e&&(i=a[l].substring(u+1))}}if(i)return i;if(!i&&t>-1){const a=s.match(/\/([a-zA-Z-]*)/g);return Array.isArray(a)?(n=a[typeof t=="number"?t:0])==null?void 0:n.replace("/",""):void 0}}}return i}};let A=null;const Ce=()=>{if(A!==null)return A;try{if(A=typeof window<"u"&&window.localStorage!==null,!A)return!1;const r="i18next.translate.boo";window.localStorage.setItem(r,"foo"),window.localStorage.removeItem(r)}catch{A=!1}return A};var yt={name:"localStorage",lookup(r){let{lookupLocalStorage:e}=r;if(e&&Ce())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(r,e){let{lookupLocalStorage:t}=e;t&&Ce()&&window.localStorage.setItem(t,r)}};let U=null;const Re=()=>{if(U!==null)return U;try{if(U=typeof window<"u"&&window.sessionStorage!==null,!U)return!1;const r="i18next.translate.boo";window.sessionStorage.setItem(r,"foo"),window.sessionStorage.removeItem(r)}catch{U=!1}return U};var bt={name:"sessionStorage",lookup(r){let{lookupSessionStorage:e}=r;if(e&&Re())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(r,e){let{lookupSessionStorage:t}=e;t&&Re()&&window.sessionStorage.setItem(t,r)}},xt={name:"navigator",lookup(r){const e=[];if(typeof navigator<"u"){const{languages:t,userLanguage:i,language:n}=navigator;if(t)for(let s=0;s<t.length;s++)e.push(t[s]);i&&e.push(i),n&&e.push(n)}return e.length>0?e:void 0}},St={name:"htmlTag",lookup(r){let{htmlTag:e}=r,t;const i=e||(typeof document<"u"?document.documentElement:null);return i&&typeof i.getAttribute=="function"&&(t=i.getAttribute("lang")),t}},vt={name:"path",lookup(r){var n;let{lookupFromPathIndex:e}=r;if(typeof window>"u")return;const t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(t)?(n=t[typeof e=="number"?e:0])==null?void 0:n.replace("/",""):void 0}},wt={name:"subdomain",lookup(r){var n,s;let{lookupFromSubdomainIndex:e}=r;const t=typeof e=="number"?e+1:1,i=typeof window<"u"&&((s=(n=window.location)==null?void 0:n.hostname)==null?void 0:s.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(i)return i[t]}};let Ie=!1;try{document.cookie,Ie=!0}catch{}const Ae=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Ie||Ae.splice(1,1);const Ot=()=>({order:Ae,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:r=>r});class Lt{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=ct(t,this.options||{},Ot()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=n=>n.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=i,this.addDetector(pt),this.addDetector(gt),this.addDetector(yt),this.addDetector(bt),this.addDetector(xt),this.addDetector(St),this.addDetector(vt),this.addDetector(wt),this.addDetector(mt)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,t=[];return e.forEach(i=>{if(this.detectors[i]){let n=this.detectors[i].lookup(this.options);n&&typeof n=="string"&&(n=[n]),n&&(t=t.concat(n))}}),t=t.filter(i=>i!=null&&!dt(i)).map(i=>this.options.convertDetectedLanguage(i)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}cacheUserLanguage(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach(i=>{this.detectors[i]&&this.detectors[i].cacheUserLanguage(e,this.options)}))}}Lt.type="languageDetector";function re(r){"@babel/helpers - typeof";return re=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},re(r)}function Ue(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":re(XMLHttpRequest))==="object"}function Pt(r){return!!r&&typeof r.then=="function"}function kt(r){return Pt(r)?r:Promise.resolve(r)}function $e(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);e&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(r,n).enumerable})),t.push.apply(t,i)}return t}function je(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?$e(Object(t),!0).forEach(function(i){Ct(r,i,t[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):$e(Object(t)).forEach(function(i){Object.defineProperty(r,i,Object.getOwnPropertyDescriptor(t,i))})}return r}function Ct(r,e,t){return(e=Rt(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Rt(r){var e=$t(r,"string");return T(e)=="symbol"?e:e+""}function $t(r,e){if(T(r)!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var i=t.call(r,e);if(T(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function T(r){"@babel/helpers - typeof";return T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(r)}var F=typeof fetch=="function"?fetch:void 0;typeof global<"u"&&global.fetch?F=global.fetch:typeof window<"u"&&window.fetch&&(F=window.fetch);var X;Ue()&&(typeof global<"u"&&global.XMLHttpRequest?X=global.XMLHttpRequest:typeof window<"u"&&window.XMLHttpRequest&&(X=window.XMLHttpRequest));var ee;typeof ActiveXObject=="function"&&(typeof global<"u"&&global.ActiveXObject?ee=global.ActiveXObject:typeof window<"u"&&window.ActiveXObject&&(ee=window.ActiveXObject));typeof F!="function"&&(F=void 0);if(!F&&!X&&!ee)try{_e(()=>import("./vendor-misc-BUjjPnRU.js").then(r=>r.b),__vite__mapDeps([0,1,2])).then(function(r){F=r.default}).catch(function(){})}catch{}var oe=function(e,t){if(t&&T(t)==="object"){var i="";for(var n in t)i+="&"+encodeURIComponent(n)+"="+encodeURIComponent(t[n]);if(!i)return e;e=e+(e.indexOf("?")!==-1?"&":"?")+i.slice(1)}return e},Ee=function(e,t,i,n){var s=function(l){if(!l.ok)return i(l.statusText||"Error",{status:l.status});l.text().then(function(u){i(null,{status:l.status,data:u})}).catch(i)};if(n){var o=n(e,t);if(o instanceof Promise){o.then(s).catch(i);return}}typeof fetch=="function"?fetch(e,t).then(s).catch(i):F(e,t).then(s).catch(i)},Ne=!1,jt=function(e,t,i,n){e.queryStringParams&&(t=oe(t,e.queryStringParams));var s=je({},typeof e.customHeaders=="function"?e.customHeaders():e.customHeaders);typeof window>"u"&&typeof global<"u"&&typeof global.process<"u"&&global.process.versions&&global.process.versions.node&&(s["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),i&&(s["Content-Type"]="application/json");var o=typeof e.requestOptions=="function"?e.requestOptions(i):e.requestOptions,a=je({method:i?"POST":"GET",body:i?e.stringify(i):void 0,headers:s},Ne?{}:o),l=typeof e.alternateFetch=="function"&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{Ee(t,a,n,l)}catch(u){if(!o||Object.keys(o).length===0||!u.message||u.message.indexOf("not implemented")<0)return n(u);try{Object.keys(o).forEach(function(c){delete a[c]}),Ee(t,a,n,l),Ne=!0}catch(c){n(c)}}},Et=function(e,t,i,n){i&&T(i)==="object"&&(i=oe("",i).slice(1)),e.queryStringParams&&(t=oe(t,e.queryStringParams));try{var s=X?new X:new ee("MSXML2.XMLHTTP.3.0");s.open(i?"POST":"GET",t,1),e.crossDomain||s.setRequestHeader("X-Requested-With","XMLHttpRequest"),s.withCredentials=!!e.withCredentials,i&&s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),s.overrideMimeType&&s.overrideMimeType("application/json");var o=e.customHeaders;if(o=typeof o=="function"?o():o,o)for(var a in o)s.setRequestHeader(a,o[a]);s.onreadystatechange=function(){s.readyState>3&&n(s.status>=400?s.statusText:null,{status:s.status,data:s.responseText})},s.send(i)}catch(l){console&&console.log(l)}},Nt=function(e,t,i,n){if(typeof i=="function"&&(n=i,i=void 0),n=n||function(){},F&&t.indexOf("file:")!==0)return jt(e,t,i,n);if(Ue()||typeof ActiveXObject=="function")return Et(e,t,i,n);n(new Error("No fetch and no xhr implementation found!"))};function H(r){"@babel/helpers - typeof";return H=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},H(r)}function Fe(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);e&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(r,n).enumerable})),t.push.apply(t,i)}return t}function ne(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Fe(Object(t),!0).forEach(function(i){He(r,i,t[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):Fe(Object(t)).forEach(function(i){Object.defineProperty(r,i,Object.getOwnPropertyDescriptor(t,i))})}return r}function Ft(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Dt(r,e){for(var t=0;t<e.length;t++){var i=e[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,Me(i.key),i)}}function Tt(r,e,t){return e&&Dt(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function He(r,e,t){return(e=Me(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Me(r){var e=It(r,"string");return H(e)=="symbol"?e:e+""}function It(r,e){if(H(r)!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var i=t.call(r,e);if(H(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}var At=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(t){return JSON.parse(t)},stringify:JSON.stringify,parsePayload:function(t,i,n){return He({},i,n||"")},parseLoadPayload:function(t,i){},request:Nt,reloadInterval:typeof window<"u"?!1:60*60*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},Ut=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Ft(this,r),this.services=e,this.options=t,this.allOptions=i,this.type="backend",this.init(e,t,i)}return Tt(r,[{key:"init",value:function(t){var i=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.services=t,this.options=ne(ne(ne({},At()),this.options||{}),n),this.allOptions=s,this.services&&this.options.reloadInterval){var o=setInterval(function(){return i.reload()},this.options.reloadInterval);H(o)==="object"&&typeof o.unref=="function"&&o.unref()}}},{key:"readMulti",value:function(t,i,n){this._readAny(t,t,i,i,n)}},{key:"read",value:function(t,i,n){this._readAny([t],t,[i],i,n)}},{key:"_readAny",value:function(t,i,n,s,o){var a=this,l=this.options.loadPath;typeof this.options.loadPath=="function"&&(l=this.options.loadPath(t,n)),l=kt(l),l.then(function(u){if(!u)return o(null,{});var c=a.services.interpolator.interpolate(u,{lng:t.join("+"),ns:n.join("+")});a.loadUrl(c,o,i,s)})}},{key:"loadUrl",value:function(t,i,n,s){var o=this,a=typeof n=="string"?[n]:n,l=typeof s=="string"?[s]:s,u=this.options.parseLoadPayload(a,l);this.options.request(this.options,t,u,function(c,f){if(f&&(f.status>=500&&f.status<600||!f.status))return i("failed loading "+t+"; status code: "+f.status,!0);if(f&&f.status>=400&&f.status<500)return i("failed loading "+t+"; status code: "+f.status,!1);if(!f&&c&&c.message){var p=c.message.toLowerCase(),h=["failed","fetch","network","load"].find(function(y){return p.indexOf(y)>-1});if(h)return i("failed loading "+t+": "+c.message,!0)}if(c)return i(c,!1);var d,m;try{typeof f.data=="string"?d=o.options.parse(f.data,n,s):d=f.data}catch{m="failed parsing "+t+" to json"}if(m)return i(m,!1);i(null,d)})}},{key:"create",value:function(t,i,n,s,o){var a=this;if(this.options.addPath){typeof t=="string"&&(t=[t]);var l=this.options.parsePayload(i,n,s),u=0,c=[],f=[];t.forEach(function(p){var h=a.options.addPath;typeof a.options.addPath=="function"&&(h=a.options.addPath(p,i));var d=a.services.interpolator.interpolate(h,{lng:p,ns:i});a.options.request(a.options,d,l,function(m,y){u+=1,c.push(m),f.push(y),u===t.length&&typeof o=="function"&&o(c,f)})})}}},{key:"reload",value:function(){var t=this,i=this.services,n=i.backendConnector,s=i.languageUtils,o=i.logger,a=n.language;if(!(a&&a.toLowerCase()==="cimode")){var l=[],u=function(f){var p=s.toResolveHierarchy(f);p.forEach(function(h){l.indexOf(h)<0&&l.push(h)})};u(a),this.allOptions.preload&&this.allOptions.preload.forEach(function(c){return u(c)}),l.forEach(function(c){t.allOptions.ns.forEach(function(f){n.read(c,f,"read",null,null,function(p,h){p&&o.warn("loading namespace ".concat(f," for language ").concat(c," failed"),p),!p&&h&&o.log("loaded namespace ".concat(f," for language ").concat(c),h),n.loaded("".concat(c,"|").concat(f),p,h)})})})}}}])}();Ut.type="backend";export{Ut as B,Lt as a,x as i};
//# sourceMappingURL=vendor-i18n-Csuit5Sx.js.map
