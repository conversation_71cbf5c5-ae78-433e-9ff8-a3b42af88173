/* Grayscale effect with hover color transition */
.grayscale-effect {
  filter: grayscale(100%);
  transition: filter 0.5s ease;
}

.grayscale-effect:hover {
  filter: grayscale(0%);
}

/* Team image specific styles */
.team-image {
  object-fit: cover !important;
  width: 300px !important;
  height: 300px !important;
  display: block !important;
  margin: 0 auto !important;
  transition: transform 0.4s ease !important;
  transform: scale(1) !important;
}

/* Override the overflow hidden on team-item-image */
.team-item-image {
  overflow: hidden !important;
  width: 300px !important;
  height: 300px !important;
  margin: 0 auto !important;
  border-radius: 8px !important;
}

/* Team image hover zoom effect */
.team-item-image:hover .team-image {
  transform: scale(1.1) !important;
}

/* Adjust <PERSON><PERSON>'s image to show upper portion */
[alt="Image of Timo Lambing"] {
  object-position: center top !important;
}

/* Add padding to <PERSON><PERSON>'s image container without changing image size */
.timo-image-container {
  position: relative !important;
}

.timo-image-container::before {
  content: "" !important;
  position: absolute !important;
  top: -10px !important;
  left: -10px !important;
  right: -10px !important;
  bottom: -10px !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-radius: 5px !important;
  z-index: -1 !important;
}
