import React from "react";
import PropTypes from "prop-types";

const AnalyticsOverview = ({ data }) => {
  if (!data) {
    return (
      <div className="analytics-overview-skeleton">
        <div className="row">
          {[1, 2, 3].map((i) => (
            <div key={i} className="col-md-4 mb-3">
              <div className="card border-0 shadow-sm">
                <div className="card-body p-4">
                  <div className="placeholder-glow">
                    <div className="placeholder col-6 mb-2"></div>
                    <div
                      className="placeholder col-8 mb-1"
                      style={{ height: "2rem" }}
                    ></div>
                    <div className="placeholder col-4"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num?.toLocaleString() || "0";
  };

  const formatPercentage = (current, previous) => {
    if (!previous || previous === 0) return null;
    const change = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(change).toFixed(1),
      isPositive: change >= 0,
      isSignificant: Math.abs(change) >= 1,
    };
  };

  const MetricCard = ({
    title,
    value,
    previousValue,
    icon,
    color = "primary",
  }) => {
    const percentage = formatPercentage(value, previousValue);

    return (
      <div className="col-md-4 mb-3">
        <div className="card border-0 shadow-sm h-100 bg-white">
          <div className="card-body p-4">
            <div className="d-flex align-items-center justify-content-between mb-3">
              <h6 className="card-title text-muted mb-0 fw-normal">{title}</h6>
              <iconify-icon
                icon={icon}
                className={`text-${color} fs-4`}
              ></iconify-icon>
            </div>

            <div className="mb-2">
              <h2 className="mb-0 fw-bold text-dark">{formatNumber(value)}</h2>
            </div>

            {percentage && percentage.isSignificant && (
              <div className="d-flex align-items-center">
                <iconify-icon
                  icon={
                    percentage.isPositive
                      ? "solar:arrow-up-bold"
                      : "solar:arrow-down-bold"
                  }
                  className={`me-1 ${
                    percentage.isPositive ? "text-success" : "text-danger"
                  }`}
                ></iconify-icon>
                <span
                  className={`small fw-medium ${
                    percentage.isPositive ? "text-success" : "text-danger"
                  }`}
                >
                  {percentage.value}%
                </span>
                <span className="text-muted small ms-1">
                  vs previous period
                </span>
              </div>
            )}

            {(!percentage || !percentage.isSignificant) && (
              <div className="text-muted small">No significant change</div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="analytics-overview">
      <div className="row">
        <MetricCard
          title="Post views"
          value={data.pageViews?.current || 0}
          previousValue={data.pageViews?.previous || 0}
          icon="solar:eye-bold"
          color="primary"
        />

        <MetricCard
          title="Visitors"
          value={data.visitors?.current || 0}
          previousValue={data.visitors?.previous || 0}
          icon="solar:users-group-rounded-bold"
          color="info"
        />

        <MetricCard
          title="Engagement"
          value={data.engagement?.current || 0}
          previousValue={data.engagement?.previous || 0}
          icon="solar:heart-bold"
          color="success"
        />
      </div>
    </div>
  );
};

AnalyticsOverview.propTypes = {
  data: PropTypes.shape({
    totalPageViews: PropTypes.number,
    totalVisitors: PropTypes.number,
    avgEngagement: PropTypes.number,
    pageViewsChange: PropTypes.number,
    visitorsChange: PropTypes.number,
    engagementChange: PropTypes.number,
  }),
};

export default AnalyticsOverview;
