import React from "react";
import Footer from "@/components/footers/Footer";
import Header from "@/components/headers/Header";
import { Link } from "react-router-dom";
import { menuItems } from "@/data/menu";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import { useTranslation } from "react-i18next";

export default function MainPageNotFound() {
  const { t } = useTranslation();

  return (
    <>
      <UnifiedSEO
        title="Page Not Found - 404"
        description="The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."
        slug="404"
        type="website"
        image="https://devskills.ee/404.jpg"
        keywords={["404", "page not found", "error", "devskills"]}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              {/* Hero Section */}
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-1 mb-20 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                    style={{ fontSize: "8rem", fontWeight: "700" }}
                  >
                    404
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p
                        className="section-title-small mb-0 opacity-075"
                        style={{ fontSize: "2rem" }}
                      >
                        Page Not Found
                      </p>
                    </div>
                  </div>
                  <div className="spacer-small"></div>
                </div>
              </section>

              {/* Content Section */}
              <section className="page-section bg-dark-1 light-content">
                <div className="container position-relative">
                  <div className="row">
                    <div className="col-lg-8 offset-lg-2 text-center">
                      <div className="wow fadeInUp" data-wow-delay="0.1s">
                        <h2 className="section-title mb-30 mb-sm-20">
                          <span className="text-gray">Oops!</span> Something
                          went wrong
                          <span className="text-gray">.</span>
                        </h2>
                        <div className="text-gray mb-40 mb-sm-30">
                          <p className="mb-20">
                            The page you were looking for could not be found. It
                            might have been removed, had its name changed, or is
                            temporarily unavailable.
                          </p>
                          <p className="mb-0">
                            Don't worry, you can navigate back to our homepage
                            or explore our services.
                          </p>
                        </div>
                        <div className="local-scroll">
                          <Link
                            to="/"
                            className="btn btn-mod btn-w btn-circle btn-medium me-3 mb-xs-10"
                            data-btn-animate="y"
                          >
                            <span className="btn-animate-y">
                              <span className="btn-animate-y-1">
                                <i className="mi-home size-18 align-center me-2" />
                                Back to Home
                              </span>
                              <span
                                className="btn-animate-y-2"
                                aria-hidden="true"
                              >
                                <i className="mi-home size-18 align-center me-2" />
                                Back to Home
                              </span>
                            </span>
                          </Link>
                          <Link
                            to="/services"
                            className="btn btn-mod btn-border-w btn-circle btn-medium"
                            data-btn-animate="y"
                          >
                            <span className="btn-animate-y">
                              <span className="btn-animate-y-1">
                                Our Services
                              </span>
                              <span
                                className="btn-animate-y-2"
                                aria-hidden="true"
                              >
                                Our Services
                              </span>
                            </span>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </main>

            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>
        </div>
      </div>
    </>
  );
}
