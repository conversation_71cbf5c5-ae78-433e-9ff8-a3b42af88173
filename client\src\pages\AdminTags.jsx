import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import { adminAPI } from "../utils/api";

const AdminTags = () => {
  const navigate = useNavigate();
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
  });

  // Load tags on component mount
  useEffect(() => {
    loadTags();
  }, []);

  const loadTags = async () => {
    try {
      setLoading(true);
      const { response, data } = await adminAPI.getTags();

      if (data.success) {
        setTags(data.data || []);
      } else {
        setError(data.message || "Failed to load tags");
      }
    } catch (error) {
      console.error("Load tags error:", error);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    try {
      let response, data;

      if (editingTag) {
        ({ response, data } = await adminAPI.updateTag(
          editingTag.id,
          formData
        ));
      } else {
        ({ response, data } = await adminAPI.createTag(formData));
      }

      if (data.success) {
        setSuccess(
          editingTag ? "Tag updated successfully!" : "Tag created successfully!"
        );
        setShowModal(false);
        setEditingTag(null);
        setFormData({ name: "" });
        loadTags();
      } else {
        setError(data.message || "Failed to save tag");
      }
    } catch (error) {
      console.error("Save tag error:", error);
      setError("Network error. Please try again.");
    }
  };

  const handleEdit = (tag) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name,
    });
    setShowModal(true);
  };

  const handleDelete = async (tagId) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this tag? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const { response, data } = await adminAPI.deleteTag(tagId);

      if (data.success) {
        setSuccess("Tag deleted successfully!");
        loadTags();
      } else {
        setError(data.message || "Failed to delete tag");
      }
    } catch (error) {
      console.error("Delete tag error:", error);
      setError("Network error. Please try again.");
    }
  };

  const openCreateModal = () => {
    setEditingTag(null);
    setFormData({ name: "" });
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingTag(null);
    setFormData({ name: "" });
    setError("");
  };

  return (
    <>
      <SEO
        title="Manage Tags - Admin"
        description="Manage blog tags in the admin panel"
        noIndex={true}
      />

      <AdminLayout title="Tags">
        {/* Action Bar */}
        <div className="mb-30">
          <div className="row align-items-center">
            <div className="col-12 col-lg-6 mb-3 mb-lg-0">
              <p className="section-descr mb-0">
                Tag your blog posts for better organization and discoverability.
                Create and manage content tags.
              </p>
            </div>
            <div className="col-12 col-lg-6 text-lg-end">
              <button
                onClick={openCreateModal}
                className="btn btn-mod btn-color btn-round w-100 w-lg-auto"
              >
                <iconify-icon
                  icon="solar:add-circle-bold"
                  className="me-2"
                ></iconify-icon>
                New Tag
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="alert alert-danger mb-30" role="alert">
            <iconify-icon
              icon="solar:danger-triangle-bold"
              className="me-2"
            ></iconify-icon>
            {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success mb-30" role="alert">
            <iconify-icon
              icon="solar:check-circle-bold"
              className="me-2"
            ></iconify-icon>
            {success}
          </div>
        )}

        {/* Tags Table */}
        <div className="admin-table">
          {loading ? (
            <div className="text-center py-60" style={{ padding: "40px 20px" }}>
              <iconify-icon
                icon="solar:refresh-bold"
                className="fa-2x color-primary-1 mb-20"
                style={{ animation: "spin 1s linear infinite" }}
              ></iconify-icon>
              <div className="hs-line-4 font-alt black">Loading tags...</div>
            </div>
          ) : tags.length === 0 ? (
            <div className="text-center py-60" style={{ padding: "40px 20px" }}>
              <iconify-icon
                icon="solar:tag-bold"
                className="fa-3x color-gray-light-1 mb-20"
              ></iconify-icon>
              <div className="hs-line-4 font-alt black mb-10">
                No tags found
              </div>
              <p className="section-descr mb-30">
                Create your first tag to start organizing your blog posts.
              </p>
              <button
                onClick={openCreateModal}
                className="btn btn-mod btn-color btn-round"
              >
                <iconify-icon
                  icon="solar:add-circle-bold"
                  className="me-2"
                ></iconify-icon>
                Create First Tag
              </button>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="d-none d-lg-block">
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Tag Name</th>
                        <th>Posts</th>
                        <th>Created</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tags.map((tag) => (
                        <tr key={tag.id}>
                          <td>
                            <div className="d-flex align-items-center">
                              <iconify-icon
                                icon="solar:tag-bold"
                                className="me-3 color-primary-1"
                              ></iconify-icon>
                              <div>
                                <div className="fw-bold">{tag.name}</div>
                                <small className="text-muted">
                                  /{tag.slug}
                                </small>
                              </div>
                            </div>
                          </td>
                          <td>
                            <span className="badge bg-secondary">
                              {tag._count?.posts || 0} posts
                            </span>
                          </td>
                          <td>
                            {new Date(tag.createdAt).toLocaleDateString()}
                          </td>
                          <td>
                            <div className="btn-group" role="group">
                              <button
                                onClick={() => handleEdit(tag)}
                                className="btn btn-sm btn-outline-primary"
                                title="Edit"
                              >
                                <iconify-icon icon="solar:pen-bold"></iconify-icon>
                              </button>
                              <button
                                onClick={() => handleDelete(tag.id)}
                                className="btn btn-sm btn-outline-danger"
                                title="Delete"
                              >
                                <iconify-icon icon="solar:trash-bin-trash-bold"></iconify-icon>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Mobile Card View */}
              <div className="d-lg-none">
                <div className="row g-3">
                  {tags.map((tag) => (
                    <div key={tag.id} className="col-12">
                      <div className="card border-0 shadow-sm">
                        <div className="card-body p-3">
                          <div className="row align-items-center">
                            <div className="col-12 mb-2">
                              <div className="d-flex align-items-center">
                                <iconify-icon
                                  icon="solar:tag-bold"
                                  className="me-3 color-primary-1"
                                  style={{ fontSize: "1.5rem" }}
                                ></iconify-icon>
                                <div className="flex-grow-1">
                                  <h6 className="mb-1 fw-bold">{tag.name}</h6>
                                  <small className="text-muted">
                                    /{tag.slug}
                                  </small>
                                </div>
                              </div>
                            </div>

                            <div className="col-6 col-sm-6 mb-2">
                              <small className="text-muted d-block">
                                Posts
                              </small>
                              <span className="badge bg-secondary">
                                {tag._count?.posts || 0} posts
                              </span>
                            </div>

                            <div className="col-6 col-sm-6 mb-2">
                              <small className="text-muted d-block">
                                Created
                              </small>
                              <small>
                                {new Date(tag.createdAt).toLocaleDateString()}
                              </small>
                            </div>

                            <div className="col-12">
                              <div className="d-flex gap-2 flex-wrap">
                                <button
                                  onClick={() => handleEdit(tag)}
                                  className="btn btn-sm btn-outline-primary flex-fill"
                                  title="Edit"
                                >
                                  <iconify-icon
                                    icon="solar:pen-bold"
                                    className="me-1"
                                  ></iconify-icon>
                                  Edit
                                </button>

                                <button
                                  onClick={() => handleDelete(tag.id)}
                                  className="btn btn-sm btn-outline-danger flex-fill"
                                  title="Delete"
                                >
                                  <iconify-icon
                                    icon="solar:trash-bin-trash-bold"
                                    className="me-1"
                                  ></iconify-icon>
                                  Delete
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="modal-overlay" onClick={closeModal}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="modal-header">
                <h4 className="modal-title">
                  <iconify-icon
                    icon="solar:tag-bold"
                    className="me-2"
                  ></iconify-icon>
                  {editingTag ? "Edit Tag" : "Create New Tag"}
                </h4>
                <button
                  type="button"
                  className="modal-close"
                  onClick={closeModal}
                >
                  <iconify-icon icon="solar:close-circle-bold"></iconify-icon>
                </button>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-12 mb-20">
                      <label className="form-label">
                        <iconify-icon
                          icon="solar:pen-bold"
                          className="me-2"
                        ></iconify-icon>
                        Tag Name *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        className="form-control"
                        placeholder="Enter tag name"
                        required
                      />
                      <small className="form-text text-muted">
                        Keep it short and descriptive (e.g., "JavaScript",
                        "Tutorial", "News")
                      </small>
                    </div>
                  </div>
                </div>

                <div className="modal-footer">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="btn btn-mod btn-gray btn-round me-3"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn btn-mod btn-color btn-round"
                  >
                    <iconify-icon
                      icon="solar:check-circle-bold"
                      className="me-2"
                    ></iconify-icon>
                    {editingTag ? "Update Tag" : "Create Tag"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default AdminTags;
