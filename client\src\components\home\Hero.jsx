// client\src\components\home\Hero.jsx
import AnimatedText from "@/components/common/AnimatedText";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { trackButtonClick } from "@/utils/analytics";

export default function Hero() {
  const { t } = useTranslation();
  const [instantShown, setInstantShown] = useState(false);

  useEffect(() => {
    // Reset state for fresh navigation
    document.body.classList.remove("hero-instant-shown");

    // Try to show instant hero (works for both fresh loads and navigation)
    const showHero = () => {
      if (window.showInstantHero) {
        const success = window.showInstantHero(false);
        if (success) {
          setInstantShown(true);

          // Add click handler to instant hero button
          setTimeout(() => {
            const instantButton = document.querySelector(
              "#home .link-hover-anim"
            );
            if (instantButton) {
              instantButton.addEventListener("click", handleDiscoverClick);
            }
          }, 150);

          return true;
        }
      }
      return false;
    };

    // Try immediate show
    if (!showHero()) {
      // If immediate show failed, try after a short delay (for navigation cases)
      const timer = setTimeout(() => {
        if (!showHero()) {
          // If instant hero still fails, let React render normally
          setInstantShown(false);
        }
      }, 50);

      return () => clearTimeout(timer);
    }

    // Cleanup function for event listeners
    return () => {
      const instantButton = document.querySelector("#home .link-hover-anim");
      if (instantButton) {
        instantButton.removeEventListener("click", handleDiscoverClick);
      }
    };
  }, []);

  const handleDiscoverClick = () => {
    trackButtonClick("Discover Now", "Hero Section", {
      button_type: "cta",
      section: "hero",
    });
  };
  return (
    <>
      <div className="container min-height-100vh d-flex align-items-center pt-100 pb-100 pt-sm-120 pb-sm-120">
        {/* Home Section Content */}
        <div className="home-content text-center">
          {!instantShown && (
            <>
              <h2 className="section-title-tiny mb-50 mb-sm-30 wow fadeInDownShort">
                {t("hero.welcome")}
              </h2>
              <h1 className="hs-title-3 mb-120 mb-sm-80 mb-xs-140">
                <span className="wow charsAnimInLong" data-splitting="chars">
                  <AnimatedText text={t("hero.studio")} />
                </span>
              </h1>
              <div
                className="local-scroll wow fadeInUpShort"
                data-wow-delay="0.57s"
              >
                <a
                  href="#about"
                  className="link-hover-anim link-circle-1 align-middle"
                  data-link-animate="y"
                  onClick={handleDiscoverClick}
                >
                  <span className="link-strong link-strong-unhovered">
                    {t("hero.discover")}{" "}
                    <i
                      className="mi-arrow-right size-18 align-middle"
                      aria-hidden="true"
                    ></i>
                  </span>
                  <span
                    className="link-strong link-strong-hovered"
                    aria-hidden="true"
                  >
                    {t("hero.discover")}{" "}
                    <i
                      className="mi-arrow-right size-18 align-middle"
                      aria-hidden="true"
                    ></i>
                  </span>
                </a>
              </div>
            </>
          )}
        </div>
        {/* End Home Section Content */}
      </div>
      {/* Scroll Down - Outside container for full width positioning */}
      <div
        className="local-scroll scroll-down-3-wrap wow fadeInUp"
        data-wow-offset={0}
      >
        <a href="#about" className="scroll-down-3">
          {!instantShown ? t("hero.scrollDown") || "Scroll Down" : ""}
        </a>
      </div>
      {/* End Scroll Down */}
    </>
  );
}
