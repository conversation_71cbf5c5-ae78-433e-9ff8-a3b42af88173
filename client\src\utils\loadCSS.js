/**
 * CSS Loading Utility
 * Loads non-critical CSS after the critical rendering path
 */

let nonCriticalCSSLoaded = false;

/**
 * Load CSS file asynchronously
 * @param {string} href - CSS file path
 * @param {string} id - Unique ID for the link element
 * @returns {Promise} - Resolves when CSS is loaded
 */
export function loadCSS(href, id = null) {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (id && document.getElementById(id)) {
      resolve();
      return;
    }

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    if (id) link.id = id;

    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));

    document.head.appendChild(link);
  });
}

/**
 * Load non-critical CSS after first paint
 * Note: Main CSS files are now preloaded in HTML for FOUC elimination
 */
export function loadNonCriticalCSS() {
  if (nonCriticalCSSLoaded) return Promise.resolve();

  nonCriticalCSSLoaded = true;

  // Only load remaining CSS files that aren't preloaded in HTML
  const cssFiles = [
    "/assets/css/magnific-popup.css",
    "/assets/css/owl.carousel.css",
    "/assets/css/splitting.css",
    "/assets/css/YTPlayer.css",
    "/assets/css/demo-main/demo-main.css",
    "/assets/css/custom.css",
    "/assets/css/style-responsive.css",
  ];

  // Load remaining CSS files in parallel
  const loadPromises = cssFiles.map((href, index) =>
    loadCSS(href, `non-critical-${index}`)
  );

  return Promise.all(loadPromises)
    .then(() => {
      console.log("Remaining non-critical CSS loaded");
      // Also load the custom styles bundle
      return import("../styles/non-critical.css");
    })
    .catch((error) => {
      console.error("Failed to load non-critical CSS:", error);
    });
}

/**
 * Initialize CSS loading strategy
 */
export function initCSSLoading() {
  // Load remaining non-critical CSS immediately (main CSS is preloaded in HTML)
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadNonCriticalCSS);
  } else {
    // DOM is already ready - load immediately
    loadNonCriticalCSS();
  }

  // Also load on window load as fallback
  window.addEventListener("load", loadNonCriticalCSS);
}

/**
 * Preload CSS for better performance
 * @param {string} href - CSS file path
 */
export function preloadCSS(href) {
  const link = document.createElement("link");
  link.rel = "preload";
  link.as = "style";
  link.href = href;
  link.onload = function () {
    this.onload = null;
    this.rel = "stylesheet";
  };
  document.head.appendChild(link);

  // Fallback for browsers that don't support preload
  const noscript = document.createElement("noscript");
  const fallbackLink = document.createElement("link");
  fallbackLink.rel = "stylesheet";
  fallbackLink.href = href;
  noscript.appendChild(fallbackLink);
  document.head.appendChild(noscript);
}
