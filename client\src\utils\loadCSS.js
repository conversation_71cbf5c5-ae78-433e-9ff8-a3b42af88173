/**
 * CSS Loading Utility
 * Loads non-critical CSS after the critical rendering path
 */

let nonCriticalCSSLoaded = false;

/**
 * Load CSS file asynchronously
 * @param {string} href - CSS file path
 * @param {string} id - Unique ID for the link element
 * @returns {Promise} - Resolves when CSS is loaded
 */
export function loadCSS(href, id = null) {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (id && document.getElementById(id)) {
      resolve();
      return;
    }

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    if (id) link.id = id;

    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));

    document.head.appendChild(link);
  });
}

/**
 * Load non-critical CSS after first paint
 */
export function loadNonCriticalCSS() {
  if (nonCriticalCSSLoaded) return Promise.resolve();

  nonCriticalCSSLoaded = true;

  // Load CSS files directly from public directory to avoid bundling
  const cssFiles = [
    "/assets/css/bootstrap.min.css",
    "/assets/css/style.css",
    "/assets/css/vertical-rhythm.min.css",
    "/assets/css/magnific-popup.css",
    "/assets/css/owl.carousel.css",
    "/assets/css/splitting.css",
    "/assets/css/YTPlayer.css",
    "/assets/css/demo-main/demo-main.css",
    "/assets/css/demo-elegant/demo-elegant.css",
    "/assets/css/custom.css",
    "/assets/css/style-responsive.css",
  ];

  // Load all CSS files in parallel
  const loadPromises = cssFiles.map((href, index) =>
    loadCSS(href, `non-critical-${index}`)
  );

  return Promise.all(loadPromises)
    .then(() => {
      console.log("Non-critical CSS loaded");
      // Also load the custom styles
      return import("../styles/non-critical.css");
    })
    .catch((error) => {
      console.error("Failed to load non-critical CSS:", error);
    });
}

/**
 * Initialize CSS loading strategy
 */
export function initCSSLoading() {
  // Load non-critical CSS after DOM is ready and critical content is painted
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      // Small delay to ensure critical content is painted first
      setTimeout(loadNonCriticalCSS, 100);
    });
  } else {
    // DOM is already ready
    setTimeout(loadNonCriticalCSS, 100);
  }

  // Also load on window load as fallback
  window.addEventListener("load", loadNonCriticalCSS);
}

/**
 * Preload CSS for better performance
 * @param {string} href - CSS file path
 */
export function preloadCSS(href) {
  const link = document.createElement("link");
  link.rel = "preload";
  link.as = "style";
  link.href = href;
  link.onload = function () {
    this.onload = null;
    this.rel = "stylesheet";
  };
  document.head.appendChild(link);

  // Fallback for browsers that don't support preload
  const noscript = document.createElement("noscript");
  const fallbackLink = document.createElement("link");
  fallbackLink.rel = "stylesheet";
  fallbackLink.href = href;
  noscript.appendChild(fallbackLink);
  document.head.appendChild(noscript);
}
