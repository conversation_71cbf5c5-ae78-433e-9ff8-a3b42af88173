import React from "react";
import Footer from "@/components/footers/Footer";
import Header from "@/components/headers/Header";
import Home from "@/components/home";
import Hero from "@/components/home/<USER>";
import { menuItems } from "@/data/menu";
import ParallaxContainer from "@/components/common/ParallaxContainer";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import { getPageSEOData } from "@/utils/seoHelpers";

// JSON-LD structured data for the homepage
const homeSchema = {
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "@id": "https://devskills.ee/#organization",
  name: "DevSkills OÜ",
  alternateName: "DevSkills Development Studio",
  url: "https://devskills.ee",
  logo: {
    "@type": "ImageObject",
    url: "https://devskills.ee/logo.png",
    width: "180",
    height: "60",
  },
  description:
    "DevSkills offers professional software development services, custom solutions, AI development, and white-label software including Business Comanager.",
  address: {
    "@type": "PostalAddress",
    streetAddress: "Tornimäe tn 7",
    addressLocality: "Tallinn",
    postalCode: "10145",
    addressCountry: "EE",
  },
  geo: {
    "@type": "GeoCoordinates",
    latitude: 59.437,
    longitude: 24.7536,
  },
  contactPoint: {
    "@type": "ContactPoint",
    telephone: "+372 5628 2038",
    contactType: "customer service",
    availableLanguage: ["English", "Estonian", "Finnish", "German", "Swedish"],
  },
  openingHoursSpecification: {
    "@type": "OpeningHoursSpecification",
    dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
    opens: "08:00",
    closes: "17:00",
  },
  serviceArea: {
    "@type": "Country",
    name: "Estonia",
  },
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: "Software Development Services",
    itemListElement: [
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Custom Software Development",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Web Development",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "AI Solutions",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "White Label Software",
        },
      },
    ],
  },
  sameAs: [
    "https://www.facebook.com/devskillsee",
    "https://www.linkedin.com/company/devskills-ee",
    "https://twitter.com/DevSkillsEE",
  ],
};

// Additional WebSite schema
const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "@id": "https://devskills.ee/#website",
  name: "DevSkills",
  alternateName: "DevSkills Development Studio",
  url: "https://devskills.ee",
  description:
    "Professional software development services and custom solutions",
  publisher: {
    "@id": "https://devskills.ee/#organization",
  },
  potentialAction: {
    "@type": "SearchAction",
    target: "https://devskills.ee/search?q={search_term_string}",
    "query-input": "required name=search_term_string",
  },
  inLanguage: ["en", "et", "fi", "de", "sv"],
};
export default function Home5MainDemoMultiPageDark() {
  const seoData = getPageSEOData("homepage");

  return (
    <>
      <UnifiedSEO
        title={seoData.title}
        description={seoData.description}
        slug=""
        type="website"
        image="https://devskills.ee/home.jpg"
        imageAlt="DevSkills Development Studio - Professional Software Development"
        schema={seoData.schema}
        keywords={seoData.keywords}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <ParallaxContainer
                className="home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <Hero />
              </ParallaxContainer>

              <Home dark />
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
