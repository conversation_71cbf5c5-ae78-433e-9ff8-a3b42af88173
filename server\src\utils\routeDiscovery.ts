import fs from "fs";
import path from "path";

export interface DiscoveredRoute {
  path: string;
  priority: string;
  changefreq:
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never";
  includeInSitemap: boolean;
  requiresLanguagePrefix: boolean;
}

// Default route configurations based on common patterns
const getDefaultRouteConfig = (
  routePath: string
): Omit<DiscoveredRoute, "path"> => {
  const defaults = {
    includeInSitemap: true,
    requiresLanguagePrefix: true,
  };

  // Route-specific configurations
  if (routePath === "" || routePath === "/") {
    return { ...defaults, priority: "1.0", changefreq: "weekly" };
  }

  if (routePath.includes("blog")) {
    return { ...defaults, priority: "0.9", changefreq: "daily" };
  }

  if (routePath.includes("portfolio") || routePath.includes("services")) {
    return { ...defaults, priority: "0.8", changefreq: "weekly" };
  }

  if (routePath.includes("about") || routePath.includes("contact")) {
    return { ...defaults, priority: "0.8", changefreq: "monthly" };
  }

  // Default for any other route
  return { ...defaults, priority: "0.7", changefreq: "monthly" };
};

// Routes that should be excluded from sitemap
const EXCLUDED_PATTERNS = [
  /^\/admin/,
  /^\/404/,
  /\*/, // Catch-all routes
  /-single/, // Dynamic single pages
  /^\/$/, // Root redirect (handled separately)
  /:/, // Dynamic parameters like :lang, :id
];

const shouldExcludeRoute = (routePath: string): boolean => {
  return EXCLUDED_PATTERNS.some((pattern) => pattern.test(routePath));
};

// Parse React Router configuration from App.jsx
export const discoverRoutesFromReactRouter = (): DiscoveredRoute[] => {
  try {
    const clientPath = path.resolve(__dirname, "../../../client");
    const appJsxPath = path.join(clientPath, "src/App.jsx");

    if (!fs.existsSync(appJsxPath)) {
      console.warn("App.jsx not found, falling back to default routes");
      return getDefaultRoutes();
    }

    const appContent = fs.readFileSync(appJsxPath, "utf-8");

    // Extract route paths using regex
    // Matches: <Route path="something" element={...} />
    const routeRegex = /<Route\s+path=["']([^"']+)["']/g;
    const routes: DiscoveredRoute[] = [];
    let match: RegExpExecArray | null;

    while ((match = routeRegex.exec(appContent)) !== null) {
      const routePath = match[1];

      // Skip excluded routes
      if (shouldExcludeRoute(routePath)) {
        continue;
      }

      // Clean up the route path
      const cleanPath = routePath.replace(/^\/+|\/+$/g, ""); // Remove leading/trailing slashes

      // Skip if already added or if it's a dynamic route
      if (routes.some((r) => r.path === cleanPath)) {
        continue;
      }

      const routeConfig = getDefaultRouteConfig(cleanPath);

      routes.push({
        path: cleanPath,
        ...routeConfig,
      });
    }

    // Always ensure we have a home route
    if (!routes.some((r) => r.path === "")) {
      routes.unshift({
        path: "",
        priority: "1.0",
        changefreq: "weekly",
        includeInSitemap: true,
        requiresLanguagePrefix: true,
      });
    }

    console.log(
      `🔍 Discovered ${routes.length} routes from React Router:`,
      routes.map((r) => r.path || "/")
    );
    return routes;
  } catch (error) {
    console.error("Error discovering routes from React Router:", error);
    return getDefaultRoutes();
  }
};

// Fallback routes if automatic discovery fails
const getDefaultRoutes = (): DiscoveredRoute[] => {
  console.log("📋 Using fallback default routes");
  return [
    {
      path: "",
      priority: "1.0",
      changefreq: "weekly",
      includeInSitemap: true,
      requiresLanguagePrefix: true,
    },
    {
      path: "about",
      priority: "0.8",
      changefreq: "monthly",
      includeInSitemap: true,
      requiresLanguagePrefix: true,
    },
    {
      path: "services",
      priority: "0.8",
      changefreq: "weekly",
      includeInSitemap: true,
      requiresLanguagePrefix: true,
    },
    {
      path: "portfolio",
      priority: "0.8",
      changefreq: "weekly",
      includeInSitemap: true,
      requiresLanguagePrefix: true,
    },
    {
      path: "blog",
      priority: "0.9",
      changefreq: "daily",
      includeInSitemap: true,
      requiresLanguagePrefix: true,
    },
    {
      path: "contact",
      priority: "0.7",
      changefreq: "monthly",
      includeInSitemap: true,
      requiresLanguagePrefix: true,
    },
  ];
};

// Cache discovered routes to avoid reading file system on every request
let cachedRoutes: DiscoveredRoute[] | null = null;
let lastDiscoveryTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in development, longer in production

export const getDiscoveredRoutes = (
  forceRefresh = false
): DiscoveredRoute[] => {
  const now = Date.now();
  const isDevelopment = process.env.NODE_ENV === "development";
  const cacheExpired =
    now - lastDiscoveryTime >
    (isDevelopment ? CACHE_DURATION : CACHE_DURATION * 12); // 1 hour in production

  if (!cachedRoutes || cacheExpired || forceRefresh) {
    cachedRoutes = discoverRoutesFromReactRouter();
    lastDiscoveryTime = now;
  }

  return cachedRoutes;
};

// Get routes that should be included in sitemap
export const getSitemapRoutes = (): DiscoveredRoute[] => {
  return getDiscoveredRoutes().filter((route) => route.includeInSitemap);
};

// Supported languages
export const SUPPORTED_LANGUAGES = ["en", "et", "fi", "de", "sv"];
