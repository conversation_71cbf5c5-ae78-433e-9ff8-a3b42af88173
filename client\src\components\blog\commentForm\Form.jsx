import React, { useState } from "react";
import PropTypes from "prop-types";
import { blogAPI } from "@/utils/api";
import { useTranslation } from "react-i18next";

export default function Form({ blogSlug, onCommentSubmitted }) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    author: "",
    email: "",
    website: "",
    content: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ type: "", text: "" });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage({ type: "", text: "" });

    try {
      const result = await blogAPI.createComment(blogSlug, formData);

      if (result.response.ok) {
        setMessage({
          type: "success",
          text:
            t("blog.comment.success") ||
            "Comment submitted successfully! It will be reviewed before being published.",
        });
        setFormData({ author: "", email: "", website: "", content: "" });
        if (onCommentSubmitted) {
          onCommentSubmitted();
        }
      } else {
        setMessage({
          type: "error",
          text:
            result.data?.message ||
            t("blog.comment.error") ||
            "Failed to submit comment. Please try again.",
        });
      }
    } catch (error) {
      console.error("Comment submission error:", error);
      setMessage({
        type: "error",
        text:
          t("blog.comment.error") ||
          "Failed to submit comment. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="form" onSubmit={handleSubmit}>
      <div className="row mb-30 mb-md-20">
        <div className="col-md-6 mb-md-20">
          {/* Name */}
          <label htmlFor="name">{t("blog.comments.name")} *</label>
          <input
            type="text"
            name="author"
            id="name"
            className="input-lg round form-control"
            placeholder={t("blog.comments.name_placeholder")}
            maxLength={100}
            value={formData.author}
            onChange={handleChange}
            required
            aria-required="true"
          />
        </div>
        <div className="col-md-6">
          {/* Email */}
          <label htmlFor="email">{t("blog.comments.email")} *</label>
          <input
            type="email"
            name="email"
            id="email"
            className="input-lg round form-control"
            placeholder={t("blog.comments.email_placeholder")}
            maxLength={100}
            value={formData.email}
            onChange={handleChange}
            required
            aria-required="true"
          />
        </div>
      </div>
      <div className="mb-30 mb-md-20">
        {/* Website */}
        <label htmlFor="website">{t("blog.comments.website")}</label>
        <input
          type="text"
          name="website"
          id="website"
          className="input-lg round form-control"
          placeholder={t("blog.comments.website_placeholder")}
          maxLength={100}
          value={formData.website}
          onChange={handleChange}
        />
      </div>
      {/* Comment */}
      <div className="mb-30 mb-md-20">
        <label htmlFor="comment">{t("blog.comments.message")}</label>
        <textarea
          name="content"
          id="comment"
          className="input-lg round form-control"
          rows={6}
          placeholder={t("blog.comments.message_placeholder")}
          maxLength={1000}
          value={formData.content}
          onChange={handleChange}
          required
        />
      </div>
      {/* Message Display */}
      {message.text && (
        <div
          className={`alert ${
            message.type === "success" ? "alert-success" : "alert-danger"
          } mb-30`}
        >
          {message.text}
        </div>
      )}

      {/* Send Button */}
      <button
        type="submit"
        className="submit_btn link-hover-anim link-circle-1 align-middle"
        data-link-animate="y"
        disabled={isSubmitting}
      >
        <span className="link-strong link-strong-unhovered">
          {isSubmitting
            ? t("blog.comments.submitting")
            : t("blog.comments.submit")}{" "}
          <i
            className="mi-arrow-right size-18 align-middle"
            aria-hidden="true"
          ></i>
        </span>
        <span className="link-strong link-strong-hovered" aria-hidden="true">
          {isSubmitting
            ? t("blog.comments.submitting")
            : t("blog.comments.submit")}{" "}
          <i
            className="mi-arrow-right size-18 align-middle"
            aria-hidden="true"
          ></i>
        </span>
      </button>
      {/* Inform Tip */}
      <div className="form-tip form-tip-2   bg-gray-light-1 round mt-30 p-3">
        * - these fields are required. By sending the form you agree to the{" "}
        <a href="#">Terms &amp; Conditions</a> and{" "}
        <a href="#">Privacy Policy</a>.
      </div>
    </form>
  );
}

Form.propTypes = {
  blogSlug: PropTypes.string.isRequired,
  onCommentSubmitted: PropTypes.func,
};
