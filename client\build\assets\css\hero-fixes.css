/* Hero Section Height Fixes - DevSkills */
/* This file fixes the oversized hero sections and page headers */

/* Fix hero section to be exactly 100vh */
.min-height-100vh {
  min-height: 100vh !important;
  /* Override the problematic 100svh */
}

/* Fix hero container to account for padding */
.min-height-100vh.pt-100.pb-100 {
  min-height: calc(100vh - 200px) !important;
  box-sizing: border-box !important;
}

/* Ensure home section is exactly 100vh */
.home-section {
  min-height: 100vh !important;
  max-height: 100vh !important;
  height: 100vh !important;
}

/* Fix theme-elegant section padding - reduce from 160px to 120px */
.theme-elegant {
  --section-padding-y: 120px !important;
}

/* Responsive override - reduce from 140px to 100px */
@media only screen and (max-width: 1366px) {
  .theme-elegant {
    --section-padding-y: 100px !important;
  }
}

/* Mobile responsive fixes */
@media only screen and (max-width: 768px) {
  .theme-elegant {
    --section-padding-y: 80px !important;
  }
  
  .min-height-100vh.pt-100.pb-100 {
    min-height: calc(100vh - 160px) !important;
  }
}

/* Ensure page sections don't get oversized */
.page-section {
  padding-top: var(--section-padding-y) !important;
  padding-bottom: var(--section-padding-y) !important;
}

/* Fix title positioning */
.hs-title-3 {
  margin-top: 0 !important;
  font-size: 38px !important;
  font-weight: 400 !important;
  line-height: 1.3 !important;
  letter-spacing: 0.3em !important;
  text-transform: uppercase !important;
}

/* Responsive title sizing */
@media only screen and (max-width: 1366px) {
  .hs-title-3 {
    font-size: calc(1.559rem + 0.96vw) !important;
  }
}
