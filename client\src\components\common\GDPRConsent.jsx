import React from "react";
import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

const GDPRConsent = () => {
  const { t } = useTranslation();
  const [showBanner, setShowBanner] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Check if banner should be shown based on body class (set by immediate script)
    const shouldShowBanner =
      document.body.classList.contains("gdpr-banner-needed");

    if (shouldShowBanner) {
      // No consent found, show banner immediately
      setShowBanner(true);
      // Remove the body class as React is now handling it
      document.body.classList.remove("gdpr-banner-needed");
    } else {
      // Consent exists, initialize Google Analytics
      const consent = localStorage.getItem("gdpr-consent");
      if (consent) {
        initializeGoogleAnalytics(JSON.parse(consent));
      }
    }
  }, []);

  const initializeGoogleAnalytics = (consentSettings) => {
    // Defer Google Analytics initialization to avoid blocking render
    setTimeout(() => {
      if (typeof window !== "undefined" && window.gtag) {
        window.gtag("consent", "update", {
          analytics_storage: consentSettings.analytics ? "granted" : "denied",
          ad_storage: consentSettings.marketing ? "granted" : "denied",
          ad_user_data: consentSettings.marketing ? "granted" : "denied",
          ad_personalization: consentSettings.marketing ? "granted" : "denied",
        });
      }
    }, 0);
  };

  const handleAcceptAll = () => {
    const consent = {
      necessary: true,
      analytics: true,
      marketing: true,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem("gdpr-consent", JSON.stringify(consent));
    initializeGoogleAnalytics(consent);
    setShowBanner(false);

    // Track consent acceptance (deferred)
    setTimeout(() => {
      if (typeof window !== "undefined" && window.gtag) {
        window.gtag("event", "consent_granted", {
          event_category: "GDPR",
          event_label: "Accept All",
        });
      }
    }, 100);
  };

  const handleRejectAll = () => {
    const consent = {
      necessary: true,
      analytics: false,
      marketing: false,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem("gdpr-consent", JSON.stringify(consent));
    initializeGoogleAnalytics(consent);
    setShowBanner(false);

    // Track consent rejection (deferred)
    setTimeout(() => {
      if (typeof window !== "undefined" && window.gtag) {
        window.gtag("event", "consent_denied", {
          event_category: "GDPR",
          event_label: "Reject All",
        });
      }
    }, 100);
  };

  const handleCustomize = (settings) => {
    const consent = {
      necessary: true,
      analytics: settings.analytics,
      marketing: settings.marketing,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem("gdpr-consent", JSON.stringify(consent));
    initializeGoogleAnalytics(consent);
    setShowBanner(false);
    setShowDetails(false);

    // Track custom consent (deferred)
    setTimeout(() => {
      if (typeof window !== "undefined" && window.gtag) {
        window.gtag("event", "consent_customized", {
          event_category: "GDPR",
          event_label: `Analytics: ${settings.analytics}, Marketing: ${settings.marketing}`,
        });
      }
    }, 100);
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Slim Bottom Banner */}
      <div className="gdpr-banner gdpr-show">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-8 col-md-7">
              <div className="gdpr-content">
                <h6 className="gdpr-title mb-2">{t("gdpr.title")}</h6>
                <p className="gdpr-description mb-0">{t("gdpr.description")}</p>
              </div>
            </div>
            <div className="col-lg-4 col-md-5">
              <div className="gdpr-buttons">
                <button
                  className="btn btn-mod btn-small btn-w btn-circle gdpr-btn-accept"
                  onClick={handleAcceptAll}
                  data-btn-animate="y"
                >
                  <span className="btn-animate-y">
                    <span className="btn-animate-y-1">
                      {t("gdpr.acceptAll")}
                    </span>
                    <span className="btn-animate-y-2" aria-hidden="true">
                      {t("gdpr.acceptAll")}
                    </span>
                  </span>
                </button>
                <button
                  className="btn btn-mod btn-small btn-border-w btn-circle"
                  onClick={handleRejectAll}
                  data-btn-animate="y"
                >
                  <span className="btn-animate-y">
                    <span className="btn-animate-y-1">
                      {t("gdpr.rejectAll")}
                    </span>
                    <span className="btn-animate-y-2" aria-hidden="true">
                      {t("gdpr.rejectAll")}
                    </span>
                  </span>
                </button>
                <button
                  className="btn btn-mod btn-small btn-border-w btn-circle"
                  onClick={() => setShowDetails(true)}
                  data-btn-animate="y"
                >
                  <span className="btn-animate-y">
                    <span className="btn-animate-y-1">
                      {t("gdpr.customize")}
                    </span>
                    <span className="btn-animate-y-2" aria-hidden="true">
                      {t("gdpr.customize")}
                    </span>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Settings Modal */}
      {showDetails && (
        <GDPRDetailsModal
          onClose={() => setShowDetails(false)}
          onSave={handleCustomize}
          t={t}
        />
      )}
    </>
  );
};

const GDPRDetailsModal = ({ onClose, onSave, t }) => {
  const [settings, setSettings] = useState({
    analytics: false,
    marketing: false,
  });

  const handleSave = () => {
    onSave(settings);
  };

  return (
    <div className="gdpr-modal">
      <div className="gdpr-modal-content">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 offset-lg-3">
              <div className="gdpr-modal-inner">
                <div className="gdpr-modal-header mb-30">
                  <div className="d-flex justify-content-between align-items-center">
                    <h5 className="gdpr-modal-title mb-0">
                      {t("gdpr.customizeTitle")}
                    </h5>
                    <button className="gdpr-close" onClick={onClose}>
                      <i className="mi-close size-18"></i>
                    </button>
                  </div>
                </div>

                <div className="gdpr-modal-body">
                  <div className="gdpr-category mb-25">
                    <div className="gdpr-category-header mb-10">
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="gdpr-category-title mb-0 mr-2">
                          {t("gdpr.necessary")}
                        </h6>
                        <span className="gdpr-required-badge">
                          {t("gdpr.required")}
                        </span>
                      </div>
                    </div>
                    <p className="gdpr-category-desc">
                      {t("gdpr.necessaryDesc")}
                    </p>
                  </div>

                  <div className="gdpr-category mb-25">
                    <div className="gdpr-category-header mb-10">
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="gdpr-category-title mb-0">
                          {t("gdpr.analytics")}
                        </h6>
                        <label className="gdpr-switch">
                          <input
                            type="checkbox"
                            checked={settings.analytics}
                            onChange={(e) =>
                              setSettings((prev) => ({
                                ...prev,
                                analytics: e.target.checked,
                              }))
                            }
                          />
                          <span className="gdpr-slider"></span>
                        </label>
                      </div>
                    </div>
                    <p className="gdpr-category-desc">
                      {t("gdpr.analyticsDesc")}
                    </p>
                  </div>

                  <div className="gdpr-category mb-30">
                    <div className="gdpr-category-header mb-10">
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="gdpr-category-title mb-0">
                          {t("gdpr.marketing")}
                        </h6>
                        <label className="gdpr-switch">
                          <input
                            type="checkbox"
                            checked={settings.marketing}
                            onChange={(e) =>
                              setSettings((prev) => ({
                                ...prev,
                                marketing: e.target.checked,
                              }))
                            }
                          />
                          <span className="gdpr-slider"></span>
                        </label>
                      </div>
                    </div>
                    <p className="gdpr-category-desc">
                      {t("gdpr.marketingDesc")}
                    </p>
                  </div>
                </div>

                <div className="gdpr-modal-footer">
                  <div className="d-flex justify-content-end gap-2">
                    <button
                      className="btn btn-mod btn-small btn-border-w btn-circle"
                      onClick={onClose}
                      data-btn-animate="y"
                    >
                      <span className="btn-animate-y">
                        <span className="btn-animate-y-1">
                          {t("gdpr.cancel")}
                        </span>
                        <span className="btn-animate-y-2" aria-hidden="true">
                          {t("gdpr.cancel")}
                        </span>
                      </span>
                    </button>
                    <button
                      className="btn btn-mod btn-small btn-w btn-circle"
                      onClick={handleSave}
                      data-btn-animate="y"
                    >
                      <span className="btn-animate-y">
                        <span className="btn-animate-y-1">
                          {t("gdpr.savePreferences")}
                        </span>
                        <span className="btn-animate-y-2" aria-hidden="true">
                          {t("gdpr.savePreferences")}
                        </span>
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

GDPRDetailsModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
};

export default GDPRConsent;
