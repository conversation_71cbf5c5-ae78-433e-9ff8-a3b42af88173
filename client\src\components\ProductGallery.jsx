import { useState } from "react";

const ProductGallery = ({ images, productTitle }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  if (!images || images.length === 0) {
    return null;
  }

  const currentImage = images[currentImageIndex];
  const hasMultipleImages = images.length > 1;

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const goToImage = (index) => {
    setCurrentImageIndex(index);
  };

  const openFullscreen = () => {
    setIsFullscreen(true);
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
  };

  const getImageUrl = (filename) => {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4004';
    return `${baseUrl}/uploads/product-images/${filename}`;
  };

  return (
    <>
      {/* Main Gallery */}
      <div className="product-gallery mb-60 mb-xs-40">
        <div className="position-relative">
          <img
            src={getImageUrl(currentImage.filename)}
            alt={currentImage.alt || productTitle}
            className="w-100 rounded cursor-pointer"
            style={{ maxHeight: "500px", objectFit: "cover" }}
            onClick={openFullscreen}
          />
          
          {/* Navigation arrows - only show if multiple images */}
          {hasMultipleImages && (
            <>
              <button
                className="btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3"
                onClick={prevImage}
                style={{ zIndex: 2 }}
              >
                <i className="mi-arrow-left" />
              </button>
              <button
                className="btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3"
                onClick={nextImage}
                style={{ zIndex: 2 }}
              >
                <i className="mi-arrow-right" />
              </button>
            </>
          )}
        </div>

        {/* Thumbnails - only show if multiple images */}
        {hasMultipleImages && (
          <div className="gallery-thumbnails mt-3">
            <div className="d-flex gap-2 flex-wrap">
              {images.map((image, index) => (
                <div
                  key={image.id}
                  className={`thumbnail-item cursor-pointer ${
                    index === currentImageIndex ? 'active' : ''
                  }`}
                  onClick={() => goToImage(index)}
                >
                  <img
                    src={getImageUrl(image.filename)}
                    alt={image.alt || `${productTitle} ${index + 1}`}
                    className="rounded"
                    style={{
                      width: "80px",
                      height: "60px",
                      objectFit: "cover",
                      border: index === currentImageIndex ? "2px solid #007bff" : "2px solid transparent",
                      opacity: index === currentImageIndex ? 1 : 0.7
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div 
          className="fullscreen-gallery position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
          style={{ 
            backgroundColor: "rgba(0, 0, 0, 0.95)", 
            zIndex: 9999 
          }}
          onClick={closeFullscreen}
        >
          <div className="position-relative" onClick={(e) => e.stopPropagation()}>
            <img
              src={getImageUrl(currentImage.filename)}
              alt={currentImage.alt || productTitle}
              className="img-fluid"
              style={{ maxHeight: "90vh", maxWidth: "90vw" }}
            />
            
            {/* Close button */}
            <button
              className="btn btn-light btn-lg position-absolute top-0 end-0 m-3"
              onClick={closeFullscreen}
              style={{ zIndex: 10000 }}
            >
              ×
            </button>

            {/* Navigation arrows in fullscreen - only show if multiple images */}
            {hasMultipleImages && (
              <>
                <button
                  className="btn btn-light btn-lg position-absolute top-50 start-0 translate-middle-y ms-3"
                  onClick={prevImage}
                  style={{ zIndex: 10000 }}
                >
                  <i className="mi-arrow-left" />
                </button>
                <button
                  className="btn btn-light btn-lg position-absolute top-50 end-0 translate-middle-y me-3"
                  onClick={nextImage}
                  style={{ zIndex: 10000 }}
                >
                  <i className="mi-arrow-right" />
                </button>
              </>
            )}
          </div>
        </div>
      )}

      <style jsx>{`
        .cursor-pointer {
          cursor: pointer;
        }
        .thumbnail-item:hover img {
          opacity: 1 !important;
        }
        .fullscreen-gallery {
          backdrop-filter: blur(5px);
        }
      `}</style>
    </>
  );
};

export default ProductGallery;
