{"version": 3, "mappings": ";2yCAKA,IAAIA,EAAuB,GAQpB,SAASC,EAAQC,EAAMC,EAAK,KAAM,CACvC,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CAEtC,GAAIF,GAAM,SAAS,eAAeA,CAAE,EAAG,CACrCC,EAAS,EACT,MACN,CAEI,MAAME,EAAO,SAAS,cAAc,MAAM,EAC1CA,EAAK,IAAM,aACXA,EAAK,KAAOJ,EACRC,IAAIG,EAAK,GAAKH,GAElBG,EAAK,OAAS,IAAMF,EAAS,EAC7BE,EAAK,QAAU,IAAMD,EAAO,IAAI,MAAM,uBAAuBH,CAAI,EAAE,CAAC,EAEpE,SAAS,KAAK,YAAYI,CAAI,CAClC,CAAG,CACH,CAKO,SAASC,GAAqB,CACnC,GAAIP,EAAsB,OAAO,QAAQ,QAAS,EAElDA,EAAuB,GAkBvB,MAAMQ,EAfW,CACf,gCACA,wBACA,sCACA,iCACA,+BACA,4BACA,2BACA,sCACA,4CACA,yBACA,kCACD,EAG6B,IAAI,CAACN,EAAMO,IACvCR,EAAQC,EAAM,gBAAgBO,CAAK,EAAE,CACtC,EAED,OAAO,QAAQ,IAAID,CAAY,EAC5B,KAAK,KACJ,QAAQ,IAAI,yBAAyB,EAE/BE,EAAA,IAAC,mBAAmC,uBAC3C,EACA,MAAOC,GAAU,CAChB,QAAQ,MAAM,mCAAoCA,CAAK,CAC7D,CAAK,CACL,CAKO,SAASC,GAAiB,CAE3B,SAAS,aAAe,UAC1B,SAAS,iBAAiB,mBAAoB,IAAM,CAElD,WAAWL,EAAoB,GAAG,CACxC,CAAK,EAGD,WAAWA,EAAoB,GAAG,EAIpC,OAAO,iBAAiB,OAAQA,CAAkB,CACpD,CCvFO,MAAMM,EAAwB,IAAM,CACzC,SAAS,iBAAiB,2BAA2B,EAAE,QAASC,GAAU,CACxEA,EAAM,iBAAiB,YAAa,SAAUC,EAAG,CAC/C,MAAMC,EAAI,OAAO,WACXC,EAAI,OAAO,YACXC,EAAU,IAAOH,EAAE,MAAQ,KAAK,YAAcC,EAC9CG,EAAU,IAAOJ,EAAE,MAAQ,KAAK,WAAaE,EAEnD,KAAK,iBAAiB,qBAAqB,EAAE,QAASG,GAAO,CAC3D,MAAMC,EAAS,SAASD,EAAG,aAAa,aAAa,CAAC,EAChDE,EAAY,eAAe,KAAK,MACpCJ,EAAUG,CACpB,CAAS,OAAO,KAAK,MAAMF,EAAUE,CAAM,CAAC,WACpCD,EAAG,MAAM,UAAYE,CAC7B,CAAO,EAED,IAAIC,EAAeR,EAAE,MAAQ,KAAK,WACfA,EAAE,MAAQ,KAAK,UAElC,KAAK,iBAAiB,4BAA4B,EAAE,QAASK,GAAO,CAClEA,EAAG,MAAM,KAAO,GAAGG,CAAY,KAC/BH,EAAG,MAAM,IAAM,MACvB,CAAO,CAKP,CAAK,EAEDN,EAAM,iBAAiB,aAAc,SAAUC,EAAG,CAChD,KAAK,iBAAiB,4BAA4B,EAAE,QAASK,GAAO,CAClE,WAAW,IAAM,CACfA,EAAG,MAAM,WAAa,iCACtBA,EAAG,MAAM,WAAa,WACvB,EAAE,EAAE,CACb,CAAO,CACP,CAAK,EAEDN,EAAM,iBAAiB,WAAY,SAAUC,EAAG,CAC9C,KAAK,iBAAiB,4BAA4B,EAAE,QAASK,GAAO,CAClEA,EAAG,MAAM,WAAa,MAC9B,CAAO,CACP,CAAK,CACL,CAAG,CACH,EAEO,SAASI,GAAiB,CAG/B,GAAI,SAAS,iBAAiB,iBAAiB,EAAE,QAC3C,OAAO,YAAc,KAAqB,CAM5C,IAASC,EAAT,UAA6B,CAC3B,SAAS,iBAAiB,iBAAiB,EAAE,QAASC,GAAY,CAE9DA,EAAQ,sBAAqB,EAAG,IAAM,OAAO,aAC7CA,EAAQ,sBAAuB,EAAC,OAAS,EAEpCA,EAAQ,UAAU,SAAS,gBAAgB,IAC9CA,EAAQ,UAAU,IAAI,gBAAgB,EACtCC,EAAS,QAAS,GAGhBD,EAAQ,UAAU,SAAS,gBAAgB,GAC7CA,EAAQ,UAAU,OAAO,gBAAgB,CAGvD,CAAS,CACT,EArBM,MAAMC,EAAW,IAAIC,EAAO,kBAAmB,CAC7C,SAAU,GACV,WAAY,EACpB,CAAO,EAoBD,OAAO,iBAAiB,SAAUH,CAAiB,CAGzD,CAGE,GAAI,SAAS,iBAAiB,iBAAiB,EAAE,QAC3C,OAAO,YAAc,KAAqB,CAI5C,IAASI,EAAT,UAAwB,CACtB,SAAS,iBAAiB,iBAAiB,EAAE,QAASH,GAAY,CAE9DA,EAAQ,sBAAqB,EAAG,IAAM,OAAO,aAC7CA,EAAQ,sBAAuB,EAAC,OAAS,EAEpCA,EAAQ,UAAU,SAAS,gBAAgB,IAC9CA,EAAQ,UAAU,IAAI,gBAAgB,EACtCI,EAAS,QAAS,GAGhBJ,EAAQ,UAAU,SAAS,gBAAgB,GAC7CA,EAAQ,UAAU,OAAO,gBAAgB,CAGvD,CAAS,CACT,EAnBM,MAAMI,EAAW,IAAIF,EAAO,kBAAmB,CAC7C,WAAY,EACpB,CAAO,EAkBD,OAAO,iBAAiB,SAAUC,CAAY,CAEpD,CAEA,CC3GO,SAASE,GAAW,CAEzB,WAAW,IAAM,CACf,GAAI,CAEE,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACnD,SACG,iBAAiB,MAAM,EACvB,QAASX,GAAOA,EAAG,UAAU,IAAI,YAAY,CAAC,EAGnD,IAAIY,EAAM,IAAIC,EAAI,CAChB,SAAU,MACV,aAAc,WACd,OAAQ,IACR,OAAQ,GACR,KAAM,GACN,SAAU,SAAUC,EAAK,CAEvBA,EAAI,UAAU,IAAI,UAAU,EAC5BA,EAAI,MAAM,QAAU,IACpBA,EAAI,MAAM,WAAa,SACxB,CACT,CAAO,EAGI,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACpD,SAAS,KAAK,UAAU,IAAI,gBAAgB,EAI9CF,EAAI,KAAM,EAGV,WAAW,IAAM,CACf,SAAS,iBAAiB,MAAM,EAAE,QAASZ,GAAO,CAC3CA,EAAG,UAAU,SAAS,UAAU,IACnCA,EAAG,MAAM,QAAU,IACnBA,EAAG,MAAM,WAAa,UACtBA,EAAG,UAAU,IAAI,UAAU,EAEvC,CAAS,CACF,EAAE,GAAI,EAGH,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACnD,SACG,iBAAiB,QAAQ,EACzB,QAASA,GAAOA,EAAG,UAAU,IAAI,YAAY,CAAC,EAEnD,IAAIe,EAAQ,IAAIF,EAAI,CAClB,SAAU,QACV,aAAc,WACd,OAAQ,IACR,OAAQ,GACR,KAAM,GACN,SAAU,SAAUC,EAAK,CACvBA,EAAI,UAAU,IAAI,UAAU,EAC5BA,EAAI,MAAM,QAAU,IACpBA,EAAI,MAAM,WAAa,SACxB,CACT,CAAO,EAEG,SAAS,KAAK,UAAU,SAAS,gBAAgB,EACnDC,EAAM,KAAM,EAEZ,SACG,iBAAiB,QAAQ,EACzB,QAASf,GAAQA,EAAG,MAAM,QAAU,GAAI,EAK3C,SAAS,KAAK,UAAU,SAAS,gBAAgB,GACjD,OAAO,YAAc,MACrB,SAAS,gBAAgB,UAAU,SAAS,WAAW,EAEvD,SAAS,iBAAiB,cAAc,EAAE,QAASA,GAAO,CACxDA,EAAG,UAAU,IAAI,aAAc,aAAc,UAAU,EACvD,YAAY,IAAM,CAChBA,EAAG,UAAU,OAAO,YAAY,CACjC,EAAE,IAAI,CACjB,CAAS,EAED,SACG,iBAAiB,cAAc,EAC/B,QAASA,GAAQA,EAAG,MAAM,QAAU,GAAI,CAE9C,OAAQT,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,EAEjD,SAAS,iBAAiB,4BAA4B,EAAE,QAASS,GAAO,CACtEA,EAAG,MAAM,QAAU,IACnBA,EAAG,UAAU,IAAI,UAAU,CACnC,CAAO,CACP,CACG,EAAE,GAAG,CACR,CClGO,MAAMgB,EAAuB,IAAM,CACxC,IAAIC,EAAU,SAAS,cAAc,WAAW,EAC5CC,EAAkB,SAAS,cAAc,sBAAsB,EAC/DC,EAAmB,SAAS,cAAc,qBAAqB,EAGnE,GAAI,CAACF,EACH,OAIF,MAAMG,EACJH,EAAQ,UAAU,SAAS,WAAW,GACtC,OAAO,SAAS,WAAa,KAC7B,OAAO,SAAS,SAAS,MAAM,iBAAiB,EAE9C,OAAO,QAAU,GACnBA,EAAQ,UAAU,OAAO,aAAa,EACtCA,EAAQ,UAAU,IAAI,eAAgB,eAAe,EACjDC,GAAiBA,EAAgB,UAAU,IAAI,cAAc,EAG7DC,GAAoB,CAACC,GACvBD,EAAiB,UAAU,OAAO,MAAM,EAItCC,GAAkB,CAACH,EAAQ,UAAU,SAAS,MAAM,GACtDA,EAAQ,UAAU,IAAI,MAAM,GAErB,OAAO,UAAY,IAC5BA,EAAQ,UAAU,IAAI,aAAa,EACnCA,EAAQ,UAAU,OAAO,eAAgB,eAAe,EACpDC,GAAiBA,EAAgB,UAAU,OAAO,cAAc,EAGhEC,GAAoB,CAACC,GACvBD,EAAiB,UAAU,IAAI,MAAM,EAInCC,GAAkB,CAACH,EAAQ,UAAU,SAAS,MAAM,GACtDA,EAAQ,UAAU,IAAI,MAAM,EAGlC,ECnBMI,EAAuBC,OAAK,IAAMhC,EAAA,WAAO,4BAAoB,OAAAiC,KAAA,GAAC,4DAC9DC,EAA0BF,OAAK,IAAMhC,EAAA,WAAO,4BAAuB,OAAAiC,KAAA,GAAC,4DACpEE,EAA0BH,OAAK,IAAMhC,EAAA,WAAO,2BAAuB,OAAAiC,KAAA,GAAC,uDACpEG,EAA2BJ,OAAK,IAAMhC,EAAA,WAAO,2BAAwB,OAAAiC,KAAA,GAAC,uDACtEI,EAAsBL,OAAK,IAAMhC,EAAA,WAAO,2BAAoB,OAAAiC,KAAA,GAAC,uDAC7DK,EAAiCN,EAAA,KAAK,IAAAhC,EAAA,IAC1C,OAAO,2BAA+B,OAAAiC,KAAA,wDACxC,EACMM,EAAgCP,EAAA,KAAK,IAAAhC,EAAA,IACzC,OAAO,2BAA8B,OAAAiC,KAAA,wDACvC,EACMO,EAA4BR,EAAA,KAAK,IAAAhC,EAAA,IACrC,OAAO,2BAA0B,OAAAiC,KAAA,wDACnC,EACMQ,EAAyBT,OAAK,IAAMhC,EAAA,WAAO,4BAAsB,OAAAiC,KAAA,GAAC,4DAClES,EAAoBV,OAAK,IAAMhC,EAAA,WAAO,2BAA6B,OAAAiC,KAAA,GAAC,uDACpEU,EAAsBX,OAAK,IAAMhC,EAAA,WAAO,2BAA+B,OAAAiC,KAAA,GAAC,uDACxEW,EAAmBZ,OAAK,IAAMhC,EAAA,WAAO,2BAAyB,OAAAiC,KAAA,GAAC,uDAI/DY,EAAcb,OAAK,UAAM,OAAO,4BAAsB,OAAAC,KAAA,GAAC,oCAGvDa,EAAa,IACjBC,EAAA,KAAC,OACC,UAAU,cACV,MAAO,CACL,SAAU,QACV,IAAK,EACL,KAAM,EACN,MAAO,OACP,OAAQ,OACR,gBAAiB,UACjB,QAAS,OACT,eAAgB,SAChB,WAAY,SACZ,OAAQ,IACV,EAEA,UAAAC,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,OAAQ,iBACR,UAAW,iBACX,aAAc,MACd,UAAW,0BACb,CACD,QACA,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,KAKN,IACJ,EAGF,SAASC,GAAM,CACP,MAAE,SAAAC,CAAS,EAAIC,EAAY,EACjCC,mBAAU,IAAM,CAEClD,EAAA,EAENmB,EAAA,EACalB,EAAA,EAGtB,MAAMkD,EAAmB,IAAM,OACzB,IAAA1B,EAAU,SAAS,cAAc,WAAW,EAChD,GAAI,CAACA,EAAS,CAEZ,WAAW0B,EAAkB,GAAG,EAChC,OAIF1B,EAAQ,MAAM,QAAU,GAEpBA,GAAA,MAAAA,EAAS,UAAU,SAAS,eACtBA,EAAA,UAAU,IAAI,gBAAgB,GAC5B2B,EAAA3B,GAAA,YAAAA,EAAS,YAAT,MAAA2B,EAAoB,SAAS,SAC9B3B,GAAA,MAAAA,EAAA,UAAU,IAAI,2BAIJD,EAAA,CACvB,EAGiB,OAAA2B,EAAA,EACjB,WAAWA,EAAkB,EAAE,EAExB,wBAAiB,SAAU3B,CAAoB,EACvCZ,EAAA,EAGf,WAAW,IAAM,CACT,MAAAyC,EAAY,SAAS,OAAS,YAC9BC,EAAe,OAAO,SAAS,KACrCC,EAAcF,EAAWC,CAAY,GACpC,GAAG,EAEC,IAAM,CACJ,2BAAoB,SAAU9B,CAAoB,CAC3D,GACC,CAACwB,CAAQ,CAAC,EAGbE,YAAU,IAAM,EACoB,IAAM,CAChC,MAAAzB,EAAU,SAAS,cAAc,WAAW,EAC9CA,IAEFA,EAAQ,MAAM,QAAU,GACxBA,EAAQ,MAAM,WAAa,UAC3BA,EAAQ,MAAM,QAAU,IAGxB,WAAW,IAAM,CACMD,EAAA,GACpB,GAAG,EAEV,GAE0B,GACzB,CAACwB,CAAQ,CAAC,EACbE,YAAU,IAAM,CACV,OAAO,OAAW,KAEbpD,EAAA,oCAAiC,EAAE,kCAAK,IAAM,CAEnD,QAAQ,IAAI,kBAAkB,EAC/B,CAEL,EAAG,EAAE,EAGD+C,EAAA,KAAAW,WAAA,WAAAV,MAACW,YAAS,SAAUX,EAAA,IAACF,EAAW,IAC9B,gBAACc,EAEC,WAACb,OAAAc,EAAA,CAAM,KAAK,SACV,UAAAb,MAACa,GAAM,MAAK,GAAC,QAASb,MAACc,GAA2B,GAAI,QACrDD,EAAM,MAAK,QAAQ,QAASb,MAACjB,GAAqB,GAAI,QAEtD8B,EAAM,MAAK,WAAW,QAASb,MAACd,GAAwB,GAAI,QAC5D2B,EAAM,MAAK,YAAY,QAASb,MAACZ,GAAyB,GAAI,EAC/DY,EAAA,IAACa,EAAA,CACC,KAAK,uBACL,cAAUvB,EAA+B,IAC3C,QACCuB,EAAM,MAAK,WAAW,QAASb,MAACb,GAAwB,GAAI,EAC7Da,EAAA,IAACa,EAAA,CACC,KAAK,sBACL,cAAUtB,EAA8B,IAC1C,QACCsB,EAAM,MAAK,OAAO,QAASb,MAACX,GAAoB,GAAI,EACrDW,EAAA,IAACa,EAAA,CACC,KAAK,kBACL,cAAUrB,EAA0B,IACtC,QACCqB,EAAM,MAAK,UAAU,QAASb,MAACP,GAAuB,GAAI,QAC1DoB,EAAM,MAAK,iBAAiB,QAASb,MAACN,GAAkB,GAAI,QAC5DmB,EAAM,MAAK,mBAAmB,QAASb,EAAA,IAACL,IAAoB,CAAI,IACnE,QAGCkB,EAAM,MAAK,WAAW,QAASb,MAACH,GAAY,GAAI,QAGhDgB,EAAM,MAAK,IAAI,QAASb,MAACe,GAAiB,GAAI,QAG9CF,EAAM,MAAK,SAAS,QAASb,MAACe,GAAiB,GAAI,QACnDF,EAAM,MAAK,YAAY,QAASb,MAACe,GAAiB,GAAI,QACtDF,EAAM,MAAK,aAAa,QAASb,MAACe,GAAiB,GAAI,QACvDF,EAAM,MAAK,sBAAsB,QAASb,MAACe,GAAiB,GAAI,QAChEF,EAAM,MAAK,QAAQ,QAASb,MAACe,GAAiB,GAAI,QAClDF,EAAM,MAAK,iBAAiB,QAASb,MAACe,GAAiB,GAAI,QAC3DF,EAAM,MAAK,WAAW,QAASb,MAACe,GAAiB,GAAI,QACrDF,EAAM,MAAK,kBAAkB,QAASb,MAACe,GAAiB,GAAI,QAC5DF,EAAM,MAAK,oBAAoB,QAASb,MAACe,GAAiB,GAAI,QAG9DF,EAAM,MAAK,IAAI,QAASb,EAAA,IAACJ,IAAiB,CAAI,IACjD,CACF,SACCoB,EAAmB,UACnBC,EAAY,KAGf,CAEJ,CClNA,MAAMC,EAAc,SAAS,eAAe,MAAM,EAG5CC,QACHC,EAAM,WAAN,CACC,SAACpB,MAAAqB,EAAA,CACC,eAACC,EACC,UAAAtB,EAAA,IAACuB,EAAA,CACC,OAAQ,CACN,mBAAoB,GACpB,qBAAsB,EACxB,EAEA,eAACtB,EAAI,IACP,CACF,EACF,GACF,EAIIuB,EAAiBN,GAAeA,EAAY,cAAc,EAGhE,GAAI,CACEM,EAEFC,EAAA,YAAYP,EAAaC,CAAgB,EAG5BO,aAAWR,CAAW,EAC9B,OAAOC,CAAgB,CAEhC,OAASlE,EAAO,CACN,cAAM,uBAAwBA,CAAK,EAE9ByE,aAAWR,CAAW,EAC9B,OAAOC,CAAgB,CAC9B", "names": ["nonCriticalCSSLoaded", "loadCSS", "href", "id", "resolve", "reject", "link", "loadNonCriticalCSS", "loadPromises", "index", "__vitePreload", "error", "initCSSLoading", "parallaxMouseMovement", "scene", "e", "w", "h", "offsetX", "offsetY", "el", "offset", "translate", "sceneOffsetX", "parallaxScroll", "addScrollParallax", "element", "rellax_y", "Rellax", "addParallaxX", "rellax_x", "init_wow", "wow", "WOW", "box", "wow_p", "headerChangeOnScroll", "mainNav", "navLogoWrapLogo", "lightAfterScroll", "shouldStayDark", "ElegantAboutPageDark", "lazy", "n", "ElegantServicesPageDark", "ElegantWebstorePageDark", "ElegantPortfolioPageDark", "ElegantBlogPageDark", "ElegantPortfolioSinglePageDark", "ElegantWebstoreSinglePageDark", "ElegantBlogSinglePageDark", "ElegantContactPageDark", "PrivacyPolicyPage", "TermsConditionsPage", "MainPageNotFound", "AdminRoutes", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "jsx", "App", "pathname", "useLocation", "useEffect", "initializeNavbar", "_a", "pageTitle", "pageLocation", "trackPageView", "Fragment", "Suspense", "Routes", "Route", "Home5MainDemoMultiPageDark", "LanguageRedirect", "ScrollTopBehaviour", "GDPRConsent", "rootElement", "AppWithProviders", "React", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>ed", "hydrateRoot", "createRoot"], "ignoreList": [], "sources": ["../../src/utils/loadCSS.js", "../../src/utils/parallax.js", "../../src/utils/initWowjs.js", "../../src/utils/changeHeaderOnScroll.js", "../../src/App.jsx", "../../src/main.jsx"], "sourcesContent": ["/**\n * CSS Loading Utility\n * Loads non-critical CSS after the critical rendering path\n */\n\nlet nonCriticalCSSLoaded = false;\n\n/**\n * Load CSS file asynchronously\n * @param {string} href - CSS file path\n * @param {string} id - Unique ID for the link element\n * @returns {Promise} - Resolves when CSS is loaded\n */\nexport function loadCSS(href, id = null) {\n  return new Promise((resolve, reject) => {\n    // Check if already loaded\n    if (id && document.getElementById(id)) {\n      resolve();\n      return;\n    }\n\n    const link = document.createElement(\"link\");\n    link.rel = \"stylesheet\";\n    link.href = href;\n    if (id) link.id = id;\n\n    link.onload = () => resolve();\n    link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));\n\n    document.head.appendChild(link);\n  });\n}\n\n/**\n * Load non-critical CSS after first paint\n */\nexport function loadNonCriticalCSS() {\n  if (nonCriticalCSSLoaded) return Promise.resolve();\n\n  nonCriticalCSSLoaded = true;\n\n  // Load CSS files directly from public directory to avoid bundling\n  const cssFiles = [\n    \"/assets/css/bootstrap.min.css\",\n    \"/assets/css/style.css\",\n    \"/assets/css/vertical-rhythm.min.css\",\n    \"/assets/css/magnific-popup.css\",\n    \"/assets/css/owl.carousel.css\",\n    \"/assets/css/splitting.css\",\n    \"/assets/css/YTPlayer.css\",\n    \"/assets/css/demo-main/demo-main.css\",\n    \"/assets/css/demo-elegant/demo-elegant.css\",\n    \"/assets/css/custom.css\",\n    \"/assets/css/style-responsive.css\",\n  ];\n\n  // Load all CSS files in parallel\n  const loadPromises = cssFiles.map((href, index) =>\n    loadCSS(href, `non-critical-${index}`)\n  );\n\n  return Promise.all(loadPromises)\n    .then(() => {\n      console.log(\"Non-critical CSS loaded\");\n      // Also load the custom styles\n      return import(\"../styles/non-critical.css\");\n    })\n    .catch((error) => {\n      console.error(\"Failed to load non-critical CSS:\", error);\n    });\n}\n\n/**\n * Initialize CSS loading strategy\n */\nexport function initCSSLoading() {\n  // Load non-critical CSS after DOM is ready and critical content is painted\n  if (document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      // Small delay to ensure critical content is painted first\n      setTimeout(loadNonCriticalCSS, 100);\n    });\n  } else {\n    // DOM is already ready\n    setTimeout(loadNonCriticalCSS, 100);\n  }\n\n  // Also load on window load as fallback\n  window.addEventListener(\"load\", loadNonCriticalCSS);\n}\n\n/**\n * Preload CSS for better performance\n * @param {string} href - CSS file path\n */\nexport function preloadCSS(href) {\n  const link = document.createElement(\"link\");\n  link.rel = \"preload\";\n  link.as = \"style\";\n  link.href = href;\n  link.onload = function () {\n    this.onload = null;\n    this.rel = \"stylesheet\";\n  };\n  document.head.appendChild(link);\n\n  // Fallback for browsers that don't support preload\n  const noscript = document.createElement(\"noscript\");\n  const fallbackLink = document.createElement(\"link\");\n  fallbackLink.rel = \"stylesheet\";\n  fallbackLink.href = href;\n  noscript.appendChild(fallbackLink);\n  document.head.appendChild(noscript);\n}\n", "import Rellax from \"rellax\";\n\nexport const parallaxMouseMovement = () => {\n  document.querySelectorAll(\".parallax-mousemove-scene\").forEach((scene) => {\n    scene.addEventListener(\"mousemove\", function (e) {\n      const w = window.innerWidth;\n      const h = window.innerHeight;\n      const offsetX = 0.5 - (e.pageX - this.offsetLeft) / w;\n      const offsetY = 0.5 - (e.pageY - this.offsetTop) / h;\n\n      this.querySelectorAll(\".parallax-mousemove\").forEach((el) => {\n        const offset = parseInt(el.getAttribute(\"data-offset\"));\n        const translate = `translate3d(${Math.round(\n          offsetX * offset\n        )}px, ${Math.round(offsetY * offset)}px, 0px)`;\n        el.style.transform = translate;\n      });\n\n      let sceneOffsetX = e.pageX - this.offsetLeft;\n      let sceneOffsetY = e.pageY - this.offsetTop;\n\n      this.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n        el.style.left = `${sceneOffsetX}px`;\n        el.style.top = `${31}px`;\n      });\n\n      // document.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n      //   el.style.left = `${sceneOffsetX}px`;\n      // });\n    });\n\n    scene.addEventListener(\"mouseenter\", function (e) {\n      this.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n        setTimeout(() => {\n          el.style.transition = \"all .27s var(--ease-out-short)\";\n          el.style.willChange = \"transform\";\n        }, 27);\n      });\n    });\n\n    scene.addEventListener(\"mouseout\", function (e) {\n      this.querySelectorAll(\".parallax-mousemove-follow\").forEach((el) => {\n        el.style.transition = \"none\";\n      });\n    });\n  });\n};\n\nexport function parallaxScroll() {\n  const mobileTest = false; // Assuming mobileTest is defined elsewhere\n\n  if (document.querySelectorAll(\"[data-rellax-y]\").length) {\n    if (window.innerWidth >= 1280 && !mobileTest) {\n      const rellax_y = new Rellax(\"[data-rellax-y]\", {\n        vertical: true,\n        horizontal: false,\n      });\n\n      function addScrollParallax() {\n        document.querySelectorAll(\"[data-rellax-y]\").forEach((element) => {\n          if (\n            element.getBoundingClientRect().top < window.innerHeight &&\n            element.getBoundingClientRect().bottom > 0\n          ) {\n            if (!element.classList.contains(\"js-in-viewport\")) {\n              element.classList.add(\"js-in-viewport\");\n              rellax_y.refresh();\n            }\n          } else {\n            if (element.classList.contains(\"js-in-viewport\")) {\n              element.classList.remove(\"js-in-viewport\");\n            }\n          }\n        });\n      }\n\n      window.addEventListener(\"scroll\", addScrollParallax);\n      // window.removeEventListener(\"scroll\", addScrollParallax);\n      // rellax_y.destroy();\n    }\n  }\n\n  if (document.querySelectorAll(\"[data-rellax-x]\").length) {\n    if (window.innerWidth >= 1280 && !mobileTest) {\n      const rellax_x = new Rellax(\"[data-rellax-x]\", {\n        horizontal: true,\n      });\n      function addParallaxX() {\n        document.querySelectorAll(\"[data-rellax-x]\").forEach((element) => {\n          if (\n            element.getBoundingClientRect().top < window.innerHeight &&\n            element.getBoundingClientRect().bottom > 0\n          ) {\n            if (!element.classList.contains(\"js-in-viewport\")) {\n              element.classList.add(\"js-in-viewport\");\n              rellax_x.refresh();\n            }\n          } else {\n            if (element.classList.contains(\"js-in-viewport\")) {\n              element.classList.remove(\"js-in-viewport\");\n            }\n          }\n        });\n      }\n      window.addEventListener(\"scroll\", addParallaxX);\n      // window.removeEventListener(\"scroll\", addParallaxX);\n    }\n  }\n}\n", "import WOW from \"wow.js\";\nexport function init_wow() {\n  // Add error handling and better timing\n  setTimeout(() => {\n    try {\n      /* Wow init */\n      if (document.body.classList.contains(\"appear-animate\")) {\n        document\n          .querySelectorAll(\".wow\")\n          .forEach((el) => el.classList.add(\"no-animate\"));\n      }\n\n      var wow = new WOW({\n        boxClass: \"wow\",\n        animateClass: \"animated\",\n        offset: 100,\n        mobile: true,\n        live: false, // Disable live detection to prevent issues\n        callback: function (box) {\n          // Ensure element stays visible after animation\n          box.classList.add(\"animated\");\n          box.style.opacity = \"1\";\n          box.style.visibility = \"visible\";\n        },\n      });\n\n      // Always ensure appear-animate class exists\n      if (!document.body.classList.contains(\"appear-animate\")) {\n        document.body.classList.add(\"appear-animate\");\n      }\n\n      // Initialize WOW\n      wow.init();\n\n      // Fallback: ensure all wow elements are visible\n      setTimeout(() => {\n        document.querySelectorAll(\".wow\").forEach((el) => {\n          if (!el.classList.contains(\"animated\")) {\n            el.style.opacity = \"1\";\n            el.style.visibility = \"visible\";\n            el.classList.add(\"animated\");\n          }\n        });\n      }, 2000);\n\n      /* Wow for portfolio init */\n      if (document.body.classList.contains(\"appear-animate\")) {\n        document\n          .querySelectorAll(\".wow-p\")\n          .forEach((el) => el.classList.add(\"no-animate\"));\n      }\n      var wow_p = new WOW({\n        boxClass: \"wow-p\",\n        animateClass: \"animated\",\n        offset: 100,\n        mobile: true,\n        live: false,\n        callback: function (box) {\n          box.classList.add(\"animated\");\n          box.style.opacity = \"1\";\n          box.style.visibility = \"visible\";\n        },\n      });\n\n      if (document.body.classList.contains(\"appear-animate\")) {\n        wow_p.init();\n      } else {\n        document\n          .querySelectorAll(\".wow-p\")\n          .forEach((el) => (el.style.opacity = \"1\"));\n      }\n\n      /* Wow for menu bar init */\n      if (\n        document.body.classList.contains(\"appear-animate\") &&\n        window.innerWidth >= 1024 &&\n        document.documentElement.classList.contains(\"no-mobile\")\n      ) {\n        document.querySelectorAll(\".wow-menubar\").forEach((el) => {\n          el.classList.add(\"no-animate\", \"fadeInDown\", \"animated\");\n          setInterval(() => {\n            el.classList.remove(\"no-animate\");\n          }, 1500);\n        });\n      } else {\n        document\n          .querySelectorAll(\".wow-menubar\")\n          .forEach((el) => (el.style.opacity = \"1\"));\n      }\n    } catch (error) {\n      console.error(\"Error initializing WOW.js:\", error);\n      // Fallback: make all elements visible\n      document.querySelectorAll(\".wow, .wow-p, .wow-menubar\").forEach((el) => {\n        el.style.opacity = \"1\";\n        el.classList.add(\"animated\");\n      });\n    }\n  }, 100);\n}\n", "export const headerChangeOnScroll = () => {\n  var mainNav = document.querySelector(\".main-nav\");\n  var navLogoWrapLogo = document.querySelector(\".nav-logo-wrap .logo\");\n  var lightAfterScroll = document.querySelector(\".light-after-scroll\");\n\n  // Exit early if main navigation doesn't exist (e.g., on admin pages)\n  if (!mainNav) {\n    return;\n  }\n\n  // Check if we're on a page that should maintain dark navbar\n  const shouldStayDark =\n    mainNav.classList.contains(\"dark-mode\") ||\n    window.location.pathname === \"/\" ||\n    window.location.pathname.match(/^\\/[a-z]{2}\\/?$/); // matches /en/, /et/, etc.\n\n  if (window.scrollY > 0) {\n    mainNav.classList.remove(\"transparent\");\n    mainNav.classList.add(\"small-height\", \"body-scrolled\");\n    if (navLogoWrapLogo) navLogoWrapLogo.classList.add(\"small-height\");\n\n    // Only remove dark class if we're not on a page that should stay dark\n    if (lightAfterScroll && !shouldStayDark) {\n      lightAfterScroll.classList.remove(\"dark\");\n    }\n\n    // Ensure dark navbar stays dark on scroll\n    if (shouldStayDark && !mainNav.classList.contains(\"dark\")) {\n      mainNav.classList.add(\"dark\");\n    }\n  } else if (window.scrollY === 0) {\n    mainNav.classList.add(\"transparent\");\n    mainNav.classList.remove(\"small-height\", \"body-scrolled\");\n    if (navLogoWrapLogo) navLogoWrapLogo.classList.remove(\"small-height\");\n\n    // Only add dark class back if we're not on a page that should stay dark\n    if (lightAfterScroll && !shouldStayDark) {\n      lightAfterScroll.classList.add(\"dark\");\n    }\n\n    // Ensure dark navbar stays dark when back to top\n    if (shouldStayDark && !mainNav.classList.contains(\"dark\")) {\n      mainNav.classList.add(\"dark\");\n    }\n  }\n};\n", "import React, { useEffect, Suspense, lazy } from \"react\";\n// CRITICAL CSS ONLY - for instant first paint\nimport \"./styles/critical.css\";\nimport \"./i18n\"; // Initialize i18n\n\n// Load non-critical CSS asynchronously\nimport { initCSSLoading } from \"./utils/loadCSS\";\n\n// Eager load GDPR component for better LCP performance\nimport GDPRConsent from \"./components/common/GDPRConsent\";\n\nimport { parallaxMouseMovement, parallaxScroll } from \"@/utils/parallax\";\n\nimport { init_wow } from \"@/utils/initWowjs\";\nimport { headerChangeOnScroll } from \"@/utils/changeHeaderOnScroll\";\nimport { Route, Routes, useLocation } from \"react-router-dom\";\nimport { trackPageView } from \"@/utils/analytics\";\n\nimport ScrollTopBehaviour from \"./components/common/ScrollTopBehaviour\";\nimport LanguageRedirect from \"./components/routing/LanguageRedirect\";\n\n// Eager load the home page for better initial performance\nimport Home5MainDemoMultiPageDark from \"@/pages/home/<USER>\";\n\n// Lazy load all other pages to reduce initial bundle size\n\nconst ElegantAboutPageDark = lazy(() => import(\"./pages/about/page\"));\nconst ElegantServicesPageDark = lazy(() => import(\"./pages/services/page\"));\nconst ElegantWebstorePageDark = lazy(() => import(\"./pages/webstore/page\"));\nconst ElegantPortfolioPageDark = lazy(() => import(\"./pages/portfolio/page\"));\nconst ElegantBlogPageDark = lazy(() => import(\"./pages/blogs/page\"));\nconst ElegantPortfolioSinglePageDark = lazy(() =>\n  import(\"./pages/portfolio-single/page\")\n);\nconst ElegantWebstoreSinglePageDark = lazy(() =>\n  import(\"./pages/webstore-single/page\")\n);\nconst ElegantBlogSinglePageDark = lazy(() =>\n  import(\"./pages/blog-single/page\")\n);\nconst ElegantContactPageDark = lazy(() => import(\"./pages/contact/page\"));\nconst PrivacyPolicyPage = lazy(() => import(\"@/pages/privacy-policy/page\"));\nconst TermsConditionsPage = lazy(() => import(\"@/pages/terms-conditions/page\"));\nconst MainPageNotFound = lazy(() => import(\"./pages/otherPages/page\"));\n\n// Admin routes - split into separate bundle for performance optimization\n// This removes ~593KB from the main bundle, improving load times for public users\nconst AdminRoutes = lazy(() => import(\"@/routes/AdminRoutes\"));\n\n// Loading component for lazy-loaded routes\nconst PageLoader = () => (\n  <div\n    className=\"page-loader\"\n    style={{\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      width: \"100%\",\n      height: \"100%\",\n      backgroundColor: \"#1a1a1a\",\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      zIndex: 9999,\n    }}\n  >\n    <div\n      className=\"loader\"\n      style={{\n        width: \"40px\",\n        height: \"40px\",\n        border: \"4px solid #333\",\n        borderTop: \"4px solid #fff\",\n        borderRadius: \"50%\",\n        animation: \"spin 1s linear infinite\",\n      }}\n    ></div>\n    <style>{`\n      @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n    `}</style>\n  </div>\n);\n\nfunction App() {\n  const { pathname } = useLocation();\n  useEffect(() => {\n    // Initialize CSS loading strategy first\n    initCSSLoading();\n\n    init_wow();\n    parallaxMouseMovement();\n\n    // Initialize navbar with better handling for page refreshes\n    const initializeNavbar = () => {\n      var mainNav = document.querySelector(\".main-nav\");\n      if (!mainNav) {\n        // If navbar doesn't exist yet, try again after a short delay\n        setTimeout(initializeNavbar, 100);\n        return;\n      }\n\n      // Ensure navbar is visible\n      mainNav.style.display = \"\";\n\n      if (mainNav?.classList.contains(\"transparent\")) {\n        mainNav.classList.add(\"js-transparent\");\n      } else if (!mainNav?.classList?.contains(\"dark\")) {\n        mainNav?.classList.add(\"js-no-transparent-white\");\n      }\n\n      // Force initial scroll check to set correct navbar state\n      headerChangeOnScroll();\n    };\n\n    // Initialize immediately and also after DOM is fully ready\n    initializeNavbar();\n    setTimeout(initializeNavbar, 50);\n\n    window.addEventListener(\"scroll\", headerChangeOnScroll);\n    parallaxScroll();\n\n    // Track page view on route change (deferred to avoid blocking render)\n    setTimeout(() => {\n      const pageTitle = document.title || \"DevSkills\";\n      const pageLocation = window.location.href;\n      trackPageView(pageTitle, pageLocation);\n    }, 100);\n\n    return () => {\n      window.removeEventListener(\"scroll\", headerChangeOnScroll);\n    };\n  }, [pathname]);\n\n  // Additional effect to handle navbar on route changes\n  useEffect(() => {\n    const handleNavbarOnRouteChange = () => {\n      const mainNav = document.querySelector(\".main-nav\");\n      if (mainNav) {\n        // Ensure navbar is visible\n        mainNav.style.display = \"\";\n        mainNav.style.visibility = \"visible\";\n        mainNav.style.opacity = \"1\";\n\n        // Force scroll check to set correct state\n        setTimeout(() => {\n          headerChangeOnScroll();\n        }, 100);\n      }\n    };\n\n    handleNavbarOnRouteChange();\n  }, [pathname]);\n  useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      // Import the script only on the client side\n      import(\"bootstrap/dist/js/bootstrap.esm\").then(() => {\n        // Bootstrap is now loaded and available\n        console.log(\"Bootstrap loaded\");\n      });\n    }\n  }, []);\n  return (\n    <>\n      <Suspense fallback={<PageLoader />}>\n        <Routes>\n          {/* Language-prefixed routes - ALL languages must have prefixes */}\n          <Route path=\"/:lang\">\n            <Route index element={<Home5MainDemoMultiPageDark />} />\n            <Route path=\"about\" element={<ElegantAboutPageDark />} />\n\n            <Route path=\"services\" element={<ElegantServicesPageDark />} />\n            <Route path=\"portfolio\" element={<ElegantPortfolioPageDark />} />\n            <Route\n              path=\"portfolio-single/:id\"\n              element={<ElegantPortfolioSinglePageDark />}\n            />\n            <Route path=\"webstore\" element={<ElegantWebstorePageDark />} />\n            <Route\n              path=\"webstore-single/:id\"\n              element={<ElegantWebstoreSinglePageDark />}\n            />\n            <Route path=\"blog\" element={<ElegantBlogPageDark />} />\n            <Route\n              path=\"blog-single/:id\"\n              element={<ElegantBlogSinglePageDark />}\n            />\n            <Route path=\"contact\" element={<ElegantContactPageDark />} />\n            <Route path=\"privacy-policy\" element={<PrivacyPolicyPage />} />\n            <Route path=\"terms-conditions\" element={<TermsConditionsPage />} />\n          </Route>\n\n          {/* Admin routes (no language prefix) - Code split for performance */}\n          <Route path=\"/admin/*\" element={<AdminRoutes />} />\n\n          {/* Root redirect - ALWAYS redirect to language-prefixed URL */}\n          <Route path=\"/\" element={<LanguageRedirect />} />\n\n          {/* Catch any non-prefixed routes and redirect them */}\n          <Route path=\"/about\" element={<LanguageRedirect />} />\n          <Route path=\"/services\" element={<LanguageRedirect />} />\n          <Route path=\"/portfolio\" element={<LanguageRedirect />} />\n          <Route path=\"/portfolio-single/*\" element={<LanguageRedirect />} />\n          <Route path=\"/blog\" element={<LanguageRedirect />} />\n          <Route path=\"/blog-single/*\" element={<LanguageRedirect />} />\n          <Route path=\"/contact\" element={<LanguageRedirect />} />\n          <Route path=\"/privacy-policy\" element={<LanguageRedirect />} />\n          <Route path=\"/terms-conditions\" element={<LanguageRedirect />} />\n\n          {/* 404 for everything else */}\n          <Route path=\"*\" element={<MainPageNotFound />} />\n        </Routes>\n      </Suspense>\n      <ScrollTopBehaviour />\n      <GDPRConsent />\n      {/* Analytics Debug Panel - Only show in development */}\n      {/* {import.meta.env.DEV && <AnalyticsDebug />} */}\n    </>\n  );\n}\n\nexport default App;\n", "import React from \"react\";\nimport { createRoot, hydrateRoot } from \"react-dom/client\";\nimport App from \"./App.jsx\";\nimport { BrowserRouter } from \"react-router-dom\";\nimport { HelmetProvider } from \"react-helmet-async\";\nimport ErrorBoundary from \"./components/common/ErrorBoundary.jsx\";\n// Non-critical CSS will be loaded asynchronously by App.jsx\n\n// Ready for Google Analytics and Google Tag Manager implementation\n\n// For react-snap compatibility\nconst rootElement = document.getElementById(\"root\");\n\n// Define the app content once to avoid duplication\nconst AppWithProviders = (\n  <React.StrictMode>\n    <ErrorBoundary>\n      <HelmetProvider>\n        <BrowserRouter\n          future={{\n            v7_startTransition: true,\n            v7_relativeSplatPath: true,\n          }}\n        >\n          <App />\n        </BrowserRouter>\n      </HelmetProvider>\n    </ErrorBoundary>\n  </React.StrictMode>\n);\n\n// Check if the document has been prerendered by react-snap\nconst hasPrerendered = rootElement && rootElement.hasChildNodes();\n\n// Use the appropriate rendering method\ntry {\n  if (hasPrerendered) {\n    // For prerendered content, use React 18 hydrateRoot\n    hydrateRoot(rootElement, AppWithProviders);\n  } else {\n    // For fresh loads, use standard rendering\n    const root = createRoot(rootElement);\n    root.render(AppWithProviders);\n  }\n} catch (error) {\n  console.error(\"Error rendering app:\", error);\n  // Fallback rendering\n  const root = createRoot(rootElement);\n  root.render(AppWithProviders);\n}\n"], "file": "assets/index-CU_BpMaE.js"}