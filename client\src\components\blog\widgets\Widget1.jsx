import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { categoriesAPI, tagsAPI, archiveAPI, blogAPI } from "@/utils/api";
import { useTranslation } from "react-i18next";

export default function Widget1({
  searchInputClass = "form-control input-md search-field input-circle",
}) {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [archiveData, setArchiveData] = useState([]);
  const [latestPosts, setLatestPosts] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const categoriesResult = await categoriesAPI.getCategories();
        if (categoriesResult.response.ok && categoriesResult.data) {
          setCategories(
            categoriesResult.data.data || categoriesResult.data.categories || []
          );
        }

        // Fetch tags
        const tagsResult = await tagsAPI.getTags();
        if (tagsResult.response.ok && tagsResult.data) {
          setTags(tagsResult.data.data || tagsResult.data.tags || []);
        }

        // Fetch archive data
        const archiveResult = await archiveAPI.getArchive();
        if (archiveResult.response.ok && archiveResult.data) {
          setArchiveData(archiveResult.data.archive || []);
        }

        // Fetch latest posts
        const postsResult = await blogAPI.getBlogPosts(currentLanguage, 1, 5);
        if (postsResult.response.ok && postsResult.data) {
          const posts =
            postsResult.data.data?.data || postsResult.data.data || [];
          setLatestPosts(Array.isArray(posts) ? posts : []);
        }
      } catch (error) {
        console.error("Error fetching widget data:", error);
      }
    };

    fetchData();
  }, [currentLanguage]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Redirect to blog page with search query
      window.location.href = `/blog?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  // Helper function to get translation for current language
  const getTranslation = (post, field) => {
    const translation = post.translations?.find(
      (t) => t.language === currentLanguage
    );
    return translation?.[field] || "";
  };

  return (
    <>
      {/* Search Widget */}
      <div className="widget">
        <form onSubmit={handleSearch} className="form">
          <div className="search-wrap">
            <button
              className="search-button animate"
              type="submit"
              title="Start Search"
            >
              <i className="mi-search size-18" />
              <span className="visually-hidden">Start search</span>
            </button>
            <input
              type="text"
              className={searchInputClass}
              placeholder={t("blog.search_placeholder") || "Search..."}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              required
            />
          </div>
        </form>
      </div>
      {/* End Search Widget */}
      {/* Widget */}
      <div className="widget">
        <h3 className="widget-title">{t("blog.categories")}</h3>
        <div className="widget-body">
          <ul className="clearlist widget-menu">
            {categories.length > 0 ? (
              categories.map((category) => (
                <li key={category.id}>
                  <a
                    href="#"
                    title=""
                    onClick={(e) => {
                      e.preventDefault();
                      window.location.href = `/${currentLanguage}/blog?category=${category.slug}`;
                    }}
                  >
                    {category.name}
                  </a>
                  <small> - {category._count?.blogPosts || 0} </small>
                </li>
              ))
            ) : (
              <li>{t("blog.no_categories")}</li>
            )}
          </ul>
        </div>
      </div>
      {/* End Widget */}
      {/* Widget */}
      <div className="widget">
        <h3 className="widget-title">{t("blog.tags")}</h3>
        <div className="widget-body">
          <div className="tags">
            {tags.length > 0 ? (
              tags.map((tag) => (
                <a
                  href="#"
                  key={tag.id}
                  onClick={(e) => {
                    e.preventDefault();
                    window.location.href = `/${currentLanguage}/blog?tag=${tag.slug}`;
                  }}
                >
                  {tag.name}
                </a>
              ))
            ) : (
              <span>{t("blog.no_tags")}</span>
            )}
          </div>
        </div>
      </div>
      {/* End Widget */}
      {/* Widget */}
      <div className="widget">
        <h3 className="widget-title">{t("blog.latest_posts")}</h3>
        <div className="widget-body">
          <ul className="clearlist widget-posts">
            {latestPosts.length > 0 ? (
              latestPosts.map((post, index) => (
                <li key={post.id || index} className="clearfix">
                  <a href={`/${currentLanguage}/blog-single/${post.slug}`}>
                    <img
                      src={
                        post.featuredImage ||
                        "/assets/images/demo-elegant/blog/1.jpg"
                      }
                      height={140}
                      style={{ height: "fit-content" }}
                      alt={getTranslation(post, "title")}
                      width={100}
                      className="widget-posts-img"
                    />
                  </a>
                  <div className="widget-posts-descr">
                    <a
                      href={`/${currentLanguage}/blog-single/${post.slug}`}
                      title=""
                    >
                      {getTranslation(post, "title")}
                    </a>
                    <span>
                      {t("blog.posted_by")}{" "}
                      {post.author?.name || "DevSkills Team"}
                    </span>
                  </div>
                </li>
              ))
            ) : (
              <li>{t("blog.no_recent_posts")}</li>
            )}
          </ul>
        </div>
      </div>
      {/* End Widget */}
      {/* Widget */}
      <div className="widget">
        <h3 className="widget-title">{t("blog.archive")}</h3>
        <div className="widget-body">
          <ul className="clearlist widget-menu">
            {archiveData.length > 0 ? (
              archiveData.map((archive, index) => (
                <li key={index}>
                  <a href="#" title="">
                    {archive.monthName} {archive.year}
                  </a>
                  <small> - {archive.count} </small>
                </li>
              ))
            ) : (
              <li>{t("blog.no_archive")}</li>
            )}
          </ul>
        </div>
      </div>
    </>
  );
}

Widget1.propTypes = {
  searchInputClass: PropTypes.string,
};
