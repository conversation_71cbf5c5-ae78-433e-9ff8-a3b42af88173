import{u as ne,d as Q,r as c,j as e,h as me,L as ue}from"./vendor-react-EBZQFYZ5.js";import{S as V,c as he,d as W,e as O,f as ce,b as ie,T as xe,g as ge,h as be,i as je,j as pe,H as Ne,P as fe,C as ve,k as ye,l as re}from"./components-common-DDbdC8oB.js";const we=()=>{const{t:x}=ne(),h=Q(),[w,S]=c.useState({email:"",password:""}),[u,b]=c.useState(!1),[f,m]=c.useState(""),C=P=>{S({...w,[P.target.name]:P.target.value}),f&&m("")},o=async P=>{P.preventDefault(),b(!0),m("");try{const{response:N,data:k}=await he.login(w);k.success?(localStorage.setItem("adminToken",k.token),localStorage.setItem("adminUser",JSON.stringify(k.user)),h("/admin/dashboard")):m(k.message||"Login failed")}catch(N){console.error("Login error:",N),m("Network error. Please try again.")}finally{b(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(V,{title:"Admin Login - DevSkills",description:"Admin login page for DevSkills content management",noIndex:!0}),e.jsx("div",{id:"page",className:"page",children:e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section",id:"admin-login",children:e.jsx("div",{className:"container relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-md-6 offset-md-3 col-lg-4 offset-lg-4",children:e.jsxs("div",{className:"form-container",children:[e.jsxs("div",{className:"text-center mb-60 mb-sm-40",children:[e.jsxs("div",{className:"hs-line-4 font-alt black mb-20 mb-xs-10",children:[e.jsx("span",{className:"color-primary-1",children:"DevSkills"})," Admin"]}),e.jsx("p",{className:"section-descr mb-0",children:"Sign in to access the admin dashboard"})]}),f&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("i",{className:"mi-warning"}),f]}),e.jsxs("form",{className:"form contact-form",onSubmit:o,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email Address"}),e.jsx("input",{type:"email",name:"email",id:"email",className:"input-lg round form-control",placeholder:"Email Address",value:w.email,onChange:C,required:!0,autoComplete:"email"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsx("input",{type:"password",name:"password",id:"password",className:"input-lg round form-control",placeholder:"Password",value:w.password,onChange:C,required:!0,autoComplete:"current-password"})]}),e.jsx("div",{className:"form-group",children:e.jsx("button",{type:"submit",className:"btn btn-mod btn-color btn-large btn-round btn-full-width",disabled:u,children:u?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"fa fa-spinner fa-spin me-2"}),"Signing in..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-lock me-2"}),"Sign In"]})})})]}),e.jsx("div",{className:"text-center mt-40",children:e.jsx("p",{className:"small opacity-07",children:"© 2024 DevSkills. All rights reserved."})})]})})})})})})})]})},Me=Object.freeze(Object.defineProperty({__proto__:null,default:we},Symbol.toStringTag,{value:"Module"})),Ce=()=>{const x=Q(),[h,w]=c.useState(null),[S,u]=c.useState(!0);return c.useEffect(()=>{(async()=>{const f=localStorage.getItem("adminToken"),m=localStorage.getItem("adminUser");if(!f||!m){x("/admin");return}try{const{response:C,data:o}=await he.getMe();C.ok&&o.success?w(o.user):(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),x("/admin"))}catch(C){console.error("Auth check failed:",C),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),x("/admin")}finally{u(!1)}})()},[x]),S?e.jsx("div",{id:"page",className:"page",children:e.jsx("main",{id:"main",children:e.jsx("section",{className:"page-section",children:e.jsx("div",{className:"container relative",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-12 text-center",children:e.jsxs("div",{className:"loading-animation",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"color-primary-1",style:{fontSize:"3rem",animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"mt-20",children:e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading..."})})]})})})})})})}):e.jsxs(e.Fragment,{children:[e.jsx(V,{title:"Admin Dashboard - DevSkills",description:"DevSkills admin dashboard for content management",noIndex:!0}),e.jsxs(W,{title:"Dashboard",children:[e.jsxs("div",{className:"row mb-40",children:[e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:document-text-bold"})}),e.jsx("div",{className:"number-2-title",children:"Total Posts"}),e.jsx("div",{className:"number-2-number",children:"0"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:folder-bold"})}),e.jsx("div",{className:"number-2-title",children:"Categories"}),e.jsx("div",{className:"number-2-number",children:"5"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:chat-round-bold"})}),e.jsx("div",{className:"number-2-title",children:"Comments"}),e.jsx("div",{className:"number-2-number",children:"0"})]})}),e.jsx("div",{className:"col-sm-6 col-lg-3 mb-md-50",children:e.jsxs("div",{className:"number-2-item",children:[e.jsx("div",{className:"number-2-icon",children:e.jsx("iconify-icon",{icon:"solar:eye-bold"})}),e.jsx("div",{className:"number-2-title",children:"Page Views"}),e.jsx("div",{className:"number-2-number",children:"-"})]})})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsx("div",{className:"mb-20",children:e.jsx("h3",{className:"hs-line-4 font-alt black mb-20 mb-xs-10",children:"Quick Actions"})}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:add-circle-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"New Blog Post"}),e.jsx("div",{className:"alt-features-descr",children:"Create a new multilingual blog post with rich content and scheduling."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>x("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:"Create Post"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:documents-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Manage Posts"}),e.jsx("div",{className:"alt-features-descr",children:"Edit, publish, schedule, and organize your existing blog posts."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>x("/admin/posts"),className:"btn btn-mod btn-color btn-round",children:"Manage Posts"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:folder-with-files-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Categories & Tags"}),e.jsx("div",{className:"alt-features-descr",children:"Organize your content with categories and tags for better navigation."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>x("/admin/categories"),className:"btn btn-mod btn-color btn-round",children:"Organize Content"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:chat-round-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Comment Management"}),e.jsx("div",{className:"alt-features-descr",children:"Review, approve, and manage comments from your blog readers."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>x("/admin/comments"),className:"btn btn-mod btn-color btn-round",children:"Manage Comments"})})]})}),e.jsx("div",{className:"col-sm-6 col-lg-4 mb-md-50",children:e.jsxs("div",{className:"alt-features-item align-center",children:[e.jsx("div",{className:"alt-features-icon",children:e.jsx("iconify-icon",{icon:"solar:chart-2-bold"})}),e.jsx("h3",{className:"alt-features-title font-alt",children:"Analytics"}),e.jsx("div",{className:"alt-features-descr",children:"View detailed analytics and insights about your blog performance."}),e.jsx("div",{className:"local-scroll mt-20",children:e.jsx("button",{onClick:()=>x("/admin/analytics"),className:"btn btn-mod btn-color btn-round",children:"View Analytics"})})]})})]})]})})]})]})},Re=Object.freeze(Object.defineProperty({__proto__:null,default:Ce},Symbol.toStringTag,{value:"Module"})),ke=()=>{const x=Q(),[h,w]=c.useState([]),[S,u]=c.useState(!0),[b,f]=c.useState(""),[m,C]=c.useState({page:1,limit:10,status:"all",search:""}),[o,P]=c.useState({});c.useEffect(()=>{N()},[m]);const N=async()=>{try{u(!0);const l={};Object.entries(m).forEach(([g,s])=>{s&&s!=="all"&&(l[g]=s)});const{response:p,data:a}=await O.getPosts(l);a.success?(w(a.data.posts),P(a.data.pagination)):f(a.message||"Failed to load posts")}catch(l){console.error("Load posts error:",l),f("Network error. Please try again.")}finally{u(!1)}},k=async l=>{if(confirm("Are you sure you want to delete this blog post? This action cannot be undone."))try{const{response:p,data:a}=await ie.deletePost(l);a.success?N():f(a.message||"Failed to delete post")}catch(p){console.error("Delete error:",p),f("Network error. Please try again.")}},T=async l=>{try{const{response:p,data:a}=await ie.toggleVisibility(l);a.success?N():f(a.message||"Failed to toggle visibility")}catch(p){console.error("Toggle visibility error:",p),f("Network error. Please try again.")}},E=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),n=l=>l?l.startsWith("http")?l:`${ce.replace("/api","")}/uploads/blog-images/${l}`:null,A=l=>l.published?l.scheduledAt&&new Date(l.scheduledAt)>new Date?e.jsxs("span",{className:"badge bg-warning",children:[e.jsx("iconify-icon",{icon:"solar:clock-circle-bold",className:"me-1"}),"Scheduled"]}):e.jsxs("span",{className:"badge bg-success",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]}):e.jsxs("span",{className:"badge bg-secondary",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-1"}),"Draft"]});return e.jsxs(e.Fragment,{children:[e.jsx(V,{title:"Manage Blog Posts - Admin",description:"Manage blog posts in the admin panel",noIndex:!0}),e.jsxs(W,{title:"Blog Posts",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Manage your blog posts, create new content, and organize your articles."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:()=>x("/admin/blog/new"),className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Post"]})})]})}),e.jsx("div",{className:"admin-table mb-30",style:{padding:"15px 20px"},children:e.jsxs("div",{className:"row g-3",children:[e.jsxs("div",{className:"col-12 col-md-6 col-lg-4",children:[e.jsx("label",{className:"form-label",children:"Search Posts"}),e.jsx("input",{type:"text",value:m.search,onChange:l=>C(p=>({...p,search:l.target.value,page:1})),className:"form-control",placeholder:"Search by title..."})]}),e.jsxs("div",{className:"col-6 col-md-3 col-lg-3",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:m.status,onChange:l=>C(p=>({...p,status:l.target.value,page:1})),className:"form-control",children:[e.jsx("option",{value:"all",children:"All Posts"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"draft",children:"Drafts"})]})]}),e.jsxs("div",{className:"col-6 col-md-3 col-lg-2",children:[e.jsx("label",{className:"form-label",children:"Per Page"}),e.jsxs("select",{value:m.limit,onChange:l=>C(p=>({...p,limit:parseInt(l.target.value),page:1})),className:"form-control",children:[e.jsx("option",{value:10,children:"10"}),e.jsx("option",{value:25,children:"25"}),e.jsx("option",{value:50,children:"50"})]})]})]})}),b&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),b]}),e.jsx("div",{className:"admin-table",children:S?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading posts..."})]}):h.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No blog posts found"}),e.jsx("p",{className:"section-descr mb-30",children:m.search||m.status!=="all"?"Try adjusting your search filters or create your first blog post.":"Get started by creating your first blog post."}),e.jsxs("button",{onClick:()=>x("/admin/blog/new"),className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Post"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Title"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Author"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:h.map(l=>{const p=l.translations.find(a=>a.language==="en")||l.translations[0];return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[l.featuredImage&&e.jsx("img",{className:"rounded me-3",src:n(l.featuredImage),alt:"",style:{width:"50px",height:"50px",objectFit:"cover"},onError:a=>{a.target.style.display="none"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:(p==null?void 0:p.title)||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",l.slug]})]})]})}),e.jsxs("td",{children:[A(l),l.featured&&e.jsxs("span",{className:"badge bg-primary ms-2",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured"]})]}),e.jsx("td",{children:l.author.name||l.author.email}),e.jsx("td",{children:E(l.createdAt)}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>x(`/admin/blog/edit/${l.id}`),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>T(l.id),className:`btn btn-sm ${l.published?"btn-outline-warning":"btn-outline-success"}`,title:l.published?"Unpublish":"Publish",children:e.jsx("iconify-icon",{icon:l.published?"solar:eye-closed-bold":"solar:eye-bold"})}),e.jsx("button",{onClick:()=>k(l.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},l.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:h.map(l=>{const p=l.translations.find(a=>a.language==="en")||l.translations[0];return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[l.featuredImage&&e.jsx("img",{className:"rounded me-3",src:n(l.featuredImage),alt:"",style:{width:"40px",height:"40px",objectFit:"cover"},onError:a=>{a.target.style.display="none"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:(p==null?void 0:p.title)||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",l.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Status"}),e.jsxs("div",{children:[A(l),l.featured&&e.jsxs("span",{className:"badge bg-primary ms-1",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured"]})]})]}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Author"}),e.jsx("small",{children:l.author.name||l.author.email})]}),e.jsxs("div",{className:"col-12 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:E(l.createdAt)})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>x(`/admin/blog/edit/${l.id}`),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>T(l.id),className:`btn btn-sm flex-fill ${l.published?"btn-outline-warning":"btn-outline-success"}`,title:l.published?"Unpublish":"Publish",children:[e.jsx("iconify-icon",{icon:l.published?"solar:eye-closed-bold":"solar:eye-bold",className:"me-1"}),l.published?"Hide":"Show"]}),e.jsxs("button",{onClick:()=>k(l.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},l.id)})})})]})}),o.pages>1&&e.jsxs("div",{className:"row mt-30 align-items-center",children:[e.jsx("div",{className:"col-12 col-md-6 mb-3 mb-md-0",children:e.jsxs("p",{className:"small text-muted mb-0 text-center text-md-start",children:["Showing ",(o.page-1)*o.limit+1," to"," ",Math.min(o.page*o.limit,o.total)," ","of ",o.total," results"]})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx("nav",{"aria-label":"Blog posts pagination",children:e.jsxs("ul",{className:"pagination pagination-sm justify-content-center justify-content-md-end mb-0",children:[e.jsx("li",{className:`page-item ${o.page<=1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>C(l=>({...l,page:l.page-1})),disabled:o.page<=1,children:"Previous"})}),e.jsx("li",{className:"page-item active",children:e.jsxs("span",{className:"page-link",children:["Page ",o.page," of ",o.pages]})}),e.jsx("li",{className:`page-item ${o.page>=o.pages?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>C(l=>({...l,page:l.page+1})),disabled:o.page>=o.pages,children:"Next"})})]})})})]})]})]})},Be=Object.freeze(Object.defineProperty({__proto__:null,default:ke},Symbol.toStringTag,{value:"Module"})),Se=()=>{var J,Y,ae,le,te;const{t:x,i18n:h}=ne(),w=Q(),{id:S}=me(),u=!!S,b=r=>r?r.startsWith("http")?r:`${ce.replace("/api","")}/uploads/blog-images/${r}`:null,[f,m]=c.useState(!1),[C,o]=c.useState(!1),[P,N]=c.useState(""),[k,T]=c.useState(""),[E]=c.useState(()=>Object.keys(h.store.data)),[n,A]=c.useState(()=>{const r={};return E.forEach(F=>{r[F]={title:"",excerpt:"",content:"",metaTitle:"",metaDesc:"",keywords:[]}}),{slug:"",featured:!1,published:!1,scheduledAt:"",featuredImage:null,featuredImageAlt:"",readTime:"",categoryIds:[],tagIds:[],translations:r}}),[l,p]=c.useState("en"),[a,g]=c.useState([]),[s,d]=c.useState([]),[t,I]=c.useState(null);c.useEffect(()=>{(async()=>{try{if(m(!0),N(""),!localStorage.getItem("adminToken")){N("Authentication required. Please log in to access this page."),m(!1);return}const[y,$]=await Promise.all([O.getCategories(),O.getTags()]);if(y.response.ok&&y.data)g(y.data.data||[]);else{if(console.error("Categories API failed:",y.response.status,y.response.statusText),y.response.status===401||y.response.status===403){N("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}g([])}if($.response.ok&&$.data)d($.data.data||[]);else{if(console.error("Tags API failed:",$.response.status,$.response.statusText),$.response.status===401||$.response.status===403){N("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}d([])}if(u){const{response:G,data:B}=await O.getPost(S);if(G.ok&&B.success)try{const L=B.data,i={};L.translations&&Array.isArray(L.translations)&&L.translations.forEach(v=>{i[v.language]=v}),A(v=>({...v,slug:L.slug||"",featured:L.featured||!1,published:L.published||!1,scheduledAt:L.scheduledAt?new Date(L.scheduledAt).toISOString().slice(0,16):"",featuredImage:null,featuredImageAlt:L.featuredImageAlt||"",readTime:L.readTime||"",categoryIds:L.categories?L.categories.map(j=>j.id):[],tagIds:L.tags?L.tags.map(j=>j.id):[],translations:{...v.translations,...i}})),L.featuredImage&&I(b(L.featuredImage))}catch(L){console.error("Failed to parse post response:",L),N("Failed to load post data - invalid response format")}else console.error("Post API failed:",G.status,G.statusText),N(B.message||`Failed to load post: ${G.status} ${G.statusText}`)}}catch(F){console.error("Error loading data:",F),F.message&&F.message.includes("fetch")?N("Failed to connect to the server. Please check if the backend is running on localhost:4004"):N("Failed to load data. Please try again.")}finally{m(!1)}})()},[S,u]);const _=(r,F)=>{A(y=>({...y,[r]:F}))},H=(r,F,y)=>{A($=>({...$,translations:{...$.translations,[r]:{...$.translations[r],[F]:y}}}))},oe=r=>{const F=r.target.files[0];if(F){A($=>({...$,featuredImage:F}));const y=new FileReader;y.onload=$=>{I($.target.result)},y.readAsDataURL(F)}},Z=async r=>{r.preventDefault(),o(!0),N(""),T("");try{const F=localStorage.getItem("adminToken"),y=new FormData;y.append("slug",n.slug),y.append("featured",n.featured),y.append("published",n.published),n.scheduledAt&&y.append("scheduledAt",n.scheduledAt),y.append("featuredImageAlt",n.featuredImageAlt),n.readTime&&y.append("readTime",n.readTime),y.append("categoryIds",JSON.stringify(n.categoryIds)),y.append("tagIds",JSON.stringify(n.tagIds)),y.append("translations",JSON.stringify(n.translations)),n.featuredImage&&y.append("featuredImage",n.featuredImage);let $;u?$=await ie.updatePost(S,y):$=await ie.createPost(y);const{response:G,data:B}=$;if(G.ok&&B&&B.success)T(`Blog post ${u?"updated":"created"} successfully!`),setTimeout(()=>{w("/admin/posts")},2e3);else{const L=(B==null?void 0:B.message)||`Failed to ${u?"update":"create"} blog post`;N(L)}}catch(F){console.error("Save error:",F),N("Network error. Please try again.")}finally{o(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(V,{title:`${u?"Edit":"Create"} Blog Post - Admin`,description:"Create or edit blog posts in the admin panel",noIndex:!0}),e.jsx(W,{title:u?"Edit Blog Post":"Create New Blog Post",children:e.jsxs("form",{onSubmit:Z,className:"admin-form",children:[P&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),P]}),k&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),k]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:settings-bold",className:"me-2 color-primary-1"}),"Basic Settings"]}),e.jsx("p",{className:"section-descr mb-0",children:"Configure the basic properties of your blog post"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"me-2"}),"Slug (URL)"]}),e.jsx("input",{type:"text",value:n.slug,onChange:r=>_("slug",r.target.value),className:"form-control",placeholder:"blog-post-url"}),e.jsx("small",{className:"form-text text-muted",children:"This will be the URL path for your blog post (e.g., /blog/your-slug)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:clock-circle-bold",className:"me-2"}),"Read Time (minutes)"]}),e.jsx("input",{type:"number",value:n.readTime,onChange:r=>_("readTime",r.target.value),className:"form-control",placeholder:"5",min:"1",max:"60"}),e.jsx("small",{className:"form-text text-muted",children:"Estimated reading time for this post"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:calendar-bold",className:"me-2"}),"Schedule Publication"]}),e.jsx("input",{type:"datetime-local",value:n.scheduledAt,onChange:r=>_("scheduledAt",r.target.value),className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Leave empty to publish immediately when published is checked"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-2"}),"Post Options"]}),e.jsxs("div",{className:"d-flex flex-column gap-2",children:[e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"featured",checked:n.featured,onChange:r=>_("featured",r.target.checked),className:"form-check-input"}),e.jsxs("label",{className:"form-check-label",htmlFor:"featured",children:[e.jsx("iconify-icon",{icon:"solar:star-bold",className:"me-1"}),"Featured Post"]}),e.jsx("small",{className:"form-text text-muted d-block",children:"Show this post prominently on the homepage"})]}),e.jsxs("div",{className:"form-check",children:[e.jsx("input",{type:"checkbox",id:"published",checked:n.published,onChange:r=>_("published",r.target.checked),className:"form-check-input"}),e.jsxs("label",{className:"form-check-label",htmlFor:"published",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]}),e.jsx("small",{className:"form-text text-muted d-block",children:"Make this post visible to the public"})]})]})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:gallery-bold",className:"me-2 color-primary-1"}),"Featured Image"]}),e.jsx("p",{className:"section-descr mb-0",children:"Upload a featured image that will be displayed with your blog post"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:upload-bold",className:"me-2"}),"Upload Image"]}),e.jsx("input",{type:"file",accept:"image/*",onChange:oe,className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Recommended size: 1200x630px. Supported formats: JPG, PNG, WebP"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:eye-bold",className:"me-2"}),"Alt Text"]}),e.jsx("input",{type:"text",value:n.featuredImageAlt,onChange:r=>_("featuredImageAlt",r.target.value),className:"form-control",placeholder:"Describe the image for accessibility"}),e.jsx("small",{className:"form-text text-muted",children:"Describe the image for screen readers and SEO"})]}),t&&e.jsxs("div",{className:"col-12",children:[e.jsx("div",{className:"mb-20",children:e.jsx("label",{className:"form-label",children:"Image Preview"})}),e.jsx("div",{className:"text-center",children:e.jsx("img",{src:t,alt:"Preview",className:"image-preview",style:{maxWidth:"400px",height:"auto"}})})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("i",{className:"mi-globe me-2 color-primary-1"}),"Content (Multi-language)"]}),e.jsx("p",{className:"section-descr mb-0",children:"Create content in multiple languages. At least English content is required."})]})}),e.jsx("div",{className:"language-tabs mb-30",children:E.map(r=>e.jsxs("button",{type:"button",onClick:()=>p(r),className:`language-tab ${l===r?"active":""}`,children:[e.jsx("i",{className:"mi-globe me-2"}),r.toUpperCase(),r==="en"&&e.jsx("span",{className:"ms-1 small",children:"(Required)"})]},r))}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Title (",l.toUpperCase(),")",l==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx("input",{type:"text",value:((J=n.translations[l])==null?void 0:J.title)||"",onChange:r=>H(l,"title",r.target.value),className:"form-control",placeholder:"Enter blog post title",required:l==="en"}),e.jsxs("small",{className:"form-text text-muted",children:["The main title of your blog post in"," ",l.toUpperCase()]})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-text me-2"}),"Excerpt (",l.toUpperCase(),")"]}),e.jsx("textarea",{value:((Y=n.translations[l])==null?void 0:Y.excerpt)||"",onChange:r=>H(l,"excerpt",r.target.value),rows:3,className:"form-control",placeholder:"Brief description of the blog post"}),e.jsx("small",{className:"form-text text-muted",children:"A short summary that will appear in blog listings and social media previews"})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-2"}),"Content (",l.toUpperCase(),")",l==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx(xe,{content:((ae=n.translations[l])==null?void 0:ae.content)||"",onChange:r=>H(l,"content",r),placeholder:"Write your blog post content here. You can paste formatted text and code snippets with syntax highlighting."}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("iconify-icon",{icon:"solar:info-circle-bold",className:"me-1"}),"Rich text editor with syntax highlighting. Paste code snippets and they will be automatically highlighted. Use the toolbar for formatting options."]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Title (",l.toUpperCase(),")"]}),e.jsx("input",{type:"text",value:((le=n.translations[l])==null?void 0:le.metaTitle)||"",onChange:r=>H(l,"metaTitle",r.target.value),className:"form-control",placeholder:"SEO title (optional)",maxLength:"60"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-search me-1"}),"Title that appears in search engine results (max 60 characters)"]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-description me-2"}),"Meta Description (",l.toUpperCase(),")"]}),e.jsx("textarea",{value:((te=n.translations[l])==null?void 0:te.metaDesc)||"",onChange:r=>H(l,"metaDesc",r.target.value),rows:3,className:"form-control",placeholder:"SEO description (optional)",maxLength:"160"}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("i",{className:"mi-search me-1"}),"Description that appears in search engine results (max 160 characters)"]})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-2 color-primary-1"}),"Categories & Tags"]}),e.jsx("p",{className:"section-descr mb-0",children:"Organize your blog post with categories and tags"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"me-2"}),"Categories"]}),e.jsx("div",{className:"categories-grid",children:a&&a.length>0?a.map(r=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`category-${r.id}`,checked:n.categoryIds.includes(r.id),onChange:()=>{const F=n.categoryIds.includes(r.id)?n.categoryIds.filter(y=>y!==r.id):[...n.categoryIds,r.id];A(y=>({...y,categoryIds:F}))}}),e.jsx("label",{className:"form-check-label",htmlFor:`category-${r.id}`,children:r.name})]},r.id)):e.jsx("p",{className:"text-muted",children:"No categories available"})})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:hashtag-bold",className:"me-2"}),"Tags"]}),e.jsx("div",{className:"tags-grid",children:s&&s.length>0?s.map(r=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`tag-${r.id}`,checked:n.tagIds.includes(r.id),onChange:()=>{const F=n.tagIds.includes(r.id)?n.tagIds.filter(y=>y!==r.id):[...n.tagIds,r.id];A(y=>({...y,tagIds:F}))}}),e.jsx("label",{className:"form-check-label",htmlFor:`tag-${r.id}`,children:r.name})]},r.id)):e.jsx("p",{className:"text-muted",children:"No tags available"})})]})]})]}),e.jsx("div",{className:"row mt-40",children:e.jsxs("div",{className:"col-12 text-end",children:[e.jsx("button",{type:"button",onClick:()=>w("/admin/posts"),className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:C,className:"btn btn-mod btn-color btn-round",children:C?e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"fa fa-spinner fa-spin me-2"}),"Saving..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-check me-2"}),u?"Update Post":"Create Post"]})})]})})]})})]})},ze=Object.freeze(Object.defineProperty({__proto__:null,default:Se},Symbol.toStringTag,{value:"Module"})),Pe=()=>{const x=Q(),[h,w]=c.useState([]),[S,u]=c.useState(!0),[b,f]=c.useState(""),[m,C]=c.useState({page:1,limit:10,status:"all",search:""}),[o,P]=c.useState({});c.useEffect(()=>{N()},[m]);const N=async()=>{var a,g;try{u(!0);const s={};Object.entries(m).forEach(([I,_])=>{_&&_!=="all"&&(s[I]=_)});const{response:d,data:t}=await O.getProducts(s);t.success?(w(((a=t.data)==null?void 0:a.products)||t.products||[]),P(((g=t.data)==null?void 0:g.pagination)||{})):f(t.message||"Failed to load products")}catch(s){console.error("Load products error:",s),f("Network error. Please try again.")}finally{u(!1)}},k=async a=>{if(window.confirm("Are you sure you want to delete this product?"))try{const{response:g,data:s}=await O.deleteProduct(a);g.ok&&s.success?w(h.filter(d=>d.id!==a)):f(s.message||"Failed to delete product")}catch(g){console.error("Delete product error:",g),f("Failed to delete product")}},T=async a=>{try{const s=h.find(I=>I.id===a).status==="published"?"draft":"published",{response:d,data:t}=await O.updateProduct(a,{status:s});d.ok&&t.success?w(h.map(I=>I.id===a?{...I,status:s}:I)):f(t.message||"Failed to update product status")}catch(g){console.error("Toggle visibility error:",g),f("Failed to update product status")}},E=(a,g)=>{C(s=>({...s,[a]:g,page:1}))},n=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),A=a=>a?a.startsWith("http")?a:`${ce.replace("/api","")}/uploads/product-images/${a}`:null,l=a=>a.images&&a.images.length>0?(a.images.find(s=>s.isDisplay)||a.images[0]).filename:a.featuredImage,p=a=>a.status==="draft"?e.jsxs("span",{className:"badge bg-secondary",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-1"}),"Draft"]}):e.jsxs("span",{className:"badge bg-success",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-1"}),"Published"]});return e.jsxs(e.Fragment,{children:[e.jsx(V,{title:"Manage Products - Admin",description:"Manage products in the admin panel",noIndex:!0}),e.jsxs(W,{title:"Products",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Manage your webstore products, create new items, and organize your catalog."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:()=>x("/admin/products/new"),className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Product"]})})]})}),e.jsx("div",{className:"admin-table mb-30",style:{padding:"15px 20px"},children:e.jsxs("div",{className:"row g-3",children:[e.jsxs("div",{className:"col-12 col-md-6 col-lg-4",children:[e.jsx("label",{className:"form-label",children:"Search Products"}),e.jsx("input",{type:"text",className:"form-control",placeholder:"Search by title or slug...",value:m.search,onChange:a=>E("search",a.target.value)})]}),e.jsxs("div",{className:"col-12 col-md-6 col-lg-4",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{className:"form-control",value:m.status,onChange:a=>E("status",a.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"published",children:"Published"}),e.jsx("option",{value:"draft",children:"Draft"})]})]}),e.jsx("div",{className:"col-12 col-lg-4 d-flex align-items-end",children:e.jsxs("button",{onClick:()=>C({page:1,limit:10,status:"all",search:""}),className:"btn btn-mod btn-border btn-round w-100",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"me-2"}),"Reset Filters"]})})]})}),b&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),b]}),e.jsx("div",{className:"admin-table",children:S?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading products..."})]}):h.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:shop-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No products found"}),e.jsx("p",{className:"section-descr mb-30",children:m.search||m.status!=="all"?"Try adjusting your search filters or create your first product.":"Get started by creating your first product."}),e.jsxs("button",{onClick:()=>x("/admin/products/new"),className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Product"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Product"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Pricing"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:h.map(a=>{var s,d;const g=((s=a.translations)==null?void 0:s.find(t=>t.language==="en"))||((d=a.translations)==null?void 0:d[0]);return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[l(a)&&e.jsx("img",{className:"rounded me-3",src:A(l(a)),alt:"",style:{width:"50px",height:"50px",objectFit:"cover"},onError:t=>{t.target.style.display="none"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:(g==null?void 0:g.title)||a.title||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsx("td",{children:p(a)}),e.jsx("td",{children:e.jsxs("div",{children:[a.whitelabelPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"Whitelabel:"})," €",a.whitelabelPrice]}),a.subscriptionPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"Subscription:"})," €",a.subscriptionPrice,"/mo"]}),!a.whitelabelPrice&&!a.subscriptionPrice&&e.jsx("span",{className:"text-muted",children:"No pricing set"})]})}),e.jsx("td",{children:n(a.createdAt)}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>x(`/admin/products/edit/${a.id}`),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>T(a.id),className:`btn btn-sm ${a.status==="published"?"btn-outline-warning":"btn-outline-success"}`,title:a.status==="published"?"Unpublish":"Publish",children:e.jsx("iconify-icon",{icon:a.status==="published"?"solar:eye-closed-bold":"solar:eye-bold"})}),e.jsx("button",{onClick:()=>k(a.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},a.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:h.map(a=>{var s,d;const g=((s=a.translations)==null?void 0:s.find(t=>t.language==="en"))||((d=a.translations)==null?void 0:d[0]);return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[l(a)&&e.jsx("img",{className:"rounded me-3",src:A(l(a)),alt:"",style:{width:"40px",height:"40px",objectFit:"cover"},onError:t=>{t.target.style.display="none"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:(g==null?void 0:g.title)||a.title||"Untitled"}),e.jsxs("small",{className:"text-muted",children:["/",a.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Status"}),e.jsx("div",{children:p(a)})]}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Pricing"}),e.jsxs("div",{children:[a.whitelabelPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"WL:"})," €",a.whitelabelPrice]}),a.subscriptionPrice&&e.jsxs("div",{className:"small",children:[e.jsx("strong",{children:"Sub:"})," €",a.subscriptionPrice,"/mo"]}),!a.whitelabelPrice&&!a.subscriptionPrice&&e.jsx("span",{className:"text-muted small",children:"No pricing"})]})]}),e.jsxs("div",{className:"col-12 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:n(a.createdAt)})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>x(`/admin/products/edit/${a.id}`),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>T(a.id),className:`btn btn-sm flex-fill ${a.status==="published"?"btn-outline-warning":"btn-outline-success"}`,title:a.status==="published"?"Unpublish":"Publish",children:[e.jsx("iconify-icon",{icon:a.status==="published"?"solar:eye-closed-bold":"solar:eye-bold",className:"me-1"}),a.status==="published"?"Hide":"Show"]}),e.jsxs("button",{onClick:()=>k(a.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},a.id)})})})]})}),o.pages>1&&e.jsxs("div",{className:"row mt-30 align-items-center",children:[e.jsx("div",{className:"col-12 col-md-6 mb-3 mb-md-0",children:e.jsxs("p",{className:"small text-muted mb-0 text-center text-md-start",children:["Showing ",(o.page-1)*o.limit+1," to"," ",Math.min(o.page*o.limit,o.total)," ","of ",o.total," results"]})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx("nav",{"aria-label":"Products pagination",children:e.jsxs("ul",{className:"pagination pagination-sm justify-content-center justify-content-md-end mb-0",children:[e.jsx("li",{className:`page-item ${o.page<=1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>C(a=>({...a,page:a.page-1})),disabled:o.page<=1,children:"Previous"})}),e.jsx("li",{className:"page-item active",children:e.jsxs("span",{className:"page-link",children:["Page ",o.page," of ",o.pages]})}),e.jsx("li",{className:`page-item ${o.page>=o.pages?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>C(a=>({...a,page:a.page+1})),disabled:o.page>=o.pages,children:"Next"})})]})})})]})]})]})},qe=Object.freeze(Object.defineProperty({__proto__:null,default:Pe},Symbol.toStringTag,{value:"Module"})),Ae=()=>{var y,$,G,B,L;const{t:x,i18n:h}=ne(),w=Q(),{id:S}=me(),u=!!S,b=i=>i?i.startsWith("http")?i:`${ce.replace("/api","")}/uploads/product-images/${i}`:null,[f,m]=c.useState(!1),[C,o]=c.useState(!1),[P,N]=c.useState(""),[k,T]=c.useState(""),[E]=c.useState(()=>Object.keys(h.store.data)),[n,A]=c.useState(()=>{const i={};return E.forEach(v=>{i[v]={title:"",excerpt:"",content:"",metaTitle:"",metaDesc:"",keywords:[]}}),{slug:"",whitelabelPrice:"",subscriptionPrice:"",demoUrl:"",status:"draft",featuredImage:null,featuredImageAlt:"",images:[],categoryIds:[],tagIds:[],translations:i}}),[l,p]=c.useState([]),[a,g]=c.useState([]),[s,d]=c.useState("en"),[t,I]=c.useState(null),[_,H]=c.useState([]),[oe,Z]=c.useState(0);c.useEffect(()=>{(async()=>{var v,j,U;try{m(!0);const[z,K]=await Promise.all([O.getCategories(),O.getTags()]);if((v=z.data)!=null&&v.success&&p(z.data.data),(j=K.data)!=null&&j.success&&g(K.data.data),u&&S){console.log("Loading product with ID:",S),console.log("Calling adminAPI.getProduct...");const X=await O.getProduct(S);if(console.log("Product API response:",X),(U=X.data)!=null&&U.success){const D=X.data.product;console.log("Product data:",D),A(R=>{var ee,q;return{...R,slug:D.slug||"",whitelabelPrice:D.whitelabelPrice||"",subscriptionPrice:D.subscriptionPrice||"",demoUrl:D.demoUrl||"",status:D.status||"draft",featuredImageAlt:D.featuredImageAlt||"",categoryIds:((ee=D.categories)==null?void 0:ee.map(se=>se.categoryId))||[],tagIds:((q=D.tags)==null?void 0:q.map(se=>se.tagId))||[]}});const M={...n.translations};if(D.translations&&Array.isArray(D.translations)&&D.translations.forEach(R=>{M[R.language]={title:R.title||"",excerpt:R.excerpt||"",content:R.content||"",metaTitle:R.metaTitle||"",metaDesc:R.metaDesc||"",keywords:R.keywords||[]}}),A(R=>({...R,translations:M})),D.images&&Array.isArray(D.images)){console.log("Loading existing images:",D.images);const R=D.images.map((q,se)=>{const de=b(q.filename);return console.log(`Image ${se}: ${q.filename} -> ${de}`),{id:q.id,file:null,preview:de,alt:q.alt||"",isDisplay:q.isDisplay,filename:q.filename,sortOrder:q.sortOrder}});console.log("Processed existing images:",R),H(R);const ee=R.findIndex(q=>q.isDisplay);ee!==-1&&Z(ee)}else console.log("No images found in product:",D.images);D.featuredImage&&(!D.images||D.images.length===0)&&I(b(D.featuredImage))}else console.error("Failed to load product:",X),N("Failed to load product data")}}catch(z){console.error("Error loading data:",z),N("Failed to load data")}finally{m(!1)}})()},[S,u]);const J=(i,v)=>{A(j=>({...j,[i]:v}))},Y=(i,v,j)=>{A(U=>({...U,translations:{...U.translations,[i]:{...U.translations[i],[v]:j}}}))},ae=i=>{const v=Array.from(i.target.files);if(v.length===0)return;if(_.length+v.length>10){N("Maximum 10 images allowed per product");return}const j=v.map((U,z)=>({file:U,preview:URL.createObjectURL(U),alt:"",isDisplay:_.length===0&&z===0}));H(U=>[...U,...j]),N("")},le=i=>{H(v=>{var U;const j=v.filter((z,K)=>K!==i);return(U=v[i])!=null&&U.isDisplay&&j.length>0&&(j[0].isDisplay=!0,Z(0)),j})},te=i=>{H(v=>v.map((j,U)=>({...j,isDisplay:U===i}))),Z(i)},r=(i,v)=>{H(j=>j.map((U,z)=>z===i?{...U,alt:v}:U))},F=async i=>{i.preventDefault(),o(!0),N(""),T("");try{const v=localStorage.getItem("adminToken"),j=new FormData;j.append("slug",n.slug),j.append("whitelabelPrice",n.whitelabelPrice),j.append("subscriptionPrice",n.subscriptionPrice),j.append("demoUrl",n.demoUrl),j.append("status",n.status),j.append("featuredImageAlt",n.featuredImageAlt),j.append("categoryIds",JSON.stringify(n.categoryIds)),j.append("tagIds",JSON.stringify(n.tagIds)),j.append("translations",JSON.stringify(n.translations));let U=0;_.forEach(M=>{M.file&&(j.append("images",M.file),j.append(`imageAlt_${U}`,M.alt),j.append(`isDisplay_${U}`,M.isDisplay),U++)});const z=_.filter(M=>!M.file&&M.id).map(M=>({id:M.id,alt:M.alt,isDisplay:M.isDisplay,sortOrder:M.sortOrder}));z.length>0&&j.append("existingImages",JSON.stringify(z));let K;u?K=await O.updateProduct(S,j):K=await O.createProduct(j);const{response:X,data:D}=K;if(X.ok&&D&&D.success)T(`Product ${u?"updated":"created"} successfully!`),setTimeout(()=>{w("/admin/products")},2e3);else{const M=(D==null?void 0:D.message)||`Failed to ${u?"update":"create"} product`;N(M)}}catch(v){console.error("Error saving product:",v),N(`Failed to ${u?"update":"create"} product`)}finally{o(!1)}};return f?e.jsx(W,{children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"spinner-border text-primary mb-3",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})}),e.jsx("p",{className:"text-muted",children:"Loading product data..."})]})})}):e.jsxs(e.Fragment,{children:[e.jsx(V,{title:`${u?"Edit":"Create"} Product | Admin`,description:"Create and manage products in the webstore"}),e.jsx(W,{title:u?"Edit Product":"Create New Product",children:e.jsxs("form",{onSubmit:F,className:"admin-form",children:[P&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),P]}),k&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),k]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:settings-bold",className:"me-2 color-primary-1"}),"Basic Settings"]}),e.jsx("p",{className:"section-descr mb-0",children:"Configure the basic properties of your product"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:link-bold",className:"me-2"}),"Slug (URL)"]}),e.jsx("input",{type:"text",value:n.slug,onChange:i=>J("slug",i.target.value),className:"form-control",placeholder:"product-url-slug"}),e.jsx("small",{className:"form-text text-muted",children:"This will be the URL path for your product (e.g., /webstore/your-slug)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),"Publication Status"]}),e.jsxs("select",{value:n.status,onChange:i=>J("status",i.target.value),className:"form-control",children:[e.jsx("option",{value:"draft",children:"Draft"}),e.jsx("option",{value:"published",children:"Published"})]}),e.jsx("small",{className:"form-text text-muted",children:"Draft products are not visible to the public"})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:dollar-minimalistic-bold",className:"me-2 color-primary-1"}),"Pricing & Demo"]}),e.jsx("p",{className:"section-descr mb-0",children:"Set pricing options and demo URL for your product"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-4 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:code-bold",className:"me-2"}),"Whitelabel Price (EUR)"]}),e.jsx("input",{type:"number",step:"0.01",value:n.whitelabelPrice,onChange:i=>J("whitelabelPrice",i.target.value),className:"form-control",placeholder:"2999.99"}),e.jsx("small",{className:"form-text text-muted",children:"Price for purchasing the source code with commercial license"})]}),e.jsxs("div",{className:"col-md-4 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"me-2"}),"Subscription Price (EUR/month)"]}),e.jsx("input",{type:"number",step:"0.01",value:n.subscriptionPrice,onChange:i=>J("subscriptionPrice",i.target.value),className:"form-control",placeholder:"99.99"}),e.jsx("small",{className:"form-text text-muted",children:"Monthly subscription price for SaaS access"})]}),e.jsxs("div",{className:"col-md-4 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:eye-bold",className:"me-2"}),"Demo URL"]}),e.jsx("input",{type:"url",value:n.demoUrl,onChange:i=>J("demoUrl",i.target.value),className:"form-control",placeholder:"https://demo.example.com"}),e.jsx("small",{className:"form-text text-muted",children:"Link to live demo of the product"})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:gallery-bold",className:"me-2 color-primary-1"}),"Featured Image"]}),e.jsx("p",{className:"section-descr mb-0",children:"Upload a featured image that will be displayed with your product"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:upload-bold",className:"me-2"}),"Upload Images (Max 10)"]}),e.jsx("input",{type:"file",accept:"image/*",multiple:!0,onChange:ae,className:"form-control"}),e.jsx("small",{className:"form-text text-muted",children:"Recommended size: 800x600px. Max file size: 5MB per image. Maximum 10 images."})]}),_.length>0&&e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("h6",{className:"mb-3",children:["Product Images (",_.length,"/10)"]}),e.jsx("div",{className:"row",children:_.map((i,v)=>e.jsx("div",{className:"col-md-4 mb-3",children:e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"position-relative",children:[e.jsx("img",{src:i.preview,alt:`Preview ${v+1}`,className:"card-img-top",style:{height:"200px",objectFit:"cover"},onError:j=>{console.error("Failed to load image:",i.preview),j.target.style.display="none"},onLoad:()=>{console.log("Successfully loaded image:",i.preview)}}),e.jsx("button",{type:"button",className:"btn btn-danger btn-sm position-absolute top-0 end-0 m-2",onClick:()=>le(v),children:"×"}),i.isDisplay&&e.jsx("span",{className:"badge bg-primary position-absolute top-0 start-0 m-2",children:"Display Image"})]}),e.jsxs("div",{className:"card-body",children:[e.jsx("div",{className:"mb-2",children:e.jsx("input",{type:"text",className:"form-control form-control-sm",placeholder:"Alt text",value:i.alt,onChange:j=>r(v,j.target.value)})}),e.jsx("div",{className:"d-flex gap-2",children:!i.isDisplay&&e.jsx("button",{type:"button",className:"btn btn-outline-primary btn-sm",onClick:()=>te(v),children:"Set as Display"})})]})]})},v))})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("i",{className:"mi-globe me-2 color-primary-1"}),"Content (Multi-language)"]}),e.jsx("p",{className:"section-descr mb-0",children:"Create content in multiple languages. At least English content is required."})]})}),e.jsx("div",{className:"language-tabs mb-30",children:E.map(i=>e.jsxs("button",{type:"button",onClick:()=>d(i),className:`language-tab ${s===i?"active":""}`,children:[e.jsx("i",{className:"mi-globe me-2"}),i.toUpperCase(),i==="en"&&e.jsx("span",{className:"ms-1 small",children:"(Required)"})]},i))}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-edit me-2"}),"Title (",s.toUpperCase(),")",s==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx("input",{type:"text",value:((y=n.translations[s])==null?void 0:y.title)||"",onChange:i=>Y(s,"title",i.target.value),className:"form-control",placeholder:"Enter product title",required:s==="en"}),e.jsxs("small",{className:"form-text text-muted",children:["The main title of your product in"," ",s.toUpperCase()]})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-text me-2"}),"Excerpt (",s.toUpperCase(),")"]}),e.jsx("textarea",{value:(($=n.translations[s])==null?void 0:$.excerpt)||"",onChange:i=>Y(s,"excerpt",i.target.value),rows:3,className:"form-control",placeholder:"Brief description of the product"}),e.jsx("small",{className:"form-text text-muted",children:"A short summary that will appear in product listings and social media previews"})]}),e.jsxs("div",{className:"col-12 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:document-text-bold",className:"me-2"}),"Content (",s.toUpperCase(),")",s==="en"&&e.jsx("span",{className:"text-danger ms-1",children:"*"})]}),e.jsx(xe,{content:((G=n.translations[s])==null?void 0:G.content)||"",onChange:i=>Y(s,"content",i),placeholder:"Write your product description here. You can paste formatted text and code snippets with syntax highlighting."}),e.jsxs("small",{className:"form-text text-muted",children:[e.jsx("iconify-icon",{icon:"solar:info-circle-bold",className:"me-1"}),"Rich text editor with syntax highlighting. Paste code snippets and they will be automatically highlighted. Use the toolbar for formatting options."]})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Title (",s.toUpperCase(),")"]}),e.jsx("input",{type:"text",value:((B=n.translations[s])==null?void 0:B.metaTitle)||"",onChange:i=>Y(s,"metaTitle",i.target.value),className:"form-control",placeholder:"SEO title (optional)",maxLength:"60"}),e.jsx("small",{className:"form-text text-muted",children:"SEO title for search engines (max 60 characters)"})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("i",{className:"mi-seo me-2"}),"Meta Description (",s.toUpperCase(),")"]}),e.jsx("textarea",{value:((L=n.translations[s])==null?void 0:L.metaDesc)||"",onChange:i=>Y(s,"metaDesc",i.target.value),rows:3,className:"form-control",placeholder:"SEO description (optional)",maxLength:"160"}),e.jsx("small",{className:"form-text text-muted",children:"SEO description for search engines (max 160 characters)"})]})]})]}),e.jsxs("div",{className:"admin-table mb-40",children:[e.jsx("div",{className:"row mb-30",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("h3",{className:"hs-line-4 font-alt black mb-0",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-2 color-primary-1"}),"Categories & Tags"]}),e.jsx("p",{className:"section-descr mb-0",children:"Organize your product with categories and tags"})]})}),e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"me-2"}),"Categories"]}),e.jsx("div",{className:"categories-grid",children:l&&l.length>0?l.map(i=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`category-${i.id}`,checked:n.categoryIds.includes(i.id),onChange:()=>{const v=n.categoryIds.includes(i.id)?n.categoryIds.filter(j=>j!==i.id):[...n.categoryIds,i.id];J("categoryIds",v)}}),e.jsx("label",{className:"form-check-label",htmlFor:`category-${i.id}`,children:i.name})]},i.id)):e.jsx("p",{className:"text-muted",children:"No categories available"})})]}),e.jsxs("div",{className:"col-md-6 mb-30",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:hashtag-bold",className:"me-2"}),"Tags"]}),e.jsx("div",{className:"tags-grid",children:a&&a.length>0?a.map(i=>e.jsxs("div",{className:"form-check mb-2",children:[e.jsx("input",{className:"form-check-input",type:"checkbox",id:`tag-${i.id}`,checked:n.tagIds.includes(i.id),onChange:()=>{const v=n.tagIds.includes(i.id)?n.tagIds.filter(j=>j!==i.id):[...n.tagIds,i.id];J("tagIds",v)}}),e.jsx("label",{className:"form-check-label",htmlFor:`tag-${i.id}`,children:i.name})]},i.id)):e.jsx("p",{className:"text-muted",children:"No tags available"})})]})]})]}),e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 text-center",children:[e.jsx("button",{type:"submit",className:"btn btn-mod btn-color btn-large btn-round",disabled:C,children:C?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"spinner-border spinner-border-sm me-2"}),u?"Updating...":"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx("i",{className:"mi-check me-2"}),u?"Update Product":"Create Product"]})}),e.jsx("div",{className:"mt-3",children:e.jsxs("small",{className:"text-muted",children:[e.jsx("i",{className:"mi-info me-1"}),u?"Changes will be saved and the product will be updated":"The product will be created and added to your webstore"]})})]})})]})})]})},He=Object.freeze(Object.defineProperty({__proto__:null,default:Ae},Symbol.toStringTag,{value:"Module"})),Ie=()=>{var l;const{t:x}=ne(),[h,w]=c.useState(!0),[S,u]=c.useState(""),[b,f]=c.useState("last30days"),[m,C]=c.useState("all"),[o,P]=c.useState(null),[N,k]=c.useState([]),T=[{value:"lastday",label:"Last day"},{value:"lastweek",label:"Last week"},{value:"last14days",label:"Last 14 days"},{value:"last30days",label:"Last 30 days"},{value:"last2months",label:"Last 2 months"},{value:"last4months",label:"Last 4 months"},{value:"last6months",label:"Last 6 months"}],E=[{value:"all",label:"All languages",flag:"🌐"},{value:"en",label:"English",flag:"🇬🇧"},{value:"et",label:"Estonian",flag:"🇪🇪"},{value:"fi",label:"Finnish",flag:"🇫🇮"},{value:"de",label:"German",flag:"🇩🇪"},{value:"sv",label:"Swedish",flag:"🇸🇪"}];c.useEffect(()=>{(async()=>{try{if(w(!0),u(""),!localStorage.getItem("adminToken")){u("Authentication required. Please log in to access this page."),w(!1);return}const[g,s]=await Promise.all([O.getBlogAnalytics(b,m),O.getBlogPostsAnalytics(b,m)]);if(g.response.ok&&g.data)P(g.data.data||g.data);else{if(console.error("Analytics API failed:",g.response.status,g.response.statusText),g.response.status===401||g.response.status===403){u("Authentication failed. Please log in again."),localStorage.removeItem("adminToken");return}u("Failed to load analytics data")}s.response.ok&&s.data?k(s.data.data||s.data):(console.error("Posts analytics API failed:",s.response.status,s.response.statusText),k([]))}catch(a){console.error("Error loading analytics data:",a),a.message&&a.message.includes("fetch")?u("Failed to connect to the server. Please check if the backend is running."):u("Failed to load analytics data. Please try again.")}finally{w(!1)}})()},[b,m]);const n=p=>{f(p)},A=p=>{C(p)};return h?e.jsxs(W,{title:"Blog Analytics",children:[e.jsx(V,{title:"Blog Analytics - Admin",description:"Blog analytics and performance metrics"}),e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsx("div",{className:"spinner-border text-primary",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})})]}):e.jsxs(W,{title:"Analytics Dashboard",children:[e.jsx(V,{title:"Analytics Dashboard - Admin",description:"Blog analytics, conversion tracking, and page performance metrics"}),e.jsxs("div",{className:"admin-content",children:[e.jsx("div",{className:"admin-header mb-4",children:e.jsx("div",{className:"d-flex justify-content-between align-items-center",children:e.jsxs("div",{children:[e.jsx("h1",{className:"admin-title mb-2",children:"Analytics Dashboard"}),e.jsxs("p",{className:"admin-subtitle text-muted mb-0",children:["Track and analyze your blog performance, conversions, and page analytics."," ",e.jsx("a",{href:"https://developers.google.com/analytics/devguides/collection/ga4",target:"_blank",rel:"noopener noreferrer",className:"text-primary",children:"Learn more"})]})]})})}),S&&e.jsxs("div",{className:"alert alert-danger mb-4",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-bold",className:"me-2"}),S]}),e.jsxs("div",{className:"row mb-4",children:[e.jsx("div",{className:"col-md-6",children:e.jsx(ge,{options:T,value:b,onChange:n,comparedPeriod:o==null?void 0:o.comparedPeriod})}),e.jsx("div",{className:"col-md-6",children:e.jsx(be,{options:E,value:m,onChange:A})})]}),o&&e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsx(je,{data:o.overview,selectedLanguage:m})})}),e.jsxs("div",{className:"row mb-4",children:[e.jsx("div",{className:"col-lg-8 col-12 mb-4",children:o&&e.jsx(pe,{data:o.chartData,timeRange:b,selectedLanguage:m})}),e.jsx("div",{className:"col-lg-4 col-12 mb-4",children:o&&e.jsx(Ne,{data:o.heatmapData,title:`Post views by time of day${m!=="all"?` (${(l=E.find(p=>p.value===m))==null?void 0:l.label})`:""}`,selectedLanguage:m})})]}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsx(fe,{data:N,loading:h,timeRange:b,selectedLanguage:m})})}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"card-header",children:[e.jsxs("h3",{className:"card-title mb-0",children:[e.jsx("iconify-icon",{icon:"solar:target-bold",className:"me-2"}),"Conversion Analytics"]}),e.jsx("p",{className:"text-muted mb-0 mt-1",children:"Track Business Comanager CTA performance and conversion metrics"})]}),e.jsx("div",{className:"card-body",children:e.jsx(ve,{timeRange:b,selectedLanguage:m})})]})})}),e.jsx("div",{className:"row mb-4",children:e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"card-header",children:[e.jsxs("h3",{className:"card-title mb-0",children:[e.jsx("iconify-icon",{icon:"solar:document-bold",className:"me-2"}),"Static Pages Analytics"]}),e.jsx("p",{className:"text-muted mb-0 mt-1",children:"Performance metrics for all application pages"})]}),e.jsx("div",{className:"card-body",children:e.jsx(ye,{timeRange:b,selectedLanguage:m})})]})})})]})]})},We=Object.freeze(Object.defineProperty({__proto__:null,default:Ie},Symbol.toStringTag,{value:"Module"})),Te=()=>{Q();const[x,h]=c.useState([]),[w,S]=c.useState(!0),[u,b]=c.useState(""),[f,m]=c.useState(""),[C,o]=c.useState(!1),[P,N]=c.useState(null),[k,T]=c.useState({name:"",description:"",color:"#4567e7"});c.useEffect(()=>{E()},[]);const E=async()=>{try{S(!0);const{response:s,data:d}=await O.getCategories();d.success?h(d.data||[]):b(d.message||"Failed to load categories")}catch(s){console.error("Load categories error:",s),b("Network error. Please try again.")}finally{S(!1)}},n=(s,d)=>{T(t=>({...t,[s]:d}))},A=async s=>{s.preventDefault(),b(""),m("");try{let d,t;P?{response:d,data:t}=await O.updateCategory(P.id,k):{response:d,data:t}=await O.createCategory(k),t.success?(m(P?"Category updated successfully!":"Category created successfully!"),o(!1),N(null),T({name:"",description:"",color:"#4567e7"}),E()):b(t.message||"Failed to save category")}catch(d){console.error("Save category error:",d),b("Network error. Please try again.")}},l=s=>{N(s),T({name:s.name,description:s.description||"",color:s.color||"#4567e7"}),o(!0)},p=async s=>{if(window.confirm("Are you sure you want to delete this category? This action cannot be undone."))try{const{response:d,data:t}=await O.deleteCategory(s);t.success?(m("Category deleted successfully!"),E()):b(t.message||"Failed to delete category")}catch(d){console.error("Delete category error:",d),b("Network error. Please try again.")}},a=()=>{N(null),T({name:"",description:"",color:"#4567e7"}),o(!0)},g=()=>{o(!1),N(null),T({name:"",description:"",color:"#4567e7"}),b("")};return e.jsxs(e.Fragment,{children:[e.jsx(V,{title:"Manage Categories - Admin",description:"Manage blog categories in the admin panel",noIndex:!0}),e.jsxs(W,{title:"Categories",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Organize your blog posts with categories. Create, edit, and manage content categories."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:a,className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Category"]})})]})}),u&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),u]}),f&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),f]}),e.jsx("div",{className:"admin-table",children:w?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading categories..."})]}):x.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No categories found"}),e.jsx("p",{className:"section-descr mb-30",children:"Create your first category to start organizing your blog posts."}),e.jsxs("button",{onClick:a,className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Category"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Category"}),e.jsx("th",{children:"Description"}),e.jsx("th",{children:"Posts"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:x.map(s=>{var d;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"rounded me-3",style:{width:"20px",height:"20px",backgroundColor:s.color||"#4567e7"}}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:s.name}),e.jsxs("small",{className:"text-muted",children:["/",s.slug]})]})]})}),e.jsx("td",{children:e.jsx("span",{className:"text-muted",children:s.description||"No description"})}),e.jsx("td",{children:e.jsxs("span",{className:"badge bg-secondary",children:[((d=s._count)==null?void 0:d.posts)||0," posts"]})}),e.jsx("td",{children:new Date(s.createdAt).toLocaleDateString()}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>l(s),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>p(s.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},s.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:x.map(s=>{var d;return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"rounded me-3",style:{width:"30px",height:"30px",backgroundColor:s.color||"#4567e7"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:s.name}),e.jsxs("small",{className:"text-muted",children:["/",s.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Description"}),e.jsx("small",{children:s.description||"No description"})]}),e.jsxs("div",{className:"col-6 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Posts"}),e.jsxs("span",{className:"badge bg-secondary",children:[((d=s._count)==null?void 0:d.posts)||0," posts"]})]}),e.jsxs("div",{className:"col-12 col-sm-4 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:new Date(s.createdAt).toLocaleDateString()})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>l(s),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>p(s.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},s.id)})})})]})}),C&&e.jsx("div",{className:"modal-overlay",onClick:g,children:e.jsxs("div",{className:"modal-content",onClick:s=>s.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h4",{className:"modal-title",children:[e.jsx("iconify-icon",{icon:"solar:folder-bold",className:"me-2"}),P?"Edit Category":"Create New Category"]}),e.jsx("button",{type:"button",className:"modal-close",onClick:g,children:e.jsx("iconify-icon",{icon:"solar:close-circle-bold"})})]}),e.jsxs("form",{onSubmit:A,children:[e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"row",children:[e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-2"}),"Category Name *"]}),e.jsx("input",{type:"text",value:k.name,onChange:s=>n("name",s.target.value),className:"form-control",placeholder:"Enter category name",required:!0})]}),e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:text-bold",className:"me-2"}),"Description"]}),e.jsx("textarea",{value:k.description,onChange:s=>n("description",s.target.value),className:"form-control",rows:3,placeholder:"Brief description of this category"})]}),e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:palette-bold",className:"me-2"}),"Color"]}),e.jsxs("div",{className:"d-flex align-items-center gap-3",children:[e.jsx("input",{type:"color",value:k.color,onChange:s=>n("color",s.target.value),className:"form-control form-control-color",style:{width:"60px",height:"40px"}}),e.jsx("input",{type:"text",value:k.color,onChange:s=>n("color",s.target.value),className:"form-control",placeholder:"#4567e7"})]}),e.jsx("small",{className:"form-text text-muted",children:"Choose a color to represent this category"})]})]})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",onClick:g,className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsxs("button",{type:"submit",className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),P?"Update Category":"Create Category"]})]})]})]})})]})]})},Je=Object.freeze(Object.defineProperty({__proto__:null,default:Te},Symbol.toStringTag,{value:"Module"})),De=()=>{Q();const[x,h]=c.useState([]),[w,S]=c.useState(!0),[u,b]=c.useState(""),[f,m]=c.useState(""),[C,o]=c.useState(!1),[P,N]=c.useState(null),[k,T]=c.useState({name:""});c.useEffect(()=>{E()},[]);const E=async()=>{try{S(!0);const{response:s,data:d}=await O.getTags();d.success?h(d.data||[]):b(d.message||"Failed to load tags")}catch(s){console.error("Load tags error:",s),b("Network error. Please try again.")}finally{S(!1)}},n=(s,d)=>{T(t=>({...t,[s]:d}))},A=async s=>{s.preventDefault(),b(""),m("");try{let d,t;P?{response:d,data:t}=await O.updateTag(P.id,k):{response:d,data:t}=await O.createTag(k),t.success?(m(P?"Tag updated successfully!":"Tag created successfully!"),o(!1),N(null),T({name:""}),E()):b(t.message||"Failed to save tag")}catch(d){console.error("Save tag error:",d),b("Network error. Please try again.")}},l=s=>{N(s),T({name:s.name}),o(!0)},p=async s=>{if(window.confirm("Are you sure you want to delete this tag? This action cannot be undone."))try{const{response:d,data:t}=await O.deleteTag(s);t.success?(m("Tag deleted successfully!"),E()):b(t.message||"Failed to delete tag")}catch(d){console.error("Delete tag error:",d),b("Network error. Please try again.")}},a=()=>{N(null),T({name:""}),o(!0)},g=()=>{o(!1),N(null),T({name:""}),b("")};return e.jsxs(e.Fragment,{children:[e.jsx(V,{title:"Manage Tags - Admin",description:"Manage blog tags in the admin panel",noIndex:!0}),e.jsxs(W,{title:"Tags",children:[e.jsx("div",{className:"mb-30",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 col-lg-6 mb-3 mb-lg-0",children:e.jsx("p",{className:"section-descr mb-0",children:"Tag your blog posts for better organization and discoverability. Create and manage content tags."})}),e.jsx("div",{className:"col-12 col-lg-6 text-lg-end",children:e.jsxs("button",{onClick:a,className:"btn btn-mod btn-color btn-round w-100 w-lg-auto",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"New Tag"]})})]})}),u&&e.jsxs("div",{className:"alert alert-danger mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:danger-triangle-bold",className:"me-2"}),u]}),f&&e.jsxs("div",{className:"alert alert-success mb-30",role:"alert",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),f]}),e.jsx("div",{className:"admin-table",children:w?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:refresh-bold",className:"fa-2x color-primary-1 mb-20",style:{animation:"spin 1s linear infinite"}}),e.jsx("div",{className:"hs-line-4 font-alt black",children:"Loading tags..."})]}):x.length===0?e.jsxs("div",{className:"text-center py-60",style:{padding:"40px 20px"},children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"fa-3x color-gray-light-1 mb-20"}),e.jsx("div",{className:"hs-line-4 font-alt black mb-10",children:"No tags found"}),e.jsx("p",{className:"section-descr mb-30",children:"Create your first tag to start organizing your blog posts."}),e.jsxs("button",{onClick:a,className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:add-circle-bold",className:"me-2"}),"Create First Tag"]})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"d-none d-lg-block",children:e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Tag Name"}),e.jsx("th",{children:"Posts"}),e.jsx("th",{children:"Created"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:x.map(s=>{var d;return e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-3 color-primary-1"}),e.jsxs("div",{children:[e.jsx("div",{className:"fw-bold",children:s.name}),e.jsxs("small",{className:"text-muted",children:["/",s.slug]})]})]})}),e.jsx("td",{children:e.jsxs("span",{className:"badge bg-secondary",children:[((d=s._count)==null?void 0:d.posts)||0," posts"]})}),e.jsx("td",{children:new Date(s.createdAt).toLocaleDateString()}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group",role:"group",children:[e.jsx("button",{onClick:()=>l(s),className:"btn btn-sm btn-outline-primary",title:"Edit",children:e.jsx("iconify-icon",{icon:"solar:pen-bold"})}),e.jsx("button",{onClick:()=>p(s.id),className:"btn btn-sm btn-outline-danger",title:"Delete",children:e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold"})})]})})]},s.id)})})]})})}),e.jsx("div",{className:"d-lg-none",children:e.jsx("div",{className:"row g-3",children:x.map(s=>{var d;return e.jsx("div",{className:"col-12",children:e.jsx("div",{className:"card border-0 shadow-sm",children:e.jsx("div",{className:"card-body p-3",children:e.jsxs("div",{className:"row align-items-center",children:[e.jsx("div",{className:"col-12 mb-2",children:e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-3 color-primary-1",style:{fontSize:"1.5rem"}}),e.jsxs("div",{className:"flex-grow-1",children:[e.jsx("h6",{className:"mb-1 fw-bold",children:s.name}),e.jsxs("small",{className:"text-muted",children:["/",s.slug]})]})]})}),e.jsxs("div",{className:"col-6 col-sm-6 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Posts"}),e.jsxs("span",{className:"badge bg-secondary",children:[((d=s._count)==null?void 0:d.posts)||0," posts"]})]}),e.jsxs("div",{className:"col-6 col-sm-6 mb-2",children:[e.jsx("small",{className:"text-muted d-block",children:"Created"}),e.jsx("small",{children:new Date(s.createdAt).toLocaleDateString()})]}),e.jsx("div",{className:"col-12",children:e.jsxs("div",{className:"d-flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>l(s),className:"btn btn-sm btn-outline-primary flex-fill",title:"Edit",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-1"}),"Edit"]}),e.jsxs("button",{onClick:()=>p(s.id),className:"btn btn-sm btn-outline-danger flex-fill",title:"Delete",children:[e.jsx("iconify-icon",{icon:"solar:trash-bin-trash-bold",className:"me-1"}),"Delete"]})]})})]})})})},s.id)})})})]})}),C&&e.jsx("div",{className:"modal-overlay",onClick:g,children:e.jsxs("div",{className:"modal-content",onClick:s=>s.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h4",{className:"modal-title",children:[e.jsx("iconify-icon",{icon:"solar:tag-bold",className:"me-2"}),P?"Edit Tag":"Create New Tag"]}),e.jsx("button",{type:"button",className:"modal-close",onClick:g,children:e.jsx("iconify-icon",{icon:"solar:close-circle-bold"})})]}),e.jsxs("form",{onSubmit:A,children:[e.jsx("div",{className:"modal-body",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12 mb-20",children:[e.jsxs("label",{className:"form-label",children:[e.jsx("iconify-icon",{icon:"solar:pen-bold",className:"me-2"}),"Tag Name *"]}),e.jsx("input",{type:"text",value:k.name,onChange:s=>n("name",s.target.value),className:"form-control",placeholder:"Enter tag name",required:!0}),e.jsx("small",{className:"form-text text-muted",children:'Keep it short and descriptive (e.g., "JavaScript", "Tutorial", "News")'})]})})}),e.jsxs("div",{className:"modal-footer",children:[e.jsx("button",{type:"button",onClick:g,className:"btn btn-mod btn-gray btn-round me-3",children:"Cancel"}),e.jsxs("button",{type:"submit",className:"btn btn-mod btn-color btn-round",children:[e.jsx("iconify-icon",{icon:"solar:check-circle-bold",className:"me-2"}),P?"Update Tag":"Create Tag"]})]})]})]})})]})]})},Ge=Object.freeze(Object.defineProperty({__proto__:null,default:De},Symbol.toStringTag,{value:"Module"})),Ee=async(x={})=>{try{const h=new URLSearchParams;x.page&&h.append("page",x.page),x.limit&&h.append("limit",x.limit),x.status&&h.append("status",x.status),x.search&&h.append("search",x.search),x.blogPostId&&h.append("blogPostId",x.blogPostId);const{response:w,data:S}=await re(`/admin/comments?${h}`);if(!w.ok)throw new Error(`HTTP error! status: ${w.status}`);return S}catch(h){throw console.error("Get admin comments error:",h),h}},Fe=async x=>{try{const{response:h,data:w}=await re(`/admin/comments/${x}/approve`,{method:"PATCH"});if(!h.ok)throw new Error(`HTTP error! status: ${h.status}`);return w}catch(h){throw console.error("Approve comment error:",h),h}},Le=async x=>{try{const{response:h,data:w}=await re(`/admin/comments/${x}/reject`,{method:"PATCH"});if(!h.ok)throw new Error(`HTTP error! status: ${h.status}`);return w}catch(h){throw console.error("Reject comment error:",h),h}},Ue=async x=>{try{const{response:h,data:w}=await re(`/admin/comments/${x}`,{method:"DELETE"});if(!h.ok)throw new Error(`HTTP error! status: ${h.status}`);return w}catch(h){throw console.error("Delete comment error:",h),h}};function $e(){const[x,h]=c.useState([]),[w,S]=c.useState(!0),[u,b]=c.useState(""),[f,m]=c.useState(1),[C,o]=c.useState(1),[P,N]=c.useState("all"),[k,T]=c.useState(""),[E,n]=c.useState({total:0,pending:0,approved:0}),A=async()=>{try{S(!0);const t=await Ee({page:f,limit:10,status:P,search:k});if(t.success){h(t.data.comments),o(t.data.pagination.pages);const I=t.data.pagination.total;n(_=>({..._,total:I}))}}catch(t){b("Failed to fetch comments"),console.error("Fetch comments error:",t)}finally{S(!1)}};c.useEffect(()=>{A()},[f,P,k]);const l=async t=>{try{await Fe(t),A()}catch{b("Failed to approve comment")}},p=async t=>{try{await Le(t),A()}catch{b("Failed to reject comment")}},a=async t=>{if(window.confirm("Are you sure you want to delete this comment? This action cannot be undone."))try{await Ue(t),A()}catch{b("Failed to delete comment")}},g=t=>{t.preventDefault(),m(1),A()},s=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),d=(t,I=100)=>t.length>I?t.substring(0,I)+"...":t;return e.jsx(W,{children:e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-12",children:[e.jsxs("div",{className:"d-flex justify-content-between align-items-center mb-4",children:[e.jsx("h1",{className:"h3 mb-0",children:"Comment Management"}),e.jsxs("div",{className:"d-flex gap-3",children:[e.jsxs("div",{className:"badge bg-primary",children:["Total: ",E.total]}),e.jsxs("div",{className:"badge bg-warning",children:["Pending: ",E.pending]}),e.jsxs("div",{className:"badge bg-success",children:["Approved: ",E.approved]})]})]}),u&&e.jsx("div",{className:"alert alert-danger",role:"alert",children:u}),e.jsx("div",{className:"card mb-4",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"row g-3",children:[e.jsxs("div",{className:"col-md-4",children:[e.jsx("label",{className:"form-label",children:"Status Filter"}),e.jsxs("select",{className:"form-select",value:P,onChange:t=>{N(t.target.value),m(1)},children:[e.jsx("option",{value:"all",children:"All Comments"}),e.jsx("option",{value:"pending",children:"Pending Approval"}),e.jsx("option",{value:"approved",children:"Approved"})]})]}),e.jsxs("div",{className:"col-md-8",children:[e.jsx("label",{className:"form-label",children:"Search"}),e.jsxs("form",{onSubmit:g,className:"d-flex",children:[e.jsx("input",{type:"text",className:"form-control",placeholder:"Search by author name, email, or content...",value:k,onChange:t=>T(t.target.value)}),e.jsx("button",{type:"submit",className:"btn btn-primary ms-2",children:"Search"})]})]})]})})}),e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[w?e.jsx("div",{className:"text-center py-4",children:e.jsx("div",{className:"spinner-border",role:"status",children:e.jsx("span",{className:"visually-hidden",children:"Loading..."})})}):x.length===0?e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-muted",children:"No comments found."})}):e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table table-hover",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Author"}),e.jsx("th",{children:"Email"}),e.jsx("th",{children:"Content"}),e.jsx("th",{children:"Blog Post"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:x.map(t=>{var I;return e.jsxs("tr",{children:[e.jsxs("td",{children:[e.jsx("strong",{children:t.author}),t.website&&e.jsx("div",{children:e.jsx("small",{children:e.jsx("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",children:t.website})})})]}),e.jsx("td",{children:e.jsx("small",{className:"text-muted",children:t.email})}),e.jsxs("td",{children:[e.jsx("div",{title:t.content,children:d(t.content)}),t.parent&&e.jsxs("small",{className:"text-muted",children:["Reply to: ",t.parent.author]})]}),e.jsx("td",{children:e.jsx(ue,{to:`/blog-single/${t.blogPost.slug}`,className:"text-decoration-none",target:"_blank",children:((I=t.blogPost.translations[0])==null?void 0:I.title)||"Untitled"})}),e.jsx("td",{children:e.jsx("span",{className:`badge ${t.approved?"bg-success":"bg-warning"}`,children:t.approved?"Approved":"Pending"})}),e.jsx("td",{children:e.jsx("small",{children:s(t.createdAt)})}),e.jsx("td",{children:e.jsxs("div",{className:"btn-group btn-group-sm",children:[t.approved?e.jsx("button",{className:"btn btn-warning",onClick:()=>p(t.id),title:"Hide",children:e.jsx("i",{className:"mi-eye-off"})}):e.jsx("button",{className:"btn btn-success",onClick:()=>l(t.id),title:"Approve",children:e.jsx("i",{className:"mi-check"})}),e.jsx("button",{className:"btn btn-danger",onClick:()=>a(t.id),title:"Delete",children:e.jsx("i",{className:"mi-trash"})})]})})]},t.id)})})]})}),C>1&&e.jsx("nav",{className:"mt-4",children:e.jsxs("ul",{className:"pagination justify-content-center",children:[e.jsx("li",{className:`page-item ${f===1?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>m(f-1),disabled:f===1,children:"Previous"})}),[...Array(C)].map((t,I)=>e.jsx("li",{className:`page-item ${f===I+1?"active":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>m(I+1),children:I+1})},I+1)),e.jsx("li",{className:`page-item ${f===C?"disabled":""}`,children:e.jsx("button",{className:"page-link",onClick:()=>m(f+1),disabled:f===C,children:"Next"})})]})})]})})]})})})})}const Ve=Object.freeze(Object.defineProperty({__proto__:null,default:$e},Symbol.toStringTag,{value:"Module"}));export{Me as A,Re as a,Be as b,ze as c,qe as d,He as e,We as f,Je as g,Ge as h,Ve as p};
//# sourceMappingURL=pages-admin-DnFYe5ub.js.map
