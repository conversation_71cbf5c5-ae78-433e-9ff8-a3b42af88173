import React, { useState, useRef, useEffect } from "react";

const LanguageSelector = ({ options, value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const selectedOption = options.find((option) => option.value === value);

  const handleOptionClick = (optionValue) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className="language-selector-wrapper d-flex justify-content-end">
      {/* Language Dropdown */}
      <div className="position-relative" ref={dropdownRef}>
        <button
          className="btn btn-outline-secondary d-flex align-items-center gap-2"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
        >
          <span className="fs-5">{selectedOption?.flag || "🌐"}</span>
          <span>{selectedOption?.label || "Select language"}</span>
          <iconify-icon
            icon="solar:alt-arrow-down-bold"
            className={`transition-transform ${isOpen ? "rotate-180" : ""}`}
          ></iconify-icon>
        </button>

        {isOpen && (
          <div
            className="dropdown-menu show position-absolute mt-1 shadow-lg border-0 bg-white rounded-3"
            style={{ right: 0, minWidth: "200px" }}
          >
            {options.map((option) => (
              <button
                key={option.value}
                className={`dropdown-item px-3 py-2 d-flex align-items-center gap-2 text-nowrap ${
                  option.value === value
                    ? "active bg-primary text-white"
                    : "text-dark"
                }`}
                onClick={() => handleOptionClick(option.value)}
              >
                <span className="fs-6">{option.flag}</span>
                <span>{option.label}</span>
                {option.value === value && (
                  <iconify-icon
                    icon="solar:check-circle-bold"
                    className="ms-auto"
                  ></iconify-icon>
                )}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LanguageSelector;
