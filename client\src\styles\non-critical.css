/* Non-Critical CSS - Below the fold content */
/* This file contains styles that can be loaded after first paint */
/* NO HEAVY IMPORTS - Heavy CSS files are loaded via loadCSS.js to prevent bundling */

/* Third-party CSS that needs to be bundled */
@import "swiper/css";
@import "jarallax/dist/jarallax.min.css";
@import "swiper/css/effect-fade";
@import "react-modal-video/css/modal-video.css";
@import "photoswipe/dist/photoswipe.css";
@import "tippy.js/dist/tippy.css";

/* Custom styles that are not critical */
@import "./module-buttons.css";
@import "./grayscale-effect.css";
@import "./languageSelector.css";
@import "./gdpr.css";
@import "./analytics.css";
@import "./tiptap.css";

/* Global styles that can be loaded after first paint */
html,
body {
  background-color: #000000 !important;
  background: #000000 !important;
}

#root {
  background-color: #000000 !important;
  min-height: 100vh;
}

/* Prevent white background on overscroll (macOS bounce effect) */
body::before {
  content: "";
  position: fixed;
  top: -100vh;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000000;
  z-index: -1;
}

body::after {
  content: "";
  position: fixed;
  bottom: -100vh;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000000;
  z-index: -1;
}

/* Service cards and other non-critical styles */
.service-card-container {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card-container:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  background: rgba(255, 255, 255, 0.08);
}

/* All other non-critical styles... */
