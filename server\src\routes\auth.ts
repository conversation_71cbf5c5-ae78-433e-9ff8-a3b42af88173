import express from 'express';
import { login, register, getMe, logout } from '../controllers/authController';
import { authenticate, authorize } from '../middleware/auth';

const router = express.Router();

// @route   POST /api/auth/login
router.post('/login', login);

// @route   POST /api/auth/register (admin only for now)
router.post('/register', authenticate, authorize('ADMIN'), register);

// @route   GET /api/auth/me
router.get('/me', authenticate, getMe);

// @route   POST /api/auth/logout
router.post('/logout', authenticate, logout);

export default router;
