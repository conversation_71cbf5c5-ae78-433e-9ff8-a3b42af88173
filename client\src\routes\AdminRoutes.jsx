import React, { Suspense } from "react";
import { Routes, Route } from "react-router-dom";

// Simple loading component for admin routes
const AdminPageLoader = () => (
  <div
    className="page-loader"
    style={{
      position: "fixed",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "#1a1a1a",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 9999,
    }}
  >
    <div
      className="loader"
      style={{
        width: "40px",
        height: "40px",
        border: "4px solid #333",
        borderTop: "4px solid #fff",
        borderRadius: "50%",
        animation: "spin 1s linear infinite",
      }}
    ></div>
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

// Lazy load all admin components to create separate bundle
const AdminLogin = React.lazy(() => import("@/pages/AdminLogin"));
const AdminDashboard = React.lazy(() => import("@/pages/AdminDashboard"));
const AdminBlogPosts = React.lazy(() => import("@/pages/AdminBlogPosts"));
const AdminBlogEditor = React.lazy(() => import("@/pages/AdminBlogEditor"));
const AdminProducts = React.lazy(() => import("@/pages/AdminProducts"));
const AdminProductEditor = React.lazy(() =>
  import("@/pages/AdminProductEditor")
);
const AdminBlogAnalytics = React.lazy(() =>
  import("@/pages/AdminBlogAnalytics")
);
const AdminCategories = React.lazy(() => import("@/pages/AdminCategories"));
const AdminTags = React.lazy(() => import("@/pages/AdminTags"));
const AdminComments = React.lazy(() => import("@/pages/admin/comments/page"));

/**
 * Admin Routes Component
 *
 * This component contains all admin-related routes and will be code-split
 * into a separate bundle, reducing the main bundle size by ~593KB.
 *
 * Benefits:
 * - Main bundle loads faster for public users
 * - Admin functionality loads only when needed
 * - Zero impact on public page performance
 */
export default function AdminRoutes() {
  return (
    <Suspense fallback={<AdminPageLoader />}>
      <Routes>
        {/* Admin authentication */}
        <Route path="/admin" element={<AdminLogin />} />

        {/* Admin dashboard and management */}
        <Route path="/admin/dashboard" element={<AdminDashboard />} />

        {/* Blog management */}
        <Route path="/admin/posts" element={<AdminBlogPosts />} />
        <Route path="/admin/blog/new" element={<AdminBlogEditor />} />
        <Route path="/admin/blog/edit/:id" element={<AdminBlogEditor />} />

        {/* Product management */}
        <Route path="/admin/products" element={<AdminProducts />} />
        <Route path="/admin/products/new" element={<AdminProductEditor />} />
        <Route
          path="/admin/products/edit/:id"
          element={<AdminProductEditor />}
        />

        {/* Analytics and content management */}
        <Route path="/admin/analytics" element={<AdminBlogAnalytics />} />
        <Route path="/admin/categories" element={<AdminCategories />} />
        <Route path="/admin/tags" element={<AdminTags />} />
        <Route path="/admin/comments" element={<AdminComments />} />
      </Routes>
    </Suspense>
  );
}
