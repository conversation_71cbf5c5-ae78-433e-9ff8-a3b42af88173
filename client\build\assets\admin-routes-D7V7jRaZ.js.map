{"version": 3, "mappings": ";ojCAIMA,EAAkB,IACtBC,EAAA,KAAC,OACC,UAAU,cACV,MAAO,CACL,SAAU,QACV,IAAK,EACL,KAAM,EACN,MAAO,OACP,OAAQ,OACR,gBAAiB,UACjB,QAAS,OACT,eAAgB,SAChB,WAAY,SACZ,OAAQ,IACV,EAEA,UAAAC,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,OACP,OAAQ,OACR,OAAQ,iBACR,UAAW,iBACX,aAAc,MACd,UAAW,0BACb,CACD,QACA,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,KAKN,IACJ,EAIIC,EAAaC,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAoB,OAAAC,KAAA,GAAC,uCAC1DC,EAAiBH,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAwB,OAAAC,KAAA,GAAC,uCAClEE,EAAiBJ,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAwB,OAAAC,KAAA,GAAC,uCAClEG,EAAkBL,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAyB,OAAAC,KAAA,GAAC,uCACpEI,EAAgBN,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAuB,OAAAC,KAAA,GAAC,uCAChEK,EAAqBP,EAAM,KAAK,IACpCC,EAAA,WAAO,2BAA4B,OAAAC,KAAA,wCACrC,EACMM,EAAqBR,EAAM,KAAK,IACpCC,EAAA,WAAO,2BAA4B,OAAAC,KAAA,wCACrC,EACMO,EAAkBT,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAyB,OAAAC,KAAA,GAAC,uCACpEQ,EAAYV,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAAmB,OAAAC,KAAA,GAAC,uCACxDS,EAAgBX,EAAM,KAAK,IAAMC,EAAA,WAAO,2BAA6B,OAAAC,KAAA,wCAAC,EAa5E,SAAwBU,GAAc,CACpC,aACGC,WAAS,gBAAWjB,EAAgB,IACnC,gBAACkB,EAEC,WAAAhB,MAACiB,GAAM,KAAK,SAAS,QAASjB,MAACC,GAAW,GAAI,QAG7CgB,EAAM,MAAK,mBAAmB,QAASjB,MAACK,GAAe,GAAI,QAG3DY,EAAM,MAAK,eAAe,QAASjB,MAACM,GAAe,GAAI,QACvDW,EAAM,MAAK,kBAAkB,QAASjB,MAACO,GAAgB,GAAI,QAC3DU,EAAM,MAAK,uBAAuB,QAASjB,MAACO,GAAgB,GAAI,QAGhEU,EAAM,MAAK,kBAAkB,QAASjB,MAACQ,GAAc,GAAI,QACzDS,EAAM,MAAK,sBAAsB,QAASjB,MAACS,GAAmB,GAAI,EACnET,EAAA,IAACiB,EAAA,CACC,KAAK,2BACL,cAAUR,EAAmB,IAC/B,QAGCQ,EAAM,MAAK,mBAAmB,QAASjB,MAACU,GAAmB,GAAI,QAC/DO,EAAM,MAAK,oBAAoB,QAASjB,MAACW,GAAgB,GAAI,QAC7DM,EAAM,MAAK,cAAc,QAASjB,MAACY,GAAU,GAAI,QACjDK,EAAM,MAAK,kBAAkB,QAASjB,EAAA,IAACa,IAAc,CAAI,IAC5D,CACF,EAEJ", "names": ["AdminPageLoader", "jsxs", "jsx", "AdminLogin", "React", "__vitePreload", "n", "AdminDashboard", "AdminBlogPosts", "AdminBlogEditor", "AdminProducts", "AdminProductEditor", "AdminBlogAnalytics", "AdminCategories", "AdminTags", "AdminComments", "AdminRoutes", "Suspense", "Routes", "Route"], "ignoreList": [], "sources": ["../../src/routes/AdminRoutes.jsx"], "sourcesContent": ["import React, { Suspense } from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\n\n// Simple loading component for admin routes\nconst AdminPageLoader = () => (\n  <div\n    className=\"page-loader\"\n    style={{\n      position: \"fixed\",\n      top: 0,\n      left: 0,\n      width: \"100%\",\n      height: \"100%\",\n      backgroundColor: \"#1a1a1a\",\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      zIndex: 9999,\n    }}\n  >\n    <div\n      className=\"loader\"\n      style={{\n        width: \"40px\",\n        height: \"40px\",\n        border: \"4px solid #333\",\n        borderTop: \"4px solid #fff\",\n        borderRadius: \"50%\",\n        animation: \"spin 1s linear infinite\",\n      }}\n    ></div>\n    <style>{`\n      @keyframes spin {\n        0% { transform: rotate(0deg); }\n        100% { transform: rotate(360deg); }\n      }\n    `}</style>\n  </div>\n);\n\n// Lazy load all admin components to create separate bundle\nconst AdminLogin = React.lazy(() => import(\"@/pages/AdminLogin\"));\nconst AdminDashboard = React.lazy(() => import(\"@/pages/AdminDashboard\"));\nconst AdminBlogPosts = React.lazy(() => import(\"@/pages/AdminBlogPosts\"));\nconst AdminBlogEditor = React.lazy(() => import(\"@/pages/AdminBlogEditor\"));\nconst AdminProducts = React.lazy(() => import(\"@/pages/AdminProducts\"));\nconst AdminProductEditor = React.lazy(() =>\n  import(\"@/pages/AdminProductEditor\")\n);\nconst AdminBlogAnalytics = React.lazy(() =>\n  import(\"@/pages/AdminBlogAnalytics\")\n);\nconst AdminCategories = React.lazy(() => import(\"@/pages/AdminCategories\"));\nconst AdminTags = React.lazy(() => import(\"@/pages/AdminTags\"));\nconst AdminComments = React.lazy(() => import(\"@/pages/admin/comments/page\"));\n\n/**\n * Admin Routes Component\n *\n * This component contains all admin-related routes and will be code-split\n * into a separate bundle, reducing the main bundle size by ~593KB.\n *\n * Benefits:\n * - Main bundle loads faster for public users\n * - Admin functionality loads only when needed\n * - Zero impact on public page performance\n */\nexport default function AdminRoutes() {\n  return (\n    <Suspense fallback={<AdminPageLoader />}>\n      <Routes>\n        {/* Admin authentication */}\n        <Route path=\"/admin\" element={<AdminLogin />} />\n\n        {/* Admin dashboard and management */}\n        <Route path=\"/admin/dashboard\" element={<AdminDashboard />} />\n\n        {/* Blog management */}\n        <Route path=\"/admin/posts\" element={<AdminBlogPosts />} />\n        <Route path=\"/admin/blog/new\" element={<AdminBlogEditor />} />\n        <Route path=\"/admin/blog/edit/:id\" element={<AdminBlogEditor />} />\n\n        {/* Product management */}\n        <Route path=\"/admin/products\" element={<AdminProducts />} />\n        <Route path=\"/admin/products/new\" element={<AdminProductEditor />} />\n        <Route\n          path=\"/admin/products/edit/:id\"\n          element={<AdminProductEditor />}\n        />\n\n        {/* Analytics and content management */}\n        <Route path=\"/admin/analytics\" element={<AdminBlogAnalytics />} />\n        <Route path=\"/admin/categories\" element={<AdminCategories />} />\n        <Route path=\"/admin/tags\" element={<AdminTags />} />\n        <Route path=\"/admin/comments\" element={<AdminComments />} />\n      </Routes>\n    </Suspense>\n  );\n}\n"], "file": "assets/admin-routes-D7V7jRaZ.js"}