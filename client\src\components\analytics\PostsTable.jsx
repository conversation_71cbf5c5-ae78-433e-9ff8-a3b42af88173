// client/src/components/analytics/PostsTable.jsx

import React, { useState } from "react";
import { Link } from "react-router-dom";
import PropTypes from "prop-types";
import { API_BASE_URL } from "../../utils/api";

const PostsTable = ({ data, loading, timeRange: _timeRange }) => {
  const [sortBy, setSortBy] = useState("publishDate");
  const [sortOrder, setSortOrder] = useState("desc");

  const getImageUrl = (filename) => {
    if (!filename) return null;

    // If it's already a full URL, return as is
    if (filename.startsWith("http")) {
      return filename;
    }

    // Construct the full URL for uploaded images
    const baseUrl = API_BASE_URL.replace("/api", "");
    return `${baseUrl}/uploads/blog-images/${filename}`;
  };
  const [visibleColumns, setVisibleColumns] = useState({
    publishDate: true,
    views: true,
    clicks: true,
    readingTime: true,
    engagement: true,
    categories: false,
  });

  const columnOptions = [
    { key: "publishDate", label: "Publish date", icon: "solar:calendar-bold" },
    { key: "views", label: "Views", icon: "solar:eye-bold" },
    { key: "clicks", label: "Clicks", icon: "solar:cursor-bold" },
    {
      key: "readingTime",
      label: "Reading time",
      icon: "solar:clock-circle-bold",
    },
    { key: "engagement", label: "Engagement", icon: "solar:heart-bold" },
    { key: "categories", label: "Categories", icon: "solar:folder-bold" },
  ];

  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  const toggleColumn = (columnKey) => {
    setVisibleColumns((prev) => ({
      ...prev,
      [columnKey]: !prev[columnKey],
    }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatReadingTime = (minutes) => {
    return minutes ? `${minutes}m` : "0m";
  };

  const formatViewTime = (seconds) => {
    if (!seconds || seconds === 0) return "0s";
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0
      ? `${minutes}m ${remainingSeconds}s`
      : `${minutes}m`;
  };

  const sortedData = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return [...data].sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      if (sortBy === "publishDate") {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [data, sortBy, sortOrder]);

  if (loading) {
    return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <h5 className="card-title mb-4">Posts Analytics</h5>
          <div className="d-flex justify-content-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card border-0 shadow-sm">
      <div className="card-body p-4">
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h5 className="card-title mb-0">Posts by</h5>
          <a href="#" className="text-primary small text-decoration-none">
            View Report
          </a>
        </div>

        {/* Column Selector */}
        <div className="row mb-4">
          <div className="col-md-6">
            <div className="d-flex flex-wrap gap-2">
              {columnOptions.map((column) => (
                <button
                  key={column.key}
                  className={`btn btn-sm d-flex align-items-center gap-1 ${
                    visibleColumns[column.key]
                      ? "btn-primary"
                      : "btn-outline-secondary"
                  }`}
                  onClick={() => toggleColumn(column.key)}
                >
                  <iconify-icon
                    icon={column.icon}
                    className="fs-6"
                  ></iconify-icon>
                  <span>{column.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="table-responsive">
          <table className="table table-hover align-middle">
            <thead className="table-light">
              <tr>
                <th scope="col" className="border-0 fw-semibold text-muted">
                  Post title
                </th>
                {visibleColumns.publishDate && (
                  <th
                    scope="col"
                    className="border-0 fw-semibold text-muted cursor-pointer"
                    onClick={() => handleSort("publishDate")}
                  >
                    <div className="d-flex align-items-center gap-1">
                      <iconify-icon icon="solar:calendar-bold"></iconify-icon>
                      <span>Publish date</span>
                      {sortBy === "publishDate" && (
                        <iconify-icon
                          icon={
                            sortOrder === "asc"
                              ? "solar:arrow-up-bold"
                              : "solar:arrow-down-bold"
                          }
                          className="text-primary"
                        ></iconify-icon>
                      )}
                    </div>
                  </th>
                )}
                {visibleColumns.views && (
                  <th
                    scope="col"
                    className="border-0 fw-semibold text-muted cursor-pointer text-center"
                    onClick={() => handleSort("views")}
                  >
                    <div className="d-flex align-items-center justify-content-center gap-1">
                      <iconify-icon icon="solar:eye-bold"></iconify-icon>
                      <span>Views</span>
                      {sortBy === "views" && (
                        <iconify-icon
                          icon={
                            sortOrder === "asc"
                              ? "solar:arrow-up-bold"
                              : "solar:arrow-down-bold"
                          }
                          className="text-primary"
                        ></iconify-icon>
                      )}
                    </div>
                  </th>
                )}
                {visibleColumns.clicks && (
                  <th
                    scope="col"
                    className="border-0 fw-semibold text-muted cursor-pointer text-center"
                    onClick={() => handleSort("clicks")}
                  >
                    <div className="d-flex align-items-center justify-content-center gap-1">
                      <iconify-icon icon="solar:cursor-bold"></iconify-icon>
                      <span>Clicks</span>
                      {sortBy === "clicks" && (
                        <iconify-icon
                          icon={
                            sortOrder === "asc"
                              ? "solar:arrow-up-bold"
                              : "solar:arrow-down-bold"
                          }
                          className="text-primary"
                        ></iconify-icon>
                      )}
                    </div>
                  </th>
                )}
                {visibleColumns.readingTime && (
                  <th
                    scope="col"
                    className="border-0 fw-semibold text-muted cursor-pointer text-center"
                    onClick={() => handleSort("readingTime")}
                  >
                    <div className="d-flex align-items-center justify-content-center gap-1">
                      <iconify-icon icon="solar:clock-circle-bold"></iconify-icon>
                      <span>Time</span>
                      {sortBy === "readingTime" && (
                        <iconify-icon
                          icon={
                            sortOrder === "asc"
                              ? "solar:arrow-up-bold"
                              : "solar:arrow-down-bold"
                          }
                          className="text-primary"
                        ></iconify-icon>
                      )}
                    </div>
                  </th>
                )}
                {visibleColumns.engagement && (
                  <th
                    scope="col"
                    className="border-0 fw-semibold text-muted cursor-pointer text-center"
                    onClick={() => handleSort("engagement")}
                  >
                    <div className="d-flex align-items-center justify-content-center gap-1">
                      <iconify-icon icon="solar:heart-bold"></iconify-icon>
                      <span>Engagement</span>
                      {sortBy === "engagement" && (
                        <iconify-icon
                          icon={
                            sortOrder === "asc"
                              ? "solar:arrow-up-bold"
                              : "solar:arrow-down-bold"
                          }
                          className="text-primary"
                        ></iconify-icon>
                      )}
                    </div>
                  </th>
                )}
                {visibleColumns.categories && (
                  <th scope="col" className="border-0 fw-semibold text-muted">
                    <div className="d-flex align-items-center gap-1">
                      <iconify-icon icon="solar:folder-bold"></iconify-icon>
                      <span>Categories</span>
                    </div>
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {sortedData.length === 0 ? (
                <tr>
                  <td colSpan="7" className="text-center py-5 text-muted">
                    <iconify-icon
                      icon="solar:document-text-bold"
                      className="fs-1 mb-3 d-block"
                    ></iconify-icon>
                    No posts data available for the selected period
                  </td>
                </tr>
              ) : (
                sortedData.map((post) => (
                  <tr key={post.id}>
                    <td>
                      <div className="d-flex align-items-center gap-3">
                        {post.featuredImage && (
                          <img
                            src={getImageUrl(post.featuredImage)}
                            alt=""
                            className="rounded"
                            style={{
                              width: "40px",
                              height: "40px",
                              objectFit: "cover",
                            }}
                          />
                        )}
                        <div>
                          <Link
                            to={`/admin/blog/edit/${post.id}`}
                            className="text-decoration-none fw-medium text-dark"
                          >
                            {post.title}
                          </Link>
                          <div className="text-muted small">
                            Published on {formatDate(post.publishedAt)}
                          </div>
                        </div>
                      </div>
                    </td>
                    {visibleColumns.publishDate && (
                      <td className="text-muted small">
                        {formatDate(post.publishedAt)}
                      </td>
                    )}
                    {visibleColumns.views && (
                      <td className="text-center fw-medium">
                        {post.views?.toLocaleString() || "0"}
                      </td>
                    )}
                    {visibleColumns.clicks && (
                      <td className="text-center fw-medium">
                        {post.clicks?.toLocaleString() || "0"}
                      </td>
                    )}
                    {visibleColumns.readingTime && (
                      <td className="text-center text-muted">
                        <div className="small">
                          <div>Est: {formatReadingTime(post.readingTime)}</div>
                          <div className="text-primary">
                            Avg: {formatViewTime(post.avgViewTime)}
                          </div>
                        </div>
                      </td>
                    )}
                    {visibleColumns.engagement && (
                      <td className="text-center fw-medium">
                        {post.engagement?.toLocaleString() || "0"}
                      </td>
                    )}
                    {visibleColumns.categories && (
                      <td>
                        <div className="d-flex flex-wrap gap-1">
                          {post.categories?.map((category) => (
                            <span
                              key={category.id}
                              className="badge bg-light text-dark"
                            >
                              {category.name}
                            </span>
                          ))}
                        </div>
                      </td>
                    )}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

PostsTable.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      title: PropTypes.string,
      publishedAt: PropTypes.string,
      featuredImage: PropTypes.string,
      readingTime: PropTypes.number,
      avgViewTime: PropTypes.number,
      categories: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.string,
          name: PropTypes.string,
        })
      ),
      views: PropTypes.number,
      clicks: PropTypes.number,
      engagement: PropTypes.number,
    })
  ),
  loading: PropTypes.bool,
  timeRange: PropTypes.string,
};

export default PostsTable;
