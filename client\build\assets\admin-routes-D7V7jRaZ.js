const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/pages-admin-DnFYe5ub.js","assets/vendor-react-EBZQFYZ5.js","assets/vendor-misc-j6k8kvFA.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css","assets/vendor-gallery-BKyWYjF6.js","assets/vendor-admin-DvrlCxcB.js","assets/components-common-DDbdC8oB.js","assets/vendor-i18n-DxzbetI3.js"])))=>i.map(i=>d[i]);
import{j as e,r as A,R as y,a as n,b as s}from"./vendor-react-EBZQFYZ5.js";const g="modulepreload",v=function(t){return"/"+t},u={},r=function(x,m,V){let c=Promise.resolve();if(m&&m.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),i=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));c=Promise.allSettled(m.map(a=>{if(a=v(a),a in u)return;u[a]=!0;const l=a.endsWith(".css"),j=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${j}`))return;const d=document.createElement("link");if(d.rel=l?"stylesheet":g,l||(d.as="script"),d.crossOrigin="",d.href=a,i&&d.setAttribute("nonce",i),document.head.appendChild(d),l)return new Promise((f,E)=>{d.addEventListener("load",f),d.addEventListener("error",()=>E(new Error(`Unable to preload CSS for ${a}`)))})}))}function _(o){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=o,window.dispatchEvent(i),!i.defaultPrevented)throw o}return c.then(o=>{for(const i of o||[])i.status==="rejected"&&_(i.reason);return x().catch(_)})},P=()=>e.jsxs("div",{className:"page-loader",style:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:"#1a1a1a",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:[e.jsx("div",{className:"loader",style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #fff",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("style",{children:`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `})]}),R=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.A),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),b=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.a),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),L=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.b),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),p=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.c),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),T=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.d),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),h=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.e),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),O=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.f),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),z=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.g),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),I=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.h),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),D=s.lazy(()=>r(()=>import("./pages-admin-DnFYe5ub.js").then(t=>t.p),__vite__mapDeps([0,1,2,3,4,5,6,7,8])));function w(){return e.jsx(A.Suspense,{fallback:e.jsx(P,{}),children:e.jsxs(y,{children:[e.jsx(n,{path:"/admin",element:e.jsx(R,{})}),e.jsx(n,{path:"/admin/dashboard",element:e.jsx(b,{})}),e.jsx(n,{path:"/admin/posts",element:e.jsx(L,{})}),e.jsx(n,{path:"/admin/blog/new",element:e.jsx(p,{})}),e.jsx(n,{path:"/admin/blog/edit/:id",element:e.jsx(p,{})}),e.jsx(n,{path:"/admin/products",element:e.jsx(T,{})}),e.jsx(n,{path:"/admin/products/new",element:e.jsx(h,{})}),e.jsx(n,{path:"/admin/products/edit/:id",element:e.jsx(h,{})}),e.jsx(n,{path:"/admin/analytics",element:e.jsx(O,{})}),e.jsx(n,{path:"/admin/categories",element:e.jsx(z,{})}),e.jsx(n,{path:"/admin/tags",element:e.jsx(I,{})}),e.jsx(n,{path:"/admin/comments",element:e.jsx(D,{})})]})})}const k=Object.freeze(Object.defineProperty({__proto__:null,default:w},Symbol.toStringTag,{value:"Module"}));export{k as A,r as _};
//# sourceMappingURL=admin-routes-D7V7jRaZ.js.map
