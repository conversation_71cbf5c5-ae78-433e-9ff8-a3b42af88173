/* Admin Panel Styles - Modern Sidebar Layout */

/* Iconify Icon Styling */
iconify-icon {
  display: inline-block;
  vertical-align: middle;
}

.admin-nav-icon iconify-icon,
.admin-user-avatar iconify-icon {
  width: 1em;
  height: 1em;
}

/* Dashboard stats and features icons */
.number-2-icon iconify-icon,
.alt-features-icon iconify-icon {
  width: 1em;
  height: 1em;
  color: inherit;
}

.me-2 {
  margin-right: 0.5rem;
}

/* Spinning animation for loading icons */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Apply spin animation to iconify icons */
iconify-icon[style*="spin"] {
  animation: spin 1s linear infinite;
}

/* Responsive improvements for admin pages */
@media (max-width: 767.98px) {
  /* Mobile-specific styles */
  .admin-table {
    margin-bottom: 20px;
  }

  /* Ensure buttons don't get too small on mobile */
  .btn-sm {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
  }

  /* Better spacing for mobile cards */
  .card-body {
    padding: 1rem !important;
  }

  /* Responsive badge sizing */
  .badge {
    font-size: 0.7rem;
  }

  /* Better mobile pagination */
  .pagination-sm .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 575.98px) {
  /* Extra small screens */
  .admin-table {
    padding: 15px !important;
  }

  /* Stack action buttons vertically on very small screens */
  .d-flex.gap-2.flex-wrap .btn {
    margin-bottom: 0.5rem;
  }

  /* Smaller images on mobile */
  .card img {
    width: 35px !important;
    height: 35px !important;
  }
}

/* Utility classes for responsive design */
.w-lg-auto {
  width: auto !important;
}

@media (min-width: 992px) {
  .w-lg-auto {
    width: auto !important;
  }
}

/* Admin Layout Wrapper */
.admin-layout-wrapper {
  display: flex;
  min-height: 100vh;
  background-color: var(--color-gray-light-2);
}

/* Mobile Overlay */
.admin-mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  display: none;
}

@media (max-width: 768px) {
  .admin-mobile-overlay {
    display: block;
  }
}

/* Sidebar */
.admin-sidebar {
  width: 280px;
  background-color: var(--color-dark-1);
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1050;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.admin-sidebar.collapsed {
  width: 70px;
}

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }

  .admin-sidebar.mobile-open {
    transform: translateX(0);
  }
}

/* Sidebar Header */
.admin-sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--color-dark-3);
  text-align: left;
}

.admin-logo {
  font-size: 18px;
  font-weight: 700;
  font-family: var(--font-alt);
}

.admin-sidebar.collapsed .admin-logo {
  font-size: 16px;
}

/* Sidebar Navigation */
.admin-sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.admin-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.admin-nav-item {
  margin-bottom: 5px;
}

.admin-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition-default);
  border-radius: 0;
}

.admin-nav-link:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.admin-nav-item.active .admin-nav-link {
  color: #fff;
  background-color: var(--color-primary-1);
  position: relative;
}

.admin-nav-item.active .admin-nav-link::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #fff;
}

.admin-nav-icon {
  width: 20px;
  height: 20px;
  text-align: center;
  margin-right: 12px;
  font-size: 18px;
  display: inline-block;
}

.admin-nav-text {
  font-weight: 500;
  transition: var(--transition-default);
}

.admin-sidebar.collapsed .admin-nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.admin-sidebar.collapsed .admin-nav-link {
  justify-content: center;
  padding: 12px 10px;
}

.admin-sidebar.collapsed .admin-nav-icon {
  margin-right: 0;
}

/* Navigation Divider */
.admin-nav-divider {
  height: 1px;
  background-color: var(--color-dark-3);
  margin: 20px 15px;
}

/* Sidebar Footer */
.admin-sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.05);
  margin-top: auto;
}

.admin-user-menu {
  position: relative;
  width: 100%;
}

.admin-user-info {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  transition: var(--transition-default);
}

.admin-user-info.clickable {
  cursor: pointer;
}

.admin-user-info.clickable:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.admin-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary-1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
}

.admin-sidebar.collapsed .admin-user-avatar {
  margin-right: 0;
}

.admin-user-details {
  flex: 1;
  min-width: 0;
}

.admin-user-dropdown-arrow {
  margin-left: auto;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  transition: var(--transition-default);
}

.admin-user-info.clickable:hover .admin-user-dropdown-arrow {
  color: #fff;
}

.admin-user-name {
  font-weight: 600;
  font-size: 14px;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-user-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.admin-logout-btn {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.admin-logout-btn:hover {
  background: #dc3545;
  color: #fff;
  border-color: #dc3545;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.admin-logout-btn:active {
  transform: translateY(0);
}

/* Collapsed sidebar logout button */
.admin-sidebar.collapsed .admin-logout-btn {
  padding: 8px;
  justify-content: center;
}

.admin-sidebar.collapsed .admin-logout-btn span {
  display: none;
}

/* User Dropdown Menu */
.admin-user-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--color-gray-light-3);
  margin-bottom: 8px;
  z-index: 1000;
  animation: dropdownSlideUp 0.2s ease-out;
}

@keyframes dropdownSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-dropdown-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  color: var(--color-dark-1);
  cursor: pointer;
  transition: var(--transition-default);
  font-size: 14px;
  font-weight: 500;
}

.admin-dropdown-item:hover {
  background-color: var(--color-gray-light-1);
  color: var(--color-primary-1);
}

.admin-dropdown-item.logout {
  color: #dc3545;
}

.admin-dropdown-item.logout:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.admin-dropdown-divider {
  height: 1px;
  background-color: var(--color-gray-light-3);
  margin: 4px 0;
}

/* Collapsed sidebar dropdown adjustments */
.admin-sidebar.collapsed .admin-user-dropdown {
  left: 70px;
  right: auto;
  width: 200px;
  bottom: 20px;
  margin-bottom: 0;
}

.admin-sidebar.collapsed .admin-user-dropdown-arrow {
  display: none;
}

/* Main Content */
.admin-main-content {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left 0.3s ease;
}

.admin-main-content.sidebar-collapsed {
  margin-left: 70px;
}

@media (max-width: 768px) {
  .admin-main-content {
    margin-left: 0;
  }

  .admin-main-content.sidebar-collapsed {
    margin-left: 0;
  }
}

/* Top Bar */
.admin-topbar {
  background-color: #fff;
  border-bottom: 1px solid var(--color-gray-light-3);
  padding: 15px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.admin-topbar-left {
  display: flex;
  align-items: center;
}

.admin-sidebar-toggle {
  background: none;
  border: none;
  padding: 8px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 4px;
  color: var(--color-gray-1);
  transition: var(--transition-default);
  font-size: 18px;
}

.admin-sidebar-toggle:hover {
  background-color: var(--color-gray-light-1);
  color: var(--color-dark-1);
}

.admin-mobile-toggle {
  background: none;
  border: none;
  padding: 8px;
  margin-right: 15px;
  cursor: pointer;
  border-radius: 4px;
  color: var(--color-gray-1);
  transition: var(--transition-default);
  font-size: 18px;
  display: none;
}

@media (max-width: 768px) {
  .admin-sidebar-toggle {
    display: none;
  }

  .admin-mobile-toggle {
    display: block;
  }
}

.admin-mobile-toggle:hover {
  background-color: var(--color-gray-light-1);
  color: var(--color-dark-1);
}

.admin-page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
  font-family: var(--font-alt);
}

.admin-topbar-right {
  display: flex;
  align-items: center;
}

.admin-welcome {
  color: var(--color-gray-1);
  font-size: 14px;
}

@media (max-width: 768px) {
  .admin-welcome {
    display: none;
  }
}

/* Content Area */
.admin-content {
  flex: 1;
  padding: 30px;
  background-color: var(--color-gray-light-2);
}

@media (max-width: 768px) {
  .admin-content {
    padding: 20px 15px;
  }
}

/* Admin Login Page */
.admin-login-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--color-dark-1) 0%,
    var(--color-dark-2) 100%
  );
}

.admin-login-section .form-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 60px 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--box-shadow-block-strong);
}

.admin-login-section .form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
}

.admin-login-section .form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--color-primary-1);
  box-shadow: 0 0 0 0.2rem rgba(69, 103, 237, 0.25);
  color: #fff;
}

.admin-login-section .form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.admin-login-section .btn-color {
  background: var(--gradient-primary-1);
  border: none;
  color: #fff;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-login-section .btn-color:hover {
  background: var(--gradient-primary-1-a);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-strong);
}

.admin-login-section .alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #ff6b6b;
  border-radius: var(--border-radius-default);
}

/* Admin Dashboard Stats */
.number-2-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition-default);
  height: 100%;
}

.number-2-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-strong);
}

.number-2-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--gradient-primary-1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  color: #fff;
}

.number-2-title {
  font-size: 16px;
  color: var(--color-gray-1);
  margin-bottom: 10px;
  font-weight: 500;
}

.number-2-number {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-dark-1);
  font-family: var(--font-alt);
}

/* Admin Quick Actions */
.alt-features-item {
  background-color: #fff;
  border-radius: 10px;
  padding: 40px 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition-default);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alt-features-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-strong);
}

.alt-features-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--gradient-primary-1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  font-size: 32px;
  color: #fff;
}

.alt-features-title {
  color: var(--color-dark-1);
  margin-bottom: 15px;
  font-size: 20px;
}

.alt-features-descr {
  color: var(--color-gray-1);
  line-height: 1.6;
  flex-grow: 1;
  margin-bottom: 20px;
}

/* Admin Forms */
.admin-form .form-group {
  margin-bottom: 25px;
}

.admin-form .form-control {
  border-radius: var(--border-radius-default);
  border: 1px solid var(--color-gray-light-4);
  padding: 12px 15px;
  font-size: 14px;
  transition: var(--transition-default);
}

.admin-form .form-control:focus {
  border-color: var(--color-primary-1);
  box-shadow: 0 0 0 0.2rem rgba(69, 103, 237, 0.25);
}

.admin-form .btn-primary {
  background: var(--gradient-primary-1);
  border: none;
  padding: 12px 30px;
  border-radius: var(--border-radius-large);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-form .btn-primary:hover {
  background: var(--gradient-primary-1-a);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-strong);
}

/* Blog Editor Specific Styles */
.admin-form .form-label {
  font-weight: 600;
  color: var(--color-dark-1);
  margin-bottom: 8px;
  display: block;
}

.admin-form .form-text {
  font-size: 12px;
  color: var(--color-gray-1);
  margin-top: 5px;
}

.admin-form .form-check {
  margin-bottom: 10px;
}

.admin-form .form-check-input {
  margin-right: 8px;
}

.admin-form .form-check-label {
  font-weight: 500;
  color: var(--color-dark-1);
}

/* Language Tabs */
.admin-form .language-tabs {
  border-bottom: 1px solid var(--color-gray-light-3);
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.admin-form .language-tab {
  padding: 12px 20px;
  background: none;
  border: none;
  color: var(--color-gray-1);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-default);
  border-bottom: 3px solid transparent;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.admin-form .language-tab:hover {
  color: var(--color-dark-1);
  background-color: var(--color-gray-light-1);
  transform: translateY(-2px);
}

.admin-form .language-tab.active {
  color: var(--color-primary-1);
  border-bottom-color: var(--color-primary-1);
  background-color: #fff;
  font-weight: 600;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.admin-form .language-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #fff;
}

/* Form Section Headers */
.admin-form .admin-table {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-gray-light-3);
}

.admin-form .admin-table h3 {
  margin-bottom: 8px;
}

.admin-form .admin-table .section-descr {
  color: var(--color-gray-1);
  font-size: 14px;
  line-height: 1.5;
}

/* Textarea Styling */
.admin-form textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

.admin-form textarea.content-editor {
  min-height: 400px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  line-height: 1.6;
}

/* File Upload Styling */
.admin-form input[type="file"] {
  padding: 10px;
  border: 2px dashed var(--color-gray-light-3);
  border-radius: var(--border-radius-default);
  background-color: var(--color-gray-light-2);
  transition: var(--transition-default);
}

.admin-form input[type="file"]:hover {
  border-color: var(--color-primary-1);
  background-color: rgba(69, 103, 237, 0.05);
}

/* Image Preview */
.admin-form .image-preview {
  max-width: 300px;
  border-radius: var(--border-radius-default);
  box-shadow: var(--box-shadow);
  margin-top: 15px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1060;
  padding: 20px;
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 25px 30px 20px;
  border-bottom: 1px solid var(--color-gray-light-3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-dark-1);
  font-family: var(--font-alt);
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--color-gray-1);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: var(--transition-default);
}

.modal-close:hover {
  color: var(--color-dark-1);
  background-color: var(--color-gray-light-1);
}

.modal-body {
  padding: 30px;
}

.modal-footer {
  padding: 20px 30px 30px;
  border-top: 1px solid var(--color-gray-light-3);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Color Input Styling */
.form-control-color {
  border: 1px solid var(--color-gray-light-4);
  border-radius: var(--border-radius-default);
  cursor: pointer;
}

.form-control-color::-webkit-color-swatch-wrapper {
  padding: 0;
  border-radius: var(--border-radius-default);
}

.form-control-color::-webkit-color-swatch {
  border: none;
  border-radius: var(--border-radius-default);
}

/* Admin Tables */
.admin-table {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.admin-table .table {
  margin-bottom: 0;
}

.admin-table .table thead th {
  background-color: var(--color-gray-light-1);
  border: none;
  font-weight: 600;
  color: var(--color-dark-1);
  padding: 20px 15px;
}

.admin-table .table tbody td {
  border-top: 1px solid var(--color-gray-light-3);
  padding: 15px;
  vertical-align: middle;
}

.admin-table .table tbody tr:hover {
  background-color: var(--color-gray-light-2);
}

/* Loading Animation */
.loading-animation {
  padding: 60px 0;
}

.loading-animation .fa-spinner {
  margin-bottom: 20px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .admin-login-section .form-container {
    margin: 20px;
    padding: 40px 30px;
  }

  .number-2-item,
  .alt-features-item {
    margin-bottom: 30px;
  }

  .admin-layout .main-nav .inner-nav ul li a {
    padding: 10px 15px;
    font-size: 14px;
  }
}
