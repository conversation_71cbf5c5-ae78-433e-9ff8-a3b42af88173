{"version": 3, "file": "pages-static-Dj38j6FX.js", "sources": ["../../src/data/skills.js", "../../src/pages/about/page.jsx", "../../src/data/features.js", "../../src/pages/services/page.jsx", "../../src/pages/contact/page.jsx"], "sourcesContent": ["// client/src/data/skills.js\n\nexport const progressData = [\n  { key: \"automation\", value: 98 },\n  { key: \"ai\", value: 95 },\n  { key: \"integration\", value: 92 },\n  { key: \"design\", value: 90 },\n  { key: \"security\", value: 96 },\n];\n", "// client/src/pages/about/page.jsx\n\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport { Link } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport About from \"@/components/home/<USER>\";\nimport Team from \"@/components/home/<USER>\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport { progressData } from \"@/data/skills\";\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\nimport { useTranslation } from \"react-i18next\";\nimport React from \"react\";\n\nconst dark = true;\n\n// JSON-LD structured data for the about page\nconst aboutSchema = {\n  \"@context\": \"https://schema.org\",\n  \"@type\": \"AboutPage\",\n  name: \"About DevSkills\",\n  description:\n    \"Learn about DevSkills, our mission, values, and the team behind our innovative software development services and custom solutions.\",\n  publisher: {\n    \"@type\": \"Organization\",\n    name: \"DevSkills\",\n    logo: \"https://devskills.ee/logo.png\",\n  },\n};\nexport default function ElegantAboutPageDark() {\n  const { t } = useTranslation();\n  const seoData = getPageSEOData(\"about\");\n\n  return (\n    <>\n      <UnifiedSEO\n        title={seoData.title}\n        description={seoData.description}\n        slug=\"about\"\n        type=\"website\"\n        image=\"https://devskills.ee/about.jpg\"\n        schema={[aboutSchema, ...seoData.schema]}\n        keywords={seoData.keywords}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"about.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"about.subtitle\")}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"spacer-small\"></div>\n                </div>\n              </section>\n              <section\n                className={`page-section  scrollSpysection pb-0 ${\n                  dark ? \"bg-dark-1 light-content\" : \"\"\n                } `}\n                id=\"about\"\n              >\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-5 d-flex align-items-center mb-md-50\">\n                      <div>\n                        <div className=\"wow linesAnimIn\" data-splitting=\"lines\">\n                          <h2 className=\"section-title mb-30 mb-sm-20\">\n                            <span className=\"text-gray\">\n                              {t(\"about.mission.title\").split(\" \")[0]}\n                            </span>{\" \"}\n                            {t(\"about.mission.title\").split(\" \")[1]}\n                            <span className=\"text-gray\">.</span>\n                          </h2>\n                          <div className=\"text-gray mb-30 mb-sm-20\">\n                            <p className=\"mb-0\">{t(\"about.mission.text1\")}</p>\n                            <p className=\"mt-4\">{t(\"about.mission.text2\")}</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <About />\n                  </div>\n                </div>\n              </section>\n              <div className=\"page-section overflow-hidden\">\n                <MarqueeDark />\n              </div>\n              <section\n                className=\"page-section pt-0 pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content\"\n                style={{\n                  backgroundImage:\n                    \"url(/assets/images/demo-elegant/backgrounds/bg-dark-1.jpg)\",\n                }}\n              >\n                <div className=\"container position-relative\">\n                  <div className=\"row\">\n                    <div className=\"col-md-6 col-xl-5\">\n                      <div className=\"call-action-1-images pb-60 pb-md-0 mt-n30 mt-md-70 mb-n30 mb-md-70 mb-sm-0\">\n                        <div className=\"call-action-1-image-1 round\">\n                          <img\n                            src=\"/assets/images/demo-elegant/about/business-dashboard.jpg\"\n                            width={678}\n                            height={840}\n                            alt=\"Business intelligence dashboard showing performance metrics\"\n                            className=\"grayscale-effect\"\n                          />\n                        </div>\n                        <div className=\"call-action-1-image-2\">\n                          <div\n                            className=\"call-action-1-image-2-inner\"\n                            data-rellax-y=\"\"\n                            data-rellax-speed=\"0.7\"\n                            data-rellax-percentage=\"0.427\"\n                          >\n                            <img\n                              src=\"/assets/images/demo-elegant/about/cybernetic-eye.jpg\"\n                              alt=\"AI-powered business automation interface\"\n                              width={300}\n                              height={409}\n                              className=\"grayscale-effect\"\n                            />\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"col-md-6 offset-xl-1 d-flex align-items-center\">\n                      <div className=\"row small-section\">\n                        <div className=\"col-xl-11\">\n                          <h2 className=\"section-title mb-30 mb-sm-20\">\n                            {t(\"about.advantage.title\")}\n                          </h2>\n                          <div className=\"text-gray mb-30 mb-sm-20\">\n                            <p className=\"mb-0\">{t(\"about.advantage.text1\")}</p>\n                            <p className=\"mt-4\">{t(\"about.advantage.text2\")}</p>\n                          </div>\n                          <div className=\"local-scroll\">\n                            <Link\n                              to=\"/services\"\n                              className=\"link-hover-anim link-circle-1 align-middle\"\n                              data-link-animate=\"y\"\n                            >\n                              <span className=\"link-strong link-strong-unhovered\">\n                                {t(\"about.advantage.button\")}{\" \"}\n                                <i\n                                  className=\"mi-arrow-right size-18 align-middle\"\n                                  aria-hidden=\"true\"\n                                ></i>\n                              </span>\n                              <span\n                                className=\"link-strong link-strong-hovered\"\n                                aria-hidden=\"true\"\n                              >\n                                {t(\"about.advantage.button\")}{\" \"}\n                                <i\n                                  className=\"mi-arrow-right size-18 align-middle\"\n                                  aria-hidden=\"true\"\n                                ></i>\n                              </span>\n                            </Link>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section\n                className={`page-section pb-0  scrollSpysection  ${\n                  dark ? \"bg-dark-1 light-content\" : \"\"\n                } `}\n                id=\"team\"\n              >\n                <Team />\n              </section>\n              <div className=\"page-section overflow-hidden\">\n                <MarqueeDark />\n              </div>\n              <>\n                {/* Skill Section */}\n                <section className=\"z-index-1 page-section bg-dark-1 bg-dark-alpha-80 parallax-6 light-content\">\n                  <div className=\"container position-relative\">\n                    <div className=\"row position-relative\">\n                      <div className=\"col-md-6 col-lg-5 mb-md-50 mb-sm-30\">\n                        <h3 className=\"section-title mb-30\">\n                          {t(\"about.values.title\")}\n                        </h3>\n                        <p className=\"text-gray mb-0\">\n                          {t(\"about.values.custom_text1\")}\n                        </p>\n                        <p className=\"text-gray mt-4\">\n                          {t(\"about.values.custom_text2\")}\n                        </p>\n                      </div>\n                      <div className=\"col-md-6 offset-lg-1 pt-10 pt-sm-0\">\n                        {/* Bar Item */}\n                        {progressData.map((elm, i) => (\n                          <div key={i} className=\"progress tpl-progress\">\n                            <div\n                              className=\"progress-bar\"\n                              role=\"progressbar\"\n                              style={{ width: `${elm.value}%` }}\n                            >\n                              <div>{t(`about.skills.${elm.key}`)}, %</div>\n                              <span>{elm.value}</span>\n                            </div>\n                          </div>\n                        ))}\n                        {/* End Bar Item */}\n\n                        {/* End Bar Item */}\n                      </div>\n                    </div>\n                  </div>\n                </section>\n                {/* End Skill Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Call Action Section */}\n                <section\n                  className=\"page-section light-content\"\n                  style={{ backgroundColor: \"#000000\" }}\n                >\n                  <div className=\"container position-relative\">\n                    {/* Decorative Waves */}\n                    <div className=\"position-relative\">\n                      <div\n                        className=\"decoration-21 d-none d-lg-block\"\n                        data-rellax-y=\"\"\n                        data-rellax-speed=\"0.7\"\n                        data-rellax-percentage=\"0.35\"\n                      >\n                        <img\n                          src=\"/assets/images/decoration-3.svg\"\n                          className=\"svg-shape\"\n                          width={148}\n                          height={148}\n                          alt=\"\"\n                        />\n                      </div>\n                    </div>\n                    {/* End Decorative Waves */}\n                    <div className=\"row text-center wow fadeInUp\">\n                      <div className=\"col-md-10 offset-md-1 col-lg-6 offset-lg-3\">\n                        <p className=\"section-descr mb-50 mb-sm-30\">\n                          {t(\"about.cta.text\")}\n                        </p>\n                        <div className=\"local-scroll\">\n                          <Link\n                            to={`/contact`}\n                            className=\"btn btn-mod btn-large btn-circle btn-hover-anim btn-w\"\n                          >\n                            <span>{t(\"about.cta.button\")}</span>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </section>\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "export const featureItems = [\n  {\n    id: 1,\n    className: \"col-md-4 col-lg-3 d-flex align-items-stretch mb-sm-30\",\n    path: \"M21.62 20.196c1.055-.922 1.737-2.262 1.737-3.772 0-1.321-.521-2.515-1.357-3.412v-6.946l-11.001-6.066-11 6v12.131l11 5.869 5.468-2.917c.578.231 1.205.367 1.865.367.903 0 1.739-.258 2.471-.676l2.394 3.226.803-.596-2.38-3.208zm-11.121 2.404l-9.5-5.069v-10.447l9.5 4.946v10.57zm1-.001v-10.567l5.067-2.608.029.015.021-.04 4.384-2.256v5.039c-.774-.488-1.686-.782-2.668-.782-2.773 0-5.024 2.252-5.024 5.024 0 1.686.838 3.171 2.113 4.083l-3.922 2.092zm6.833-2.149c-2.219 0-4.024-1.808-4.024-4.026s1.805-4.025 4.024-4.025c2.22 0 4.025 1.807 4.025 4.025 0 2.218-1.805 4.026-4.025 4.026zm-.364-3.333l-1.306-1.147-.66.751 2.029 1.782 2.966-3.12-.725-.689-2.304 2.423zm-16.371-10.85l4.349-2.372 9.534 4.964-4.479 2.305-9.404-4.897zm9.4-5.127l9.404 5.186-3.832 1.972-9.565-4.98 3.993-2.178z\",\n    title: \"Unique Design\",\n    description:\n      \"Fusce aliquet quam eget neque ultrices elementum felis id arcu blandit sagittis.\",\n  },\n  {\n    id: 2,\n    className: \"col-md-4 col-lg-3 d-flex align-items-stretch mb-sm-30\",\n    path: \"M12 0c-3.371 2.866-5.484 3-9 3v11.535c0 4.603 3.203 5.804 9 9.465 5.797-3.661 9-4.862 9-9.465v-11.535c-3.516 0-5.629-.134-9-3zm0 1.292c2.942 2.31 5.12 2.655 8 2.701v10.542c0 3.891-2.638 4.943-8 8.284-5.375-3.35-8-4.414-8-8.284v-10.542c2.88-.046 5.058-.391 8-2.701zm5 7.739l-5.992 6.623-3.672-3.931.701-.683 3.008 3.184 5.227-5.878.728.685z\",\n    title: \"Quality Code\",\n    description:\n      \"Lorem ipsum dolor sit amet rembe adipiscing elite Inwege maximus ligula imsum.\",\n  },\n  {\n    id: 3,\n    className: \"col-md-4 col-lg-3 d-flex align-items-stretch\",\n    path: \"M6.514 24.015h-3v-3.39c-2.08-.638-3.5-2.652-3.5-5.04 0-1.19.202-1.693 1.774-5.603.521-1.294 1.195-2.97 2.068-5.179.204-.518.67-.806 1.17-.802.482.004.941.284 1.146.802.718 1.817 1.302 3.274 1.777 4.454.26-.596.567-1.288.928-2.103.694-1.565 1.591-3.592 2.754-6.265.258-.592.881-.906 1.397-.888.572.015 1.126.329 1.369.888 1.163 2.673 2.06 4.7 2.754 6.265 2.094 4.727 2.363 5.334 2.363 6.764 0 2.927-2.078 5.422-5 6.082v4.015h-3v-4.015c-.943-.213-1.797-.617-2.523-1.165-.612.845-1.466 1.48-2.477 1.79v3.39zm14.493-6c1.652 0 2.993 1.341 2.993 2.993s-1.341 2.993-2.993 2.993-2.993-1.341-2.993-2.993 1.341-2.993 2.993-2.993zm.007.993c1.104 0 2 .896 2 2s-.896 2-2 2-2-.896-2-2 .896-2 2-2zm-7.5 3.993v-3.839c4.906-.786 5-4.751 5-5.244 0-1.218-.216-1.705-2.277-6.359-2.134-4.82-2.721-6.198-2.755-6.261-.079-.145-.193-.292-.455-.297-.238 0-.37.092-.481.297-.034.063-.621 1.441-2.755 6.261-2.061 4.654-2.277 5.141-2.277 6.359 0 .493.094 4.458 5 5.244v3.839h1zm-6.123-12.448l-.08-.198c-1.589-3.957-2.04-5.116-2.067-5.171-.072-.151-.15-.226-.226-.228-.109 0-.188.13-.235.228-.028.05-.316.818-2.066 5.171-1.542 3.833-1.703 4.233-1.703 5.23 0 1.988 1.076 3.728 3.5 4.25v3.166h1v-3.166c1.266-.273 2.159-.876 2.725-1.666-1.078-1.12-1.725-2.619-1.725-4.251 0-.979.126-1.572.877-3.365z\",\n    title: \"Clean and Minimal\",\n    description:\n      \"Maecenas volutpat, diam enime volutpa cramas luctus interdum sodales.\",\n  },\n];\n\nexport const featureItems2 = [\n  {\n    id: 1,\n    className: \"col-lg-6\",\n    path: \"M21.62 20.196c1.055-.922 1.737-2.262 1.737-3.772 0-1.321-.521-2.515-1.357-3.412v-6.946l-11.001-6.066-11 6v12.131l11 5.869 5.468-2.917c.578.231 1.205.367 1.865.367.903 0 1.739-.258 2.471-.676l2.394 3.226.803-.596-2.38-3.208zm-11.121 2.404l-9.5-5.069v-10.447l9.5 4.946v10.57zm1-.001v-10.567l5.067-2.608.029.015.021-.04 4.384-2.256v5.039c-.774-.488-1.686-.782-2.668-.782-2.773 0-5.024 2.252-5.024 5.024 0 1.686.838 3.171 2.113 4.083l-3.922 2.092zm6.833-2.149c-2.219 0-4.024-1.808-4.024-4.026s1.805-4.025 4.024-4.025c2.22 0 4.025 1.807 4.025 4.025 0 2.218-1.805 4.026-4.025 4.026zm-.364-3.333l-1.306-1.147-.66.751 2.029 1.782 2.966-3.12-.725-.689-2.304 2.423zm-16.371-10.85l4.349-2.372 9.534 4.964-4.479 2.305-9.404-4.897zm9.4-5.127l9.404 5.186-3.832 1.972-9.565-4.98 3.993-2.178z\",\n    title: \"Unique Design\",\n    description:\n      \"Fusce aliquet quam eget neque ultrices elementum felis id arcu blandit sagittis.\",\n  },\n  {\n    id: 2,\n    className: \"col-lg-6\",\n    path: \"M12 0c-3.371 2.866-5.484 3-9 3v11.535c0 4.603 3.203 5.804 9 9.465 5.797-3.661 9-4.862 9-9.465v-11.535c-3.516 0-5.629-.134-9-3zm0 1.292c2.942 2.31 5.12 2.655 8 2.701v10.542c0 3.891-2.638 4.943-8 8.284-5.375-3.35-8-4.414-8-8.284v-10.542c2.88-.046 5.058-.391 8-2.701zm5 7.739l-5.992 6.623-3.672-3.931.701-.683 3.008 3.184 5.227-5.878.728.685z\",\n    title: \"Quality Code\",\n    description:\n      \"Lorem ipsum dolor sit amet rembe adipiscing elite Inwege maximus ligula imsum.\",\n  },\n  {\n    id: 3,\n    className: \"col-lg-6\",\n    path: \"M6.514 24.015h-3v-3.39c-2.08-.638-3.5-2.652-3.5-5.04 0-1.19.202-1.693 1.774-5.603.521-1.294 1.195-2.97 2.068-5.179.204-.518.67-.806 1.17-.802.482.004.941.284 1.146.802.718 1.817 1.302 3.274 1.777 4.454.26-.596.567-1.288.928-2.103.694-1.565 1.591-3.592 2.754-6.265.258-.592.881-.906 1.397-.888.572.015 1.126.329 1.369.888 1.163 2.673 2.06 4.7 2.754 6.265 2.094 4.727 2.363 5.334 2.363 6.764 0 2.927-2.078 5.422-5 6.082v4.015h-3v-4.015c-.943-.213-1.797-.617-2.523-1.165-.612.845-1.466 1.48-2.477 1.79v3.39zm14.493-6c1.652 0 2.993 1.341 2.993 2.993s-1.341 2.993-2.993 2.993-2.993-1.341-2.993-2.993 1.341-2.993 2.993-2.993zm.007.993c1.104 0 2 .896 2 2s-.896 2-2 2-2-.896-2-2 .896-2 2-2zm-7.5 3.993v-3.839c4.906-.786 5-4.751 5-5.244 0-1.218-.216-1.705-2.277-6.359-2.134-4.82-2.721-6.198-2.755-6.261-.079-.145-.193-.292-.455-.297-.238 0-.37.092-.481.297-.034.063-.621 1.441-2.755 6.261-2.061 4.654-2.277 5.141-2.277 6.359 0 .493.094 4.458 5 5.244v3.839h1zm-6.123-12.448l-.08-.198c-1.589-3.957-2.04-5.116-2.067-5.171-.072-.151-.15-.226-.226-.228-.109 0-.188.13-.235.228-.028.05-.316.818-2.066 5.171-1.542 3.833-1.703 4.233-1.703 5.23 0 1.988 1.076 3.728 3.5 4.25v3.166h1v-3.166c1.266-.273 2.159-.876 2.725-1.666-1.078-1.12-1.725-2.619-1.725-4.251 0-.979.126-1.572.877-3.365z\",\n    title: \"Clean and Minimal\",\n    description:\n      \"Maecenas volutpat, diam enime volutpa cramas luctus interdum sodales.\",\n  },\n  {\n    id: 4,\n    className: \"col-lg-6\",\n    path: \"M16 3.383l-.924-.383-7.297 17.617.924.383 7.297-17.617zm.287 3.617l6.153 4.825-6.173 5.175.678.737 7.055-5.912-7.048-5.578-.665.753zm-8.478 0l-6.249 4.825 6.003 5.175-.679.737-6.884-5.912 7.144-5.578.665.753z\",\n    title: \"Easy Customization\",\n    description:\n      \"Praesent sed nisi eleifend lorem ember fermete acome ante lorem ipsum.\",\n  },\n];\n\nexport const qualities = [\n  \"We're Creative\",\n  \"We're Punctual\",\n  \"We're Friendly\",\n  \"We're Professional\",\n  \"We're Witty\",\n  \"We're Honest\",\n  \"We're Smart\",\n  \"We're Resonance\",\n];\n\nexport const features = [\n  { id: 1, text: \"No credit card required\" },\n  { id: 2, text: \"Free 30 minute consultation\" },\n  { id: 3, text: \"Free marketing report\" },\n];\n\nexport const features2 = [\n  {\n    id: 1,\n    text: \"Business to business\",\n  },\n  {\n    id: 2,\n    text: \"Non-profit business\",\n  },\n  {\n    id: 3,\n    text: \"Business to customer\",\n  },\n  {\n    id: 4,\n    text: \"Ecommerce\",\n  },\n];\n\nexport const features3 = [\n  { text: \"We're professional\" },\n  { text: \"We're creative\" },\n  { text: \"We're honest\" },\n  { text: \"We're friendly\" },\n];\n\nexport const features4 = [\n  {\n    text: \"Lorem ipsum dolor sit amet, consectetur adipiscing.\",\n  },\n  {\n    text: \"Vivamus hendrerit eros vitae tincidunt vulputate.\",\n  },\n  {\n    text: \"Aenean at bibendum enim. In auctor consectetur urna.\",\n  },\n  {\n    text: \"Proin ut gravida lorem, quis scelerisque metus.\",\n  },\n];\n\nexport const features5 = [\n  {\n    titleKey: \"services.results.title\",\n    descriptionKey: \"services.results.text\",\n    svgPath:\n      \"M21.62 20.196c1.055-.922 1.737-2.262 1.737-3.772 0-1.321-.521-2.515-1.357-3.412v-6.946l-11.001-6.066-11 6v12.131l11 5.869 5.468-2.917c.578.231 1.205.367 1.865.367.903 0 1.739-.258 2.471-.676l2.394 3.226.803-.596-2.38-3.208zm-11.121 2.404l-9.5-5.069v-10.447l9.5 4.946v10.57zm1-.001v-10.567l5.067-2.608.029.015.021-.04 4.384-2.256v5.039c-.774-.488-1.686-.782-2.668-.782-2.773 0-5.024 2.252-5.024 5.024 0 1.686.838 3.171 2.113 4.083l-3.922 2.092zm6.833-2.149c-2.219 0-4.024-1.808-4.024-4.026s1.805-4.025 4.024-4.025c2.22 0 4.025 1.807 4.025 4.025 0 2.218-1.805 4.026-4.025 4.026zm-.364-3.333l-1.306-1.147-.66.751 2.029 1.782 2.966-3.12-.725-.689-2.304 2.423zm-16.371-10.85l4.349-2.372 9.534 4.964-4.479 2.305-9.404-4.897zm9.4-5.127l9.404 5.186-3.832 1.972-9.565-4.98 3.993-2.178z\",\n  },\n  {\n    titleKey: \"services.innovation.title\",\n    descriptionKey: \"services.innovation.text\",\n    svgPath:\n      \"M12 0c-3.371 2.866-5.484 3-9 3v11.535c0 4.603 3.203 5.804 9 9.465 5.797-3.661 9-4.862 9-9.465v-11.535c-3.516 0-5.629-.134-9-3zm0 1.292c2.942 2.31 5.12 2.655 8 2.701v10.542c0 3.891-2.638 4.943-8 8.284-5.375-3.35-8-4.414-8-8.284v-10.542c2.88-.046 5.058-.391 8-2.701zm5 7.739l-5.992 6.623-3.672-3.931.701-.683 3.008 3.184 5.227-5.878.728.685z\",\n  },\n  {\n    titleKey: \"services.business.title\",\n    descriptionKey: \"services.business.text\",\n    svgPath:\n      \"M6.514 24.015h-3v-3.39c-2.08-.638-3.5-2.652-3.5-5.04 0-1.19.202-1.693 1.774-5.603.521-1.294 1.195-2.97 2.068-5.179.204-.518.67-.806 1.17-.802.482.004.941.284 1.146.802.718 1.817 1.302 3.274 1.777 4.454.26-.596.567-1.288.928-2.103.694-1.565 1.591-3.592 2.754-6.265.258-.592.881-.906 1.397-.888.572.015 1.126.329 1.369.888 1.163 2.673 2.06 4.7 2.754 6.265 2.094 4.727 2.363 5.334 2.363 6.764 0 2.927-2.078 5.422-5 6.082v4.015h-3v-4.015c-.943-.213-1.797-.617-2.523-1.165-.612.845-1.466 1.48-2.477 1.79v3.39zm14.493-6c1.652 0 2.993 1.341 2.993 2.993s-1.341 2.993-2.993 2.993-2.993-1.341-2.993-2.993 1.341-2.993 2.993-2.993zm.007.993c1.104 0 2 .896 2 2s-.896 2-2 2-2-.896-2-2 .896-2 2-2zm-7.5 3.993v-3.839c4.906-.786 5-4.751 5-5.244 0-1.218-.216-1.705-2.277-6.359-2.134-4.82-2.721-6.198-2.755-6.261-.079-.145-.193-.292-.455-.297-.238 0-.37.092-.481.297-.034.063-.621 1.441-2.755 6.261-2.061 4.654-2.277 5.141-2.277 6.359 0 .493.094 4.458 5 5.244v3.839h1zm-6.123-12.448l-.08-.198c-1.589-3.957-2.04-5.116-2.067-5.171-.072-.151-.15-.226-.226-.228-.109 0-.188.13-.235.228-.028.05-.316.818-2.066 5.171-1.542 3.833-1.703 4.233-1.703 5.23 0 1.988 1.076 3.728 3.5 4.25v3.166h1v-3.166c1.266-.273 2.159-.876 2.725-1.666-1.078-1.12-1.725-2.619-1.725-4.251 0-.979.126-1.572.877-3.365z\",\n  },\n  {\n    titleKey: \"services.support.title\",\n    descriptionKey: \"services.support.text\",\n    svgPath:\n      \"M4.57 18.55l2.43.335-1.769 1.7.432 2.415-2.163-1.157-2.163 1.157.432-2.415-1.769-1.7 2.43-.335 1.07-2.207 1.07 2.207zm8.5 0l2.43.335-1.769 1.7.432 2.415-2.163-1.157-2.163 1.157.432-2.415-1.769-1.7 2.43-.335 1.07-2.207 1.07 2.207zm8.5 0l2.43.335-1.769 1.7.432 2.415-2.163-1.157-2.163 1.157.432-2.415-1.769-1.7 2.43-.335 1.07-2.207 1.07 2.207zm-18.473.918l-.917.126.667.641-.163.91.816-.436.816.436-.163-.91.667-.641-.917-.126-.403-.832-.403.832zm8.5 0l-.917.126.667.641-.163.91.816-.436.816.436-.163-.91.667-.641-.917-.126-.403-.832-.403.832zm8.5 0l-.917.126.667.641-.163.91.816-.436.816.436-.163-.91.667-.641-.917-.126-.403-.832-.403.832zm-14.497-5.293l-4-8h5.381c-.3-1.42-1.573-2.5-3.066-2.5-1.838 0-3.315 1.662-3.315 3.5s1.477 3.5 3.315 3.5h2.684l.002.01c.283-.397.618-.747.995-1.041zm.915-8h-1.464c.691-.592 1.579-1 2.564-1 1.719 0 3.125 1.266 3.125 3.5s-1.406 3.5-3.125 3.5c-1.718 0-3.125-1.266-3.125-3.5 0-.39.065-.764.179-1.115-.293.381-.49.841-.553 1.354h3.662c.32-.662.866-1.211 1.559-1.538zm9.932 8.5h-1.25c-.427-.59-.994-1.082-1.661-1.431-.153-.446-.411-.838-.742-1.141h2.203c.3-1.42 1.573-2.5 3.066-2.5 1.838 0 3.315 1.662 3.315 3.5s-1.477 3.5-3.315 3.5h-2.684zm-10.848 0h-1.715c-.09-.465-.365-.869-.752-1.155.337-.312.596-.709.762-1.162h3.183c-.158.418-.278.87-.348 1.349zm8.348 8h-1.715c-.09-.465-.365-.869-.752-1.155.337-.312.596-.709.762-1.162h3.183c-.158.418-.278.87-.348 1.349zm-6.5 0h-1.25c-.427-.59-.994-1.082-1.661-1.431-.153-.446-.411-.838-.742-1.141h2.203c.3-1.42 1.573-2.5 3.066-2.5 1.838 0 3.315 1.662 3.315 3.5s-1.477 3.5-3.315 3.5h-2.684z\",\n  },\n];\n\n// Helper function to get translated features\nexport const getTranslatedFeatures = (t) => {\n  return features5.map((feature) => ({\n    ...feature,\n    title: t(feature.titleKey),\n    description: t(feature.descriptionKey),\n  }));\n};\n\nexport const featuresData = [\n  {\n    parentClass: \"mb-100 mb-md-60\",\n    number: \"01\",\n    title1: \"Unique\",\n    title2: \"Design\",\n    desc: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. In sem dolor, rhoncus eleifend fermentum vel, bibendum vel neque. Cras eu elementum enim. Curabitur elit leo, fringilla in velit at, fermentum semper augue. Vestibulum sit amet venenatis metus. Aenean a arcu sed augue consequat sagittis.`,\n    iconPath:\n      \"M24 26.5c0 0 1-0.625 2.5-0.875 2.625-0.5 4.875-0.625 5.875 1.125 1.25 2.125 2.375 6.625 2.125 10.625-0.125 2.125-0.625 2.75-0.625 2.75s-1.875 1.25-5.875 0.875c-4.375-0.375-5-1.875-5-1.875s0.625-1.5 1-5.375 0-7.25 0-7.25zM19 35.625c-2.125 0.5-4.75 1.25-4.75 1.25s5.625-0.5 8-1.125c-0.5 2.625-1.25 4.25-1.25 4.25s-10.375 10-14.5 10c-3.5 0-6.5-8.5-6.5-18s3.25-18 6.5-18c4.375 0 13.25 7.125 16 12.125 0 0 0.375 2 0.25 4-1.25-0.625-3-1.25-5.5-1.75-3.5-0.75-5.25-0.375-5.25-0.375s2.25 0.5 7.125 2.125c1.875 0.625 2.75 1 3.625 1.25 0 0.375-0.125 0.875-0.125 1.25 0 0.625 0 1.25-0.125 1.875-0.625 0.25-1.875 0.75-3.5 1.125zM49.5 14c3.25 0 6.5 8.5 6.5 18s-3 18-6.5 18c-4.625 0-13-8.5-14-9.875 0 0 0.5-0.625 0.625-2.75v-1.125c2 0.875 8.75 1.625 8.75 1.625s-2.625-0.75-5-1.375c-1.625-0.5-3.125-1-3.75-1.25-0.125-1.375-0.25-2.875-0.5-4.125 2.125-1.375 8.625-4.25 8.625-4.25s-1.875 0.25-4.375 1.125c-1.75 0.625-3.625 1.375-4.625 1.75-0.375-1.25-0.875-2.25-1.25-3 0-0.125-0.125-0.5-0.125-0.5v-0.125c2.75-5 11.125-12.125 15.625-12.125z\",\n  },\n  {\n    parentClass: \"mt-n140 mt-sm-0 mb-100 mb-md-60\",\n    number: \"02\",\n    title1: \"Quality\",\n    title2: \"Code\",\n    desc: `Praesent nec tempus dui. Curabitur molestie, sapien semper dictum porttitor, odio ipsum hendrerit urna, quis rutrum mi tortor quis velit. Nulla scelerisque eu nisi id volutpat. Nullam fringilla, eros ut semper tincidunt, massa ante congue erat, in vestibulum nibh urna vitae lorem aliquet ornare.`,\n    iconPath:\n      \"M0 46l29.5-29.5 10 10-29.5 29.5h-10v-10zM47.25 18.75l-5 5-10-10 5-5c1.125-1.125 2.5-1.125 3.625 0l6.375 6.375c1.125 1.125 1.125 2.5 0 3.625z\",\n  },\n  {\n    parentClass: \"mb-md-60\",\n    number: \"03\",\n    title1: \"Clean and\",\n    title2: \"Minimal\",\n    desc: `Integer sit amet nunc sem. Quisque scelerisque dui id fermentum venenatis. Cras non tempor nisi. Aliquam lobortis ligula id efficitur iaculis ipum. Curabitur elit leo, fringilla in velit at, fermentum semper augue. Vestibulum sit amet venenatis metus. Aenean a arcu sed augue consequat sagittis.`,\n    iconPath:\n      \"M24 8c13.25 0 24 9.625 24 21.375 0 7.375-6 13.25-13.375 13.25h-4.75c-2.25 0-4 1.75-4 4 0 1.125 0.375 2.125 1 2.75s1.125 1.5 1.125 2.625c0 2.25-1.75 4-4 4-13.25 0-24-10.75-24-24s10.75-24 24-24zM9.375 32c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4zM17.375 21.375c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4zM30.625 21.375c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4zM38.625 32c2.25 0 4-1.75 4-4s-1.75-4-4-4-4 1.75-4 4 1.75 4 4 4z\",\n  },\n  {\n    parentClass: \"mt-n140 mt-sm-0 mb-0\",\n    number: \"04\",\n    title1: \"Premium\",\n    title2: \"Suport\",\n    desc: `Aliquam nec erat eleifend, volutpat dolor eu, ultrices erat. Etiam et arcu nec quam laoreet hendrerit eget sed velit. Nullam dapibus magna odio. Fusce convallis elit at ex sodales, quis maximus augue hendrerit. Phasellus eget turpis vel nibh convallis vulputate. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`,\n    iconPath:\n      \"M25.25 46.625l-15.625 9.375 4.125-17.75-13.75-12 18.125-1.5 7.125-16.75 7.125 16.75 18.125 1.5-13.75 12 4.125 17.75z\",\n  },\n];\n\nexport const featuresListData = [\n  { text: \"Agency Website\" },\n  { text: \"Personal Website\" },\n  { text: \"Landing Page\" },\n  { text: \"Portfolio and Gallery\" },\n];\n", "// client/src/pages/services/page.jsx\n\nimport React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport { Link } from \"react-router-dom\";\nimport { menuItems } from \"@/data/menu\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport { getTranslatedFeatures } from \"@/data/features\";\nimport { services6 } from \"@/data/services\";\nimport { useTranslation } from \"react-i18next\";\n\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\nimport \"@/styles/benefits-cards.css\";\nexport default function ElegantServicesPageDark() {\n  const { t, i18n } = useTranslation();\n  const currentLanguage = i18n.language || \"et\";\n  // Get translated features directly using the t function\n  const translatedFeatures = getTranslatedFeatures(t);\n\n  const seoData = getPageSEOData(\"services\");\n\n  const title =\n    currentLanguage === \"en\"\n      ? seoData.title\n      : \"Teenused | DevSkills - Äri Transformatsioon Tehnoloogia Abil\";\n\n  const description =\n    currentLanguage === \"en\"\n      ? seoData.description\n      : \"Avasta, kuidas DevSkills saab transformeerida sinu ettevõtet tipptasemel tehnoloogialahenduste, tehisintellekti rakendamise ja äriprotsesside optimeerimise abil.\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title={title}\n        description={description}\n        slug=\"services\"\n        type=\"website\"\n        image=\"https://devskills.ee/services.jpg\"\n        schema={seoData.schema}\n        keywords={seoData.keywords}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/7.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"services.page.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"services.page.subtitle\")}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <>\n                {/* Services Section */}\n                <section className=\"page-section bg-dark-1 light-content pb-0\">\n                  <div className=\"container\">\n                    <div className=\"row mb-n30\">\n                      {services6.map((elm, i) => (\n                        <div\n                          key={i}\n                          className=\"col-md-6 col-lg-4 d-flex align-items-stretch mb-30\"\n                        >\n                          <div className=\"service-card-container\">\n                            <div className=\"service-card-img\">\n                              <img\n                                src={elm.image}\n                                width={400}\n                                height={250}\n                                alt={t(`services.${elm.key}.title`)}\n                                className=\"service-card-image\"\n                              />\n                            </div>\n                            <div className=\"service-card-content\">\n                              <h3 className=\"service-card-title\">\n                                {t(`services.${elm.key}.title`)}\n                              </h3>\n                              <div className=\"service-card-text\">\n                                {t(`services.${elm.key}.text`)}\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </section>\n                {/* End Services Section */}\n                {/* Marquee Section */}\n                <div className=\"page-section overflow-hidden\">\n                  <MarqueeDark />\n                </div>\n                {/* End Marquee Section */}\n                {/* Benefits Section */}\n                <section className=\"page-section bg-dark-1 light-content pt-0 z-index-1\">\n                  <div className=\"container position-relative\">\n                    {/* Grid */}\n                    <div className=\"row\">\n                      {/* Text */}\n                      <div className=\"col-md-12 col-lg-3 mb-md-50\">\n                        <h2 className=\"section-caption mb-xs-10\">\n                          {t(\"services.approach.title\")}\n                        </h2>\n                        <h3 className=\"section-title-small mb-40\">\n                          {t(\"services.approach.subtitle\")}\n                        </h3>\n                        <div className=\"section-line\" />\n                      </div>\n                      {/* End Text */}\n                      {/* Feature Item */}\n                      {translatedFeatures.slice(0, 3).map((elm, i) => (\n                        <div\n                          key={i}\n                          className=\"col-md-4 col-lg-3 d-flex align-items-stretch mb-sm-30\"\n                        >\n                          <div className=\"alt-features-item border-left mt-0 benefits-card\">\n                            <div className=\"alt-features-icon icon-hover-anim\">\n                              <span className=\"icon-strong icon-strong-unhovered\">\n                                <svg\n                                  width={24}\n                                  height={24}\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  aria-hidden=\"true\"\n                                  focusable=\"false\"\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fillRule=\"evenodd\"\n                                  clipRule=\"evenodd\"\n                                >\n                                  <path d={elm.svgPath} />\n                                </svg>\n                              </span>\n                              <span\n                                className=\"icon-strong icon-strong-hovered\"\n                                aria-hidden=\"true\"\n                              >\n                                <svg\n                                  width={24}\n                                  height={24}\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  aria-hidden=\"true\"\n                                  focusable=\"false\"\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fillRule=\"evenodd\"\n                                  clipRule=\"evenodd\"\n                                >\n                                  <path d={elm.svgPath} />\n                                </svg>\n                              </span>\n                            </div>\n                            <h4 className=\"alt-features-title\">{elm.title}</h4>\n                            <div className=\"alt-features-descr\">\n                              {elm.description}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n\n                      {/* End Feature Item */}\n                    </div>\n                    {/* End Grid */}\n                  </div>\n                </section>\n                {/* End Benefits Section */}\n                {/* Divider */}\n                <hr className=\"mt-0 mb-0 white\" />\n                {/* End Divider */}\n                {/* Call Action Section */}\n                <section\n                  className=\"page-section light-content\"\n                  style={{ backgroundColor: \"#000000\" }}\n                >\n                  <div className=\"container position-relative\">\n                    {/* Decorative Waves */}\n                    <div className=\"position-relative\">\n                      <div\n                        className=\"decoration-21 d-none d-lg-block\"\n                        data-rellax-y=\"\"\n                        data-rellax-speed=\"0.7\"\n                        data-rellax-percentage=\"0.35\"\n                      >\n                        <img\n                          src=\"/assets/images/decoration-3.svg\"\n                          className=\"svg-shape\"\n                          width={148}\n                          height={148}\n                          alt=\"\"\n                        />\n                      </div>\n                    </div>\n                    {/* End Decorative Waves */}\n                    <div className=\"row text-center wow fadeInUp\">\n                      <div className=\"col-md-10 offset-md-1 col-lg-6 offset-lg-3\">\n                        <p className=\"section-descr mb-50 mb-sm-30\">\n                          {t(\"services.cta.text\")}\n                        </p>\n                        <div className=\"local-scroll\">\n                          <Link\n                            to=\"/contact\"\n                            className=\"btn btn-mod btn-large btn-circle btn-hover-anim btn-w\"\n                          >\n                            <span>{t(\"services.cta.button\")}</span>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </section>\n              </>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n", "import React from \"react\";\nimport Footer from \"@/components/footers/Footer\";\nimport Header from \"@/components/headers/Header\";\nimport { menuItems } from \"@/data/menu\";\nimport Contact from \"@/components/home/<USER>\";\nimport MarqueeDark from \"@/components/home/<USER>\";\nimport Map from \"@/components/common/Map\";\nimport { useTranslation } from \"react-i18next\";\n\nimport UnifiedSEO from \"@/components/common/UnifiedSEO\";\nimport { getPageSEOData } from \"@/utils/seoHelpers\";\n\nconst dark = true;\nexport default function ElegantContactPageDark() {\n  const { t, currentLanguage } = useTranslation();\n\n  const seoData = getPageSEOData(\"contact\");\n\n  const title =\n    currentLanguage === \"en\"\n      ? seoData.title\n      : \"Kontakt | DevSkills - Võta Meie Meeskonnaga Ühendust\";\n\n  const description =\n    currentLanguage === \"en\"\n      ? seoData.description\n      : \"Võta ühendust DevSkills-iga äri transformatsiooni, tarkvara arenduse ja tehisintellekti rakendamise teenuste osas.\";\n\n  return (\n    <>\n      <UnifiedSEO\n        title={title}\n        description={description}\n        slug=\"contact\"\n        type=\"website\"\n        image=\"https://devskills.ee/contact.jpg\"\n        schema={seoData.schema}\n        keywords={seoData.keywords}\n      />\n      <div className=\"theme-elegant\">\n        <div className=\"dark-mode\">\n          <div className=\"page bg-dark-1\" id=\"top\">\n            <nav className=\"main-nav dark transparent stick-fixed wow-menubar\">\n              <Header links={menuItems} />\n            </nav>\n            <main id=\"main\">\n              <section\n                className=\"page-section bg-dark-alpha-50 light-content\"\n                style={{\n                  backgroundImage: \"url(/assets/images/demo-elegant/3.jpg)\",\n                }}\n                id=\"home\"\n              >\n                <div className=\"container position-relative pt-20 pt-sm-20 text-center\">\n                  <h1\n                    className=\"hs-title-3 mb-10 wow fadeInUpShort\"\n                    data-wow-duration=\"0.6s\"\n                  >\n                    {t(\"contact.page.title\")}\n                  </h1>\n                  <div className=\"row wow fadeIn\" data-wow-delay=\"0.2s\">\n                    <div className=\"col-md-8 offset-md-2 col-lg-6 offset-lg-3\">\n                      <p className=\"section-title-tiny mb-0 opacity-075\">\n                        {t(\"contact.page.subtitle\")}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </section>\n              <section\n                className={`page-section  scrollSpysection mb-0 pb-0  ${\n                  dark ? \"bg-dark-1 light-content\" : \"\"\n                } `}\n                id=\"contact\"\n              >\n                <Contact />\n              </section>\n              <div className=\"page-section overflow-hidden\">\n                <MarqueeDark />\n              </div>\n              <div className=\"google-map light-content\">\n                <Map />\n              </div>\n\n              {/* Legal Links Section */}\n              <section className=\"page-section bg-dark-1 light-content pt-40 pb-40\">\n                <div className=\"container\">\n                  <div className=\"row\">\n                    <div className=\"col-lg-8 offset-lg-2 text-center\">\n                      <h3 className=\"section-title-small mb-30\">\n                        {t(\"legal.title\")}\n                      </h3>\n                      <div className=\"row\">\n                        <div className=\"col-md-6 mb-30\">\n                          <div className=\"card bg-dark-2 border-0 h-100\">\n                            <div className=\"card-body text-center p-40\">\n                              <div className=\"mb-20\">\n                                <i\n                                  className=\"mi-shield text-primary\"\n                                  style={{ fontSize: \"2.5rem\" }}\n                                ></i>\n                              </div>\n                              <h4 className=\"card-title text-white mb-20\">\n                                {t(\"legal.privacy.title\")}\n                              </h4>\n                              <p className=\"text-gray mb-30\">\n                                {t(\"legal.privacy.description\")}\n                              </p>\n                              <a\n                                href={`/${currentLanguage}/privacy-policy`}\n                                className=\"btn btn-mod btn-border btn-circle btn-medium\"\n                              >\n                                {t(\"legal.privacy.button\")}\n                              </a>\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"col-md-6 mb-30\">\n                          <div className=\"card bg-dark-2 border-0 h-100\">\n                            <div className=\"card-body text-center p-40\">\n                              <div className=\"mb-20\">\n                                <i\n                                  className=\"mi-document text-primary\"\n                                  style={{ fontSize: \"2.5rem\" }}\n                                ></i>\n                              </div>\n                              <h4 className=\"card-title text-white mb-20\">\n                                {t(\"legal.terms.title\")}\n                              </h4>\n                              <p className=\"text-gray mb-30\">\n                                {t(\"legal.terms.description\")}\n                              </p>\n                              <a\n                                href={`/${currentLanguage}/terms-conditions`}\n                                className=\"btn btn-mod btn-border btn-circle btn-medium\"\n                              >\n                                {t(\"legal.terms.button\")}\n                              </a>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </section>\n            </main>\n            <footer className=\"bg-dark-2 light-content footer z-index-1 position-relative\">\n              <Footer />\n            </footer>\n          </div>{\" \"}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": ["progressData", "aboutSchema", "ElegantAboutPageDark", "t", "useTranslation", "seoData", "getPageSEOData", "jsxs", "Fragment", "jsx", "UnifiedSEO", "Header", "menuItems", "About", "MarqueeDark", "Link", "Team", "elm", "i", "Footer", "features5", "getTranslatedFeatures", "feature", "ElegantServicesPageDark", "i18n", "currentLanguage", "translatedFeatures", "title", "description", "services6", "ElegantContactPageDark", "Contact", "Map"], "mappings": "4SAEO,MAAMA,EAAe,CAC1B,CAAE,IAAK,aAAc,MAAO,EAAI,EAChC,CAAE,IAAK,KAAM,MAAO,EAAI,EACxB,CAAE,IAAK,cAAe,MAAO,EAAI,EACjC,CAAE,IAAK,SAAU,MAAO,EAAI,EAC5B,CAAE,IAAK,WAAY,MAAO,EAAI,CAChC,ECUMC,EAAc,CAClB,WAAY,qBACZ,QAAS,YACT,KAAM,kBACN,YACE,qIACF,UAAW,CACT,QAAS,eACT,KAAM,YACN,KAAM,+BAAA,CAEV,EACA,SAAwBC,GAAuB,CACvC,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvBC,EAAUC,EAAe,OAAO,EAEtC,OAEIC,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAOL,EAAQ,MACf,YAAaA,EAAQ,YACrB,KAAK,QACL,KAAK,UACL,MAAM,iCACN,OAAQ,CAACJ,EAAa,GAAGI,EAAQ,MAAM,EACvC,SAAUA,EAAQ,QAAA,CACpB,QACC,MAAI,CAAA,UAAU,gBACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,aAAa,CAAA,CAClB,QACC,MAAI,CAAA,UAAU,iBAAiB,iBAAe,OAC7C,eAAC,MAAI,CAAA,UAAU,4CACb,SAAAA,MAAC,KAAE,UAAU,sCACV,WAAE,gBAAgB,EACrB,EACF,CACF,CAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,cAAe,CAAA,CAAA,CAChC,CAAA,CAAA,CACF,EACAA,EAAA,IAAC,UAAA,CACC,UAAW,+DAGX,GAAG,QAEH,eAAC,MAAI,CAAA,UAAU,8BACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,8CACb,SAACA,EAAA,IAAA,MAAA,CACC,gBAAC,MAAI,CAAA,UAAU,kBAAkB,iBAAe,QAC9C,SAAA,CAACF,EAAAA,KAAA,KAAA,CAAG,UAAU,+BACZ,SAAA,CAACE,EAAAA,IAAA,OAAA,CAAK,UAAU,YACb,SAAEN,EAAA,qBAAqB,EAAE,MAAM,GAAG,EAAE,CAAC,CACxC,CAAA,EAAQ,IACPA,EAAE,qBAAqB,EAAE,MAAM,GAAG,EAAE,CAAC,EACrCM,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAC,GAAA,CAAA,CAAA,EAC/B,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAAAE,MAAC,IAAE,CAAA,UAAU,OAAQ,SAAAN,EAAE,qBAAqB,EAAE,QAC7C,IAAE,CAAA,UAAU,OAAQ,SAAAA,EAAE,qBAAqB,CAAE,CAAA,CAAA,CAChD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,QACCU,EAAM,CAAA,CAAA,CAAA,CAAA,CACT,CACF,CAAA,CAAA,CACF,QACC,MAAI,CAAA,UAAU,+BACb,SAAAJ,MAACK,GAAY,CAAA,EACf,EACAL,EAAA,IAAC,UAAA,CACC,UAAU,6EACV,MAAO,CACL,gBACE,4DACJ,EAEA,eAAC,MAAI,CAAA,UAAU,8BACb,SAACF,EAAAA,KAAA,MAAA,CAAI,UAAU,MACb,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oBACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,6EACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,2DACJ,MAAO,IACP,OAAQ,IACR,IAAI,8DACJ,UAAU,kBAAA,CAAA,EAEd,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,wBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,8BACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,QAEvB,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,uDACJ,IAAI,2CACJ,MAAO,IACP,OAAQ,IACR,UAAU,kBAAA,CAAA,CACZ,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAA,EAAA,IAAC,MAAI,CAAA,UAAU,iDACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,oBACb,SAAAF,OAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,+BACX,SAAAN,EAAE,uBAAuB,EAC5B,EACAI,EAAAA,KAAC,MAAI,CAAA,UAAU,2BACb,SAAA,CAAAE,MAAC,IAAE,CAAA,UAAU,OAAQ,SAAAN,EAAE,uBAAuB,EAAE,QAC/C,IAAE,CAAA,UAAU,OAAQ,SAAAA,EAAE,uBAAuB,CAAE,CAAA,CAAA,EAClD,EACAM,EAAAA,IAAC,MAAI,CAAA,UAAU,eACb,SAAAF,EAAA,KAACQ,EAAA,CACC,GAAG,YACH,UAAU,6CACV,oBAAkB,IAElB,SAAA,CAACR,EAAAA,KAAA,OAAA,CAAK,UAAU,oCACb,SAAA,CAAAJ,EAAE,wBAAwB,EAAG,IAC9BM,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,EACH,EACAF,EAAA,KAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEX,SAAA,CAAAJ,EAAE,wBAAwB,EAAG,IAC9BM,EAAA,IAAC,IAAA,CACC,UAAU,sCACV,cAAY,MAAA,CAAA,CACb,CAAA,CAAA,CACH,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,EACAA,EAAA,IAAC,UAAA,CACC,UAAW,gEAGX,GAAG,OAEH,eAACO,EAAK,CAAA,CAAA,CAAA,CACR,QACC,MAAI,CAAA,UAAU,+BACb,SAAAP,MAACK,GAAY,CAAA,EACf,EAGEP,OAAAC,EAAAA,SAAA,CAAA,SAAA,CAACC,EAAA,IAAA,UAAA,CAAQ,UAAU,6EACjB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAAAF,OAAC,MAAI,CAAA,UAAU,wBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,sBACX,SAAAN,EAAE,oBAAoB,EACzB,QACC,IAAE,CAAA,UAAU,iBACV,SAAAA,EAAE,2BAA2B,EAChC,QACC,IAAE,CAAA,UAAU,iBACV,SAAAA,EAAE,2BAA2B,CAChC,CAAA,CAAA,EACF,EACCM,EAAA,IAAA,MAAA,CAAI,UAAU,qCAEZ,SAAaT,EAAA,IAAI,CAACiB,EAAKC,IACtBT,EAAAA,IAAC,MAAY,CAAA,UAAU,wBACrB,SAAAF,EAAA,KAAC,MAAA,CACC,UAAU,eACV,KAAK,cACL,MAAO,CAAE,MAAO,GAAGU,EAAI,KAAK,GAAI,EAEhC,SAAA,CAAAV,OAAC,MAAK,CAAA,SAAA,CAAEJ,EAAA,gBAAgBc,EAAI,GAAG,EAAE,EAAE,KAAA,EAAG,EACtCR,EAAAA,IAAC,OAAM,CAAA,SAAAQ,EAAI,KAAM,CAAA,CAAA,CAAA,CAAA,CACnB,EARQC,CASV,CACD,CAIH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAT,EAAAA,IAAC,KAAG,CAAA,UAAU,iBAAkB,CAAA,EAGhCA,EAAA,IAAC,UAAA,CACC,UAAU,6BACV,MAAO,CAAE,gBAAiB,SAAU,EAEpC,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,8BAEb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,kCACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,OAEvB,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,kCACJ,UAAU,YACV,MAAO,IACP,OAAQ,IACR,IAAI,EAAA,CAAA,CACN,CAAA,EAEJ,QAEC,MAAI,CAAA,UAAU,+BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAAAE,MAAC,IAAE,CAAA,UAAU,+BACV,SAAAN,EAAE,gBAAgB,EACrB,EACAM,EAAAA,IAAC,MAAI,CAAA,UAAU,eACb,SAAAA,EAAA,IAACM,EAAA,CACC,GAAI,WACJ,UAAU,wDAEV,SAACN,EAAA,IAAA,OAAA,CAAM,SAAEN,EAAA,kBAAkB,CAAE,CAAA,CAAA,CAAA,CAEjC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAM,EAAA,IAACU,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,8GC1KaC,EAAY,CACvB,CACE,SAAU,yBACV,eAAgB,wBAChB,QACE,0wBACH,EACD,CACE,SAAU,4BACV,eAAgB,2BAChB,QACE,qVACH,EACD,CACE,SAAU,0BACV,eAAgB,yBAChB,QACE,qvCACH,EACD,CACE,SAAU,yBACV,eAAgB,wBAChB,QACE,8hDACH,CACH,EAGaC,EAAyBlB,GAC7BiB,EAAU,IAAKE,IAAa,CACjC,GAAGA,EACH,MAAOnB,EAAEmB,EAAQ,QAAQ,EACzB,YAAanB,EAAEmB,EAAQ,cAAc,CACzC,EAAI,EC1IJ,SAAwBC,GAA0B,CAChD,KAAM,CAAE,EAAApB,EAAG,KAAAqB,CAAK,EAAIpB,EAAe,EAC7BqB,EAAkBD,EAAK,UAAY,KAEnCE,EAAqBL,EAAsBlB,CAAC,EAE5CE,EAAUC,EAAe,UAAU,EAEnCqB,EACJF,IAAoB,KAChBpB,EAAQ,MACR,+DAEAuB,EACJH,IAAoB,KAChBpB,EAAQ,YACR,oKAEN,OAEIE,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAAiB,EACA,YAAAC,EACA,KAAK,WACL,KAAK,UACL,MAAM,oCACN,OAAQvB,EAAQ,OAChB,SAAUA,EAAQ,QAAA,CACpB,QACC,MAAI,CAAA,UAAU,gBACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,qBAAqB,CAAA,CAC1B,QACC,MAAI,CAAA,UAAU,iBAAiB,iBAAe,OAC7C,eAAC,MAAI,CAAA,UAAU,4CACb,SAAAA,EAAA,IAAC,KAAE,UAAU,sCACV,WAAE,wBAAwB,CAC7B,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EAGEF,OAAAC,EAAAA,SAAA,CAAA,SAAA,CAAAC,MAAC,UAAQ,CAAA,UAAU,4CACjB,SAAAA,EAAAA,IAAC,OAAI,UAAU,YACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,aACZ,SAAAoB,EAAU,IAAI,CAACZ,EAAKC,IACnBT,EAAA,IAAC,MAAA,CAEC,UAAU,qDAEV,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,mBACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAKQ,EAAI,MACT,MAAO,IACP,OAAQ,IACR,IAAKd,EAAE,YAAYc,EAAI,GAAG,QAAQ,EAClC,UAAU,oBAAA,CAAA,EAEd,EACAV,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAACE,EAAAA,IAAA,KAAA,CAAG,UAAU,qBACX,SAAAN,EAAE,YAAYc,EAAI,GAAG,QAAQ,CAChC,CAAA,EACAR,EAAAA,IAAC,OAAI,UAAU,oBACZ,WAAE,YAAYQ,EAAI,GAAG,OAAO,CAC/B,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EArBKC,CAAA,CAuBR,CACH,CAAA,CACF,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,+BACb,SAAAT,MAACK,GAAY,CAAA,EACf,EAGAL,EAAA,IAAC,UAAQ,CAAA,UAAU,sDACjB,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,8BAEb,SAAAF,OAAC,MAAI,CAAA,UAAU,MAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,2BACX,SAAAN,EAAE,yBAAyB,EAC9B,QACC,KAAG,CAAA,UAAU,4BACX,SAAAA,EAAE,4BAA4B,EACjC,EACAM,EAAAA,IAAC,MAAI,CAAA,UAAU,cAAe,CAAA,CAAA,EAChC,EAGCiB,EAAmB,MAAM,EAAG,CAAC,EAAE,IAAI,CAACT,EAAKC,IACxCT,EAAA,IAAC,MAAA,CAEC,UAAU,wDAEV,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACE,EAAAA,IAAA,OAAA,CAAK,UAAU,oCACd,SAAAA,EAAA,IAAC,MAAA,CACC,MAAO,GACP,OAAQ,GACR,QAAQ,YACR,KAAK,eACL,cAAY,OACZ,UAAU,QACV,MAAM,6BACN,SAAS,UACT,SAAS,UAET,SAACA,EAAA,IAAA,OAAA,CAAK,EAAGQ,EAAI,OAAS,CAAA,CAAA,CAAA,EAE1B,EACAR,EAAA,IAAC,OAAA,CACC,UAAU,kCACV,cAAY,OAEZ,SAAAA,EAAA,IAAC,MAAA,CACC,MAAO,GACP,OAAQ,GACR,QAAQ,YACR,KAAK,eACL,cAAY,OACZ,UAAU,QACV,MAAM,6BACN,SAAS,UACT,SAAS,UAET,SAACA,EAAA,IAAA,OAAA,CAAK,EAAGQ,EAAI,OAAS,CAAA,CAAA,CAAA,CACxB,CAAA,CACF,EACF,EACCR,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAsB,WAAI,MAAM,EAC7CA,EAAA,IAAA,MAAA,CAAI,UAAU,qBACZ,WAAI,WACP,CAAA,CAAA,CACF,CAAA,CAAA,EA3CKS,CA6CR,CAAA,CAAA,CAGH,CAAA,CAEF,CAAA,EACF,EAGAT,EAAAA,IAAC,KAAG,CAAA,UAAU,iBAAkB,CAAA,EAGhCA,EAAA,IAAC,UAAA,CACC,UAAU,6BACV,MAAO,CAAE,gBAAiB,SAAU,EAEpC,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,8BAEb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,kCACV,gBAAc,GACd,oBAAkB,MAClB,yBAAuB,OAEvB,SAAAA,EAAA,IAAC,MAAA,CACC,IAAI,kCACJ,UAAU,YACV,MAAO,IACP,OAAQ,IACR,IAAI,EAAA,CAAA,CACN,CAAA,EAEJ,QAEC,MAAI,CAAA,UAAU,+BACb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAAAE,MAAC,IAAE,CAAA,UAAU,+BACV,SAAAN,EAAE,mBAAmB,EACxB,EACAM,EAAAA,IAAC,MAAI,CAAA,UAAU,eACb,SAAAA,EAAA,IAACM,EAAA,CACC,GAAG,WACH,UAAU,wDAEV,SAACN,EAAA,IAAA,OAAA,CAAM,SAAEN,EAAA,qBAAqB,CAAE,CAAA,CAAA,CAAA,CAEpC,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAM,EAAA,IAACU,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ,8GCnOA,SAAwBW,GAAyB,CAC/C,KAAM,CAAE,EAAA3B,EAAG,gBAAAsB,CAAgB,EAAIrB,EAAe,EAExCC,EAAUC,EAAe,SAAS,EAElCqB,EACJF,IAAoB,KAChBpB,EAAQ,MACR,uDAEAuB,EACJH,IAAoB,KAChBpB,EAAQ,YACR,qHAEN,OAEIE,EAAA,KAAAC,WAAA,CAAA,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,MAAAiB,EACA,YAAAC,EACA,KAAK,UACL,KAAK,UACL,MAAM,mCACN,OAAQvB,EAAQ,OAChB,SAAUA,EAAQ,QAAA,CACpB,QACC,MAAI,CAAA,UAAU,gBACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAI,CAAA,UAAU,iBAAiB,GAAG,MACjC,SAAA,CAAAE,EAAAA,IAAC,OAAI,UAAU,oDACb,eAACE,EAAO,CAAA,MAAOC,EAAW,CAC5B,CAAA,EACAL,EAAAA,KAAC,OAAK,CAAA,GAAG,OACP,SAAA,CAAAE,EAAA,IAAC,UAAA,CACC,UAAU,8CACV,MAAO,CACL,gBAAiB,wCACnB,EACA,GAAG,OAEH,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,yDACb,SAAA,CAAAE,EAAA,IAAC,KAAA,CACC,UAAU,qCACV,oBAAkB,OAEjB,WAAE,oBAAoB,CAAA,CACzB,QACC,MAAI,CAAA,UAAU,iBAAiB,iBAAe,OAC7C,eAAC,MAAI,CAAA,UAAU,4CACb,SAAAA,EAAA,IAAC,KAAE,UAAU,sCACV,WAAE,uBAAuB,CAC5B,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,EACAA,EAAA,IAAC,UAAA,CACC,UAAW,qEAGX,GAAG,UAEH,eAACsB,EAAQ,CAAA,CAAA,CAAA,CACX,QACC,MAAI,CAAA,UAAU,+BACb,SAAAtB,MAACK,GAAY,CAAA,EACf,QACC,MAAI,CAAA,UAAU,2BACb,SAAAL,MAACuB,GAAI,CAAA,EACP,EAGCvB,MAAA,UAAA,CAAQ,UAAU,mDACjB,eAAC,MAAI,CAAA,UAAU,YACb,SAAAA,EAAA,IAAC,OAAI,UAAU,MACb,SAACF,OAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAE,MAAC,KAAG,CAAA,UAAU,4BACX,SAAAN,EAAE,aAAa,EAClB,EACAI,EAAAA,KAAC,MAAI,CAAA,UAAU,MACb,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,gCACb,SAAAF,OAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,QACb,SAAAA,EAAA,IAAC,IAAA,CACC,UAAU,yBACV,MAAO,CAAE,SAAU,QAAS,CAAA,CAAA,EAEhC,QACC,KAAG,CAAA,UAAU,8BACX,SAAAN,EAAE,qBAAqB,EAC1B,QACC,IAAE,CAAA,UAAU,kBACV,SAAAA,EAAE,2BAA2B,EAChC,EACAM,EAAA,IAAC,IAAA,CACC,KAAM,IAAIgB,CAAe,kBACzB,UAAU,+CAET,WAAE,sBAAsB,CAAA,CAAA,CAC3B,CACF,CAAA,CACF,CAAA,EACF,EACAhB,EAAA,IAAC,MAAI,CAAA,UAAU,iBACb,SAAAA,EAAA,IAAC,MAAI,CAAA,UAAU,gCACb,SAAAF,OAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,QACb,SAAAA,EAAA,IAAC,IAAA,CACC,UAAU,2BACV,MAAO,CAAE,SAAU,QAAS,CAAA,CAAA,EAEhC,QACC,KAAG,CAAA,UAAU,8BACX,SAAAN,EAAE,mBAAmB,EACxB,QACC,IAAE,CAAA,UAAU,kBACV,SAAAA,EAAE,yBAAyB,EAC9B,EACAM,EAAA,IAAC,IAAA,CACC,KAAM,IAAIgB,CAAe,oBACzB,UAAU,+CAET,WAAE,oBAAoB,CAAA,CAAA,CACzB,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAAA,CACF,EACF,CACF,CAAA,CAAA,EACF,QACC,SAAO,CAAA,UAAU,6DAChB,SAAAhB,EAAA,IAACU,IAAO,CACV,CAAA,CAAA,EACF,EAAO,GAAA,CAAA,CACT,CACF,CAAA,CAAA,EACF,CAEJ"}