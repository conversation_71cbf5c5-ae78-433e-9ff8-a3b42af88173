import { useTranslation } from "react-i18next";

export const useContactItems = () => {
  const { t } = useTranslation();

  return [
    {
      iconClass: "mi-location",
      title: t("contact.address.title"),
      text: t("contact.address.text"),
      link: {
        url: "https://maps.app.goo.gl/kjvsp1j2f15nff1B8",
        text: t("contact.address.link"),
        rel: "nofollow noopener",
        target: "_blank",
      },
    },
    {
      iconClass: "mi-email",
      title: t("contact.email.title"),
      text: t("contact.email.text"),
      link: {
        url: "mailto:<EMAIL>",
        text: t("contact.email.link"),
      },
    },
    {
      iconClass: "mi-mobile",
      title: t("contact.phone.title"),
      text: t("contact.phone.text"),
      link: {
        url: "tel:+37256282038",
        text: t("contact.phone.link"),
      },
    },
  ];
};

// For backward compatibility
export const contactItems = [
  {
    iconClass: "mi-location",
    title: "Address",
    text: "Devskills OÜ, Tornimäe tn 7, 10145 Tallinn",
    link: {
      url: "https://maps.app.goo.gl/kjvsp1j2f15nff1B8",
      text: "See Map",
      rel: "nofollow noopener",
      target: "_blank",
    },
  },
  {
    iconClass: "mi-email",
    title: "Email",
    text: "<EMAIL>",
    link: {
      url: "mailto:<EMAIL>",
      text: "Say Hello",
    },
  },
  {
    iconClass: "mi-mobile",
    title: "Phone",
    text: "+372 5628 2038",
    link: {
      url: "tel:+37256282038",
      text: "Call now",
    },
  },
];
