{"version": 3, "file": "vendor-gallery-BKyWYjF6.js", "sources": ["../../node_modules/.pnpm/photoswipe@5.4.4/node_modules/photoswipe/dist/photoswipe.esm.js"], "sourcesContent": ["/*!\n  * PhotoSwipe 5.4.4 - https://photoswipe.com\n  * (c) 2024 <PERSON><PERSON><PERSON>\n  */\n/** @typedef {import('../photoswipe.js').Point} Point */\n\n/**\r\n * @template {keyof HTMLElementTagNameMap} T\r\n * @param {string} className\r\n * @param {T} tagName\r\n * @param {Node} [appendToEl]\r\n * @returns {HTMLElementTagNameMap[T]}\r\n */\nfunction createElement(className, tagName, appendToEl) {\n  const el = document.createElement(tagName);\n\n  if (className) {\n    el.className = className;\n  }\n\n  if (appendToEl) {\n    appendToEl.appendChild(el);\n  }\n\n  return el;\n}\n/**\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\n\nfunction equalizePoints(p1, p2) {\n  p1.x = p2.x;\n  p1.y = p2.y;\n\n  if (p2.id !== undefined) {\n    p1.id = p2.id;\n  }\n\n  return p1;\n}\n/**\r\n * @param {Point} p\r\n */\n\nfunction roundPoint(p) {\n  p.x = Math.round(p.x);\n  p.y = Math.round(p.y);\n}\n/**\r\n * Returns distance between two points.\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {number}\r\n */\n\nfunction getDistanceBetween(p1, p2) {\n  const x = Math.abs(p1.x - p2.x);\n  const y = Math.abs(p1.y - p2.y);\n  return Math.sqrt(x * x + y * y);\n}\n/**\r\n * Whether X and Y positions of points are equal\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {boolean}\r\n */\n\nfunction pointsEqual(p1, p2) {\n  return p1.x === p2.x && p1.y === p2.y;\n}\n/**\r\n * The float result between the min and max values.\r\n *\r\n * @param {number} val\r\n * @param {number} min\r\n * @param {number} max\r\n * @returns {number}\r\n */\n\nfunction clamp(val, min, max) {\n  return Math.min(Math.max(val, min), max);\n}\n/**\r\n * Get transform string\r\n *\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n * @returns {string}\r\n */\n\nfunction toTransformString(x, y, scale) {\n  let propValue = `translate3d(${x}px,${y || 0}px,0)`;\n\n  if (scale !== undefined) {\n    propValue += ` scale3d(${scale},${scale},1)`;\n  }\n\n  return propValue;\n}\n/**\r\n * Apply transform:translate(x, y) scale(scale) to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n */\n\nfunction setTransform(el, x, y, scale) {\n  el.style.transform = toTransformString(x, y, scale);\n}\nconst defaultCSSEasing = 'cubic-bezier(.4,0,.22,1)';\n/**\r\n * Apply CSS transition to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string} [prop] CSS property to animate\r\n * @param {number} [duration] in ms\r\n * @param {string} [ease] CSS easing function\r\n */\n\nfunction setTransitionStyle(el, prop, duration, ease) {\n  // inOut: 'cubic-bezier(.4, 0, .22, 1)', // for \"toggle state\" transitions\n  // out: 'cubic-bezier(0, 0, .22, 1)', // for \"show\" transitions\n  // in: 'cubic-bezier(.4, 0, 1, 1)'// for \"hide\" transitions\n  el.style.transition = prop ? `${prop} ${duration}ms ${ease || defaultCSSEasing}` : 'none';\n}\n/**\r\n * Apply width and height CSS properties to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string | number} w\r\n * @param {string | number} h\r\n */\n\nfunction setWidthHeight(el, w, h) {\n  el.style.width = typeof w === 'number' ? `${w}px` : w;\n  el.style.height = typeof h === 'number' ? `${h}px` : h;\n}\n/**\r\n * @param {HTMLElement} el\r\n */\n\nfunction removeTransitionStyle(el) {\n  setTransitionStyle(el);\n}\n/**\r\n * @param {HTMLImageElement} img\r\n * @returns {Promise<HTMLImageElement | void>}\r\n */\n\nfunction decodeImage(img) {\n  if ('decode' in img) {\n    return img.decode().catch(() => {});\n  }\n\n  if (img.complete) {\n    return Promise.resolve(img);\n  }\n\n  return new Promise((resolve, reject) => {\n    img.onload = () => resolve(img);\n\n    img.onerror = reject;\n  });\n}\n/** @typedef {LOAD_STATE[keyof LOAD_STATE]} LoadState */\n\n/** @type {{ IDLE: 'idle'; LOADING: 'loading'; LOADED: 'loaded'; ERROR: 'error' }} */\n\nconst LOAD_STATE = {\n  IDLE: 'idle',\n  LOADING: 'loading',\n  LOADED: 'loaded',\n  ERROR: 'error'\n};\n/**\r\n * Check if click or keydown event was dispatched\r\n * with a special key or via mouse wheel.\r\n *\r\n * @param {MouseEvent | KeyboardEvent} e\r\n * @returns {boolean}\r\n */\n\nfunction specialKeyUsed(e) {\n  return 'button' in e && e.button === 1 || e.ctrlKey || e.metaKey || e.altKey || e.shiftKey;\n}\n/**\r\n * Parse `gallery` or `children` options.\r\n *\r\n * @param {import('../photoswipe.js').ElementProvider} [option]\r\n * @param {string} [legacySelector]\r\n * @param {HTMLElement | Document} [parent]\r\n * @returns HTMLElement[]\r\n */\n\nfunction getElementsFromOption(option, legacySelector, parent = document) {\n  /** @type {HTMLElement[]} */\n  let elements = [];\n\n  if (option instanceof Element) {\n    elements = [option];\n  } else if (option instanceof NodeList || Array.isArray(option)) {\n    elements = Array.from(option);\n  } else {\n    const selector = typeof option === 'string' ? option : legacySelector;\n\n    if (selector) {\n      elements = Array.from(parent.querySelectorAll(selector));\n    }\n  }\n\n  return elements;\n}\n/**\r\n * Check if browser is Safari\r\n *\r\n * @returns {boolean}\r\n */\n\nfunction isSafari() {\n  return !!(navigator.vendor && navigator.vendor.match(/apple/i));\n}\n\n// Detect passive event listener support\nlet supportsPassive = false;\n/* eslint-disable */\n\ntry {\n  /* @ts-ignore */\n  window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n    get: () => {\n      supportsPassive = true;\n    }\n  }));\n} catch (e) {}\n/* eslint-enable */\n\n/**\r\n * @typedef {Object} PoolItem\r\n * @prop {HTMLElement | Window | Document | undefined | null} target\r\n * @prop {string} type\r\n * @prop {EventListenerOrEventListenerObject} listener\r\n * @prop {boolean} [passive]\r\n */\n\n\nclass DOMEvents {\n  constructor() {\n    /**\r\n     * @type {PoolItem[]}\r\n     * @private\r\n     */\n    this._pool = [];\n  }\n  /**\r\n   * Adds event listeners\r\n   *\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type Can be multiple, separated by space.\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   */\n\n\n  add(target, type, listener, passive) {\n    this._toggleListener(target, type, listener, passive);\n  }\n  /**\r\n   * Removes event listeners\r\n   *\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   */\n\n\n  remove(target, type, listener, passive) {\n    this._toggleListener(target, type, listener, passive, true);\n  }\n  /**\r\n   * Removes all bound events\r\n   */\n\n\n  removeAll() {\n    this._pool.forEach(poolItem => {\n      this._toggleListener(poolItem.target, poolItem.type, poolItem.listener, poolItem.passive, true, true);\n    });\n\n    this._pool = [];\n  }\n  /**\r\n   * Adds or removes event\r\n   *\r\n   * @private\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   * @param {boolean} [unbind] Whether the event should be added or removed\r\n   * @param {boolean} [skipPool] Whether events pool should be skipped\r\n   */\n\n\n  _toggleListener(target, type, listener, passive, unbind, skipPool) {\n    if (!target) {\n      return;\n    }\n\n    const methodName = unbind ? 'removeEventListener' : 'addEventListener';\n    const types = type.split(' ');\n    types.forEach(eType => {\n      if (eType) {\n        // Events pool is used to easily unbind all events when PhotoSwipe is closed,\n        // so developer doesn't need to do this manually\n        if (!skipPool) {\n          if (unbind) {\n            // Remove from the events pool\n            this._pool = this._pool.filter(poolItem => {\n              return poolItem.type !== eType || poolItem.listener !== listener || poolItem.target !== target;\n            });\n          } else {\n            // Add to the events pool\n            this._pool.push({\n              target,\n              type: eType,\n              listener,\n              passive\n            });\n          }\n        } // most PhotoSwipe events call preventDefault,\n        // and we do not need browser to scroll the page\n\n\n        const eventOptions = supportsPassive ? {\n          passive: passive || false\n        } : false;\n        target[methodName](eType, listener, eventOptions);\n      }\n    });\n  }\n\n}\n\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\n\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\n\n/** @typedef {import('../photoswipe.js').Point} Point */\n\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\n\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {PhotoSwipeBase} pswp\r\n * @returns {Point}\r\n */\nfunction getViewportSize(options, pswp) {\n  if (options.getViewportSizeFn) {\n    const newViewportSize = options.getViewportSizeFn(options, pswp);\n\n    if (newViewportSize) {\n      return newViewportSize;\n    }\n  }\n\n  return {\n    x: document.documentElement.clientWidth,\n    // TODO: height on mobile is very incosistent due to toolbar\n    // find a way to improve this\n    //\n    // document.documentElement.clientHeight - doesn't seem to work well\n    y: window.innerHeight\n  };\n}\n/**\r\n * Parses padding option.\r\n * Supported formats:\r\n *\r\n * // Object\r\n * padding: {\r\n *  top: 0,\r\n *  bottom: 0,\r\n *  left: 0,\r\n *  right: 0\r\n * }\r\n *\r\n * // A function that returns the object\r\n * paddingFn: (viewportSize, itemData, index) => {\r\n *  return {\r\n *    top: 0,\r\n *    bottom: 0,\r\n *    left: 0,\r\n *    right: 0\r\n *  };\r\n * }\r\n *\r\n * // Legacy variant\r\n * paddingLeft: 0,\r\n * paddingRight: 0,\r\n * paddingTop: 0,\r\n * paddingBottom: 0,\r\n *\r\n * @param {'left' | 'top' | 'bottom' | 'right'} prop\r\n * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n * @param {Point} viewportSize PhotoSwipe viewport size, for example: { x:800, y:600 }\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {number} index Slide index\r\n * @returns {number}\r\n */\n\nfunction parsePaddingOption(prop, options, viewportSize, itemData, index) {\n  let paddingValue = 0;\n\n  if (options.paddingFn) {\n    paddingValue = options.paddingFn(viewportSize, itemData, index)[prop];\n  } else if (options.padding) {\n    paddingValue = options.padding[prop];\n  } else {\n    const legacyPropName = 'padding' + prop[0].toUpperCase() + prop.slice(1); // @ts-expect-error\n\n    if (options[legacyPropName]) {\n      // @ts-expect-error\n      paddingValue = options[legacyPropName];\n    }\n  }\n\n  return Number(paddingValue) || 0;\n}\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {Point} viewportSize\r\n * @param {SlideData} itemData\r\n * @param {number} index\r\n * @returns {Point}\r\n */\n\nfunction getPanAreaSize(options, viewportSize, itemData, index) {\n  return {\n    x: viewportSize.x - parsePaddingOption('left', options, viewportSize, itemData, index) - parsePaddingOption('right', options, viewportSize, itemData, index),\n    y: viewportSize.y - parsePaddingOption('top', options, viewportSize, itemData, index) - parsePaddingOption('bottom', options, viewportSize, itemData, index)\n  };\n}\n\n/** @typedef {import('./slide.js').default} Slide */\n\n/** @typedef {Record<Axis, number>} Point */\n\n/** @typedef {'x' | 'y'} Axis */\n\n/**\r\n * Calculates minimum, maximum and initial (center) bounds of a slide\r\n */\n\nclass PanBounds {\n  /**\r\n   * @param {Slide} slide\r\n   */\n  constructor(slide) {\n    this.slide = slide;\n    this.currZoomLevel = 1;\n    this.center =\n    /** @type {Point} */\n    {\n      x: 0,\n      y: 0\n    };\n    this.max =\n    /** @type {Point} */\n    {\n      x: 0,\n      y: 0\n    };\n    this.min =\n    /** @type {Point} */\n    {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\r\n   * _getItemBounds\r\n   *\r\n   * @param {number} currZoomLevel\r\n   */\n\n\n  update(currZoomLevel) {\n    this.currZoomLevel = currZoomLevel;\n\n    if (!this.slide.width) {\n      this.reset();\n    } else {\n      this._updateAxis('x');\n\n      this._updateAxis('y');\n\n      this.slide.pswp.dispatch('calcBounds', {\n        slide: this.slide\n      });\n    }\n  }\n  /**\r\n   * _calculateItemBoundsForAxis\r\n   *\r\n   * @param {Axis} axis\r\n   */\n\n\n  _updateAxis(axis) {\n    const {\n      pswp\n    } = this.slide;\n    const elSize = this.slide[axis === 'x' ? 'width' : 'height'] * this.currZoomLevel;\n    const paddingProp = axis === 'x' ? 'left' : 'top';\n    const padding = parsePaddingOption(paddingProp, pswp.options, pswp.viewportSize, this.slide.data, this.slide.index);\n    const panAreaSize = this.slide.panAreaSize[axis]; // Default position of element.\n    // By default, it is center of viewport:\n\n    this.center[axis] = Math.round((panAreaSize - elSize) / 2) + padding; // maximum pan position\n\n    this.max[axis] = elSize > panAreaSize ? Math.round(panAreaSize - elSize) + padding : this.center[axis]; // minimum pan position\n\n    this.min[axis] = elSize > panAreaSize ? padding : this.center[axis];\n  } // _getZeroBounds\n\n\n  reset() {\n    this.center.x = 0;\n    this.center.y = 0;\n    this.max.x = 0;\n    this.max.y = 0;\n    this.min.x = 0;\n    this.min.y = 0;\n  }\n  /**\r\n   * Correct pan position if it's beyond the bounds\r\n   *\r\n   * @param {Axis} axis x or y\r\n   * @param {number} panOffset\r\n   * @returns {number}\r\n   */\n\n\n  correctPan(axis, panOffset) {\n    // checkPanBounds\n    return clamp(panOffset, this.max[axis], this.min[axis]);\n  }\n\n}\n\nconst MAX_IMAGE_WIDTH = 4000;\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\n\n/** @typedef {import('../photoswipe.js').Point} Point */\n\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\n\n/** @typedef {'fit' | 'fill' | number | ((zoomLevelObject: ZoomLevel) => number)} ZoomLevelOption */\n\n/**\r\n * Calculates zoom levels for specific slide.\r\n * Depends on viewport size and image size.\r\n */\n\nclass ZoomLevel {\n  /**\r\n   * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {number} index Slide index\r\n   * @param {PhotoSwipe} [pswp] PhotoSwipe instance, can be undefined if not initialized yet\r\n   */\n  constructor(options, itemData, index, pswp) {\n    this.pswp = pswp;\n    this.options = options;\n    this.itemData = itemData;\n    this.index = index;\n    /** @type { Point | null } */\n\n    this.panAreaSize = null;\n    /** @type { Point | null } */\n\n    this.elementSize = null;\n    this.fit = 1;\n    this.fill = 1;\n    this.vFill = 1;\n    this.initial = 1;\n    this.secondary = 1;\n    this.max = 1;\n    this.min = 1;\n  }\n  /**\r\n   * Calculate initial, secondary and maximum zoom level for the specified slide.\r\n   *\r\n   * It should be called when either image or viewport size changes.\r\n   *\r\n   * @param {number} maxWidth\r\n   * @param {number} maxHeight\r\n   * @param {Point} panAreaSize\r\n   */\n\n\n  update(maxWidth, maxHeight, panAreaSize) {\n    /** @type {Point} */\n    const elementSize = {\n      x: maxWidth,\n      y: maxHeight\n    };\n    this.elementSize = elementSize;\n    this.panAreaSize = panAreaSize;\n    const hRatio = panAreaSize.x / elementSize.x;\n    const vRatio = panAreaSize.y / elementSize.y;\n    this.fit = Math.min(1, hRatio < vRatio ? hRatio : vRatio);\n    this.fill = Math.min(1, hRatio > vRatio ? hRatio : vRatio); // zoom.vFill defines zoom level of the image\n    // when it has 100% of viewport vertical space (height)\n\n    this.vFill = Math.min(1, vRatio);\n    this.initial = this._getInitial();\n    this.secondary = this._getSecondary();\n    this.max = Math.max(this.initial, this.secondary, this._getMax());\n    this.min = Math.min(this.fit, this.initial, this.secondary);\n\n    if (this.pswp) {\n      this.pswp.dispatch('zoomLevelsUpdate', {\n        zoomLevels: this,\n        slideData: this.itemData\n      });\n    }\n  }\n  /**\r\n   * Parses user-defined zoom option.\r\n   *\r\n   * @private\r\n   * @param {'initial' | 'secondary' | 'max'} optionPrefix Zoom level option prefix (initial, secondary, max)\r\n   * @returns { number | undefined }\r\n   */\n\n\n  _parseZoomLevelOption(optionPrefix) {\n    const optionName =\n    /** @type {'initialZoomLevel' | 'secondaryZoomLevel' | 'maxZoomLevel'} */\n    optionPrefix + 'ZoomLevel';\n    const optionValue = this.options[optionName];\n\n    if (!optionValue) {\n      return;\n    }\n\n    if (typeof optionValue === 'function') {\n      return optionValue(this);\n    }\n\n    if (optionValue === 'fill') {\n      return this.fill;\n    }\n\n    if (optionValue === 'fit') {\n      return this.fit;\n    }\n\n    return Number(optionValue);\n  }\n  /**\r\n   * Get zoom level to which image will be zoomed after double-tap gesture,\r\n   * or when user clicks on zoom icon,\r\n   * or mouse-click on image itself.\r\n   * If you return 1 image will be zoomed to its original size.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\n\n\n  _getSecondary() {\n    let currZoomLevel = this._parseZoomLevelOption('secondary');\n\n    if (currZoomLevel) {\n      return currZoomLevel;\n    } // 3x of \"fit\" state, but not larger than original\n\n\n    currZoomLevel = Math.min(1, this.fit * 3);\n\n    if (this.elementSize && currZoomLevel * this.elementSize.x > MAX_IMAGE_WIDTH) {\n      currZoomLevel = MAX_IMAGE_WIDTH / this.elementSize.x;\n    }\n\n    return currZoomLevel;\n  }\n  /**\r\n   * Get initial image zoom level.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\n\n\n  _getInitial() {\n    return this._parseZoomLevelOption('initial') || this.fit;\n  }\n  /**\r\n   * Maximum zoom level when user zooms\r\n   * via zoom/pinch gesture,\r\n   * via cmd/ctrl-wheel or via trackpad.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\n\n\n  _getMax() {\n    // max zoom level is x4 from \"fit state\",\n    // used for zoom gesture and ctrl/trackpad zoom\n    return this._parseZoomLevelOption('max') || Math.max(1, this.fit * 4);\n  }\n\n}\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n/**\r\n * Renders and allows to control a single slide\r\n */\n\nclass Slide {\n  /**\r\n   * @param {SlideData} data\r\n   * @param {number} index\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(data, index, pswp) {\n    this.data = data;\n    this.index = index;\n    this.pswp = pswp;\n    this.isActive = index === pswp.currIndex;\n    this.currentResolution = 0;\n    /** @type {Point} */\n\n    this.panAreaSize = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point} */\n\n    this.pan = {\n      x: 0,\n      y: 0\n    };\n    this.isFirstSlide = this.isActive && !pswp.opener.isOpen;\n    this.zoomLevels = new ZoomLevel(pswp.options, data, index, pswp);\n    this.pswp.dispatch('gettingData', {\n      slide: this,\n      data: this.data,\n      index\n    });\n    this.content = this.pswp.contentLoader.getContentBySlide(this);\n    this.container = createElement('pswp__zoom-wrap', 'div');\n    /** @type {HTMLElement | null} */\n\n    this.holderElement = null;\n    this.currZoomLevel = 1;\n    /** @type {number} */\n\n    this.width = this.content.width;\n    /** @type {number} */\n\n    this.height = this.content.height;\n    this.heavyAppended = false;\n    this.bounds = new PanBounds(this);\n    this.prevDisplayedWidth = -1;\n    this.prevDisplayedHeight = -1;\n    this.pswp.dispatch('slideInit', {\n      slide: this\n    });\n  }\n  /**\r\n   * If this slide is active/current/visible\r\n   *\r\n   * @param {boolean} isActive\r\n   */\n\n\n  setIsActive(isActive) {\n    if (isActive && !this.isActive) {\n      // slide just became active\n      this.activate();\n    } else if (!isActive && this.isActive) {\n      // slide just became non-active\n      this.deactivate();\n    }\n  }\n  /**\r\n   * Appends slide content to DOM\r\n   *\r\n   * @param {HTMLElement} holderElement\r\n   */\n\n\n  append(holderElement) {\n    this.holderElement = holderElement;\n    this.container.style.transformOrigin = '0 0'; // Slide appended to DOM\n\n    if (!this.data) {\n      return;\n    }\n\n    this.calculateSize();\n    this.load();\n    this.updateContentSize();\n    this.appendHeavy();\n    this.holderElement.appendChild(this.container);\n    this.zoomAndPanToInitial();\n    this.pswp.dispatch('firstZoomPan', {\n      slide: this\n    });\n    this.applyCurrentZoomPan();\n    this.pswp.dispatch('afterSetContent', {\n      slide: this\n    });\n\n    if (this.isActive) {\n      this.activate();\n    }\n  }\n\n  load() {\n    this.content.load(false);\n    this.pswp.dispatch('slideLoad', {\n      slide: this\n    });\n  }\n  /**\r\n   * Append \"heavy\" DOM elements\r\n   *\r\n   * This may depend on a type of slide,\r\n   * but generally these are large images.\r\n   */\n\n\n  appendHeavy() {\n    const {\n      pswp\n    } = this;\n    const appendHeavyNearby = true; // todo\n    // Avoid appending heavy elements during animations\n\n    if (this.heavyAppended || !pswp.opener.isOpen || pswp.mainScroll.isShifted() || !this.isActive && !appendHeavyNearby) {\n      return;\n    }\n\n    if (this.pswp.dispatch('appendHeavy', {\n      slide: this\n    }).defaultPrevented) {\n      return;\n    }\n\n    this.heavyAppended = true;\n    this.content.append();\n    this.pswp.dispatch('appendHeavyContent', {\n      slide: this\n    });\n  }\n  /**\r\n   * Triggered when this slide is active (selected).\r\n   *\r\n   * If it's part of opening/closing transition -\r\n   * activate() will trigger after the transition is ended.\r\n   */\n\n\n  activate() {\n    this.isActive = true;\n    this.appendHeavy();\n    this.content.activate();\n    this.pswp.dispatch('slideActivate', {\n      slide: this\n    });\n  }\n  /**\r\n   * Triggered when this slide becomes inactive.\r\n   *\r\n   * Slide can become inactive only after it was active.\r\n   */\n\n\n  deactivate() {\n    this.isActive = false;\n    this.content.deactivate();\n\n    if (this.currZoomLevel !== this.zoomLevels.initial) {\n      // allow filtering\n      this.calculateSize();\n    } // reset zoom level\n\n\n    this.currentResolution = 0;\n    this.zoomAndPanToInitial();\n    this.applyCurrentZoomPan();\n    this.updateContentSize();\n    this.pswp.dispatch('slideDeactivate', {\n      slide: this\n    });\n  }\n  /**\r\n   * The slide should destroy itself, it will never be used again.\r\n   * (unbind all events and destroy internal components)\r\n   */\n\n\n  destroy() {\n    this.content.hasSlide = false;\n    this.content.remove();\n    this.container.remove();\n    this.pswp.dispatch('slideDestroy', {\n      slide: this\n    });\n  }\n\n  resize() {\n    if (this.currZoomLevel === this.zoomLevels.initial || !this.isActive) {\n      // Keep initial zoom level if it was before the resize,\n      // as well as when this slide is not active\n      // Reset position and scale to original state\n      this.calculateSize();\n      this.currentResolution = 0;\n      this.zoomAndPanToInitial();\n      this.applyCurrentZoomPan();\n      this.updateContentSize();\n    } else {\n      // readjust pan position if it's beyond the bounds\n      this.calculateSize();\n      this.bounds.update(this.currZoomLevel);\n      this.panTo(this.pan.x, this.pan.y);\n    }\n  }\n  /**\r\n   * Apply size to current slide content,\r\n   * based on the current resolution and scale.\r\n   *\r\n   * @param {boolean} [force] if size should be updated even if dimensions weren't changed\r\n   */\n\n\n  updateContentSize(force) {\n    // Use initial zoom level\n    // if resolution is not defined (user didn't zoom yet)\n    const scaleMultiplier = this.currentResolution || this.zoomLevels.initial;\n\n    if (!scaleMultiplier) {\n      return;\n    }\n\n    const width = Math.round(this.width * scaleMultiplier) || this.pswp.viewportSize.x;\n    const height = Math.round(this.height * scaleMultiplier) || this.pswp.viewportSize.y;\n\n    if (!this.sizeChanged(width, height) && !force) {\n      return;\n    }\n\n    this.content.setDisplayedSize(width, height);\n  }\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\n\n\n  sizeChanged(width, height) {\n    if (width !== this.prevDisplayedWidth || height !== this.prevDisplayedHeight) {\n      this.prevDisplayedWidth = width;\n      this.prevDisplayedHeight = height;\n      return true;\n    }\n\n    return false;\n  }\n  /** @returns {HTMLImageElement | HTMLDivElement | null | undefined} */\n\n\n  getPlaceholderElement() {\n    var _this$content$placeho;\n\n    return (_this$content$placeho = this.content.placeholder) === null || _this$content$placeho === void 0 ? void 0 : _this$content$placeho.element;\n  }\n  /**\r\n   * Zoom current slide image to...\r\n   *\r\n   * @param {number} destZoomLevel Destination zoom level.\r\n   * @param {Point} [centerPoint]\r\n   * Transform origin center point, or false if viewport center should be used.\r\n   * @param {number | false} [transitionDuration] Transition duration, may be set to 0.\r\n   * @param {boolean} [ignoreBounds] Minimum and maximum zoom levels will be ignored.\r\n   */\n\n\n  zoomTo(destZoomLevel, centerPoint, transitionDuration, ignoreBounds) {\n    const {\n      pswp\n    } = this;\n\n    if (!this.isZoomable() || pswp.mainScroll.isShifted()) {\n      return;\n    }\n\n    pswp.dispatch('beforeZoomTo', {\n      destZoomLevel,\n      centerPoint,\n      transitionDuration\n    }); // stop all pan and zoom transitions\n\n    pswp.animations.stopAllPan(); // if (!centerPoint) {\n    //   centerPoint = pswp.getViewportCenterPoint();\n    // }\n\n    const prevZoomLevel = this.currZoomLevel;\n\n    if (!ignoreBounds) {\n      destZoomLevel = clamp(destZoomLevel, this.zoomLevels.min, this.zoomLevels.max);\n    } // if (transitionDuration === undefined) {\n    //   transitionDuration = this.pswp.options.zoomAnimationDuration;\n    // }\n\n\n    this.setZoomLevel(destZoomLevel);\n    this.pan.x = this.calculateZoomToPanOffset('x', centerPoint, prevZoomLevel);\n    this.pan.y = this.calculateZoomToPanOffset('y', centerPoint, prevZoomLevel);\n    roundPoint(this.pan);\n\n    const finishTransition = () => {\n      this._setResolution(destZoomLevel);\n\n      this.applyCurrentZoomPan();\n    };\n\n    if (!transitionDuration) {\n      finishTransition();\n    } else {\n      pswp.animations.startTransition({\n        isPan: true,\n        name: 'zoomTo',\n        target: this.container,\n        transform: this.getCurrentTransform(),\n        onComplete: finishTransition,\n        duration: transitionDuration,\n        easing: pswp.options.easing\n      });\n    }\n  }\n  /**\r\n   * @param {Point} [centerPoint]\r\n   */\n\n\n  toggleZoom(centerPoint) {\n    this.zoomTo(this.currZoomLevel === this.zoomLevels.initial ? this.zoomLevels.secondary : this.zoomLevels.initial, centerPoint, this.pswp.options.zoomAnimationDuration);\n  }\n  /**\r\n   * Updates zoom level property and recalculates new pan bounds,\r\n   * unlike zoomTo it does not apply transform (use applyCurrentZoomPan)\r\n   *\r\n   * @param {number} currZoomLevel\r\n   */\n\n\n  setZoomLevel(currZoomLevel) {\n    this.currZoomLevel = currZoomLevel;\n    this.bounds.update(this.currZoomLevel);\n  }\n  /**\r\n   * Get pan position after zoom at a given `point`.\r\n   *\r\n   * Always call setZoomLevel(newZoomLevel) beforehand to recalculate\r\n   * pan bounds according to the new zoom level.\r\n   *\r\n   * @param {'x' | 'y'} axis\r\n   * @param {Point} [point]\r\n   * point based on which zoom is performed, usually refers to the current mouse position,\r\n   * if false - viewport center will be used.\r\n   * @param {number} [prevZoomLevel] Zoom level before new zoom was applied.\r\n   * @returns {number}\r\n   */\n\n\n  calculateZoomToPanOffset(axis, point, prevZoomLevel) {\n    const totalPanDistance = this.bounds.max[axis] - this.bounds.min[axis];\n\n    if (totalPanDistance === 0) {\n      return this.bounds.center[axis];\n    }\n\n    if (!point) {\n      point = this.pswp.getViewportCenterPoint();\n    }\n\n    if (!prevZoomLevel) {\n      prevZoomLevel = this.zoomLevels.initial;\n    }\n\n    const zoomFactor = this.currZoomLevel / prevZoomLevel;\n    return this.bounds.correctPan(axis, (this.pan[axis] - point[axis]) * zoomFactor + point[axis]);\n  }\n  /**\r\n   * Apply pan and keep it within bounds.\r\n   *\r\n   * @param {number} panX\r\n   * @param {number} panY\r\n   */\n\n\n  panTo(panX, panY) {\n    this.pan.x = this.bounds.correctPan('x', panX);\n    this.pan.y = this.bounds.correctPan('y', panY);\n    this.applyCurrentZoomPan();\n  }\n  /**\r\n   * If the slide in the current state can be panned by the user\r\n   * @returns {boolean}\r\n   */\n\n\n  isPannable() {\n    return Boolean(this.width) && this.currZoomLevel > this.zoomLevels.fit;\n  }\n  /**\r\n   * If the slide can be zoomed\r\n   * @returns {boolean}\r\n   */\n\n\n  isZoomable() {\n    return Boolean(this.width) && this.content.isZoomable();\n  }\n  /**\r\n   * Apply transform and scale based on\r\n   * the current pan position (this.pan) and zoom level (this.currZoomLevel)\r\n   */\n\n\n  applyCurrentZoomPan() {\n    this._applyZoomTransform(this.pan.x, this.pan.y, this.currZoomLevel);\n\n    if (this === this.pswp.currSlide) {\n      this.pswp.dispatch('zoomPanUpdate', {\n        slide: this\n      });\n    }\n  }\n\n  zoomAndPanToInitial() {\n    this.currZoomLevel = this.zoomLevels.initial; // pan according to the zoom level\n\n    this.bounds.update(this.currZoomLevel);\n    equalizePoints(this.pan, this.bounds.center);\n    this.pswp.dispatch('initialZoomPan', {\n      slide: this\n    });\n  }\n  /**\r\n   * Set translate and scale based on current resolution\r\n   *\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} zoom\r\n   * @private\r\n   */\n\n\n  _applyZoomTransform(x, y, zoom) {\n    zoom /= this.currentResolution || this.zoomLevels.initial;\n    setTransform(this.container, x, y, zoom);\n  }\n\n  calculateSize() {\n    const {\n      pswp\n    } = this;\n    equalizePoints(this.panAreaSize, getPanAreaSize(pswp.options, pswp.viewportSize, this.data, this.index));\n    this.zoomLevels.update(this.width, this.height, this.panAreaSize);\n    pswp.dispatch('calcSlideSize', {\n      slide: this\n    });\n  }\n  /** @returns {string} */\n\n\n  getCurrentTransform() {\n    const scale = this.currZoomLevel / (this.currentResolution || this.zoomLevels.initial);\n    return toTransformString(this.pan.x, this.pan.y, scale);\n  }\n  /**\r\n   * Set resolution and re-render the image.\r\n   *\r\n   * For example, if the real image size is 2000x1500,\r\n   * and resolution is 0.5 - it will be rendered as 1000x750.\r\n   *\r\n   * Image with zoom level 2 and resolution 0.5 is\r\n   * the same as image with zoom level 1 and resolution 1.\r\n   *\r\n   * Used to optimize animations and make\r\n   * sure that browser renders image in the highest quality.\r\n   * Also used by responsive images to load the correct one.\r\n   *\r\n   * @param {number} newResolution\r\n   */\n\n\n  _setResolution(newResolution) {\n    if (newResolution === this.currentResolution) {\n      return;\n    }\n\n    this.currentResolution = newResolution;\n    this.updateContentSize();\n    this.pswp.dispatch('resolutionChanged');\n  }\n\n}\n\n/** @typedef {import('../photoswipe.js').Point} Point */\n\n/** @typedef {import('./gestures.js').default} Gestures */\n\nconst PAN_END_FRICTION = 0.35;\nconst VERTICAL_DRAG_FRICTION = 0.6; // 1 corresponds to the third of viewport height\n\nconst MIN_RATIO_TO_CLOSE = 0.4; // Minimum speed required to navigate\n// to next or previous slide\n\nconst MIN_NEXT_SLIDE_SPEED = 0.5;\n/**\r\n * @param {number} initialVelocity\r\n * @param {number} decelerationRate\r\n * @returns {number}\r\n */\n\nfunction project(initialVelocity, decelerationRate) {\n  return initialVelocity * decelerationRate / (1 - decelerationRate);\n}\n/**\r\n * Handles single pointer dragging\r\n */\n\n\nclass DragHandler {\n  /**\r\n   * @param {Gestures} gestures\r\n   */\n  constructor(gestures) {\n    this.gestures = gestures;\n    this.pswp = gestures.pswp;\n    /** @type {Point} */\n\n    this.startPan = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  start() {\n    if (this.pswp.currSlide) {\n      equalizePoints(this.startPan, this.pswp.currSlide.pan);\n    }\n\n    this.pswp.animations.stopAll();\n  }\n\n  change() {\n    const {\n      p1,\n      prevP1,\n      dragAxis\n    } = this.gestures;\n    const {\n      currSlide\n    } = this.pswp;\n\n    if (dragAxis === 'y' && this.pswp.options.closeOnVerticalDrag && currSlide && currSlide.currZoomLevel <= currSlide.zoomLevels.fit && !this.gestures.isMultitouch) {\n      // Handle vertical drag to close\n      const panY = currSlide.pan.y + (p1.y - prevP1.y);\n\n      if (!this.pswp.dispatch('verticalDrag', {\n        panY\n      }).defaultPrevented) {\n        this._setPanWithFriction('y', panY, VERTICAL_DRAG_FRICTION);\n\n        const bgOpacity = 1 - Math.abs(this._getVerticalDragRatio(currSlide.pan.y));\n        this.pswp.applyBgOpacity(bgOpacity);\n        currSlide.applyCurrentZoomPan();\n      }\n    } else {\n      const mainScrollChanged = this._panOrMoveMainScroll('x');\n\n      if (!mainScrollChanged) {\n        this._panOrMoveMainScroll('y');\n\n        if (currSlide) {\n          roundPoint(currSlide.pan);\n          currSlide.applyCurrentZoomPan();\n        }\n      }\n    }\n  }\n\n  end() {\n    const {\n      velocity\n    } = this.gestures;\n    const {\n      mainScroll,\n      currSlide\n    } = this.pswp;\n    let indexDiff = 0;\n    this.pswp.animations.stopAll(); // Handle main scroll if it's shifted\n\n    if (mainScroll.isShifted()) {\n      // Position of the main scroll relative to the viewport\n      const mainScrollShiftDiff = mainScroll.x - mainScroll.getCurrSlideX(); // Ratio between 0 and 1:\n      // 0 - slide is not visible at all,\n      // 0.5 - half of the slide is visible\n      // 1 - slide is fully visible\n\n      const currentSlideVisibilityRatio = mainScrollShiftDiff / this.pswp.viewportSize.x; // Go next slide.\n      //\n      // - if velocity and its direction is matched,\n      //   and we see at least tiny part of the next slide\n      //\n      // - or if we see less than 50% of the current slide\n      //   and velocity is close to 0\n      //\n\n      if (velocity.x < -MIN_NEXT_SLIDE_SPEED && currentSlideVisibilityRatio < 0 || velocity.x < 0.1 && currentSlideVisibilityRatio < -0.5) {\n        // Go to next slide\n        indexDiff = 1;\n        velocity.x = Math.min(velocity.x, 0);\n      } else if (velocity.x > MIN_NEXT_SLIDE_SPEED && currentSlideVisibilityRatio > 0 || velocity.x > -0.1 && currentSlideVisibilityRatio > 0.5) {\n        // Go to prev slide\n        indexDiff = -1;\n        velocity.x = Math.max(velocity.x, 0);\n      }\n\n      mainScroll.moveIndexBy(indexDiff, true, velocity.x);\n    } // Restore zoom level\n\n\n    if (currSlide && currSlide.currZoomLevel > currSlide.zoomLevels.max || this.gestures.isMultitouch) {\n      this.gestures.zoomLevels.correctZoomPan(true);\n    } else {\n      // we run two animations instead of one,\n      // as each axis has own pan boundaries and thus different spring function\n      // (correctZoomPan does not have this functionality,\n      //  it animates all properties with single timing function)\n      this._finishPanGestureForAxis('x');\n\n      this._finishPanGestureForAxis('y');\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   */\n\n\n  _finishPanGestureForAxis(axis) {\n    const {\n      velocity\n    } = this.gestures;\n    const {\n      currSlide\n    } = this.pswp;\n\n    if (!currSlide) {\n      return;\n    }\n\n    const {\n      pan,\n      bounds\n    } = currSlide;\n    const panPos = pan[axis];\n    const restoreBgOpacity = this.pswp.bgOpacity < 1 && axis === 'y'; // 0.995 means - scroll view loses 0.5% of its velocity per millisecond\n    // Increasing this number will reduce travel distance\n\n    const decelerationRate = 0.995; // 0.99\n    // Pan position if there is no bounds\n\n    const projectedPosition = panPos + project(velocity[axis], decelerationRate);\n\n    if (restoreBgOpacity) {\n      const vDragRatio = this._getVerticalDragRatio(panPos);\n\n      const projectedVDragRatio = this._getVerticalDragRatio(projectedPosition); // If we are above and moving upwards,\n      // or if we are below and moving downwards\n\n\n      if (vDragRatio < 0 && projectedVDragRatio < -MIN_RATIO_TO_CLOSE || vDragRatio > 0 && projectedVDragRatio > MIN_RATIO_TO_CLOSE) {\n        this.pswp.close();\n        return;\n      }\n    } // Pan position with corrected bounds\n\n\n    const correctedPanPosition = bounds.correctPan(axis, projectedPosition); // Exit if pan position should not be changed\n    // or if speed it too low\n\n    if (panPos === correctedPanPosition) {\n      return;\n    } // Overshoot if the final position is out of pan bounds\n\n\n    const dampingRatio = correctedPanPosition === projectedPosition ? 1 : 0.82;\n    const initialBgOpacity = this.pswp.bgOpacity;\n    const totalPanDist = correctedPanPosition - panPos;\n    this.pswp.animations.startSpring({\n      name: 'panGesture' + axis,\n      isPan: true,\n      start: panPos,\n      end: correctedPanPosition,\n      velocity: velocity[axis],\n      dampingRatio,\n      onUpdate: pos => {\n        // Animate opacity of background relative to Y pan position of an image\n        if (restoreBgOpacity && this.pswp.bgOpacity < 1) {\n          // 0 - start of animation, 1 - end of animation\n          const animationProgressRatio = 1 - (correctedPanPosition - pos) / totalPanDist; // We clamp opacity to keep it between 0 and 1.\n          // As progress ratio can be larger than 1 due to overshoot,\n          // and we do not want to bounce opacity.\n\n          this.pswp.applyBgOpacity(clamp(initialBgOpacity + (1 - initialBgOpacity) * animationProgressRatio, 0, 1));\n        }\n\n        pan[axis] = Math.floor(pos);\n        currSlide.applyCurrentZoomPan();\n      }\n    });\n  }\n  /**\r\n   * Update position of the main scroll,\r\n   * or/and update pan position of the current slide.\r\n   *\r\n   * Should return true if it changes (or can change) main scroll.\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @returns {boolean}\r\n   */\n\n\n  _panOrMoveMainScroll(axis) {\n    const {\n      p1,\n      dragAxis,\n      prevP1,\n      isMultitouch\n    } = this.gestures;\n    const {\n      currSlide,\n      mainScroll\n    } = this.pswp;\n    const delta = p1[axis] - prevP1[axis];\n    const newMainScrollX = mainScroll.x + delta;\n\n    if (!delta || !currSlide) {\n      return false;\n    } // Always move main scroll if image can not be panned\n\n\n    if (axis === 'x' && !currSlide.isPannable() && !isMultitouch) {\n      mainScroll.moveTo(newMainScrollX, true);\n      return true; // changed main scroll\n    }\n\n    const {\n      bounds\n    } = currSlide;\n    const newPan = currSlide.pan[axis] + delta;\n\n    if (this.pswp.options.allowPanToNext && dragAxis === 'x' && axis === 'x' && !isMultitouch) {\n      const currSlideMainScrollX = mainScroll.getCurrSlideX(); // Position of the main scroll relative to the viewport\n\n      const mainScrollShiftDiff = mainScroll.x - currSlideMainScrollX;\n      const isLeftToRight = delta > 0;\n      const isRightToLeft = !isLeftToRight;\n\n      if (newPan > bounds.min[axis] && isLeftToRight) {\n        // Panning from left to right, beyond the left edge\n        // Wether the image was at minimum pan position (or less)\n        // when this drag gesture started.\n        // Minimum pan position refers to the left edge of the image.\n        const wasAtMinPanPosition = bounds.min[axis] <= this.startPan[axis];\n\n        if (wasAtMinPanPosition) {\n          mainScroll.moveTo(newMainScrollX, true);\n          return true;\n        } else {\n          this._setPanWithFriction(axis, newPan); //currSlide.pan[axis] = newPan;\n\n        }\n      } else if (newPan < bounds.max[axis] && isRightToLeft) {\n        // Paning from right to left, beyond the right edge\n        // Maximum pan position refers to the right edge of the image.\n        const wasAtMaxPanPosition = this.startPan[axis] <= bounds.max[axis];\n\n        if (wasAtMaxPanPosition) {\n          mainScroll.moveTo(newMainScrollX, true);\n          return true;\n        } else {\n          this._setPanWithFriction(axis, newPan); //currSlide.pan[axis] = newPan;\n\n        }\n      } else {\n        // If main scroll is shifted\n        if (mainScrollShiftDiff !== 0) {\n          // If main scroll is shifted right\n          if (mainScrollShiftDiff > 0\n          /*&& isRightToLeft*/\n          ) {\n            mainScroll.moveTo(Math.max(newMainScrollX, currSlideMainScrollX), true);\n            return true;\n          } else if (mainScrollShiftDiff < 0\n          /*&& isLeftToRight*/\n          ) {\n            // Main scroll is shifted left (Position is less than 0 comparing to the viewport 0)\n            mainScroll.moveTo(Math.min(newMainScrollX, currSlideMainScrollX), true);\n            return true;\n          }\n        } else {\n          // We are within pan bounds, so just pan\n          this._setPanWithFriction(axis, newPan);\n        }\n      }\n    } else {\n      if (axis === 'y') {\n        // Do not pan vertically if main scroll is shifted o\n        if (!mainScroll.isShifted() && bounds.min.y !== bounds.max.y) {\n          this._setPanWithFriction(axis, newPan);\n        }\n      } else {\n        this._setPanWithFriction(axis, newPan);\n      }\n    }\n\n    return false;\n  } // If we move above - the ratio is negative\n  // If we move below the ratio is positive\n\n  /**\r\n   * Relation between pan Y position and third of viewport height.\r\n   *\r\n   * When we are at initial position (center bounds) - the ratio is 0,\r\n   * if position is shifted upwards - the ratio is negative,\r\n   * if position is shifted downwards - the ratio is positive.\r\n   *\r\n   * @private\r\n   * @param {number} panY The current pan Y position.\r\n   * @returns {number}\r\n   */\n\n\n  _getVerticalDragRatio(panY) {\n    var _this$pswp$currSlide$, _this$pswp$currSlide;\n\n    return (panY - ((_this$pswp$currSlide$ = (_this$pswp$currSlide = this.pswp.currSlide) === null || _this$pswp$currSlide === void 0 ? void 0 : _this$pswp$currSlide.bounds.center.y) !== null && _this$pswp$currSlide$ !== void 0 ? _this$pswp$currSlide$ : 0)) / (this.pswp.viewportSize.y / 3);\n  }\n  /**\r\n   * Set pan position of the current slide.\r\n   * Apply friction if the position is beyond the pan bounds,\r\n   * or if custom friction is defined.\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} potentialPan\r\n   * @param {number} [customFriction] (0.1 - 1)\r\n   */\n\n\n  _setPanWithFriction(axis, potentialPan, customFriction) {\n    const {\n      currSlide\n    } = this.pswp;\n\n    if (!currSlide) {\n      return;\n    }\n\n    const {\n      pan,\n      bounds\n    } = currSlide;\n    const correctedPan = bounds.correctPan(axis, potentialPan); // If we are out of pan bounds\n\n    if (correctedPan !== potentialPan || customFriction) {\n      const delta = Math.round(potentialPan - pan[axis]);\n      pan[axis] += delta * (customFriction || PAN_END_FRICTION);\n    } else {\n      pan[axis] = potentialPan;\n    }\n  }\n\n}\n\n/** @typedef {import('../photoswipe.js').Point} Point */\n\n/** @typedef {import('./gestures.js').default} Gestures */\n\nconst UPPER_ZOOM_FRICTION = 0.05;\nconst LOWER_ZOOM_FRICTION = 0.15;\n/**\r\n * Get center point between two points\r\n *\r\n * @param {Point} p\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\n\nfunction getZoomPointsCenter(p, p1, p2) {\n  p.x = (p1.x + p2.x) / 2;\n  p.y = (p1.y + p2.y) / 2;\n  return p;\n}\n\nclass ZoomHandler {\n  /**\r\n   * @param {Gestures} gestures\r\n   */\n  constructor(gestures) {\n    this.gestures = gestures;\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\n\n    this._startPan = {\n      x: 0,\n      y: 0\n    };\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\n\n    this._startZoomPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\n\n    this._zoomPoint = {\n      x: 0,\n      y: 0\n    };\n    /** @private */\n\n    this._wasOverFitZoomLevel = false;\n    /** @private */\n\n    this._startZoomLevel = 1;\n  }\n\n  start() {\n    const {\n      currSlide\n    } = this.gestures.pswp;\n\n    if (currSlide) {\n      this._startZoomLevel = currSlide.currZoomLevel;\n      equalizePoints(this._startPan, currSlide.pan);\n    }\n\n    this.gestures.pswp.animations.stopAllPan();\n    this._wasOverFitZoomLevel = false;\n  }\n\n  change() {\n    const {\n      p1,\n      startP1,\n      p2,\n      startP2,\n      pswp\n    } = this.gestures;\n    const {\n      currSlide\n    } = pswp;\n\n    if (!currSlide) {\n      return;\n    }\n\n    const minZoomLevel = currSlide.zoomLevels.min;\n    const maxZoomLevel = currSlide.zoomLevels.max;\n\n    if (!currSlide.isZoomable() || pswp.mainScroll.isShifted()) {\n      return;\n    }\n\n    getZoomPointsCenter(this._startZoomPoint, startP1, startP2);\n    getZoomPointsCenter(this._zoomPoint, p1, p2);\n\n    let currZoomLevel = 1 / getDistanceBetween(startP1, startP2) * getDistanceBetween(p1, p2) * this._startZoomLevel; // slightly over the zoom.fit\n\n\n    if (currZoomLevel > currSlide.zoomLevels.initial + currSlide.zoomLevels.initial / 15) {\n      this._wasOverFitZoomLevel = true;\n    }\n\n    if (currZoomLevel < minZoomLevel) {\n      if (pswp.options.pinchToClose && !this._wasOverFitZoomLevel && this._startZoomLevel <= currSlide.zoomLevels.initial) {\n        // fade out background if zooming out\n        const bgOpacity = 1 - (minZoomLevel - currZoomLevel) / (minZoomLevel / 1.2);\n\n        if (!pswp.dispatch('pinchClose', {\n          bgOpacity\n        }).defaultPrevented) {\n          pswp.applyBgOpacity(bgOpacity);\n        }\n      } else {\n        // Apply the friction if zoom level is below the min\n        currZoomLevel = minZoomLevel - (minZoomLevel - currZoomLevel) * LOWER_ZOOM_FRICTION;\n      }\n    } else if (currZoomLevel > maxZoomLevel) {\n      // Apply the friction if zoom level is above the max\n      currZoomLevel = maxZoomLevel + (currZoomLevel - maxZoomLevel) * UPPER_ZOOM_FRICTION;\n    }\n\n    currSlide.pan.x = this._calculatePanForZoomLevel('x', currZoomLevel);\n    currSlide.pan.y = this._calculatePanForZoomLevel('y', currZoomLevel);\n    currSlide.setZoomLevel(currZoomLevel);\n    currSlide.applyCurrentZoomPan();\n  }\n\n  end() {\n    const {\n      pswp\n    } = this.gestures;\n    const {\n      currSlide\n    } = pswp;\n\n    if ((!currSlide || currSlide.currZoomLevel < currSlide.zoomLevels.initial) && !this._wasOverFitZoomLevel && pswp.options.pinchToClose) {\n      pswp.close();\n    } else {\n      this.correctZoomPan();\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} currZoomLevel\r\n   * @returns {number}\r\n   */\n\n\n  _calculatePanForZoomLevel(axis, currZoomLevel) {\n    const zoomFactor = currZoomLevel / this._startZoomLevel;\n    return this._zoomPoint[axis] - (this._startZoomPoint[axis] - this._startPan[axis]) * zoomFactor;\n  }\n  /**\r\n   * Correct currZoomLevel and pan if they are\r\n   * beyond minimum or maximum values.\r\n   * With animation.\r\n   *\r\n   * @param {boolean} [ignoreGesture]\r\n   * Wether gesture coordinates should be ignored when calculating destination pan position.\r\n   */\n\n\n  correctZoomPan(ignoreGesture) {\n    const {\n      pswp\n    } = this.gestures;\n    const {\n      currSlide\n    } = pswp;\n\n    if (!(currSlide !== null && currSlide !== void 0 && currSlide.isZoomable())) {\n      return;\n    }\n\n    if (this._zoomPoint.x === 0) {\n      ignoreGesture = true;\n    }\n\n    const prevZoomLevel = currSlide.currZoomLevel;\n    /** @type {number} */\n\n    let destinationZoomLevel;\n    let currZoomLevelNeedsChange = true;\n\n    if (prevZoomLevel < currSlide.zoomLevels.initial) {\n      destinationZoomLevel = currSlide.zoomLevels.initial; // zoom to min\n    } else if (prevZoomLevel > currSlide.zoomLevels.max) {\n      destinationZoomLevel = currSlide.zoomLevels.max; // zoom to max\n    } else {\n      currZoomLevelNeedsChange = false;\n      destinationZoomLevel = prevZoomLevel;\n    }\n\n    const initialBgOpacity = pswp.bgOpacity;\n    const restoreBgOpacity = pswp.bgOpacity < 1;\n    const initialPan = equalizePoints({\n      x: 0,\n      y: 0\n    }, currSlide.pan);\n    let destinationPan = equalizePoints({\n      x: 0,\n      y: 0\n    }, initialPan);\n\n    if (ignoreGesture) {\n      this._zoomPoint.x = 0;\n      this._zoomPoint.y = 0;\n      this._startZoomPoint.x = 0;\n      this._startZoomPoint.y = 0;\n      this._startZoomLevel = prevZoomLevel;\n      equalizePoints(this._startPan, initialPan);\n    }\n\n    if (currZoomLevelNeedsChange) {\n      destinationPan = {\n        x: this._calculatePanForZoomLevel('x', destinationZoomLevel),\n        y: this._calculatePanForZoomLevel('y', destinationZoomLevel)\n      };\n    } // set zoom level, so pan bounds are updated according to it\n\n\n    currSlide.setZoomLevel(destinationZoomLevel);\n    destinationPan = {\n      x: currSlide.bounds.correctPan('x', destinationPan.x),\n      y: currSlide.bounds.correctPan('y', destinationPan.y)\n    }; // return zoom level and its bounds to initial\n\n    currSlide.setZoomLevel(prevZoomLevel);\n    const panNeedsChange = !pointsEqual(destinationPan, initialPan);\n\n    if (!panNeedsChange && !currZoomLevelNeedsChange && !restoreBgOpacity) {\n      // update resolution after gesture\n      currSlide._setResolution(destinationZoomLevel);\n\n      currSlide.applyCurrentZoomPan(); // nothing to animate\n\n      return;\n    }\n\n    pswp.animations.stopAllPan();\n    pswp.animations.startSpring({\n      isPan: true,\n      start: 0,\n      end: 1000,\n      velocity: 0,\n      dampingRatio: 1,\n      naturalFrequency: 40,\n      onUpdate: now => {\n        now /= 1000; // 0 - start, 1 - end\n\n        if (panNeedsChange || currZoomLevelNeedsChange) {\n          if (panNeedsChange) {\n            currSlide.pan.x = initialPan.x + (destinationPan.x - initialPan.x) * now;\n            currSlide.pan.y = initialPan.y + (destinationPan.y - initialPan.y) * now;\n          }\n\n          if (currZoomLevelNeedsChange) {\n            const newZoomLevel = prevZoomLevel + (destinationZoomLevel - prevZoomLevel) * now;\n            currSlide.setZoomLevel(newZoomLevel);\n          }\n\n          currSlide.applyCurrentZoomPan();\n        } // Restore background opacity\n\n\n        if (restoreBgOpacity && pswp.bgOpacity < 1) {\n          // We clamp opacity to keep it between 0 and 1.\n          // As progress ratio can be larger than 1 due to overshoot,\n          // and we do not want to bounce opacity.\n          pswp.applyBgOpacity(clamp(initialBgOpacity + (1 - initialBgOpacity) * now, 0, 1));\n        }\n      },\n      onComplete: () => {\n        // update resolution after transition ends\n        currSlide._setResolution(destinationZoomLevel);\n\n        currSlide.applyCurrentZoomPan();\n      }\n    });\n  }\n\n}\n\n/**\r\n * @template {string} T\r\n * @template {string} P\r\n * @typedef {import('../types.js').AddPostfix<T, P>} AddPostfix<T, P>\r\n */\n\n/** @typedef {import('./gestures.js').default} Gestures */\n\n/** @typedef {import('../photoswipe.js').Point} Point */\n\n/** @typedef {'imageClick' | 'bgClick' | 'tap' | 'doubleTap'} Actions */\n\n/**\r\n * Whether the tap was performed on the main slide\r\n * (rather than controls or caption).\r\n *\r\n * @param {PointerEvent} event\r\n * @returns {boolean}\r\n */\nfunction didTapOnMainContent(event) {\n  return !!\n  /** @type {HTMLElement} */\n  event.target.closest('.pswp__container');\n}\n/**\r\n * Tap, double-tap handler.\r\n */\n\n\nclass TapHandler {\n  /**\r\n   * @param {Gestures} gestures\r\n   */\n  constructor(gestures) {\n    this.gestures = gestures;\n  }\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\n\n\n  click(point, originalEvent) {\n    const targetClassList =\n    /** @type {HTMLElement} */\n    originalEvent.target.classList;\n    const isImageClick = targetClassList.contains('pswp__img');\n    const isBackgroundClick = targetClassList.contains('pswp__item') || targetClassList.contains('pswp__zoom-wrap');\n\n    if (isImageClick) {\n      this._doClickOrTapAction('imageClick', point, originalEvent);\n    } else if (isBackgroundClick) {\n      this._doClickOrTapAction('bgClick', point, originalEvent);\n    }\n  }\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\n\n\n  tap(point, originalEvent) {\n    if (didTapOnMainContent(originalEvent)) {\n      this._doClickOrTapAction('tap', point, originalEvent);\n    }\n  }\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\n\n\n  doubleTap(point, originalEvent) {\n    if (didTapOnMainContent(originalEvent)) {\n      this._doClickOrTapAction('doubleTap', point, originalEvent);\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {Actions} actionName\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\n\n\n  _doClickOrTapAction(actionName, point, originalEvent) {\n    var _this$gestures$pswp$e;\n\n    const {\n      pswp\n    } = this.gestures;\n    const {\n      currSlide\n    } = pswp;\n    const actionFullName =\n    /** @type {AddPostfix<Actions, 'Action'>} */\n    actionName + 'Action';\n    const optionValue = pswp.options[actionFullName];\n\n    if (pswp.dispatch(actionFullName, {\n      point,\n      originalEvent\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (typeof optionValue === 'function') {\n      optionValue.call(pswp, point, originalEvent);\n      return;\n    }\n\n    switch (optionValue) {\n      case 'close':\n      case 'next':\n        pswp[optionValue]();\n        break;\n\n      case 'zoom':\n        currSlide === null || currSlide === void 0 || currSlide.toggleZoom(point);\n        break;\n\n      case 'zoom-or-close':\n        // by default click zooms current image,\n        // if it can not be zoomed - gallery will be closed\n        if (currSlide !== null && currSlide !== void 0 && currSlide.isZoomable() && currSlide.zoomLevels.secondary !== currSlide.zoomLevels.initial) {\n          currSlide.toggleZoom(point);\n        } else if (pswp.options.clickToCloseNonZoomable) {\n          pswp.close();\n        }\n\n        break;\n\n      case 'toggle-controls':\n        (_this$gestures$pswp$e = this.gestures.pswp.element) === null || _this$gestures$pswp$e === void 0 || _this$gestures$pswp$e.classList.toggle('pswp--ui-visible'); // if (_controlsVisible) {\n        //   _ui.hideControls();\n        // } else {\n        //   _ui.showControls();\n        // }\n\n        break;\n    }\n  }\n\n}\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {import('../photoswipe.js').Point} Point */\n// How far should user should drag\n// until we can determine that the gesture is swipe and its direction\n\nconst AXIS_SWIPE_HYSTERISIS = 10; //const PAN_END_FRICTION = 0.35;\n\nconst DOUBLE_TAP_DELAY = 300; // ms\n\nconst MIN_TAP_DISTANCE = 25; // px\n\n/**\r\n * Gestures class bind touch, pointer or mouse events\r\n * and emits drag to drag-handler and zoom events zoom-handler.\r\n *\r\n * Drag and zoom events are emited in requestAnimationFrame,\r\n * and only when one of pointers was actually changed.\r\n */\n\nclass Gestures {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp;\n    /** @type {'x' | 'y' | null} */\n\n    this.dragAxis = null; // point objects are defined once and reused\n    // PhotoSwipe keeps track only of two pointers, others are ignored\n\n    /** @type {Point} */\n\n    this.p1 = {\n      x: 0,\n      y: 0\n    }; // the first pressed pointer\n\n    /** @type {Point} */\n\n    this.p2 = {\n      x: 0,\n      y: 0\n    }; // the second pressed pointer\n\n    /** @type {Point} */\n\n    this.prevP1 = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point} */\n\n    this.prevP2 = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point} */\n\n    this.startP1 = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point} */\n\n    this.startP2 = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point} */\n\n    this.velocity = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point}\r\n     * @private\r\n     */\n\n    this._lastStartP1 = {\n      x: 0,\n      y: 0\n    };\n    /** @type {Point}\r\n     * @private\r\n     */\n\n    this._intervalP1 = {\n      x: 0,\n      y: 0\n    };\n    /** @private */\n\n    this._numActivePoints = 0;\n    /** @type {Point[]}\r\n     * @private\r\n     */\n\n    this._ongoingPointers = [];\n    /** @private */\n\n    this._touchEventEnabled = 'ontouchstart' in window;\n    /** @private */\n\n    this._pointerEventEnabled = !!window.PointerEvent;\n    this.supportsTouch = this._touchEventEnabled || this._pointerEventEnabled && navigator.maxTouchPoints > 1;\n    /** @private */\n\n    this._numActivePoints = 0;\n    /** @private */\n\n    this._intervalTime = 0;\n    /** @private */\n\n    this._velocityCalculated = false;\n    this.isMultitouch = false;\n    this.isDragging = false;\n    this.isZooming = false;\n    /** @type {number | null} */\n\n    this.raf = null;\n    /** @type {NodeJS.Timeout | null}\r\n     * @private\r\n     */\n\n    this._tapTimer = null;\n\n    if (!this.supportsTouch) {\n      // disable pan to next slide for non-touch devices\n      pswp.options.allowPanToNext = false;\n    }\n\n    this.drag = new DragHandler(this);\n    this.zoomLevels = new ZoomHandler(this);\n    this.tapHandler = new TapHandler(this);\n    pswp.on('bindEvents', () => {\n      pswp.events.add(pswp.scrollWrap, 'click',\n      /** @type EventListener */\n      this._onClick.bind(this));\n\n      if (this._pointerEventEnabled) {\n        this._bindEvents('pointer', 'down', 'up', 'cancel');\n      } else if (this._touchEventEnabled) {\n        this._bindEvents('touch', 'start', 'end', 'cancel'); // In previous versions we also bound mouse event here,\n        // in case device supports both touch and mouse events,\n        // but newer versions of browsers now support PointerEvent.\n        // on iOS10 if you bind touchmove/end after touchstart,\n        // and you don't preventDefault touchstart (which PhotoSwipe does),\n        // preventDefault will have no effect on touchmove and touchend.\n        // Unless you bind it previously.\n\n\n        if (pswp.scrollWrap) {\n          pswp.scrollWrap.ontouchmove = () => {};\n\n          pswp.scrollWrap.ontouchend = () => {};\n        }\n      } else {\n        this._bindEvents('mouse', 'down', 'up');\n      }\n    });\n  }\n  /**\r\n   * @private\r\n   * @param {'mouse' | 'touch' | 'pointer'} pref\r\n   * @param {'down' | 'start'} down\r\n   * @param {'up' | 'end'} up\r\n   * @param {'cancel'} [cancel]\r\n   */\n\n\n  _bindEvents(pref, down, up, cancel) {\n    const {\n      pswp\n    } = this;\n    const {\n      events\n    } = pswp;\n    const cancelEvent = cancel ? pref + cancel : '';\n    events.add(pswp.scrollWrap, pref + down,\n    /** @type EventListener */\n    this.onPointerDown.bind(this));\n    events.add(window, pref + 'move',\n    /** @type EventListener */\n    this.onPointerMove.bind(this));\n    events.add(window, pref + up,\n    /** @type EventListener */\n    this.onPointerUp.bind(this));\n\n    if (cancelEvent) {\n      events.add(pswp.scrollWrap, cancelEvent,\n      /** @type EventListener */\n      this.onPointerUp.bind(this));\n    }\n  }\n  /**\r\n   * @param {PointerEvent} e\r\n   */\n\n\n  onPointerDown(e) {\n    // We do not call preventDefault for touch events\n    // to allow browser to show native dialog on longpress\n    // (the one that allows to save image or open it in new tab).\n    //\n    // Desktop Safari allows to drag images when preventDefault isn't called on mousedown,\n    // even though preventDefault IS called on mousemove. That's why we preventDefault mousedown.\n    const isMousePointer = e.type === 'mousedown' || e.pointerType === 'mouse'; // Allow dragging only via left mouse button.\n    // http://www.quirksmode.org/js/events_properties.html\n    // https://developer.mozilla.org/en-US/docs/Web/API/event.button\n\n    if (isMousePointer && e.button > 0) {\n      return;\n    }\n\n    const {\n      pswp\n    } = this; // if PhotoSwipe is opening or closing\n\n    if (!pswp.opener.isOpen) {\n      e.preventDefault();\n      return;\n    }\n\n    if (pswp.dispatch('pointerDown', {\n      originalEvent: e\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (isMousePointer) {\n      pswp.mouseDetected(); // preventDefault mouse event to prevent\n      // browser image drag feature\n\n      this._preventPointerEventBehaviour(e, 'down');\n    }\n\n    pswp.animations.stopAll();\n\n    this._updatePoints(e, 'down');\n\n    if (this._numActivePoints === 1) {\n      this.dragAxis = null; // we need to store initial point to determine the main axis,\n      // drag is activated only after the axis is determined\n\n      equalizePoints(this.startP1, this.p1);\n    }\n\n    if (this._numActivePoints > 1) {\n      // Tap or double tap should not trigger if more than one pointer\n      this._clearTapTimer();\n\n      this.isMultitouch = true;\n    } else {\n      this.isMultitouch = false;\n    }\n  }\n  /**\r\n   * @param {PointerEvent} e\r\n   */\n\n\n  onPointerMove(e) {\n    this._preventPointerEventBehaviour(e, 'move');\n\n    if (!this._numActivePoints) {\n      return;\n    }\n\n    this._updatePoints(e, 'move');\n\n    if (this.pswp.dispatch('pointerMove', {\n      originalEvent: e\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (this._numActivePoints === 1 && !this.isDragging) {\n      if (!this.dragAxis) {\n        this._calculateDragDirection();\n      } // Drag axis was detected, emit drag.start\n\n\n      if (this.dragAxis && !this.isDragging) {\n        if (this.isZooming) {\n          this.isZooming = false;\n          this.zoomLevels.end();\n        }\n\n        this.isDragging = true;\n\n        this._clearTapTimer(); // Tap can not trigger after drag\n        // Adjust starting point\n\n\n        this._updateStartPoints();\n\n        this._intervalTime = Date.now(); //this._startTime = this._intervalTime;\n\n        this._velocityCalculated = false;\n        equalizePoints(this._intervalP1, this.p1);\n        this.velocity.x = 0;\n        this.velocity.y = 0;\n        this.drag.start();\n\n        this._rafStopLoop();\n\n        this._rafRenderLoop();\n      }\n    } else if (this._numActivePoints > 1 && !this.isZooming) {\n      this._finishDrag();\n\n      this.isZooming = true; // Adjust starting points\n\n      this._updateStartPoints();\n\n      this.zoomLevels.start();\n\n      this._rafStopLoop();\n\n      this._rafRenderLoop();\n    }\n  }\n  /**\r\n   * @private\r\n   */\n\n\n  _finishDrag() {\n    if (this.isDragging) {\n      this.isDragging = false; // Try to calculate velocity,\n      // if it wasn't calculated yet in drag.change\n\n      if (!this._velocityCalculated) {\n        this._updateVelocity(true);\n      }\n\n      this.drag.end();\n      this.dragAxis = null;\n    }\n  }\n  /**\r\n   * @param {PointerEvent} e\r\n   */\n\n\n  onPointerUp(e) {\n    if (!this._numActivePoints) {\n      return;\n    }\n\n    this._updatePoints(e, 'up');\n\n    if (this.pswp.dispatch('pointerUp', {\n      originalEvent: e\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (this._numActivePoints === 0) {\n      this._rafStopLoop();\n\n      if (this.isDragging) {\n        this._finishDrag();\n      } else if (!this.isZooming && !this.isMultitouch) {\n        //this.zoomLevels.correctZoomPan();\n        this._finishTap(e);\n      }\n    }\n\n    if (this._numActivePoints < 2 && this.isZooming) {\n      this.isZooming = false;\n      this.zoomLevels.end();\n\n      if (this._numActivePoints === 1) {\n        // Since we have 1 point left, we need to reinitiate drag\n        this.dragAxis = null;\n\n        this._updateStartPoints();\n      }\n    }\n  }\n  /**\r\n   * @private\r\n   */\n\n\n  _rafRenderLoop() {\n    if (this.isDragging || this.isZooming) {\n      this._updateVelocity();\n\n      if (this.isDragging) {\n        // make sure that pointer moved since the last update\n        if (!pointsEqual(this.p1, this.prevP1)) {\n          this.drag.change();\n        }\n      } else\n        /* if (this.isZooming) */\n        {\n          if (!pointsEqual(this.p1, this.prevP1) || !pointsEqual(this.p2, this.prevP2)) {\n            this.zoomLevels.change();\n          }\n        }\n\n      this._updatePrevPoints();\n\n      this.raf = requestAnimationFrame(this._rafRenderLoop.bind(this));\n    }\n  }\n  /**\r\n   * Update velocity at 50ms interval\r\n   *\r\n   * @private\r\n   * @param {boolean} [force]\r\n   */\n\n\n  _updateVelocity(force) {\n    const time = Date.now();\n    const duration = time - this._intervalTime;\n\n    if (duration < 50 && !force) {\n      return;\n    }\n\n    this.velocity.x = this._getVelocity('x', duration);\n    this.velocity.y = this._getVelocity('y', duration);\n    this._intervalTime = time;\n    equalizePoints(this._intervalP1, this.p1);\n    this._velocityCalculated = true;\n  }\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   */\n\n\n  _finishTap(e) {\n    const {\n      mainScroll\n    } = this.pswp; // Do not trigger tap events if main scroll is shifted\n\n    if (mainScroll.isShifted()) {\n      // restore main scroll position\n      // (usually happens if stopped in the middle of animation)\n      mainScroll.moveIndexBy(0, true);\n      return;\n    } // Do not trigger tap for touchcancel or pointercancel\n\n\n    if (e.type.indexOf('cancel') > 0) {\n      return;\n    } // Trigger click instead of tap for mouse events\n\n\n    if (e.type === 'mouseup' || e.pointerType === 'mouse') {\n      this.tapHandler.click(this.startP1, e);\n      return;\n    } // Disable delay if there is no doubleTapAction\n\n\n    const tapDelay = this.pswp.options.doubleTapAction ? DOUBLE_TAP_DELAY : 0; // If tapTimer is defined - we tapped recently,\n    // check if the current tap is close to the previous one,\n    // if yes - trigger double tap\n\n    if (this._tapTimer) {\n      this._clearTapTimer(); // Check if two taps were more or less on the same place\n\n\n      if (getDistanceBetween(this._lastStartP1, this.startP1) < MIN_TAP_DISTANCE) {\n        this.tapHandler.doubleTap(this.startP1, e);\n      }\n    } else {\n      equalizePoints(this._lastStartP1, this.startP1);\n      this._tapTimer = setTimeout(() => {\n        this.tapHandler.tap(this.startP1, e);\n\n        this._clearTapTimer();\n      }, tapDelay);\n    }\n  }\n  /**\r\n   * @private\r\n   */\n\n\n  _clearTapTimer() {\n    if (this._tapTimer) {\n      clearTimeout(this._tapTimer);\n      this._tapTimer = null;\n    }\n  }\n  /**\r\n   * Get velocity for axis\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} duration\r\n   * @returns {number}\r\n   */\n\n\n  _getVelocity(axis, duration) {\n    // displacement is like distance, but can be negative.\n    const displacement = this.p1[axis] - this._intervalP1[axis];\n\n    if (Math.abs(displacement) > 1 && duration > 5) {\n      return displacement / duration;\n    }\n\n    return 0;\n  }\n  /**\r\n   * @private\r\n   */\n\n\n  _rafStopLoop() {\n    if (this.raf) {\n      cancelAnimationFrame(this.raf);\n      this.raf = null;\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   * @param {'up' | 'down' | 'move'} pointerType Normalized pointer type\r\n   */\n\n\n  _preventPointerEventBehaviour(e, pointerType) {\n    const preventPointerEvent = this.pswp.applyFilters('preventPointerEvent', true, e, pointerType);\n\n    if (preventPointerEvent) {\n      e.preventDefault();\n    }\n  }\n  /**\r\n   * Parses and normalizes points from the touch, mouse or pointer event.\r\n   * Updates p1 and p2.\r\n   *\r\n   * @private\r\n   * @param {PointerEvent | TouchEvent} e\r\n   * @param {'up' | 'down' | 'move'} pointerType Normalized pointer type\r\n   */\n\n\n  _updatePoints(e, pointerType) {\n    if (this._pointerEventEnabled) {\n      const pointerEvent =\n      /** @type {PointerEvent} */\n      e; // Try to find the current pointer in ongoing pointers by its ID\n\n      const pointerIndex = this._ongoingPointers.findIndex(ongoingPointer => {\n        return ongoingPointer.id === pointerEvent.pointerId;\n      });\n\n      if (pointerType === 'up' && pointerIndex > -1) {\n        // release the pointer - remove it from ongoing\n        this._ongoingPointers.splice(pointerIndex, 1);\n      } else if (pointerType === 'down' && pointerIndex === -1) {\n        // add new pointer\n        this._ongoingPointers.push(this._convertEventPosToPoint(pointerEvent, {\n          x: 0,\n          y: 0\n        }));\n      } else if (pointerIndex > -1) {\n        // update existing pointer\n        this._convertEventPosToPoint(pointerEvent, this._ongoingPointers[pointerIndex]);\n      }\n\n      this._numActivePoints = this._ongoingPointers.length; // update points that PhotoSwipe uses\n      // to calculate position and scale\n\n      if (this._numActivePoints > 0) {\n        equalizePoints(this.p1, this._ongoingPointers[0]);\n      }\n\n      if (this._numActivePoints > 1) {\n        equalizePoints(this.p2, this._ongoingPointers[1]);\n      }\n    } else {\n      const touchEvent =\n      /** @type {TouchEvent} */\n      e;\n      this._numActivePoints = 0;\n\n      if (touchEvent.type.indexOf('touch') > -1) {\n        // Touch Event\n        // https://developer.mozilla.org/en-US/docs/Web/API/TouchEvent\n        if (touchEvent.touches && touchEvent.touches.length > 0) {\n          this._convertEventPosToPoint(touchEvent.touches[0], this.p1);\n\n          this._numActivePoints++;\n\n          if (touchEvent.touches.length > 1) {\n            this._convertEventPosToPoint(touchEvent.touches[1], this.p2);\n\n            this._numActivePoints++;\n          }\n        }\n      } else {\n        // Mouse Event\n        this._convertEventPosToPoint(\n        /** @type {PointerEvent} */\n        e, this.p1);\n\n        if (pointerType === 'up') {\n          // clear all points on mouseup\n          this._numActivePoints = 0;\n        } else {\n          this._numActivePoints++;\n        }\n      }\n    }\n  }\n  /** update points that were used during previous rAF tick\r\n   * @private\r\n   */\n\n\n  _updatePrevPoints() {\n    equalizePoints(this.prevP1, this.p1);\n    equalizePoints(this.prevP2, this.p2);\n  }\n  /** update points at the start of gesture\r\n   * @private\r\n   */\n\n\n  _updateStartPoints() {\n    equalizePoints(this.startP1, this.p1);\n    equalizePoints(this.startP2, this.p2);\n\n    this._updatePrevPoints();\n  }\n  /** @private */\n\n\n  _calculateDragDirection() {\n    if (this.pswp.mainScroll.isShifted()) {\n      // if main scroll position is shifted – direction is always horizontal\n      this.dragAxis = 'x';\n    } else {\n      // calculate delta of the last touchmove tick\n      const diff = Math.abs(this.p1.x - this.startP1.x) - Math.abs(this.p1.y - this.startP1.y);\n\n      if (diff !== 0) {\n        // check if pointer was shifted horizontally or vertically\n        const axisToCheck = diff > 0 ? 'x' : 'y';\n\n        if (Math.abs(this.p1[axisToCheck] - this.startP1[axisToCheck]) >= AXIS_SWIPE_HYSTERISIS) {\n          this.dragAxis = axisToCheck;\n        }\n      }\n    }\n  }\n  /**\r\n   * Converts touch, pointer or mouse event\r\n   * to PhotoSwipe point.\r\n   *\r\n   * @private\r\n   * @param {Touch | PointerEvent} e\r\n   * @param {Point} p\r\n   * @returns {Point}\r\n   */\n\n\n  _convertEventPosToPoint(e, p) {\n    p.x = e.pageX - this.pswp.offset.x;\n    p.y = e.pageY - this.pswp.offset.y;\n\n    if ('pointerId' in e) {\n      p.id = e.pointerId;\n    } else if (e.identifier !== undefined) {\n      p.id = e.identifier;\n    }\n\n    return p;\n  }\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   */\n\n\n  _onClick(e) {\n    // Do not allow click event to pass through after drag\n    if (this.pswp.mainScroll.isShifted()) {\n      e.preventDefault();\n      e.stopPropagation();\n    }\n  }\n\n}\n\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {import('./slide/slide.js').default} Slide */\n\n/** @typedef {{ el: HTMLDivElement; slide?: Slide }} ItemHolder */\n\nconst MAIN_SCROLL_END_FRICTION = 0.35; // const MIN_SWIPE_TRANSITION_DURATION = 250;\n// const MAX_SWIPE_TRABSITION_DURATION = 500;\n// const DEFAULT_SWIPE_TRANSITION_DURATION = 333;\n\n/**\r\n * Handles movement of the main scrolling container\r\n * (for example, it repositions when user swipes left or right).\r\n *\r\n * Also stores its state.\r\n */\n\nclass MainScroll {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp;\n    this.x = 0;\n    this.slideWidth = 0;\n    /** @private */\n\n    this._currPositionIndex = 0;\n    /** @private */\n\n    this._prevPositionIndex = 0;\n    /** @private */\n\n    this._containerShiftIndex = -1;\n    /** @type {ItemHolder[]} */\n\n    this.itemHolders = [];\n  }\n  /**\r\n   * Position the scroller and slide containers\r\n   * according to viewport size.\r\n   *\r\n   * @param {boolean} [resizeSlides] Whether slides content should resized\r\n   */\n\n\n  resize(resizeSlides) {\n    const {\n      pswp\n    } = this;\n    const newSlideWidth = Math.round(pswp.viewportSize.x + pswp.viewportSize.x * pswp.options.spacing); // Mobile browsers might trigger a resize event during a gesture.\n    // (due to toolbar appearing or hiding).\n    // Avoid re-adjusting main scroll position if width wasn't changed\n\n    const slideWidthChanged = newSlideWidth !== this.slideWidth;\n\n    if (slideWidthChanged) {\n      this.slideWidth = newSlideWidth;\n      this.moveTo(this.getCurrSlideX());\n    }\n\n    this.itemHolders.forEach((itemHolder, index) => {\n      if (slideWidthChanged) {\n        setTransform(itemHolder.el, (index + this._containerShiftIndex) * this.slideWidth);\n      }\n\n      if (resizeSlides && itemHolder.slide) {\n        itemHolder.slide.resize();\n      }\n    });\n  }\n  /**\r\n   * Reset X position of the main scroller to zero\r\n   */\n\n\n  resetPosition() {\n    // Position on the main scroller (offset)\n    // it is independent from slide index\n    this._currPositionIndex = 0;\n    this._prevPositionIndex = 0; // This will force recalculation of size on next resize()\n\n    this.slideWidth = 0; // _containerShiftIndex*viewportSize will give you amount of transform of the current slide\n\n    this._containerShiftIndex = -1;\n  }\n  /**\r\n   * Create and append array of three items\r\n   * that hold data about slides in DOM\r\n   */\n\n\n  appendHolders() {\n    this.itemHolders = []; // append our three slide holders -\n    // previous, current, and next\n\n    for (let i = 0; i < 3; i++) {\n      const el = createElement('pswp__item', 'div', this.pswp.container);\n      el.setAttribute('role', 'group');\n      el.setAttribute('aria-roledescription', 'slide');\n      el.setAttribute('aria-hidden', 'true'); // hide nearby item holders until initial zoom animation finishes (to avoid extra Paints)\n\n      el.style.display = i === 1 ? 'block' : 'none';\n      this.itemHolders.push({\n        el //index: -1\n\n      });\n    }\n  }\n  /**\r\n   * Whether the main scroll can be horizontally swiped to the next or previous slide.\r\n   * @returns {boolean}\r\n   */\n\n\n  canBeSwiped() {\n    return this.pswp.getNumItems() > 1;\n  }\n  /**\r\n   * Move main scroll by X amount of slides.\r\n   * For example:\r\n   *   `-1` will move to the previous slide,\r\n   *    `0` will reset the scroll position of the current slide,\r\n   *    `3` will move three slides forward\r\n   *\r\n   * If loop option is enabled - index will be automatically looped too,\r\n   * (for example `-1` will move to the last slide of the gallery).\r\n   *\r\n   * @param {number} diff\r\n   * @param {boolean} [animate]\r\n   * @param {number} [velocityX]\r\n   * @returns {boolean} whether index was changed or not\r\n   */\n\n\n  moveIndexBy(diff, animate, velocityX) {\n    const {\n      pswp\n    } = this;\n    let newIndex = pswp.potentialIndex + diff;\n    const numSlides = pswp.getNumItems();\n\n    if (pswp.canLoop()) {\n      newIndex = pswp.getLoopedIndex(newIndex);\n      const distance = (diff + numSlides) % numSlides;\n\n      if (distance <= numSlides / 2) {\n        // go forward\n        diff = distance;\n      } else {\n        // go backwards\n        diff = distance - numSlides;\n      }\n    } else {\n      if (newIndex < 0) {\n        newIndex = 0;\n      } else if (newIndex >= numSlides) {\n        newIndex = numSlides - 1;\n      }\n\n      diff = newIndex - pswp.potentialIndex;\n    }\n\n    pswp.potentialIndex = newIndex;\n    this._currPositionIndex -= diff;\n    pswp.animations.stopMainScroll();\n    const destinationX = this.getCurrSlideX();\n\n    if (!animate) {\n      this.moveTo(destinationX);\n      this.updateCurrItem();\n    } else {\n      pswp.animations.startSpring({\n        isMainScroll: true,\n        start: this.x,\n        end: destinationX,\n        velocity: velocityX || 0,\n        naturalFrequency: 30,\n        dampingRatio: 1,\n        //0.7,\n        onUpdate: x => {\n          this.moveTo(x);\n        },\n        onComplete: () => {\n          this.updateCurrItem();\n          pswp.appendHeavy();\n        }\n      });\n      let currDiff = pswp.potentialIndex - pswp.currIndex;\n\n      if (pswp.canLoop()) {\n        const currDistance = (currDiff + numSlides) % numSlides;\n\n        if (currDistance <= numSlides / 2) {\n          // go forward\n          currDiff = currDistance;\n        } else {\n          // go backwards\n          currDiff = currDistance - numSlides;\n        }\n      } // Force-append new slides during transition\n      // if difference between slides is more than 1\n\n\n      if (Math.abs(currDiff) > 1) {\n        this.updateCurrItem();\n      }\n    }\n\n    return Boolean(diff);\n  }\n  /**\r\n   * X position of the main scroll for the current slide\r\n   * (ignores position during dragging)\r\n   * @returns {number}\r\n   */\n\n\n  getCurrSlideX() {\n    return this.slideWidth * this._currPositionIndex;\n  }\n  /**\r\n   * Whether scroll position is shifted.\r\n   * For example, it will return true if the scroll is being dragged or animated.\r\n   * @returns {boolean}\r\n   */\n\n\n  isShifted() {\n    return this.x !== this.getCurrSlideX();\n  }\n  /**\r\n   * Update slides X positions and set their content\r\n   */\n\n\n  updateCurrItem() {\n    var _this$itemHolders$;\n\n    const {\n      pswp\n    } = this;\n    const positionDifference = this._prevPositionIndex - this._currPositionIndex;\n\n    if (!positionDifference) {\n      return;\n    }\n\n    this._prevPositionIndex = this._currPositionIndex;\n    pswp.currIndex = pswp.potentialIndex;\n    let diffAbs = Math.abs(positionDifference);\n    /** @type {ItemHolder | undefined} */\n\n    let tempHolder;\n\n    if (diffAbs >= 3) {\n      this._containerShiftIndex += positionDifference + (positionDifference > 0 ? -3 : 3);\n      diffAbs = 3; // If slides are changed by 3 screens or more - clean up previous slides\n\n      this.itemHolders.forEach(itemHolder => {\n        var _itemHolder$slide;\n\n        (_itemHolder$slide = itemHolder.slide) === null || _itemHolder$slide === void 0 || _itemHolder$slide.destroy();\n        itemHolder.slide = undefined;\n      });\n    }\n\n    for (let i = 0; i < diffAbs; i++) {\n      if (positionDifference > 0) {\n        tempHolder = this.itemHolders.shift();\n\n        if (tempHolder) {\n          this.itemHolders[2] = tempHolder; // move first to last\n\n          this._containerShiftIndex++;\n          setTransform(tempHolder.el, (this._containerShiftIndex + 2) * this.slideWidth);\n          pswp.setContent(tempHolder, pswp.currIndex - diffAbs + i + 2);\n        }\n      } else {\n        tempHolder = this.itemHolders.pop();\n\n        if (tempHolder) {\n          this.itemHolders.unshift(tempHolder); // move last to first\n\n          this._containerShiftIndex--;\n          setTransform(tempHolder.el, this._containerShiftIndex * this.slideWidth);\n          pswp.setContent(tempHolder, pswp.currIndex + diffAbs - i - 2);\n        }\n      }\n    } // Reset transfrom every 50ish navigations in one direction.\n    //\n    // Otherwise transform will keep growing indefinitely,\n    // which might cause issues as browsers have a maximum transform limit.\n    // I wasn't able to reach it, but just to be safe.\n    // This should not cause noticable lag.\n\n\n    if (Math.abs(this._containerShiftIndex) > 50 && !this.isShifted()) {\n      this.resetPosition();\n      this.resize();\n    } // Pan transition might be running (and consntantly updating pan position)\n\n\n    pswp.animations.stopAllPan();\n    this.itemHolders.forEach((itemHolder, i) => {\n      if (itemHolder.slide) {\n        // Slide in the 2nd holder is always active\n        itemHolder.slide.setIsActive(i === 1);\n      }\n    });\n    pswp.currSlide = (_this$itemHolders$ = this.itemHolders[1]) === null || _this$itemHolders$ === void 0 ? void 0 : _this$itemHolders$.slide;\n    pswp.contentLoader.updateLazy(positionDifference);\n\n    if (pswp.currSlide) {\n      pswp.currSlide.applyCurrentZoomPan();\n    }\n\n    pswp.dispatch('change');\n  }\n  /**\r\n   * Move the X position of the main scroll container\r\n   *\r\n   * @param {number} x\r\n   * @param {boolean} [dragging]\r\n   */\n\n\n  moveTo(x, dragging) {\n    if (!this.pswp.canLoop() && dragging) {\n      // Apply friction\n      let newSlideIndexOffset = (this.slideWidth * this._currPositionIndex - x) / this.slideWidth;\n      newSlideIndexOffset += this.pswp.currIndex;\n      const delta = Math.round(x - this.x);\n\n      if (newSlideIndexOffset < 0 && delta > 0 || newSlideIndexOffset >= this.pswp.getNumItems() - 1 && delta < 0) {\n        x = this.x + delta * MAIN_SCROLL_END_FRICTION;\n      }\n    }\n\n    this.x = x;\n\n    if (this.pswp.container) {\n      setTransform(this.pswp.container, x);\n    }\n\n    this.pswp.dispatch('moveMainScroll', {\n      x,\n      dragging: dragging !== null && dragging !== void 0 ? dragging : false\n    });\n  }\n\n}\n\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\n\n/**\r\n * @template T\r\n * @typedef {import('./types.js').Methods<T>} Methods<T>\r\n */\n\nconst KeyboardKeyCodesMap = {\n  Escape: 27,\n  z: 90,\n  ArrowLeft: 37,\n  ArrowUp: 38,\n  ArrowRight: 39,\n  ArrowDown: 40,\n  Tab: 9\n};\n/**\r\n * @template {keyof KeyboardKeyCodesMap} T\r\n * @param {T} key\r\n * @param {boolean} isKeySupported\r\n * @returns {T | number | undefined}\r\n */\n\nconst getKeyboardEventKey = (key, isKeySupported) => {\n  return isKeySupported ? key : KeyboardKeyCodesMap[key];\n};\n/**\r\n * - Manages keyboard shortcuts.\r\n * - Helps trap focus within photoswipe.\r\n */\n\n\nclass Keyboard {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp;\n    /** @private */\n\n    this._wasFocused = false;\n    pswp.on('bindEvents', () => {\n      if (pswp.options.trapFocus) {\n        // Dialog was likely opened by keyboard if initial point is not defined\n        if (!pswp.options.initialPointerPos) {\n          // focus causes layout,\n          // which causes lag during the animation,\n          // that's why we delay it until the opener transition ends\n          this._focusRoot();\n        }\n\n        pswp.events.add(document, 'focusin',\n        /** @type EventListener */\n        this._onFocusIn.bind(this));\n      }\n\n      pswp.events.add(document, 'keydown',\n      /** @type EventListener */\n      this._onKeyDown.bind(this));\n    });\n    const lastActiveElement =\n    /** @type {HTMLElement} */\n    document.activeElement;\n    pswp.on('destroy', () => {\n      if (pswp.options.returnFocus && lastActiveElement && this._wasFocused) {\n        lastActiveElement.focus();\n      }\n    });\n  }\n  /** @private */\n\n\n  _focusRoot() {\n    if (!this._wasFocused && this.pswp.element) {\n      this.pswp.element.focus();\n      this._wasFocused = true;\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {KeyboardEvent} e\r\n   */\n\n\n  _onKeyDown(e) {\n    const {\n      pswp\n    } = this;\n\n    if (pswp.dispatch('keydown', {\n      originalEvent: e\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (specialKeyUsed(e)) {\n      // don't do anything if special key pressed\n      // to prevent from overriding default browser actions\n      // for example, in Chrome on Mac cmd+arrow-left returns to previous page\n      return;\n    }\n    /** @type {Methods<PhotoSwipe> | undefined} */\n\n\n    let keydownAction;\n    /** @type {'x' | 'y' | undefined} */\n\n    let axis;\n    let isForward = false;\n    const isKeySupported = ('key' in e);\n\n    switch (isKeySupported ? e.key : e.keyCode) {\n      case getKeyboardEventKey('Escape', isKeySupported):\n        if (pswp.options.escKey) {\n          keydownAction = 'close';\n        }\n\n        break;\n\n      case getKeyboardEventKey('z', isKeySupported):\n        keydownAction = 'toggleZoom';\n        break;\n\n      case getKeyboardEventKey('ArrowLeft', isKeySupported):\n        axis = 'x';\n        break;\n\n      case getKeyboardEventKey('ArrowUp', isKeySupported):\n        axis = 'y';\n        break;\n\n      case getKeyboardEventKey('ArrowRight', isKeySupported):\n        axis = 'x';\n        isForward = true;\n        break;\n\n      case getKeyboardEventKey('ArrowDown', isKeySupported):\n        isForward = true;\n        axis = 'y';\n        break;\n\n      case getKeyboardEventKey('Tab', isKeySupported):\n        this._focusRoot();\n\n        break;\n    } // if left/right/top/bottom key\n\n\n    if (axis) {\n      // prevent page scroll\n      e.preventDefault();\n      const {\n        currSlide\n      } = pswp;\n\n      if (pswp.options.arrowKeys && axis === 'x' && pswp.getNumItems() > 1) {\n        keydownAction = isForward ? 'next' : 'prev';\n      } else if (currSlide && currSlide.currZoomLevel > currSlide.zoomLevels.fit) {\n        // up/down arrow keys pan the image vertically\n        // left/right arrow keys pan horizontally.\n        // Unless there is only one image,\n        // or arrowKeys option is disabled\n        currSlide.pan[axis] += isForward ? -80 : 80;\n        currSlide.panTo(currSlide.pan.x, currSlide.pan.y);\n      }\n    }\n\n    if (keydownAction) {\n      e.preventDefault(); // @ts-ignore\n\n      pswp[keydownAction]();\n    }\n  }\n  /**\r\n   * Trap focus inside photoswipe\r\n   *\r\n   * @private\r\n   * @param {FocusEvent} e\r\n   */\n\n\n  _onFocusIn(e) {\n    const {\n      template\n    } = this.pswp;\n\n    if (template && document !== e.target && template !== e.target && !template.contains(\n    /** @type {Node} */\n    e.target)) {\n      // focus root element\n      template.focus();\n    }\n  }\n\n}\n\nconst DEFAULT_EASING = 'cubic-bezier(.4,0,.22,1)';\n/** @typedef {import('./animations.js').SharedAnimationProps} SharedAnimationProps */\n\n/** @typedef {Object} DefaultCssAnimationProps\r\n *\r\n * @prop {HTMLElement} target\r\n * @prop {number} [duration]\r\n * @prop {string} [easing]\r\n * @prop {string} [transform]\r\n * @prop {string} [opacity]\r\n * */\n\n/** @typedef {SharedAnimationProps & DefaultCssAnimationProps} CssAnimationProps */\n\n/**\r\n * Runs CSS transition.\r\n */\n\nclass CSSAnimation {\n  /**\r\n   * onComplete can be unpredictable, be careful about current state\r\n   *\r\n   * @param {CssAnimationProps} props\r\n   */\n  constructor(props) {\n    var _props$prop;\n\n    this.props = props;\n    const {\n      target,\n      onComplete,\n      transform,\n      onFinish = () => {},\n      duration = 333,\n      easing = DEFAULT_EASING\n    } = props;\n    this.onFinish = onFinish; // support only transform and opacity\n\n    const prop = transform ? 'transform' : 'opacity';\n    const propValue = (_props$prop = props[prop]) !== null && _props$prop !== void 0 ? _props$prop : '';\n    /** @private */\n\n    this._target = target;\n    /** @private */\n\n    this._onComplete = onComplete;\n    /** @private */\n\n    this._finished = false;\n    /** @private */\n\n    this._onTransitionEnd = this._onTransitionEnd.bind(this); // Using timeout hack to make sure that animation\n    // starts even if the animated property was changed recently,\n    // otherwise transitionend might not fire or transition won't start.\n    // https://drafts.csswg.org/css-transitions/#starting\n    //\n    // ¯\\_(ツ)_/¯\n\n    /** @private */\n\n    this._helperTimeout = setTimeout(() => {\n      setTransitionStyle(target, prop, duration, easing);\n      this._helperTimeout = setTimeout(() => {\n        target.addEventListener('transitionend', this._onTransitionEnd, false);\n        target.addEventListener('transitioncancel', this._onTransitionEnd, false); // Safari occasionally does not emit transitionend event\n        // if element property was modified during the transition,\n        // which may be caused by resize or third party component,\n        // using timeout as a safety fallback\n\n        this._helperTimeout = setTimeout(() => {\n          this._finalizeAnimation();\n        }, duration + 500);\n        target.style[prop] = propValue;\n      }, 30); // Do not reduce this number\n    }, 0);\n  }\n  /**\r\n   * @private\r\n   * @param {TransitionEvent} e\r\n   */\n\n\n  _onTransitionEnd(e) {\n    if (e.target === this._target) {\n      this._finalizeAnimation();\n    }\n  }\n  /**\r\n   * @private\r\n   */\n\n\n  _finalizeAnimation() {\n    if (!this._finished) {\n      this._finished = true;\n      this.onFinish();\n\n      if (this._onComplete) {\n        this._onComplete();\n      }\n    }\n  } // Destroy is called automatically onFinish\n\n\n  destroy() {\n    if (this._helperTimeout) {\n      clearTimeout(this._helperTimeout);\n    }\n\n    removeTransitionStyle(this._target);\n\n    this._target.removeEventListener('transitionend', this._onTransitionEnd, false);\n\n    this._target.removeEventListener('transitioncancel', this._onTransitionEnd, false);\n\n    if (!this._finished) {\n      this._finalizeAnimation();\n    }\n  }\n\n}\n\nconst DEFAULT_NATURAL_FREQUENCY = 12;\nconst DEFAULT_DAMPING_RATIO = 0.75;\n/**\r\n * Spring easing helper\r\n */\n\nclass SpringEaser {\n  /**\r\n   * @param {number} initialVelocity Initial velocity, px per ms.\r\n   *\r\n   * @param {number} [dampingRatio]\r\n   * Determines how bouncy animation will be.\r\n   * From 0 to 1, 0 - always overshoot, 1 - do not overshoot.\r\n   * \"overshoot\" refers to part of animation that\r\n   * goes beyond the final value.\r\n   *\r\n   * @param {number} [naturalFrequency]\r\n   * Determines how fast animation will slow down.\r\n   * The higher value - the stiffer the transition will be,\r\n   * and the faster it will slow down.\r\n   * Recommended value from 10 to 50\r\n   */\n  constructor(initialVelocity, dampingRatio, naturalFrequency) {\n    this.velocity = initialVelocity * 1000; // convert to \"pixels per second\"\n    // https://en.wikipedia.org/wiki/Damping_ratio\n\n    this._dampingRatio = dampingRatio || DEFAULT_DAMPING_RATIO; // https://en.wikipedia.org/wiki/Natural_frequency\n\n    this._naturalFrequency = naturalFrequency || DEFAULT_NATURAL_FREQUENCY;\n    this._dampedFrequency = this._naturalFrequency;\n\n    if (this._dampingRatio < 1) {\n      this._dampedFrequency *= Math.sqrt(1 - this._dampingRatio * this._dampingRatio);\n    }\n  }\n  /**\r\n   * @param {number} deltaPosition Difference between current and end position of the animation\r\n   * @param {number} deltaTime Frame duration in milliseconds\r\n   *\r\n   * @returns {number} Displacement, relative to the end position.\r\n   */\n\n\n  easeFrame(deltaPosition, deltaTime) {\n    // Inspired by Apple Webkit and Android spring function implementation\n    // https://en.wikipedia.org/wiki/Oscillation\n    // https://en.wikipedia.org/wiki/Damping_ratio\n    // we ignore mass (assume that it's 1kg)\n    let displacement = 0;\n    let coeff;\n    deltaTime /= 1000;\n    const naturalDumpingPow = Math.E ** (-this._dampingRatio * this._naturalFrequency * deltaTime);\n\n    if (this._dampingRatio === 1) {\n      coeff = this.velocity + this._naturalFrequency * deltaPosition;\n      displacement = (deltaPosition + coeff * deltaTime) * naturalDumpingPow;\n      this.velocity = displacement * -this._naturalFrequency + coeff * naturalDumpingPow;\n    } else if (this._dampingRatio < 1) {\n      coeff = 1 / this._dampedFrequency * (this._dampingRatio * this._naturalFrequency * deltaPosition + this.velocity);\n      const dumpedFCos = Math.cos(this._dampedFrequency * deltaTime);\n      const dumpedFSin = Math.sin(this._dampedFrequency * deltaTime);\n      displacement = naturalDumpingPow * (deltaPosition * dumpedFCos + coeff * dumpedFSin);\n      this.velocity = displacement * -this._naturalFrequency * this._dampingRatio + naturalDumpingPow * (-this._dampedFrequency * deltaPosition * dumpedFSin + this._dampedFrequency * coeff * dumpedFCos);\n    } // Overdamped (>1) damping ratio is not supported\n\n\n    return displacement;\n  }\n\n}\n\n/** @typedef {import('./animations.js').SharedAnimationProps} SharedAnimationProps */\n\n/**\r\n * @typedef {Object} DefaultSpringAnimationProps\r\n *\r\n * @prop {number} start\r\n * @prop {number} end\r\n * @prop {number} velocity\r\n * @prop {number} [dampingRatio]\r\n * @prop {number} [naturalFrequency]\r\n * @prop {(end: number) => void} onUpdate\r\n */\n\n/** @typedef {SharedAnimationProps & DefaultSpringAnimationProps} SpringAnimationProps */\n\nclass SpringAnimation {\n  /**\r\n   * @param {SpringAnimationProps} props\r\n   */\n  constructor(props) {\n    this.props = props;\n    this._raf = 0;\n    const {\n      start,\n      end,\n      velocity,\n      onUpdate,\n      onComplete,\n      onFinish = () => {},\n      dampingRatio,\n      naturalFrequency\n    } = props;\n    this.onFinish = onFinish;\n    const easer = new SpringEaser(velocity, dampingRatio, naturalFrequency);\n    let prevTime = Date.now();\n    let deltaPosition = start - end;\n\n    const animationLoop = () => {\n      if (this._raf) {\n        deltaPosition = easer.easeFrame(deltaPosition, Date.now() - prevTime); // Stop the animation if velocity is low and position is close to end\n\n        if (Math.abs(deltaPosition) < 1 && Math.abs(easer.velocity) < 50) {\n          // Finalize the animation\n          onUpdate(end);\n\n          if (onComplete) {\n            onComplete();\n          }\n\n          this.onFinish();\n        } else {\n          prevTime = Date.now();\n          onUpdate(deltaPosition + end);\n          this._raf = requestAnimationFrame(animationLoop);\n        }\n      }\n    };\n\n    this._raf = requestAnimationFrame(animationLoop);\n  } // Destroy is called automatically onFinish\n\n\n  destroy() {\n    if (this._raf >= 0) {\n      cancelAnimationFrame(this._raf);\n    }\n\n    this._raf = 0;\n  }\n\n}\n\n/** @typedef {import('./css-animation.js').CssAnimationProps} CssAnimationProps */\n\n/** @typedef {import('./spring-animation.js').SpringAnimationProps} SpringAnimationProps */\n\n/** @typedef {Object} SharedAnimationProps\r\n * @prop {string} [name]\r\n * @prop {boolean} [isPan]\r\n * @prop {boolean} [isMainScroll]\r\n * @prop {VoidFunction} [onComplete]\r\n * @prop {VoidFunction} [onFinish]\r\n */\n\n/** @typedef {SpringAnimation | CSSAnimation} Animation */\n\n/** @typedef {SpringAnimationProps | CssAnimationProps} AnimationProps */\n\n/**\r\n * Manages animations\r\n */\n\nclass Animations {\n  constructor() {\n    /** @type {Animation[]} */\n    this.activeAnimations = [];\n  }\n  /**\r\n   * @param {SpringAnimationProps} props\r\n   */\n\n\n  startSpring(props) {\n    this._start(props, true);\n  }\n  /**\r\n   * @param {CssAnimationProps} props\r\n   */\n\n\n  startTransition(props) {\n    this._start(props);\n  }\n  /**\r\n   * @private\r\n   * @param {AnimationProps} props\r\n   * @param {boolean} [isSpring]\r\n   * @returns {Animation}\r\n   */\n\n\n  _start(props, isSpring) {\n    const animation = isSpring ? new SpringAnimation(\n    /** @type SpringAnimationProps */\n    props) : new CSSAnimation(\n    /** @type CssAnimationProps */\n    props);\n    this.activeAnimations.push(animation);\n\n    animation.onFinish = () => this.stop(animation);\n\n    return animation;\n  }\n  /**\r\n   * @param {Animation} animation\r\n   */\n\n\n  stop(animation) {\n    animation.destroy();\n    const index = this.activeAnimations.indexOf(animation);\n\n    if (index > -1) {\n      this.activeAnimations.splice(index, 1);\n    }\n  }\n\n  stopAll() {\n    // _stopAllAnimations\n    this.activeAnimations.forEach(animation => {\n      animation.destroy();\n    });\n    this.activeAnimations = [];\n  }\n  /**\r\n   * Stop all pan or zoom transitions\r\n   */\n\n\n  stopAllPan() {\n    this.activeAnimations = this.activeAnimations.filter(animation => {\n      if (animation.props.isPan) {\n        animation.destroy();\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  stopMainScroll() {\n    this.activeAnimations = this.activeAnimations.filter(animation => {\n      if (animation.props.isMainScroll) {\n        animation.destroy();\n        return false;\n      }\n\n      return true;\n    });\n  }\n  /**\r\n   * Returns true if main scroll transition is running\r\n   */\n  // isMainScrollRunning() {\n  //   return this.activeAnimations.some((animation) => {\n  //     return animation.props.isMainScroll;\n  //   });\n  // }\n\n  /**\r\n   * Returns true if any pan or zoom transition is running\r\n   */\n\n\n  isPanRunning() {\n    return this.activeAnimations.some(animation => {\n      return animation.props.isPan;\n    });\n  }\n\n}\n\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\n\n/**\r\n * Handles scroll wheel.\r\n * Can pan and zoom current slide image.\r\n */\nclass ScrollWheel {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp;\n    pswp.events.add(pswp.element, 'wheel',\n    /** @type EventListener */\n    this._onWheel.bind(this));\n  }\n  /**\r\n   * @private\r\n   * @param {WheelEvent} e\r\n   */\n\n\n  _onWheel(e) {\n    e.preventDefault();\n    const {\n      currSlide\n    } = this.pswp;\n    let {\n      deltaX,\n      deltaY\n    } = e;\n\n    if (!currSlide) {\n      return;\n    }\n\n    if (this.pswp.dispatch('wheel', {\n      originalEvent: e\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (e.ctrlKey || this.pswp.options.wheelToZoom) {\n      // zoom\n      if (currSlide.isZoomable()) {\n        let zoomFactor = -deltaY;\n\n        if (e.deltaMode === 1\n        /* DOM_DELTA_LINE */\n        ) {\n          zoomFactor *= 0.05;\n        } else {\n          zoomFactor *= e.deltaMode ? 1 : 0.002;\n        }\n\n        zoomFactor = 2 ** zoomFactor;\n        const destZoomLevel = currSlide.currZoomLevel * zoomFactor;\n        currSlide.zoomTo(destZoomLevel, {\n          x: e.clientX,\n          y: e.clientY\n        });\n      }\n    } else {\n      // pan\n      if (currSlide.isPannable()) {\n        if (e.deltaMode === 1\n        /* DOM_DELTA_LINE */\n        ) {\n          // 18 - average line height\n          deltaX *= 18;\n          deltaY *= 18;\n        }\n\n        currSlide.panTo(currSlide.pan.x - deltaX, currSlide.pan.y - deltaY);\n      }\n    }\n  }\n\n}\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/**\r\n * @template T\r\n * @typedef {import('../types.js').Methods<T>} Methods<T>\r\n */\n\n/**\r\n * @typedef {Object} UIElementMarkupProps\r\n * @prop {boolean} [isCustomSVG]\r\n * @prop {string} inner\r\n * @prop {string} [outlineID]\r\n * @prop {number | string} [size]\r\n */\n\n/**\r\n * @typedef {Object} UIElementData\r\n * @prop {DefaultUIElements | string} [name]\r\n * @prop {string} [className]\r\n * @prop {UIElementMarkup} [html]\r\n * @prop {boolean} [isButton]\r\n * @prop {keyof HTMLElementTagNameMap} [tagName]\r\n * @prop {string} [title]\r\n * @prop {string} [ariaLabel]\r\n * @prop {(element: HTMLElement, pswp: PhotoSwipe) => void} [onInit]\r\n * @prop {Methods<PhotoSwipe> | ((e: MouseEvent, element: HTMLElement, pswp: PhotoSwipe) => void)} [onClick]\r\n * @prop {'bar' | 'wrapper' | 'root'} [appendTo]\r\n * @prop {number} [order]\r\n */\n\n/** @typedef {'arrowPrev' | 'arrowNext' | 'close' | 'zoom' | 'counter'} DefaultUIElements */\n\n/** @typedef {string | UIElementMarkupProps} UIElementMarkup */\n\n/**\r\n * @param {UIElementMarkup} [htmlData]\r\n * @returns {string}\r\n */\n\nfunction addElementHTML(htmlData) {\n  if (typeof htmlData === 'string') {\n    // Allow developers to provide full svg,\n    // For example:\n    // <svg viewBox=\"0 0 32 32\" width=\"32\" height=\"32\" aria-hidden=\"true\" class=\"pswp__icn\">\n    //   <path d=\"...\" />\n    //   <circle ... />\n    // </svg>\n    // Can also be any HTML string.\n    return htmlData;\n  }\n\n  if (!htmlData || !htmlData.isCustomSVG) {\n    return '';\n  }\n\n  const svgData = htmlData;\n  let out = '<svg aria-hidden=\"true\" class=\"pswp__icn\" viewBox=\"0 0 %d %d\" width=\"%d\" height=\"%d\">'; // replace all %d with size\n\n  out = out.split('%d').join(\n  /** @type {string} */\n  svgData.size || 32); // Icons may contain outline/shadow,\n  // to make it we \"clone\" base icon shape and add border to it.\n  // Icon itself and border are styled via CSS.\n  //\n  // Property shadowID defines ID of element that should be cloned.\n\n  if (svgData.outlineID) {\n    out += '<use class=\"pswp__icn-shadow\" xlink:href=\"#' + svgData.outlineID + '\"/>';\n  }\n\n  out += svgData.inner;\n  out += '</svg>';\n  return out;\n}\n\nclass UIElement {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   * @param {UIElementData} data\r\n   */\n  constructor(pswp, data) {\n    var _container;\n\n    const name = data.name || data.className;\n    let elementHTML = data.html; // @ts-expect-error lookup only by `data.name` maybe?\n\n    if (pswp.options[name] === false) {\n      // exit if element is disabled from options\n      return;\n    } // Allow to override SVG icons from options\n    // @ts-expect-error lookup only by `data.name` maybe?\n\n\n    if (typeof pswp.options[name + 'SVG'] === 'string') {\n      // arrowPrevSVG\n      // arrowNextSVG\n      // closeSVG\n      // zoomSVG\n      // @ts-expect-error lookup only by `data.name` maybe?\n      elementHTML = pswp.options[name + 'SVG'];\n    }\n\n    pswp.dispatch('uiElementCreate', {\n      data\n    });\n    let className = '';\n\n    if (data.isButton) {\n      className += 'pswp__button ';\n      className += data.className || `pswp__button--${data.name}`;\n    } else {\n      className += data.className || `pswp__${data.name}`;\n    }\n\n    let tagName = data.isButton ? data.tagName || 'button' : data.tagName || 'div';\n    tagName =\n    /** @type {keyof HTMLElementTagNameMap} */\n    tagName.toLowerCase();\n    /** @type {HTMLElement} */\n\n    const element = createElement(className, tagName);\n\n    if (data.isButton) {\n      if (tagName === 'button') {\n        /** @type {HTMLButtonElement} */\n        element.type = 'button';\n      }\n\n      let {\n        title\n      } = data;\n      const {\n        ariaLabel\n      } = data; // @ts-expect-error lookup only by `data.name` maybe?\n\n      if (typeof pswp.options[name + 'Title'] === 'string') {\n        // @ts-expect-error lookup only by `data.name` maybe?\n        title = pswp.options[name + 'Title'];\n      }\n\n      if (title) {\n        element.title = title;\n      }\n\n      const ariaText = ariaLabel || title;\n\n      if (ariaText) {\n        element.setAttribute('aria-label', ariaText);\n      }\n    }\n\n    element.innerHTML = addElementHTML(elementHTML);\n\n    if (data.onInit) {\n      data.onInit(element, pswp);\n    }\n\n    if (data.onClick) {\n      element.onclick = e => {\n        if (typeof data.onClick === 'string') {\n          // @ts-ignore\n          pswp[data.onClick]();\n        } else if (typeof data.onClick === 'function') {\n          data.onClick(e, element, pswp);\n        }\n      };\n    } // Top bar is default position\n\n\n    const appendTo = data.appendTo || 'bar';\n    /** @type {HTMLElement | undefined} root element by default */\n\n    let container = pswp.element;\n\n    if (appendTo === 'bar') {\n      if (!pswp.topBar) {\n        pswp.topBar = createElement('pswp__top-bar pswp__hide-on-close', 'div', pswp.scrollWrap);\n      }\n\n      container = pswp.topBar;\n    } else {\n      // element outside of top bar gets a secondary class\n      // that makes element fade out on close\n      element.classList.add('pswp__hide-on-close');\n\n      if (appendTo === 'wrapper') {\n        container = pswp.scrollWrap;\n      }\n    }\n\n    (_container = container) === null || _container === void 0 || _container.appendChild(pswp.applyFilters('uiElement', element, data));\n  }\n\n}\n\n/*\r\n  Backward and forward arrow buttons\r\n */\n\n/** @typedef {import('./ui-element.js').UIElementData} UIElementData */\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/**\r\n *\r\n * @param {HTMLElement} element\r\n * @param {PhotoSwipe} pswp\r\n * @param {boolean} [isNextButton]\r\n */\nfunction initArrowButton(element, pswp, isNextButton) {\n  element.classList.add('pswp__button--arrow'); // TODO: this should point to a unique id for this instance\n\n  element.setAttribute('aria-controls', 'pswp__items');\n  pswp.on('change', () => {\n    if (!pswp.options.loop) {\n      if (isNextButton) {\n        /** @type {HTMLButtonElement} */\n        element.disabled = !(pswp.currIndex < pswp.getNumItems() - 1);\n      } else {\n        /** @type {HTMLButtonElement} */\n        element.disabled = !(pswp.currIndex > 0);\n      }\n    }\n  });\n}\n/** @type {UIElementData} */\n\n\nconst arrowPrev = {\n  name: 'arrowPrev',\n  className: 'pswp__button--arrow--prev',\n  title: 'Previous',\n  order: 10,\n  isButton: true,\n  appendTo: 'wrapper',\n  html: {\n    isCustomSVG: true,\n    size: 60,\n    inner: '<path d=\"M29 43l-3 3-16-16 16-16 3 3-13 13 13 13z\" id=\"pswp__icn-arrow\"/>',\n    outlineID: 'pswp__icn-arrow'\n  },\n  onClick: 'prev',\n  onInit: initArrowButton\n};\n/** @type {UIElementData} */\n\nconst arrowNext = {\n  name: 'arrowNext',\n  className: 'pswp__button--arrow--next',\n  title: 'Next',\n  order: 11,\n  isButton: true,\n  appendTo: 'wrapper',\n  html: {\n    isCustomSVG: true,\n    size: 60,\n    inner: '<use xlink:href=\"#pswp__icn-arrow\"/>',\n    outlineID: 'pswp__icn-arrow'\n  },\n  onClick: 'next',\n  onInit: (el, pswp) => {\n    initArrowButton(el, pswp, true);\n  }\n};\n\n/** @type {import('./ui-element.js').UIElementData} UIElementData */\nconst closeButton = {\n  name: 'close',\n  title: 'Close',\n  order: 20,\n  isButton: true,\n  html: {\n    isCustomSVG: true,\n    inner: '<path d=\"M24 10l-2-2-6 6-6-6-2 2 6 6-6 6 2 2 6-6 6 6 2-2-6-6z\" id=\"pswp__icn-close\"/>',\n    outlineID: 'pswp__icn-close'\n  },\n  onClick: 'close'\n};\n\n/** @type {import('./ui-element.js').UIElementData} UIElementData */\nconst zoomButton = {\n  name: 'zoom',\n  title: 'Zoom',\n  order: 10,\n  isButton: true,\n  html: {\n    isCustomSVG: true,\n    // eslint-disable-next-line max-len\n    inner: '<path d=\"M17.426 19.926a6 6 0 1 1 1.5-1.5L23 22.5 21.5 24l-4.074-4.074z\" id=\"pswp__icn-zoom\"/>' + '<path fill=\"currentColor\" class=\"pswp__zoom-icn-bar-h\" d=\"M11 16v-2h6v2z\"/>' + '<path fill=\"currentColor\" class=\"pswp__zoom-icn-bar-v\" d=\"M13 12h2v6h-2z\"/>',\n    outlineID: 'pswp__icn-zoom'\n  },\n  onClick: 'toggleZoom'\n};\n\n/** @type {import('./ui-element.js').UIElementData} UIElementData */\nconst loadingIndicator = {\n  name: 'preloader',\n  appendTo: 'bar',\n  order: 7,\n  html: {\n    isCustomSVG: true,\n    // eslint-disable-next-line max-len\n    inner: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M21.2 16a5.2 5.2 0 1 1-5.2-5.2V8a8 8 0 1 0 8 8h-2.8Z\" id=\"pswp__icn-loading\"/>',\n    outlineID: 'pswp__icn-loading'\n  },\n  onInit: (indicatorElement, pswp) => {\n    /** @type {boolean | undefined} */\n    let isVisible;\n    /** @type {NodeJS.Timeout | null} */\n\n    let delayTimeout = null;\n    /**\r\n     * @param {string} className\r\n     * @param {boolean} add\r\n     */\n\n    const toggleIndicatorClass = (className, add) => {\n      indicatorElement.classList.toggle('pswp__preloader--' + className, add);\n    };\n    /**\r\n     * @param {boolean} visible\r\n     */\n\n\n    const setIndicatorVisibility = visible => {\n      if (isVisible !== visible) {\n        isVisible = visible;\n        toggleIndicatorClass('active', visible);\n      }\n    };\n\n    const updatePreloaderVisibility = () => {\n      var _pswp$currSlide;\n\n      if (!((_pswp$currSlide = pswp.currSlide) !== null && _pswp$currSlide !== void 0 && _pswp$currSlide.content.isLoading())) {\n        setIndicatorVisibility(false);\n\n        if (delayTimeout) {\n          clearTimeout(delayTimeout);\n          delayTimeout = null;\n        }\n\n        return;\n      }\n\n      if (!delayTimeout) {\n        // display loading indicator with delay\n        delayTimeout = setTimeout(() => {\n          var _pswp$currSlide2;\n\n          setIndicatorVisibility(Boolean((_pswp$currSlide2 = pswp.currSlide) === null || _pswp$currSlide2 === void 0 ? void 0 : _pswp$currSlide2.content.isLoading()));\n          delayTimeout = null;\n        }, pswp.options.preloaderDelay);\n      }\n    };\n\n    pswp.on('change', updatePreloaderVisibility);\n    pswp.on('loadComplete', e => {\n      if (pswp.currSlide === e.slide) {\n        updatePreloaderVisibility();\n      }\n    }); // expose the method\n\n    if (pswp.ui) {\n      pswp.ui.updatePreloaderVisibility = updatePreloaderVisibility;\n    }\n  }\n};\n\n/** @type {import('./ui-element.js').UIElementData} UIElementData */\nconst counterIndicator = {\n  name: 'counter',\n  order: 5,\n  onInit: (counterElement, pswp) => {\n    pswp.on('change', () => {\n      counterElement.innerText = pswp.currIndex + 1 + pswp.options.indexIndicatorSep + pswp.getNumItems();\n    });\n  }\n};\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {import('./ui-element.js').UIElementData} UIElementData */\n\n/**\r\n * Set special class on element when image is zoomed.\r\n *\r\n * By default, it is used to adjust\r\n * zoom icon and zoom cursor via CSS.\r\n *\r\n * @param {HTMLElement} el\r\n * @param {boolean} isZoomedIn\r\n */\n\nfunction setZoomedIn(el, isZoomedIn) {\n  el.classList.toggle('pswp--zoomed-in', isZoomedIn);\n}\n\nclass UI {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp;\n    this.isRegistered = false;\n    /** @type {UIElementData[]} */\n\n    this.uiElementsData = [];\n    /** @type {(UIElement | UIElementData)[]} */\n\n    this.items = [];\n    /** @type {() => void} */\n\n    this.updatePreloaderVisibility = () => {};\n    /**\r\n     * @private\r\n     * @type {number | undefined}\r\n     */\n\n\n    this._lastUpdatedZoomLevel = undefined;\n  }\n\n  init() {\n    const {\n      pswp\n    } = this;\n    this.isRegistered = false;\n    this.uiElementsData = [closeButton, arrowPrev, arrowNext, zoomButton, loadingIndicator, counterIndicator];\n    pswp.dispatch('uiRegister'); // sort by order\n\n    this.uiElementsData.sort((a, b) => {\n      // default order is 0\n      return (a.order || 0) - (b.order || 0);\n    });\n    this.items = [];\n    this.isRegistered = true;\n    this.uiElementsData.forEach(uiElementData => {\n      this.registerElement(uiElementData);\n    });\n    pswp.on('change', () => {\n      var _pswp$element;\n\n      (_pswp$element = pswp.element) === null || _pswp$element === void 0 || _pswp$element.classList.toggle('pswp--one-slide', pswp.getNumItems() === 1);\n    });\n    pswp.on('zoomPanUpdate', () => this._onZoomPanUpdate());\n  }\n  /**\r\n   * @param {UIElementData} elementData\r\n   */\n\n\n  registerElement(elementData) {\n    if (this.isRegistered) {\n      this.items.push(new UIElement(this.pswp, elementData));\n    } else {\n      this.uiElementsData.push(elementData);\n    }\n  }\n  /**\r\n   * Fired each time zoom or pan position is changed.\r\n   * Update classes that control visibility of zoom button and cursor icon.\r\n   *\r\n   * @private\r\n   */\n\n\n  _onZoomPanUpdate() {\n    const {\n      template,\n      currSlide,\n      options\n    } = this.pswp;\n\n    if (this.pswp.opener.isClosing || !template || !currSlide) {\n      return;\n    }\n\n    let {\n      currZoomLevel\n    } = currSlide; // if not open yet - check against initial zoom level\n\n    if (!this.pswp.opener.isOpen) {\n      currZoomLevel = currSlide.zoomLevels.initial;\n    }\n\n    if (currZoomLevel === this._lastUpdatedZoomLevel) {\n      return;\n    }\n\n    this._lastUpdatedZoomLevel = currZoomLevel;\n    const currZoomLevelDiff = currSlide.zoomLevels.initial - currSlide.zoomLevels.secondary; // Initial and secondary zoom levels are almost equal\n\n    if (Math.abs(currZoomLevelDiff) < 0.01 || !currSlide.isZoomable()) {\n      // disable zoom\n      setZoomedIn(template, false);\n      template.classList.remove('pswp--zoom-allowed');\n      return;\n    }\n\n    template.classList.add('pswp--zoom-allowed');\n    const potentialZoomLevel = currZoomLevel === currSlide.zoomLevels.initial ? currSlide.zoomLevels.secondary : currSlide.zoomLevels.initial;\n    setZoomedIn(template, potentialZoomLevel <= currZoomLevel);\n\n    if (options.imageClickAction === 'zoom' || options.imageClickAction === 'zoom-or-close') {\n      template.classList.add('pswp--click-to-zoom');\n    }\n  }\n\n}\n\n/** @typedef {import('./slide.js').SlideData} SlideData */\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {{ x: number; y: number; w: number; innerRect?: { w: number; h: number; x: number; y: number } }} Bounds */\n\n/**\r\n * @param {HTMLElement} el\r\n * @returns Bounds\r\n */\nfunction getBoundsByElement(el) {\n  const thumbAreaRect = el.getBoundingClientRect();\n  return {\n    x: thumbAreaRect.left,\n    y: thumbAreaRect.top,\n    w: thumbAreaRect.width\n  };\n}\n/**\r\n * @param {HTMLElement} el\r\n * @param {number} imageWidth\r\n * @param {number} imageHeight\r\n * @returns Bounds\r\n */\n\n\nfunction getCroppedBoundsByElement(el, imageWidth, imageHeight) {\n  const thumbAreaRect = el.getBoundingClientRect(); // fill image into the area\n  // (do they same as object-fit:cover does to retrieve coordinates)\n\n  const hRatio = thumbAreaRect.width / imageWidth;\n  const vRatio = thumbAreaRect.height / imageHeight;\n  const fillZoomLevel = hRatio > vRatio ? hRatio : vRatio;\n  const offsetX = (thumbAreaRect.width - imageWidth * fillZoomLevel) / 2;\n  const offsetY = (thumbAreaRect.height - imageHeight * fillZoomLevel) / 2;\n  /**\r\n   * Coordinates of the image,\r\n   * as if it was not cropped,\r\n   * height is calculated automatically\r\n   *\r\n   * @type {Bounds}\r\n   */\n\n  const bounds = {\n    x: thumbAreaRect.left + offsetX,\n    y: thumbAreaRect.top + offsetY,\n    w: imageWidth * fillZoomLevel\n  }; // Coordinates of inner crop area\n  // relative to the image\n\n  bounds.innerRect = {\n    w: thumbAreaRect.width,\n    h: thumbAreaRect.height,\n    x: offsetX,\n    y: offsetY\n  };\n  return bounds;\n}\n/**\r\n * Get dimensions of thumbnail image\r\n * (click on which opens photoswipe or closes photoswipe to)\r\n *\r\n * @param {number} index\r\n * @param {SlideData} itemData\r\n * @param {PhotoSwipe} instance PhotoSwipe instance\r\n * @returns {Bounds | undefined}\r\n */\n\n\nfunction getThumbBounds(index, itemData, instance) {\n  // legacy event, before filters were introduced\n  const event = instance.dispatch('thumbBounds', {\n    index,\n    itemData,\n    instance\n  }); // @ts-expect-error\n\n  if (event.thumbBounds) {\n    // @ts-expect-error\n    return event.thumbBounds;\n  }\n\n  const {\n    element\n  } = itemData;\n  /** @type {Bounds | undefined} */\n\n  let thumbBounds;\n  /** @type {HTMLElement | null | undefined} */\n\n  let thumbnail;\n\n  if (element && instance.options.thumbSelector !== false) {\n    const thumbSelector = instance.options.thumbSelector || 'img';\n    thumbnail = element.matches(thumbSelector) ? element :\n    /** @type {HTMLElement | null} */\n    element.querySelector(thumbSelector);\n  }\n\n  thumbnail = instance.applyFilters('thumbEl', thumbnail, itemData, index);\n\n  if (thumbnail) {\n    if (!itemData.thumbCropped) {\n      thumbBounds = getBoundsByElement(thumbnail);\n    } else {\n      thumbBounds = getCroppedBoundsByElement(thumbnail, itemData.width || itemData.w || 0, itemData.height || itemData.h || 0);\n    }\n  }\n\n  return instance.applyFilters('thumbBounds', thumbBounds, itemData, index);\n}\n\n/** @typedef {import('../lightbox/lightbox.js').default} PhotoSwipeLightbox */\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\n\n/** @typedef {import('../photoswipe.js').DataSource} DataSource */\n\n/** @typedef {import('../ui/ui-element.js').UIElementData} UIElementData */\n\n/** @typedef {import('../slide/content.js').default} ContentDefault */\n\n/** @typedef {import('../slide/slide.js').default} Slide */\n\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\n\n/** @typedef {import('../slide/zoom-level.js').default} ZoomLevel */\n\n/** @typedef {import('../slide/get-thumb-bounds.js').Bounds} Bounds */\n\n/**\r\n * Allow adding an arbitrary props to the Content\r\n * https://photoswipe.com/custom-content/#using-webp-image-format\r\n * @typedef {ContentDefault & Record<string, any>} Content\r\n */\n\n/** @typedef {{ x?: number; y?: number }} Point */\n\n/**\r\n * @typedef {Object} PhotoSwipeEventsMap https://photoswipe.com/events/\r\n *\r\n *\r\n * https://photoswipe.com/adding-ui-elements/\r\n *\r\n * @prop {undefined} uiRegister\r\n * @prop {{ data: UIElementData }} uiElementCreate\r\n *\r\n *\r\n * https://photoswipe.com/events/#initialization-events\r\n *\r\n * @prop {undefined} beforeOpen\r\n * @prop {undefined} firstUpdate\r\n * @prop {undefined} initialLayout\r\n * @prop {undefined} change\r\n * @prop {undefined} afterInit\r\n * @prop {undefined} bindEvents\r\n *\r\n *\r\n * https://photoswipe.com/events/#opening-or-closing-transition-events\r\n *\r\n * @prop {undefined} openingAnimationStart\r\n * @prop {undefined} openingAnimationEnd\r\n * @prop {undefined} closingAnimationStart\r\n * @prop {undefined} closingAnimationEnd\r\n *\r\n *\r\n * https://photoswipe.com/events/#closing-events\r\n *\r\n * @prop {undefined} close\r\n * @prop {undefined} destroy\r\n *\r\n *\r\n * https://photoswipe.com/events/#pointer-and-gesture-events\r\n *\r\n * @prop {{ originalEvent: PointerEvent }} pointerDown\r\n * @prop {{ originalEvent: PointerEvent }} pointerMove\r\n * @prop {{ originalEvent: PointerEvent }} pointerUp\r\n * @prop {{ bgOpacity: number }} pinchClose can be default prevented\r\n * @prop {{ panY: number }} verticalDrag can be default prevented\r\n *\r\n *\r\n * https://photoswipe.com/events/#slide-content-events\r\n *\r\n * @prop {{ content: Content }} contentInit\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoad can be default prevented\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoadImage can be default prevented\r\n * @prop {{ content: Content; slide: Slide; isError?: boolean }} loadComplete\r\n * @prop {{ content: Content; slide: Slide }} loadError\r\n * @prop {{ content: Content; width: number; height: number }} contentResize can be default prevented\r\n * @prop {{ content: Content; width: number; height: number; slide: Slide }} imageSizeChange\r\n * @prop {{ content: Content }} contentLazyLoad can be default prevented\r\n * @prop {{ content: Content }} contentAppend can be default prevented\r\n * @prop {{ content: Content }} contentActivate can be default prevented\r\n * @prop {{ content: Content }} contentDeactivate can be default prevented\r\n * @prop {{ content: Content }} contentRemove can be default prevented\r\n * @prop {{ content: Content }} contentDestroy can be default prevented\r\n *\r\n *\r\n * undocumented\r\n *\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} imageClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} bgClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} tapAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} doubleTapAction can be default prevented\r\n *\r\n * @prop {{ originalEvent: KeyboardEvent }} keydown can be default prevented\r\n * @prop {{ x: number; dragging: boolean }} moveMainScroll\r\n * @prop {{ slide: Slide }} firstZoomPan\r\n * @prop {{ slide: Slide | undefined, data: SlideData, index: number }} gettingData\r\n * @prop {undefined} beforeResize\r\n * @prop {undefined} resize\r\n * @prop {undefined} viewportSize\r\n * @prop {undefined} updateScrollOffset\r\n * @prop {{ slide: Slide }} slideInit\r\n * @prop {{ slide: Slide }} afterSetContent\r\n * @prop {{ slide: Slide }} slideLoad\r\n * @prop {{ slide: Slide }} appendHeavy can be default prevented\r\n * @prop {{ slide: Slide }} appendHeavyContent\r\n * @prop {{ slide: Slide }} slideActivate\r\n * @prop {{ slide: Slide }} slideDeactivate\r\n * @prop {{ slide: Slide }} slideDestroy\r\n * @prop {{ destZoomLevel: number, centerPoint: Point | undefined, transitionDuration: number | false | undefined }} beforeZoomTo\r\n * @prop {{ slide: Slide }} zoomPanUpdate\r\n * @prop {{ slide: Slide }} initialZoomPan\r\n * @prop {{ slide: Slide }} calcSlideSize\r\n * @prop {undefined} resolutionChanged\r\n * @prop {{ originalEvent: WheelEvent }} wheel can be default prevented\r\n * @prop {{ content: Content }} contentAppendImage can be default prevented\r\n * @prop {{ index: number; itemData: SlideData }} lazyLoadSlide can be default prevented\r\n * @prop {undefined} lazyLoad\r\n * @prop {{ slide: Slide }} calcBounds\r\n * @prop {{ zoomLevels: ZoomLevel, slideData: SlideData }} zoomLevelsUpdate\r\n *\r\n *\r\n * legacy\r\n *\r\n * @prop {undefined} init\r\n * @prop {undefined} initialZoomIn\r\n * @prop {undefined} initialZoomOut\r\n * @prop {undefined} initialZoomInEnd\r\n * @prop {undefined} initialZoomOutEnd\r\n * @prop {{ dataSource: DataSource | undefined, numItems: number }} numItems\r\n * @prop {{ itemData: SlideData; index: number }} itemData\r\n * @prop {{ index: number, itemData: SlideData, instance: PhotoSwipe }} thumbBounds\r\n */\n\n/**\r\n * @typedef {Object} PhotoSwipeFiltersMap https://photoswipe.com/filters/\r\n *\r\n * @prop {(numItems: number, dataSource: DataSource | undefined) => number} numItems\r\n * Modify the total amount of slides. Example on Data sources page.\r\n * https://photoswipe.com/filters/#numitems\r\n *\r\n * @prop {(itemData: SlideData, index: number) => SlideData} itemData\r\n * Modify slide item data. Example on Data sources page.\r\n * https://photoswipe.com/filters/#itemdata\r\n *\r\n * @prop {(itemData: SlideData, element: HTMLElement, linkEl: HTMLAnchorElement) => SlideData} domItemData\r\n * Modify item data when it's parsed from DOM element. Example on Data sources page.\r\n * https://photoswipe.com/filters/#domitemdata\r\n *\r\n * @prop {(clickedIndex: number, e: MouseEvent, instance: PhotoSwipeLightbox) => number} clickedIndex\r\n * Modify clicked gallery item index.\r\n * https://photoswipe.com/filters/#clickedindex\r\n *\r\n * @prop {(placeholderSrc: string | false, content: Content) => string | false} placeholderSrc\r\n * Modify placeholder image source.\r\n * https://photoswipe.com/filters/#placeholdersrc\r\n *\r\n * @prop {(isContentLoading: boolean, content: Content) => boolean} isContentLoading\r\n * Modify if the content is currently loading.\r\n * https://photoswipe.com/filters/#iscontentloading\r\n *\r\n * @prop {(isContentZoomable: boolean, content: Content) => boolean} isContentZoomable\r\n * Modify if the content can be zoomed.\r\n * https://photoswipe.com/filters/#iscontentzoomable\r\n *\r\n * @prop {(useContentPlaceholder: boolean, content: Content) => boolean} useContentPlaceholder\r\n * Modify if the placeholder should be used for the content.\r\n * https://photoswipe.com/filters/#usecontentplaceholder\r\n *\r\n * @prop {(isKeepingPlaceholder: boolean, content: Content) => boolean} isKeepingPlaceholder\r\n * Modify if the placeholder should be kept after the content is loaded.\r\n * https://photoswipe.com/filters/#iskeepingplaceholder\r\n *\r\n *\r\n * @prop {(contentErrorElement: HTMLElement, content: Content) => HTMLElement} contentErrorElement\r\n * Modify an element when the content has error state (for example, if image cannot be loaded).\r\n * https://photoswipe.com/filters/#contenterrorelement\r\n *\r\n * @prop {(element: HTMLElement, data: UIElementData) => HTMLElement} uiElement\r\n * Modify a UI element that's being created.\r\n * https://photoswipe.com/filters/#uielement\r\n *\r\n * @prop {(thumbnail: HTMLElement | null | undefined, itemData: SlideData, index: number) => HTMLElement} thumbEl\r\n * Modify the thumbnail element from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbel\r\n *\r\n * @prop {(thumbBounds: Bounds | undefined, itemData: SlideData, index: number) => Bounds} thumbBounds\r\n * Modify the thumbnail bounds from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbbounds\r\n *\r\n * @prop {(srcsetSizesWidth: number, content: Content) => number} srcsetSizesWidth\r\n *\r\n * @prop {(preventPointerEvent: boolean, event: PointerEvent, pointerType: string) => boolean} preventPointerEvent\r\n *\r\n */\n\n/**\r\n * @template {keyof PhotoSwipeFiltersMap} T\r\n * @typedef {{ fn: PhotoSwipeFiltersMap[T], priority: number }} Filter\r\n */\n\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {PhotoSwipeEventsMap[T] extends undefined ? PhotoSwipeEvent<T> : PhotoSwipeEvent<T> & PhotoSwipeEventsMap[T]} AugmentedEvent\r\n */\n\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {(event: AugmentedEvent<T>) => void} EventCallback\r\n */\n\n/**\r\n * Base PhotoSwipe event object\r\n *\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n */\nclass PhotoSwipeEvent {\n  /**\r\n   * @param {T} type\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   */\n  constructor(type, details) {\n    this.type = type;\n    this.defaultPrevented = false;\n\n    if (details) {\n      Object.assign(this, details);\n    }\n  }\n\n  preventDefault() {\n    this.defaultPrevented = true;\n  }\n\n}\n/**\r\n * PhotoSwipe base class that can listen and dispatch for events.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox, extended by base.js\r\n */\n\n\nclass Eventable {\n  constructor() {\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeEventsMap]?: ((event: AugmentedEvent<T>) => void)[] }}\r\n     */\n    this._listeners = {};\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeFiltersMap]?: Filter<T>[] }}\r\n     */\n\n    this._filters = {};\n    /** @type {PhotoSwipe | undefined} */\n\n    this.pswp = undefined;\n    /** @type {PhotoSwipeOptions | undefined} */\n\n    this.options = undefined;\n  }\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   * @param {number} priority\r\n   */\n\n\n  addFilter(name, fn, priority = 100) {\n    var _this$_filters$name, _this$_filters$name2, _this$pswp;\n\n    if (!this._filters[name]) {\n      this._filters[name] = [];\n    }\n\n    (_this$_filters$name = this._filters[name]) === null || _this$_filters$name === void 0 || _this$_filters$name.push({\n      fn,\n      priority\n    });\n    (_this$_filters$name2 = this._filters[name]) === null || _this$_filters$name2 === void 0 || _this$_filters$name2.sort((f1, f2) => f1.priority - f2.priority);\n    (_this$pswp = this.pswp) === null || _this$pswp === void 0 || _this$pswp.addFilter(name, fn, priority);\n  }\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   */\n\n\n  removeFilter(name, fn) {\n    if (this._filters[name]) {\n      // @ts-expect-error\n      this._filters[name] = this._filters[name].filter(filter => filter.fn !== fn);\n    }\n\n    if (this.pswp) {\n      this.pswp.removeFilter(name, fn);\n    }\n  }\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {Parameters<PhotoSwipeFiltersMap[T]>} args\r\n   * @returns {Parameters<PhotoSwipeFiltersMap[T]>[0]}\r\n   */\n\n\n  applyFilters(name, ...args) {\n    var _this$_filters$name3;\n\n    (_this$_filters$name3 = this._filters[name]) === null || _this$_filters$name3 === void 0 || _this$_filters$name3.forEach(filter => {\n      // @ts-expect-error\n      args[0] = filter.fn.apply(this, args);\n    });\n    return args[0];\n  }\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\n\n\n  on(name, fn) {\n    var _this$_listeners$name, _this$pswp2;\n\n    if (!this._listeners[name]) {\n      this._listeners[name] = [];\n    }\n\n    (_this$_listeners$name = this._listeners[name]) === null || _this$_listeners$name === void 0 || _this$_listeners$name.push(fn); // When binding events to lightbox,\n    // also bind events to PhotoSwipe Core,\n    // if it's open.\n\n    (_this$pswp2 = this.pswp) === null || _this$pswp2 === void 0 || _this$pswp2.on(name, fn);\n  }\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\n\n\n  off(name, fn) {\n    var _this$pswp3;\n\n    if (this._listeners[name]) {\n      // @ts-expect-error\n      this._listeners[name] = this._listeners[name].filter(listener => fn !== listener);\n    }\n\n    (_this$pswp3 = this.pswp) === null || _this$pswp3 === void 0 || _this$pswp3.off(name, fn);\n  }\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   * @returns {AugmentedEvent<T>}\r\n   */\n\n\n  dispatch(name, details) {\n    var _this$_listeners$name2;\n\n    if (this.pswp) {\n      return this.pswp.dispatch(name, details);\n    }\n\n    const event =\n    /** @type {AugmentedEvent<T>} */\n    new PhotoSwipeEvent(name, details);\n    (_this$_listeners$name2 = this._listeners[name]) === null || _this$_listeners$name2 === void 0 || _this$_listeners$name2.forEach(listener => {\n      listener.call(this, event);\n    });\n    return event;\n  }\n\n}\n\nclass Placeholder {\n  /**\r\n   * @param {string | false} imageSrc\r\n   * @param {HTMLElement} container\r\n   */\n  constructor(imageSrc, container) {\n    // Create placeholder\n    // (stretched thumbnail or simple div behind the main image)\n\n    /** @type {HTMLImageElement | HTMLDivElement | null} */\n    this.element = createElement('pswp__img pswp__img--placeholder', imageSrc ? 'img' : 'div', container);\n\n    if (imageSrc) {\n      const imgEl =\n      /** @type {HTMLImageElement} */\n      this.element;\n      imgEl.decoding = 'async';\n      imgEl.alt = '';\n      imgEl.src = imageSrc;\n      imgEl.setAttribute('role', 'presentation');\n    }\n\n    this.element.setAttribute('aria-hidden', 'true');\n  }\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\n\n\n  setDisplayedSize(width, height) {\n    if (!this.element) {\n      return;\n    }\n\n    if (this.element.tagName === 'IMG') {\n      // Use transform scale() to modify img placeholder size\n      // (instead of changing width/height directly).\n      // This helps with performance, specifically in iOS15 Safari.\n      setWidthHeight(this.element, 250, 'auto');\n      this.element.style.transformOrigin = '0 0';\n      this.element.style.transform = toTransformString(0, 0, width / 250);\n    } else {\n      setWidthHeight(this.element, width, height);\n    }\n  }\n\n  destroy() {\n    var _this$element;\n\n    if ((_this$element = this.element) !== null && _this$element !== void 0 && _this$element.parentNode) {\n      this.element.remove();\n    }\n\n    this.element = null;\n  }\n\n}\n\n/** @typedef {import('./slide.js').default} Slide */\n\n/** @typedef {import('./slide.js').SlideData} SlideData */\n\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\n\n/** @typedef {import('../util/util.js').LoadState} LoadState */\n\nclass Content {\n  /**\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n   * @param {number} index\r\n   */\n  constructor(itemData, instance, index) {\n    this.instance = instance;\n    this.data = itemData;\n    this.index = index;\n    /** @type {HTMLImageElement | HTMLDivElement | undefined} */\n\n    this.element = undefined;\n    /** @type {Placeholder | undefined} */\n\n    this.placeholder = undefined;\n    /** @type {Slide | undefined} */\n\n    this.slide = undefined;\n    this.displayedImageWidth = 0;\n    this.displayedImageHeight = 0;\n    this.width = Number(this.data.w) || Number(this.data.width) || 0;\n    this.height = Number(this.data.h) || Number(this.data.height) || 0;\n    this.isAttached = false;\n    this.hasSlide = false;\n    this.isDecoding = false;\n    /** @type {LoadState} */\n\n    this.state = LOAD_STATE.IDLE;\n\n    if (this.data.type) {\n      this.type = this.data.type;\n    } else if (this.data.src) {\n      this.type = 'image';\n    } else {\n      this.type = 'html';\n    }\n\n    this.instance.dispatch('contentInit', {\n      content: this\n    });\n  }\n\n  removePlaceholder() {\n    if (this.placeholder && !this.keepPlaceholder()) {\n      // With delay, as image might be loaded, but not rendered\n      setTimeout(() => {\n        if (this.placeholder) {\n          this.placeholder.destroy();\n          this.placeholder = undefined;\n        }\n      }, 1000);\n    }\n  }\n  /**\r\n   * Preload content\r\n   *\r\n   * @param {boolean} isLazy\r\n   * @param {boolean} [reload]\r\n   */\n\n\n  load(isLazy, reload) {\n    if (this.slide && this.usePlaceholder()) {\n      if (!this.placeholder) {\n        const placeholderSrc = this.instance.applyFilters('placeholderSrc', // use  image-based placeholder only for the first slide,\n        // as rendering (even small stretched thumbnail) is an expensive operation\n        this.data.msrc && this.slide.isFirstSlide ? this.data.msrc : false, this);\n        this.placeholder = new Placeholder(placeholderSrc, this.slide.container);\n      } else {\n        const placeholderEl = this.placeholder.element; // Add placeholder to DOM if it was already created\n\n        if (placeholderEl && !placeholderEl.parentElement) {\n          this.slide.container.prepend(placeholderEl);\n        }\n      }\n    }\n\n    if (this.element && !reload) {\n      return;\n    }\n\n    if (this.instance.dispatch('contentLoad', {\n      content: this,\n      isLazy\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (this.isImageContent()) {\n      this.element = createElement('pswp__img', 'img'); // Start loading only after width is defined, as sizes might depend on it.\n      // Due to Safari feature, we must define sizes before srcset.\n\n      if (this.displayedImageWidth) {\n        this.loadImage(isLazy);\n      }\n    } else {\n      this.element = createElement('pswp__content', 'div');\n      this.element.innerHTML = this.data.html || '';\n    }\n\n    if (reload && this.slide) {\n      this.slide.updateContentSize(true);\n    }\n  }\n  /**\r\n   * Preload image\r\n   *\r\n   * @param {boolean} isLazy\r\n   */\n\n\n  loadImage(isLazy) {\n    var _this$data$src, _this$data$alt;\n\n    if (!this.isImageContent() || !this.element || this.instance.dispatch('contentLoadImage', {\n      content: this,\n      isLazy\n    }).defaultPrevented) {\n      return;\n    }\n\n    const imageElement =\n    /** @type HTMLImageElement */\n    this.element;\n    this.updateSrcsetSizes();\n\n    if (this.data.srcset) {\n      imageElement.srcset = this.data.srcset;\n    }\n\n    imageElement.src = (_this$data$src = this.data.src) !== null && _this$data$src !== void 0 ? _this$data$src : '';\n    imageElement.alt = (_this$data$alt = this.data.alt) !== null && _this$data$alt !== void 0 ? _this$data$alt : '';\n    this.state = LOAD_STATE.LOADING;\n\n    if (imageElement.complete) {\n      this.onLoaded();\n    } else {\n      imageElement.onload = () => {\n        this.onLoaded();\n      };\n\n      imageElement.onerror = () => {\n        this.onError();\n      };\n    }\n  }\n  /**\r\n   * Assign slide to content\r\n   *\r\n   * @param {Slide} slide\r\n   */\n\n\n  setSlide(slide) {\n    this.slide = slide;\n    this.hasSlide = true;\n    this.instance = slide.pswp; // todo: do we need to unset slide?\n  }\n  /**\r\n   * Content load success handler\r\n   */\n\n\n  onLoaded() {\n    this.state = LOAD_STATE.LOADED;\n\n    if (this.slide && this.element) {\n      this.instance.dispatch('loadComplete', {\n        slide: this.slide,\n        content: this\n      }); // if content is reloaded\n\n      if (this.slide.isActive && this.slide.heavyAppended && !this.element.parentNode) {\n        this.append();\n        this.slide.updateContentSize(true);\n      }\n\n      if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\n        this.removePlaceholder();\n      }\n    }\n  }\n  /**\r\n   * Content load error handler\r\n   */\n\n\n  onError() {\n    this.state = LOAD_STATE.ERROR;\n\n    if (this.slide) {\n      this.displayError();\n      this.instance.dispatch('loadComplete', {\n        slide: this.slide,\n        isError: true,\n        content: this\n      });\n      this.instance.dispatch('loadError', {\n        slide: this.slide,\n        content: this\n      });\n    }\n  }\n  /**\r\n   * @returns {Boolean} If the content is currently loading\r\n   */\n\n\n  isLoading() {\n    return this.instance.applyFilters('isContentLoading', this.state === LOAD_STATE.LOADING, this);\n  }\n  /**\r\n   * @returns {Boolean} If the content is in error state\r\n   */\n\n\n  isError() {\n    return this.state === LOAD_STATE.ERROR;\n  }\n  /**\r\n   * @returns {boolean} If the content is image\r\n   */\n\n\n  isImageContent() {\n    return this.type === 'image';\n  }\n  /**\r\n   * Update content size\r\n   *\r\n   * @param {Number} width\r\n   * @param {Number} height\r\n   */\n\n\n  setDisplayedSize(width, height) {\n    if (!this.element) {\n      return;\n    }\n\n    if (this.placeholder) {\n      this.placeholder.setDisplayedSize(width, height);\n    }\n\n    if (this.instance.dispatch('contentResize', {\n      content: this,\n      width,\n      height\n    }).defaultPrevented) {\n      return;\n    }\n\n    setWidthHeight(this.element, width, height);\n\n    if (this.isImageContent() && !this.isError()) {\n      const isInitialSizeUpdate = !this.displayedImageWidth && width;\n      this.displayedImageWidth = width;\n      this.displayedImageHeight = height;\n\n      if (isInitialSizeUpdate) {\n        this.loadImage(false);\n      } else {\n        this.updateSrcsetSizes();\n      }\n\n      if (this.slide) {\n        this.instance.dispatch('imageSizeChange', {\n          slide: this.slide,\n          width,\n          height,\n          content: this\n        });\n      }\n    }\n  }\n  /**\r\n   * @returns {boolean} If the content can be zoomed\r\n   */\n\n\n  isZoomable() {\n    return this.instance.applyFilters('isContentZoomable', this.isImageContent() && this.state !== LOAD_STATE.ERROR, this);\n  }\n  /**\r\n   * Update image srcset sizes attribute based on width and height\r\n   */\n\n\n  updateSrcsetSizes() {\n    // Handle srcset sizes attribute.\n    //\n    // Never lower quality, if it was increased previously.\n    // Chrome does this automatically, Firefox and Safari do not,\n    // so we store largest used size in dataset.\n    if (!this.isImageContent() || !this.element || !this.data.srcset) {\n      return;\n    }\n\n    const image =\n    /** @type HTMLImageElement */\n    this.element;\n    const sizesWidth = this.instance.applyFilters('srcsetSizesWidth', this.displayedImageWidth, this);\n\n    if (!image.dataset.largestUsedSize || sizesWidth > parseInt(image.dataset.largestUsedSize, 10)) {\n      image.sizes = sizesWidth + 'px';\n      image.dataset.largestUsedSize = String(sizesWidth);\n    }\n  }\n  /**\r\n   * @returns {boolean} If content should use a placeholder (from msrc by default)\r\n   */\n\n\n  usePlaceholder() {\n    return this.instance.applyFilters('useContentPlaceholder', this.isImageContent(), this);\n  }\n  /**\r\n   * Preload content with lazy-loading param\r\n   */\n\n\n  lazyLoad() {\n    if (this.instance.dispatch('contentLazyLoad', {\n      content: this\n    }).defaultPrevented) {\n      return;\n    }\n\n    this.load(true);\n  }\n  /**\r\n   * @returns {boolean} If placeholder should be kept after content is loaded\r\n   */\n\n\n  keepPlaceholder() {\n    return this.instance.applyFilters('isKeepingPlaceholder', this.isLoading(), this);\n  }\n  /**\r\n   * Destroy the content\r\n   */\n\n\n  destroy() {\n    this.hasSlide = false;\n    this.slide = undefined;\n\n    if (this.instance.dispatch('contentDestroy', {\n      content: this\n    }).defaultPrevented) {\n      return;\n    }\n\n    this.remove();\n\n    if (this.placeholder) {\n      this.placeholder.destroy();\n      this.placeholder = undefined;\n    }\n\n    if (this.isImageContent() && this.element) {\n      this.element.onload = null;\n      this.element.onerror = null;\n      this.element = undefined;\n    }\n  }\n  /**\r\n   * Display error message\r\n   */\n\n\n  displayError() {\n    if (this.slide) {\n      var _this$instance$option, _this$instance$option2;\n\n      let errorMsgEl = createElement('pswp__error-msg', 'div');\n      errorMsgEl.innerText = (_this$instance$option = (_this$instance$option2 = this.instance.options) === null || _this$instance$option2 === void 0 ? void 0 : _this$instance$option2.errorMsg) !== null && _this$instance$option !== void 0 ? _this$instance$option : '';\n      errorMsgEl =\n      /** @type {HTMLDivElement} */\n      this.instance.applyFilters('contentErrorElement', errorMsgEl, this);\n      this.element = createElement('pswp__content pswp__error-msg-container', 'div');\n      this.element.appendChild(errorMsgEl);\n      this.slide.container.innerText = '';\n      this.slide.container.appendChild(this.element);\n      this.slide.updateContentSize(true);\n      this.removePlaceholder();\n    }\n  }\n  /**\r\n   * Append the content\r\n   */\n\n\n  append() {\n    if (this.isAttached || !this.element) {\n      return;\n    }\n\n    this.isAttached = true;\n\n    if (this.state === LOAD_STATE.ERROR) {\n      this.displayError();\n      return;\n    }\n\n    if (this.instance.dispatch('contentAppend', {\n      content: this\n    }).defaultPrevented) {\n      return;\n    }\n\n    const supportsDecode = ('decode' in this.element);\n\n    if (this.isImageContent()) {\n      // Use decode() on nearby slides\n      //\n      // Nearby slide images are in DOM and not hidden via display:none.\n      // However, they are placed offscreen (to the left and right side).\n      //\n      // Some browsers do not composite the image until it's actually visible,\n      // using decode() helps.\n      //\n      // You might ask \"why dont you just decode() and then append all images\",\n      // that's because I want to show image before it's fully loaded,\n      // as browser can render parts of image while it is loading.\n      // We do not do this in Safari due to partial loading bug.\n      if (supportsDecode && this.slide && (!this.slide.isActive || isSafari())) {\n        this.isDecoding = true; // purposefully using finally instead of then,\n        // as if srcset sizes changes dynamically - it may cause decode error\n\n        /** @type {HTMLImageElement} */\n\n        this.element.decode().catch(() => {}).finally(() => {\n          this.isDecoding = false;\n          this.appendImage();\n        });\n      } else {\n        this.appendImage();\n      }\n    } else if (this.slide && !this.element.parentNode) {\n      this.slide.container.appendChild(this.element);\n    }\n  }\n  /**\r\n   * Activate the slide,\r\n   * active slide is generally the current one,\r\n   * meaning the user can see it.\r\n   */\n\n\n  activate() {\n    if (this.instance.dispatch('contentActivate', {\n      content: this\n    }).defaultPrevented || !this.slide) {\n      return;\n    }\n\n    if (this.isImageContent() && this.isDecoding && !isSafari()) {\n      // add image to slide when it becomes active,\n      // even if it's not finished decoding\n      this.appendImage();\n    } else if (this.isError()) {\n      this.load(false, true); // try to reload\n    }\n\n    if (this.slide.holderElement) {\n      this.slide.holderElement.setAttribute('aria-hidden', 'false');\n    }\n  }\n  /**\r\n   * Deactivate the content\r\n   */\n\n\n  deactivate() {\n    this.instance.dispatch('contentDeactivate', {\n      content: this\n    });\n\n    if (this.slide && this.slide.holderElement) {\n      this.slide.holderElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n  /**\r\n   * Remove the content from DOM\r\n   */\n\n\n  remove() {\n    this.isAttached = false;\n\n    if (this.instance.dispatch('contentRemove', {\n      content: this\n    }).defaultPrevented) {\n      return;\n    }\n\n    if (this.element && this.element.parentNode) {\n      this.element.remove();\n    }\n\n    if (this.placeholder && this.placeholder.element) {\n      this.placeholder.element.remove();\n    }\n  }\n  /**\r\n   * Append the image content to slide container\r\n   */\n\n\n  appendImage() {\n    if (!this.isAttached) {\n      return;\n    }\n\n    if (this.instance.dispatch('contentAppendImage', {\n      content: this\n    }).defaultPrevented) {\n      return;\n    } // ensure that element exists and is not already appended\n\n\n    if (this.slide && this.element && !this.element.parentNode) {\n      this.slide.container.appendChild(this.element);\n    }\n\n    if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\n      this.removePlaceholder();\n    }\n  }\n\n}\n\n/** @typedef {import('./content.js').default} Content */\n\n/** @typedef {import('./slide.js').default} Slide */\n\n/** @typedef {import('./slide.js').SlideData} SlideData */\n\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\n\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\n\nconst MIN_SLIDES_TO_CACHE = 5;\n/**\r\n * Lazy-load an image\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n * @param {number} index\r\n * @returns {Content} Image that is being decoded or false.\r\n */\n\nfunction lazyLoadData(itemData, instance, index) {\n  const content = instance.createContentFromData(itemData, index);\n  /** @type {ZoomLevel | undefined} */\n\n  let zoomLevel;\n  const {\n    options\n  } = instance; // We need to know dimensions of the image to preload it,\n  // as it might use srcset, and we need to define sizes\n\n  if (options) {\n    zoomLevel = new ZoomLevel(options, itemData, -1);\n    let viewportSize;\n\n    if (instance.pswp) {\n      viewportSize = instance.pswp.viewportSize;\n    } else {\n      viewportSize = getViewportSize(options, instance);\n    }\n\n    const panAreaSize = getPanAreaSize(options, viewportSize, itemData, index);\n    zoomLevel.update(content.width, content.height, panAreaSize);\n  }\n\n  content.lazyLoad();\n\n  if (zoomLevel) {\n    content.setDisplayedSize(Math.ceil(content.width * zoomLevel.initial), Math.ceil(content.height * zoomLevel.initial));\n  }\n\n  return content;\n}\n/**\r\n * Lazy-loads specific slide.\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * By default, it loads image based on viewport size and initial zoom level.\r\n *\r\n * @param {number} index Slide index\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox eventable instance\r\n * @returns {Content | undefined}\r\n */\n\nfunction lazyLoadSlide(index, instance) {\n  const itemData = instance.getItemData(index);\n\n  if (instance.dispatch('lazyLoadSlide', {\n    index,\n    itemData\n  }).defaultPrevented) {\n    return;\n  }\n\n  return lazyLoadData(itemData, instance, index);\n}\n\nclass ContentLoader {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp; // Total amount of cached images\n\n    this.limit = Math.max(pswp.options.preload[0] + pswp.options.preload[1] + 1, MIN_SLIDES_TO_CACHE);\n    /** @type {Content[]} */\n\n    this._cachedItems = [];\n  }\n  /**\r\n   * Lazy load nearby slides based on `preload` option.\r\n   *\r\n   * @param {number} [diff] Difference between slide indexes that was changed recently, or 0.\r\n   */\n\n\n  updateLazy(diff) {\n    const {\n      pswp\n    } = this;\n\n    if (pswp.dispatch('lazyLoad').defaultPrevented) {\n      return;\n    }\n\n    const {\n      preload\n    } = pswp.options;\n    const isForward = diff === undefined ? true : diff >= 0;\n    let i; // preload[1] - num items to preload in forward direction\n\n    for (i = 0; i <= preload[1]; i++) {\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? i : -i));\n    } // preload[0] - num items to preload in backward direction\n\n\n    for (i = 1; i <= preload[0]; i++) {\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? -i : i));\n    }\n  }\n  /**\r\n   * @param {number} initialIndex\r\n   */\n\n\n  loadSlideByIndex(initialIndex) {\n    const index = this.pswp.getLoopedIndex(initialIndex); // try to get cached content\n\n    let content = this.getContentByIndex(index);\n\n    if (!content) {\n      // no cached content, so try to load from scratch:\n      content = lazyLoadSlide(index, this.pswp); // if content can be loaded, add it to cache:\n\n      if (content) {\n        this.addToCache(content);\n      }\n    }\n  }\n  /**\r\n   * @param {Slide} slide\r\n   * @returns {Content}\r\n   */\n\n\n  getContentBySlide(slide) {\n    let content = this.getContentByIndex(slide.index);\n\n    if (!content) {\n      // create content if not found in cache\n      content = this.pswp.createContentFromData(slide.data, slide.index);\n      this.addToCache(content);\n    } // assign slide to content\n\n\n    content.setSlide(slide);\n    return content;\n  }\n  /**\r\n   * @param {Content} content\r\n   */\n\n\n  addToCache(content) {\n    // move to the end of array\n    this.removeByIndex(content.index);\n\n    this._cachedItems.push(content);\n\n    if (this._cachedItems.length > this.limit) {\n      // Destroy the first content that's not attached\n      const indexToRemove = this._cachedItems.findIndex(item => {\n        return !item.isAttached && !item.hasSlide;\n      });\n\n      if (indexToRemove !== -1) {\n        const removedItem = this._cachedItems.splice(indexToRemove, 1)[0];\n\n        removedItem.destroy();\n      }\n    }\n  }\n  /**\r\n   * Removes an image from cache, does not destroy() it, just removes.\r\n   *\r\n   * @param {number} index\r\n   */\n\n\n  removeByIndex(index) {\n    const indexToRemove = this._cachedItems.findIndex(item => item.index === index);\n\n    if (indexToRemove !== -1) {\n      this._cachedItems.splice(indexToRemove, 1);\n    }\n  }\n  /**\r\n   * @param {number} index\r\n   * @returns {Content | undefined}\r\n   */\n\n\n  getContentByIndex(index) {\n    return this._cachedItems.find(content => content.index === index);\n  }\n\n  destroy() {\n    this._cachedItems.forEach(content => content.destroy());\n\n    this._cachedItems = [];\n  }\n\n}\n\n/** @typedef {import(\"../photoswipe.js\").default} PhotoSwipe */\n\n/** @typedef {import(\"../slide/slide.js\").SlideData} SlideData */\n\n/**\r\n * PhotoSwipe base class that can retrieve data about every slide.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox\r\n */\n\nclass PhotoSwipeBase extends Eventable {\n  /**\r\n   * Get total number of slides\r\n   *\r\n   * @returns {number}\r\n   */\n  getNumItems() {\n    var _this$options;\n\n    let numItems = 0;\n    const dataSource = (_this$options = this.options) === null || _this$options === void 0 ? void 0 : _this$options.dataSource;\n\n    if (dataSource && 'length' in dataSource) {\n      // may be an array or just object with length property\n      numItems = dataSource.length;\n    } else if (dataSource && 'gallery' in dataSource) {\n      // query DOM elements\n      if (!dataSource.items) {\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\n      }\n\n      if (dataSource.items) {\n        numItems = dataSource.items.length;\n      }\n    } // legacy event, before filters were introduced\n\n\n    const event = this.dispatch('numItems', {\n      dataSource,\n      numItems\n    });\n    return this.applyFilters('numItems', event.numItems, dataSource);\n  }\n  /**\r\n   * @param {SlideData} slideData\r\n   * @param {number} index\r\n   * @returns {Content}\r\n   */\n\n\n  createContentFromData(slideData, index) {\n    return new Content(slideData, this, index);\n  }\n  /**\r\n   * Get item data by index.\r\n   *\r\n   * \"item data\" should contain normalized information that PhotoSwipe needs to generate a slide.\r\n   * For example, it may contain properties like\r\n   * `src`, `srcset`, `w`, `h`, which will be used to generate a slide with image.\r\n   *\r\n   * @param {number} index\r\n   * @returns {SlideData}\r\n   */\n\n\n  getItemData(index) {\n    var _this$options2;\n\n    const dataSource = (_this$options2 = this.options) === null || _this$options2 === void 0 ? void 0 : _this$options2.dataSource;\n    /** @type {SlideData | HTMLElement} */\n\n    let dataSourceItem = {};\n\n    if (Array.isArray(dataSource)) {\n      // Datasource is an array of elements\n      dataSourceItem = dataSource[index];\n    } else if (dataSource && 'gallery' in dataSource) {\n      // dataSource has gallery property,\n      // thus it was created by Lightbox, based on\n      // gallery and children options\n      // query DOM elements\n      if (!dataSource.items) {\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\n      }\n\n      dataSourceItem = dataSource.items[index];\n    }\n\n    let itemData = dataSourceItem;\n\n    if (itemData instanceof Element) {\n      itemData = this._domElementToItemData(itemData);\n    } // Dispatching the itemData event,\n    // it's a legacy verion before filters were introduced\n\n\n    const event = this.dispatch('itemData', {\n      itemData: itemData || {},\n      index\n    });\n    return this.applyFilters('itemData', event.itemData, index);\n  }\n  /**\r\n   * Get array of gallery DOM elements,\r\n   * based on childSelector and gallery element.\r\n   *\r\n   * @param {HTMLElement} galleryElement\r\n   * @returns {HTMLElement[]}\r\n   */\n\n\n  _getGalleryDOMElements(galleryElement) {\n    var _this$options3, _this$options4;\n\n    if ((_this$options3 = this.options) !== null && _this$options3 !== void 0 && _this$options3.children || (_this$options4 = this.options) !== null && _this$options4 !== void 0 && _this$options4.childSelector) {\n      return getElementsFromOption(this.options.children, this.options.childSelector, galleryElement) || [];\n    }\n\n    return [galleryElement];\n  }\n  /**\r\n   * Converts DOM element to item data object.\r\n   *\r\n   * @param {HTMLElement} element DOM element\r\n   * @returns {SlideData}\r\n   */\n\n\n  _domElementToItemData(element) {\n    /** @type {SlideData} */\n    const itemData = {\n      element\n    };\n    const linkEl =\n    /** @type {HTMLAnchorElement} */\n    element.tagName === 'A' ? element : element.querySelector('a');\n\n    if (linkEl) {\n      // src comes from data-pswp-src attribute,\n      // if it's empty link href is used\n      itemData.src = linkEl.dataset.pswpSrc || linkEl.href;\n\n      if (linkEl.dataset.pswpSrcset) {\n        itemData.srcset = linkEl.dataset.pswpSrcset;\n      }\n\n      itemData.width = linkEl.dataset.pswpWidth ? parseInt(linkEl.dataset.pswpWidth, 10) : 0;\n      itemData.height = linkEl.dataset.pswpHeight ? parseInt(linkEl.dataset.pswpHeight, 10) : 0; // support legacy w & h properties\n\n      itemData.w = itemData.width;\n      itemData.h = itemData.height;\n\n      if (linkEl.dataset.pswpType) {\n        itemData.type = linkEl.dataset.pswpType;\n      }\n\n      const thumbnailEl = element.querySelector('img');\n\n      if (thumbnailEl) {\n        var _thumbnailEl$getAttri;\n\n        // msrc is URL to placeholder image that's displayed before large image is loaded\n        // by default it's displayed only for the first slide\n        itemData.msrc = thumbnailEl.currentSrc || thumbnailEl.src;\n        itemData.alt = (_thumbnailEl$getAttri = thumbnailEl.getAttribute('alt')) !== null && _thumbnailEl$getAttri !== void 0 ? _thumbnailEl$getAttri : '';\n      }\n\n      if (linkEl.dataset.pswpCropped || linkEl.dataset.cropped) {\n        itemData.thumbCropped = true;\n      }\n    }\n\n    return this.applyFilters('domItemData', itemData, element, linkEl);\n  }\n  /**\r\n   * Lazy-load by slide data\r\n   *\r\n   * @param {SlideData} itemData Data about the slide\r\n   * @param {number} index\r\n   * @returns {Content} Image that is being decoded or false.\r\n   */\n\n\n  lazyLoadData(itemData, index) {\n    return lazyLoadData(itemData, this, index);\n  }\n\n}\n\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\n\n/** @typedef {import('./slide/get-thumb-bounds.js').Bounds} Bounds */\n\n/** @typedef {import('./util/animations.js').AnimationProps} AnimationProps */\n// some browsers do not paint\n// elements which opacity is set to 0,\n// since we need to pre-render elements for the animation -\n// we set it to the minimum amount\n\nconst MIN_OPACITY = 0.003;\n/**\r\n * Manages opening and closing transitions of the PhotoSwipe.\r\n *\r\n * It can perform zoom, fade or no transition.\r\n */\n\nclass Opener {\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\n  constructor(pswp) {\n    this.pswp = pswp;\n    this.isClosed = true;\n    this.isOpen = false;\n    this.isClosing = false;\n    this.isOpening = false;\n    /**\r\n     * @private\r\n     * @type {number | false | undefined}\r\n     */\n\n    this._duration = undefined;\n    /** @private */\n\n    this._useAnimation = false;\n    /** @private */\n\n    this._croppedZoom = false;\n    /** @private */\n\n    this._animateRootOpacity = false;\n    /** @private */\n\n    this._animateBgOpacity = false;\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | HTMLImageElement | null | undefined }\r\n     */\n\n    this._placeholder = undefined;\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | undefined }\r\n     */\n\n    this._opacityElement = undefined;\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | undefined }\r\n     */\n\n    this._cropContainer1 = undefined;\n    /**\r\n     * @private\r\n     * @type { HTMLElement | null | undefined }\r\n     */\n\n    this._cropContainer2 = undefined;\n    /**\r\n     * @private\r\n     * @type {Bounds | undefined}\r\n     */\n\n    this._thumbBounds = undefined;\n    this._prepareOpen = this._prepareOpen.bind(this); // Override initial zoom and pan position\n\n    pswp.on('firstZoomPan', this._prepareOpen);\n  }\n\n  open() {\n    this._prepareOpen();\n\n    this._start();\n  }\n\n  close() {\n    if (this.isClosed || this.isClosing || this.isOpening) {\n      // if we close during opening animation\n      // for now do nothing,\n      // browsers aren't good at changing the direction of the CSS transition\n      return;\n    }\n\n    const slide = this.pswp.currSlide;\n    this.isOpen = false;\n    this.isOpening = false;\n    this.isClosing = true;\n    this._duration = this.pswp.options.hideAnimationDuration;\n\n    if (slide && slide.currZoomLevel * slide.width >= this.pswp.options.maxWidthToAnimate) {\n      this._duration = 0;\n    }\n\n    this._applyStartProps();\n\n    setTimeout(() => {\n      this._start();\n    }, this._croppedZoom ? 30 : 0);\n  }\n  /** @private */\n\n\n  _prepareOpen() {\n    this.pswp.off('firstZoomPan', this._prepareOpen);\n\n    if (!this.isOpening) {\n      const slide = this.pswp.currSlide;\n      this.isOpening = true;\n      this.isClosing = false;\n      this._duration = this.pswp.options.showAnimationDuration;\n\n      if (slide && slide.zoomLevels.initial * slide.width >= this.pswp.options.maxWidthToAnimate) {\n        this._duration = 0;\n      }\n\n      this._applyStartProps();\n    }\n  }\n  /** @private */\n\n\n  _applyStartProps() {\n    const {\n      pswp\n    } = this;\n    const slide = this.pswp.currSlide;\n    const {\n      options\n    } = pswp;\n\n    if (options.showHideAnimationType === 'fade') {\n      options.showHideOpacity = true;\n      this._thumbBounds = undefined;\n    } else if (options.showHideAnimationType === 'none') {\n      options.showHideOpacity = false;\n      this._duration = 0;\n      this._thumbBounds = undefined;\n    } else if (this.isOpening && pswp._initialThumbBounds) {\n      // Use initial bounds if defined\n      this._thumbBounds = pswp._initialThumbBounds;\n    } else {\n      this._thumbBounds = this.pswp.getThumbBounds();\n    }\n\n    this._placeholder = slide === null || slide === void 0 ? void 0 : slide.getPlaceholderElement();\n    pswp.animations.stopAll(); // Discard animations when duration is less than 50ms\n\n    this._useAnimation = Boolean(this._duration && this._duration > 50);\n    this._animateZoom = Boolean(this._thumbBounds) && (slide === null || slide === void 0 ? void 0 : slide.content.usePlaceholder()) && (!this.isClosing || !pswp.mainScroll.isShifted());\n\n    if (!this._animateZoom) {\n      this._animateRootOpacity = true;\n\n      if (this.isOpening && slide) {\n        slide.zoomAndPanToInitial();\n        slide.applyCurrentZoomPan();\n      }\n    } else {\n      var _options$showHideOpac;\n\n      this._animateRootOpacity = (_options$showHideOpac = options.showHideOpacity) !== null && _options$showHideOpac !== void 0 ? _options$showHideOpac : false;\n    }\n\n    this._animateBgOpacity = !this._animateRootOpacity && this.pswp.options.bgOpacity > MIN_OPACITY;\n    this._opacityElement = this._animateRootOpacity ? pswp.element : pswp.bg;\n\n    if (!this._useAnimation) {\n      this._duration = 0;\n      this._animateZoom = false;\n      this._animateBgOpacity = false;\n      this._animateRootOpacity = true;\n\n      if (this.isOpening) {\n        if (pswp.element) {\n          pswp.element.style.opacity = String(MIN_OPACITY);\n        }\n\n        pswp.applyBgOpacity(1);\n      }\n\n      return;\n    }\n\n    if (this._animateZoom && this._thumbBounds && this._thumbBounds.innerRect) {\n      var _this$pswp$currSlide;\n\n      // Properties are used when animation from cropped thumbnail\n      this._croppedZoom = true;\n      this._cropContainer1 = this.pswp.container;\n      this._cropContainer2 = (_this$pswp$currSlide = this.pswp.currSlide) === null || _this$pswp$currSlide === void 0 ? void 0 : _this$pswp$currSlide.holderElement;\n\n      if (pswp.container) {\n        pswp.container.style.overflow = 'hidden';\n        pswp.container.style.width = pswp.viewportSize.x + 'px';\n      }\n    } else {\n      this._croppedZoom = false;\n    }\n\n    if (this.isOpening) {\n      // Apply styles before opening transition\n      if (this._animateRootOpacity) {\n        if (pswp.element) {\n          pswp.element.style.opacity = String(MIN_OPACITY);\n        }\n\n        pswp.applyBgOpacity(1);\n      } else {\n        if (this._animateBgOpacity && pswp.bg) {\n          pswp.bg.style.opacity = String(MIN_OPACITY);\n        }\n\n        if (pswp.element) {\n          pswp.element.style.opacity = '1';\n        }\n      }\n\n      if (this._animateZoom) {\n        this._setClosedStateZoomPan();\n\n        if (this._placeholder) {\n          // tell browser that we plan to animate the placeholder\n          this._placeholder.style.willChange = 'transform'; // hide placeholder to allow hiding of\n          // elements that overlap it (such as icons over the thumbnail)\n\n          this._placeholder.style.opacity = String(MIN_OPACITY);\n        }\n      }\n    } else if (this.isClosing) {\n      // hide nearby slides to make sure that\n      // they are not painted during the transition\n      if (pswp.mainScroll.itemHolders[0]) {\n        pswp.mainScroll.itemHolders[0].el.style.display = 'none';\n      }\n\n      if (pswp.mainScroll.itemHolders[2]) {\n        pswp.mainScroll.itemHolders[2].el.style.display = 'none';\n      }\n\n      if (this._croppedZoom) {\n        if (pswp.mainScroll.x !== 0) {\n          // shift the main scroller to zero position\n          pswp.mainScroll.resetPosition();\n          pswp.mainScroll.resize();\n        }\n      }\n    }\n  }\n  /** @private */\n\n\n  _start() {\n    if (this.isOpening && this._useAnimation && this._placeholder && this._placeholder.tagName === 'IMG') {\n      // To ensure smooth animation\n      // we wait till the current slide image placeholder is decoded,\n      // but no longer than 250ms,\n      // and no shorter than 50ms\n      // (just using requestanimationframe is not enough in Firefox,\n      // for some reason)\n      new Promise(resolve => {\n        let decoded = false;\n        let isDelaying = true;\n        decodeImage(\n        /** @type {HTMLImageElement} */\n        this._placeholder).finally(() => {\n          decoded = true;\n\n          if (!isDelaying) {\n            resolve(true);\n          }\n        });\n        setTimeout(() => {\n          isDelaying = false;\n\n          if (decoded) {\n            resolve(true);\n          }\n        }, 50);\n        setTimeout(resolve, 250);\n      }).finally(() => this._initiate());\n    } else {\n      this._initiate();\n    }\n  }\n  /** @private */\n\n\n  _initiate() {\n    var _this$pswp$element, _this$pswp$element2;\n\n    (_this$pswp$element = this.pswp.element) === null || _this$pswp$element === void 0 || _this$pswp$element.style.setProperty('--pswp-transition-duration', this._duration + 'ms');\n    this.pswp.dispatch(this.isOpening ? 'openingAnimationStart' : 'closingAnimationStart'); // legacy event\n\n    this.pswp.dispatch(\n    /** @type {'initialZoomIn' | 'initialZoomOut'} */\n    'initialZoom' + (this.isOpening ? 'In' : 'Out'));\n    (_this$pswp$element2 = this.pswp.element) === null || _this$pswp$element2 === void 0 || _this$pswp$element2.classList.toggle('pswp--ui-visible', this.isOpening);\n\n    if (this.isOpening) {\n      if (this._placeholder) {\n        // unhide the placeholder\n        this._placeholder.style.opacity = '1';\n      }\n\n      this._animateToOpenState();\n    } else if (this.isClosing) {\n      this._animateToClosedState();\n    }\n\n    if (!this._useAnimation) {\n      this._onAnimationComplete();\n    }\n  }\n  /** @private */\n\n\n  _onAnimationComplete() {\n    const {\n      pswp\n    } = this;\n    this.isOpen = this.isOpening;\n    this.isClosed = this.isClosing;\n    this.isOpening = false;\n    this.isClosing = false;\n    pswp.dispatch(this.isOpen ? 'openingAnimationEnd' : 'closingAnimationEnd'); // legacy event\n\n    pswp.dispatch(\n    /** @type {'initialZoomInEnd' | 'initialZoomOutEnd'} */\n    'initialZoom' + (this.isOpen ? 'InEnd' : 'OutEnd'));\n\n    if (this.isClosed) {\n      pswp.destroy();\n    } else if (this.isOpen) {\n      var _pswp$currSlide;\n\n      if (this._animateZoom && pswp.container) {\n        pswp.container.style.overflow = 'visible';\n        pswp.container.style.width = '100%';\n      }\n\n      (_pswp$currSlide = pswp.currSlide) === null || _pswp$currSlide === void 0 || _pswp$currSlide.applyCurrentZoomPan();\n    }\n  }\n  /** @private */\n\n\n  _animateToOpenState() {\n    const {\n      pswp\n    } = this;\n\n    if (this._animateZoom) {\n      if (this._croppedZoom && this._cropContainer1 && this._cropContainer2) {\n        this._animateTo(this._cropContainer1, 'transform', 'translate3d(0,0,0)');\n\n        this._animateTo(this._cropContainer2, 'transform', 'none');\n      }\n\n      if (pswp.currSlide) {\n        pswp.currSlide.zoomAndPanToInitial();\n\n        this._animateTo(pswp.currSlide.container, 'transform', pswp.currSlide.getCurrentTransform());\n      }\n    }\n\n    if (this._animateBgOpacity && pswp.bg) {\n      this._animateTo(pswp.bg, 'opacity', String(pswp.options.bgOpacity));\n    }\n\n    if (this._animateRootOpacity && pswp.element) {\n      this._animateTo(pswp.element, 'opacity', '1');\n    }\n  }\n  /** @private */\n\n\n  _animateToClosedState() {\n    const {\n      pswp\n    } = this;\n\n    if (this._animateZoom) {\n      this._setClosedStateZoomPan(true);\n    } // do not animate opacity if it's already at 0\n\n\n    if (this._animateBgOpacity && pswp.bgOpacity > 0.01 && pswp.bg) {\n      this._animateTo(pswp.bg, 'opacity', '0');\n    }\n\n    if (this._animateRootOpacity && pswp.element) {\n      this._animateTo(pswp.element, 'opacity', '0');\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {boolean} [animate]\r\n   */\n\n\n  _setClosedStateZoomPan(animate) {\n    if (!this._thumbBounds) return;\n    const {\n      pswp\n    } = this;\n    const {\n      innerRect\n    } = this._thumbBounds;\n    const {\n      currSlide,\n      viewportSize\n    } = pswp;\n\n    if (this._croppedZoom && innerRect && this._cropContainer1 && this._cropContainer2) {\n      const containerOnePanX = -viewportSize.x + (this._thumbBounds.x - innerRect.x) + innerRect.w;\n      const containerOnePanY = -viewportSize.y + (this._thumbBounds.y - innerRect.y) + innerRect.h;\n      const containerTwoPanX = viewportSize.x - innerRect.w;\n      const containerTwoPanY = viewportSize.y - innerRect.h;\n\n      if (animate) {\n        this._animateTo(this._cropContainer1, 'transform', toTransformString(containerOnePanX, containerOnePanY));\n\n        this._animateTo(this._cropContainer2, 'transform', toTransformString(containerTwoPanX, containerTwoPanY));\n      } else {\n        setTransform(this._cropContainer1, containerOnePanX, containerOnePanY);\n        setTransform(this._cropContainer2, containerTwoPanX, containerTwoPanY);\n      }\n    }\n\n    if (currSlide) {\n      equalizePoints(currSlide.pan, innerRect || this._thumbBounds);\n      currSlide.currZoomLevel = this._thumbBounds.w / currSlide.width;\n\n      if (animate) {\n        this._animateTo(currSlide.container, 'transform', currSlide.getCurrentTransform());\n      } else {\n        currSlide.applyCurrentZoomPan();\n      }\n    }\n  }\n  /**\r\n   * @private\r\n   * @param {HTMLElement} target\r\n   * @param {'transform' | 'opacity'} prop\r\n   * @param {string} propValue\r\n   */\n\n\n  _animateTo(target, prop, propValue) {\n    if (!this._duration) {\n      target.style[prop] = propValue;\n      return;\n    }\n\n    const {\n      animations\n    } = this.pswp;\n    /** @type {AnimationProps} */\n\n    const animProps = {\n      duration: this._duration,\n      easing: this.pswp.options.easing,\n      onComplete: () => {\n        if (!animations.activeAnimations.length) {\n          this._onAnimationComplete();\n        }\n      },\n      target\n    };\n    animProps[prop] = propValue;\n    animations.startTransition(animProps);\n  }\n\n}\n\n/**\r\n * @template T\r\n * @typedef {import('./types.js').Type<T>} Type<T>\r\n */\n\n/** @typedef {import('./slide/slide.js').SlideData} SlideData */\n\n/** @typedef {import('./slide/zoom-level.js').ZoomLevelOption} ZoomLevelOption */\n\n/** @typedef {import('./ui/ui-element.js').UIElementData} UIElementData */\n\n/** @typedef {import('./main-scroll.js').ItemHolder} ItemHolder */\n\n/** @typedef {import('./core/eventable.js').PhotoSwipeEventsMap} PhotoSwipeEventsMap */\n\n/** @typedef {import('./core/eventable.js').PhotoSwipeFiltersMap} PhotoSwipeFiltersMap */\n\n/** @typedef {import('./slide/get-thumb-bounds').Bounds} Bounds */\n\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('./core/eventable.js').EventCallback<T>} EventCallback<T>\r\n */\n\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('./core/eventable.js').AugmentedEvent<T>} AugmentedEvent<T>\r\n */\n\n/** @typedef {{ x: number; y: number; id?: string | number }} Point */\n\n/** @typedef {{ top: number; bottom: number; left: number; right: number }} Padding */\n\n/** @typedef {SlideData[]} DataSourceArray */\n\n/** @typedef {{ gallery: HTMLElement; items?: HTMLElement[] }} DataSourceObject */\n\n/** @typedef {DataSourceArray | DataSourceObject} DataSource */\n\n/** @typedef {(point: Point, originalEvent: PointerEvent) => void} ActionFn */\n\n/** @typedef {'close' | 'next' | 'zoom' | 'zoom-or-close' | 'toggle-controls'} ActionType */\n\n/** @typedef {Type<PhotoSwipe> | { default: Type<PhotoSwipe> }} PhotoSwipeModule */\n\n/** @typedef {PhotoSwipeModule | Promise<PhotoSwipeModule> | (() => Promise<PhotoSwipeModule>)} PhotoSwipeModuleOption */\n\n/**\r\n * @typedef {string | NodeListOf<HTMLElement> | HTMLElement[] | HTMLElement} ElementProvider\r\n */\n\n/** @typedef {Partial<PreparedPhotoSwipeOptions>} PhotoSwipeOptions https://photoswipe.com/options/ */\n\n/**\r\n * @typedef {Object} PreparedPhotoSwipeOptions\r\n *\r\n * @prop {DataSource} [dataSource]\r\n * Pass an array of any items via dataSource option. Its length will determine amount of slides\r\n * (which may be modified further from numItems event).\r\n *\r\n * Each item should contain data that you need to generate slide\r\n * (for image slide it would be src (image URL), width (image width), height, srcset, alt).\r\n *\r\n * If these properties are not present in your initial array, you may \"pre-parse\" each item from itemData filter.\r\n *\r\n * @prop {number} bgOpacity\r\n * Background backdrop opacity, always define it via this option and not via CSS rgba color.\r\n *\r\n * @prop {number} spacing\r\n * Spacing between slides. Defined as ratio relative to the viewport width (0.1 = 10% of viewport).\r\n *\r\n * @prop {boolean} allowPanToNext\r\n * Allow swipe navigation to the next slide when the current slide is zoomed. Does not apply to mouse events.\r\n *\r\n * @prop {boolean} loop\r\n * If set to true you'll be able to swipe from the last to the first image.\r\n * Option is always false when there are less than 3 slides.\r\n *\r\n * @prop {boolean} [wheelToZoom]\r\n * By default PhotoSwipe zooms image with ctrl-wheel, if you enable this option - image will zoom just via wheel.\r\n *\r\n * @prop {boolean} pinchToClose\r\n * Pinch touch gesture to close the gallery.\r\n *\r\n * @prop {boolean} closeOnVerticalDrag\r\n * Vertical drag gesture to close the PhotoSwipe.\r\n *\r\n * @prop {Padding} [padding]\r\n * Slide area padding (in pixels).\r\n *\r\n * @prop {(viewportSize: Point, itemData: SlideData, index: number) => Padding} [paddingFn]\r\n * The option is checked frequently, so make sure it's performant. Overrides padding option if defined. For example:\r\n *\r\n * @prop {number | false} hideAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {number | false} showAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {number | false} zoomAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {string} easing\r\n * String, 'cubic-bezier(.4,0,.22,1)'. CSS easing function for open/close/zoom transitions.\r\n *\r\n * @prop {boolean} escKey\r\n * Esc key to close.\r\n *\r\n * @prop {boolean} arrowKeys\r\n * Left/right arrow keys for navigation.\r\n *\r\n * @prop {boolean} trapFocus\r\n * Trap focus within PhotoSwipe element while it's open.\r\n *\r\n * @prop {boolean} returnFocus\r\n * Restore focus the last active element after PhotoSwipe is closed.\r\n *\r\n * @prop {boolean} clickToCloseNonZoomable\r\n * If image is not zoomable (for example, smaller than viewport) it can be closed by clicking on it.\r\n *\r\n * @prop {ActionType | ActionFn | false} imageClickAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} bgClickAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} tapAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} doubleTapAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {number} preloaderDelay\r\n * Delay before the loading indicator will be displayed,\r\n * if image is loaded during it - the indicator will not be displayed at all. Can be zero.\r\n *\r\n * @prop {string} indexIndicatorSep\r\n * Used for slide count indicator (\"1 of 10 \").\r\n *\r\n * @prop {(options: PhotoSwipeOptions, pswp: PhotoSwipeBase) => Point} [getViewportSizeFn]\r\n * A function that should return slide viewport width and height, in format {x: 100, y: 100}.\r\n *\r\n * @prop {string} errorMsg\r\n * Message to display when the image wasn't able to load. If you need to display HTML - use contentErrorElement filter.\r\n *\r\n * @prop {[number, number]} preload\r\n * Lazy loading of nearby slides based on direction of movement. Should be an array with two integers,\r\n * first one - number of items to preload before the current image, second one - after the current image.\r\n * Two nearby images are always loaded.\r\n *\r\n * @prop {string} [mainClass]\r\n * Class that will be added to the root element of PhotoSwipe, may contain multiple separated by space.\r\n * Example on Styling page.\r\n *\r\n * @prop {HTMLElement} [appendToEl]\r\n * Element to which PhotoSwipe dialog will be appended when it opens.\r\n *\r\n * @prop {number} maxWidthToAnimate\r\n * Maximum width of image to animate, if initial rendered image width\r\n * is larger than this value - the opening/closing transition will be automatically disabled.\r\n *\r\n * @prop {string} [closeTitle]\r\n * Translating\r\n *\r\n * @prop {string} [zoomTitle]\r\n * Translating\r\n *\r\n * @prop {string} [arrowPrevTitle]\r\n * Translating\r\n *\r\n * @prop {string} [arrowNextTitle]\r\n * Translating\r\n *\r\n * @prop {'zoom' | 'fade' | 'none'} [showHideAnimationType]\r\n * To adjust opening or closing transition type use lightbox option `showHideAnimationType` (`String`).\r\n * It supports three values - `zoom` (default), `fade` (default if there is no thumbnail) and `none`.\r\n *\r\n * Animations are automatically disabled if user `(prefers-reduced-motion: reduce)`.\r\n *\r\n * @prop {number} index\r\n * Defines start slide index.\r\n *\r\n * @prop {(e: MouseEvent) => number} [getClickedIndexFn]\r\n *\r\n * @prop {boolean} [arrowPrev]\r\n * @prop {boolean} [arrowNext]\r\n * @prop {boolean} [zoom]\r\n * @prop {boolean} [close]\r\n * @prop {boolean} [counter]\r\n *\r\n * @prop {string} [arrowPrevSVG]\r\n * @prop {string} [arrowNextSVG]\r\n * @prop {string} [zoomSVG]\r\n * @prop {string} [closeSVG]\r\n * @prop {string} [counterSVG]\r\n *\r\n * @prop {string} [arrowPrevTitle]\r\n * @prop {string} [arrowNextTitle]\r\n * @prop {string} [zoomTitle]\r\n * @prop {string} [closeTitle]\r\n * @prop {string} [counterTitle]\r\n *\r\n * @prop {ZoomLevelOption} [initialZoomLevel]\r\n * @prop {ZoomLevelOption} [secondaryZoomLevel]\r\n * @prop {ZoomLevelOption} [maxZoomLevel]\r\n *\r\n * @prop {boolean} [mouseMovePan]\r\n * @prop {Point | null} [initialPointerPos]\r\n * @prop {boolean} [showHideOpacity]\r\n *\r\n * @prop {PhotoSwipeModuleOption} [pswpModule]\r\n * @prop {() => Promise<any>} [openPromise]\r\n * @prop {boolean} [preloadFirstSlide]\r\n * @prop {ElementProvider} [gallery]\r\n * @prop {string} [gallerySelector]\r\n * @prop {ElementProvider} [children]\r\n * @prop {string} [childSelector]\r\n * @prop {string | false} [thumbSelector]\r\n */\n\n/** @type {PreparedPhotoSwipeOptions} */\n\nconst defaultOptions = {\n  allowPanToNext: true,\n  spacing: 0.1,\n  loop: true,\n  pinchToClose: true,\n  closeOnVerticalDrag: true,\n  hideAnimationDuration: 333,\n  showAnimationDuration: 333,\n  zoomAnimationDuration: 333,\n  escKey: true,\n  arrowKeys: true,\n  trapFocus: true,\n  returnFocus: true,\n  maxWidthToAnimate: 4000,\n  clickToCloseNonZoomable: true,\n  imageClickAction: 'zoom-or-close',\n  bgClickAction: 'close',\n  tapAction: 'toggle-controls',\n  doubleTapAction: 'zoom',\n  indexIndicatorSep: ' / ',\n  preloaderDelay: 2000,\n  bgOpacity: 0.8,\n  index: 0,\n  errorMsg: 'The image cannot be loaded',\n  preload: [1, 2],\n  easing: 'cubic-bezier(.4,0,.22,1)'\n};\n/**\r\n * PhotoSwipe Core\r\n */\n\nclass PhotoSwipe extends PhotoSwipeBase {\n  /**\r\n   * @param {PhotoSwipeOptions} [options]\r\n   */\n  constructor(options) {\n    super();\n    this.options = this._prepareOptions(options || {});\n    /**\r\n     * offset of viewport relative to document\r\n     *\r\n     * @type {Point}\r\n     */\n\n    this.offset = {\n      x: 0,\n      y: 0\n    };\n    /**\r\n     * @type {Point}\r\n     * @private\r\n     */\n\n    this._prevViewportSize = {\n      x: 0,\n      y: 0\n    };\n    /**\r\n     * Size of scrollable PhotoSwipe viewport\r\n     *\r\n     * @type {Point}\r\n     */\n\n    this.viewportSize = {\n      x: 0,\n      y: 0\n    };\n    /**\r\n     * background (backdrop) opacity\r\n     */\n\n    this.bgOpacity = 1;\n    this.currIndex = 0;\n    this.potentialIndex = 0;\n    this.isOpen = false;\n    this.isDestroying = false;\n    this.hasMouse = false;\n    /**\r\n     * @private\r\n     * @type {SlideData}\r\n     */\n\n    this._initialItemData = {};\n    /** @type {Bounds | undefined} */\n\n    this._initialThumbBounds = undefined;\n    /** @type {HTMLDivElement | undefined} */\n\n    this.topBar = undefined;\n    /** @type {HTMLDivElement | undefined} */\n\n    this.element = undefined;\n    /** @type {HTMLDivElement | undefined} */\n\n    this.template = undefined;\n    /** @type {HTMLDivElement | undefined} */\n\n    this.container = undefined;\n    /** @type {HTMLElement | undefined} */\n\n    this.scrollWrap = undefined;\n    /** @type {Slide | undefined} */\n\n    this.currSlide = undefined;\n    this.events = new DOMEvents();\n    this.animations = new Animations();\n    this.mainScroll = new MainScroll(this);\n    this.gestures = new Gestures(this);\n    this.opener = new Opener(this);\n    this.keyboard = new Keyboard(this);\n    this.contentLoader = new ContentLoader(this);\n  }\n  /** @returns {boolean} */\n\n\n  init() {\n    if (this.isOpen || this.isDestroying) {\n      return false;\n    }\n\n    this.isOpen = true;\n    this.dispatch('init'); // legacy\n\n    this.dispatch('beforeOpen');\n\n    this._createMainStructure(); // add classes to the root element of PhotoSwipe\n\n\n    let rootClasses = 'pswp--open';\n\n    if (this.gestures.supportsTouch) {\n      rootClasses += ' pswp--touch';\n    }\n\n    if (this.options.mainClass) {\n      rootClasses += ' ' + this.options.mainClass;\n    }\n\n    if (this.element) {\n      this.element.className += ' ' + rootClasses;\n    }\n\n    this.currIndex = this.options.index || 0;\n    this.potentialIndex = this.currIndex;\n    this.dispatch('firstUpdate'); // starting index can be modified here\n    // initialize scroll wheel handler to block the scroll\n\n    this.scrollWheel = new ScrollWheel(this); // sanitize index\n\n    if (Number.isNaN(this.currIndex) || this.currIndex < 0 || this.currIndex >= this.getNumItems()) {\n      this.currIndex = 0;\n    }\n\n    if (!this.gestures.supportsTouch) {\n      // enable mouse features if no touch support detected\n      this.mouseDetected();\n    } // causes forced synchronous layout\n\n\n    this.updateSize();\n    this.offset.y = window.pageYOffset;\n    this._initialItemData = this.getItemData(this.currIndex);\n    this.dispatch('gettingData', {\n      index: this.currIndex,\n      data: this._initialItemData,\n      slide: undefined\n    }); // *Layout* - calculate size and position of elements here\n\n    this._initialThumbBounds = this.getThumbBounds();\n    this.dispatch('initialLayout');\n    this.on('openingAnimationEnd', () => {\n      const {\n        itemHolders\n      } = this.mainScroll; // Add content to the previous and next slide\n\n      if (itemHolders[0]) {\n        itemHolders[0].el.style.display = 'block';\n        this.setContent(itemHolders[0], this.currIndex - 1);\n      }\n\n      if (itemHolders[2]) {\n        itemHolders[2].el.style.display = 'block';\n        this.setContent(itemHolders[2], this.currIndex + 1);\n      }\n\n      this.appendHeavy();\n      this.contentLoader.updateLazy();\n      this.events.add(window, 'resize', this._handlePageResize.bind(this));\n      this.events.add(window, 'scroll', this._updatePageScrollOffset.bind(this));\n      this.dispatch('bindEvents');\n    }); // set content for center slide (first time)\n\n    if (this.mainScroll.itemHolders[1]) {\n      this.setContent(this.mainScroll.itemHolders[1], this.currIndex);\n    }\n\n    this.dispatch('change');\n    this.opener.open();\n    this.dispatch('afterInit');\n    return true;\n  }\n  /**\r\n   * Get looped slide index\r\n   * (for example, -1 will return the last slide)\r\n   *\r\n   * @param {number} index\r\n   * @returns {number}\r\n   */\n\n\n  getLoopedIndex(index) {\n    const numSlides = this.getNumItems();\n\n    if (this.options.loop) {\n      if (index > numSlides - 1) {\n        index -= numSlides;\n      }\n\n      if (index < 0) {\n        index += numSlides;\n      }\n    }\n\n    return clamp(index, 0, numSlides - 1);\n  }\n\n  appendHeavy() {\n    this.mainScroll.itemHolders.forEach(itemHolder => {\n      var _itemHolder$slide;\n\n      (_itemHolder$slide = itemHolder.slide) === null || _itemHolder$slide === void 0 || _itemHolder$slide.appendHeavy();\n    });\n  }\n  /**\r\n   * Change the slide\r\n   * @param {number} index New index\r\n   */\n\n\n  goTo(index) {\n    this.mainScroll.moveIndexBy(this.getLoopedIndex(index) - this.potentialIndex);\n  }\n  /**\r\n   * Go to the next slide.\r\n   */\n\n\n  next() {\n    this.goTo(this.potentialIndex + 1);\n  }\n  /**\r\n   * Go to the previous slide.\r\n   */\n\n\n  prev() {\n    this.goTo(this.potentialIndex - 1);\n  }\n  /**\r\n   * @see slide/slide.js zoomTo\r\n   *\r\n   * @param {Parameters<Slide['zoomTo']>} args\r\n   */\n\n\n  zoomTo(...args) {\n    var _this$currSlide;\n\n    (_this$currSlide = this.currSlide) === null || _this$currSlide === void 0 || _this$currSlide.zoomTo(...args);\n  }\n  /**\r\n   * @see slide/slide.js toggleZoom\r\n   */\n\n\n  toggleZoom() {\n    var _this$currSlide2;\n\n    (_this$currSlide2 = this.currSlide) === null || _this$currSlide2 === void 0 || _this$currSlide2.toggleZoom();\n  }\n  /**\r\n   * Close the gallery.\r\n   * After closing transition ends - destroy it\r\n   */\n\n\n  close() {\n    if (!this.opener.isOpen || this.isDestroying) {\n      return;\n    }\n\n    this.isDestroying = true;\n    this.dispatch('close');\n    this.events.removeAll();\n    this.opener.close();\n  }\n  /**\r\n   * Destroys the gallery:\r\n   * - instantly closes the gallery\r\n   * - unbinds events,\r\n   * - cleans intervals and timeouts\r\n   * - removes elements from DOM\r\n   */\n\n\n  destroy() {\n    var _this$element;\n\n    if (!this.isDestroying) {\n      this.options.showHideAnimationType = 'none';\n      this.close();\n      return;\n    }\n\n    this.dispatch('destroy');\n    this._listeners = {};\n\n    if (this.scrollWrap) {\n      this.scrollWrap.ontouchmove = null;\n      this.scrollWrap.ontouchend = null;\n    }\n\n    (_this$element = this.element) === null || _this$element === void 0 || _this$element.remove();\n    this.mainScroll.itemHolders.forEach(itemHolder => {\n      var _itemHolder$slide2;\n\n      (_itemHolder$slide2 = itemHolder.slide) === null || _itemHolder$slide2 === void 0 || _itemHolder$slide2.destroy();\n    });\n    this.contentLoader.destroy();\n    this.events.removeAll();\n  }\n  /**\r\n   * Refresh/reload content of a slide by its index\r\n   *\r\n   * @param {number} slideIndex\r\n   */\n\n\n  refreshSlideContent(slideIndex) {\n    this.contentLoader.removeByIndex(slideIndex);\n    this.mainScroll.itemHolders.forEach((itemHolder, i) => {\n      var _this$currSlide$index, _this$currSlide3;\n\n      let potentialHolderIndex = ((_this$currSlide$index = (_this$currSlide3 = this.currSlide) === null || _this$currSlide3 === void 0 ? void 0 : _this$currSlide3.index) !== null && _this$currSlide$index !== void 0 ? _this$currSlide$index : 0) - 1 + i;\n\n      if (this.canLoop()) {\n        potentialHolderIndex = this.getLoopedIndex(potentialHolderIndex);\n      }\n\n      if (potentialHolderIndex === slideIndex) {\n        // set the new slide content\n        this.setContent(itemHolder, slideIndex, true); // activate the new slide if it's current\n\n        if (i === 1) {\n          var _itemHolder$slide3;\n\n          this.currSlide = itemHolder.slide;\n          (_itemHolder$slide3 = itemHolder.slide) === null || _itemHolder$slide3 === void 0 || _itemHolder$slide3.setIsActive(true);\n        }\n      }\n    });\n    this.dispatch('change');\n  }\n  /**\r\n   * Set slide content\r\n   *\r\n   * @param {ItemHolder} holder mainScroll.itemHolders array item\r\n   * @param {number} index Slide index\r\n   * @param {boolean} [force] If content should be set even if index wasn't changed\r\n   */\n\n\n  setContent(holder, index, force) {\n    if (this.canLoop()) {\n      index = this.getLoopedIndex(index);\n    }\n\n    if (holder.slide) {\n      if (holder.slide.index === index && !force) {\n        // exit if holder already contains this slide\n        // this could be common when just three slides are used\n        return;\n      } // destroy previous slide\n\n\n      holder.slide.destroy();\n      holder.slide = undefined;\n    } // exit if no loop and index is out of bounds\n\n\n    if (!this.canLoop() && (index < 0 || index >= this.getNumItems())) {\n      return;\n    }\n\n    const itemData = this.getItemData(index);\n    holder.slide = new Slide(itemData, index, this); // set current slide\n\n    if (index === this.currIndex) {\n      this.currSlide = holder.slide;\n    }\n\n    holder.slide.append(holder.el);\n  }\n  /** @returns {Point} */\n\n\n  getViewportCenterPoint() {\n    return {\n      x: this.viewportSize.x / 2,\n      y: this.viewportSize.y / 2\n    };\n  }\n  /**\r\n   * Update size of all elements.\r\n   * Executed on init and on page resize.\r\n   *\r\n   * @param {boolean} [force] Update size even if size of viewport was not changed.\r\n   */\n\n\n  updateSize(force) {\n    // let item;\n    // let itemIndex;\n    if (this.isDestroying) {\n      // exit if PhotoSwipe is closed or closing\n      // (to avoid errors, as resize event might be delayed)\n      return;\n    } //const newWidth = this.scrollWrap.clientWidth;\n    //const newHeight = this.scrollWrap.clientHeight;\n\n\n    const newViewportSize = getViewportSize(this.options, this);\n\n    if (!force && pointsEqual(newViewportSize, this._prevViewportSize)) {\n      // Exit if dimensions were not changed\n      return;\n    } //this._prevViewportSize.x = newWidth;\n    //this._prevViewportSize.y = newHeight;\n\n\n    equalizePoints(this._prevViewportSize, newViewportSize);\n    this.dispatch('beforeResize');\n    equalizePoints(this.viewportSize, this._prevViewportSize);\n\n    this._updatePageScrollOffset();\n\n    this.dispatch('viewportSize'); // Resize slides only after opener animation is finished\n    // and don't re-calculate size on inital size update\n\n    this.mainScroll.resize(this.opener.isOpen);\n\n    if (!this.hasMouse && window.matchMedia('(any-hover: hover)').matches) {\n      this.mouseDetected();\n    }\n\n    this.dispatch('resize');\n  }\n  /**\r\n   * @param {number} opacity\r\n   */\n\n\n  applyBgOpacity(opacity) {\n    this.bgOpacity = Math.max(opacity, 0);\n\n    if (this.bg) {\n      this.bg.style.opacity = String(this.bgOpacity * this.options.bgOpacity);\n    }\n  }\n  /**\r\n   * Whether mouse is detected\r\n   */\n\n\n  mouseDetected() {\n    if (!this.hasMouse) {\n      var _this$element2;\n\n      this.hasMouse = true;\n      (_this$element2 = this.element) === null || _this$element2 === void 0 || _this$element2.classList.add('pswp--has_mouse');\n    }\n  }\n  /**\r\n   * Page resize event handler\r\n   *\r\n   * @private\r\n   */\n\n\n  _handlePageResize() {\n    this.updateSize(); // In iOS webview, if element size depends on document size,\n    // it'll be measured incorrectly in resize event\n    //\n    // https://bugs.webkit.org/show_bug.cgi?id=170595\n    // https://hackernoon.com/onresize-event-broken-in-mobile-safari-d8469027bf4d\n\n    if (/iPhone|iPad|iPod/i.test(window.navigator.userAgent)) {\n      setTimeout(() => {\n        this.updateSize();\n      }, 500);\n    }\n  }\n  /**\r\n   * Page scroll offset is used\r\n   * to get correct coordinates\r\n   * relative to PhotoSwipe viewport.\r\n   *\r\n   * @private\r\n   */\n\n\n  _updatePageScrollOffset() {\n    this.setScrollOffset(0, window.pageYOffset);\n  }\n  /**\r\n   * @param {number} x\r\n   * @param {number} y\r\n   */\n\n\n  setScrollOffset(x, y) {\n    this.offset.x = x;\n    this.offset.y = y;\n    this.dispatch('updateScrollOffset');\n  }\n  /**\r\n   * Create main HTML structure of PhotoSwipe,\r\n   * and add it to DOM\r\n   *\r\n   * @private\r\n   */\n\n\n  _createMainStructure() {\n    // root DOM element of PhotoSwipe (.pswp)\n    this.element = createElement('pswp', 'div');\n    this.element.setAttribute('tabindex', '-1');\n    this.element.setAttribute('role', 'dialog'); // template is legacy prop\n\n    this.template = this.element; // Background is added as a separate element,\n    // as animating opacity is faster than animating rgba()\n\n    this.bg = createElement('pswp__bg', 'div', this.element);\n    this.scrollWrap = createElement('pswp__scroll-wrap', 'section', this.element);\n    this.container = createElement('pswp__container', 'div', this.scrollWrap); // aria pattern: carousel\n\n    this.scrollWrap.setAttribute('aria-roledescription', 'carousel');\n    this.container.setAttribute('aria-live', 'off');\n    this.container.setAttribute('id', 'pswp__items');\n    this.mainScroll.appendHolders();\n    this.ui = new UI(this);\n    this.ui.init(); // append to DOM\n\n    (this.options.appendToEl || document.body).appendChild(this.element);\n  }\n  /**\r\n   * Get position and dimensions of small thumbnail\r\n   *   {x:,y:,w:}\r\n   *\r\n   * Height is optional (calculated based on the large image)\r\n   *\r\n   * @returns {Bounds | undefined}\r\n   */\n\n\n  getThumbBounds() {\n    return getThumbBounds(this.currIndex, this.currSlide ? this.currSlide.data : this._initialItemData, this);\n  }\n  /**\r\n   * If the PhotoSwipe can have continuous loop\r\n   * @returns Boolean\r\n   */\n\n\n  canLoop() {\n    return this.options.loop && this.getNumItems() > 2;\n  }\n  /**\r\n   * @private\r\n   * @param {PhotoSwipeOptions} options\r\n   * @returns {PreparedPhotoSwipeOptions}\r\n   */\n\n\n  _prepareOptions(options) {\n    if (window.matchMedia('(prefers-reduced-motion), (update: slow)').matches) {\n      options.showHideAnimationType = 'none';\n      options.zoomAnimationDuration = 0;\n    }\n    /** @type {PreparedPhotoSwipeOptions} */\n\n\n    return { ...defaultOptions,\n      ...options\n    };\n  }\n\n}\n\nexport { PhotoSwipe as default };\n//# sourceMappingURL=photoswipe.esm.js.map\n"], "names": ["createElement", "className", "tagName", "appendToEl", "el", "equalizePoints", "p1", "p2", "roundPoint", "p", "getDistanceBetween", "x", "y", "pointsEqual", "clamp", "val", "min", "max", "toTransformString", "scale", "propValue", "setTransform", "defaultCSSEasing", "setTransitionStyle", "prop", "duration", "ease", "setWidthHeight", "w", "h", "removeTransitionStyle", "decodeImage", "img", "resolve", "reject", "LOAD_STATE", "specialKeyUsed", "e", "getElementsFromOption", "option", "legacySelector", "parent", "elements", "selector", "<PERSON><PERSON><PERSON><PERSON>", "supportsPassive", "DOMEvents", "target", "type", "listener", "passive", "poolItem", "unbind", "skip<PERSON><PERSON>", "methodName", "eType", "eventOptions", "getViewportSize", "options", "pswp", "newViewportSize", "parsePaddingOption", "viewportSize", "itemData", "index", "paddingValue", "legacyPropName", "getPanAreaSize", "PanBounds", "slide", "currZoomLevel", "axis", "elSize", "padding", "panAreaSize", "panOffset", "MAX_IMAGE_WIDTH", "ZoomLevel", "max<PERSON><PERSON><PERSON>", "maxHeight", "elementSize", "hRatio", "vRatio", "optionPrefix", "optionName", "optionValue", "Slide", "data", "isActive", "holderElement", "force", "scaleMultiplier", "width", "height", "_this$content$placeho", "destZoomLevel", "centerPoint", "transitionDuration", "ignoreBounds", "prevZoomLevel", "finishTransition", "point", "zoomFactor", "panX", "panY", "zoom", "newResolution", "PAN_END_FRICTION", "VERTICAL_DRAG_FRICTION", "MIN_RATIO_TO_CLOSE", "MIN_NEXT_SLIDE_SPEED", "project", "initialVelocity", "decelerationRate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gestures", "prevP1", "dragAxis", "currSlide", "bgOpacity", "velocity", "mainScroll", "indexDiff", "currentSlideVisibilityRatio", "pan", "bounds", "panPos", "restoreBgOpacity", "projectedPosition", "vDragRatio", "projectedVDragRatio", "correctedPanPosition", "dampingRatio", "initialBgOpacity", "totalPanDist", "pos", "animationProgressRatio", "isMultitouch", "delta", "newMainScrollX", "newPan", "currSlideMainScrollX", "mainScrollShiftDiff", "isLeftToRight", "isRightToLeft", "_this$pswp$currSlide$", "_this$pswp$currSlide", "potentialPan", "customFriction", "UPPER_ZOOM_FRICTION", "LOWER_ZOOM_FRICTION", "getZoomPointsCenter", "Zoom<PERSON><PERSON><PERSON>", "startP1", "startP2", "minZoomLevel", "maxZoomLevel", "ignoreGesture", "destinationZoomLevel", "currZoomLevelNeedsChange", "initialPan", "destinationPan", "panNeedsChange", "now", "newZoomLevel", "didTapOnMainContent", "event", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "originalEvent", "targetClassList", "isImageClick", "isBackgroundClick", "actionName", "_this$gestures$pswp$e", "actionFullName", "AXIS_SWIPE_HYSTERISIS", "DOUBLE_TAP_DELAY", "MIN_TAP_DISTANCE", "Gestures", "pref", "down", "up", "cancel", "events", "cancelEvent", "isMousePointer", "time", "tap<PERSON>elay", "displacement", "pointerType", "pointerEvent", "pointerIndex", "ongoingPointer", "touchEvent", "diff", "axisToCheck", "MAIN_SCROLL_END_FRICTION", "MainScroll", "resizeSlides", "newSlideWidth", "slideWidthChanged", "itemHolder", "i", "animate", "velocityX", "newIndex", "numSlides", "distance", "destinationX", "currDiff", "currDistance", "_this$itemHolders$", "positionDifference", "diffAbs", "tempHolder", "_itemHolder$slide", "dragging", "newSlideIndexOffset", "KeyboardKeyCodesMap", "getKeyboardEventKey", "key", "isKeySupported", "Keyboard", "lastActiveElement", "keydownAction", "isForward", "template", "DEFAULT_EASING", "CSSAnimation", "props", "_props$prop", "onComplete", "transform", "onFinish", "easing", "DEFAULT_NATURAL_FREQUENCY", "DEFAULT_DAMPING_RATIO", "SpringEaser", "naturalFrequency", "deltaPosition", "deltaTime", "coeff", "naturalDumpingPow", "dumpedFCos", "dumpedFSin", "SpringAnimation", "start", "end", "onUpdate", "easer", "prevTime", "animationLoop", "Animations", "isSpring", "animation", "ScrollWheel", "deltaX", "deltaY", "addElementHTML", "htmlData", "svgData", "out", "UIElement", "_container", "name", "elementHTML", "element", "title", "aria<PERSON><PERSON><PERSON>", "ariaText", "appendTo", "container", "initArrowButton", "isNextButton", "arrowPrev", "arrowNext", "closeButton", "zoomButton", "loadingIndicator", "indicatorElement", "isVisible", "delayTimeout", "toggleIndicatorClass", "add", "setIndicatorVisibility", "visible", "updatePreloaderVisibility", "_pswp$currSlide", "_pswp$currSlide2", "counterIndicator", "counterElement", "setZoomedIn", "isZoomedIn", "UI", "a", "b", "uiElementData", "_pswp$element", "elementData", "currZoomLevelDiff", "potentialZoomLevel", "getBoundsByElement", "thumbAreaRect", "getCroppedBoundsByElement", "imageWidth", "imageHeight", "fillZoomLevel", "offsetX", "offsetY", "getThumbBounds", "instance", "thumbBounds", "thumbnail", "thumbSelector", "PhotoSwipeEvent", "details", "Eventable", "fn", "priority", "_this$_filters$name", "_this$_filters$name2", "_this$pswp", "f1", "f2", "filter", "args", "_this$_filters$name3", "_this$_listeners$name", "_this$pswp2", "_this$pswp3", "_this$_listeners$name2", "Placeholder", "imageSrc", "imgEl", "_this$element", "Content", "isLazy", "reload", "placeholder<PERSON><PERSON>", "placeholderSrc", "_this$data$src", "_this$data$alt", "imageElement", "isInitialSizeUpdate", "image", "sizesWidth", "_this$instance$option", "_this$instance$option2", "errorMsgEl", "supportsDecode", "MIN_SLIDES_TO_CACHE", "lazyLoadData", "content", "zoomLevel", "lazyLoadSlide", "ContentLoader", "preload", "initialIndex", "indexToRemove", "item", "PhotoSwipeBase", "_this$options", "numItems", "dataSource", "slideData", "_this$options2", "dataSourceItem", "galleryElement", "_this$options3", "_this$options4", "linkEl", "thumbnailEl", "_thumbnailEl$getAttri", "MIN_OPACITY", "Opener", "_options$showHideOpac", "decoded", "is<PERSON><PERSON>ying", "_this$pswp$element", "_this$pswp$element2", "innerRect", "containerOnePanX", "containerOnePanY", "containerTwoPanX", "containerTwoPanY", "animations", "animProps", "defaultOptions", "PhotoSwipe", "rootClasses", "itemHolders", "_this$currSlide", "_this$currSlide2", "_itemHolder$slide2", "slideIndex", "_this$currSlide$index", "_this$currSlide3", "potentialHolderIndex", "_itemHolder$slide3", "holder", "opacity", "_this$element2"], "mappings": "AAAA;AAAA;AAAA;AAAA,IAaA,SAASA,EAAcC,EAAWC,EAASC,EAAY,CACrD,MAAMC,EAAK,SAAS,cAAcF,CAAO,EAEzC,OAAID,IACFG,EAAG,UAAYH,GAGbE,GACFA,EAAW,YAAYC,CAAE,EAGpBA,CACT,CAOA,SAASC,EAAeC,EAAIC,EAAI,CAC9B,OAAAD,EAAG,EAAIC,EAAG,EACVD,EAAG,EAAIC,EAAG,EAENA,EAAG,KAAO,SACZD,EAAG,GAAKC,EAAG,IAGND,CACT,CAKA,SAASE,EAAWC,EAAG,CACrBA,EAAE,EAAI,KAAK,MAAMA,EAAE,CAAC,EACpBA,EAAE,EAAI,KAAK,MAAMA,EAAE,CAAC,CACtB,CASA,SAASC,EAAmBJ,EAAIC,EAAI,CAClC,MAAMI,EAAI,KAAK,IAAIL,EAAG,EAAIC,EAAG,CAAC,EACxBK,EAAI,KAAK,IAAIN,EAAG,EAAIC,EAAG,CAAC,EAC9B,OAAO,KAAK,KAAKI,EAAIA,EAAIC,EAAIA,CAAC,CAChC,CASA,SAASC,EAAYP,EAAIC,EAAI,CAC3B,OAAOD,EAAG,IAAMC,EAAG,GAAKD,EAAG,IAAMC,EAAG,CACtC,CAUA,SAASO,EAAMC,EAAKC,EAAKC,EAAK,CAC5B,OAAO,KAAK,IAAI,KAAK,IAAIF,EAAKC,CAAG,EAAGC,CAAG,CACzC,CAUA,SAASC,EAAkBP,EAAGC,EAAGO,EAAO,CACtC,IAAIC,EAAY,eAAeT,CAAC,MAAMC,GAAK,CAAC,QAE5C,OAAIO,IAAU,SACZC,GAAa,YAAYD,CAAK,IAAIA,CAAK,OAGlCC,CACT,CAUA,SAASC,EAAajB,EAAIO,EAAGC,EAAGO,EAAO,CACrCf,EAAG,MAAM,UAAYc,EAAkBP,EAAGC,EAAGO,CAAK,CACpD,CACA,MAAMG,EAAmB,2BAUzB,SAASC,EAAmBnB,EAAIoB,EAAMC,EAAUC,EAAM,CAIpDtB,EAAG,MAAM,WAAaoB,EAAO,GAAGA,CAAI,IAAIC,CAAQ,MAAMC,GAAQJ,CAAgB,GAAK,MACrF,CASA,SAASK,EAAevB,EAAIwB,EAAGC,EAAG,CAChCzB,EAAG,MAAM,MAAQ,OAAOwB,GAAM,SAAW,GAAGA,CAAC,KAAOA,EACpDxB,EAAG,MAAM,OAAS,OAAOyB,GAAM,SAAW,GAAGA,CAAC,KAAOA,CACvD,CAKA,SAASC,EAAsB1B,EAAI,CACjCmB,EAAmBnB,CAAE,CACvB,CAMA,SAAS2B,EAAYC,EAAK,CACxB,MAAI,WAAYA,EACPA,EAAI,OAAM,EAAG,MAAM,IAAM,CAAA,CAAE,EAGhCA,EAAI,SACC,QAAQ,QAAQA,CAAG,EAGrB,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtCF,EAAI,OAAS,IAAMC,EAAQD,CAAG,EAE9BA,EAAI,QAAUE,CAClB,CAAG,CACH,CAKA,MAAMC,EAAa,CACjB,KAAM,OACN,QAAS,UACT,OAAQ,SACR,MAAO,OACT,EASA,SAASC,EAAeC,EAAG,CACzB,MAAO,WAAYA,GAAKA,EAAE,SAAW,GAAKA,EAAE,SAAWA,EAAE,SAAWA,EAAE,QAAUA,EAAE,QACpF,CAUA,SAASC,EAAsBC,EAAQC,EAAgBC,EAAS,SAAU,CAExE,IAAIC,EAAW,CAAE,EAEjB,GAAIH,aAAkB,QACpBG,EAAW,CAACH,CAAM,UACTA,aAAkB,UAAY,MAAM,QAAQA,CAAM,EAC3DG,EAAW,MAAM,KAAKH,CAAM,MACvB,CACL,MAAMI,EAAW,OAAOJ,GAAW,SAAWA,EAASC,EAEnDG,IACFD,EAAW,MAAM,KAAKD,EAAO,iBAAiBE,CAAQ,CAAC,EAE7D,CAEE,OAAOD,CACT,CAOA,SAASE,GAAW,CAClB,MAAO,CAAC,EAAE,UAAU,QAAU,UAAU,OAAO,MAAM,QAAQ,EAC/D,CAGA,IAAIC,EAAkB,GAGtB,GAAI,CAEF,OAAO,iBAAiB,OAAQ,KAAM,OAAO,eAAe,CAAE,EAAE,UAAW,CACzE,IAAK,IAAM,CACTA,EAAkB,EACxB,CACA,CAAG,CAAC,CACJ,MAAY,CAAA,CAYZ,MAAMC,CAAU,CACd,aAAc,CAKZ,KAAK,MAAQ,CAAE,CACnB,CAWE,IAAIC,EAAQC,EAAMC,EAAUC,EAAS,CACnC,KAAK,gBAAgBH,EAAQC,EAAMC,EAAUC,CAAO,CACxD,CAWE,OAAOH,EAAQC,EAAMC,EAAUC,EAAS,CACtC,KAAK,gBAAgBH,EAAQC,EAAMC,EAAUC,EAAS,EAAI,CAC9D,CAME,WAAY,CACV,KAAK,MAAM,QAAQC,GAAY,CAC7B,KAAK,gBAAgBA,EAAS,OAAQA,EAAS,KAAMA,EAAS,SAAUA,EAAS,QAAS,GAAM,EAAI,CAC1G,CAAK,EAED,KAAK,MAAQ,CAAE,CACnB,CAcE,gBAAgBJ,EAAQC,EAAMC,EAAUC,EAASE,EAAQC,EAAU,CACjE,GAAI,CAACN,EACH,OAGF,MAAMO,EAAaF,EAAS,sBAAwB,mBACtCJ,EAAK,MAAM,GAAG,EACtB,QAAQO,GAAS,CACrB,GAAIA,EAAO,CAGJF,IACCD,EAEF,KAAK,MAAQ,KAAK,MAAM,OAAOD,GACtBA,EAAS,OAASI,GAASJ,EAAS,WAAaF,GAAYE,EAAS,SAAWJ,CACzF,EAGD,KAAK,MAAM,KAAK,CACd,OAAAA,EACA,KAAMQ,EACN,SAAAN,EACA,QAAAC,CACd,CAAa,GAML,MAAMM,EAAeX,EAAkB,CACrC,QAASK,GAAW,EAC9B,EAAY,GACJH,EAAOO,CAAU,EAAEC,EAAON,EAAUO,CAAY,CACxD,CACA,CAAK,CACL,CAEA,CAeA,SAASC,EAAgBC,EAASC,EAAM,CACtC,GAAID,EAAQ,kBAAmB,CAC7B,MAAME,EAAkBF,EAAQ,kBAAkBA,EAASC,CAAI,EAE/D,GAAIC,EACF,OAAOA,CAEb,CAEE,MAAO,CACL,EAAG,SAAS,gBAAgB,YAK5B,EAAG,OAAO,WACX,CACH,CAqCA,SAASC,EAAmBrC,EAAMkC,EAASI,EAAcC,EAAUC,EAAO,CACxE,IAAIC,EAAe,EAEnB,GAAIP,EAAQ,UACVO,EAAeP,EAAQ,UAAUI,EAAcC,EAAUC,CAAK,EAAExC,CAAI,UAC3DkC,EAAQ,QACjBO,EAAeP,EAAQ,QAAQlC,CAAI,MAC9B,CACL,MAAM0C,EAAiB,UAAY1C,EAAK,CAAC,EAAE,YAAW,EAAKA,EAAK,MAAM,CAAC,EAEnEkC,EAAQQ,CAAc,IAExBD,EAAeP,EAAQQ,CAAc,EAE3C,CAEE,OAAO,OAAOD,CAAY,GAAK,CACjC,CASA,SAASE,EAAeT,EAASI,EAAcC,EAAUC,EAAO,CAC9D,MAAO,CACL,EAAGF,EAAa,EAAID,EAAmB,OAAQH,EAASI,EAAcC,EAAUC,CAAK,EAAIH,EAAmB,QAASH,EAASI,EAAcC,EAAUC,CAAK,EAC3J,EAAGF,EAAa,EAAID,EAAmB,MAAOH,EAASI,EAAcC,EAAUC,CAAK,EAAIH,EAAmB,SAAUH,EAASI,EAAcC,EAAUC,CAAK,CAC5J,CACH,CAYA,MAAMI,CAAU,CAId,YAAYC,EAAO,CACjB,KAAK,MAAQA,EACb,KAAK,cAAgB,EACrB,KAAK,OAEL,CACE,EAAG,EACH,EAAG,CACJ,EACD,KAAK,IAEL,CACE,EAAG,EACH,EAAG,CACJ,EACD,KAAK,IAEL,CACE,EAAG,EACH,EAAG,CACJ,CACL,CAQE,OAAOC,EAAe,CACpB,KAAK,cAAgBA,EAEhB,KAAK,MAAM,OAGd,KAAK,YAAY,GAAG,EAEpB,KAAK,YAAY,GAAG,EAEpB,KAAK,MAAM,KAAK,SAAS,aAAc,CACrC,MAAO,KAAK,KACpB,CAAO,GARD,KAAK,MAAO,CAUlB,CAQE,YAAYC,EAAM,CAChB,KAAM,CACJ,KAAAZ,CACD,EAAG,KAAK,MACHa,EAAS,KAAK,MAAMD,IAAS,IAAM,QAAU,QAAQ,EAAI,KAAK,cAE9DE,EAAUZ,EADIU,IAAS,IAAM,OAAS,MACIZ,EAAK,QAASA,EAAK,aAAc,KAAK,MAAM,KAAM,KAAK,MAAM,KAAK,EAC5Ge,EAAc,KAAK,MAAM,YAAYH,CAAI,EAG/C,KAAK,OAAOA,CAAI,EAAI,KAAK,OAAOG,EAAcF,GAAU,CAAC,EAAIC,EAE7D,KAAK,IAAIF,CAAI,EAAIC,EAASE,EAAc,KAAK,MAAMA,EAAcF,CAAM,EAAIC,EAAU,KAAK,OAAOF,CAAI,EAErG,KAAK,IAAIA,CAAI,EAAIC,EAASE,EAAcD,EAAU,KAAK,OAAOF,CAAI,CACnE,CAGD,OAAQ,CACN,KAAK,OAAO,EAAI,EAChB,KAAK,OAAO,EAAI,EAChB,KAAK,IAAI,EAAI,EACb,KAAK,IAAI,EAAI,EACb,KAAK,IAAI,EAAI,EACb,KAAK,IAAI,EAAI,CACjB,CAUE,WAAWA,EAAMI,EAAW,CAE1B,OAAO7D,EAAM6D,EAAW,KAAK,IAAIJ,CAAI,EAAG,KAAK,IAAIA,CAAI,CAAC,CAC1D,CAEA,CAEA,MAAMK,EAAkB,IAgBxB,MAAMC,CAAU,CAOd,YAAYnB,EAASK,EAAUC,EAAOL,EAAM,CAC1C,KAAK,KAAOA,EACZ,KAAK,QAAUD,EACf,KAAK,SAAWK,EAChB,KAAK,MAAQC,EAGb,KAAK,YAAc,KAGnB,KAAK,YAAc,KACnB,KAAK,IAAM,EACX,KAAK,KAAO,EACZ,KAAK,MAAQ,EACb,KAAK,QAAU,EACf,KAAK,UAAY,EACjB,KAAK,IAAM,EACX,KAAK,IAAM,CACf,CAYE,OAAOc,EAAUC,EAAWL,EAAa,CAEvC,MAAMM,EAAc,CAClB,EAAGF,EACH,EAAGC,CACJ,EACD,KAAK,YAAcC,EACnB,KAAK,YAAcN,EACnB,MAAMO,EAASP,EAAY,EAAIM,EAAY,EACrCE,EAASR,EAAY,EAAIM,EAAY,EAC3C,KAAK,IAAM,KAAK,IAAI,EAAGC,EAASC,EAASD,EAASC,CAAM,EACxD,KAAK,KAAO,KAAK,IAAI,EAAGD,EAASC,EAASD,EAASC,CAAM,EAGzD,KAAK,MAAQ,KAAK,IAAI,EAAGA,CAAM,EAC/B,KAAK,QAAU,KAAK,YAAa,EACjC,KAAK,UAAY,KAAK,cAAe,EACrC,KAAK,IAAM,KAAK,IAAI,KAAK,QAAS,KAAK,UAAW,KAAK,SAAS,EAChE,KAAK,IAAM,KAAK,IAAI,KAAK,IAAK,KAAK,QAAS,KAAK,SAAS,EAEtD,KAAK,MACP,KAAK,KAAK,SAAS,mBAAoB,CACrC,WAAY,KACZ,UAAW,KAAK,QACxB,CAAO,CAEP,CAUE,sBAAsBC,EAAc,CAClC,MAAMC,EAEND,EAAe,YACTE,EAAc,KAAK,QAAQD,CAAU,EAE3C,GAAKC,EAIL,OAAI,OAAOA,GAAgB,WAClBA,EAAY,IAAI,EAGrBA,IAAgB,OACX,KAAK,KAGVA,IAAgB,MACX,KAAK,IAGP,OAAOA,CAAW,CAC7B,CAYE,eAAgB,CACd,IAAIf,EAAgB,KAAK,sBAAsB,WAAW,EAE1D,OAAIA,IAKJA,EAAgB,KAAK,IAAI,EAAG,KAAK,IAAM,CAAC,EAEpC,KAAK,aAAeA,EAAgB,KAAK,YAAY,EAAIM,IAC3DN,EAAgBM,EAAkB,KAAK,YAAY,GAG9CN,EACX,CASE,aAAc,CACZ,OAAO,KAAK,sBAAsB,SAAS,GAAK,KAAK,GACzD,CAWE,SAAU,CAGR,OAAO,KAAK,sBAAsB,KAAK,GAAK,KAAK,IAAI,EAAG,KAAK,IAAM,CAAC,CACxE,CAEA,CAOA,MAAMgB,CAAM,CAMV,YAAYC,EAAMvB,EAAOL,EAAM,CAC7B,KAAK,KAAO4B,EACZ,KAAK,MAAQvB,EACb,KAAK,KAAOL,EACZ,KAAK,SAAWK,IAAUL,EAAK,UAC/B,KAAK,kBAAoB,EAGzB,KAAK,YAAc,CACjB,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,IAAM,CACT,EAAG,EACH,EAAG,CACJ,EACD,KAAK,aAAe,KAAK,UAAY,CAACA,EAAK,OAAO,OAClD,KAAK,WAAa,IAAIkB,EAAUlB,EAAK,QAAS4B,EAAMvB,EAAOL,CAAI,EAC/D,KAAK,KAAK,SAAS,cAAe,CAChC,MAAO,KACP,KAAM,KAAK,KACX,MAAAK,CACN,CAAK,EACD,KAAK,QAAU,KAAK,KAAK,cAAc,kBAAkB,IAAI,EAC7D,KAAK,UAAYhE,EAAc,kBAAmB,KAAK,EAGvD,KAAK,cAAgB,KACrB,KAAK,cAAgB,EAGrB,KAAK,MAAQ,KAAK,QAAQ,MAG1B,KAAK,OAAS,KAAK,QAAQ,OAC3B,KAAK,cAAgB,GACrB,KAAK,OAAS,IAAIoE,EAAU,IAAI,EAChC,KAAK,mBAAqB,GAC1B,KAAK,oBAAsB,GAC3B,KAAK,KAAK,SAAS,YAAa,CAC9B,MAAO,IACb,CAAK,CACL,CAQE,YAAYoB,EAAU,CAChBA,GAAY,CAAC,KAAK,SAEpB,KAAK,SAAU,EACN,CAACA,GAAY,KAAK,UAE3B,KAAK,WAAY,CAEvB,CAQE,OAAOC,EAAe,CACpB,KAAK,cAAgBA,EACrB,KAAK,UAAU,MAAM,gBAAkB,MAElC,KAAK,OAIV,KAAK,cAAe,EACpB,KAAK,KAAM,EACX,KAAK,kBAAmB,EACxB,KAAK,YAAa,EAClB,KAAK,cAAc,YAAY,KAAK,SAAS,EAC7C,KAAK,oBAAqB,EAC1B,KAAK,KAAK,SAAS,eAAgB,CACjC,MAAO,IACb,CAAK,EACD,KAAK,oBAAqB,EAC1B,KAAK,KAAK,SAAS,kBAAmB,CACpC,MAAO,IACb,CAAK,EAEG,KAAK,UACP,KAAK,SAAU,EAErB,CAEE,MAAO,CACL,KAAK,QAAQ,KAAK,EAAK,EACvB,KAAK,KAAK,SAAS,YAAa,CAC9B,MAAO,IACb,CAAK,CACL,CASE,aAAc,CACZ,KAAM,CACJ,KAAA9B,CACN,EAAQ,KAIA,KAAK,eAAiB,CAACA,EAAK,OAAO,QAAUA,EAAK,WAAW,UAAW,GAAI,CAAC,KAAK,UAAY,IAI9F,KAAK,KAAK,SAAS,cAAe,CACpC,MAAO,IACR,CAAA,EAAE,mBAIH,KAAK,cAAgB,GACrB,KAAK,QAAQ,OAAQ,EACrB,KAAK,KAAK,SAAS,qBAAsB,CACvC,MAAO,IACb,CAAK,EACL,CASE,UAAW,CACT,KAAK,SAAW,GAChB,KAAK,YAAa,EAClB,KAAK,QAAQ,SAAU,EACvB,KAAK,KAAK,SAAS,gBAAiB,CAClC,MAAO,IACb,CAAK,CACL,CAQE,YAAa,CACX,KAAK,SAAW,GAChB,KAAK,QAAQ,WAAY,EAErB,KAAK,gBAAkB,KAAK,WAAW,SAEzC,KAAK,cAAe,EAItB,KAAK,kBAAoB,EACzB,KAAK,oBAAqB,EAC1B,KAAK,oBAAqB,EAC1B,KAAK,kBAAmB,EACxB,KAAK,KAAK,SAAS,kBAAmB,CACpC,MAAO,IACb,CAAK,CACL,CAOE,SAAU,CACR,KAAK,QAAQ,SAAW,GACxB,KAAK,QAAQ,OAAQ,EACrB,KAAK,UAAU,OAAQ,EACvB,KAAK,KAAK,SAAS,eAAgB,CACjC,MAAO,IACb,CAAK,CACL,CAEE,QAAS,CACH,KAAK,gBAAkB,KAAK,WAAW,SAAW,CAAC,KAAK,UAI1D,KAAK,cAAe,EACpB,KAAK,kBAAoB,EACzB,KAAK,oBAAqB,EAC1B,KAAK,oBAAqB,EAC1B,KAAK,kBAAmB,IAGxB,KAAK,cAAe,EACpB,KAAK,OAAO,OAAO,KAAK,aAAa,EACrC,KAAK,MAAM,KAAK,IAAI,EAAG,KAAK,IAAI,CAAC,EAEvC,CASE,kBAAkB+B,EAAO,CAGvB,MAAMC,EAAkB,KAAK,mBAAqB,KAAK,WAAW,QAElE,GAAI,CAACA,EACH,OAGF,MAAMC,EAAQ,KAAK,MAAM,KAAK,MAAQD,CAAe,GAAK,KAAK,KAAK,aAAa,EAC3EE,EAAS,KAAK,MAAM,KAAK,OAASF,CAAe,GAAK,KAAK,KAAK,aAAa,EAE/E,CAAC,KAAK,YAAYC,EAAOC,CAAM,GAAK,CAACH,GAIzC,KAAK,QAAQ,iBAAiBE,EAAOC,CAAM,CAC/C,CAOE,YAAYD,EAAOC,EAAQ,CACzB,OAAID,IAAU,KAAK,oBAAsBC,IAAW,KAAK,qBACvD,KAAK,mBAAqBD,EAC1B,KAAK,oBAAsBC,EACpB,IAGF,EACX,CAIE,uBAAwB,CACtB,IAAIC,EAEJ,OAAQA,EAAwB,KAAK,QAAQ,eAAiB,MAAQA,IAA0B,OAAS,OAASA,EAAsB,OAC5I,CAYE,OAAOC,EAAeC,EAAaC,EAAoBC,EAAc,CACnE,KAAM,CACJ,KAAAvC,CACN,EAAQ,KAEJ,GAAI,CAAC,KAAK,WAAU,GAAMA,EAAK,WAAW,YACxC,OAGFA,EAAK,SAAS,eAAgB,CAC5B,cAAAoC,EACA,YAAAC,EACA,mBAAAC,CACN,CAAK,EAEDtC,EAAK,WAAW,aAIhB,MAAMwC,EAAgB,KAAK,cAEtBD,IACHH,EAAgBjF,EAAMiF,EAAe,KAAK,WAAW,IAAK,KAAK,WAAW,GAAG,GAM/E,KAAK,aAAaA,CAAa,EAC/B,KAAK,IAAI,EAAI,KAAK,yBAAyB,IAAKC,EAAaG,CAAa,EAC1E,KAAK,IAAI,EAAI,KAAK,yBAAyB,IAAKH,EAAaG,CAAa,EAC1E3F,EAAW,KAAK,GAAG,EAEnB,MAAM4F,EAAmB,IAAM,CAC7B,KAAK,eAAeL,CAAa,EAEjC,KAAK,oBAAqB,CAC3B,EAEIE,EAGHtC,EAAK,WAAW,gBAAgB,CAC9B,MAAO,GACP,KAAM,SACN,OAAQ,KAAK,UACb,UAAW,KAAK,oBAAqB,EACrC,WAAYyC,EACZ,SAAUH,EACV,OAAQtC,EAAK,QAAQ,MAC7B,CAAO,EAVDyC,EAAkB,CAYxB,CAME,WAAWJ,EAAa,CACtB,KAAK,OAAO,KAAK,gBAAkB,KAAK,WAAW,QAAU,KAAK,WAAW,UAAY,KAAK,WAAW,QAASA,EAAa,KAAK,KAAK,QAAQ,qBAAqB,CAC1K,CASE,aAAa1B,EAAe,CAC1B,KAAK,cAAgBA,EACrB,KAAK,OAAO,OAAO,KAAK,aAAa,CACzC,CAgBE,yBAAyBC,EAAM8B,EAAOF,EAAe,CAGnD,GAFyB,KAAK,OAAO,IAAI5B,CAAI,EAAI,KAAK,OAAO,IAAIA,CAAI,IAE5C,EACvB,OAAO,KAAK,OAAO,OAAOA,CAAI,EAG3B8B,IACHA,EAAQ,KAAK,KAAK,uBAAwB,GAGvCF,IACHA,EAAgB,KAAK,WAAW,SAGlC,MAAMG,EAAa,KAAK,cAAgBH,EACxC,OAAO,KAAK,OAAO,WAAW5B,GAAO,KAAK,IAAIA,CAAI,EAAI8B,EAAM9B,CAAI,GAAK+B,EAAaD,EAAM9B,CAAI,CAAC,CACjG,CASE,MAAMgC,EAAMC,EAAM,CAChB,KAAK,IAAI,EAAI,KAAK,OAAO,WAAW,IAAKD,CAAI,EAC7C,KAAK,IAAI,EAAI,KAAK,OAAO,WAAW,IAAKC,CAAI,EAC7C,KAAK,oBAAqB,CAC9B,CAOE,YAAa,CACX,MAAO,EAAQ,KAAK,OAAU,KAAK,cAAgB,KAAK,WAAW,GACvE,CAOE,YAAa,CACX,MAAO,EAAQ,KAAK,OAAU,KAAK,QAAQ,WAAY,CAC3D,CAOE,qBAAsB,CACpB,KAAK,oBAAoB,KAAK,IAAI,EAAG,KAAK,IAAI,EAAG,KAAK,aAAa,EAE/D,OAAS,KAAK,KAAK,WACrB,KAAK,KAAK,SAAS,gBAAiB,CAClC,MAAO,IACf,CAAO,CAEP,CAEE,qBAAsB,CACpB,KAAK,cAAgB,KAAK,WAAW,QAErC,KAAK,OAAO,OAAO,KAAK,aAAa,EACrCnG,EAAe,KAAK,IAAK,KAAK,OAAO,MAAM,EAC3C,KAAK,KAAK,SAAS,iBAAkB,CACnC,MAAO,IACb,CAAK,CACL,CAWE,oBAAoBM,EAAGC,EAAG6F,EAAM,CAC9BA,GAAQ,KAAK,mBAAqB,KAAK,WAAW,QAClDpF,EAAa,KAAK,UAAWV,EAAGC,EAAG6F,CAAI,CAC3C,CAEE,eAAgB,CACd,KAAM,CACJ,KAAA9C,CACN,EAAQ,KACJtD,EAAe,KAAK,YAAa8D,EAAeR,EAAK,QAASA,EAAK,aAAc,KAAK,KAAM,KAAK,KAAK,CAAC,EACvG,KAAK,WAAW,OAAO,KAAK,MAAO,KAAK,OAAQ,KAAK,WAAW,EAChEA,EAAK,SAAS,gBAAiB,CAC7B,MAAO,IACb,CAAK,CACL,CAIE,qBAAsB,CACpB,MAAMxC,EAAQ,KAAK,eAAiB,KAAK,mBAAqB,KAAK,WAAW,SAC9E,OAAOD,EAAkB,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGC,CAAK,CAC1D,CAkBE,eAAeuF,EAAe,CACxBA,IAAkB,KAAK,oBAI3B,KAAK,kBAAoBA,EACzB,KAAK,kBAAmB,EACxB,KAAK,KAAK,SAAS,mBAAmB,EAC1C,CAEA,CAMA,MAAMC,EAAmB,IACnBC,EAAyB,GAEzBC,EAAqB,GAGrBC,EAAuB,GAO7B,SAASC,GAAQC,EAAiBC,EAAkB,CAClD,OAAOD,EAAkBC,GAAoB,EAAIA,EACnD,CAMA,MAAMC,EAAY,CAIhB,YAAYC,EAAU,CACpB,KAAK,SAAWA,EAChB,KAAK,KAAOA,EAAS,KAGrB,KAAK,SAAW,CACd,EAAG,EACH,EAAG,CACJ,CACL,CAEE,OAAQ,CACF,KAAK,KAAK,WACZ9G,EAAe,KAAK,SAAU,KAAK,KAAK,UAAU,GAAG,EAGvD,KAAK,KAAK,WAAW,QAAS,CAClC,CAEE,QAAS,CACP,KAAM,CACJ,GAAAC,EACA,OAAA8G,EACA,SAAAC,CACD,EAAG,KAAK,SACH,CACJ,UAAAC,CACD,EAAG,KAAK,KAET,GAAID,IAAa,KAAO,KAAK,KAAK,QAAQ,qBAAuBC,GAAaA,EAAU,eAAiBA,EAAU,WAAW,KAAO,CAAC,KAAK,SAAS,aAAc,CAEhK,MAAMd,EAAOc,EAAU,IAAI,GAAKhH,EAAG,EAAI8G,EAAO,GAE9C,GAAI,CAAC,KAAK,KAAK,SAAS,eAAgB,CACtC,KAAAZ,CACD,CAAA,EAAE,iBAAkB,CACnB,KAAK,oBAAoB,IAAKA,EAAMI,CAAsB,EAE1D,MAAMW,EAAY,EAAI,KAAK,IAAI,KAAK,sBAAsBD,EAAU,IAAI,CAAC,CAAC,EAC1E,KAAK,KAAK,eAAeC,CAAS,EAClCD,EAAU,oBAAqB,CACvC,CACA,MACgC,KAAK,qBAAqB,GAAG,IAGrD,KAAK,qBAAqB,GAAG,EAEzBA,IACF9G,EAAW8G,EAAU,GAAG,EACxBA,EAAU,oBAAqB,GAIzC,CAEE,KAAM,CACJ,KAAM,CACJ,SAAAE,CACD,EAAG,KAAK,SACH,CACJ,WAAAC,EACA,UAAAH,CACD,EAAG,KAAK,KACT,IAAII,EAAY,EAGhB,GAFA,KAAK,KAAK,WAAW,UAEjBD,EAAW,YAAa,CAO1B,MAAME,GALsBF,EAAW,EAAIA,EAAW,cAAa,GAKT,KAAK,KAAK,aAAa,EAS7ED,EAAS,EAAI,KAAyBG,EAA8B,GAAKH,EAAS,EAAI,IAAOG,EAA8B,KAE7HD,EAAY,EACZF,EAAS,EAAI,KAAK,IAAIA,EAAS,EAAG,CAAC,IAC1BA,EAAS,EAAIV,GAAwBa,EAA8B,GAAKH,EAAS,EAAI,KAAQG,EAA8B,MAEpID,EAAY,GACZF,EAAS,EAAI,KAAK,IAAIA,EAAS,EAAG,CAAC,GAGrCC,EAAW,YAAYC,EAAW,GAAMF,EAAS,CAAC,CACnD,CAGGF,GAAaA,EAAU,cAAgBA,EAAU,WAAW,KAAO,KAAK,SAAS,aACnF,KAAK,SAAS,WAAW,eAAe,EAAI,GAM5C,KAAK,yBAAyB,GAAG,EAEjC,KAAK,yBAAyB,GAAG,EAEvC,CAOE,yBAAyB/C,EAAM,CAC7B,KAAM,CACJ,SAAAiD,CACD,EAAG,KAAK,SACH,CACJ,UAAAF,CACD,EAAG,KAAK,KAET,GAAI,CAACA,EACH,OAGF,KAAM,CACJ,IAAAM,EACA,OAAAC,CACN,EAAQP,EACEQ,EAASF,EAAIrD,CAAI,EACjBwD,EAAmB,KAAK,KAAK,UAAY,GAAKxD,IAAS,IAMvDyD,EAAoBF,EAASf,GAAQS,EAASjD,CAAI,EAH/B,IAGkD,EAE3E,GAAIwD,EAAkB,CACpB,MAAME,EAAa,KAAK,sBAAsBH,CAAM,EAE9CI,EAAsB,KAAK,sBAAsBF,CAAiB,EAIxE,GAAIC,EAAa,GAAKC,EAAsB,KAAuBD,EAAa,GAAKC,EAAsBrB,EAAoB,CAC7H,KAAK,KAAK,MAAO,EACjB,MACR,CACK,CAGD,MAAMsB,EAAuBN,EAAO,WAAWtD,EAAMyD,CAAiB,EAGtE,GAAIF,IAAWK,EACb,OAIF,MAAMC,EAAeD,IAAyBH,EAAoB,EAAI,IAChEK,EAAmB,KAAK,KAAK,UAC7BC,EAAeH,EAAuBL,EAC5C,KAAK,KAAK,WAAW,YAAY,CAC/B,KAAM,aAAevD,EACrB,MAAO,GACP,MAAOuD,EACP,IAAKK,EACL,SAAUX,EAASjD,CAAI,EACvB,aAAA6D,EACA,SAAUG,GAAO,CAEf,GAAIR,GAAoB,KAAK,KAAK,UAAY,EAAG,CAE/C,MAAMS,EAAyB,GAAKL,EAAuBI,GAAOD,EAIlE,KAAK,KAAK,eAAexH,EAAMuH,GAAoB,EAAIA,GAAoBG,EAAwB,EAAG,CAAC,CAAC,CAClH,CAEQZ,EAAIrD,CAAI,EAAI,KAAK,MAAMgE,CAAG,EAC1BjB,EAAU,oBAAqB,CACvC,CACA,CAAK,CACL,CAaE,qBAAqB/C,EAAM,CACzB,KAAM,CACJ,GAAAjE,EACA,SAAA+G,EACA,OAAAD,EACA,aAAAqB,CACD,EAAG,KAAK,SACH,CACJ,UAAAnB,EACA,WAAAG,CACD,EAAG,KAAK,KACHiB,EAAQpI,EAAGiE,CAAI,EAAI6C,EAAO7C,CAAI,EAC9BoE,EAAiBlB,EAAW,EAAIiB,EAEtC,GAAI,CAACA,GAAS,CAACpB,EACb,MAAO,GAIT,GAAI/C,IAAS,KAAO,CAAC+C,EAAU,WAAU,GAAM,CAACmB,EAC9C,OAAAhB,EAAW,OAAOkB,EAAgB,EAAI,EAC/B,GAGT,KAAM,CACJ,OAAAd,CACN,EAAQP,EACEsB,EAAStB,EAAU,IAAI/C,CAAI,EAAImE,EAErC,GAAI,KAAK,KAAK,QAAQ,gBAAkBrB,IAAa,KAAO9C,IAAS,KAAO,CAACkE,EAAc,CACzF,MAAMI,EAAuBpB,EAAW,gBAElCqB,EAAsBrB,EAAW,EAAIoB,EACrCE,EAAgBL,EAAQ,EACxBM,EAAgB,CAACD,EAEvB,GAAIH,EAASf,EAAO,IAAItD,CAAI,GAAKwE,EAAe,CAO9C,GAF4BlB,EAAO,IAAItD,CAAI,GAAK,KAAK,SAASA,CAAI,EAGhE,OAAAkD,EAAW,OAAOkB,EAAgB,EAAI,EAC/B,GAEP,KAAK,oBAAoBpE,EAAMqE,CAAM,CAG/C,SAAiBA,EAASf,EAAO,IAAItD,CAAI,GAAKyE,EAAe,CAKrD,GAF4B,KAAK,SAASzE,CAAI,GAAKsD,EAAO,IAAItD,CAAI,EAGhE,OAAAkD,EAAW,OAAOkB,EAAgB,EAAI,EAC/B,GAEP,KAAK,oBAAoBpE,EAAMqE,CAAM,CAG/C,SAEYE,IAAwB,EAAG,CAE7B,GAAIA,EAAsB,EAGxB,OAAArB,EAAW,OAAO,KAAK,IAAIkB,EAAgBE,CAAoB,EAAG,EAAI,EAC/D,GACF,GAAIC,EAAsB,EAI/B,OAAArB,EAAW,OAAO,KAAK,IAAIkB,EAAgBE,CAAoB,EAAG,EAAI,EAC/D,EAEnB,MAEU,KAAK,oBAAoBtE,EAAMqE,CAAM,CAG/C,MACUrE,IAAS,IAEP,CAACkD,EAAW,UAAS,GAAMI,EAAO,IAAI,IAAMA,EAAO,IAAI,GACzD,KAAK,oBAAoBtD,EAAMqE,CAAM,EAGvC,KAAK,oBAAoBrE,EAAMqE,CAAM,EAIzC,MAAO,EACR,CAgBD,sBAAsBpC,EAAM,CAC1B,IAAIyC,EAAuBC,EAE3B,OAAQ1C,IAASyC,GAAyBC,EAAuB,KAAK,KAAK,aAAe,MAAQA,IAAyB,OAAS,OAASA,EAAqB,OAAO,OAAO,KAAO,MAAQD,IAA0B,OAASA,EAAwB,KAAO,KAAK,KAAK,aAAa,EAAI,EAChS,CAaE,oBAAoB1E,EAAM4E,EAAcC,EAAgB,CACtD,KAAM,CACJ,UAAA9B,CACD,EAAG,KAAK,KAET,GAAI,CAACA,EACH,OAGF,KAAM,CACJ,IAAAM,EACA,OAAAC,CACN,EAAQP,EAGJ,GAFqBO,EAAO,WAAWtD,EAAM4E,CAAY,IAEpCA,GAAgBC,EAAgB,CACnD,MAAMV,EAAQ,KAAK,MAAMS,EAAevB,EAAIrD,CAAI,CAAC,EACjDqD,EAAIrD,CAAI,GAAKmE,GAASU,GAAkBzC,EAC9C,MACMiB,EAAIrD,CAAI,EAAI4E,CAElB,CAEA,CAMA,MAAME,GAAsB,IACtBC,GAAsB,IAU5B,SAASC,EAAoB9I,EAAGH,EAAIC,EAAI,CACtC,OAAAE,EAAE,GAAKH,EAAG,EAAIC,EAAG,GAAK,EACtBE,EAAE,GAAKH,EAAG,EAAIC,EAAG,GAAK,EACfE,CACT,CAEA,MAAM+I,EAAY,CAIhB,YAAYrC,EAAU,CACpB,KAAK,SAAWA,EAMhB,KAAK,UAAY,CACf,EAAG,EACH,EAAG,CACJ,EAMD,KAAK,gBAAkB,CACrB,EAAG,EACH,EAAG,CACJ,EAMD,KAAK,WAAa,CAChB,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,qBAAuB,GAG5B,KAAK,gBAAkB,CAC3B,CAEE,OAAQ,CACN,KAAM,CACJ,UAAAG,CACN,EAAQ,KAAK,SAAS,KAEdA,IACF,KAAK,gBAAkBA,EAAU,cACjCjH,EAAe,KAAK,UAAWiH,EAAU,GAAG,GAG9C,KAAK,SAAS,KAAK,WAAW,WAAY,EAC1C,KAAK,qBAAuB,EAChC,CAEE,QAAS,CACP,KAAM,CACJ,GAAAhH,EACA,QAAAmJ,EACA,GAAAlJ,EACA,QAAAmJ,EACA,KAAA/F,CACD,EAAG,KAAK,SACH,CACJ,UAAA2D,CACN,EAAQ3D,EAEJ,GAAI,CAAC2D,EACH,OAGF,MAAMqC,EAAerC,EAAU,WAAW,IACpCsC,EAAetC,EAAU,WAAW,IAE1C,GAAI,CAACA,EAAU,WAAU,GAAM3D,EAAK,WAAW,YAC7C,OAGF4F,EAAoB,KAAK,gBAAiBE,EAASC,CAAO,EAC1DH,EAAoB,KAAK,WAAYjJ,EAAIC,CAAE,EAE3C,IAAI+D,EAAgB,EAAI5D,EAAmB+I,EAASC,CAAO,EAAIhJ,EAAmBJ,EAAIC,CAAE,EAAI,KAAK,gBAOjG,GAJI+D,EAAgBgD,EAAU,WAAW,QAAUA,EAAU,WAAW,QAAU,KAChF,KAAK,qBAAuB,IAG1BhD,EAAgBqF,EAClB,GAAIhG,EAAK,QAAQ,cAAgB,CAAC,KAAK,sBAAwB,KAAK,iBAAmB2D,EAAU,WAAW,QAAS,CAEnH,MAAMC,EAAY,GAAKoC,EAAerF,IAAkBqF,EAAe,KAElEhG,EAAK,SAAS,aAAc,CAC/B,UAAA4D,CACD,CAAA,EAAE,kBACD5D,EAAK,eAAe4D,CAAS,CAEvC,MAEQjD,EAAgBqF,GAAgBA,EAAerF,GAAiBgF,QAEzDhF,EAAgBsF,IAEzBtF,EAAgBsF,GAAgBtF,EAAgBsF,GAAgBP,IAGlE/B,EAAU,IAAI,EAAI,KAAK,0BAA0B,IAAKhD,CAAa,EACnEgD,EAAU,IAAI,EAAI,KAAK,0BAA0B,IAAKhD,CAAa,EACnEgD,EAAU,aAAahD,CAAa,EACpCgD,EAAU,oBAAqB,CACnC,CAEE,KAAM,CACJ,KAAM,CACJ,KAAA3D,CACD,EAAG,KAAK,SACH,CACJ,UAAA2D,CACN,EAAQ3D,GAEC,CAAC2D,GAAaA,EAAU,cAAgBA,EAAU,WAAW,UAAY,CAAC,KAAK,sBAAwB3D,EAAK,QAAQ,aACvHA,EAAK,MAAO,EAEZ,KAAK,eAAgB,CAE3B,CASE,0BAA0BY,EAAMD,EAAe,CAC7C,MAAMgC,EAAahC,EAAgB,KAAK,gBACxC,OAAO,KAAK,WAAWC,CAAI,GAAK,KAAK,gBAAgBA,CAAI,EAAI,KAAK,UAAUA,CAAI,GAAK+B,CACzF,CAWE,eAAeuD,EAAe,CAC5B,KAAM,CACJ,KAAAlG,CACD,EAAG,KAAK,SACH,CACJ,UAAA2D,CACN,EAAQ3D,EAEJ,GAAI,EAAE2D,GAAc,MAAgCA,EAAU,WAAU,GACtE,OAGE,KAAK,WAAW,IAAM,IACxBuC,EAAgB,IAGlB,MAAM1D,EAAgBmB,EAAU,cAGhC,IAAIwC,EACAC,EAA2B,GAE3B5D,EAAgBmB,EAAU,WAAW,QACvCwC,EAAuBxC,EAAU,WAAW,QACnCnB,EAAgBmB,EAAU,WAAW,IAC9CwC,EAAuBxC,EAAU,WAAW,KAE5CyC,EAA2B,GAC3BD,EAAuB3D,GAGzB,MAAMkC,EAAmB1E,EAAK,UACxBoE,EAAmBpE,EAAK,UAAY,EACpCqG,EAAa3J,EAAe,CAChC,EAAG,EACH,EAAG,CACT,EAAOiH,EAAU,GAAG,EAChB,IAAI2C,EAAiB5J,EAAe,CAClC,EAAG,EACH,EAAG,CACJ,EAAE2J,CAAU,EAETH,IACF,KAAK,WAAW,EAAI,EACpB,KAAK,WAAW,EAAI,EACpB,KAAK,gBAAgB,EAAI,EACzB,KAAK,gBAAgB,EAAI,EACzB,KAAK,gBAAkB1D,EACvB9F,EAAe,KAAK,UAAW2J,CAAU,GAGvCD,IACFE,EAAiB,CACf,EAAG,KAAK,0BAA0B,IAAKH,CAAoB,EAC3D,EAAG,KAAK,0BAA0B,IAAKA,CAAoB,CAC5D,GAIHxC,EAAU,aAAawC,CAAoB,EAC3CG,EAAiB,CACf,EAAG3C,EAAU,OAAO,WAAW,IAAK2C,EAAe,CAAC,EACpD,EAAG3C,EAAU,OAAO,WAAW,IAAK2C,EAAe,CAAC,CAC1D,EAEI3C,EAAU,aAAanB,CAAa,EACpC,MAAM+D,EAAiB,CAACrJ,EAAYoJ,EAAgBD,CAAU,EAE9D,GAAI,CAACE,GAAkB,CAACH,GAA4B,CAAChC,EAAkB,CAErET,EAAU,eAAewC,CAAoB,EAE7CxC,EAAU,oBAAmB,EAE7B,MACN,CAEI3D,EAAK,WAAW,WAAY,EAC5BA,EAAK,WAAW,YAAY,CAC1B,MAAO,GACP,MAAO,EACP,IAAK,IACL,SAAU,EACV,aAAc,EACd,iBAAkB,GAClB,SAAUwG,GAAO,CAGf,GAFAA,GAAO,IAEHD,GAAkBH,EAA0B,CAM9C,GALIG,IACF5C,EAAU,IAAI,EAAI0C,EAAW,GAAKC,EAAe,EAAID,EAAW,GAAKG,EACrE7C,EAAU,IAAI,EAAI0C,EAAW,GAAKC,EAAe,EAAID,EAAW,GAAKG,GAGnEJ,EAA0B,CAC5B,MAAMK,EAAejE,GAAiB2D,EAAuB3D,GAAiBgE,EAC9E7C,EAAU,aAAa8C,CAAY,CAC/C,CAEU9C,EAAU,oBAAqB,CAChC,CAGGS,GAAoBpE,EAAK,UAAY,GAIvCA,EAAK,eAAe7C,EAAMuH,GAAoB,EAAIA,GAAoB8B,EAAK,EAAG,CAAC,CAAC,CAEnF,EACD,WAAY,IAAM,CAEhB7C,EAAU,eAAewC,CAAoB,EAE7CxC,EAAU,oBAAqB,CACvC,CACA,CAAK,CACL,CAEA,CAqBA,SAAS+C,EAAoBC,EAAO,CAClC,MAAO,CAAC,CAERA,EAAM,OAAO,QAAQ,kBAAkB,CACzC,CAMA,MAAMC,EAAW,CAIf,YAAYpD,EAAU,CACpB,KAAK,SAAWA,CACpB,CAOE,MAAMd,EAAOmE,EAAe,CAC1B,MAAMC,EAEND,EAAc,OAAO,UACfE,EAAeD,EAAgB,SAAS,WAAW,EACnDE,EAAoBF,EAAgB,SAAS,YAAY,GAAKA,EAAgB,SAAS,iBAAiB,EAE1GC,EACF,KAAK,oBAAoB,aAAcrE,EAAOmE,CAAa,EAClDG,GACT,KAAK,oBAAoB,UAAWtE,EAAOmE,CAAa,CAE9D,CAOE,IAAInE,EAAOmE,EAAe,CACpBH,EAAoBG,CAAa,GACnC,KAAK,oBAAoB,MAAOnE,EAAOmE,CAAa,CAE1D,CAOE,UAAUnE,EAAOmE,EAAe,CAC1BH,EAAoBG,CAAa,GACnC,KAAK,oBAAoB,YAAanE,EAAOmE,CAAa,CAEhE,CASE,oBAAoBI,EAAYvE,EAAOmE,EAAe,CACpD,IAAIK,EAEJ,KAAM,CACJ,KAAAlH,CACD,EAAG,KAAK,SACH,CACJ,UAAA2D,CACN,EAAQ3D,EACEmH,EAENF,EAAa,SACPvF,EAAc1B,EAAK,QAAQmH,CAAc,EAE/C,GAAI,CAAAnH,EAAK,SAASmH,EAAgB,CAChC,MAAAzE,EACA,cAAAmE,CACD,CAAA,EAAE,iBAIH,IAAI,OAAOnF,GAAgB,WAAY,CACrCA,EAAY,KAAK1B,EAAM0C,EAAOmE,CAAa,EAC3C,MACN,CAEI,OAAQnF,EAAW,CACjB,IAAK,QACL,IAAK,OACH1B,EAAK0B,CAAW,EAAG,EACnB,MAEF,IAAK,OACHiC,GAAc,MAAgCA,EAAU,WAAWjB,CAAK,EACxE,MAEF,IAAK,gBAGCiB,GAAc,MAAgCA,EAAU,cAAgBA,EAAU,WAAW,YAAcA,EAAU,WAAW,QAClIA,EAAU,WAAWjB,CAAK,EACjB1C,EAAK,QAAQ,yBACtBA,EAAK,MAAO,EAGd,MAEF,IAAK,mBACFkH,EAAwB,KAAK,SAAS,KAAK,WAAa,MAAQA,IAA0B,QAAUA,EAAsB,UAAU,OAAO,kBAAkB,EAM9J,KACR,EACA,CAEA,CAQA,MAAME,GAAwB,GAExBC,GAAmB,IAEnBC,GAAmB,GAUzB,MAAMC,EAAS,CAIb,YAAYvH,EAAM,CAChB,KAAK,KAAOA,EAGZ,KAAK,SAAW,KAKhB,KAAK,GAAK,CACR,EAAG,EACH,EAAG,CACT,EAII,KAAK,GAAK,CACR,EAAG,EACH,EAAG,CACT,EAII,KAAK,OAAS,CACZ,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,OAAS,CACZ,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,QAAU,CACb,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,QAAU,CACb,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,SAAW,CACd,EAAG,EACH,EAAG,CACJ,EAKD,KAAK,aAAe,CAClB,EAAG,EACH,EAAG,CACJ,EAKD,KAAK,YAAc,CACjB,EAAG,EACH,EAAG,CACJ,EAGD,KAAK,iBAAmB,EAKxB,KAAK,iBAAmB,CAAE,EAG1B,KAAK,mBAAqB,iBAAkB,OAG5C,KAAK,qBAAuB,CAAC,CAAC,OAAO,aACrC,KAAK,cAAgB,KAAK,oBAAsB,KAAK,sBAAwB,UAAU,eAAiB,EAGxG,KAAK,iBAAmB,EAGxB,KAAK,cAAgB,EAGrB,KAAK,oBAAsB,GAC3B,KAAK,aAAe,GACpB,KAAK,WAAa,GAClB,KAAK,UAAY,GAGjB,KAAK,IAAM,KAKX,KAAK,UAAY,KAEZ,KAAK,gBAERA,EAAK,QAAQ,eAAiB,IAGhC,KAAK,KAAO,IAAIuD,GAAY,IAAI,EAChC,KAAK,WAAa,IAAIsC,GAAY,IAAI,EACtC,KAAK,WAAa,IAAIe,GAAW,IAAI,EACrC5G,EAAK,GAAG,aAAc,IAAM,CAC1BA,EAAK,OAAO,IAAIA,EAAK,WAAY,QAEjC,KAAK,SAAS,KAAK,IAAI,CAAC,EAEpB,KAAK,qBACP,KAAK,YAAY,UAAW,OAAQ,KAAM,QAAQ,EACzC,KAAK,oBACd,KAAK,YAAY,QAAS,QAAS,MAAO,QAAQ,EAS9CA,EAAK,aACPA,EAAK,WAAW,YAAc,IAAM,CAAE,EAEtCA,EAAK,WAAW,WAAa,IAAM,CAAE,IAGvC,KAAK,YAAY,QAAS,OAAQ,IAAI,CAE9C,CAAK,CACL,CAUE,YAAYwH,EAAMC,EAAMC,EAAIC,EAAQ,CAClC,KAAM,CACJ,KAAA3H,CACN,EAAQ,KACE,CACJ,OAAA4H,CACN,EAAQ5H,EACE6H,EAAcF,EAASH,EAAOG,EAAS,GAC7CC,EAAO,IAAI5H,EAAK,WAAYwH,EAAOC,EAEnC,KAAK,cAAc,KAAK,IAAI,CAAC,EAC7BG,EAAO,IAAI,OAAQJ,EAAO,OAE1B,KAAK,cAAc,KAAK,IAAI,CAAC,EAC7BI,EAAO,IAAI,OAAQJ,EAAOE,EAE1B,KAAK,YAAY,KAAK,IAAI,CAAC,EAEvBG,GACFD,EAAO,IAAI5H,EAAK,WAAY6H,EAE5B,KAAK,YAAY,KAAK,IAAI,CAAC,CAEjC,CAME,cAAcnJ,EAAG,CAOf,MAAMoJ,EAAiBpJ,EAAE,OAAS,aAAeA,EAAE,cAAgB,QAInE,GAAIoJ,GAAkBpJ,EAAE,OAAS,EAC/B,OAGF,KAAM,CACJ,KAAAsB,CACD,EAAG,KAEJ,GAAI,CAACA,EAAK,OAAO,OAAQ,CACvBtB,EAAE,eAAgB,EAClB,MACN,CAEQsB,EAAK,SAAS,cAAe,CAC/B,cAAetB,CAChB,CAAA,EAAE,mBAICoJ,IACF9H,EAAK,cAAa,EAGlB,KAAK,8BAA8BtB,EAAG,MAAM,GAG9CsB,EAAK,WAAW,QAAS,EAEzB,KAAK,cAActB,EAAG,MAAM,EAExB,KAAK,mBAAqB,IAC5B,KAAK,SAAW,KAGhBhC,EAAe,KAAK,QAAS,KAAK,EAAE,GAGlC,KAAK,iBAAmB,GAE1B,KAAK,eAAgB,EAErB,KAAK,aAAe,IAEpB,KAAK,aAAe,GAE1B,CAME,cAAcgC,EAAG,CACf,KAAK,8BAA8BA,EAAG,MAAM,EAEvC,KAAK,mBAIV,KAAK,cAAcA,EAAG,MAAM,EAExB,MAAK,KAAK,SAAS,cAAe,CACpC,cAAeA,CAChB,CAAA,EAAE,mBAIC,KAAK,mBAAqB,GAAK,CAAC,KAAK,YAClC,KAAK,UACR,KAAK,wBAAyB,EAI5B,KAAK,UAAY,CAAC,KAAK,aACrB,KAAK,YACP,KAAK,UAAY,GACjB,KAAK,WAAW,IAAK,GAGvB,KAAK,WAAa,GAElB,KAAK,eAAc,EAInB,KAAK,mBAAoB,EAEzB,KAAK,cAAgB,KAAK,MAE1B,KAAK,oBAAsB,GAC3BhC,EAAe,KAAK,YAAa,KAAK,EAAE,EACxC,KAAK,SAAS,EAAI,EAClB,KAAK,SAAS,EAAI,EAClB,KAAK,KAAK,MAAO,EAEjB,KAAK,aAAc,EAEnB,KAAK,eAAgB,IAEd,KAAK,iBAAmB,GAAK,CAAC,KAAK,YAC5C,KAAK,YAAa,EAElB,KAAK,UAAY,GAEjB,KAAK,mBAAoB,EAEzB,KAAK,WAAW,MAAO,EAEvB,KAAK,aAAc,EAEnB,KAAK,eAAgB,IAE3B,CAME,aAAc,CACR,KAAK,aACP,KAAK,WAAa,GAGb,KAAK,qBACR,KAAK,gBAAgB,EAAI,EAG3B,KAAK,KAAK,IAAK,EACf,KAAK,SAAW,KAEtB,CAME,YAAYgC,EAAG,CACR,KAAK,mBAIV,KAAK,cAAcA,EAAG,IAAI,EAEtB,MAAK,KAAK,SAAS,YAAa,CAClC,cAAeA,CAChB,CAAA,EAAE,mBAIC,KAAK,mBAAqB,IAC5B,KAAK,aAAc,EAEf,KAAK,WACP,KAAK,YAAa,EACT,CAAC,KAAK,WAAa,CAAC,KAAK,cAElC,KAAK,WAAWA,CAAC,GAIjB,KAAK,iBAAmB,GAAK,KAAK,YACpC,KAAK,UAAY,GACjB,KAAK,WAAW,IAAK,EAEjB,KAAK,mBAAqB,IAE5B,KAAK,SAAW,KAEhB,KAAK,mBAAoB,KAGjC,CAME,gBAAiB,EACX,KAAK,YAAc,KAAK,aAC1B,KAAK,gBAAiB,EAElB,KAAK,WAEFxB,EAAY,KAAK,GAAI,KAAK,MAAM,GACnC,KAAK,KAAK,OAAQ,GAKd,CAACA,EAAY,KAAK,GAAI,KAAK,MAAM,GAAK,CAACA,EAAY,KAAK,GAAI,KAAK,MAAM,IACzE,KAAK,WAAW,OAAQ,EAI9B,KAAK,kBAAmB,EAExB,KAAK,IAAM,sBAAsB,KAAK,eAAe,KAAK,IAAI,CAAC,EAErE,CASE,gBAAgB6E,EAAO,CACrB,MAAMgG,EAAO,KAAK,IAAK,EACjBjK,EAAWiK,EAAO,KAAK,cAEzBjK,EAAW,IAAM,CAACiE,IAItB,KAAK,SAAS,EAAI,KAAK,aAAa,IAAKjE,CAAQ,EACjD,KAAK,SAAS,EAAI,KAAK,aAAa,IAAKA,CAAQ,EACjD,KAAK,cAAgBiK,EACrBrL,EAAe,KAAK,YAAa,KAAK,EAAE,EACxC,KAAK,oBAAsB,GAC/B,CAOE,WAAWgC,EAAG,CACZ,KAAM,CACJ,WAAAoF,CACN,EAAQ,KAAK,KAET,GAAIA,EAAW,YAAa,CAG1BA,EAAW,YAAY,EAAG,EAAI,EAC9B,MACD,CAGD,GAAIpF,EAAE,KAAK,QAAQ,QAAQ,EAAI,EAC7B,OAIF,GAAIA,EAAE,OAAS,WAAaA,EAAE,cAAgB,QAAS,CACrD,KAAK,WAAW,MAAM,KAAK,QAASA,CAAC,EACrC,MACD,CAGD,MAAMsJ,EAAW,KAAK,KAAK,QAAQ,gBAAkBX,GAAmB,EAIpE,KAAK,WACP,KAAK,eAAc,EAGftK,EAAmB,KAAK,aAAc,KAAK,OAAO,EAAIuK,IACxD,KAAK,WAAW,UAAU,KAAK,QAAS5I,CAAC,IAG3ChC,EAAe,KAAK,aAAc,KAAK,OAAO,EAC9C,KAAK,UAAY,WAAW,IAAM,CAChC,KAAK,WAAW,IAAI,KAAK,QAASgC,CAAC,EAEnC,KAAK,eAAgB,CACtB,EAAEsJ,CAAQ,EAEjB,CAME,gBAAiB,CACX,KAAK,YACP,aAAa,KAAK,SAAS,EAC3B,KAAK,UAAY,KAEvB,CAWE,aAAapH,EAAM9C,EAAU,CAE3B,MAAMmK,EAAe,KAAK,GAAGrH,CAAI,EAAI,KAAK,YAAYA,CAAI,EAE1D,OAAI,KAAK,IAAIqH,CAAY,EAAI,GAAKnK,EAAW,EACpCmK,EAAenK,EAGjB,CACX,CAME,cAAe,CACT,KAAK,MACP,qBAAqB,KAAK,GAAG,EAC7B,KAAK,IAAM,KAEjB,CAQE,8BAA8BY,EAAGwJ,EAAa,CAChB,KAAK,KAAK,aAAa,sBAAuB,GAAMxJ,EAAGwJ,CAAW,GAG5FxJ,EAAE,eAAgB,CAExB,CAWE,cAAcA,EAAGwJ,EAAa,CAC5B,GAAI,KAAK,qBAAsB,CAC7B,MAAMC,EAENzJ,EAEM0J,EAAe,KAAK,iBAAiB,UAAUC,GAC5CA,EAAe,KAAOF,EAAa,SAC3C,EAEGD,IAAgB,MAAQE,EAAe,GAEzC,KAAK,iBAAiB,OAAOA,EAAc,CAAC,EACnCF,IAAgB,QAAUE,IAAiB,GAEpD,KAAK,iBAAiB,KAAK,KAAK,wBAAwBD,EAAc,CACpE,EAAG,EACH,EAAG,CACb,CAAS,CAAC,EACOC,EAAe,IAExB,KAAK,wBAAwBD,EAAc,KAAK,iBAAiBC,CAAY,CAAC,EAGhF,KAAK,iBAAmB,KAAK,iBAAiB,OAG1C,KAAK,iBAAmB,GAC1B1L,EAAe,KAAK,GAAI,KAAK,iBAAiB,CAAC,CAAC,EAG9C,KAAK,iBAAmB,GAC1BA,EAAe,KAAK,GAAI,KAAK,iBAAiB,CAAC,CAAC,CAExD,KAAW,CACL,MAAM4L,EAEN5J,EACA,KAAK,iBAAmB,EAEpB4J,EAAW,KAAK,QAAQ,OAAO,EAAI,GAGjCA,EAAW,SAAWA,EAAW,QAAQ,OAAS,IACpD,KAAK,wBAAwBA,EAAW,QAAQ,CAAC,EAAG,KAAK,EAAE,EAE3D,KAAK,mBAEDA,EAAW,QAAQ,OAAS,IAC9B,KAAK,wBAAwBA,EAAW,QAAQ,CAAC,EAAG,KAAK,EAAE,EAE3D,KAAK,sBAKT,KAAK,wBAEL5J,EAAG,KAAK,EAAE,EAENwJ,IAAgB,KAElB,KAAK,iBAAmB,EAExB,KAAK,mBAGf,CACA,CAME,mBAAoB,CAClBxL,EAAe,KAAK,OAAQ,KAAK,EAAE,EACnCA,EAAe,KAAK,OAAQ,KAAK,EAAE,CACvC,CAME,oBAAqB,CACnBA,EAAe,KAAK,QAAS,KAAK,EAAE,EACpCA,EAAe,KAAK,QAAS,KAAK,EAAE,EAEpC,KAAK,kBAAmB,CAC5B,CAIE,yBAA0B,CACxB,GAAI,KAAK,KAAK,WAAW,UAAS,EAEhC,KAAK,SAAW,QACX,CAEL,MAAM6L,EAAO,KAAK,IAAI,KAAK,GAAG,EAAI,KAAK,QAAQ,CAAC,EAAI,KAAK,IAAI,KAAK,GAAG,EAAI,KAAK,QAAQ,CAAC,EAEvF,GAAIA,IAAS,EAAG,CAEd,MAAMC,EAAcD,EAAO,EAAI,IAAM,IAEjC,KAAK,IAAI,KAAK,GAAGC,CAAW,EAAI,KAAK,QAAQA,CAAW,CAAC,GAAKpB,KAChE,KAAK,SAAWoB,EAE1B,CACA,CACA,CAYE,wBAAwB9J,EAAG5B,EAAG,CAC5B,OAAAA,EAAE,EAAI4B,EAAE,MAAQ,KAAK,KAAK,OAAO,EACjC5B,EAAE,EAAI4B,EAAE,MAAQ,KAAK,KAAK,OAAO,EAE7B,cAAeA,EACjB5B,EAAE,GAAK4B,EAAE,UACAA,EAAE,aAAe,SAC1B5B,EAAE,GAAK4B,EAAE,YAGJ5B,CACX,CAOE,SAAS4B,EAAG,CAEN,KAAK,KAAK,WAAW,UAAS,IAChCA,EAAE,eAAgB,EAClBA,EAAE,gBAAiB,EAEzB,CAEA,CAQA,MAAM+J,GAA2B,IAWjC,MAAMC,EAAW,CAIf,YAAY1I,EAAM,CAChB,KAAK,KAAOA,EACZ,KAAK,EAAI,EACT,KAAK,WAAa,EAGlB,KAAK,mBAAqB,EAG1B,KAAK,mBAAqB,EAG1B,KAAK,qBAAuB,GAG5B,KAAK,YAAc,CAAE,CACzB,CASE,OAAO2I,EAAc,CACnB,KAAM,CACJ,KAAA3I,CACN,EAAQ,KACE4I,EAAgB,KAAK,MAAM5I,EAAK,aAAa,EAAIA,EAAK,aAAa,EAAIA,EAAK,QAAQ,OAAO,EAI3F6I,EAAoBD,IAAkB,KAAK,WAE7CC,IACF,KAAK,WAAaD,EAClB,KAAK,OAAO,KAAK,eAAe,GAGlC,KAAK,YAAY,QAAQ,CAACE,EAAYzI,IAAU,CAC1CwI,GACFnL,EAAaoL,EAAW,IAAKzI,EAAQ,KAAK,sBAAwB,KAAK,UAAU,EAG/EsI,GAAgBG,EAAW,OAC7BA,EAAW,MAAM,OAAQ,CAEjC,CAAK,CACL,CAME,eAAgB,CAGd,KAAK,mBAAqB,EAC1B,KAAK,mBAAqB,EAE1B,KAAK,WAAa,EAElB,KAAK,qBAAuB,EAChC,CAOE,eAAgB,CACd,KAAK,YAAc,GAGnB,QAASC,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAC1B,MAAMtM,EAAKJ,EAAc,aAAc,MAAO,KAAK,KAAK,SAAS,EACjEI,EAAG,aAAa,OAAQ,OAAO,EAC/BA,EAAG,aAAa,uBAAwB,OAAO,EAC/CA,EAAG,aAAa,cAAe,MAAM,EAErCA,EAAG,MAAM,QAAUsM,IAAM,EAAI,QAAU,OACvC,KAAK,YAAY,KAAK,CACpB,GAAAtM,CAER,CAAO,CACP,CACA,CAOE,aAAc,CACZ,OAAO,KAAK,KAAK,YAAW,EAAK,CACrC,CAkBE,YAAY8L,EAAMS,EAASC,EAAW,CACpC,KAAM,CACJ,KAAAjJ,CACN,EAAQ,KACJ,IAAIkJ,EAAWlJ,EAAK,eAAiBuI,EACrC,MAAMY,EAAYnJ,EAAK,YAAa,EAEpC,GAAIA,EAAK,UAAW,CAClBkJ,EAAWlJ,EAAK,eAAekJ,CAAQ,EACvC,MAAME,GAAYb,EAAOY,GAAaA,EAElCC,GAAYD,EAAY,EAE1BZ,EAAOa,EAGPb,EAAOa,EAAWD,CAE1B,MACUD,EAAW,EACbA,EAAW,EACFA,GAAYC,IACrBD,EAAWC,EAAY,GAGzBZ,EAAOW,EAAWlJ,EAAK,eAGzBA,EAAK,eAAiBkJ,EACtB,KAAK,oBAAsBX,EAC3BvI,EAAK,WAAW,eAAgB,EAChC,MAAMqJ,EAAe,KAAK,cAAe,EAEzC,GAAI,CAACL,EACH,KAAK,OAAOK,CAAY,EACxB,KAAK,eAAgB,MAChB,CACLrJ,EAAK,WAAW,YAAY,CAC1B,aAAc,GACd,MAAO,KAAK,EACZ,IAAKqJ,EACL,SAAUJ,GAAa,EACvB,iBAAkB,GAClB,aAAc,EAEd,SAAUjM,GAAK,CACb,KAAK,OAAOA,CAAC,CACd,EACD,WAAY,IAAM,CAChB,KAAK,eAAgB,EACrBgD,EAAK,YAAa,CAC5B,CACA,CAAO,EACD,IAAIsJ,EAAWtJ,EAAK,eAAiBA,EAAK,UAE1C,GAAIA,EAAK,UAAW,CAClB,MAAMuJ,GAAgBD,EAAWH,GAAaA,EAE1CI,GAAgBJ,EAAY,EAE9BG,EAAWC,EAGXD,EAAWC,EAAeJ,CAE7B,CAIG,KAAK,IAAIG,CAAQ,EAAI,GACvB,KAAK,eAAgB,CAE7B,CAEI,MAAO,EAAQf,CACnB,CAQE,eAAgB,CACd,OAAO,KAAK,WAAa,KAAK,kBAClC,CAQE,WAAY,CACV,OAAO,KAAK,IAAM,KAAK,cAAe,CAC1C,CAME,gBAAiB,CACf,IAAIiB,EAEJ,KAAM,CACJ,KAAAxJ,CACN,EAAQ,KACEyJ,EAAqB,KAAK,mBAAqB,KAAK,mBAE1D,GAAI,CAACA,EACH,OAGF,KAAK,mBAAqB,KAAK,mBAC/BzJ,EAAK,UAAYA,EAAK,eACtB,IAAI0J,EAAU,KAAK,IAAID,CAAkB,EAGrCE,EAEAD,GAAW,IACb,KAAK,sBAAwBD,GAAsBA,EAAqB,EAAI,GAAK,GACjFC,EAAU,EAEV,KAAK,YAAY,QAAQZ,GAAc,CACrC,IAAIc,GAEHA,EAAoBd,EAAW,SAAW,MAAQc,IAAsB,QAAUA,EAAkB,QAAS,EAC9Gd,EAAW,MAAQ,MAC3B,CAAO,GAGH,QAASC,EAAI,EAAGA,EAAIW,EAASX,IACvBU,EAAqB,GACvBE,EAAa,KAAK,YAAY,MAAO,EAEjCA,IACF,KAAK,YAAY,CAAC,EAAIA,EAEtB,KAAK,uBACLjM,EAAaiM,EAAW,IAAK,KAAK,qBAAuB,GAAK,KAAK,UAAU,EAC7E3J,EAAK,WAAW2J,EAAY3J,EAAK,UAAY0J,EAAUX,EAAI,CAAC,KAG9DY,EAAa,KAAK,YAAY,IAAK,EAE/BA,IACF,KAAK,YAAY,QAAQA,CAAU,EAEnC,KAAK,uBACLjM,EAAaiM,EAAW,GAAI,KAAK,qBAAuB,KAAK,UAAU,EACvE3J,EAAK,WAAW2J,EAAY3J,EAAK,UAAY0J,EAAUX,EAAI,CAAC,IAW9D,KAAK,IAAI,KAAK,oBAAoB,EAAI,IAAM,CAAC,KAAK,cACpD,KAAK,cAAe,EACpB,KAAK,OAAQ,GAIf/I,EAAK,WAAW,WAAY,EAC5B,KAAK,YAAY,QAAQ,CAAC8I,EAAYC,IAAM,CACtCD,EAAW,OAEbA,EAAW,MAAM,YAAYC,IAAM,CAAC,CAE5C,CAAK,EACD/I,EAAK,WAAawJ,EAAqB,KAAK,YAAY,CAAC,KAAO,MAAQA,IAAuB,OAAS,OAASA,EAAmB,MACpIxJ,EAAK,cAAc,WAAWyJ,CAAkB,EAE5CzJ,EAAK,WACPA,EAAK,UAAU,oBAAqB,EAGtCA,EAAK,SAAS,QAAQ,CAC1B,CASE,OAAOhD,EAAG6M,EAAU,CAClB,GAAI,CAAC,KAAK,KAAK,QAAO,GAAMA,EAAU,CAEpC,IAAIC,GAAuB,KAAK,WAAa,KAAK,mBAAqB9M,GAAK,KAAK,WACjF8M,GAAuB,KAAK,KAAK,UACjC,MAAM/E,EAAQ,KAAK,MAAM/H,EAAI,KAAK,CAAC,GAE/B8M,EAAsB,GAAK/E,EAAQ,GAAK+E,GAAuB,KAAK,KAAK,YAAa,EAAG,GAAK/E,EAAQ,KACxG/H,EAAI,KAAK,EAAI+H,EAAQ0D,GAE7B,CAEI,KAAK,EAAIzL,EAEL,KAAK,KAAK,WACZU,EAAa,KAAK,KAAK,UAAWV,CAAC,EAGrC,KAAK,KAAK,SAAS,iBAAkB,CACnC,EAAAA,EACA,SAAU6M,GAAsD,EACtE,CAAK,CACL,CAEA,CASA,MAAME,GAAsB,CAC1B,OAAQ,GACR,EAAG,GACH,UAAW,GACX,QAAS,GACT,WAAY,GACZ,UAAW,GACX,IAAK,CACP,EAQMC,EAAsB,CAACC,EAAKC,IACzBA,EAAiBD,EAAMF,GAAoBE,CAAG,EAQvD,MAAME,EAAS,CAIb,YAAYnK,EAAM,CAChB,KAAK,KAAOA,EAGZ,KAAK,YAAc,GACnBA,EAAK,GAAG,aAAc,IAAM,CACtBA,EAAK,QAAQ,YAEVA,EAAK,QAAQ,mBAIhB,KAAK,WAAY,EAGnBA,EAAK,OAAO,IAAI,SAAU,UAE1B,KAAK,WAAW,KAAK,IAAI,CAAC,GAG5BA,EAAK,OAAO,IAAI,SAAU,UAE1B,KAAK,WAAW,KAAK,IAAI,CAAC,CAChC,CAAK,EACD,MAAMoK,EAEN,SAAS,cACTpK,EAAK,GAAG,UAAW,IAAM,CACnBA,EAAK,QAAQ,aAAeoK,GAAqB,KAAK,aACxDA,EAAkB,MAAO,CAEjC,CAAK,CACL,CAIE,YAAa,CACP,CAAC,KAAK,aAAe,KAAK,KAAK,UACjC,KAAK,KAAK,QAAQ,MAAO,EACzB,KAAK,YAAc,GAEzB,CAOE,WAAW1L,EAAG,CACZ,KAAM,CACJ,KAAAsB,CACN,EAAQ,KAQJ,GANIA,EAAK,SAAS,UAAW,CAC3B,cAAetB,CAChB,CAAA,EAAE,kBAICD,EAAeC,CAAC,EAIlB,OAKF,IAAI2L,EAGAzJ,EACA0J,EAAY,GAChB,MAAMJ,EAAkB,QAASxL,EAEjC,OAAQwL,EAAiBxL,EAAE,IAAMA,EAAE,QAAO,CACxC,KAAKsL,EAAoB,SAAUE,CAAc,EAC3ClK,EAAK,QAAQ,SACfqK,EAAgB,SAGlB,MAEF,KAAKL,EAAoB,IAAKE,CAAc,EAC1CG,EAAgB,aAChB,MAEF,KAAKL,EAAoB,YAAaE,CAAc,EAClDtJ,EAAO,IACP,MAEF,KAAKoJ,EAAoB,UAAWE,CAAc,EAChDtJ,EAAO,IACP,MAEF,KAAKoJ,EAAoB,aAAcE,CAAc,EACnDtJ,EAAO,IACP0J,EAAY,GACZ,MAEF,KAAKN,EAAoB,YAAaE,CAAc,EAClDI,EAAY,GACZ1J,EAAO,IACP,MAEF,KAAKoJ,EAAoB,MAAOE,CAAc,EAC5C,KAAK,WAAY,EAEjB,KACH,CAGD,GAAItJ,EAAM,CAERlC,EAAE,eAAgB,EAClB,KAAM,CACJ,UAAAiF,CACR,EAAU3D,EAEAA,EAAK,QAAQ,WAAaY,IAAS,KAAOZ,EAAK,YAAa,EAAG,EACjEqK,EAAgBC,EAAY,OAAS,OAC5B3G,GAAaA,EAAU,cAAgBA,EAAU,WAAW,MAKrEA,EAAU,IAAI/C,CAAI,GAAK0J,EAAY,IAAM,GACzC3G,EAAU,MAAMA,EAAU,IAAI,EAAGA,EAAU,IAAI,CAAC,EAExD,CAEQ0G,IACF3L,EAAE,eAAc,EAEhBsB,EAAKqK,CAAa,EAAG,EAE3B,CASE,WAAW3L,EAAG,CACZ,KAAM,CACJ,SAAA6L,CACD,EAAG,KAAK,KAELA,GAAY,WAAa7L,EAAE,QAAU6L,IAAa7L,EAAE,QAAU,CAAC6L,EAAS,SAE5E7L,EAAE,MAAM,GAEN6L,EAAS,MAAO,CAEtB,CAEA,CAEA,MAAMC,GAAiB,2BAkBvB,MAAMC,EAAa,CAMjB,YAAYC,EAAO,CACjB,IAAIC,EAEJ,KAAK,MAAQD,EACb,KAAM,CACJ,OAAAtL,EACA,WAAAwL,EACA,UAAAC,EACA,SAAAC,EAAW,IAAM,CAAE,EACnB,SAAAhN,EAAW,IACX,OAAAiN,EAASP,EACf,EAAQE,EACJ,KAAK,SAAWI,EAEhB,MAAMjN,EAAOgN,EAAY,YAAc,UACjCpN,GAAakN,EAAcD,EAAM7M,CAAI,KAAO,MAAQ8M,IAAgB,OAASA,EAAc,GAGjG,KAAK,QAAUvL,EAGf,KAAK,YAAcwL,EAGnB,KAAK,UAAY,GAGjB,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EASvD,KAAK,eAAiB,WAAW,IAAM,CACrChN,EAAmBwB,EAAQvB,EAAMC,EAAUiN,CAAM,EACjD,KAAK,eAAiB,WAAW,IAAM,CACrC3L,EAAO,iBAAiB,gBAAiB,KAAK,iBAAkB,EAAK,EACrEA,EAAO,iBAAiB,mBAAoB,KAAK,iBAAkB,EAAK,EAKxE,KAAK,eAAiB,WAAW,IAAM,CACrC,KAAK,mBAAoB,CACnC,EAAWtB,EAAW,GAAG,EACjBsB,EAAO,MAAMvB,CAAI,EAAIJ,CACtB,EAAE,EAAE,CACN,EAAE,CAAC,CACR,CAOE,iBAAiBiB,EAAG,CACdA,EAAE,SAAW,KAAK,SACpB,KAAK,mBAAoB,CAE/B,CAME,oBAAqB,CACd,KAAK,YACR,KAAK,UAAY,GACjB,KAAK,SAAU,EAEX,KAAK,aACP,KAAK,YAAa,EAGvB,CAGD,SAAU,CACJ,KAAK,gBACP,aAAa,KAAK,cAAc,EAGlCP,EAAsB,KAAK,OAAO,EAElC,KAAK,QAAQ,oBAAoB,gBAAiB,KAAK,iBAAkB,EAAK,EAE9E,KAAK,QAAQ,oBAAoB,mBAAoB,KAAK,iBAAkB,EAAK,EAE5E,KAAK,WACR,KAAK,mBAAoB,CAE/B,CAEA,CAEA,MAAM6M,GAA4B,GAC5BC,GAAwB,IAK9B,MAAMC,EAAY,CAgBhB,YAAY7H,EAAiBoB,EAAc0G,EAAkB,CAC3D,KAAK,SAAW9H,EAAkB,IAGlC,KAAK,cAAgBoB,GAAgBwG,GAErC,KAAK,kBAAoBE,GAAoBH,GAC7C,KAAK,iBAAmB,KAAK,kBAEzB,KAAK,cAAgB,IACvB,KAAK,kBAAoB,KAAK,KAAK,EAAI,KAAK,cAAgB,KAAK,aAAa,EAEpF,CASE,UAAUI,EAAeC,EAAW,CAKlC,IAAIpD,EAAe,EACfqD,EACJD,GAAa,IACb,MAAME,EAAoB,KAAK,IAAM,CAAC,KAAK,cAAgB,KAAK,kBAAoBF,GAEpF,GAAI,KAAK,gBAAkB,EACzBC,EAAQ,KAAK,SAAW,KAAK,kBAAoBF,EACjDnD,GAAgBmD,EAAgBE,EAAQD,GAAaE,EACrD,KAAK,SAAWtD,EAAe,CAAC,KAAK,kBAAoBqD,EAAQC,UACxD,KAAK,cAAgB,EAAG,CACjCD,EAAQ,EAAI,KAAK,kBAAoB,KAAK,cAAgB,KAAK,kBAAoBF,EAAgB,KAAK,UACxG,MAAMI,EAAa,KAAK,IAAI,KAAK,iBAAmBH,CAAS,EACvDI,EAAa,KAAK,IAAI,KAAK,iBAAmBJ,CAAS,EAC7DpD,EAAesD,GAAqBH,EAAgBI,EAAaF,EAAQG,GACzE,KAAK,SAAWxD,EAAe,CAAC,KAAK,kBAAoB,KAAK,cAAgBsD,GAAqB,CAAC,KAAK,iBAAmBH,EAAgBK,EAAa,KAAK,iBAAmBH,EAAQE,EAC1L,CAGD,OAAOvD,CACX,CAEA,CAiBA,MAAMyD,EAAgB,CAIpB,YAAYhB,EAAO,CACjB,KAAK,MAAQA,EACb,KAAK,KAAO,EACZ,KAAM,CACJ,MAAAiB,EACA,IAAAC,EACA,SAAA/H,EACA,SAAAgI,EACA,WAAAjB,EACA,SAAAE,EAAW,IAAM,CAAE,EACnB,aAAArG,EACA,iBAAA0G,CACN,EAAQT,EACJ,KAAK,SAAWI,EAChB,MAAMgB,EAAQ,IAAIZ,GAAYrH,EAAUY,EAAc0G,CAAgB,EACtE,IAAIY,EAAW,KAAK,IAAK,EACrBX,EAAgBO,EAAQC,EAE5B,MAAMI,EAAgB,IAAM,CACtB,KAAK,OACPZ,EAAgBU,EAAM,UAAUV,EAAe,KAAK,IAAG,EAAKW,CAAQ,EAEhE,KAAK,IAAIX,CAAa,EAAI,GAAK,KAAK,IAAIU,EAAM,QAAQ,EAAI,IAE5DD,EAASD,CAAG,EAERhB,GACFA,EAAY,EAGd,KAAK,SAAU,IAEfmB,EAAW,KAAK,IAAK,EACrBF,EAAST,EAAgBQ,CAAG,EAC5B,KAAK,KAAO,sBAAsBI,CAAa,GAGpD,EAED,KAAK,KAAO,sBAAsBA,CAAa,CAChD,CAGD,SAAU,CACJ,KAAK,MAAQ,GACf,qBAAqB,KAAK,IAAI,EAGhC,KAAK,KAAO,CAChB,CAEA,CAsBA,MAAMC,EAAW,CACf,aAAc,CAEZ,KAAK,iBAAmB,CAAE,CAC9B,CAME,YAAYvB,EAAO,CACjB,KAAK,OAAOA,EAAO,EAAI,CAC3B,CAME,gBAAgBA,EAAO,CACrB,KAAK,OAAOA,CAAK,CACrB,CASE,OAAOA,EAAOwB,EAAU,CACtB,MAAMC,EAAYD,EAAW,IAAIR,GAEjChB,CAAK,EAAI,IAAID,GAEbC,CAAK,EACL,YAAK,iBAAiB,KAAKyB,CAAS,EAEpCA,EAAU,SAAW,IAAM,KAAK,KAAKA,CAAS,EAEvCA,CACX,CAME,KAAKA,EAAW,CACdA,EAAU,QAAS,EACnB,MAAM9L,EAAQ,KAAK,iBAAiB,QAAQ8L,CAAS,EAEjD9L,EAAQ,IACV,KAAK,iBAAiB,OAAOA,EAAO,CAAC,CAE3C,CAEE,SAAU,CAER,KAAK,iBAAiB,QAAQ8L,GAAa,CACzCA,EAAU,QAAS,CACzB,CAAK,EACD,KAAK,iBAAmB,CAAE,CAC9B,CAME,YAAa,CACX,KAAK,iBAAmB,KAAK,iBAAiB,OAAOA,GAC/CA,EAAU,MAAM,OAClBA,EAAU,QAAS,EACZ,IAGF,EACR,CACL,CAEE,gBAAiB,CACf,KAAK,iBAAmB,KAAK,iBAAiB,OAAOA,GAC/CA,EAAU,MAAM,cAClBA,EAAU,QAAS,EACZ,IAGF,EACR,CACL,CAeE,cAAe,CACb,OAAO,KAAK,iBAAiB,KAAKA,GACzBA,EAAU,MAAM,KACxB,CACL,CAEA,CAQA,MAAMC,EAAY,CAIhB,YAAYpM,EAAM,CAChB,KAAK,KAAOA,EACZA,EAAK,OAAO,IAAIA,EAAK,QAAS,QAE9B,KAAK,SAAS,KAAK,IAAI,CAAC,CAC5B,CAOE,SAAStB,EAAG,CACVA,EAAE,eAAgB,EAClB,KAAM,CACJ,UAAAiF,CACD,EAAG,KAAK,KACT,GAAI,CACF,OAAA0I,EACA,OAAAC,CACN,EAAQ5N,EAEJ,GAAKiF,GAID,MAAK,KAAK,SAAS,QAAS,CAC9B,cAAejF,CAChB,CAAA,EAAE,iBAIH,GAAIA,EAAE,SAAW,KAAK,KAAK,QAAQ,aAEjC,GAAIiF,EAAU,aAAc,CAC1B,IAAIhB,EAAa,CAAC2J,EAEd5N,EAAE,YAAc,EAGlBiE,GAAc,IAEdA,GAAcjE,EAAE,UAAY,EAAI,KAGlCiE,EAAa,GAAKA,EAClB,MAAMP,EAAgBuB,EAAU,cAAgBhB,EAChDgB,EAAU,OAAOvB,EAAe,CAC9B,EAAG1D,EAAE,QACL,EAAGA,EAAE,OACf,CAAS,CACT,OAGUiF,EAAU,eACRjF,EAAE,YAAc,IAIlB2N,GAAU,GACVC,GAAU,IAGZ3I,EAAU,MAAMA,EAAU,IAAI,EAAI0I,EAAQ1I,EAAU,IAAI,EAAI2I,CAAM,EAG1E,CAEA,CAyCA,SAASC,GAAeC,EAAU,CAChC,GAAI,OAAOA,GAAa,SAQtB,OAAOA,EAGT,GAAI,CAACA,GAAY,CAACA,EAAS,YACzB,MAAO,GAGT,MAAMC,EAAUD,EAChB,IAAIE,EAAM,wFAEV,OAAAA,EAAMA,EAAI,MAAM,IAAI,EAAE,KAEtBD,EAAQ,MAAQ,EAAE,EAMdA,EAAQ,YACVC,GAAO,8CAAgDD,EAAQ,UAAY,OAG7EC,GAAOD,EAAQ,MACfC,GAAO,SACAA,CACT,CAEA,MAAMC,EAAU,CAKd,YAAY3M,EAAM4B,EAAM,CACtB,IAAIgL,EAEJ,MAAMC,EAAOjL,EAAK,MAAQA,EAAK,UAC/B,IAAIkL,EAAclL,EAAK,KAEvB,GAAI5B,EAAK,QAAQ6M,CAAI,IAAM,GAEzB,OAKE,OAAO7M,EAAK,QAAQ6M,EAAO,KAAK,GAAM,WAMxCC,EAAc9M,EAAK,QAAQ6M,EAAO,KAAK,GAGzC7M,EAAK,SAAS,kBAAmB,CAC/B,KAAA4B,CACN,CAAK,EACD,IAAItF,EAAY,GAEZsF,EAAK,UACPtF,GAAa,gBACbA,GAAasF,EAAK,WAAa,iBAAiBA,EAAK,IAAI,IAEzDtF,GAAasF,EAAK,WAAa,SAASA,EAAK,IAAI,GAGnD,IAAIrF,EAAUqF,EAAK,SAAWA,EAAK,SAAW,SAAWA,EAAK,SAAW,MACzErF,EAEAA,EAAQ,YAAa,EAGrB,MAAMwQ,EAAU1Q,EAAcC,EAAWC,CAAO,EAEhD,GAAIqF,EAAK,SAAU,CACbrF,IAAY,WAEdwQ,EAAQ,KAAO,UAGjB,GAAI,CACF,MAAAC,CACR,EAAUpL,EACJ,KAAM,CACJ,UAAAqL,CACD,EAAGrL,EAEA,OAAO5B,EAAK,QAAQ6M,EAAO,OAAO,GAAM,WAE1CG,EAAQhN,EAAK,QAAQ6M,EAAO,OAAO,GAGjCG,IACFD,EAAQ,MAAQC,GAGlB,MAAME,EAAWD,GAAaD,EAE1BE,GACFH,EAAQ,aAAa,aAAcG,CAAQ,CAEnD,CAEIH,EAAQ,UAAYR,GAAeO,CAAW,EAE1ClL,EAAK,QACPA,EAAK,OAAOmL,EAAS/M,CAAI,EAGvB4B,EAAK,UACPmL,EAAQ,QAAUrO,GAAK,CACjB,OAAOkD,EAAK,SAAY,SAE1B5B,EAAK4B,EAAK,OAAO,EAAG,EACX,OAAOA,EAAK,SAAY,YACjCA,EAAK,QAAQlD,EAAGqO,EAAS/M,CAAI,CAEhC,GAIH,MAAMmN,EAAWvL,EAAK,UAAY,MAGlC,IAAIwL,EAAYpN,EAAK,QAEjBmN,IAAa,OACVnN,EAAK,SACRA,EAAK,OAAS3D,EAAc,oCAAqC,MAAO2D,EAAK,UAAU,GAGzFoN,EAAYpN,EAAK,SAIjB+M,EAAQ,UAAU,IAAI,qBAAqB,EAEvCI,IAAa,YACfC,EAAYpN,EAAK,cAIpB4M,EAAaQ,KAAe,MAAQR,IAAe,QAAUA,EAAW,YAAY5M,EAAK,aAAa,YAAa+M,EAASnL,CAAI,CAAC,CACtI,CAEA,CAgBA,SAASyL,EAAgBN,EAAS/M,EAAMsN,EAAc,CACpDP,EAAQ,UAAU,IAAI,qBAAqB,EAE3CA,EAAQ,aAAa,gBAAiB,aAAa,EACnD/M,EAAK,GAAG,SAAU,IAAM,CACjBA,EAAK,QAAQ,OACZsN,EAEFP,EAAQ,SAAW,EAAE/M,EAAK,UAAYA,EAAK,YAAa,EAAG,GAG3D+M,EAAQ,SAAW,EAAE/M,EAAK,UAAY,GAG9C,CAAG,CACH,CAIA,MAAMuN,GAAY,CAChB,KAAM,YACN,UAAW,4BACX,MAAO,WACP,MAAO,GACP,SAAU,GACV,SAAU,UACV,KAAM,CACJ,YAAa,GACb,KAAM,GACN,MAAO,4EACP,UAAW,iBACZ,EACD,QAAS,OACT,OAAQF,CACV,EAGMG,GAAY,CAChB,KAAM,YACN,UAAW,4BACX,MAAO,OACP,MAAO,GACP,SAAU,GACV,SAAU,UACV,KAAM,CACJ,YAAa,GACb,KAAM,GACN,MAAO,uCACP,UAAW,iBACZ,EACD,QAAS,OACT,OAAQ,CAAC/Q,EAAIuD,IAAS,CACpBqN,EAAgB5Q,EAAIuD,EAAM,EAAI,CAClC,CACA,EAGMyN,GAAc,CAClB,KAAM,QACN,MAAO,QACP,MAAO,GACP,SAAU,GACV,KAAM,CACJ,YAAa,GACb,MAAO,wFACP,UAAW,iBACZ,EACD,QAAS,OACX,EAGMC,GAAa,CACjB,KAAM,OACN,MAAO,OACP,MAAO,GACP,SAAU,GACV,KAAM,CACJ,YAAa,GAEb,MAAO,uPACP,UAAW,gBACZ,EACD,QAAS,YACX,EAGMC,GAAmB,CACvB,KAAM,YACN,SAAU,MACV,MAAO,EACP,KAAM,CACJ,YAAa,GAEb,MAAO,kIACP,UAAW,mBACZ,EACD,OAAQ,CAACC,EAAkB5N,IAAS,CAElC,IAAI6N,EAGAC,EAAe,KAMnB,MAAMC,EAAuB,CAACzR,EAAW0R,IAAQ,CAC/CJ,EAAiB,UAAU,OAAO,oBAAsBtR,EAAW0R,CAAG,CACvE,EAMKC,EAAyBC,GAAW,CACpCL,IAAcK,IAChBL,EAAYK,EACZH,EAAqB,SAAUG,CAAO,EAEzC,EAEKC,EAA4B,IAAM,CACtC,IAAIC,EAEJ,GAAI,GAAGA,EAAkBpO,EAAK,aAAe,MAAQoO,IAAoB,QAAUA,EAAgB,QAAQ,UAAW,GAAG,CACvHH,EAAuB,EAAK,EAExBH,IACF,aAAaA,CAAY,EACzBA,EAAe,MAGjB,MACR,CAEWA,IAEHA,EAAe,WAAW,IAAM,CAC9B,IAAIO,EAEJJ,EAAuB,GAAS,GAAAI,EAAmBrO,EAAK,aAAe,MAAQqO,IAAqB,SAAkBA,EAAiB,QAAQ,UAAW,EAAC,EAC3JP,EAAe,IACzB,EAAW9N,EAAK,QAAQ,cAAc,EAEjC,EAEDA,EAAK,GAAG,SAAUmO,CAAyB,EAC3CnO,EAAK,GAAG,eAAgBtB,GAAK,CACvBsB,EAAK,YAActB,EAAE,OACvByP,EAA2B,CAEnC,CAAK,EAEGnO,EAAK,KACPA,EAAK,GAAG,0BAA4BmO,EAE1C,CACA,EAGMG,GAAmB,CACvB,KAAM,UACN,MAAO,EACP,OAAQ,CAACC,EAAgBvO,IAAS,CAChCA,EAAK,GAAG,SAAU,IAAM,CACtBuO,EAAe,UAAYvO,EAAK,UAAY,EAAIA,EAAK,QAAQ,kBAAoBA,EAAK,YAAa,CACzG,CAAK,CACL,CACA,EAgBA,SAASwO,EAAY/R,EAAIgS,EAAY,CACnChS,EAAG,UAAU,OAAO,kBAAmBgS,CAAU,CACnD,CAEA,MAAMC,EAAG,CAIP,YAAY1O,EAAM,CAChB,KAAK,KAAOA,EACZ,KAAK,aAAe,GAGpB,KAAK,eAAiB,CAAE,EAGxB,KAAK,MAAQ,CAAE,EAGf,KAAK,0BAA4B,IAAM,CAAE,EAOzC,KAAK,sBAAwB,MACjC,CAEE,MAAO,CACL,KAAM,CACJ,KAAAA,CACN,EAAQ,KACJ,KAAK,aAAe,GACpB,KAAK,eAAiB,CAACyN,GAAaF,GAAWC,GAAWE,GAAYC,GAAkBW,EAAgB,EACxGtO,EAAK,SAAS,YAAY,EAE1B,KAAK,eAAe,KAAK,CAAC2O,EAAGC,KAEnBD,EAAE,OAAS,IAAMC,EAAE,OAAS,EACrC,EACD,KAAK,MAAQ,CAAE,EACf,KAAK,aAAe,GACpB,KAAK,eAAe,QAAQC,GAAiB,CAC3C,KAAK,gBAAgBA,CAAa,CACxC,CAAK,EACD7O,EAAK,GAAG,SAAU,IAAM,CACtB,IAAI8O,GAEHA,EAAgB9O,EAAK,WAAa,MAAQ8O,IAAkB,QAAUA,EAAc,UAAU,OAAO,kBAAmB9O,EAAK,YAAW,IAAO,CAAC,CACvJ,CAAK,EACDA,EAAK,GAAG,gBAAiB,IAAM,KAAK,iBAAgB,CAAE,CAC1D,CAME,gBAAgB+O,EAAa,CACvB,KAAK,aACP,KAAK,MAAM,KAAK,IAAIpC,GAAU,KAAK,KAAMoC,CAAW,CAAC,EAErD,KAAK,eAAe,KAAKA,CAAW,CAE1C,CASE,kBAAmB,CACjB,KAAM,CACJ,SAAAxE,EACA,UAAA5G,EACA,QAAA5D,CACD,EAAG,KAAK,KAET,GAAI,KAAK,KAAK,OAAO,WAAa,CAACwK,GAAY,CAAC5G,EAC9C,OAGF,GAAI,CACF,cAAAhD,CACD,EAAGgD,EAMJ,GAJK,KAAK,KAAK,OAAO,SACpBhD,EAAgBgD,EAAU,WAAW,SAGnChD,IAAkB,KAAK,sBACzB,OAGF,KAAK,sBAAwBA,EAC7B,MAAMqO,EAAoBrL,EAAU,WAAW,QAAUA,EAAU,WAAW,UAE9E,GAAI,KAAK,IAAIqL,CAAiB,EAAI,KAAQ,CAACrL,EAAU,aAAc,CAEjE6K,EAAYjE,EAAU,EAAK,EAC3BA,EAAS,UAAU,OAAO,oBAAoB,EAC9C,MACN,CAEIA,EAAS,UAAU,IAAI,oBAAoB,EAC3C,MAAM0E,EAAqBtO,IAAkBgD,EAAU,WAAW,QAAUA,EAAU,WAAW,UAAYA,EAAU,WAAW,QAClI6K,EAAYjE,EAAU0E,GAAsBtO,CAAa,GAErDZ,EAAQ,mBAAqB,QAAUA,EAAQ,mBAAqB,kBACtEwK,EAAS,UAAU,IAAI,qBAAqB,CAElD,CAEA,CAYA,SAAS2E,GAAmBzS,EAAI,CAC9B,MAAM0S,EAAgB1S,EAAG,sBAAuB,EAChD,MAAO,CACL,EAAG0S,EAAc,KACjB,EAAGA,EAAc,IACjB,EAAGA,EAAc,KAClB,CACH,CASA,SAASC,GAA0B3S,EAAI4S,EAAYC,EAAa,CAC9D,MAAMH,EAAgB1S,EAAG,wBAGnB6E,EAAS6N,EAAc,MAAQE,EAC/B9N,EAAS4N,EAAc,OAASG,EAChCC,EAAgBjO,EAASC,EAASD,EAASC,EAC3CiO,GAAWL,EAAc,MAAQE,EAAaE,GAAiB,EAC/DE,GAAWN,EAAc,OAASG,EAAcC,GAAiB,EASjErL,EAAS,CACb,EAAGiL,EAAc,KAAOK,EACxB,EAAGL,EAAc,IAAMM,EACvB,EAAGJ,EAAaE,CACpB,EAGE,OAAArL,EAAO,UAAY,CACjB,EAAGiL,EAAc,MACjB,EAAGA,EAAc,OACjB,EAAGK,EACH,EAAGC,CACJ,EACMvL,CACT,CAYA,SAASwL,GAAerP,EAAOD,EAAUuP,EAAU,CAEjD,MAAMhJ,EAAQgJ,EAAS,SAAS,cAAe,CAC7C,MAAAtP,EACA,SAAAD,EACA,SAAAuP,CACJ,CAAG,EAED,GAAIhJ,EAAM,YAER,OAAOA,EAAM,YAGf,KAAM,CACJ,QAAAoG,CACJ,EAAM3M,EAGJ,IAAIwP,EAGAC,EAEJ,GAAI9C,GAAW4C,EAAS,QAAQ,gBAAkB,GAAO,CACvD,MAAMG,EAAgBH,EAAS,QAAQ,eAAiB,MACxDE,EAAY9C,EAAQ,QAAQ+C,CAAa,EAAI/C,EAE7CA,EAAQ,cAAc+C,CAAa,CACvC,CAEE,OAAAD,EAAYF,EAAS,aAAa,UAAWE,EAAWzP,EAAUC,CAAK,EAEnEwP,IACGzP,EAAS,aAGZwP,EAAcR,GAA0BS,EAAWzP,EAAS,OAASA,EAAS,GAAK,EAAGA,EAAS,QAAUA,EAAS,GAAK,CAAC,EAFxHwP,EAAcV,GAAmBW,CAAS,GAMvCF,EAAS,aAAa,cAAeC,EAAaxP,EAAUC,CAAK,CAC1E,CA4NA,MAAM0P,EAAgB,CAKpB,YAAY1Q,EAAM2Q,EAAS,CACzB,KAAK,KAAO3Q,EACZ,KAAK,iBAAmB,GAEpB2Q,GACF,OAAO,OAAO,KAAMA,CAAO,CAEjC,CAEE,gBAAiB,CACf,KAAK,iBAAmB,EAC5B,CAEA,CAOA,MAAMC,EAAU,CACd,aAAc,CAIZ,KAAK,WAAa,CAAE,EAKpB,KAAK,SAAW,CAAE,EAGlB,KAAK,KAAO,OAGZ,KAAK,QAAU,MACnB,CASE,UAAUpD,EAAMqD,EAAIC,EAAW,IAAK,CAClC,IAAIC,EAAqBC,EAAsBC,EAE1C,KAAK,SAASzD,CAAI,IACrB,KAAK,SAASA,CAAI,EAAI,CAAE,IAGzBuD,EAAsB,KAAK,SAASvD,CAAI,KAAO,MAAQuD,IAAwB,QAAUA,EAAoB,KAAK,CACjH,GAAAF,EACA,SAAAC,CACN,CAAK,GACAE,EAAuB,KAAK,SAASxD,CAAI,KAAO,MAAQwD,IAAyB,QAAUA,EAAqB,KAAK,CAACE,EAAIC,IAAOD,EAAG,SAAWC,EAAG,QAAQ,GAC1JF,EAAa,KAAK,QAAU,MAAQA,IAAe,QAAUA,EAAW,UAAUzD,EAAMqD,EAAIC,CAAQ,CACzG,CAQE,aAAatD,EAAMqD,EAAI,CACjB,KAAK,SAASrD,CAAI,IAEpB,KAAK,SAASA,CAAI,EAAI,KAAK,SAASA,CAAI,EAAE,OAAO4D,GAAUA,EAAO,KAAOP,CAAE,GAGzE,KAAK,MACP,KAAK,KAAK,aAAarD,EAAMqD,CAAE,CAErC,CASE,aAAarD,KAAS6D,EAAM,CAC1B,IAAIC,EAEJ,OAACA,EAAuB,KAAK,SAAS9D,CAAI,KAAO,MAAQ8D,IAAyB,QAAUA,EAAqB,QAAQF,GAAU,CAEjIC,EAAK,CAAC,EAAID,EAAO,GAAG,MAAM,KAAMC,CAAI,CAC1C,CAAK,EACMA,EAAK,CAAC,CACjB,CAQE,GAAG7D,EAAMqD,EAAI,CACX,IAAIU,EAAuBC,EAEtB,KAAK,WAAWhE,CAAI,IACvB,KAAK,WAAWA,CAAI,EAAI,CAAE,IAG3B+D,EAAwB,KAAK,WAAW/D,CAAI,KAAO,MAAQ+D,IAA0B,QAAUA,EAAsB,KAAKV,CAAE,GAI5HW,EAAc,KAAK,QAAU,MAAQA,IAAgB,QAAUA,EAAY,GAAGhE,EAAMqD,CAAE,CAC3F,CAQE,IAAIrD,EAAMqD,EAAI,CACZ,IAAIY,EAEA,KAAK,WAAWjE,CAAI,IAEtB,KAAK,WAAWA,CAAI,EAAI,KAAK,WAAWA,CAAI,EAAE,OAAOvN,GAAY4Q,IAAO5Q,CAAQ,IAGjFwR,EAAc,KAAK,QAAU,MAAQA,IAAgB,QAAUA,EAAY,IAAIjE,EAAMqD,CAAE,CAC5F,CASE,SAASrD,EAAMmD,EAAS,CACtB,IAAIe,EAEJ,GAAI,KAAK,KACP,OAAO,KAAK,KAAK,SAASlE,EAAMmD,CAAO,EAGzC,MAAMrJ,EAEN,IAAIoJ,GAAgBlD,EAAMmD,CAAO,EACjC,OAACe,EAAyB,KAAK,WAAWlE,CAAI,KAAO,MAAQkE,IAA2B,QAAUA,EAAuB,QAAQzR,GAAY,CAC3IA,EAAS,KAAK,KAAMqH,CAAK,CAC/B,CAAK,EACMA,CACX,CAEA,CAEA,MAAMqK,EAAY,CAKhB,YAAYC,EAAU7D,EAAW,CAO/B,GAFA,KAAK,QAAU/Q,EAAc,mCAAoC4U,EAAW,MAAQ,MAAO7D,CAAS,EAEhG6D,EAAU,CACZ,MAAMC,EAEN,KAAK,QACLA,EAAM,SAAW,QACjBA,EAAM,IAAM,GACZA,EAAM,IAAMD,EACZC,EAAM,aAAa,OAAQ,cAAc,CAC/C,CAEI,KAAK,QAAQ,aAAa,cAAe,MAAM,CACnD,CAOE,iBAAiBjP,EAAOC,EAAQ,CACzB,KAAK,UAIN,KAAK,QAAQ,UAAY,OAI3BlE,EAAe,KAAK,QAAS,IAAK,MAAM,EACxC,KAAK,QAAQ,MAAM,gBAAkB,MACrC,KAAK,QAAQ,MAAM,UAAYT,EAAkB,EAAG,EAAG0E,EAAQ,GAAG,GAElEjE,EAAe,KAAK,QAASiE,EAAOC,CAAM,EAEhD,CAEE,SAAU,CACR,IAAIiP,GAECA,EAAgB,KAAK,WAAa,MAAQA,IAAkB,QAAUA,EAAc,YACvF,KAAK,QAAQ,OAAQ,EAGvB,KAAK,QAAU,IACnB,CAEA,CAUA,MAAMC,EAAQ,CAMZ,YAAYhR,EAAUuP,EAAUtP,EAAO,CACrC,KAAK,SAAWsP,EAChB,KAAK,KAAOvP,EACZ,KAAK,MAAQC,EAGb,KAAK,QAAU,OAGf,KAAK,YAAc,OAGnB,KAAK,MAAQ,OACb,KAAK,oBAAsB,EAC3B,KAAK,qBAAuB,EAC5B,KAAK,MAAQ,OAAO,KAAK,KAAK,CAAC,GAAK,OAAO,KAAK,KAAK,KAAK,GAAK,EAC/D,KAAK,OAAS,OAAO,KAAK,KAAK,CAAC,GAAK,OAAO,KAAK,KAAK,MAAM,GAAK,EACjE,KAAK,WAAa,GAClB,KAAK,SAAW,GAChB,KAAK,WAAa,GAGlB,KAAK,MAAQ7B,EAAW,KAEpB,KAAK,KAAK,KACZ,KAAK,KAAO,KAAK,KAAK,KACb,KAAK,KAAK,IACnB,KAAK,KAAO,QAEZ,KAAK,KAAO,OAGd,KAAK,SAAS,SAAS,cAAe,CACpC,QAAS,IACf,CAAK,CACL,CAEE,mBAAoB,CACd,KAAK,aAAe,CAAC,KAAK,gBAAe,GAE3C,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,QAAS,EAC1B,KAAK,YAAc,OAEtB,EAAE,GAAI,CAEb,CASE,KAAK6S,EAAQC,EAAQ,CACnB,GAAI,KAAK,OAAS,KAAK,eAAc,EACnC,GAAK,KAAK,YAKH,CACL,MAAMC,EAAgB,KAAK,YAAY,QAEnCA,GAAiB,CAACA,EAAc,eAClC,KAAK,MAAM,UAAU,QAAQA,CAAa,CAEpD,KAX6B,CACrB,MAAMC,EAAiB,KAAK,SAAS,aAAa,iBAElD,KAAK,KAAK,MAAQ,KAAK,MAAM,aAAe,KAAK,KAAK,KAAO,GAAO,IAAI,EACxE,KAAK,YAAc,IAAIR,GAAYQ,EAAgB,KAAK,MAAM,SAAS,CAC/E,CASQ,KAAK,SAAW,CAACF,GAIjB,KAAK,SAAS,SAAS,cAAe,CACxC,QAAS,KACT,OAAAD,CACD,CAAA,EAAE,mBAIC,KAAK,kBACP,KAAK,QAAUhV,EAAc,YAAa,KAAK,EAG3C,KAAK,qBACP,KAAK,UAAUgV,CAAM,IAGvB,KAAK,QAAUhV,EAAc,gBAAiB,KAAK,EACnD,KAAK,QAAQ,UAAY,KAAK,KAAK,MAAQ,IAGzCiV,GAAU,KAAK,OACjB,KAAK,MAAM,kBAAkB,EAAI,EAEvC,CAQE,UAAUD,EAAQ,CAChB,IAAII,EAAgBC,EAEpB,GAAI,CAAC,KAAK,eAAc,GAAM,CAAC,KAAK,SAAW,KAAK,SAAS,SAAS,mBAAoB,CACxF,QAAS,KACT,OAAAL,CACD,CAAA,EAAE,iBACD,OAGF,MAAMM,EAEN,KAAK,QACL,KAAK,kBAAmB,EAEpB,KAAK,KAAK,SACZA,EAAa,OAAS,KAAK,KAAK,QAGlCA,EAAa,KAAOF,EAAiB,KAAK,KAAK,OAAS,MAAQA,IAAmB,OAASA,EAAiB,GAC7GE,EAAa,KAAOD,EAAiB,KAAK,KAAK,OAAS,MAAQA,IAAmB,OAASA,EAAiB,GAC7G,KAAK,MAAQlT,EAAW,QAEpBmT,EAAa,SACf,KAAK,SAAU,GAEfA,EAAa,OAAS,IAAM,CAC1B,KAAK,SAAU,CAChB,EAEDA,EAAa,QAAU,IAAM,CAC3B,KAAK,QAAS,CACf,EAEP,CAQE,SAASjR,EAAO,CACd,KAAK,MAAQA,EACb,KAAK,SAAW,GAChB,KAAK,SAAWA,EAAM,IAC1B,CAME,UAAW,CACT,KAAK,MAAQlC,EAAW,OAEpB,KAAK,OAAS,KAAK,UACrB,KAAK,SAAS,SAAS,eAAgB,CACrC,MAAO,KAAK,MACZ,QAAS,IACjB,CAAO,EAEG,KAAK,MAAM,UAAY,KAAK,MAAM,eAAiB,CAAC,KAAK,QAAQ,aACnE,KAAK,OAAQ,EACb,KAAK,MAAM,kBAAkB,EAAI,IAG/B,KAAK,QAAUA,EAAW,QAAU,KAAK,QAAUA,EAAW,QAChE,KAAK,kBAAmB,EAGhC,CAME,SAAU,CACR,KAAK,MAAQA,EAAW,MAEpB,KAAK,QACP,KAAK,aAAc,EACnB,KAAK,SAAS,SAAS,eAAgB,CACrC,MAAO,KAAK,MACZ,QAAS,GACT,QAAS,IACjB,CAAO,EACD,KAAK,SAAS,SAAS,YAAa,CAClC,MAAO,KAAK,MACZ,QAAS,IACjB,CAAO,EAEP,CAME,WAAY,CACV,OAAO,KAAK,SAAS,aAAa,mBAAoB,KAAK,QAAUA,EAAW,QAAS,IAAI,CACjG,CAME,SAAU,CACR,OAAO,KAAK,QAAUA,EAAW,KACrC,CAME,gBAAiB,CACf,OAAO,KAAK,OAAS,OACzB,CASE,iBAAiByD,EAAOC,EAAQ,CAC9B,GAAK,KAAK,UAIN,KAAK,aACP,KAAK,YAAY,iBAAiBD,EAAOC,CAAM,EAG7C,MAAK,SAAS,SAAS,gBAAiB,CAC1C,QAAS,KACT,MAAAD,EACA,OAAAC,CACD,CAAA,EAAE,mBAIHlE,EAAe,KAAK,QAASiE,EAAOC,CAAM,EAEtC,KAAK,eAAc,GAAM,CAAC,KAAK,QAAO,IAAI,CAC5C,MAAM0P,EAAsB,CAAC,KAAK,qBAAuB3P,EACzD,KAAK,oBAAsBA,EAC3B,KAAK,qBAAuBC,EAExB0P,EACF,KAAK,UAAU,EAAK,EAEpB,KAAK,kBAAmB,EAGtB,KAAK,OACP,KAAK,SAAS,SAAS,kBAAmB,CACxC,MAAO,KAAK,MACZ,MAAA3P,EACA,OAAAC,EACA,QAAS,IACnB,CAAS,CAET,CACA,CAME,YAAa,CACX,OAAO,KAAK,SAAS,aAAa,oBAAqB,KAAK,eAAgB,GAAI,KAAK,QAAU1D,EAAW,MAAO,IAAI,CACzH,CAME,mBAAoB,CAMlB,GAAI,CAAC,KAAK,eAAc,GAAM,CAAC,KAAK,SAAW,CAAC,KAAK,KAAK,OACxD,OAGF,MAAMqT,EAEN,KAAK,QACCC,EAAa,KAAK,SAAS,aAAa,mBAAoB,KAAK,oBAAqB,IAAI,GAE5F,CAACD,EAAM,QAAQ,iBAAmBC,EAAa,SAASD,EAAM,QAAQ,gBAAiB,EAAE,KAC3FA,EAAM,MAAQC,EAAa,KAC3BD,EAAM,QAAQ,gBAAkB,OAAOC,CAAU,EAEvD,CAME,gBAAiB,CACf,OAAO,KAAK,SAAS,aAAa,wBAAyB,KAAK,eAAgB,EAAE,IAAI,CAC1F,CAME,UAAW,CACL,KAAK,SAAS,SAAS,kBAAmB,CAC5C,QAAS,IACV,CAAA,EAAE,kBAIH,KAAK,KAAK,EAAI,CAClB,CAME,iBAAkB,CAChB,OAAO,KAAK,SAAS,aAAa,uBAAwB,KAAK,UAAW,EAAE,IAAI,CACpF,CAME,SAAU,CACR,KAAK,SAAW,GAChB,KAAK,MAAQ,OAET,MAAK,SAAS,SAAS,iBAAkB,CAC3C,QAAS,IACV,CAAA,EAAE,mBAIH,KAAK,OAAQ,EAET,KAAK,cACP,KAAK,YAAY,QAAS,EAC1B,KAAK,YAAc,QAGjB,KAAK,kBAAoB,KAAK,UAChC,KAAK,QAAQ,OAAS,KACtB,KAAK,QAAQ,QAAU,KACvB,KAAK,QAAU,QAErB,CAME,cAAe,CACb,GAAI,KAAK,MAAO,CACd,IAAIC,EAAuBC,EAE3B,IAAIC,EAAa5V,EAAc,kBAAmB,KAAK,EACvD4V,EAAW,WAAaF,GAAyBC,EAAyB,KAAK,SAAS,WAAa,MAAQA,IAA2B,OAAS,OAASA,EAAuB,YAAc,MAAQD,IAA0B,OAASA,EAAwB,GAClQE,EAEA,KAAK,SAAS,aAAa,sBAAuBA,EAAY,IAAI,EAClE,KAAK,QAAU5V,EAAc,0CAA2C,KAAK,EAC7E,KAAK,QAAQ,YAAY4V,CAAU,EACnC,KAAK,MAAM,UAAU,UAAY,GACjC,KAAK,MAAM,UAAU,YAAY,KAAK,OAAO,EAC7C,KAAK,MAAM,kBAAkB,EAAI,EACjC,KAAK,kBAAmB,CAC9B,CACA,CAME,QAAS,CACP,GAAI,KAAK,YAAc,CAAC,KAAK,QAC3B,OAKF,GAFA,KAAK,WAAa,GAEd,KAAK,QAAUzT,EAAW,MAAO,CACnC,KAAK,aAAc,EACnB,MACN,CAEI,GAAI,KAAK,SAAS,SAAS,gBAAiB,CAC1C,QAAS,IACV,CAAA,EAAE,iBACD,OAGF,MAAM0T,EAAkB,WAAY,KAAK,QAErC,KAAK,iBAaHA,GAAkB,KAAK,QAAU,CAAC,KAAK,MAAM,UAAYjT,EAAQ,IACnE,KAAK,WAAa,GAKlB,KAAK,QAAQ,OAAQ,EAAC,MAAM,IAAM,CAAE,CAAA,EAAE,QAAQ,IAAM,CAClD,KAAK,WAAa,GAClB,KAAK,YAAa,CAC5B,CAAS,GAED,KAAK,YAAa,EAEX,KAAK,OAAS,CAAC,KAAK,QAAQ,YACrC,KAAK,MAAM,UAAU,YAAY,KAAK,OAAO,CAEnD,CAQE,UAAW,CACL,KAAK,SAAS,SAAS,kBAAmB,CAC5C,QAAS,IACV,CAAA,EAAE,kBAAoB,CAAC,KAAK,QAIzB,KAAK,eAAgB,GAAI,KAAK,YAAc,CAACA,IAG/C,KAAK,YAAa,EACT,KAAK,WACd,KAAK,KAAK,GAAO,EAAI,EAGnB,KAAK,MAAM,eACb,KAAK,MAAM,cAAc,aAAa,cAAe,OAAO,EAElE,CAME,YAAa,CACX,KAAK,SAAS,SAAS,oBAAqB,CAC1C,QAAS,IACf,CAAK,EAEG,KAAK,OAAS,KAAK,MAAM,eAC3B,KAAK,MAAM,cAAc,aAAa,cAAe,MAAM,CAEjE,CAME,QAAS,CACP,KAAK,WAAa,GAEd,MAAK,SAAS,SAAS,gBAAiB,CAC1C,QAAS,IACV,CAAA,EAAE,mBAIC,KAAK,SAAW,KAAK,QAAQ,YAC/B,KAAK,QAAQ,OAAQ,EAGnB,KAAK,aAAe,KAAK,YAAY,SACvC,KAAK,YAAY,QAAQ,OAAQ,EAEvC,CAME,aAAc,CACP,KAAK,aAIN,KAAK,SAAS,SAAS,qBAAsB,CAC/C,QAAS,IACV,CAAA,EAAE,mBAKC,KAAK,OAAS,KAAK,SAAW,CAAC,KAAK,QAAQ,YAC9C,KAAK,MAAM,UAAU,YAAY,KAAK,OAAO,GAG3C,KAAK,QAAUT,EAAW,QAAU,KAAK,QAAUA,EAAW,QAChE,KAAK,kBAAmB,GAE9B,CAEA,CAYA,MAAM2T,GAAsB,EAY5B,SAASC,EAAahS,EAAUuP,EAAUtP,EAAO,CAC/C,MAAMgS,EAAU1C,EAAS,sBAAsBvP,EAAUC,CAAK,EAG9D,IAAIiS,EACJ,KAAM,CACJ,QAAAvS,CACD,EAAG4P,EAGJ,GAAI5P,EAAS,CACXuS,EAAY,IAAIpR,EAAUnB,EAASK,EAAU,EAAE,EAC/C,IAAID,EAEAwP,EAAS,KACXxP,EAAewP,EAAS,KAAK,aAE7BxP,EAAeL,EAAgBC,EAAS4P,CAAQ,EAGlD,MAAM5O,EAAcP,EAAeT,EAASI,EAAcC,EAAUC,CAAK,EACzEiS,EAAU,OAAOD,EAAQ,MAAOA,EAAQ,OAAQtR,CAAW,CAC/D,CAEE,OAAAsR,EAAQ,SAAU,EAEdC,GACFD,EAAQ,iBAAiB,KAAK,KAAKA,EAAQ,MAAQC,EAAU,OAAO,EAAG,KAAK,KAAKD,EAAQ,OAASC,EAAU,OAAO,CAAC,EAG/GD,CACT,CAaA,SAASE,GAAclS,EAAOsP,EAAU,CACtC,MAAMvP,EAAWuP,EAAS,YAAYtP,CAAK,EAE3C,GAAI,CAAAsP,EAAS,SAAS,gBAAiB,CACrC,MAAAtP,EACA,SAAAD,CACD,CAAA,EAAE,iBAIH,OAAOgS,EAAahS,EAAUuP,EAAUtP,CAAK,CAC/C,CAEA,MAAMmS,EAAc,CAIlB,YAAYxS,EAAM,CAChB,KAAK,KAAOA,EAEZ,KAAK,MAAQ,KAAK,IAAIA,EAAK,QAAQ,QAAQ,CAAC,EAAIA,EAAK,QAAQ,QAAQ,CAAC,EAAI,EAAGmS,EAAmB,EAGhG,KAAK,aAAe,CAAE,CAC1B,CAQE,WAAW5J,EAAM,CACf,KAAM,CACJ,KAAAvI,CACN,EAAQ,KAEJ,GAAIA,EAAK,SAAS,UAAU,EAAE,iBAC5B,OAGF,KAAM,CACJ,QAAAyS,CACD,EAAGzS,EAAK,QACHsK,EAAY/B,IAAS,OAAY,GAAOA,GAAQ,EACtD,IAAIQ,EAEJ,IAAKA,EAAI,EAAGA,GAAK0J,EAAQ,CAAC,EAAG1J,IAC3B,KAAK,iBAAiB/I,EAAK,WAAasK,EAAYvB,EAAI,CAACA,EAAE,EAI7D,IAAKA,EAAI,EAAGA,GAAK0J,EAAQ,CAAC,EAAG1J,IAC3B,KAAK,iBAAiB/I,EAAK,WAAasK,EAAY,CAACvB,EAAIA,EAAE,CAEjE,CAME,iBAAiB2J,EAAc,CAC7B,MAAMrS,EAAQ,KAAK,KAAK,eAAeqS,CAAY,EAEnD,IAAIL,EAAU,KAAK,kBAAkBhS,CAAK,EAErCgS,IAEHA,EAAUE,GAAclS,EAAO,KAAK,IAAI,EAEpCgS,GACF,KAAK,WAAWA,CAAO,EAG/B,CAOE,kBAAkB3R,EAAO,CACvB,IAAI2R,EAAU,KAAK,kBAAkB3R,EAAM,KAAK,EAEhD,OAAK2R,IAEHA,EAAU,KAAK,KAAK,sBAAsB3R,EAAM,KAAMA,EAAM,KAAK,EACjE,KAAK,WAAW2R,CAAO,GAIzBA,EAAQ,SAAS3R,CAAK,EACf2R,CACX,CAME,WAAWA,EAAS,CAMlB,GAJA,KAAK,cAAcA,EAAQ,KAAK,EAEhC,KAAK,aAAa,KAAKA,CAAO,EAE1B,KAAK,aAAa,OAAS,KAAK,MAAO,CAEzC,MAAMM,EAAgB,KAAK,aAAa,UAAUC,GACzC,CAACA,EAAK,YAAc,CAACA,EAAK,QAClC,EAEGD,IAAkB,IACA,KAAK,aAAa,OAAOA,EAAe,CAAC,EAAE,CAAC,EAEpD,QAAS,CAE7B,CACA,CAQE,cAActS,EAAO,CACnB,MAAMsS,EAAgB,KAAK,aAAa,UAAUC,GAAQA,EAAK,QAAUvS,CAAK,EAE1EsS,IAAkB,IACpB,KAAK,aAAa,OAAOA,EAAe,CAAC,CAE/C,CAOE,kBAAkBtS,EAAO,CACvB,OAAO,KAAK,aAAa,KAAKgS,GAAWA,EAAQ,QAAUhS,CAAK,CACpE,CAEE,SAAU,CACR,KAAK,aAAa,QAAQgS,GAAWA,EAAQ,QAAO,CAAE,EAEtD,KAAK,aAAe,CAAE,CAC1B,CAEA,CAWA,MAAMQ,WAAuB5C,EAAU,CAMrC,aAAc,CACZ,IAAI6C,EAEJ,IAAIC,EAAW,EACf,MAAMC,GAAcF,EAAgB,KAAK,WAAa,MAAQA,IAAkB,OAAS,OAASA,EAAc,WAE5GE,GAAc,WAAYA,EAE5BD,EAAWC,EAAW,OACbA,GAAc,YAAaA,IAE/BA,EAAW,QACdA,EAAW,MAAQ,KAAK,uBAAuBA,EAAW,OAAO,GAG/DA,EAAW,QACbD,EAAWC,EAAW,MAAM,SAKhC,MAAMrM,EAAQ,KAAK,SAAS,WAAY,CACtC,WAAAqM,EACA,SAAAD,CACN,CAAK,EACD,OAAO,KAAK,aAAa,WAAYpM,EAAM,SAAUqM,CAAU,CACnE,CAQE,sBAAsBC,EAAW5S,EAAO,CACtC,OAAO,IAAI+Q,GAAQ6B,EAAW,KAAM5S,CAAK,CAC7C,CAaE,YAAYA,EAAO,CACjB,IAAI6S,EAEJ,MAAMF,GAAcE,EAAiB,KAAK,WAAa,MAAQA,IAAmB,OAAS,OAASA,EAAe,WAGnH,IAAIC,EAAiB,CAAE,EAEnB,MAAM,QAAQH,CAAU,EAE1BG,EAAiBH,EAAW3S,CAAK,EACxB2S,GAAc,YAAaA,IAK/BA,EAAW,QACdA,EAAW,MAAQ,KAAK,uBAAuBA,EAAW,OAAO,GAGnEG,EAAiBH,EAAW,MAAM3S,CAAK,GAGzC,IAAID,EAAW+S,EAEX/S,aAAoB,UACtBA,EAAW,KAAK,sBAAsBA,CAAQ,GAKhD,MAAMuG,EAAQ,KAAK,SAAS,WAAY,CACtC,SAAUvG,GAAY,CAAE,EACxB,MAAAC,CACN,CAAK,EACD,OAAO,KAAK,aAAa,WAAYsG,EAAM,SAAUtG,CAAK,CAC9D,CAUE,uBAAuB+S,EAAgB,CACrC,IAAIC,EAAgBC,EAEpB,OAAKD,EAAiB,KAAK,WAAa,MAAQA,IAAmB,QAAUA,EAAe,WAAaC,EAAiB,KAAK,WAAa,MAAQA,IAAmB,QAAUA,EAAe,cACvL3U,EAAsB,KAAK,QAAQ,SAAU,KAAK,QAAQ,cAAeyU,CAAc,GAAK,CAAE,EAGhG,CAACA,CAAc,CAC1B,CASE,sBAAsBrG,EAAS,CAE7B,MAAM3M,EAAW,CACf,QAAA2M,CACD,EACKwG,EAENxG,EAAQ,UAAY,IAAMA,EAAUA,EAAQ,cAAc,GAAG,EAE7D,GAAIwG,EAAQ,CAGVnT,EAAS,IAAMmT,EAAO,QAAQ,SAAWA,EAAO,KAE5CA,EAAO,QAAQ,aACjBnT,EAAS,OAASmT,EAAO,QAAQ,YAGnCnT,EAAS,MAAQmT,EAAO,QAAQ,UAAY,SAASA,EAAO,QAAQ,UAAW,EAAE,EAAI,EACrFnT,EAAS,OAASmT,EAAO,QAAQ,WAAa,SAASA,EAAO,QAAQ,WAAY,EAAE,EAAI,EAExFnT,EAAS,EAAIA,EAAS,MACtBA,EAAS,EAAIA,EAAS,OAElBmT,EAAO,QAAQ,WACjBnT,EAAS,KAAOmT,EAAO,QAAQ,UAGjC,MAAMC,EAAczG,EAAQ,cAAc,KAAK,EAE/C,GAAIyG,EAAa,CACf,IAAIC,EAIJrT,EAAS,KAAOoT,EAAY,YAAcA,EAAY,IACtDpT,EAAS,KAAOqT,EAAwBD,EAAY,aAAa,KAAK,KAAO,MAAQC,IAA0B,OAASA,EAAwB,EACxJ,EAEUF,EAAO,QAAQ,aAAeA,EAAO,QAAQ,WAC/CnT,EAAS,aAAe,GAEhC,CAEI,OAAO,KAAK,aAAa,cAAeA,EAAU2M,EAASwG,CAAM,CACrE,CAUE,aAAanT,EAAUC,EAAO,CAC5B,OAAO+R,EAAahS,EAAU,KAAMC,CAAK,CAC7C,CAEA,CAYA,MAAMqT,EAAc,KAOpB,MAAMC,EAAO,CAIX,YAAY3T,EAAM,CAChB,KAAK,KAAOA,EACZ,KAAK,SAAW,GAChB,KAAK,OAAS,GACd,KAAK,UAAY,GACjB,KAAK,UAAY,GAMjB,KAAK,UAAY,OAGjB,KAAK,cAAgB,GAGrB,KAAK,aAAe,GAGpB,KAAK,oBAAsB,GAG3B,KAAK,kBAAoB,GAMzB,KAAK,aAAe,OAMpB,KAAK,gBAAkB,OAMvB,KAAK,gBAAkB,OAMvB,KAAK,gBAAkB,OAMvB,KAAK,aAAe,OACpB,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAE/CA,EAAK,GAAG,eAAgB,KAAK,YAAY,CAC7C,CAEE,MAAO,CACL,KAAK,aAAc,EAEnB,KAAK,OAAQ,CACjB,CAEE,OAAQ,CACN,GAAI,KAAK,UAAY,KAAK,WAAa,KAAK,UAI1C,OAGF,MAAMU,EAAQ,KAAK,KAAK,UACxB,KAAK,OAAS,GACd,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,KAAK,UAAY,KAAK,KAAK,QAAQ,sBAE/BA,GAASA,EAAM,cAAgBA,EAAM,OAAS,KAAK,KAAK,QAAQ,oBAClE,KAAK,UAAY,GAGnB,KAAK,iBAAkB,EAEvB,WAAW,IAAM,CACf,KAAK,OAAQ,CACd,EAAE,KAAK,aAAe,GAAK,CAAC,CACjC,CAIE,cAAe,CAGb,GAFA,KAAK,KAAK,IAAI,eAAgB,KAAK,YAAY,EAE3C,CAAC,KAAK,UAAW,CACnB,MAAMA,EAAQ,KAAK,KAAK,UACxB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,KAAK,UAAY,KAAK,KAAK,QAAQ,sBAE/BA,GAASA,EAAM,WAAW,QAAUA,EAAM,OAAS,KAAK,KAAK,QAAQ,oBACvE,KAAK,UAAY,GAGnB,KAAK,iBAAkB,CAC7B,CACA,CAIE,kBAAmB,CACjB,KAAM,CACJ,KAAAV,CACN,EAAQ,KACEU,EAAQ,KAAK,KAAK,UAClB,CACJ,QAAAX,CACN,EAAQC,EAsBJ,GApBID,EAAQ,wBAA0B,QACpCA,EAAQ,gBAAkB,GAC1B,KAAK,aAAe,QACXA,EAAQ,wBAA0B,QAC3CA,EAAQ,gBAAkB,GAC1B,KAAK,UAAY,EACjB,KAAK,aAAe,QACX,KAAK,WAAaC,EAAK,oBAEhC,KAAK,aAAeA,EAAK,oBAEzB,KAAK,aAAe,KAAK,KAAK,eAAgB,EAGhD,KAAK,aAAeU,GAAU,KAA2B,OAASA,EAAM,sBAAuB,EAC/FV,EAAK,WAAW,UAEhB,KAAK,cAAgB,GAAQ,KAAK,WAAa,KAAK,UAAY,IAChE,KAAK,aAAe,EAAQ,KAAK,eAAkBU,GAAU,KAA2B,OAASA,EAAM,QAAQ,eAAgB,KAAM,CAAC,KAAK,WAAa,CAACV,EAAK,WAAW,aAErK,CAAC,KAAK,aACR,KAAK,oBAAsB,GAEvB,KAAK,WAAaU,IACpBA,EAAM,oBAAqB,EAC3BA,EAAM,oBAAqB,OAExB,CACL,IAAIkT,EAEJ,KAAK,qBAAuBA,EAAwB7T,EAAQ,mBAAqB,MAAQ6T,IAA0B,OAASA,EAAwB,EAC1J,CAKI,GAHA,KAAK,kBAAoB,CAAC,KAAK,qBAAuB,KAAK,KAAK,QAAQ,UAAYF,EACpF,KAAK,gBAAkB,KAAK,oBAAsB1T,EAAK,QAAUA,EAAK,GAElE,CAAC,KAAK,cAAe,CACvB,KAAK,UAAY,EACjB,KAAK,aAAe,GACpB,KAAK,kBAAoB,GACzB,KAAK,oBAAsB,GAEvB,KAAK,YACHA,EAAK,UACPA,EAAK,QAAQ,MAAM,QAAU,OAAO0T,CAAW,GAGjD1T,EAAK,eAAe,CAAC,GAGvB,MACN,CAEI,GAAI,KAAK,cAAgB,KAAK,cAAgB,KAAK,aAAa,UAAW,CACzE,IAAIuF,EAGJ,KAAK,aAAe,GACpB,KAAK,gBAAkB,KAAK,KAAK,UACjC,KAAK,iBAAmBA,EAAuB,KAAK,KAAK,aAAe,MAAQA,IAAyB,OAAS,OAASA,EAAqB,cAE5IvF,EAAK,YACPA,EAAK,UAAU,MAAM,SAAW,SAChCA,EAAK,UAAU,MAAM,MAAQA,EAAK,aAAa,EAAI,KAE3D,MACM,KAAK,aAAe,GAGlB,KAAK,WAEH,KAAK,qBACHA,EAAK,UACPA,EAAK,QAAQ,MAAM,QAAU,OAAO0T,CAAW,GAGjD1T,EAAK,eAAe,CAAC,IAEjB,KAAK,mBAAqBA,EAAK,KACjCA,EAAK,GAAG,MAAM,QAAU,OAAO0T,CAAW,GAGxC1T,EAAK,UACPA,EAAK,QAAQ,MAAM,QAAU,MAI7B,KAAK,eACP,KAAK,uBAAwB,EAEzB,KAAK,eAEP,KAAK,aAAa,MAAM,WAAa,YAGrC,KAAK,aAAa,MAAM,QAAU,OAAO0T,CAAW,KAG/C,KAAK,YAGV1T,EAAK,WAAW,YAAY,CAAC,IAC/BA,EAAK,WAAW,YAAY,CAAC,EAAE,GAAG,MAAM,QAAU,QAGhDA,EAAK,WAAW,YAAY,CAAC,IAC/BA,EAAK,WAAW,YAAY,CAAC,EAAE,GAAG,MAAM,QAAU,QAGhD,KAAK,cACHA,EAAK,WAAW,IAAM,IAExBA,EAAK,WAAW,cAAe,EAC/BA,EAAK,WAAW,OAAQ,GAIlC,CAIE,QAAS,CACH,KAAK,WAAa,KAAK,eAAiB,KAAK,cAAgB,KAAK,aAAa,UAAY,MAO7F,IAAI,QAAQ1B,GAAW,CACrB,IAAIuV,EAAU,GACVC,EAAa,GACjB1V,EAEA,KAAK,YAAY,EAAE,QAAQ,IAAM,CAC/ByV,EAAU,GAELC,GACHxV,EAAQ,EAAI,CAExB,CAAS,EACD,WAAW,IAAM,CACfwV,EAAa,GAETD,GACFvV,EAAQ,EAAI,CAEf,EAAE,EAAE,EACL,WAAWA,EAAS,GAAG,CACxB,CAAA,EAAE,QAAQ,IAAM,KAAK,UAAS,CAAE,EAEjC,KAAK,UAAW,CAEtB,CAIE,WAAY,CACV,IAAIyV,EAAoBC,GAEvBD,EAAqB,KAAK,KAAK,WAAa,MAAQA,IAAuB,QAAUA,EAAmB,MAAM,YAAY,6BAA8B,KAAK,UAAY,IAAI,EAC9K,KAAK,KAAK,SAAS,KAAK,UAAY,wBAA0B,uBAAuB,EAErF,KAAK,KAAK,SAEV,eAAiB,KAAK,UAAY,KAAO,MAAM,GAC9CC,EAAsB,KAAK,KAAK,WAAa,MAAQA,IAAwB,QAAUA,EAAoB,UAAU,OAAO,mBAAoB,KAAK,SAAS,EAE3J,KAAK,WACH,KAAK,eAEP,KAAK,aAAa,MAAM,QAAU,KAGpC,KAAK,oBAAqB,GACjB,KAAK,WACd,KAAK,sBAAuB,EAGzB,KAAK,eACR,KAAK,qBAAsB,CAEjC,CAIE,sBAAuB,CACrB,KAAM,CACJ,KAAAhU,CACN,EAAQ,KAWJ,GAVA,KAAK,OAAS,KAAK,UACnB,KAAK,SAAW,KAAK,UACrB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjBA,EAAK,SAAS,KAAK,OAAS,sBAAwB,qBAAqB,EAEzEA,EAAK,SAEL,eAAiB,KAAK,OAAS,QAAU,SAAS,EAE9C,KAAK,SACPA,EAAK,QAAS,UACL,KAAK,OAAQ,CACtB,IAAIoO,EAEA,KAAK,cAAgBpO,EAAK,YAC5BA,EAAK,UAAU,MAAM,SAAW,UAChCA,EAAK,UAAU,MAAM,MAAQ,SAG9BoO,EAAkBpO,EAAK,aAAe,MAAQoO,IAAoB,QAAUA,EAAgB,oBAAqB,CACxH,CACA,CAIE,qBAAsB,CACpB,KAAM,CACJ,KAAApO,CACN,EAAQ,KAEA,KAAK,eACH,KAAK,cAAgB,KAAK,iBAAmB,KAAK,kBACpD,KAAK,WAAW,KAAK,gBAAiB,YAAa,oBAAoB,EAEvE,KAAK,WAAW,KAAK,gBAAiB,YAAa,MAAM,GAGvDA,EAAK,YACPA,EAAK,UAAU,oBAAqB,EAEpC,KAAK,WAAWA,EAAK,UAAU,UAAW,YAAaA,EAAK,UAAU,qBAAqB,IAI3F,KAAK,mBAAqBA,EAAK,IACjC,KAAK,WAAWA,EAAK,GAAI,UAAW,OAAOA,EAAK,QAAQ,SAAS,CAAC,EAGhE,KAAK,qBAAuBA,EAAK,SACnC,KAAK,WAAWA,EAAK,QAAS,UAAW,GAAG,CAElD,CAIE,uBAAwB,CACtB,KAAM,CACJ,KAAAA,CACN,EAAQ,KAEA,KAAK,cACP,KAAK,uBAAuB,EAAI,EAI9B,KAAK,mBAAqBA,EAAK,UAAY,KAAQA,EAAK,IAC1D,KAAK,WAAWA,EAAK,GAAI,UAAW,GAAG,EAGrC,KAAK,qBAAuBA,EAAK,SACnC,KAAK,WAAWA,EAAK,QAAS,UAAW,GAAG,CAElD,CAOE,uBAAuBgJ,EAAS,CAC9B,GAAI,CAAC,KAAK,aAAc,OACxB,KAAM,CACJ,KAAAhJ,CACN,EAAQ,KACE,CACJ,UAAAiU,CACD,EAAG,KAAK,aACH,CACJ,UAAAtQ,EACA,aAAAxD,CACN,EAAQH,EAEJ,GAAI,KAAK,cAAgBiU,GAAa,KAAK,iBAAmB,KAAK,gBAAiB,CAClF,MAAMC,EAAmB,CAAC/T,EAAa,GAAK,KAAK,aAAa,EAAI8T,EAAU,GAAKA,EAAU,EACrFE,EAAmB,CAAChU,EAAa,GAAK,KAAK,aAAa,EAAI8T,EAAU,GAAKA,EAAU,EACrFG,EAAmBjU,EAAa,EAAI8T,EAAU,EAC9CI,EAAmBlU,EAAa,EAAI8T,EAAU,EAEhDjL,GACF,KAAK,WAAW,KAAK,gBAAiB,YAAazL,EAAkB2W,EAAkBC,CAAgB,CAAC,EAExG,KAAK,WAAW,KAAK,gBAAiB,YAAa5W,EAAkB6W,EAAkBC,CAAgB,CAAC,IAExG3W,EAAa,KAAK,gBAAiBwW,EAAkBC,CAAgB,EACrEzW,EAAa,KAAK,gBAAiB0W,EAAkBC,CAAgB,EAE7E,CAEQ1Q,IACFjH,EAAeiH,EAAU,IAAKsQ,GAAa,KAAK,YAAY,EAC5DtQ,EAAU,cAAgB,KAAK,aAAa,EAAIA,EAAU,MAEtDqF,EACF,KAAK,WAAWrF,EAAU,UAAW,YAAaA,EAAU,qBAAqB,EAEjFA,EAAU,oBAAqB,EAGvC,CASE,WAAWvE,EAAQvB,EAAMJ,EAAW,CAClC,GAAI,CAAC,KAAK,UAAW,CACnB2B,EAAO,MAAMvB,CAAI,EAAIJ,EACrB,MACN,CAEI,KAAM,CACJ,WAAA6W,CACD,EAAG,KAAK,KAGHC,EAAY,CAChB,SAAU,KAAK,UACf,OAAQ,KAAK,KAAK,QAAQ,OAC1B,WAAY,IAAM,CACXD,EAAW,iBAAiB,QAC/B,KAAK,qBAAsB,CAE9B,EACD,OAAAlV,CACD,EACDmV,EAAU1W,CAAI,EAAIJ,EAClB6W,EAAW,gBAAgBC,CAAS,CACxC,CAEA,CAgOA,MAAMC,GAAiB,CACrB,eAAgB,GAChB,QAAS,GACT,KAAM,GACN,aAAc,GACd,oBAAqB,GACrB,sBAAuB,IACvB,sBAAuB,IACvB,sBAAuB,IACvB,OAAQ,GACR,UAAW,GACX,UAAW,GACX,YAAa,GACb,kBAAmB,IACnB,wBAAyB,GACzB,iBAAkB,gBAClB,cAAe,QACf,UAAW,kBACX,gBAAiB,OACjB,kBAAmB,MACnB,eAAgB,IAChB,UAAW,GACX,MAAO,EACP,SAAU,6BACV,QAAS,CAAC,EAAG,CAAC,EACd,OAAQ,0BACV,EAKA,MAAMC,WAAmB5B,EAAe,CAItC,YAAY9S,EAAS,CACnB,MAAO,EACP,KAAK,QAAU,KAAK,gBAAgBA,GAAW,CAAA,CAAE,EAOjD,KAAK,OAAS,CACZ,EAAG,EACH,EAAG,CACJ,EAMD,KAAK,kBAAoB,CACvB,EAAG,EACH,EAAG,CACJ,EAOD,KAAK,aAAe,CAClB,EAAG,EACH,EAAG,CACJ,EAKD,KAAK,UAAY,EACjB,KAAK,UAAY,EACjB,KAAK,eAAiB,EACtB,KAAK,OAAS,GACd,KAAK,aAAe,GACpB,KAAK,SAAW,GAMhB,KAAK,iBAAmB,CAAE,EAG1B,KAAK,oBAAsB,OAG3B,KAAK,OAAS,OAGd,KAAK,QAAU,OAGf,KAAK,SAAW,OAGhB,KAAK,UAAY,OAGjB,KAAK,WAAa,OAGlB,KAAK,UAAY,OACjB,KAAK,OAAS,IAAIZ,EAClB,KAAK,WAAa,IAAI8M,GACtB,KAAK,WAAa,IAAIvD,GAAW,IAAI,EACrC,KAAK,SAAW,IAAInB,GAAS,IAAI,EACjC,KAAK,OAAS,IAAIoM,GAAO,IAAI,EAC7B,KAAK,SAAW,IAAIxJ,GAAS,IAAI,EACjC,KAAK,cAAgB,IAAIqI,GAAc,IAAI,CAC/C,CAIE,MAAO,CACL,GAAI,KAAK,QAAU,KAAK,aACtB,MAAO,GAGT,KAAK,OAAS,GACd,KAAK,SAAS,MAAM,EAEpB,KAAK,SAAS,YAAY,EAE1B,KAAK,qBAAoB,EAGzB,IAAIkC,EAAc,aAElB,OAAI,KAAK,SAAS,gBAChBA,GAAe,gBAGb,KAAK,QAAQ,YACfA,GAAe,IAAM,KAAK,QAAQ,WAGhC,KAAK,UACP,KAAK,QAAQ,WAAa,IAAMA,GAGlC,KAAK,UAAY,KAAK,QAAQ,OAAS,EACvC,KAAK,eAAiB,KAAK,UAC3B,KAAK,SAAS,aAAa,EAG3B,KAAK,YAAc,IAAItI,GAAY,IAAI,GAEnC,OAAO,MAAM,KAAK,SAAS,GAAK,KAAK,UAAY,GAAK,KAAK,WAAa,KAAK,YAAW,KAC1F,KAAK,UAAY,GAGd,KAAK,SAAS,eAEjB,KAAK,cAAe,EAItB,KAAK,WAAY,EACjB,KAAK,OAAO,EAAI,OAAO,YACvB,KAAK,iBAAmB,KAAK,YAAY,KAAK,SAAS,EACvD,KAAK,SAAS,cAAe,CAC3B,MAAO,KAAK,UACZ,KAAM,KAAK,iBACX,MAAO,MACb,CAAK,EAED,KAAK,oBAAsB,KAAK,eAAgB,EAChD,KAAK,SAAS,eAAe,EAC7B,KAAK,GAAG,sBAAuB,IAAM,CACnC,KAAM,CACJ,YAAAuI,CACR,EAAU,KAAK,WAELA,EAAY,CAAC,IACfA,EAAY,CAAC,EAAE,GAAG,MAAM,QAAU,QAClC,KAAK,WAAWA,EAAY,CAAC,EAAG,KAAK,UAAY,CAAC,GAGhDA,EAAY,CAAC,IACfA,EAAY,CAAC,EAAE,GAAG,MAAM,QAAU,QAClC,KAAK,WAAWA,EAAY,CAAC,EAAG,KAAK,UAAY,CAAC,GAGpD,KAAK,YAAa,EAClB,KAAK,cAAc,WAAY,EAC/B,KAAK,OAAO,IAAI,OAAQ,SAAU,KAAK,kBAAkB,KAAK,IAAI,CAAC,EACnE,KAAK,OAAO,IAAI,OAAQ,SAAU,KAAK,wBAAwB,KAAK,IAAI,CAAC,EACzE,KAAK,SAAS,YAAY,CAChC,CAAK,EAEG,KAAK,WAAW,YAAY,CAAC,GAC/B,KAAK,WAAW,KAAK,WAAW,YAAY,CAAC,EAAG,KAAK,SAAS,EAGhE,KAAK,SAAS,QAAQ,EACtB,KAAK,OAAO,KAAM,EAClB,KAAK,SAAS,WAAW,EAClB,EACX,CAUE,eAAetU,EAAO,CACpB,MAAM8I,EAAY,KAAK,YAAa,EAEpC,OAAI,KAAK,QAAQ,OACX9I,EAAQ8I,EAAY,IACtB9I,GAAS8I,GAGP9I,EAAQ,IACVA,GAAS8I,IAINhM,EAAMkD,EAAO,EAAG8I,EAAY,CAAC,CACxC,CAEE,aAAc,CACZ,KAAK,WAAW,YAAY,QAAQL,GAAc,CAChD,IAAIc,GAEHA,EAAoBd,EAAW,SAAW,MAAQc,IAAsB,QAAUA,EAAkB,YAAa,CACxH,CAAK,CACL,CAOE,KAAKvJ,EAAO,CACV,KAAK,WAAW,YAAY,KAAK,eAAeA,CAAK,EAAI,KAAK,cAAc,CAChF,CAME,MAAO,CACL,KAAK,KAAK,KAAK,eAAiB,CAAC,CACrC,CAME,MAAO,CACL,KAAK,KAAK,KAAK,eAAiB,CAAC,CACrC,CAQE,UAAUqQ,EAAM,CACd,IAAIkE,GAEHA,EAAkB,KAAK,aAAe,MAAQA,IAAoB,QAAUA,EAAgB,OAAO,GAAGlE,CAAI,CAC/G,CAME,YAAa,CACX,IAAImE,GAEHA,EAAmB,KAAK,aAAe,MAAQA,IAAqB,QAAUA,EAAiB,WAAY,CAChH,CAOE,OAAQ,CACF,CAAC,KAAK,OAAO,QAAU,KAAK,eAIhC,KAAK,aAAe,GACpB,KAAK,SAAS,OAAO,EACrB,KAAK,OAAO,UAAW,EACvB,KAAK,OAAO,MAAO,EACvB,CAUE,SAAU,CACR,IAAI1D,EAEJ,GAAI,CAAC,KAAK,aAAc,CACtB,KAAK,QAAQ,sBAAwB,OACrC,KAAK,MAAO,EACZ,MACN,CAEI,KAAK,SAAS,SAAS,EACvB,KAAK,WAAa,CAAE,EAEhB,KAAK,aACP,KAAK,WAAW,YAAc,KAC9B,KAAK,WAAW,WAAa,OAG9BA,EAAgB,KAAK,WAAa,MAAQA,IAAkB,QAAUA,EAAc,OAAQ,EAC7F,KAAK,WAAW,YAAY,QAAQrI,GAAc,CAChD,IAAIgM,GAEHA,EAAqBhM,EAAW,SAAW,MAAQgM,IAAuB,QAAUA,EAAmB,QAAS,CACvH,CAAK,EACD,KAAK,cAAc,QAAS,EAC5B,KAAK,OAAO,UAAW,CAC3B,CAQE,oBAAoBC,EAAY,CAC9B,KAAK,cAAc,cAAcA,CAAU,EAC3C,KAAK,WAAW,YAAY,QAAQ,CAACjM,EAAYC,IAAM,CACrD,IAAIiM,EAAuBC,EAE3B,IAAIC,IAAyBF,GAAyBC,EAAmB,KAAK,aAAe,MAAQA,IAAqB,OAAS,OAASA,EAAiB,SAAW,MAAQD,IAA0B,OAASA,EAAwB,GAAK,EAAIjM,EAMpP,GAJI,KAAK,YACPmM,EAAuB,KAAK,eAAeA,CAAoB,GAG7DA,IAAyBH,IAE3B,KAAK,WAAWjM,EAAYiM,EAAY,EAAI,EAExChM,IAAM,GAAG,CACX,IAAIoM,EAEJ,KAAK,UAAYrM,EAAW,OAC3BqM,EAAqBrM,EAAW,SAAW,MAAQqM,IAAuB,QAAUA,EAAmB,YAAY,EAAI,CAClI,CAEA,CAAK,EACD,KAAK,SAAS,QAAQ,CAC1B,CAUE,WAAWC,EAAQ/U,EAAO0B,EAAO,CAK/B,GAJI,KAAK,YACP1B,EAAQ,KAAK,eAAeA,CAAK,GAG/B+U,EAAO,MAAO,CAChB,GAAIA,EAAO,MAAM,QAAU/U,GAAS,CAAC0B,EAGnC,OAIFqT,EAAO,MAAM,QAAS,EACtBA,EAAO,MAAQ,MAChB,CAGD,GAAI,CAAC,KAAK,QAAO,IAAO/U,EAAQ,GAAKA,GAAS,KAAK,YAAW,GAC5D,OAGF,MAAMD,EAAW,KAAK,YAAYC,CAAK,EACvC+U,EAAO,MAAQ,IAAIzT,EAAMvB,EAAUC,EAAO,IAAI,EAE1CA,IAAU,KAAK,YACjB,KAAK,UAAY+U,EAAO,OAG1BA,EAAO,MAAM,OAAOA,EAAO,EAAE,CACjC,CAIE,wBAAyB,CACvB,MAAO,CACL,EAAG,KAAK,aAAa,EAAI,EACzB,EAAG,KAAK,aAAa,EAAI,CAC1B,CACL,CASE,WAAWrT,EAAO,CAGhB,GAAI,KAAK,aAGP,OAKF,MAAM9B,EAAkBH,EAAgB,KAAK,QAAS,IAAI,EAEtD,CAACiC,GAAS7E,EAAY+C,EAAiB,KAAK,iBAAiB,IAOjEvD,EAAe,KAAK,kBAAmBuD,CAAe,EACtD,KAAK,SAAS,cAAc,EAC5BvD,EAAe,KAAK,aAAc,KAAK,iBAAiB,EAExD,KAAK,wBAAyB,EAE9B,KAAK,SAAS,cAAc,EAG5B,KAAK,WAAW,OAAO,KAAK,OAAO,MAAM,EAErC,CAAC,KAAK,UAAY,OAAO,WAAW,oBAAoB,EAAE,SAC5D,KAAK,cAAe,EAGtB,KAAK,SAAS,QAAQ,EAC1B,CAME,eAAe2Y,EAAS,CACtB,KAAK,UAAY,KAAK,IAAIA,EAAS,CAAC,EAEhC,KAAK,KACP,KAAK,GAAG,MAAM,QAAU,OAAO,KAAK,UAAY,KAAK,QAAQ,SAAS,EAE5E,CAME,eAAgB,CACd,GAAI,CAAC,KAAK,SAAU,CAClB,IAAIC,EAEJ,KAAK,SAAW,IACfA,EAAiB,KAAK,WAAa,MAAQA,IAAmB,QAAUA,EAAe,UAAU,IAAI,iBAAiB,CAC7H,CACA,CAQE,mBAAoB,CAClB,KAAK,WAAU,EAMX,oBAAoB,KAAK,OAAO,UAAU,SAAS,GACrD,WAAW,IAAM,CACf,KAAK,WAAY,CAClB,EAAE,GAAG,CAEZ,CAUE,yBAA0B,CACxB,KAAK,gBAAgB,EAAG,OAAO,WAAW,CAC9C,CAOE,gBAAgBtY,EAAGC,EAAG,CACpB,KAAK,OAAO,EAAID,EAChB,KAAK,OAAO,EAAIC,EAChB,KAAK,SAAS,oBAAoB,CACtC,CASE,sBAAuB,CAErB,KAAK,QAAUZ,EAAc,OAAQ,KAAK,EAC1C,KAAK,QAAQ,aAAa,WAAY,IAAI,EAC1C,KAAK,QAAQ,aAAa,OAAQ,QAAQ,EAE1C,KAAK,SAAW,KAAK,QAGrB,KAAK,GAAKA,EAAc,WAAY,MAAO,KAAK,OAAO,EACvD,KAAK,WAAaA,EAAc,oBAAqB,UAAW,KAAK,OAAO,EAC5E,KAAK,UAAYA,EAAc,kBAAmB,MAAO,KAAK,UAAU,EAExE,KAAK,WAAW,aAAa,uBAAwB,UAAU,EAC/D,KAAK,UAAU,aAAa,YAAa,KAAK,EAC9C,KAAK,UAAU,aAAa,KAAM,aAAa,EAC/C,KAAK,WAAW,cAAe,EAC/B,KAAK,GAAK,IAAIqS,GAAG,IAAI,EACrB,KAAK,GAAG,QAEP,KAAK,QAAQ,YAAc,SAAS,MAAM,YAAY,KAAK,OAAO,CACvE,CAWE,gBAAiB,CACf,OAAOgB,GAAe,KAAK,UAAW,KAAK,UAAY,KAAK,UAAU,KAAO,KAAK,iBAAkB,IAAI,CAC5G,CAOE,SAAU,CACR,OAAO,KAAK,QAAQ,MAAQ,KAAK,YAAa,EAAG,CACrD,CAQE,gBAAgB3P,EAAS,CACvB,OAAI,OAAO,WAAW,0CAA0C,EAAE,UAChEA,EAAQ,sBAAwB,OAChCA,EAAQ,sBAAwB,GAK3B,CAAE,GAAGyU,GACV,GAAGzU,CACJ,CACL,CAEA", "x_google_ignoreList": [0]}