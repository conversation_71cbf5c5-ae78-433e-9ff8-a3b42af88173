const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/pages-static-BIE16m2Q.js","assets/vendor-react-EBZQFYZ5.js","assets/vendor-misc-j6k8kvFA.js","assets/vendor-animations-Dl3DQHMd.js","assets/vendor-misc-Cg1sXqY3.css","assets/vendor-gallery-BKyWYjF6.js","assets/vendor-admin-DvrlCxcB.js","assets/components-layout-DGce1jMY.js","assets/components-common-DDbdC8oB.js","assets/vendor-i18n-DxzbetI3.js","assets/admin-routes-D7V7jRaZ.js","assets/components-home-B-IXSbjU.js","assets/vendor-utils-t--hEgTQ.js","assets/components-layout-LOVgDiEq.css","assets/pages-other-BrCzhuD6.js","assets/pages-static-D4YCAEbJ.css","assets/vendor-ui-DQIoTyJ0.js"])))=>i.map(i=>d[i]);
import{r as n,c as _,j as t,R as j,a,b as L,e as E,B as b,l as h}from"./vendor-react-EBZQFYZ5.js";import{_ as l}from"./admin-routes-D7V7jRaZ.js";import"./components-layout-DGce1jMY.js";import{x as d,y as P,G as A,E as S}from"./components-common-DDbdC8oB.js";import{R as g,W as v}from"./vendor-animations-Dl3DQHMd.js";import{c as R}from"./components-home-B-IXSbjU.js";import{H as D}from"./pages-other-BrCzhuD6.js";import"./vendor-misc-j6k8kvFA.js";import"./vendor-gallery-BKyWYjF6.js";import"./vendor-admin-DvrlCxcB.js";import"./vendor-i18n-DxzbetI3.js";import"./vendor-utils-t--hEgTQ.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const c of r)if(c.type==="childList")for(const m of c.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&i(m)}).observe(document,{childList:!0,subtree:!0});function e(r){const c={};return r.integrity&&(c.integrity=r.integrity),r.referrerPolicy&&(c.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?c.credentials="include":r.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function i(r){if(r.ep)return;r.ep=!0;const c=e(r);fetch(r.href,c)}})();const T=()=>{document.querySelectorAll(".parallax-mousemove-scene").forEach(o=>{o.addEventListener("mousemove",function(s){const e=window.innerWidth,i=window.innerHeight,r=.5-(s.pageX-this.offsetLeft)/e,c=.5-(s.pageY-this.offsetTop)/i;this.querySelectorAll(".parallax-mousemove").forEach(p=>{const x=parseInt(p.getAttribute("data-offset")),w=`translate3d(${Math.round(r*x)}px, ${Math.round(c*x)}px, 0px)`;p.style.transform=w});let m=s.pageX-this.offsetLeft;s.pageY-this.offsetTop,this.querySelectorAll(".parallax-mousemove-follow").forEach(p=>{p.style.left=`${m}px`,p.style.top="31px"})}),o.addEventListener("mouseenter",function(s){this.querySelectorAll(".parallax-mousemove-follow").forEach(e=>{setTimeout(()=>{e.style.transition="all .27s var(--ease-out-short)",e.style.willChange="transform"},27)})}),o.addEventListener("mouseout",function(s){this.querySelectorAll(".parallax-mousemove-follow").forEach(e=>{e.style.transition="none"})})})};function O(){if(document.querySelectorAll("[data-rellax-y]").length&&window.innerWidth>=1280){let s=function(){document.querySelectorAll("[data-rellax-y]").forEach(e=>{e.getBoundingClientRect().top<window.innerHeight&&e.getBoundingClientRect().bottom>0?e.classList.contains("js-in-viewport")||(e.classList.add("js-in-viewport"),o.refresh()):e.classList.contains("js-in-viewport")&&e.classList.remove("js-in-viewport")})};const o=new g("[data-rellax-y]",{vertical:!0,horizontal:!1});window.addEventListener("scroll",s)}if(document.querySelectorAll("[data-rellax-x]").length&&window.innerWidth>=1280){let s=function(){document.querySelectorAll("[data-rellax-x]").forEach(e=>{e.getBoundingClientRect().top<window.innerHeight&&e.getBoundingClientRect().bottom>0?e.classList.contains("js-in-viewport")||(e.classList.add("js-in-viewport"),o.refresh()):e.classList.contains("js-in-viewport")&&e.classList.remove("js-in-viewport")})};const o=new g("[data-rellax-x]",{horizontal:!0});window.addEventListener("scroll",s)}}function q(){setTimeout(()=>{try{document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow").forEach(e=>e.classList.add("no-animate"));var o=new v({boxClass:"wow",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(e){e.classList.add("animated"),e.style.opacity="1",e.style.visibility="visible"}});document.body.classList.contains("appear-animate")||document.body.classList.add("appear-animate"),o.init(),setTimeout(()=>{document.querySelectorAll(".wow").forEach(e=>{e.classList.contains("animated")||(e.style.opacity="1",e.style.visibility="visible",e.classList.add("animated"))})},2e3),document.body.classList.contains("appear-animate")&&document.querySelectorAll(".wow-p").forEach(e=>e.classList.add("no-animate"));var s=new v({boxClass:"wow-p",animateClass:"animated",offset:100,mobile:!0,live:!1,callback:function(e){e.classList.add("animated"),e.style.opacity="1",e.style.visibility="visible"}});document.body.classList.contains("appear-animate")?s.init():document.querySelectorAll(".wow-p").forEach(e=>e.style.opacity="1"),document.body.classList.contains("appear-animate")&&window.innerWidth>=1024&&document.documentElement.classList.contains("no-mobile")?document.querySelectorAll(".wow-menubar").forEach(e=>{e.classList.add("no-animate","fadeInDown","animated"),setInterval(()=>{e.classList.remove("no-animate")},1500)}):document.querySelectorAll(".wow-menubar").forEach(e=>e.style.opacity="1")}catch(e){console.error("Error initializing WOW.js:",e),document.querySelectorAll(".wow, .wow-p, .wow-menubar").forEach(i=>{i.style.opacity="1",i.classList.add("animated")})}},100)}const f=()=>{var o=document.querySelector(".main-nav"),s=document.querySelector(".nav-logo-wrap .logo"),e=document.querySelector(".light-after-scroll");if(!o)return;const i=o.classList.contains("dark-mode")||window.location.pathname==="/"||window.location.pathname.match(/^\/[a-z]{2}\/?$/);window.scrollY>0?(o.classList.remove("transparent"),o.classList.add("small-height","body-scrolled"),s&&s.classList.add("small-height"),e&&!i&&e.classList.remove("dark"),i&&!o.classList.contains("dark")&&o.classList.add("dark")):window.scrollY===0&&(o.classList.add("transparent"),o.classList.remove("small-height","body-scrolled"),s&&s.classList.remove("small-height"),e&&!i&&e.classList.add("dark"),i&&!o.classList.contains("dark")&&o.classList.add("dark"))},k=n.lazy(()=>l(()=>import("./pages-static-BIE16m2Q.js").then(o=>o.p),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]))),I=n.lazy(()=>l(()=>import("./pages-static-BIE16m2Q.js").then(o=>o.a),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]))),z=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.p),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),C=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.a),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),V=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.b),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),W=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.c),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),B=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.d),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),M=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.e),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),N=n.lazy(()=>l(()=>import("./pages-static-BIE16m2Q.js").then(o=>o.b),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]))),H=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.f),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),X=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.h),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),Y=n.lazy(()=>l(()=>import("./pages-other-BrCzhuD6.js").then(o=>o.i),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]))),$=n.lazy(()=>l(()=>import("./admin-routes-D7V7jRaZ.js").then(o=>o.A),__vite__mapDeps([10,1,2,3,4,5,6]))),F=()=>t.jsxs("div",{className:"page-loader",style:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:"#1a1a1a",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:[t.jsx("div",{className:"loader",style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #fff",borderRadius:"50%",animation:"spin 1s linear infinite"}}),t.jsx("style",{children:`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `})]});function G(){const{pathname:o}=_();return n.useEffect(()=>{q(),T();const s=()=>{var i;var e=document.querySelector(".main-nav");if(!e){setTimeout(s,100);return}e.style.display="",e!=null&&e.classList.contains("transparent")?e.classList.add("js-transparent"):(i=e==null?void 0:e.classList)!=null&&i.contains("dark")||e==null||e.classList.add("js-no-transparent-white"),f()};return s(),setTimeout(s,50),window.addEventListener("scroll",f),O(),setTimeout(()=>{const e=document.title||"DevSkills",i=window.location.href;R(e,i)},100),()=>{window.removeEventListener("scroll",f)}},[o]),n.useEffect(()=>{(()=>{const e=document.querySelector(".main-nav");e&&(e.style.display="",e.style.visibility="visible",e.style.opacity="1",setTimeout(()=>{f()},100))})()},[o]),n.useEffect(()=>{typeof window<"u"&&l(()=>import("./vendor-ui-DQIoTyJ0.js"),__vite__mapDeps([16,2,3,4])).then(()=>{console.log("Bootstrap loaded")})},[]),t.jsxs(t.Fragment,{children:[t.jsx(n.Suspense,{fallback:t.jsx(F,{}),children:t.jsxs(j,{children:[t.jsxs(a,{path:"/:lang",children:[t.jsx(a,{index:!0,element:t.jsx(D,{})}),t.jsx(a,{path:"about",element:t.jsx(k,{})}),t.jsx(a,{path:"services",element:t.jsx(I,{})}),t.jsx(a,{path:"portfolio",element:t.jsx(C,{})}),t.jsx(a,{path:"portfolio-single/:id",element:t.jsx(W,{})}),t.jsx(a,{path:"webstore",element:t.jsx(z,{})}),t.jsx(a,{path:"webstore-single/:id",element:t.jsx(B,{})}),t.jsx(a,{path:"blog",element:t.jsx(V,{})}),t.jsx(a,{path:"blog-single/:id",element:t.jsx(M,{})}),t.jsx(a,{path:"contact",element:t.jsx(N,{})}),t.jsx(a,{path:"privacy-policy",element:t.jsx(H,{})}),t.jsx(a,{path:"terms-conditions",element:t.jsx(X,{})})]}),t.jsx(a,{path:"/admin/*",element:t.jsx($,{})}),t.jsx(a,{path:"/",element:t.jsx(d,{})}),t.jsx(a,{path:"/about",element:t.jsx(d,{})}),t.jsx(a,{path:"/services",element:t.jsx(d,{})}),t.jsx(a,{path:"/portfolio",element:t.jsx(d,{})}),t.jsx(a,{path:"/portfolio-single/*",element:t.jsx(d,{})}),t.jsx(a,{path:"/blog",element:t.jsx(d,{})}),t.jsx(a,{path:"/blog-single/*",element:t.jsx(d,{})}),t.jsx(a,{path:"/contact",element:t.jsx(d,{})}),t.jsx(a,{path:"/privacy-policy",element:t.jsx(d,{})}),t.jsx(a,{path:"/terms-conditions",element:t.jsx(d,{})}),t.jsx(a,{path:"*",element:t.jsx(Y,{})})]})}),t.jsx(P,{}),t.jsx(A,{})]})}const u=document.getElementById("root"),y=t.jsx(L.StrictMode,{children:t.jsx(S,{children:t.jsx(E,{children:t.jsx(b,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:t.jsx(G,{})})})})}),K=u&&u.hasChildNodes();try{K?h.hydrateRoot(u,y):h.createRoot(u).render(y)}catch(o){console.error("Error rendering app:",o),h.createRoot(u).render(y)}
//# sourceMappingURL=index-Dw2Em5R0.js.map
