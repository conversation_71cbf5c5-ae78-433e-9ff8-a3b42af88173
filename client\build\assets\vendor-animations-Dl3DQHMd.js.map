{"version": 3, "file": "vendor-animations-Dl3DQHMd.js", "sources": ["../../node_modules/.pnpm/rellax@1.12.1/node_modules/rellax/rellax.js", "../../node_modules/.pnpm/wow.js@1.2.2/node_modules/wow.js/dist/wow.js", "../../node_modules/.pnpm/jarallax@2.2.1/node_modules/jarallax/dist/jarallax.esm.js"], "sourcesContent": ["\n// ------------------------------------------\n// Rellax.js\n// Buttery smooth parallax library\n// Copyright (c) 2016 <PERSON> (@moeamaya)\n// MIT license\n//\n// Thanks to Paraxify.js and <PERSON>\n// for parallax concepts\n// ------------------------------------------\n\n(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define([], factory);\n  } else if (typeof module === 'object' && module.exports) {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory();\n  } else {\n    // Browser globals (root is window)\n    root.Rellax = factory();\n  }\n}(typeof window !== \"undefined\" ? window : global, function () {\n  var Rellax = function(el, options){\n    \"use strict\";\n\n    var self = Object.create(Rellax.prototype);\n\n    var posY = 0;\n    var screenY = 0;\n    var posX = 0;\n    var screenX = 0;\n    var blocks = [];\n    var pause = true;\n\n    // check what requestAnimationFrame to use, and if\n    // it's not supported, use the onscroll event\n    var loop = window.requestAnimationFrame ||\n      window.webkitRequestAnimationFrame ||\n      window.mozRequestAnimationFrame ||\n      window.msRequestAnimationFrame ||\n      window.oRequestAnimationFrame ||\n      function(callback){ return setTimeout(callback, 1000 / 60); };\n\n    // store the id for later use\n    var loopId = null;\n\n    // Test via a getter in the options object to see if the passive property is accessed\n    var supportsPassive = false;\n    try {\n      var opts = Object.defineProperty({}, 'passive', {\n        get: function() {\n          supportsPassive = true;\n        }\n      });\n      window.addEventListener(\"testPassive\", null, opts);\n      window.removeEventListener(\"testPassive\", null, opts);\n    } catch (e) {}\n\n    // check what cancelAnimation method to use\n    var clearLoop = window.cancelAnimationFrame || window.mozCancelAnimationFrame || clearTimeout;\n\n    // check which transform property to use\n    var transformProp = window.transformProp || (function(){\n        var testEl = document.createElement('div');\n        if (testEl.style.transform === null) {\n          var vendors = ['Webkit', 'Moz', 'ms'];\n          for (var vendor in vendors) {\n            if (testEl.style[ vendors[vendor] + 'Transform' ] !== undefined) {\n              return vendors[vendor] + 'Transform';\n            }\n          }\n        }\n        return 'transform';\n      })();\n\n    // Default Settings\n    self.options = {\n      speed: -2,\n\t    verticalSpeed: null,\n\t    horizontalSpeed: null,\n      breakpoints: [576, 768, 1201],\n      center: false,\n      wrapper: null,\n      relativeToWrapper: false,\n      round: true,\n      vertical: true,\n      horizontal: false,\n      verticalScrollAxis: \"y\",\n      horizontalScrollAxis: \"x\",\n      callback: function() {},\n    };\n\n    // User defined options (might have more in the future)\n    if (options){\n      Object.keys(options).forEach(function(key){\n        self.options[key] = options[key];\n      });\n    }\n\n    function validateCustomBreakpoints () {\n      if (self.options.breakpoints.length === 3 && Array.isArray(self.options.breakpoints)) {\n        var isAscending = true;\n        var isNumerical = true;\n        var lastVal;\n        self.options.breakpoints.forEach(function (i) {\n          if (typeof i !== 'number') isNumerical = false;\n          if (lastVal !== null) {\n            if (i < lastVal) isAscending = false;\n          }\n          lastVal = i;\n        });\n        if (isAscending && isNumerical) return;\n      }\n      // revert defaults if set incorrectly\n      self.options.breakpoints = [576, 768, 1201];\n      console.warn(\"Rellax: You must pass an array of 3 numbers in ascending order to the breakpoints option. Defaults reverted\");\n    }\n\n    if (options && options.breakpoints) {\n      validateCustomBreakpoints();\n    }\n\n    // By default, rellax class\n    if (!el) {\n      el = '.rellax';\n    }\n\n    // check if el is a className or a node\n    var elements = typeof el === 'string' ? document.querySelectorAll(el) : [el];\n\n    // Now query selector\n    if (elements.length > 0) {\n      self.elems = elements;\n    }\n\n    // The elements don't exist\n    else {\n      console.warn(\"Rellax: The elements you're trying to select don't exist.\");\n      return;\n    }\n\n    // Has a wrapper and it exists\n    if (self.options.wrapper) {\n      if (!self.options.wrapper.nodeType) {\n        var wrapper = document.querySelector(self.options.wrapper);\n\n        if (wrapper) {\n          self.options.wrapper = wrapper;\n        } else {\n          console.warn(\"Rellax: The wrapper you're trying to use doesn't exist.\");\n          return;\n        }\n      }\n    }\n\n    // set a placeholder for the current breakpoint\n    var currentBreakpoint;\n\n    // helper to determine current breakpoint\n    var getCurrentBreakpoint = function (w) {\n      var bp = self.options.breakpoints;\n      if (w < bp[0]) return 'xs';\n      if (w >= bp[0] && w < bp[1]) return 'sm';\n      if (w >= bp[1] && w < bp[2]) return 'md';\n      return 'lg';\n    };\n\n    // Get and cache initial position of all elements\n    var cacheBlocks = function() {\n      for (var i = 0; i < self.elems.length; i++){\n        var block = createBlock(self.elems[i]);\n        blocks.push(block);\n      }\n    };\n\n\n    // Let's kick this script off\n    // Build array for cached element values\n    var init = function() {\n      for (var i = 0; i < blocks.length; i++){\n        self.elems[i].style.cssText = blocks[i].style;\n      }\n\n      blocks = [];\n\n      screenY = window.innerHeight;\n      screenX = window.innerWidth;\n      currentBreakpoint = getCurrentBreakpoint(screenX);\n\n      setPosition();\n\n      cacheBlocks();\n\n      animate();\n\n      // If paused, unpause and set listener for window resizing events\n      if (pause) {\n        window.addEventListener('resize', init);\n        pause = false;\n        // Start the loop\n        update();\n      }\n    };\n\n    // We want to cache the parallax blocks'\n    // values: base, top, height, speed\n    // el: is dom object, return: el cache values\n    var createBlock = function(el) {\n      var dataPercentage = el.getAttribute( 'data-rellax-percentage' );\n      var dataSpeed = el.getAttribute( 'data-rellax-speed' );\n      var dataXsSpeed = el.getAttribute( 'data-rellax-xs-speed' );\n      var dataMobileSpeed = el.getAttribute( 'data-rellax-mobile-speed' );\n      var dataTabletSpeed = el.getAttribute( 'data-rellax-tablet-speed' );\n      var dataDesktopSpeed = el.getAttribute( 'data-rellax-desktop-speed' );\n      var dataVerticalSpeed = el.getAttribute('data-rellax-vertical-speed');\n      var dataHorizontalSpeed = el.getAttribute('data-rellax-horizontal-speed');\n      var dataVericalScrollAxis = el.getAttribute('data-rellax-vertical-scroll-axis');\n      var dataHorizontalScrollAxis = el.getAttribute('data-rellax-horizontal-scroll-axis');\n      var dataZindex = el.getAttribute( 'data-rellax-zindex' ) || 0;\n      var dataMin = el.getAttribute( 'data-rellax-min' );\n      var dataMax = el.getAttribute( 'data-rellax-max' );\n      var dataMinX = el.getAttribute('data-rellax-min-x');\n      var dataMaxX = el.getAttribute('data-rellax-max-x');\n      var dataMinY = el.getAttribute('data-rellax-min-y');\n      var dataMaxY = el.getAttribute('data-rellax-max-y');\n      var mapBreakpoints;\n      var breakpoints = true;\n\n      if (!dataXsSpeed && !dataMobileSpeed && !dataTabletSpeed && !dataDesktopSpeed) {\n        breakpoints = false;\n      } else {\n        mapBreakpoints = {\n          'xs': dataXsSpeed,\n          'sm': dataMobileSpeed,\n          'md': dataTabletSpeed,\n          'lg': dataDesktopSpeed\n        };\n      }\n\n      // initializing at scrollY = 0 (top of browser), scrollX = 0 (left of browser)\n      // ensures elements are positioned based on HTML layout.\n      //\n      // If the element has the percentage attribute, the posY and posX needs to be\n      // the current scroll position's value, so that the elements are still positioned based on HTML layout\n      var wrapperPosY = self.options.wrapper ? self.options.wrapper.scrollTop : (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop);\n      // If the option relativeToWrapper is true, use the wrappers offset to top, subtracted from the current page scroll.\n      if (self.options.relativeToWrapper) {\n        var scrollPosY = (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop);\n        wrapperPosY = scrollPosY - self.options.wrapper.offsetTop;\n      }\n      var posY = self.options.vertical ? ( dataPercentage || self.options.center ? wrapperPosY : 0 ) : 0;\n      var posX = self.options.horizontal ? ( dataPercentage || self.options.center ? self.options.wrapper ? self.options.wrapper.scrollLeft : (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft) : 0 ) : 0;\n\n      var blockTop = posY + el.getBoundingClientRect().top;\n      var blockHeight = el.clientHeight || el.offsetHeight || el.scrollHeight;\n\n      var blockLeft = posX + el.getBoundingClientRect().left;\n      var blockWidth = el.clientWidth || el.offsetWidth || el.scrollWidth;\n\n      // apparently parallax equation everyone uses\n      var percentageY = dataPercentage ? dataPercentage : (posY - blockTop + screenY) / (blockHeight + screenY);\n      var percentageX = dataPercentage ? dataPercentage : (posX - blockLeft + screenX) / (blockWidth + screenX);\n      if(self.options.center){ percentageX = 0.5; percentageY = 0.5; }\n\n      // Optional individual block speed as data attr, otherwise global speed\n      var speed = (breakpoints && mapBreakpoints[currentBreakpoint] !== null) ? Number(mapBreakpoints[currentBreakpoint]) : (dataSpeed ? dataSpeed : self.options.speed);\n      var verticalSpeed = dataVerticalSpeed ? dataVerticalSpeed : self.options.verticalSpeed;\n      var horizontalSpeed = dataHorizontalSpeed ? dataHorizontalSpeed : self.options.horizontalSpeed;\n\n      // Optional individual block movement axis direction as data attr, otherwise gobal movement direction\n      var verticalScrollAxis = dataVericalScrollAxis ? dataVericalScrollAxis : self.options.verticalScrollAxis;\n      var horizontalScrollAxis = dataHorizontalScrollAxis ? dataHorizontalScrollAxis : self.options.horizontalScrollAxis;\n\n      var bases = updatePosition(percentageX, percentageY, speed, verticalSpeed, horizontalSpeed);\n\n      // ~~Store non-translate3d transforms~~\n      // Store inline styles and extract transforms\n      var style = el.style.cssText;\n      var transform = '';\n\n      // Check if there's an inline styled transform\n      var searchResult = /transform\\s*:/i.exec(style);\n      if (searchResult) {\n        // Get the index of the transform\n        var index = searchResult.index;\n\n        // Trim the style to the transform point and get the following semi-colon index\n        var trimmedStyle = style.slice(index);\n        var delimiter = trimmedStyle.indexOf(';');\n\n        // Remove \"transform\" string and save the attribute\n        if (delimiter) {\n          transform = \" \" + trimmedStyle.slice(11, delimiter).replace(/\\s/g,'');\n        } else {\n          transform = \" \" + trimmedStyle.slice(11).replace(/\\s/g,'');\n        }\n      }\n\n      return {\n        baseX: bases.x,\n        baseY: bases.y,\n        top: blockTop,\n        left: blockLeft,\n        height: blockHeight,\n        width: blockWidth,\n        speed: speed,\n        verticalSpeed: verticalSpeed,\n        horizontalSpeed: horizontalSpeed,\n        verticalScrollAxis: verticalScrollAxis,\n        horizontalScrollAxis: horizontalScrollAxis,\n        style: style,\n        transform: transform,\n        zindex: dataZindex,\n        min: dataMin,\n        max: dataMax,\n        minX: dataMinX,\n        maxX: dataMaxX,\n        minY: dataMinY,\n        maxY: dataMaxY\n      };\n    };\n\n    // set scroll position (posY, posX)\n    // side effect method is not ideal, but okay for now\n    // returns true if the scroll changed, false if nothing happened\n    var setPosition = function() {\n      var oldY = posY;\n      var oldX = posX;\n\n      posY = self.options.wrapper ? self.options.wrapper.scrollTop : (document.documentElement || document.body.parentNode || document.body).scrollTop || window.pageYOffset;\n      posX = self.options.wrapper ? self.options.wrapper.scrollLeft : (document.documentElement || document.body.parentNode || document.body).scrollLeft || window.pageXOffset;\n      // If option relativeToWrapper is true, use relative wrapper value instead.\n      if (self.options.relativeToWrapper) {\n        var scrollPosY = (document.documentElement || document.body.parentNode || document.body).scrollTop || window.pageYOffset;\n        posY = scrollPosY - self.options.wrapper.offsetTop;\n      }\n\n\n      if (oldY != posY && self.options.vertical) {\n        // scroll changed, return true\n        return true;\n      }\n\n      if (oldX != posX && self.options.horizontal) {\n        // scroll changed, return true\n        return true;\n      }\n\n      // scroll did not change\n      return false;\n    };\n\n    // Ahh a pure function, gets new transform value\n    // based on scrollPosition and speed\n    // Allow for decimal pixel values\n    var updatePosition = function(percentageX, percentageY, speed, verticalSpeed, horizontalSpeed) {\n      var result = {};\n      var valueX = ((horizontalSpeed ? horizontalSpeed : speed) * (100 * (1 - percentageX)));\n      var valueY = ((verticalSpeed ? verticalSpeed : speed) * (100 * (1 - percentageY)));\n\n      result.x = self.options.round ? Math.round(valueX) : Math.round(valueX * 100) / 100;\n      result.y = self.options.round ? Math.round(valueY) : Math.round(valueY * 100) / 100;\n\n      return result;\n    };\n\n    // Remove event listeners and loop again\n    var deferredUpdate = function() {\n      window.removeEventListener('resize', deferredUpdate);\n      window.removeEventListener('orientationchange', deferredUpdate);\n      (self.options.wrapper ? self.options.wrapper : window).removeEventListener('scroll', deferredUpdate);\n      (self.options.wrapper ? self.options.wrapper : document).removeEventListener('touchmove', deferredUpdate);\n\n      // loop again\n      loopId = loop(update);\n    };\n\n    // Loop\n    var update = function() {\n      if (setPosition() && pause === false) {\n        animate();\n\n        // loop again\n        loopId = loop(update);\n      } else {\n        loopId = null;\n\n        // Don't animate until we get a position updating event\n        window.addEventListener('resize', deferredUpdate);\n        window.addEventListener('orientationchange', deferredUpdate);\n        (self.options.wrapper ? self.options.wrapper : window).addEventListener('scroll', deferredUpdate, supportsPassive ? { passive: true } : false);\n        (self.options.wrapper ? self.options.wrapper : document).addEventListener('touchmove', deferredUpdate, supportsPassive ? { passive: true } : false);\n      }\n    };\n\n    // Transform3d on parallax element\n    var animate = function() {\n      var positions;\n      for (var i = 0; i < self.elems.length; i++){\n        // Determine relevant movement directions\n        var verticalScrollAxis = blocks[i].verticalScrollAxis.toLowerCase();\n        var horizontalScrollAxis = blocks[i].horizontalScrollAxis.toLowerCase();\n        var verticalScrollX = verticalScrollAxis.indexOf(\"x\") != -1 ? posY : 0;\n        var verticalScrollY = verticalScrollAxis.indexOf(\"y\") != -1 ? posY : 0;\n        var horizontalScrollX = horizontalScrollAxis.indexOf(\"x\") != -1 ? posX : 0;\n        var horizontalScrollY = horizontalScrollAxis.indexOf(\"y\") != -1 ? posX : 0;\n\n        var percentageY = ((verticalScrollY + horizontalScrollY - blocks[i].top + screenY) / (blocks[i].height + screenY));\n        var percentageX = ((verticalScrollX + horizontalScrollX - blocks[i].left + screenX) / (blocks[i].width + screenX));\n\n        // Subtracting initialize value, so element stays in same spot as HTML\n        positions = updatePosition(percentageX, percentageY, blocks[i].speed, blocks[i].verticalSpeed, blocks[i].horizontalSpeed);\n        var positionY = positions.y - blocks[i].baseY;\n        var positionX = positions.x - blocks[i].baseX;\n\n        // The next two \"if\" blocks go like this:\n        // Check if a limit is defined (first \"min\", then \"max\");\n        // Check if we need to change the Y or the X\n        // (Currently working only if just one of the axes is enabled)\n        // Then, check if the new position is inside the allowed limit\n        // If so, use new position. If not, set position to limit.\n\n        // Check if a min limit is defined\n        if (blocks[i].min !== null) {\n          if (self.options.vertical && !self.options.horizontal) {\n            positionY = positionY <= blocks[i].min ? blocks[i].min : positionY;\n          }\n          if (self.options.horizontal && !self.options.vertical) {\n            positionX = positionX <= blocks[i].min ? blocks[i].min : positionX;\n          }\n        }\n\n        // Check if directional min limits are defined\n        if (blocks[i].minY != null) {\n            positionY = positionY <= blocks[i].minY ? blocks[i].minY : positionY;\n        }\n        if (blocks[i].minX != null) {\n            positionX = positionX <= blocks[i].minX ? blocks[i].minX : positionX;\n        }\n\n        // Check if a max limit is defined\n        if (blocks[i].max !== null) {\n          if (self.options.vertical && !self.options.horizontal) {\n            positionY = positionY >= blocks[i].max ? blocks[i].max : positionY;\n          }\n          if (self.options.horizontal && !self.options.vertical) {\n            positionX = positionX >= blocks[i].max ? blocks[i].max : positionX;\n          }\n        }\n\n        // Check if directional max limits are defined\n        if (blocks[i].maxY != null) {\n            positionY = positionY >= blocks[i].maxY ? blocks[i].maxY : positionY;\n        }\n        if (blocks[i].maxX != null) {\n            positionX = positionX >= blocks[i].maxX ? blocks[i].maxX : positionX;\n        }\n\n        var zindex = blocks[i].zindex;\n\n        // Move that element\n        // (Set the new translation and append initial inline transforms.)\n        var translate = 'translate3d(' + (self.options.horizontal ? positionX : '0') + 'px,' + (self.options.vertical ? positionY : '0') + 'px,' + zindex + 'px) ' + blocks[i].transform;\n        self.elems[i].style[transformProp] = translate;\n      }\n      self.options.callback(positions);\n    };\n\n    self.destroy = function() {\n      for (var i = 0; i < self.elems.length; i++){\n        self.elems[i].style.cssText = blocks[i].style;\n      }\n\n      // Remove resize event listener if not pause, and pause\n      if (!pause) {\n        window.removeEventListener('resize', init);\n        pause = true;\n      }\n\n      // Clear the animation loop to prevent possible memory leak\n      clearLoop(loopId);\n      loopId = null;\n    };\n\n    // Init\n    init();\n\n    // Allow to recalculate the initial values whenever we want\n    self.refresh = init;\n\n    return self;\n  };\n  return Rellax;\n}));\n", "(function (global, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define(['module', 'exports'], factory);\n  } else if (typeof exports !== \"undefined\") {\n    factory(module, exports);\n  } else {\n    var mod = {\n      exports: {}\n    };\n    factory(mod, mod.exports);\n    global.WOW = mod.exports;\n  }\n})(this, function (module, exports) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n\n  var _class, _temp;\n\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n\n  var _createClass = function () {\n    function defineProperties(target, props) {\n      for (var i = 0; i < props.length; i++) {\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n      }\n    }\n\n    return function (Constructor, protoProps, staticProps) {\n      if (protoProps) defineProperties(Constructor.prototype, protoProps);\n      if (staticProps) defineProperties(Constructor, staticProps);\n      return Constructor;\n    };\n  }();\n\n  function isIn(needle, haystack) {\n    return haystack.indexOf(needle) >= 0;\n  }\n\n  function extend(custom, defaults) {\n    for (var key in defaults) {\n      if (custom[key] == null) {\n        var value = defaults[key];\n        custom[key] = value;\n      }\n    }\n    return custom;\n  }\n\n  function isMobile(agent) {\n    return (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(agent)\n    );\n  }\n\n  function createEvent(event) {\n    var bubble = arguments.length <= 1 || arguments[1] === undefined ? false : arguments[1];\n    var cancel = arguments.length <= 2 || arguments[2] === undefined ? false : arguments[2];\n    var detail = arguments.length <= 3 || arguments[3] === undefined ? null : arguments[3];\n\n    var customEvent = void 0;\n    if (document.createEvent != null) {\n      // W3C DOM\n      customEvent = document.createEvent('CustomEvent');\n      customEvent.initCustomEvent(event, bubble, cancel, detail);\n    } else if (document.createEventObject != null) {\n      // IE DOM < 9\n      customEvent = document.createEventObject();\n      customEvent.eventType = event;\n    } else {\n      customEvent.eventName = event;\n    }\n\n    return customEvent;\n  }\n\n  function emitEvent(elem, event) {\n    if (elem.dispatchEvent != null) {\n      // W3C DOM\n      elem.dispatchEvent(event);\n    } else if (event in (elem != null)) {\n      elem[event]();\n    } else if ('on' + event in (elem != null)) {\n      elem['on' + event]();\n    }\n  }\n\n  function addEvent(elem, event, fn) {\n    if (elem.addEventListener != null) {\n      // W3C DOM\n      elem.addEventListener(event, fn, false);\n    } else if (elem.attachEvent != null) {\n      // IE DOM\n      elem.attachEvent('on' + event, fn);\n    } else {\n      // fallback\n      elem[event] = fn;\n    }\n  }\n\n  function removeEvent(elem, event, fn) {\n    if (elem.removeEventListener != null) {\n      // W3C DOM\n      elem.removeEventListener(event, fn, false);\n    } else if (elem.detachEvent != null) {\n      // IE DOM\n      elem.detachEvent('on' + event, fn);\n    } else {\n      // fallback\n      delete elem[event];\n    }\n  }\n\n  function getInnerHeight() {\n    if ('innerHeight' in window) {\n      return window.innerHeight;\n    }\n\n    return document.documentElement.clientHeight;\n  }\n\n  // Minimalistic WeakMap shim, just in case.\n  var WeakMap = window.WeakMap || window.MozWeakMap || function () {\n    function WeakMap() {\n      _classCallCheck(this, WeakMap);\n\n      this.keys = [];\n      this.values = [];\n    }\n\n    _createClass(WeakMap, [{\n      key: 'get',\n      value: function get(key) {\n        for (var i = 0; i < this.keys.length; i++) {\n          var item = this.keys[i];\n          if (item === key) {\n            return this.values[i];\n          }\n        }\n        return undefined;\n      }\n    }, {\n      key: 'set',\n      value: function set(key, value) {\n        for (var i = 0; i < this.keys.length; i++) {\n          var item = this.keys[i];\n          if (item === key) {\n            this.values[i] = value;\n            return this;\n          }\n        }\n        this.keys.push(key);\n        this.values.push(value);\n        return this;\n      }\n    }]);\n\n    return WeakMap;\n  }();\n\n  // Dummy MutationObserver, to avoid raising exceptions.\n  var MutationObserver = window.MutationObserver || window.WebkitMutationObserver || window.MozMutationObserver || (_temp = _class = function () {\n    function MutationObserver() {\n      _classCallCheck(this, MutationObserver);\n\n      if (typeof console !== 'undefined' && console !== null) {\n        console.warn('MutationObserver is not supported by your browser.');\n        console.warn('WOW.js cannot detect dom mutations, please call .sync() after loading new content.');\n      }\n    }\n\n    _createClass(MutationObserver, [{\n      key: 'observe',\n      value: function observe() {}\n    }]);\n\n    return MutationObserver;\n  }(), _class.notSupported = true, _temp);\n\n  // getComputedStyle shim, from http://stackoverflow.com/a/21797294\n  var getComputedStyle = window.getComputedStyle || function getComputedStyle(el) {\n    var getComputedStyleRX = /(\\-([a-z]){1})/g;\n    return {\n      getPropertyValue: function getPropertyValue(prop) {\n        if (prop === 'float') {\n          prop = 'styleFloat';\n        }\n        if (getComputedStyleRX.test(prop)) {\n          prop.replace(getComputedStyleRX, function (_, _char) {\n            return _char.toUpperCase();\n          });\n        }\n        var currentStyle = el.currentStyle;\n\n        return (currentStyle != null ? currentStyle[prop] : void 0) || null;\n      }\n    };\n  };\n\n  var WOW = function () {\n    function WOW() {\n      var options = arguments.length <= 0 || arguments[0] === undefined ? {} : arguments[0];\n\n      _classCallCheck(this, WOW);\n\n      this.defaults = {\n        boxClass: 'wow',\n        animateClass: 'animated',\n        offset: 0,\n        mobile: true,\n        live: true,\n        callback: null,\n        scrollContainer: null\n      };\n\n      this.animate = function animateFactory() {\n        if ('requestAnimationFrame' in window) {\n          return function (callback) {\n            return window.requestAnimationFrame(callback);\n          };\n        }\n        return function (callback) {\n          return callback();\n        };\n      }();\n\n      this.vendors = ['moz', 'webkit'];\n\n      this.start = this.start.bind(this);\n      this.resetAnimation = this.resetAnimation.bind(this);\n      this.scrollHandler = this.scrollHandler.bind(this);\n      this.scrollCallback = this.scrollCallback.bind(this);\n      this.scrolled = true;\n      this.config = extend(options, this.defaults);\n      if (options.scrollContainer != null) {\n        this.config.scrollContainer = document.querySelector(options.scrollContainer);\n      }\n      // Map of elements to animation names:\n      this.animationNameCache = new WeakMap();\n      this.wowEvent = createEvent(this.config.boxClass);\n    }\n\n    _createClass(WOW, [{\n      key: 'init',\n      value: function init() {\n        this.element = window.document.documentElement;\n        if (isIn(document.readyState, ['interactive', 'complete'])) {\n          this.start();\n        } else {\n          addEvent(document, 'DOMContentLoaded', this.start);\n        }\n        this.finished = [];\n      }\n    }, {\n      key: 'start',\n      value: function start() {\n        var _this = this;\n\n        this.stopped = false;\n        this.boxes = [].slice.call(this.element.querySelectorAll('.' + this.config.boxClass));\n        this.all = this.boxes.slice(0);\n        if (this.boxes.length) {\n          if (this.disabled()) {\n            this.resetStyle();\n          } else {\n            for (var i = 0; i < this.boxes.length; i++) {\n              var box = this.boxes[i];\n              this.applyStyle(box, true);\n            }\n          }\n        }\n        if (!this.disabled()) {\n          addEvent(this.config.scrollContainer || window, 'scroll', this.scrollHandler);\n          addEvent(window, 'resize', this.scrollHandler);\n          this.interval = setInterval(this.scrollCallback, 50);\n        }\n        if (this.config.live) {\n          var mut = new MutationObserver(function (records) {\n            for (var j = 0; j < records.length; j++) {\n              var record = records[j];\n              for (var k = 0; k < record.addedNodes.length; k++) {\n                var node = record.addedNodes[k];\n                _this.doSync(node);\n              }\n            }\n            return undefined;\n          });\n          mut.observe(document.body, {\n            childList: true,\n            subtree: true\n          });\n        }\n      }\n    }, {\n      key: 'stop',\n      value: function stop() {\n        this.stopped = true;\n        removeEvent(this.config.scrollContainer || window, 'scroll', this.scrollHandler);\n        removeEvent(window, 'resize', this.scrollHandler);\n        if (this.interval != null) {\n          clearInterval(this.interval);\n        }\n      }\n    }, {\n      key: 'sync',\n      value: function sync() {\n        if (MutationObserver.notSupported) {\n          this.doSync(this.element);\n        }\n      }\n    }, {\n      key: 'doSync',\n      value: function doSync(element) {\n        if (typeof element === 'undefined' || element === null) {\n          element = this.element;\n        }\n        if (element.nodeType !== 1) {\n          return;\n        }\n        element = element.parentNode || element;\n        var iterable = element.querySelectorAll('.' + this.config.boxClass);\n        for (var i = 0; i < iterable.length; i++) {\n          var box = iterable[i];\n          if (!isIn(box, this.all)) {\n            this.boxes.push(box);\n            this.all.push(box);\n            if (this.stopped || this.disabled()) {\n              this.resetStyle();\n            } else {\n              this.applyStyle(box, true);\n            }\n            this.scrolled = true;\n          }\n        }\n      }\n    }, {\n      key: 'show',\n      value: function show(box) {\n        this.applyStyle(box);\n        box.className = box.className + ' ' + this.config.animateClass;\n        if (this.config.callback != null) {\n          this.config.callback(box);\n        }\n        emitEvent(box, this.wowEvent);\n\n        addEvent(box, 'animationend', this.resetAnimation);\n        addEvent(box, 'oanimationend', this.resetAnimation);\n        addEvent(box, 'webkitAnimationEnd', this.resetAnimation);\n        addEvent(box, 'MSAnimationEnd', this.resetAnimation);\n\n        return box;\n      }\n    }, {\n      key: 'applyStyle',\n      value: function applyStyle(box, hidden) {\n        var _this2 = this;\n\n        var duration = box.getAttribute('data-wow-duration');\n        var delay = box.getAttribute('data-wow-delay');\n        var iteration = box.getAttribute('data-wow-iteration');\n\n        return this.animate(function () {\n          return _this2.customStyle(box, hidden, duration, delay, iteration);\n        });\n      }\n    }, {\n      key: 'resetStyle',\n      value: function resetStyle() {\n        for (var i = 0; i < this.boxes.length; i++) {\n          var box = this.boxes[i];\n          box.style.visibility = 'visible';\n        }\n        return undefined;\n      }\n    }, {\n      key: 'resetAnimation',\n      value: function resetAnimation(event) {\n        if (event.type.toLowerCase().indexOf('animationend') >= 0) {\n          var target = event.target || event.srcElement;\n          target.className = target.className.replace(this.config.animateClass, '').trim();\n        }\n      }\n    }, {\n      key: 'customStyle',\n      value: function customStyle(box, hidden, duration, delay, iteration) {\n        if (hidden) {\n          this.cacheAnimationName(box);\n        }\n        box.style.visibility = hidden ? 'hidden' : 'visible';\n\n        if (duration) {\n          this.vendorSet(box.style, { animationDuration: duration });\n        }\n        if (delay) {\n          this.vendorSet(box.style, { animationDelay: delay });\n        }\n        if (iteration) {\n          this.vendorSet(box.style, { animationIterationCount: iteration });\n        }\n        this.vendorSet(box.style, { animationName: hidden ? 'none' : this.cachedAnimationName(box) });\n\n        return box;\n      }\n    }, {\n      key: 'vendorSet',\n      value: function vendorSet(elem, properties) {\n        for (var name in properties) {\n          if (properties.hasOwnProperty(name)) {\n            var value = properties[name];\n            elem['' + name] = value;\n            for (var i = 0; i < this.vendors.length; i++) {\n              var vendor = this.vendors[i];\n              elem['' + vendor + name.charAt(0).toUpperCase() + name.substr(1)] = value;\n            }\n          }\n        }\n      }\n    }, {\n      key: 'vendorCSS',\n      value: function vendorCSS(elem, property) {\n        var style = getComputedStyle(elem);\n        var result = style.getPropertyCSSValue(property);\n        for (var i = 0; i < this.vendors.length; i++) {\n          var vendor = this.vendors[i];\n          result = result || style.getPropertyCSSValue('-' + vendor + '-' + property);\n        }\n        return result;\n      }\n    }, {\n      key: 'animationName',\n      value: function animationName(box) {\n        var aName = void 0;\n        try {\n          aName = this.vendorCSS(box, 'animation-name').cssText;\n        } catch (error) {\n          // Opera, fall back to plain property value\n          aName = getComputedStyle(box).getPropertyValue('animation-name');\n        }\n\n        if (aName === 'none') {\n          return ''; // SVG/Firefox, unable to get animation name?\n        }\n\n        return aName;\n      }\n    }, {\n      key: 'cacheAnimationName',\n      value: function cacheAnimationName(box) {\n        // https://bugzilla.mozilla.org/show_bug.cgi?id=921834\n        // box.dataset is not supported for SVG elements in Firefox\n        return this.animationNameCache.set(box, this.animationName(box));\n      }\n    }, {\n      key: 'cachedAnimationName',\n      value: function cachedAnimationName(box) {\n        return this.animationNameCache.get(box);\n      }\n    }, {\n      key: 'scrollHandler',\n      value: function scrollHandler() {\n        this.scrolled = true;\n      }\n    }, {\n      key: 'scrollCallback',\n      value: function scrollCallback() {\n        if (this.scrolled) {\n          this.scrolled = false;\n          var results = [];\n          for (var i = 0; i < this.boxes.length; i++) {\n            var box = this.boxes[i];\n            if (box) {\n              if (this.isVisible(box)) {\n                this.show(box);\n                continue;\n              }\n              results.push(box);\n            }\n          }\n          this.boxes = results;\n          if (!this.boxes.length && !this.config.live) {\n            this.stop();\n          }\n        }\n      }\n    }, {\n      key: 'offsetTop',\n      value: function offsetTop(element) {\n        // SVG elements don't have an offsetTop in Firefox.\n        // This will use their nearest parent that has an offsetTop.\n        // Also, using ('offsetTop' of element) causes an exception in Firefox.\n        while (element.offsetTop === undefined) {\n          element = element.parentNode;\n        }\n        var top = element.offsetTop;\n        while (element.offsetParent) {\n          element = element.offsetParent;\n          top += element.offsetTop;\n        }\n        return top;\n      }\n    }, {\n      key: 'isVisible',\n      value: function isVisible(box) {\n        var offset = box.getAttribute('data-wow-offset') || this.config.offset;\n        var viewTop = this.config.scrollContainer && this.config.scrollContainer.scrollTop || window.pageYOffset;\n        var viewBottom = viewTop + Math.min(this.element.clientHeight, getInnerHeight()) - offset;\n        var top = this.offsetTop(box);\n        var bottom = top + box.clientHeight;\n\n        return top <= viewBottom && bottom >= viewTop;\n      }\n    }, {\n      key: 'disabled',\n      value: function disabled() {\n        return !this.config.mobile && isMobile(navigator.userAgent);\n      }\n    }]);\n\n    return WOW;\n  }();\n\n  exports.default = WOW;\n  module.exports = exports['default'];\n});\n", "/*!\n * Jarallax v2.2.1 (https://github.com/nk-o/jarallax)\n * Copyright 2024 nK <https://nkdev.info>\n * Licensed under MIT (https://github.com/nk-o/jarallax/blob/master/LICENSE)\n */\nvar defaults$1 = {\n  // Base parallax options.\n  type: 'scroll',\n  speed: 0.5,\n  containerClass: 'jarallax-container',\n  imgSrc: null,\n  imgElement: '.jarallax-img',\n  imgSize: 'cover',\n  imgPosition: '50% 50%',\n  imgRepeat: 'no-repeat',\n  keepImg: false,\n  elementInViewport: null,\n  zIndex: -100,\n  disableParallax: false,\n  // Callbacks.\n  onScroll: null,\n  onInit: null,\n  onDestroy: null,\n  onCoverImage: null,\n  // Video options.\n  videoClass: 'jarallax-video',\n  videoSrc: null,\n  videoStartTime: 0,\n  videoEndTime: 0,\n  videoVolume: 0,\n  videoLoop: true,\n  videoPlayOnlyVisible: true,\n  videoLazyLoading: true,\n  disableVideo: false,\n  // Video callbacks.\n  onVideoInsert: null,\n  onVideoWorkerInit: null\n};\n\n/* eslint-disable import/no-mutable-exports */\n/* eslint-disable no-restricted-globals */\nlet win$1;\nif (typeof window !== 'undefined') {\n  win$1 = window;\n} else if (typeof global !== 'undefined') {\n  win$1 = global;\n} else if (typeof self !== 'undefined') {\n  win$1 = self;\n} else {\n  win$1 = {};\n}\nvar global$2 = win$1;\n\n/**\n * Add styles to element.\n *\n * @param {Element} el - element.\n * @param {String|Object} styles - styles list.\n *\n * @returns {Element}\n */\nfunction css(el, styles) {\n  if (typeof styles === 'string') {\n    return global$2.getComputedStyle(el).getPropertyValue(styles);\n  }\n  Object.keys(styles).forEach(key => {\n    el.style[key] = styles[key];\n  });\n  return el;\n}\n\n/**\n * Extend like jQuery.extend\n *\n * @param {Object} out - output object.\n * @param {...any} args - additional objects to extend.\n *\n * @returns {Object}\n */\nfunction extend$1(out, ...args) {\n  out = out || {};\n  Object.keys(args).forEach(i => {\n    if (!args[i]) {\n      return;\n    }\n    Object.keys(args[i]).forEach(key => {\n      out[key] = args[i][key];\n    });\n  });\n  return out;\n}\n\n/**\n * Get all parents of the element.\n *\n * @param {Element} elem - DOM element.\n *\n * @returns {Array}\n */\nfunction getParents(elem) {\n  const parents = [];\n  while (elem.parentElement !== null) {\n    elem = elem.parentElement;\n    if (elem.nodeType === 1) {\n      parents.push(elem);\n    }\n  }\n  return parents;\n}\n\n/**\n * Document ready callback.\n * @param {Function} callback - callback will be fired once Document ready.\n */\nfunction ready(callback) {\n  if (document.readyState === 'complete' || document.readyState === 'interactive') {\n    // Already ready or interactive, execute callback\n    callback();\n  } else {\n    document.addEventListener('DOMContentLoaded', callback, {\n      capture: true,\n      once: true,\n      passive: true\n    });\n  }\n}\n\nconst {\n  navigator: navigator$1\n} = global$2;\nconst mobileAgent = /*#__PURE__*/ /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator$1.userAgent);\nfunction isMobile() {\n  return mobileAgent;\n}\n\nlet wndW;\nlet wndH;\nlet $deviceHelper;\n\n/**\n * The most popular mobile browsers changes height after page scroll and this generates image jumping.\n * We can fix it using this workaround with vh units.\n */\nfunction getDeviceHeight() {\n  if (!$deviceHelper && document.body) {\n    $deviceHelper = document.createElement('div');\n    $deviceHelper.style.cssText = 'position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;';\n    document.body.appendChild($deviceHelper);\n  }\n  return ($deviceHelper ? $deviceHelper.clientHeight : 0) || global$2.innerHeight || document.documentElement.clientHeight;\n}\nfunction updateWindowHeight() {\n  wndW = global$2.innerWidth || document.documentElement.clientWidth;\n  if (isMobile()) {\n    wndH = getDeviceHeight();\n  } else {\n    wndH = global$2.innerHeight || document.documentElement.clientHeight;\n  }\n}\nupdateWindowHeight();\nglobal$2.addEventListener('resize', updateWindowHeight);\nglobal$2.addEventListener('orientationchange', updateWindowHeight);\nglobal$2.addEventListener('load', updateWindowHeight);\nready(() => {\n  updateWindowHeight();\n});\nfunction getWindowSize() {\n  return {\n    width: wndW,\n    height: wndH\n  };\n}\n\n// List with all jarallax instances\n// need to render all in one scroll/resize event.\nconst jarallaxList = [];\nfunction updateParallax() {\n  if (!jarallaxList.length) {\n    return;\n  }\n  const {\n    width: wndW,\n    height: wndH\n  } = getWindowSize();\n  jarallaxList.forEach((data, k) => {\n    const {\n      instance,\n      oldData\n    } = data;\n    if (!instance.isVisible()) {\n      return;\n    }\n    const clientRect = instance.$item.getBoundingClientRect();\n    const newData = {\n      width: clientRect.width,\n      height: clientRect.height,\n      top: clientRect.top,\n      bottom: clientRect.bottom,\n      wndW,\n      wndH\n    };\n    const isResized = !oldData || oldData.wndW !== newData.wndW || oldData.wndH !== newData.wndH || oldData.width !== newData.width || oldData.height !== newData.height;\n    const isScrolled = isResized || !oldData || oldData.top !== newData.top || oldData.bottom !== newData.bottom;\n    jarallaxList[k].oldData = newData;\n    if (isResized) {\n      instance.onResize();\n    }\n    if (isScrolled) {\n      instance.onScroll();\n    }\n  });\n  global$2.requestAnimationFrame(updateParallax);\n}\nconst visibilityObserver = /*#__PURE__*/new global$2.IntersectionObserver(entries => {\n  entries.forEach(entry => {\n    entry.target.jarallax.isElementInViewport = entry.isIntersecting;\n  });\n}, {\n  // We have to start parallax calculation before the block is in view\n  // to prevent possible parallax jumping.\n  rootMargin: '50px'\n});\nfunction addObserver(instance) {\n  jarallaxList.push({\n    instance\n  });\n  if (jarallaxList.length === 1) {\n    global$2.requestAnimationFrame(updateParallax);\n  }\n  visibilityObserver.observe(instance.options.elementInViewport || instance.$item);\n}\nfunction removeObserver(instance) {\n  jarallaxList.forEach((data, key) => {\n    if (data.instance.instanceID === instance.instanceID) {\n      jarallaxList.splice(key, 1);\n    }\n  });\n  visibilityObserver.unobserve(instance.options.elementInViewport || instance.$item);\n}\n\n/* eslint-disable class-methods-use-this */\nconst {\n  navigator\n} = global$2;\nlet instanceID = 0;\n\n// Jarallax class\nclass Jarallax {\n  constructor(item, userOptions) {\n    const self = this;\n    self.instanceID = instanceID;\n    instanceID += 1;\n    self.$item = item;\n    self.defaults = {\n      ...defaults$1\n    };\n\n    // prepare data-options\n    const dataOptions = self.$item.dataset || {};\n    const pureDataOptions = {};\n    Object.keys(dataOptions).forEach(key => {\n      const lowerCaseOption = key.substr(0, 1).toLowerCase() + key.substr(1);\n      if (lowerCaseOption && typeof self.defaults[lowerCaseOption] !== 'undefined') {\n        pureDataOptions[lowerCaseOption] = dataOptions[key];\n      }\n    });\n    self.options = self.extend({}, self.defaults, pureDataOptions, userOptions);\n    self.pureOptions = self.extend({}, self.options);\n\n    // prepare 'true' and 'false' strings to boolean\n    Object.keys(self.options).forEach(key => {\n      if (self.options[key] === 'true') {\n        self.options[key] = true;\n      } else if (self.options[key] === 'false') {\n        self.options[key] = false;\n      }\n    });\n\n    // fix speed option [-1.0, 2.0]\n    self.options.speed = Math.min(2, Math.max(-1, parseFloat(self.options.speed)));\n\n    // prepare disableParallax callback\n    if (typeof self.options.disableParallax === 'string') {\n      self.options.disableParallax = new RegExp(self.options.disableParallax);\n    }\n    if (self.options.disableParallax instanceof RegExp) {\n      const disableParallaxRegexp = self.options.disableParallax;\n      self.options.disableParallax = () => disableParallaxRegexp.test(navigator.userAgent);\n    }\n    if (typeof self.options.disableParallax !== 'function') {\n      // Support for `true` option value.\n      const disableParallaxDefault = self.options.disableParallax;\n      self.options.disableParallax = () => disableParallaxDefault === true;\n    }\n\n    // prepare disableVideo callback\n    if (typeof self.options.disableVideo === 'string') {\n      self.options.disableVideo = new RegExp(self.options.disableVideo);\n    }\n    if (self.options.disableVideo instanceof RegExp) {\n      const disableVideoRegexp = self.options.disableVideo;\n      self.options.disableVideo = () => disableVideoRegexp.test(navigator.userAgent);\n    }\n    if (typeof self.options.disableVideo !== 'function') {\n      // Support for `true` option value.\n      const disableVideoDefault = self.options.disableVideo;\n      self.options.disableVideo = () => disableVideoDefault === true;\n    }\n\n    // custom element to check if parallax in viewport\n    let elementInVP = self.options.elementInViewport;\n    // get first item from array\n    if (elementInVP && typeof elementInVP === 'object' && typeof elementInVP.length !== 'undefined') {\n      [elementInVP] = elementInVP;\n    }\n    // check if dom element\n    if (!(elementInVP instanceof Element)) {\n      elementInVP = null;\n    }\n    self.options.elementInViewport = elementInVP;\n    self.image = {\n      src: self.options.imgSrc || null,\n      $container: null,\n      useImgTag: false,\n      // 1. Position fixed is needed for the most of browsers because absolute position have glitches\n      // 2. On MacOS with smooth scroll there is a huge lags with absolute position - https://github.com/nk-o/jarallax/issues/75\n      // 3. Previously used 'absolute' for mobile devices. But we re-tested on iPhone 12 and 'fixed' position is working better, then 'absolute', so for now position is always 'fixed'\n      position: 'fixed'\n    };\n    if (self.initImg() && self.canInitParallax()) {\n      self.init();\n    }\n  }\n  css(el, styles) {\n    return css(el, styles);\n  }\n  extend(out, ...args) {\n    return extend$1(out, ...args);\n  }\n\n  // get window size and scroll position. Useful for extensions\n  getWindowData() {\n    const {\n      width,\n      height\n    } = getWindowSize();\n    return {\n      width,\n      height,\n      y: document.documentElement.scrollTop\n    };\n  }\n\n  // Jarallax functions\n  initImg() {\n    const self = this;\n\n    // find image element\n    let $imgElement = self.options.imgElement;\n    if ($imgElement && typeof $imgElement === 'string') {\n      $imgElement = self.$item.querySelector($imgElement);\n    }\n\n    // check if dom element\n    if (!($imgElement instanceof Element)) {\n      if (self.options.imgSrc) {\n        $imgElement = new Image();\n        $imgElement.src = self.options.imgSrc;\n      } else {\n        $imgElement = null;\n      }\n    }\n    if ($imgElement) {\n      if (self.options.keepImg) {\n        self.image.$item = $imgElement.cloneNode(true);\n      } else {\n        self.image.$item = $imgElement;\n        self.image.$itemParent = $imgElement.parentNode;\n      }\n      self.image.useImgTag = true;\n    }\n\n    // true if there is img tag\n    if (self.image.$item) {\n      return true;\n    }\n\n    // get image src\n    if (self.image.src === null) {\n      self.image.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n      self.image.bgImage = self.css(self.$item, 'background-image');\n    }\n    return !(!self.image.bgImage || self.image.bgImage === 'none');\n  }\n  canInitParallax() {\n    return !this.options.disableParallax();\n  }\n  init() {\n    const self = this;\n    const containerStyles = {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden'\n    };\n    let imageStyles = {\n      pointerEvents: 'none',\n      transformStyle: 'preserve-3d',\n      backfaceVisibility: 'hidden'\n    };\n    if (!self.options.keepImg) {\n      // save default user styles\n      const curStyle = self.$item.getAttribute('style');\n      if (curStyle) {\n        self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n      }\n      if (self.image.useImgTag) {\n        const curImgStyle = self.image.$item.getAttribute('style');\n        if (curImgStyle) {\n          self.image.$item.setAttribute('data-jarallax-original-styles', curImgStyle);\n        }\n      }\n    }\n\n    // set relative position and z-index to the parent\n    if (self.css(self.$item, 'position') === 'static') {\n      self.css(self.$item, {\n        position: 'relative'\n      });\n    }\n    if (self.css(self.$item, 'z-index') === 'auto') {\n      self.css(self.$item, {\n        zIndex: 0\n      });\n    }\n\n    // container for parallax image\n    self.image.$container = document.createElement('div');\n    self.css(self.image.$container, containerStyles);\n    self.css(self.image.$container, {\n      'z-index': self.options.zIndex\n    });\n\n    // it will remove some image overlapping\n    // overlapping occur due to an image position fixed inside absolute position element\n    // needed only when background in fixed position\n    if (this.image.position === 'fixed') {\n      self.css(self.image.$container, {\n        '-webkit-clip-path': 'polygon(0 0, 100% 0, 100% 100%, 0 100%)',\n        'clip-path': 'polygon(0 0, 100% 0, 100% 100%, 0 100%)'\n      });\n    }\n\n    // Add container unique ID.\n    self.image.$container.setAttribute('id', `jarallax-container-${self.instanceID}`);\n\n    // Add container class.\n    if (self.options.containerClass) {\n      self.image.$container.setAttribute('class', self.options.containerClass);\n    }\n    self.$item.appendChild(self.image.$container);\n\n    // use img tag\n    if (self.image.useImgTag) {\n      imageStyles = self.extend({\n        'object-fit': self.options.imgSize,\n        'object-position': self.options.imgPosition,\n        'max-width': 'none'\n      }, containerStyles, imageStyles);\n\n      // use div with background image\n    } else {\n      self.image.$item = document.createElement('div');\n      if (self.image.src) {\n        imageStyles = self.extend({\n          'background-position': self.options.imgPosition,\n          'background-size': self.options.imgSize,\n          'background-repeat': self.options.imgRepeat,\n          'background-image': self.image.bgImage || `url(\"${self.image.src}\")`\n        }, containerStyles, imageStyles);\n      }\n    }\n    if (self.options.type === 'opacity' || self.options.type === 'scale' || self.options.type === 'scale-opacity' || self.options.speed === 1) {\n      self.image.position = 'absolute';\n    }\n\n    // 1. Check if one of parents have transform style (without this check, scroll transform will be inverted if used parallax with position fixed)\n    //    discussion - https://github.com/nk-o/jarallax/issues/9\n    // 2. Check if parents have overflow scroll\n    if (self.image.position === 'fixed') {\n      const $parents = getParents(self.$item).filter(el => {\n        const styles = global$2.getComputedStyle(el);\n        const parentTransform = styles['-webkit-transform'] || styles['-moz-transform'] || styles.transform;\n        const overflowRegex = /(auto|scroll)/;\n        return parentTransform && parentTransform !== 'none' || overflowRegex.test(styles.overflow + styles['overflow-y'] + styles['overflow-x']);\n      });\n      self.image.position = $parents.length ? 'absolute' : 'fixed';\n    }\n\n    // add position to parallax block\n    imageStyles.position = self.image.position;\n\n    // insert parallax image\n    self.css(self.image.$item, imageStyles);\n    self.image.$container.appendChild(self.image.$item);\n\n    // set initial position and size\n    self.onResize();\n    self.onScroll(true);\n\n    // call onInit event\n    if (self.options.onInit) {\n      self.options.onInit.call(self);\n    }\n\n    // remove default user background\n    if (self.css(self.$item, 'background-image') !== 'none') {\n      self.css(self.$item, {\n        'background-image': 'none'\n      });\n    }\n    addObserver(self);\n  }\n  destroy() {\n    const self = this;\n    removeObserver(self);\n\n    // return styles on container as before jarallax init\n    const originalStylesTag = self.$item.getAttribute('data-jarallax-original-styles');\n    self.$item.removeAttribute('data-jarallax-original-styles');\n    // null occurs if there is no style tag before jarallax init\n    if (!originalStylesTag) {\n      self.$item.removeAttribute('style');\n    } else {\n      self.$item.setAttribute('style', originalStylesTag);\n    }\n    if (self.image.useImgTag) {\n      // return styles on img tag as before jarallax init\n      const originalStylesImgTag = self.image.$item.getAttribute('data-jarallax-original-styles');\n      self.image.$item.removeAttribute('data-jarallax-original-styles');\n      // null occurs if there is no style tag before jarallax init\n      if (!originalStylesImgTag) {\n        self.image.$item.removeAttribute('style');\n      } else {\n        self.image.$item.setAttribute('style', originalStylesTag);\n      }\n\n      // move img tag to its default position\n      if (self.image.$itemParent) {\n        self.image.$itemParent.appendChild(self.image.$item);\n      }\n    }\n\n    // remove additional dom elements\n    if (self.image.$container) {\n      self.image.$container.parentNode.removeChild(self.image.$container);\n    }\n\n    // call onDestroy event\n    if (self.options.onDestroy) {\n      self.options.onDestroy.call(self);\n    }\n\n    // delete jarallax from item\n    delete self.$item.jarallax;\n  }\n  coverImage() {\n    const self = this;\n    const {\n      height: wndH\n    } = getWindowSize();\n    const rect = self.image.$container.getBoundingClientRect();\n    const contH = rect.height;\n    const {\n      speed\n    } = self.options;\n    const isScroll = self.options.type === 'scroll' || self.options.type === 'scroll-opacity';\n    let scrollDist = 0;\n    let resultH = contH;\n    let resultMT = 0;\n\n    // scroll parallax\n    if (isScroll) {\n      // scroll distance and height for image\n      if (speed < 0) {\n        scrollDist = speed * Math.max(contH, wndH);\n        if (wndH < contH) {\n          scrollDist -= speed * (contH - wndH);\n        }\n      } else {\n        scrollDist = speed * (contH + wndH);\n      }\n\n      // size for scroll parallax\n      if (speed > 1) {\n        resultH = Math.abs(scrollDist - wndH);\n      } else if (speed < 0) {\n        resultH = scrollDist / speed + Math.abs(scrollDist);\n      } else {\n        resultH += (wndH - contH) * (1 - speed);\n      }\n      scrollDist /= 2;\n    }\n\n    // store scroll distance\n    self.parallaxScrollDistance = scrollDist;\n\n    // vertical center\n    if (isScroll) {\n      resultMT = (wndH - resultH) / 2;\n    } else {\n      resultMT = (contH - resultH) / 2;\n    }\n\n    // apply result to item\n    self.css(self.image.$item, {\n      height: `${resultH}px`,\n      marginTop: `${resultMT}px`,\n      left: self.image.position === 'fixed' ? `${rect.left}px` : '0',\n      width: `${rect.width}px`\n    });\n\n    // call onCoverImage event\n    if (self.options.onCoverImage) {\n      self.options.onCoverImage.call(self);\n    }\n\n    // return some useful data. Used in the video cover function\n    return {\n      image: {\n        height: resultH,\n        marginTop: resultMT\n      },\n      container: rect\n    };\n  }\n  isVisible() {\n    return this.isElementInViewport || false;\n  }\n  onScroll(force) {\n    const self = this;\n\n    // stop calculations if item is not in viewport\n    if (!force && !self.isVisible()) {\n      return;\n    }\n    const {\n      height: wndH\n    } = getWindowSize();\n    const rect = self.$item.getBoundingClientRect();\n    const contT = rect.top;\n    const contH = rect.height;\n    const styles = {};\n\n    // calculate parallax helping variables\n    const beforeTop = Math.max(0, contT);\n    const beforeTopEnd = Math.max(0, contH + contT);\n    const afterTop = Math.max(0, -contT);\n    const beforeBottom = Math.max(0, contT + contH - wndH);\n    const beforeBottomEnd = Math.max(0, contH - (contT + contH - wndH));\n    const afterBottom = Math.max(0, -contT + wndH - contH);\n    const fromViewportCenter = 1 - 2 * ((wndH - contT) / (wndH + contH));\n\n    // calculate on how percent of section is visible\n    let visiblePercent = 1;\n    if (contH < wndH) {\n      visiblePercent = 1 - (afterTop || beforeBottom) / contH;\n    } else if (beforeTopEnd <= wndH) {\n      visiblePercent = beforeTopEnd / wndH;\n    } else if (beforeBottomEnd <= wndH) {\n      visiblePercent = beforeBottomEnd / wndH;\n    }\n\n    // opacity\n    if (self.options.type === 'opacity' || self.options.type === 'scale-opacity' || self.options.type === 'scroll-opacity') {\n      styles.transform = 'translate3d(0,0,0)';\n      styles.opacity = visiblePercent;\n    }\n\n    // scale\n    if (self.options.type === 'scale' || self.options.type === 'scale-opacity') {\n      let scale = 1;\n      if (self.options.speed < 0) {\n        scale -= self.options.speed * visiblePercent;\n      } else {\n        scale += self.options.speed * (1 - visiblePercent);\n      }\n      styles.transform = `scale(${scale}) translate3d(0,0,0)`;\n    }\n\n    // scroll\n    if (self.options.type === 'scroll' || self.options.type === 'scroll-opacity') {\n      let positionY = self.parallaxScrollDistance * fromViewportCenter;\n\n      // fix if parallax block in absolute position\n      if (self.image.position === 'absolute') {\n        positionY -= contT;\n      }\n      styles.transform = `translate3d(0,${positionY}px,0)`;\n    }\n    self.css(self.image.$item, styles);\n\n    // call onScroll event\n    if (self.options.onScroll) {\n      self.options.onScroll.call(self, {\n        section: rect,\n        beforeTop,\n        beforeTopEnd,\n        afterTop,\n        beforeBottom,\n        beforeBottomEnd,\n        afterBottom,\n        visiblePercent,\n        fromViewportCenter\n      });\n    }\n  }\n  onResize() {\n    this.coverImage();\n  }\n}\n\n// global definition\nconst jarallax$1 = function (items, options, ...args) {\n  // check for dom element\n  // thanks: http://stackoverflow.com/questions/384286/javascript-isdom-how-do-you-check-if-a-javascript-object-is-a-dom-object\n  if (typeof HTMLElement === 'object' ? items instanceof HTMLElement : items && typeof items === 'object' && items !== null && items.nodeType === 1 && typeof items.nodeName === 'string') {\n    items = [items];\n  }\n  const len = items.length;\n  let k = 0;\n  let ret;\n  for (k; k < len; k += 1) {\n    if (typeof options === 'object' || typeof options === 'undefined') {\n      if (!items[k].jarallax) {\n        items[k].jarallax = new Jarallax(items[k], options);\n      }\n    } else if (items[k].jarallax) {\n      // eslint-disable-next-line prefer-spread\n      ret = items[k].jarallax[options].apply(items[k].jarallax, args);\n    }\n    if (typeof ret !== 'undefined') {\n      return ret;\n    }\n  }\n  return items;\n};\njarallax$1.constructor = Jarallax;\n\n/*!\n * Video Worker v2.2.0 (https://github.com/nk-o/video-worker)\n * Copyright 2024 nK <https://nkdev.info>\n * Licensed under MIT (https://github.com/nk-o/video-worker/blob/master/LICENSE)\n */\n\nvar defaults = {\n  autoplay: false,\n  loop: false,\n  mute: false,\n  volume: 100,\n  showControls: true,\n  accessibilityHidden: false,\n  // start / end video time in seconds\n  startTime: 0,\n  endTime: 0\n};\n\n/**\n * Extend like jQuery.extend\n *\n * @param {Object} out - output object.\n * @param {...any} args - additional objects to extend.\n *\n * @returns {Object}\n */\nfunction extend(out, ...args) {\n  out = out || {};\n  Object.keys(args).forEach(i => {\n    if (!args[i]) {\n      return;\n    }\n    Object.keys(args[i]).forEach(key => {\n      out[key] = args[i][key];\n    });\n  });\n  return out;\n}\nlet ID = 0;\nclass VideoWorkerBase {\n  type = 'none';\n  constructor(url, options) {\n    const self = this;\n    self.url = url;\n    self.options_default = {\n      ...defaults\n    };\n    self.options = extend({}, self.options_default, options);\n\n    // check URL\n    self.videoID = self.constructor.parseURL(url);\n\n    // init\n    if (self.videoID) {\n      self.init();\n    }\n  }\n  isValid() {\n    return !!this.videoID;\n  }\n  init() {\n    const self = this;\n    self.ID = ID;\n    ID += 1;\n    self.playerID = `VideoWorker-${self.ID}`;\n  }\n\n  // events\n  on(name, callback) {\n    this.userEventsList = this.userEventsList || [];\n\n    // add new callback in events list\n    (this.userEventsList[name] || (this.userEventsList[name] = [])).push(callback);\n  }\n  off(name, callback) {\n    if (!this.userEventsList || !this.userEventsList[name]) {\n      return;\n    }\n    if (!callback) {\n      delete this.userEventsList[name];\n    } else {\n      this.userEventsList[name].forEach((val, key) => {\n        if (val === callback) {\n          this.userEventsList[name][key] = false;\n        }\n      });\n    }\n  }\n  fire(name, ...args) {\n    if (this.userEventsList && typeof this.userEventsList[name] !== 'undefined') {\n      this.userEventsList[name].forEach(val => {\n        // call with all arguments\n        if (val) {\n          val.apply(this, args);\n        }\n      });\n    }\n  }\n\n  /**\n   * Methods used in providers.\n   */\n  /* eslint-disable */\n  static parseURL(url) {\n    return false;\n  }\n  play(start) {}\n  pause() {}\n  mute() {}\n  unmute() {}\n  setVolume(volume = false) {}\n  getVolume(callback) {}\n  getMuted(callback) {}\n  setCurrentTime(currentTime = false) {}\n  getCurrentTime(callback) {}\n  getImageURL(callback) {}\n  getVideo(callback) {}\n  /* eslint-enable */\n}\n\n/* eslint-disable import/no-mutable-exports */\n/* eslint-disable no-restricted-globals */\nlet win;\nif (typeof window !== 'undefined') {\n  win = window;\n} else if (typeof global !== 'undefined') {\n  win = global;\n} else if (typeof self !== 'undefined') {\n  win = self;\n} else {\n  win = {};\n}\nvar global$1 = win;\n\n// Deferred\n// thanks http://stackoverflow.com/questions/18096715/implement-deferred-object-without-using-jquery\nfunction Deferred() {\n  this.doneCallbacks = [];\n  this.failCallbacks = [];\n}\nDeferred.prototype = {\n  execute(list, args) {\n    let i = list.length;\n    // eslint-disable-next-line no-param-reassign\n    args = Array.prototype.slice.call(args);\n    while (i) {\n      i -= 1;\n      list[i].apply(null, args);\n    }\n  },\n  resolve(...args) {\n    this.execute(this.doneCallbacks, args);\n  },\n  reject(...args) {\n    this.execute(this.failCallbacks, args);\n  },\n  done(callback) {\n    this.doneCallbacks.push(callback);\n  },\n  fail(callback) {\n    this.failCallbacks.push(callback);\n  }\n};\nlet YoutubeAPIadded = 0;\nlet loadingYoutubePlayer = 0;\nconst loadingYoutubeDefer = /*#__PURE__*/new Deferred();\nfunction loadAPI$1() {\n  if (YoutubeAPIadded) {\n    return;\n  }\n  YoutubeAPIadded = true;\n  const src = 'https://www.youtube.com/iframe_api';\n\n  // add script in head section\n  let tag = document.createElement('script');\n  let head = document.getElementsByTagName('head')[0];\n  tag.src = src;\n  head.appendChild(tag);\n  head = null;\n  tag = null;\n}\nfunction onAPIready$1(callback) {\n  // Listen for global YT player callback\n  if ((typeof global$1.YT === 'undefined' || global$1.YT.loaded === 0) && !loadingYoutubePlayer) {\n    // Prevents Ready event from being called twice\n    loadingYoutubePlayer = 1;\n\n    // Creates deferred so, other players know when to wait.\n    global$1.onYouTubeIframeAPIReady = function () {\n      global$1.onYouTubeIframeAPIReady = null;\n      loadingYoutubeDefer.resolve('done');\n      callback();\n    };\n  } else if (typeof global$1.YT === 'object' && global$1.YT.loaded === 1) {\n    callback();\n  } else {\n    loadingYoutubeDefer.done(() => {\n      callback();\n    });\n  }\n}\nclass VideoWorkerYoutube extends VideoWorkerBase {\n  type = 'youtube';\n  static parseURL(url) {\n    // eslint-disable-next-line no-useless-escape\n    const regExp = /.*(?:youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|shorts\\/|watch\\?v=)([^#\\&\\?]*).*/;\n    const match = url.match(regExp);\n    return match && match[1].length === 11 ? match[1] : false;\n  }\n  init() {\n    super.init();\n    loadAPI$1();\n  }\n  play(start) {\n    const self = this;\n    if (!self.player || !self.player.playVideo) {\n      return;\n    }\n    if (typeof start !== 'undefined') {\n      self.player.seekTo(start || 0);\n    }\n    if (global$1.YT.PlayerState.PLAYING !== self.player.getPlayerState()) {\n      // Don't play if video is already ended and with no loop.\n      if (self.options.endTime && !self.options.loop) {\n        self.getCurrentTime(seconds => {\n          if (seconds < self.options.endTime) {\n            self.player.playVideo();\n          }\n        });\n      } else {\n        self.player.playVideo();\n      }\n    }\n  }\n  pause() {\n    const self = this;\n    if (!self.player || !self.player.pauseVideo) {\n      return;\n    }\n    if (global$1.YT.PlayerState.PLAYING === self.player.getPlayerState()) {\n      self.player.pauseVideo();\n    }\n  }\n  mute() {\n    const self = this;\n    if (!self.player || !self.player.mute) {\n      return;\n    }\n    self.player.mute();\n  }\n  unmute() {\n    const self = this;\n    if (!self.player || !self.player.unMute) {\n      return;\n    }\n    self.player.unMute();\n  }\n  setVolume(volume = false) {\n    const self = this;\n    if (!self.player || typeof volume !== 'number' || !self.player.setVolume) {\n      return;\n    }\n    self.player.setVolume(volume);\n  }\n  getVolume(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(false);\n      return;\n    }\n    if (self.player.getVolume) {\n      callback(self.player.getVolume());\n    }\n  }\n  getMuted(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(null);\n      return;\n    }\n    if (self.player.isMuted) {\n      callback(self.player.isMuted());\n    }\n  }\n  setCurrentTime(currentTime = false) {\n    const self = this;\n    if (!self.player || typeof currentTime !== 'number' || !self.player.seekTo) {\n      return;\n    }\n    self.player.seekTo(currentTime);\n  }\n  getCurrentTime(callback) {\n    const self = this;\n    if (!self.player || !self.player.getCurrentTime) {\n      return;\n    }\n    callback(self.player.getCurrentTime());\n  }\n  getImageURL(callback) {\n    const self = this;\n    if (self.videoImage) {\n      callback(self.videoImage);\n      return;\n    }\n    const availableSizes = ['maxresdefault', 'sddefault', 'hqdefault', '0'];\n    let step = 0;\n    const tempImg = new Image();\n    tempImg.onload = function () {\n      // if no thumbnail, youtube add their own image with width = 120px\n      if ((this.naturalWidth || this.width) !== 120 || step === availableSizes.length - 1) {\n        // ok\n        self.videoImage = `https://img.youtube.com/vi/${self.videoID}/${availableSizes[step]}.jpg`;\n        callback(self.videoImage);\n      } else {\n        // try another size\n        step += 1;\n        this.src = `https://img.youtube.com/vi/${self.videoID}/${availableSizes[step]}.jpg`;\n      }\n    };\n    tempImg.src = `https://img.youtube.com/vi/${self.videoID}/${availableSizes[step]}.jpg`;\n  }\n  getVideo(callback) {\n    const self = this;\n\n    // return generated video block\n    if (self.$video) {\n      callback(self.$video);\n      return;\n    }\n\n    // generate new video block\n    onAPIready$1(() => {\n      let hiddenDiv;\n      if (!self.$video) {\n        hiddenDiv = document.createElement('div');\n        hiddenDiv.style.display = 'none';\n      }\n      self.playerOptions = {\n        // GDPR Compliance.\n        host: 'https://www.youtube-nocookie.com',\n        videoId: self.videoID,\n        playerVars: {\n          autohide: 1,\n          rel: 0,\n          autoplay: 0,\n          // autoplay enable on mobile devices\n          playsinline: 1\n        }\n      };\n\n      // hide controls\n      if (!self.options.showControls) {\n        self.playerOptions.playerVars.iv_load_policy = 3;\n        self.playerOptions.playerVars.modestbranding = 1;\n        self.playerOptions.playerVars.controls = 0;\n        self.playerOptions.playerVars.showinfo = 0;\n        self.playerOptions.playerVars.disablekb = 1;\n      }\n\n      // events\n      let ytStarted;\n      let ytProgressInterval;\n      self.playerOptions.events = {\n        onReady(e) {\n          // mute\n          if (self.options.mute) {\n            e.target.mute();\n          } else if (typeof self.options.volume === 'number') {\n            e.target.setVolume(self.options.volume);\n          }\n\n          // autoplay\n          if (self.options.autoplay) {\n            self.play(self.options.startTime);\n          }\n          self.fire('ready', e);\n\n          // For seamless loops, set the endTime to 0.1 seconds less than the video's duration\n          // https://github.com/nk-o/video-worker/issues/2\n          if (self.options.loop && !self.options.endTime) {\n            const secondsOffset = 0.1;\n            self.options.endTime = self.player.getDuration() - secondsOffset;\n          }\n\n          // volumechange\n          setInterval(() => {\n            self.getVolume(volume => {\n              if (self.options.volume !== volume) {\n                self.options.volume = volume;\n                self.fire('volumechange', e);\n              }\n            });\n          }, 150);\n        },\n        onStateChange(e) {\n          // loop\n          if (self.options.loop && e.data === global$1.YT.PlayerState.ENDED) {\n            self.play(self.options.startTime);\n          }\n          if (!ytStarted && e.data === global$1.YT.PlayerState.PLAYING) {\n            ytStarted = 1;\n            self.fire('started', e);\n          }\n          if (e.data === global$1.YT.PlayerState.PLAYING) {\n            self.fire('play', e);\n          }\n          if (e.data === global$1.YT.PlayerState.PAUSED) {\n            self.fire('pause', e);\n          }\n          if (e.data === global$1.YT.PlayerState.ENDED) {\n            self.fire('ended', e);\n          }\n\n          // progress check\n          if (e.data === global$1.YT.PlayerState.PLAYING) {\n            ytProgressInterval = setInterval(() => {\n              self.fire('timeupdate', e);\n\n              // check for end of video and play again or stop\n              if (self.options.endTime && self.player.getCurrentTime() >= self.options.endTime) {\n                if (self.options.loop) {\n                  self.play(self.options.startTime);\n                } else {\n                  self.pause();\n                }\n              }\n            }, 150);\n          } else {\n            clearInterval(ytProgressInterval);\n          }\n        },\n        onError(e) {\n          self.fire('error', e);\n        }\n      };\n      const firstInit = !self.$video;\n      if (firstInit) {\n        const div = document.createElement('div');\n        div.setAttribute('id', self.playerID);\n        hiddenDiv.appendChild(div);\n        document.body.appendChild(hiddenDiv);\n      }\n      self.player = self.player || new global$1.YT.Player(self.playerID, self.playerOptions);\n      if (firstInit) {\n        self.$video = document.getElementById(self.playerID);\n\n        // add accessibility attributes\n        if (self.options.accessibilityHidden) {\n          self.$video.setAttribute('tabindex', '-1');\n          self.$video.setAttribute('aria-hidden', 'true');\n        }\n\n        // get video width and height\n        self.videoWidth = parseInt(self.$video.getAttribute('width'), 10) || 1280;\n        self.videoHeight = parseInt(self.$video.getAttribute('height'), 10) || 720;\n      }\n      callback(self.$video);\n    });\n  }\n}\nlet VimeoAPIadded = 0;\nlet loadingVimeoPlayer = 0;\nconst loadingVimeoDefer = /*#__PURE__*/new Deferred();\nfunction loadAPI() {\n  if (VimeoAPIadded) {\n    return;\n  }\n  VimeoAPIadded = true;\n\n  // Useful when Vimeo API added using RequireJS https://github.com/nk-o/video-worker/pull/7\n  if (typeof global$1.Vimeo !== 'undefined') {\n    return;\n  }\n  const src = 'https://player.vimeo.com/api/player.js';\n\n  // add script in head section\n  let tag = document.createElement('script');\n  let head = document.getElementsByTagName('head')[0];\n  tag.src = src;\n  head.appendChild(tag);\n  head = null;\n  tag = null;\n}\nfunction onAPIready(callback) {\n  if (typeof global$1.Vimeo === 'undefined' && !loadingVimeoPlayer) {\n    loadingVimeoPlayer = 1;\n    const vimeoInterval = setInterval(() => {\n      if (typeof global$1.Vimeo !== 'undefined') {\n        clearInterval(vimeoInterval);\n        loadingVimeoDefer.resolve('done');\n        callback();\n      }\n    }, 20);\n  } else if (typeof global$1.Vimeo !== 'undefined') {\n    callback();\n  } else {\n    loadingVimeoDefer.done(() => {\n      callback();\n    });\n  }\n}\nclass VideoWorkerVimeo extends VideoWorkerBase {\n  type = 'vimeo';\n  static parseURL(url) {\n    // eslint-disable-next-line no-useless-escape\n    const regExp = /https?:\\/\\/(?:www\\.|player\\.)?vimeo.com\\/(?:channels\\/(?:\\w+\\/)?|groups\\/([^/]*)\\/videos\\/|album\\/(\\d+)\\/video\\/|video\\/|)(\\d+)(?:$|\\/|\\?)/;\n    const match = url.match(regExp);\n    return match && match[3] ? match[3] : false;\n  }\n\n  // Try to extract a hash for private videos from the URL.\n  // Thanks to https://github.com/sampotts/plyr\n  static parseURLHash(url) {\n    /* This regex matches a hexadecimal hash if given in any of these forms:\n     *  - [https://player.]vimeo.com/video/{id}/{hash}[?params]\n     *  - [https://player.]vimeo.com/video/{id}?h={hash}[&params]\n     *  - [https://player.]vimeo.com/video/{id}?[params]&h={hash}\n     *  - video/{id}/{hash}\n     * If matched, the hash is available in capture group 4\n     */\n    const regex = /^.*(vimeo.com\\/|video\\/)(\\d+)(\\?.*&*h=|\\/)+([\\d,a-f]+)/;\n    const found = url.match(regex);\n    return found && found.length === 5 ? found[4] : null;\n  }\n  init() {\n    super.init();\n    loadAPI();\n  }\n  play(start) {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    if (typeof start !== 'undefined') {\n      self.player.setCurrentTime(start);\n    }\n    self.player.getPaused().then(paused => {\n      if (paused) {\n        // Don't play if video is already ended and with no loop.\n        if (self.options.endTime && !self.options.loop) {\n          self.getCurrentTime(seconds => {\n            if (seconds < self.options.endTime) {\n              self.player.play();\n            }\n          });\n        } else {\n          self.player.play();\n        }\n      }\n    });\n  }\n  pause() {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    self.player.getPaused().then(paused => {\n      if (!paused) {\n        self.player.pause();\n      }\n    });\n  }\n  mute() {\n    const self = this;\n    if (!self.player || !self.player.setVolume) {\n      return;\n    }\n    self.setVolume(0);\n  }\n  unmute() {\n    const self = this;\n    if (!self.player || !self.player.setVolume) {\n      return;\n    }\n\n    // In case the default volume is 0, we have to set 100 when unmute.\n    self.setVolume(self.options.volume || 100);\n  }\n  setVolume(volume = false) {\n    const self = this;\n    if (!self.player || typeof volume !== 'number' || !self.player.setVolume) {\n      return;\n    }\n    self.player.setVolume(volume / 100);\n  }\n  getVolume(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(false);\n      return;\n    }\n    if (self.player.getVolume) {\n      self.player.getVolume().then(volume => {\n        callback(volume * 100);\n      });\n    }\n  }\n  getMuted(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(null);\n      return;\n    }\n    if (self.player.getVolume) {\n      self.player.getVolume().then(volume => {\n        callback(!!volume);\n      });\n    }\n  }\n  setCurrentTime(currentTime = false) {\n    const self = this;\n    if (!self.player || typeof currentTime !== 'number' || !self.player.setCurrentTime) {\n      return;\n    }\n    self.player.setCurrentTime(currentTime);\n  }\n  getCurrentTime(callback) {\n    const self = this;\n    if (!self.player || !self.player.getCurrentTime) {\n      return;\n    }\n    self.player.getCurrentTime().then(currentTime => {\n      callback(currentTime);\n    });\n  }\n  getImageURL(callback) {\n    const self = this;\n    if (self.videoImage) {\n      callback(self.videoImage);\n      return;\n    }\n\n    // We should provide width to get HQ thumbnail URL.\n    let width = global$1.innerWidth || 1920;\n    if (global$1.devicePixelRatio) {\n      width *= global$1.devicePixelRatio;\n    }\n    width = Math.min(width, 1920);\n    let request = new XMLHttpRequest();\n    // https://vimeo.com/api/oembed.json?url=https://vimeo.com/235212527\n    request.open('GET', `https://vimeo.com/api/oembed.json?url=${self.url}&width=${width}`, true);\n    request.onreadystatechange = function () {\n      if (this.readyState === 4) {\n        if (this.status >= 200 && this.status < 400) {\n          // Success!\n          const response = JSON.parse(this.responseText);\n          if (response.thumbnail_url) {\n            self.videoImage = response.thumbnail_url;\n            callback(self.videoImage);\n          }\n        }\n      }\n    };\n    request.send();\n    request = null;\n  }\n  getVideo(callback) {\n    const self = this;\n\n    // return generated video block\n    if (self.$video) {\n      callback(self.$video);\n      return;\n    }\n\n    // generate new video block\n    onAPIready(() => {\n      let hiddenDiv;\n      if (!self.$video) {\n        hiddenDiv = document.createElement('div');\n        hiddenDiv.style.display = 'none';\n      }\n      self.playerOptions = {\n        // GDPR Compliance.\n        dnt: 1,\n        id: self.videoID,\n        autopause: 0,\n        transparent: 0,\n        autoplay: self.options.autoplay ? 1 : 0,\n        loop: self.options.loop ? 1 : 0,\n        muted: self.options.mute || self.options.volume === 0 ? 1 : 0\n      };\n\n      // private video hash\n      const urlHash = self.constructor.parseURLHash(self.url);\n      if (urlHash) {\n        self.playerOptions.h = urlHash;\n      }\n\n      // hide controls\n      if (!self.options.showControls) {\n        self.playerOptions.controls = 0;\n      }\n\n      // enable background option\n      if (!self.options.showControls && self.options.loop && self.options.autoplay) {\n        self.playerOptions.background = 1;\n      }\n      if (!self.$video) {\n        let playerOptionsString = '';\n        Object.keys(self.playerOptions).forEach(key => {\n          if (playerOptionsString !== '') {\n            playerOptionsString += '&';\n          }\n          playerOptionsString += `${key}=${encodeURIComponent(self.playerOptions[key])}`;\n        });\n\n        // we need to create iframe manually because when we create it using API\n        // js events won't triggers after iframe moved to another place\n        self.$video = document.createElement('iframe');\n        self.$video.setAttribute('id', self.playerID);\n        self.$video.setAttribute('src', `https://player.vimeo.com/video/${self.videoID}?${playerOptionsString}`);\n        self.$video.setAttribute('frameborder', '0');\n        self.$video.setAttribute('mozallowfullscreen', '');\n        self.$video.setAttribute('allowfullscreen', '');\n        self.$video.setAttribute('title', 'Vimeo video player');\n\n        // add accessibility attributes\n        if (self.options.accessibilityHidden) {\n          self.$video.setAttribute('tabindex', '-1');\n          self.$video.setAttribute('aria-hidden', 'true');\n        }\n        hiddenDiv.appendChild(self.$video);\n        document.body.appendChild(hiddenDiv);\n      }\n      self.player = self.player || new global$1.Vimeo.Player(self.$video, self.playerOptions);\n\n      // Since Vimeo removed the `volume` parameter, we have to set it manually.\n      if (!self.options.mute && typeof self.options.volume === 'number') {\n        self.setVolume(self.options.volume);\n      }\n\n      // set current time for autoplay\n      if (self.options.startTime && self.options.autoplay) {\n        self.player.setCurrentTime(self.options.startTime);\n      }\n\n      // get video width and height\n      self.player.getVideoWidth().then(width => {\n        self.videoWidth = width || 1280;\n      });\n      self.player.getVideoHeight().then(height => {\n        self.videoHeight = height || 720;\n      });\n\n      // events\n      let vmStarted;\n      self.player.on('timeupdate', e => {\n        if (!vmStarted) {\n          self.fire('started', e);\n          vmStarted = 1;\n        }\n        self.fire('timeupdate', e);\n\n        // check for end of video and play again or stop\n        if (self.options.endTime && e.seconds >= self.options.endTime) {\n          if (self.options.loop) {\n            self.play(self.options.startTime);\n          } else {\n            self.pause();\n          }\n        }\n      });\n      self.player.on('play', e => {\n        self.fire('play', e);\n\n        // check for the start time and start with it\n        if (self.options.startTime && e.seconds === 0) {\n          self.play(self.options.startTime);\n        }\n      });\n      self.player.on('pause', e => {\n        self.fire('pause', e);\n      });\n      self.player.on('ended', e => {\n        self.fire('ended', e);\n      });\n      self.player.on('loaded', e => {\n        self.fire('ready', e);\n      });\n      self.player.on('volumechange', e => {\n        self.getVolume(volume => {\n          self.options.volume = volume;\n        });\n        self.fire('volumechange', e);\n      });\n      self.player.on('error', e => {\n        self.fire('error', e);\n      });\n      callback(self.$video);\n    });\n  }\n}\nclass VideoWorkerLocal extends VideoWorkerBase {\n  type = 'local';\n  static parseURL(url) {\n    // eslint-disable-next-line no-useless-escape\n    const videoFormats = url.split(/,(?=mp4\\:|webm\\:|ogv\\:|ogg\\:)/);\n    const result = {};\n    let ready = 0;\n    videoFormats.forEach(val => {\n      // eslint-disable-next-line no-useless-escape\n      const match = val.match(/^(mp4|webm|ogv|ogg)\\:(.*)/);\n      if (match && match[1] && match[2]) {\n        // eslint-disable-next-line prefer-destructuring\n        result[match[1] === 'ogv' ? 'ogg' : match[1]] = match[2];\n        ready = 1;\n      }\n    });\n    return ready ? result : false;\n  }\n  play(start) {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    if (typeof start !== 'undefined') {\n      self.player.currentTime = start;\n    }\n    if (self.player.paused) {\n      // Don't play if video is already ended and with no loop.\n      if (self.options.endTime && !self.options.loop) {\n        self.getCurrentTime(seconds => {\n          if (seconds < self.options.endTime) {\n            self.player.play();\n          }\n        });\n      } else {\n        self.player.play();\n      }\n    }\n  }\n  pause() {\n    const self = this;\n    if (!self.player || self.player.paused) {\n      return;\n    }\n    self.player.pause();\n  }\n  mute() {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    self.$video.muted = true;\n  }\n  unmute() {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    self.$video.muted = false;\n  }\n  setVolume(volume = false) {\n    const self = this;\n    if (!self.player || typeof volume !== 'number') {\n      return;\n    }\n    self.$video.volume = volume / 100;\n  }\n  getVolume(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(false);\n      return;\n    }\n    callback(self.$video.volume * 100);\n  }\n  getMuted(callback) {\n    const self = this;\n    if (!self.player) {\n      callback(null);\n      return;\n    }\n    callback(self.$video.muted);\n  }\n  setCurrentTime(currentTime = false) {\n    const self = this;\n    if (!self.player || typeof currentTime !== 'number') {\n      return;\n    }\n    self.$video.currentTime = currentTime;\n  }\n  getCurrentTime(callback) {\n    const self = this;\n    if (!self.player) {\n      return;\n    }\n    callback(self.player.currentTime);\n  }\n  getImageURL(callback) {\n    const self = this;\n    if (self.videoImage) {\n      callback(self.videoImage);\n    }\n  }\n  getVideo(callback) {\n    const self = this;\n\n    // return generated video block\n    if (self.$video) {\n      callback(self.$video);\n      return;\n    }\n\n    // generate new video block\n    let hiddenDiv;\n    if (!self.$video) {\n      hiddenDiv = document.createElement('div');\n      hiddenDiv.style.display = 'none';\n    }\n    function addSourceElement(element, src, type) {\n      const source = document.createElement('source');\n      source.src = src;\n      source.type = type;\n      element.appendChild(source);\n    }\n    if (!self.$video) {\n      self.$video = document.createElement('video');\n      self.player = self.$video;\n\n      // show controls\n      if (self.options.showControls) {\n        self.$video.controls = true;\n      }\n\n      // set volume\n      if (typeof self.options.volume === 'number') {\n        self.setVolume(self.options.volume);\n      }\n\n      // mute (it is required to mute after the volume set)\n      if (self.options.mute) {\n        self.mute();\n      }\n\n      // loop\n      if (self.options.loop) {\n        self.$video.loop = true;\n      }\n\n      // autoplay enable on mobile devices\n      self.$video.setAttribute('playsinline', '');\n      self.$video.setAttribute('webkit-playsinline', '');\n\n      // add accessibility attributes\n      if (self.options.accessibilityHidden) {\n        self.$video.setAttribute('tabindex', '-1');\n        self.$video.setAttribute('aria-hidden', 'true');\n      }\n      self.$video.setAttribute('id', self.playerID);\n      hiddenDiv.appendChild(self.$video);\n      document.body.appendChild(hiddenDiv);\n      Object.keys(self.videoID).forEach(key => {\n        addSourceElement(self.$video, self.videoID[key], `video/${key}`);\n      });\n    }\n    let locStarted;\n    self.player.addEventListener('playing', e => {\n      if (!locStarted) {\n        self.fire('started', e);\n      }\n      locStarted = 1;\n    });\n    self.player.addEventListener('timeupdate', function (e) {\n      self.fire('timeupdate', e);\n\n      // check for end of video and play again or stop\n      if (self.options.endTime && this.currentTime >= self.options.endTime) {\n        if (self.options.loop) {\n          self.play(self.options.startTime);\n        } else {\n          self.pause();\n        }\n      }\n    });\n    self.player.addEventListener('play', e => {\n      self.fire('play', e);\n    });\n    self.player.addEventListener('pause', e => {\n      self.fire('pause', e);\n    });\n    self.player.addEventListener('ended', e => {\n      self.fire('ended', e);\n    });\n    self.player.addEventListener('loadedmetadata', function () {\n      // get video width and height\n      self.videoWidth = this.videoWidth || 1280;\n      self.videoHeight = this.videoHeight || 720;\n      self.fire('ready');\n\n      // autoplay\n      if (self.options.autoplay) {\n        self.play(self.options.startTime);\n      }\n    });\n    self.player.addEventListener('volumechange', e => {\n      self.getVolume(volume => {\n        self.options.volume = volume;\n      });\n      self.fire('volumechange', e);\n    });\n    self.player.addEventListener('error', e => {\n      self.fire('error', e);\n    });\n    callback(self.$video);\n  }\n}\nfunction VideoWorker(url, options) {\n  let result = false;\n  Object.keys(VideoWorker.providers).forEach(key => {\n    if (!result && VideoWorker.providers[key].parseURL(url)) {\n      result = new VideoWorker.providers[key](url, options);\n    }\n  });\n  return result || new VideoWorkerBase(url, options);\n}\nVideoWorker.BaseClass = VideoWorkerBase;\nVideoWorker.providers = {\n  Youtube: VideoWorkerYoutube,\n  Vimeo: VideoWorkerVimeo,\n  Local: VideoWorkerLocal\n};\n\nfunction jarallaxVideo$1(jarallax = global$2.jarallax) {\n  if (typeof jarallax === 'undefined') {\n    return;\n  }\n  const Jarallax = jarallax.constructor;\n\n  // append video after when block will be visible.\n  const defOnScroll = Jarallax.prototype.onScroll;\n  Jarallax.prototype.onScroll = function () {\n    const self = this;\n    defOnScroll.apply(self);\n    const isReady = !self.isVideoInserted && self.video && (!self.options.videoLazyLoading || self.isElementInViewport) && !self.options.disableVideo();\n    if (isReady) {\n      self.isVideoInserted = true;\n      self.video.getVideo(video => {\n        const $parent = video.parentNode;\n        self.css(video, {\n          position: self.image.position,\n          top: '0px',\n          left: '0px',\n          right: '0px',\n          bottom: '0px',\n          width: '100%',\n          height: '100%',\n          maxWidth: 'none',\n          maxHeight: 'none',\n          pointerEvents: 'none',\n          transformStyle: 'preserve-3d',\n          backfaceVisibility: 'hidden',\n          margin: 0,\n          zIndex: -1\n        });\n        self.$video = video;\n\n        // add Poster attribute to self-hosted video\n        if (self.video.type === 'local') {\n          if (self.image.src) {\n            self.$video.setAttribute('poster', self.image.src);\n          } else if (self.image.$item && self.image.$item.tagName === 'IMG' && self.image.$item.src) {\n            self.$video.setAttribute('poster', self.image.$item.src);\n          }\n        }\n\n        // add classname to video element\n        if (self.options.videoClass) {\n          self.$video.setAttribute('class', `${self.options.videoClass} ${self.options.videoClass}-${self.video.type}`);\n        }\n\n        // insert video tag\n        self.image.$container.appendChild(video);\n\n        // remove parent video element (created by VideoWorker)\n        $parent.parentNode.removeChild($parent);\n\n        // call onVideoInsert event\n        if (self.options.onVideoInsert) {\n          self.options.onVideoInsert.call(self);\n        }\n      });\n    }\n  };\n\n  // cover video\n  const defCoverImage = Jarallax.prototype.coverImage;\n  Jarallax.prototype.coverImage = function () {\n    const self = this;\n    const imageData = defCoverImage.apply(self);\n    const node = self.image.$item ? self.image.$item.nodeName : false;\n    if (imageData && self.video && node && (node === 'IFRAME' || node === 'VIDEO')) {\n      let h = imageData.image.height;\n      let w = h * self.image.width / self.image.height;\n      let ml = (imageData.container.width - w) / 2;\n      let mt = imageData.image.marginTop;\n      if (imageData.container.width > w) {\n        w = imageData.container.width;\n        h = w * self.image.height / self.image.width;\n        ml = 0;\n        mt += (imageData.image.height - h) / 2;\n      }\n\n      // add video height over than need to hide controls\n      if (node === 'IFRAME') {\n        h += 400;\n        mt -= 200;\n      }\n      self.css(self.$video, {\n        width: `${w}px`,\n        marginLeft: `${ml}px`,\n        height: `${h}px`,\n        marginTop: `${mt}px`\n      });\n    }\n    return imageData;\n  };\n\n  // init video\n  const defInitImg = Jarallax.prototype.initImg;\n  Jarallax.prototype.initImg = function () {\n    const self = this;\n    const defaultResult = defInitImg.apply(self);\n    if (!self.options.videoSrc) {\n      self.options.videoSrc = self.$item.getAttribute('data-jarallax-video') || null;\n    }\n    if (self.options.videoSrc) {\n      self.defaultInitImgResult = defaultResult;\n      return true;\n    }\n    return defaultResult;\n  };\n  const defCanInitParallax = Jarallax.prototype.canInitParallax;\n  Jarallax.prototype.canInitParallax = function () {\n    const self = this;\n    let defaultResult = defCanInitParallax.apply(self);\n    if (!self.options.videoSrc) {\n      return defaultResult;\n    }\n\n    // Init video api\n    const video = new VideoWorker(self.options.videoSrc, {\n      autoplay: true,\n      loop: self.options.videoLoop,\n      showControls: false,\n      accessibilityHidden: true,\n      startTime: self.options.videoStartTime || 0,\n      endTime: self.options.videoEndTime || 0,\n      mute: !self.options.videoVolume,\n      volume: self.options.videoVolume || 0\n    });\n\n    // call onVideoWorkerInit event\n    if (self.options.onVideoWorkerInit) {\n      self.options.onVideoWorkerInit.call(self, video);\n    }\n    function resetDefaultImage() {\n      if (self.image.$default_item) {\n        self.image.$item = self.image.$default_item;\n        self.image.$item.style.display = 'block';\n\n        // set image width and height\n        self.coverImage();\n        self.onScroll();\n      }\n    }\n    if (video.isValid()) {\n      // Force enable parallax.\n      // When the parallax disabled on mobile devices, we still need to display videos.\n      // https://github.com/nk-o/jarallax/issues/159\n      if (this.options.disableParallax()) {\n        defaultResult = true;\n        self.image.position = 'absolute';\n        self.options.type = 'scroll';\n        self.options.speed = 1;\n      }\n\n      // if parallax will not be inited, we can add thumbnail on background.\n      if (!defaultResult) {\n        if (!self.defaultInitImgResult) {\n          video.getImageURL(url => {\n            // save default user styles\n            const curStyle = self.$item.getAttribute('style');\n            if (curStyle) {\n              self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n            }\n\n            // set new background\n            self.css(self.$item, {\n              'background-image': `url(\"${url}\")`,\n              'background-position': 'center',\n              'background-size': 'cover'\n            });\n          });\n        }\n\n        // init video\n      } else {\n        video.on('ready', () => {\n          if (self.options.videoPlayOnlyVisible) {\n            const oldOnScroll = self.onScroll;\n            self.onScroll = function () {\n              oldOnScroll.apply(self);\n              if (!self.videoError && (self.options.videoLoop || !self.options.videoLoop && !self.videoEnded)) {\n                if (self.isVisible()) {\n                  video.play();\n                } else {\n                  video.pause();\n                }\n              }\n            };\n          } else {\n            video.play();\n          }\n        });\n        video.on('started', () => {\n          self.image.$default_item = self.image.$item;\n          self.image.$item = self.$video;\n\n          // set video width and height\n          self.image.width = self.video.videoWidth || 1280;\n          self.image.height = self.video.videoHeight || 720;\n          self.coverImage();\n          self.onScroll();\n\n          // hide image\n          if (self.image.$default_item) {\n            self.image.$default_item.style.display = 'none';\n          }\n        });\n        video.on('ended', () => {\n          self.videoEnded = true;\n          if (!self.options.videoLoop) {\n            // show default image if Loop disabled.\n            resetDefaultImage();\n          }\n        });\n        video.on('error', () => {\n          self.videoError = true;\n\n          // show default image if video loading error.\n          resetDefaultImage();\n        });\n        self.video = video;\n\n        // set image if not exists\n        if (!self.defaultInitImgResult) {\n          // set empty image on self-hosted video if not defined\n          self.image.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n          if (video.type !== 'local') {\n            video.getImageURL(url => {\n              self.image.bgImage = `url(\"${url}\")`;\n              self.init();\n            });\n            return false;\n          }\n        }\n      }\n    }\n    return defaultResult;\n  };\n\n  // Destroy video parallax\n  const defDestroy = Jarallax.prototype.destroy;\n  Jarallax.prototype.destroy = function () {\n    const self = this;\n    if (self.image.$default_item) {\n      self.image.$item = self.image.$default_item;\n      delete self.image.$default_item;\n    }\n    defDestroy.apply(self);\n  };\n}\n\nfunction jarallaxElement$1(jarallax = global$2.jarallax) {\n  // eslint-disable-next-line no-console\n  console.warn(\"Jarallax Element extension is DEPRECATED, please, avoid using it. We recommend you look at something like `lax.js` library <https://github.com/alexfoxy/lax.js>. It is much more powerful and has a less code (in cases when you don't want to add parallax backgrounds).\");\n  if (typeof jarallax === 'undefined') {\n    return;\n  }\n  const Jarallax = jarallax.constructor;\n\n  // redefine default methods\n  ['initImg', 'canInitParallax', 'init', 'destroy', 'coverImage', 'isVisible', 'onScroll', 'onResize'].forEach(key => {\n    const def = Jarallax.prototype[key];\n    Jarallax.prototype[key] = function (...args) {\n      const self = this;\n      if (key === 'initImg' && self.$item.getAttribute('data-jarallax-element') !== null) {\n        self.options.type = 'element';\n        self.pureOptions.speed = self.$item.getAttribute('data-jarallax-element') || '100';\n      }\n      if (self.options.type !== 'element') {\n        return def.apply(self, args);\n      }\n      self.pureOptions.threshold = self.$item.getAttribute('data-threshold') || '';\n      switch (key) {\n        case 'init':\n          {\n            const speedArr = `${self.pureOptions.speed}`.split(' ');\n            self.options.speed = self.pureOptions.speed || 0;\n            self.options.speedY = speedArr[0] ? parseFloat(speedArr[0]) : 0;\n            self.options.speedX = speedArr[1] ? parseFloat(speedArr[1]) : 0;\n            const thresholdArr = self.pureOptions.threshold.split(' ');\n            self.options.thresholdY = thresholdArr[0] ? parseFloat(thresholdArr[0]) : null;\n            self.options.thresholdX = thresholdArr[1] ? parseFloat(thresholdArr[1]) : null;\n            def.apply(self, args);\n\n            // restore background image if available.\n            const originalStylesTag = self.$item.getAttribute('data-jarallax-original-styles');\n            if (originalStylesTag) {\n              self.$item.setAttribute('style', originalStylesTag);\n            }\n            return true;\n          }\n        case 'onResize':\n          {\n            const defTransform = self.css(self.$item, 'transform');\n            self.css(self.$item, {\n              transform: ''\n            });\n            const rect = self.$item.getBoundingClientRect();\n            self.itemData = {\n              width: rect.width,\n              height: rect.height,\n              y: rect.top + self.getWindowData().y,\n              x: rect.left\n            };\n            self.css(self.$item, {\n              transform: defTransform\n            });\n            break;\n          }\n        case 'onScroll':\n          {\n            const wnd = self.getWindowData();\n            const centerPercent = (wnd.y + wnd.height / 2 - self.itemData.y - self.itemData.height / 2) / (wnd.height / 2);\n            const moveY = centerPercent * self.options.speedY;\n            const moveX = centerPercent * self.options.speedX;\n            let my = moveY;\n            let mx = moveX;\n            if (self.options.thresholdY !== null && moveY > self.options.thresholdY) my = 0;\n            if (self.options.thresholdX !== null && moveX > self.options.thresholdX) mx = 0;\n            self.css(self.$item, {\n              transform: `translate3d(${mx}px,${my}px,0)`\n            });\n            break;\n          }\n        case 'initImg':\n        case 'isVisible':\n        case 'coverImage':\n          return true;\n        // no default\n      }\n      return def.apply(self, args);\n    };\n  });\n}\n\nconst jarallax = jarallax$1;\nconst jarallaxVideo = function jarallaxVideo() {\n  return jarallaxVideo$1(jarallax);\n};\nconst jarallaxElement = function jarallaxElement() {\n  return jarallaxElement$1(jarallax);\n};\n\nexport { jarallax, jarallaxElement, jarallaxVideo };\n//# sourceMappingURL=jarallax.esm.js.map\n"], "names": ["root", "factory", "module", "global", "Rellax", "el", "options", "self", "posY", "screenY", "posX", "screenX", "blocks", "pause", "loop", "callback", "loopId", "supportsPassive", "opts", "clearLoop", "transformProp", "testEl", "vendors", "vendor", "key", "validateCustomBreakpoints", "isAscending", "isNumerical", "lastVal", "i", "elements", "wrapper", "currentBreakpoint", "getCurrentBreakpoint", "w", "bp", "cacheBlocks", "block", "createBlock", "init", "setPosition", "animate", "update", "dataPercentage", "dataSpeed", "dataXsSpeed", "dataMobileSpeed", "dataTabletSpeed", "dataDesktopSpeed", "dataVerticalSpeed", "dataHorizontalSpeed", "dataVericalScrollAxis", "dataHorizontalScrollAxis", "dataZindex", "dataMin", "dataMax", "dataMinX", "dataMaxX", "dataMinY", "dataMaxY", "mapBreakpoints", "breakpoints", "wrapperPosY", "scrollPosY", "blockTop", "blockHeight", "blockLeft", "blockWidth", "percentageY", "percentageX", "speed", "verticalSpeed", "horizontalSpeed", "verticalScrollAxis", "horizontalScrollAxis", "bases", "updatePosition", "style", "transform", "searchResult", "index", "trimmedStyle", "delimiter", "oldY", "oldX", "result", "valueX", "valueY", "deferredUpdate", "positions", "verticalScrollX", "verticalScrollY", "horizontalScrollX", "horizontalScrollY", "positionY", "positionX", "zindex", "translate", "exports", "this", "_class", "_temp", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_createClass", "defineProperties", "target", "props", "descriptor", "protoProps", "staticProps", "isIn", "needle", "haystack", "extend", "custom", "defaults", "value", "isMobile", "agent", "createEvent", "event", "bubble", "cancel", "detail", "customEvent", "emitEvent", "elem", "addEvent", "fn", "removeEvent", "getInnerHeight", "WeakMap", "item", "MutationObserver", "getComputedStyle", "getComputedStyleRX", "prop", "_", "_char", "currentStyle", "WOW", "_this", "box", "mut", "records", "j", "record", "k", "node", "element", "iterable", "hidden", "_this2", "duration", "delay", "iteration", "properties", "name", "property", "aName", "results", "top", "offset", "viewTop", "viewBottom", "bottom", "defaults$1", "win$1", "global$2", "css", "styles", "extend$1", "out", "args", "getParents", "parents", "ready", "navigator$1", "mobileAgent", "wndW", "wndH", "$deviceHelper", "getDeviceHeight", "updateWindowHeight", "getWindowSize", "jarallaxList", "updateParallax", "data", "oldData", "clientRect", "newData", "isResized", "isScrolled", "visibilityObserver", "entries", "entry", "addObserver", "removeObserver", "navigator", "instanceID", "Jarallax", "userOptions", "dataOptions", "pureDataOptions", "lowerCaseOption", "disableParallaxRegexp", "disableParallaxDefault", "disableVideoRegexp", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elementInVP", "width", "height", "$imgElement", "containerStyles", "imageStyles", "curStyle", "curImgStyle", "$parents", "parentTransform", "originalStylesTag", "originalStylesImgTag", "rect", "contH", "isScroll", "scrollDist", "resultH", "resultMT", "force", "contT", "beforeTop", "beforeTopEnd", "afterTop", "beforeBottom", "beforeBottomEnd", "afterBottom", "fromViewportCenter", "visiblePercent", "scale", "jarallax$1", "items", "len", "ret", "jarall<PERSON>"], "mappings": "yPAWC,SAAUA,EAAMC,EAAS,CAIiBC,EAAO,QAI9CA,EAAA,QAAiBD,EAAS,EAG1BD,EAAK,OAASC,EAAS,CAE1B,GAAC,OAAO,OAAW,IAAc,OAASE,GAAQ,UAAY,CAC7D,IAAIC,EAAS,SAASC,EAAIC,EAAQ,CAGhC,IAAIC,EAAO,OAAO,OAAOH,EAAO,SAAS,EAErCI,EAAO,EACPC,EAAU,EACVC,EAAO,EACPC,EAAU,EACVC,EAAS,CAAE,EACXC,EAAQ,GAIRC,EAAO,OAAO,uBAChB,OAAO,6BACP,OAAO,0BACP,OAAO,yBACP,OAAO,wBACP,SAASC,EAAS,CAAE,OAAO,WAAWA,EAAU,IAAO,EAAE,CAAI,EAG3DC,EAAS,KAGTC,EAAkB,GACtB,GAAI,CACF,IAAIC,EAAO,OAAO,eAAe,CAAA,EAAI,UAAW,CAC9C,IAAK,UAAW,CACdD,EAAkB,GAE5B,CAAO,EACD,OAAO,iBAAiB,cAAe,KAAMC,CAAI,EACjD,OAAO,oBAAoB,cAAe,KAAMA,CAAI,CACrD,MAAW,CAAA,CAGZ,IAAIC,EAAY,OAAO,sBAAwB,OAAO,yBAA2B,aAG7EC,EAAgB,OAAO,eAAkB,UAAU,CACnD,IAAIC,EAAS,SAAS,cAAc,KAAK,EACzC,GAAIA,EAAO,MAAM,YAAc,KAAM,CACnC,IAAIC,EAAU,CAAC,SAAU,MAAO,IAAI,EACpC,QAASC,KAAUD,EACjB,GAAID,EAAO,MAAOC,EAAQC,CAAM,EAAI,WAAa,IAAK,OACpD,OAAOD,EAAQC,CAAM,EAAI,YAI/B,MAAO,WACf,EAAU,EAGNhB,EAAK,QAAU,CACb,MAAO,GACR,cAAe,KACf,gBAAiB,KAChB,YAAa,CAAC,IAAK,IAAK,IAAI,EAC5B,OAAQ,GACR,QAAS,KACT,kBAAmB,GACnB,MAAO,GACP,SAAU,GACV,WAAY,GACZ,mBAAoB,IACpB,qBAAsB,IACtB,SAAU,UAAW,CAAE,CACxB,EAGGD,GACF,OAAO,KAAKA,CAAO,EAAE,QAAQ,SAASkB,EAAI,CACxCjB,EAAK,QAAQiB,CAAG,EAAIlB,EAAQkB,CAAG,CACvC,CAAO,EAGH,SAASC,GAA6B,CACpC,GAAIlB,EAAK,QAAQ,YAAY,SAAW,GAAK,MAAM,QAAQA,EAAK,QAAQ,WAAW,EAAG,CACpF,IAAImB,EAAc,GACdC,EAAc,GACdC,EAQJ,GAPArB,EAAK,QAAQ,YAAY,QAAQ,SAAUsB,EAAG,CACxC,OAAOA,GAAM,WAAUF,EAAc,IACrCC,IAAY,MACVC,EAAID,IAASF,EAAc,IAEjCE,EAAUC,CACpB,CAAS,EACGH,GAAeC,EAAa,OAGlCpB,EAAK,QAAQ,YAAc,CAAC,IAAK,IAAK,IAAI,EAC1C,QAAQ,KAAK,6GAA6G,EAGxHD,GAAWA,EAAQ,aACrBmB,EAA2B,EAIxBpB,IACHA,EAAK,WAIP,IAAIyB,EAAW,OAAOzB,GAAO,SAAW,SAAS,iBAAiBA,CAAE,EAAI,CAACA,CAAE,EAG3E,GAAIyB,EAAS,OAAS,EACpBvB,EAAK,MAAQuB,MAIV,CACH,QAAQ,KAAK,2DAA2D,EACxE,OAIF,GAAIvB,EAAK,QAAQ,SACX,CAACA,EAAK,QAAQ,QAAQ,SAAU,CAClC,IAAIwB,EAAU,SAAS,cAAcxB,EAAK,QAAQ,OAAO,EAEzD,GAAIwB,EACFxB,EAAK,QAAQ,QAAUwB,MAClB,CACL,QAAQ,KAAK,yDAAyD,EACtE,QAMN,IAAIC,EAGAC,EAAuB,SAAUC,EAAG,CACtC,IAAIC,EAAK5B,EAAK,QAAQ,YACtB,OAAI2B,EAAIC,EAAG,CAAC,EAAU,KAClBD,GAAKC,EAAG,CAAC,GAAKD,EAAIC,EAAG,CAAC,EAAU,KAChCD,GAAKC,EAAG,CAAC,GAAKD,EAAIC,EAAG,CAAC,EAAU,KAC7B,IACR,EAGGC,EAAc,UAAW,CAC3B,QAASP,EAAI,EAAGA,EAAItB,EAAK,MAAM,OAAQsB,IAAI,CACzC,IAAIQ,EAAQC,EAAY/B,EAAK,MAAMsB,CAAC,CAAC,EACrCjB,EAAO,KAAKyB,CAAK,EAEpB,EAKGE,EAAO,UAAW,CACpB,QAASV,EAAI,EAAGA,EAAIjB,EAAO,OAAQiB,IACjCtB,EAAK,MAAMsB,CAAC,EAAE,MAAM,QAAUjB,EAAOiB,CAAC,EAAE,MAG1CjB,EAAS,CAAE,EAEXH,EAAU,OAAO,YACjBE,EAAU,OAAO,WACjBqB,EAAoBC,EAAqBtB,CAAO,EAEhD6B,EAAa,EAEbJ,EAAa,EAEbK,EAAS,EAGL5B,IACF,OAAO,iBAAiB,SAAU0B,CAAI,EACtC1B,EAAQ,GAER6B,EAAQ,EAEX,EAKGJ,EAAc,SAASjC,EAAI,CAC7B,IAAIsC,EAAiBtC,EAAG,aAAc,wBAA0B,EAC5DuC,EAAYvC,EAAG,aAAc,mBAAqB,EAClDwC,EAAcxC,EAAG,aAAc,sBAAwB,EACvDyC,EAAkBzC,EAAG,aAAc,0BAA4B,EAC/D0C,EAAkB1C,EAAG,aAAc,0BAA4B,EAC/D2C,EAAmB3C,EAAG,aAAc,2BAA6B,EACjE4C,EAAoB5C,EAAG,aAAa,4BAA4B,EAChE6C,EAAsB7C,EAAG,aAAa,8BAA8B,EACpE8C,EAAwB9C,EAAG,aAAa,kCAAkC,EAC1E+C,EAA2B/C,EAAG,aAAa,oCAAoC,EAC/EgD,EAAahD,EAAG,aAAc,oBAAsB,GAAI,EACxDiD,EAAUjD,EAAG,aAAc,iBAAmB,EAC9CkD,EAAUlD,EAAG,aAAc,iBAAmB,EAC9CmD,GAAWnD,EAAG,aAAa,mBAAmB,EAC9CoD,GAAWpD,EAAG,aAAa,mBAAmB,EAC9CqD,GAAWrD,EAAG,aAAa,mBAAmB,EAC9CsD,GAAWtD,EAAG,aAAa,mBAAmB,EAC9CuD,EACAC,GAAc,GAEd,CAAChB,GAAe,CAACC,GAAmB,CAACC,GAAmB,CAACC,EAC3Da,GAAc,GAEdD,EAAiB,CACf,GAAMf,EACN,GAAMC,EACN,GAAMC,EACN,GAAMC,CACP,EAQH,IAAIc,GAAcvD,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAQ,UAAa,OAAO,aAAe,SAAS,gBAAgB,WAAa,SAAS,KAAK,UAErJ,GAAIA,EAAK,QAAQ,kBAAmB,CAClC,IAAIwD,GAAc,OAAO,aAAe,SAAS,gBAAgB,WAAa,SAAS,KAAK,UAC5FD,GAAcC,GAAaxD,EAAK,QAAQ,QAAQ,UAElD,IAAIC,GAAOD,EAAK,QAAQ,WAAaoC,GAAkBpC,EAAK,QAAQ,QAASuD,GAAoB,EAC7FpD,GAAOH,EAAK,QAAQ,aAAeoC,GAAkBpC,EAAK,QAAQ,QAASA,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAQ,WAAc,OAAO,aAAe,SAAS,gBAAgB,YAAc,SAAS,KAAK,WAAoB,EAEpOyD,GAAWxD,GAAOH,EAAG,sBAAuB,EAAC,IAC7C4D,GAAc5D,EAAG,cAAgBA,EAAG,cAAgBA,EAAG,aAEvD6D,GAAYxD,GAAOL,EAAG,sBAAuB,EAAC,KAC9C8D,GAAa9D,EAAG,aAAeA,EAAG,aAAeA,EAAG,YAGpD+D,GAAczB,IAAmCnC,GAAOwD,GAAWvD,IAAYwD,GAAcxD,GAC7F4D,GAAc1B,IAAmCjC,GAAOwD,GAAYvD,IAAYwD,GAAaxD,GAC9FJ,EAAK,QAAQ,SAAS8D,GAAc,GAAKD,GAAc,IAG1D,IAAIE,GAAST,IAAeD,EAAe5B,CAAiB,IAAM,KAAQ,OAAO4B,EAAe5B,CAAiB,CAAC,EAAKY,GAAwBrC,EAAK,QAAQ,MACxJgE,GAAgBtB,GAAwC1C,EAAK,QAAQ,cACrEiE,GAAkBtB,GAA4C3C,EAAK,QAAQ,gBAG3EkE,GAAqBtB,GAAgD5C,EAAK,QAAQ,mBAClFmE,GAAuBtB,GAAsD7C,EAAK,QAAQ,qBAE1FoE,GAAQC,EAAeP,GAAaD,GAAaE,GAAOC,GAAeC,EAAe,EAItFK,EAAQxE,EAAG,MAAM,QACjByE,EAAY,GAGZC,GAAe,iBAAiB,KAAKF,CAAK,EAC9C,GAAIE,GAAc,CAEhB,IAAIC,GAAQD,GAAa,MAGrBE,EAAeJ,EAAM,MAAMG,EAAK,EAChCE,GAAYD,EAAa,QAAQ,GAAG,EAGpCC,GACFJ,EAAY,IAAMG,EAAa,MAAM,GAAIC,EAAS,EAAE,QAAQ,MAAM,EAAE,EAEpEJ,EAAY,IAAMG,EAAa,MAAM,EAAE,EAAE,QAAQ,MAAM,EAAE,EAI7D,MAAO,CACL,MAAON,GAAM,EACb,MAAOA,GAAM,EACb,IAAKX,GACL,KAAME,GACN,OAAQD,GACR,MAAOE,GACP,MAAOG,GACP,cAAeC,GACf,gBAAiBC,GACjB,mBAAoBC,GACpB,qBAAsBC,GACtB,MAAOG,EACP,UAAWC,EACX,OAAQzB,EACR,IAAKC,EACL,IAAKC,EACL,KAAMC,GACN,KAAMC,GACN,KAAMC,GACN,KAAMC,EACP,CACF,EAKGnB,EAAc,UAAW,CAC3B,IAAI2C,EAAO3E,EACP4E,EAAO1E,EAKX,GAHAF,EAAOD,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAQ,WAAa,SAAS,iBAAmB,SAAS,KAAK,YAAc,SAAS,MAAM,WAAa,OAAO,YAC3JG,EAAOH,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAQ,YAAc,SAAS,iBAAmB,SAAS,KAAK,YAAc,SAAS,MAAM,YAAc,OAAO,YAEzJA,EAAK,QAAQ,kBAAmB,CAClC,IAAIwD,GAAc,SAAS,iBAAmB,SAAS,KAAK,YAAc,SAAS,MAAM,WAAa,OAAO,YAC7GvD,EAAOuD,EAAaxD,EAAK,QAAQ,QAAQ,UAS3C,MALI,GAAA4E,GAAQ3E,GAAQD,EAAK,QAAQ,UAK7B6E,GAAQ1E,GAAQH,EAAK,QAAQ,WAOlC,EAKGqE,EAAiB,SAASP,EAAaD,EAAaE,EAAOC,EAAeC,EAAiB,CAC7F,IAAIa,EAAS,CAAE,EACXC,GAAWd,GAAoCF,IAAU,KAAO,EAAID,IACpEkB,GAAWhB,GAAgCD,IAAU,KAAO,EAAIF,IAEpE,OAAAiB,EAAO,EAAI9E,EAAK,QAAQ,MAAQ,KAAK,MAAM+E,CAAM,EAAI,KAAK,MAAMA,EAAS,GAAG,EAAI,IAChFD,EAAO,EAAI9E,EAAK,QAAQ,MAAQ,KAAK,MAAMgF,CAAM,EAAI,KAAK,MAAMA,EAAS,GAAG,EAAI,IAEzEF,CACR,EAGGG,EAAiB,UAAW,CAC9B,OAAO,oBAAoB,SAAUA,CAAc,EACnD,OAAO,oBAAoB,oBAAqBA,CAAc,GAC7DjF,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAU,QAAQ,oBAAoB,SAAUiF,CAAc,GAClGjF,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAU,UAAU,oBAAoB,YAAaiF,CAAc,EAGxGxE,EAASF,EAAK4B,CAAM,CACrB,EAGGA,EAAS,UAAW,CAClBF,EAAW,GAAM3B,IAAU,IAC7B4B,EAAS,EAGTzB,EAASF,EAAK4B,CAAM,IAEpB1B,EAAS,KAGT,OAAO,iBAAiB,SAAUwE,CAAc,EAChD,OAAO,iBAAiB,oBAAqBA,CAAc,GAC1DjF,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAU,QAAQ,iBAAiB,SAAUiF,EAAgBvE,EAAkB,CAAE,QAAS,EAAM,EAAG,EAAK,GAC5IV,EAAK,QAAQ,QAAUA,EAAK,QAAQ,QAAU,UAAU,iBAAiB,YAAaiF,EAAgBvE,EAAkB,CAAE,QAAS,EAAM,EAAG,EAAK,EAErJ,EAGGwB,EAAU,UAAW,CAEvB,QADIgD,EACK5D,EAAI,EAAGA,EAAItB,EAAK,MAAM,OAAQsB,IAAI,CAEzC,IAAI4C,EAAqB7D,EAAOiB,CAAC,EAAE,mBAAmB,YAAa,EAC/D6C,EAAuB9D,EAAOiB,CAAC,EAAE,qBAAqB,YAAa,EACnE6D,EAAkBjB,EAAmB,QAAQ,GAAG,GAAK,GAAKjE,EAAO,EACjEmF,EAAkBlB,EAAmB,QAAQ,GAAG,GAAK,GAAKjE,EAAO,EACjEoF,EAAoBlB,EAAqB,QAAQ,GAAG,GAAK,GAAKhE,EAAO,EACrEmF,EAAoBnB,EAAqB,QAAQ,GAAG,GAAK,GAAKhE,EAAO,EAErE0D,GAAgBuB,EAAkBE,EAAoBjF,EAAOiB,CAAC,EAAE,IAAMpB,IAAYG,EAAOiB,CAAC,EAAE,OAASpB,GACrG4D,GAAgBqB,EAAkBE,EAAoBhF,EAAOiB,CAAC,EAAE,KAAOlB,IAAYC,EAAOiB,CAAC,EAAE,MAAQlB,GAGzG8E,EAAYb,EAAeP,EAAaD,EAAaxD,EAAOiB,CAAC,EAAE,MAAOjB,EAAOiB,CAAC,EAAE,cAAejB,EAAOiB,CAAC,EAAE,eAAe,EACxH,IAAIiE,EAAYL,EAAU,EAAI7E,EAAOiB,CAAC,EAAE,MACpCkE,EAAYN,EAAU,EAAI7E,EAAOiB,CAAC,EAAE,MAUpCjB,EAAOiB,CAAC,EAAE,MAAQ,OAChBtB,EAAK,QAAQ,UAAY,CAACA,EAAK,QAAQ,aACzCuF,EAAYA,GAAalF,EAAOiB,CAAC,EAAE,IAAMjB,EAAOiB,CAAC,EAAE,IAAMiE,GAEvDvF,EAAK,QAAQ,YAAc,CAACA,EAAK,QAAQ,WAC3CwF,EAAYA,GAAanF,EAAOiB,CAAC,EAAE,IAAMjB,EAAOiB,CAAC,EAAE,IAAMkE,IAKzDnF,EAAOiB,CAAC,EAAE,MAAQ,OAClBiE,EAAYA,GAAalF,EAAOiB,CAAC,EAAE,KAAOjB,EAAOiB,CAAC,EAAE,KAAOiE,GAE3DlF,EAAOiB,CAAC,EAAE,MAAQ,OAClBkE,EAAYA,GAAanF,EAAOiB,CAAC,EAAE,KAAOjB,EAAOiB,CAAC,EAAE,KAAOkE,GAI3DnF,EAAOiB,CAAC,EAAE,MAAQ,OAChBtB,EAAK,QAAQ,UAAY,CAACA,EAAK,QAAQ,aACzCuF,EAAYA,GAAalF,EAAOiB,CAAC,EAAE,IAAMjB,EAAOiB,CAAC,EAAE,IAAMiE,GAEvDvF,EAAK,QAAQ,YAAc,CAACA,EAAK,QAAQ,WAC3CwF,EAAYA,GAAanF,EAAOiB,CAAC,EAAE,IAAMjB,EAAOiB,CAAC,EAAE,IAAMkE,IAKzDnF,EAAOiB,CAAC,EAAE,MAAQ,OAClBiE,EAAYA,GAAalF,EAAOiB,CAAC,EAAE,KAAOjB,EAAOiB,CAAC,EAAE,KAAOiE,GAE3DlF,EAAOiB,CAAC,EAAE,MAAQ,OAClBkE,EAAYA,GAAanF,EAAOiB,CAAC,EAAE,KAAOjB,EAAOiB,CAAC,EAAE,KAAOkE,GAG/D,IAAIC,EAASpF,EAAOiB,CAAC,EAAE,OAInBoE,EAAY,gBAAkB1F,EAAK,QAAQ,WAAawF,EAAY,KAAO,OAASxF,EAAK,QAAQ,SAAWuF,EAAY,KAAO,MAAQE,EAAS,OAASpF,EAAOiB,CAAC,EAAE,UACvKtB,EAAK,MAAMsB,CAAC,EAAE,MAAMT,CAAa,EAAI6E,EAEvC1F,EAAK,QAAQ,SAASkF,CAAS,CAChC,EAED,OAAAlF,EAAK,QAAU,UAAW,CACxB,QAASsB,EAAI,EAAGA,EAAItB,EAAK,MAAM,OAAQsB,IACrCtB,EAAK,MAAMsB,CAAC,EAAE,MAAM,QAAUjB,EAAOiB,CAAC,EAAE,MAIrChB,IACH,OAAO,oBAAoB,SAAU0B,CAAI,EACzC1B,EAAQ,IAIVM,EAAUH,CAAM,EAChBA,EAAS,IACV,EAGDuB,EAAM,EAGNhC,EAAK,QAAUgC,EAERhC,CACR,EACD,OAAOH,CACT,CAAC,8EChfA,SAAUD,EAAQF,EAAS,CAIxBA,EAAQC,EAAQgG,CAAO,CAQ3B,GAAGC,GAAM,SAAUjG,EAAQgG,EAAS,CAGlC,OAAO,eAAeA,EAAS,aAAc,CAC3C,MAAO,EACX,CAAG,EAED,IAAIE,EAAQC,EAEZ,SAASC,EAAgBC,EAAUC,EAAa,CAC9C,GAAI,EAAED,aAAoBC,GACxB,MAAM,IAAI,UAAU,mCAAmC,EAI3D,IAAIC,EAAe,UAAY,CAC7B,SAASC,EAAiBC,EAAQC,EAAO,CACvC,QAAS/E,EAAI,EAAGA,EAAI+E,EAAM,OAAQ/E,IAAK,CACrC,IAAIgF,EAAaD,EAAM/E,CAAC,EACxBgF,EAAW,WAAaA,EAAW,YAAc,GACjDA,EAAW,aAAe,GACtB,UAAWA,IAAYA,EAAW,SAAW,IACjD,OAAO,eAAeF,EAAQE,EAAW,IAAKA,CAAU,GAI5D,OAAO,SAAUL,EAAaM,EAAYC,EAAa,CACrD,OAAID,GAAYJ,EAAiBF,EAAY,UAAWM,CAAU,EAC9DC,GAAaL,EAAiBF,EAAaO,CAAW,EACnDP,CACR,CACL,EAAK,EAEH,SAASQ,EAAKC,EAAQC,EAAU,CAC9B,OAAOA,EAAS,QAAQD,CAAM,GAAK,EAGrC,SAASE,EAAOC,EAAQC,EAAU,CAChC,QAAS7F,KAAO6F,EACd,GAAID,EAAO5F,CAAG,GAAK,KAAM,CACvB,IAAI8F,EAAQD,EAAS7F,CAAG,EACxB4F,EAAO5F,CAAG,EAAI8F,EAGlB,OAAOF,EAGT,SAASG,EAASC,EAAO,CACvB,MAAQ,iEAAiE,KAAKA,CAAK,EAIrF,SAASC,EAAYC,EAAO,CAC1B,IAAIC,EAAS,UAAU,QAAU,GAAK,UAAU,CAAC,IAAM,OAAY,GAAQ,UAAU,CAAC,EAClFC,EAAS,UAAU,QAAU,GAAK,UAAU,CAAC,IAAM,OAAY,GAAQ,UAAU,CAAC,EAClFC,EAAS,UAAU,QAAU,GAAK,UAAU,CAAC,IAAM,OAAY,KAAO,UAAU,CAAC,EAEjFC,EAAc,OAClB,OAAI,SAAS,aAAe,MAE1BA,EAAc,SAAS,YAAY,aAAa,EAChDA,EAAY,gBAAgBJ,EAAOC,EAAQC,EAAQC,CAAM,GAChD,SAAS,mBAAqB,MAEvCC,EAAc,SAAS,kBAAmB,EAC1CA,EAAY,UAAYJ,GAExBI,EAAY,UAAYJ,EAGnBI,EAGT,SAASC,EAAUC,EAAMN,EAAO,CAC1BM,EAAK,eAAiB,KAExBA,EAAK,cAAcN,CAAK,EACfA,KAAUM,GAAQ,MAC3BA,EAAKN,CAAK,EAAG,EACJ,KAAOA,KAAUM,GAAQ,OAClCA,EAAK,KAAON,CAAK,EAAG,EAIxB,SAASO,EAASD,EAAMN,EAAOQ,EAAI,CAC7BF,EAAK,kBAAoB,KAE3BA,EAAK,iBAAiBN,EAAOQ,EAAI,EAAK,EAC7BF,EAAK,aAAe,KAE7BA,EAAK,YAAY,KAAON,EAAOQ,CAAE,EAGjCF,EAAKN,CAAK,EAAIQ,EAIlB,SAASC,EAAYH,EAAMN,EAAOQ,EAAI,CAChCF,EAAK,qBAAuB,KAE9BA,EAAK,oBAAoBN,EAAOQ,EAAI,EAAK,EAChCF,EAAK,aAAe,KAE7BA,EAAK,YAAY,KAAON,EAAOQ,CAAE,EAGjC,OAAOF,EAAKN,CAAK,EAIrB,SAASU,GAAiB,CACxB,MAAI,gBAAiB,OACZ,OAAO,YAGT,SAAS,gBAAgB,aAIlC,IAAIC,EAAU,OAAO,SAAW,OAAO,YAAc,UAAY,CAC/D,SAASA,GAAU,CACjB/B,EAAgB,KAAM+B,CAAO,EAE7B,KAAK,KAAO,CAAE,EACd,KAAK,OAAS,CAAE,EAGlB,OAAA5B,EAAa4B,EAAS,CAAC,CACrB,IAAK,MACL,MAAO,SAAa7G,EAAK,CACvB,QAASK,EAAI,EAAGA,EAAI,KAAK,KAAK,OAAQA,IAAK,CACzC,IAAIyG,EAAO,KAAK,KAAKzG,CAAC,EACtB,GAAIyG,IAAS9G,EACX,OAAO,KAAK,OAAOK,CAAC,GAKhC,EAAO,CACD,IAAK,MACL,MAAO,SAAaL,EAAK8F,EAAO,CAC9B,QAASzF,EAAI,EAAGA,EAAI,KAAK,KAAK,OAAQA,IAAK,CACzC,IAAIyG,EAAO,KAAK,KAAKzG,CAAC,EACtB,GAAIyG,IAAS9G,EACX,YAAK,OAAOK,CAAC,EAAIyF,EACV,KAGX,YAAK,KAAK,KAAK9F,CAAG,EAClB,KAAK,OAAO,KAAK8F,CAAK,EACf,MAEV,CAAC,EAEKe,CACX,EAAK,EAGCE,EAAmB,OAAO,kBAAoB,OAAO,wBAA0B,OAAO,sBAAwBlC,EAAQD,EAAS,UAAY,CAC7I,SAASmC,GAAmB,CAC1BjC,EAAgB,KAAMiC,CAAgB,EAElC,OAAO,QAAY,KAAe,UAAY,OAChD,QAAQ,KAAK,oDAAoD,EACjE,QAAQ,KAAK,oFAAoF,GAIrG,OAAA9B,EAAa8B,EAAkB,CAAC,CAC9B,IAAK,UACL,MAAO,UAAmB,CAAA,EAC3B,CAAC,EAEKA,CACR,EAAA,EAAInC,EAAO,aAAe,GAAMC,GAG7BmC,EAAmB,OAAO,kBAAoB,SAA0BnI,EAAI,CAC9E,IAAIoI,EAAqB,kBACzB,MAAO,CACL,iBAAkB,SAA0BC,EAAM,CAC5CA,IAAS,UACXA,EAAO,cAELD,EAAmB,KAAKC,CAAI,GAC9BA,EAAK,QAAQD,EAAoB,SAAUE,EAAGC,EAAO,CACnD,OAAOA,EAAM,YAAa,CACtC,CAAW,EAEH,IAAIC,EAAexI,EAAG,aAEtB,OAAQwI,GAAgB,KAAOA,EAAaH,CAAI,EAAI,SAAW,KAElE,CACF,EAEGI,EAAM,UAAY,CACpB,SAASA,GAAM,CACb,IAAIxI,EAAU,UAAU,QAAU,GAAK,UAAU,CAAC,IAAM,OAAY,GAAK,UAAU,CAAC,EAEpFgG,EAAgB,KAAMwC,CAAG,EAEzB,KAAK,SAAW,CACd,SAAU,MACV,aAAc,WACd,OAAQ,EACR,OAAQ,GACR,KAAM,GACN,SAAU,KACV,gBAAiB,IAClB,EAED,KAAK,QAAU,UAA0B,CACvC,MAAI,0BAA2B,OACtB,SAAU/H,EAAU,CACzB,OAAO,OAAO,sBAAsBA,CAAQ,CAC7C,EAEI,SAAUA,EAAU,CACzB,OAAOA,EAAU,CAClB,CACT,EAAS,EAEH,KAAK,QAAU,CAAC,MAAO,QAAQ,EAE/B,KAAK,MAAQ,KAAK,MAAM,KAAK,IAAI,EACjC,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,SAAW,GAChB,KAAK,OAASoG,EAAO7G,EAAS,KAAK,QAAQ,EACvCA,EAAQ,iBAAmB,OAC7B,KAAK,OAAO,gBAAkB,SAAS,cAAcA,EAAQ,eAAe,GAG9E,KAAK,mBAAqB,IAAI+H,EAC9B,KAAK,SAAWZ,EAAY,KAAK,OAAO,QAAQ,EAGlD,OAAAhB,EAAaqC,EAAK,CAAC,CACjB,IAAK,OACL,MAAO,UAAgB,CACrB,KAAK,QAAU,OAAO,SAAS,gBAC3B9B,EAAK,SAAS,WAAY,CAAC,cAAe,UAAU,CAAC,EACvD,KAAK,MAAO,EAEZiB,EAAS,SAAU,mBAAoB,KAAK,KAAK,EAEnD,KAAK,SAAW,CAAE,EAE1B,EAAO,CACD,IAAK,QACL,MAAO,UAAiB,CACtB,IAAIc,EAAQ,KAKZ,GAHA,KAAK,QAAU,GACf,KAAK,MAAQ,CAAA,EAAG,MAAM,KAAK,KAAK,QAAQ,iBAAiB,IAAM,KAAK,OAAO,QAAQ,CAAC,EACpF,KAAK,IAAM,KAAK,MAAM,MAAM,CAAC,EACzB,KAAK,MAAM,OACb,GAAI,KAAK,WACP,KAAK,WAAY,MAEjB,SAASlH,EAAI,EAAGA,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC1C,IAAImH,EAAM,KAAK,MAAMnH,CAAC,EACtB,KAAK,WAAWmH,EAAK,EAAI,EAS/B,GALK,KAAK,aACRf,EAAS,KAAK,OAAO,iBAAmB,OAAQ,SAAU,KAAK,aAAa,EAC5EA,EAAS,OAAQ,SAAU,KAAK,aAAa,EAC7C,KAAK,SAAW,YAAY,KAAK,eAAgB,EAAE,GAEjD,KAAK,OAAO,KAAM,CACpB,IAAIgB,EAAM,IAAIV,EAAiB,SAAUW,EAAS,CAChD,QAASC,EAAI,EAAGA,EAAID,EAAQ,OAAQC,IAElC,QADIC,EAASF,EAAQC,CAAC,EACbE,EAAI,EAAGA,EAAID,EAAO,WAAW,OAAQC,IAAK,CACjD,IAAIC,EAAOF,EAAO,WAAWC,CAAC,EAC9BN,EAAM,OAAOO,CAAI,EAIjC,CAAW,EACDL,EAAI,QAAQ,SAAS,KAAM,CACzB,UAAW,GACX,QAAS,EACrB,CAAW,GAGX,EAAO,CACD,IAAK,OACL,MAAO,UAAgB,CACrB,KAAK,QAAU,GACfd,EAAY,KAAK,OAAO,iBAAmB,OAAQ,SAAU,KAAK,aAAa,EAC/EA,EAAY,OAAQ,SAAU,KAAK,aAAa,EAC5C,KAAK,UAAY,MACnB,cAAc,KAAK,QAAQ,EAGrC,EAAO,CACD,IAAK,OACL,MAAO,UAAgB,CACjBI,EAAiB,cACnB,KAAK,OAAO,KAAK,OAAO,EAGlC,EAAO,CACD,IAAK,SACL,MAAO,SAAgBgB,EAAS,CAI9B,IAHI,OAAOA,EAAY,KAAeA,IAAY,QAChDA,EAAU,KAAK,SAEbA,EAAQ,WAAa,EAGzB,CAAAA,EAAUA,EAAQ,YAAcA,EAEhC,QADIC,EAAWD,EAAQ,iBAAiB,IAAM,KAAK,OAAO,QAAQ,EACzD1H,EAAI,EAAGA,EAAI2H,EAAS,OAAQ3H,IAAK,CACxC,IAAImH,EAAMQ,EAAS3H,CAAC,EACfmF,EAAKgC,EAAK,KAAK,GAAG,IACrB,KAAK,MAAM,KAAKA,CAAG,EACnB,KAAK,IAAI,KAAKA,CAAG,EACb,KAAK,SAAW,KAAK,SAAQ,EAC/B,KAAK,WAAY,EAEjB,KAAK,WAAWA,EAAK,EAAI,EAE3B,KAAK,SAAW,MAI5B,EAAO,CACD,IAAK,OACL,MAAO,SAAcA,EAAK,CACxB,YAAK,WAAWA,CAAG,EACnBA,EAAI,UAAYA,EAAI,UAAY,IAAM,KAAK,OAAO,aAC9C,KAAK,OAAO,UAAY,MAC1B,KAAK,OAAO,SAASA,CAAG,EAE1BjB,EAAUiB,EAAK,KAAK,QAAQ,EAE5Bf,EAASe,EAAK,eAAgB,KAAK,cAAc,EACjDf,EAASe,EAAK,gBAAiB,KAAK,cAAc,EAClDf,EAASe,EAAK,qBAAsB,KAAK,cAAc,EACvDf,EAASe,EAAK,iBAAkB,KAAK,cAAc,EAE5CA,EAEf,EAAO,CACD,IAAK,aACL,MAAO,SAAoBA,EAAKS,EAAQ,CACtC,IAAIC,EAAS,KAETC,EAAWX,EAAI,aAAa,mBAAmB,EAC/CY,EAAQZ,EAAI,aAAa,gBAAgB,EACzCa,EAAYb,EAAI,aAAa,oBAAoB,EAErD,OAAO,KAAK,QAAQ,UAAY,CAC9B,OAAOU,EAAO,YAAYV,EAAKS,EAAQE,EAAUC,EAAOC,CAAS,CAC3E,CAAS,EAET,EAAO,CACD,IAAK,aACL,MAAO,UAAsB,CAC3B,QAAShI,EAAI,EAAGA,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC1C,IAAImH,EAAM,KAAK,MAAMnH,CAAC,EACtBmH,EAAI,MAAM,WAAa,WAIjC,EAAO,CACD,IAAK,iBACL,MAAO,SAAwBtB,EAAO,CACpC,GAAIA,EAAM,KAAK,YAAW,EAAG,QAAQ,cAAc,GAAK,EAAG,CACzD,IAAIf,EAASe,EAAM,QAAUA,EAAM,WACnCf,EAAO,UAAYA,EAAO,UAAU,QAAQ,KAAK,OAAO,aAAc,EAAE,EAAE,KAAM,GAG1F,EAAO,CACD,IAAK,cACL,MAAO,SAAqBqC,EAAKS,EAAQE,EAAUC,EAAOC,EAAW,CACnE,OAAIJ,GACF,KAAK,mBAAmBT,CAAG,EAE7BA,EAAI,MAAM,WAAaS,EAAS,SAAW,UAEvCE,GACF,KAAK,UAAUX,EAAI,MAAO,CAAE,kBAAmBW,EAAU,EAEvDC,GACF,KAAK,UAAUZ,EAAI,MAAO,CAAE,eAAgBY,EAAO,EAEjDC,GACF,KAAK,UAAUb,EAAI,MAAO,CAAE,wBAAyBa,EAAW,EAElE,KAAK,UAAUb,EAAI,MAAO,CAAE,cAAeS,EAAS,OAAS,KAAK,oBAAoBT,CAAG,CAAC,CAAE,EAErFA,EAEf,EAAO,CACD,IAAK,YACL,MAAO,SAAmBhB,EAAM8B,EAAY,CAC1C,QAASC,KAAQD,EACf,GAAIA,EAAW,eAAeC,CAAI,EAAG,CACnC,IAAIzC,EAAQwC,EAAWC,CAAI,EAC3B/B,EAAK,GAAK+B,CAAI,EAAIzC,EAClB,QAASzF,EAAI,EAAGA,EAAI,KAAK,QAAQ,OAAQA,IAAK,CAC5C,IAAIN,EAAS,KAAK,QAAQM,CAAC,EAC3BmG,EAAK,GAAKzG,EAASwI,EAAK,OAAO,CAAC,EAAE,YAAW,EAAKA,EAAK,OAAO,CAAC,CAAC,EAAIzC,IAKlF,EAAO,CACD,IAAK,YACL,MAAO,SAAmBU,EAAMgC,EAAU,CAGxC,QAFInF,EAAQ2D,EAAiBR,CAAI,EAC7B3C,EAASR,EAAM,oBAAoBmF,CAAQ,EACtCnI,EAAI,EAAGA,EAAI,KAAK,QAAQ,OAAQA,IAAK,CAC5C,IAAIN,EAAS,KAAK,QAAQM,CAAC,EAC3BwD,EAASA,GAAUR,EAAM,oBAAoB,IAAMtD,EAAS,IAAMyI,CAAQ,EAE5E,OAAO3E,EAEf,EAAO,CACD,IAAK,gBACL,MAAO,SAAuB2D,EAAK,CACjC,IAAIiB,EAAQ,OACZ,GAAI,CACFA,EAAQ,KAAK,UAAUjB,EAAK,gBAAgB,EAAE,OAC/C,MAAe,CAEdiB,EAAQzB,EAAiBQ,CAAG,EAAE,iBAAiB,gBAAgB,EAGjE,OAAIiB,IAAU,OACL,GAGFA,EAEf,EAAO,CACD,IAAK,qBACL,MAAO,SAA4BjB,EAAK,CAGtC,OAAO,KAAK,mBAAmB,IAAIA,EAAK,KAAK,cAAcA,CAAG,CAAC,EAEvE,EAAO,CACD,IAAK,sBACL,MAAO,SAA6BA,EAAK,CACvC,OAAO,KAAK,mBAAmB,IAAIA,CAAG,EAE9C,EAAO,CACD,IAAK,gBACL,MAAO,UAAyB,CAC9B,KAAK,SAAW,GAExB,EAAO,CACD,IAAK,iBACL,MAAO,UAA0B,CAC/B,GAAI,KAAK,SAAU,CACjB,KAAK,SAAW,GAEhB,QADIkB,EAAU,CAAE,EACPrI,EAAI,EAAGA,EAAI,KAAK,MAAM,OAAQA,IAAK,CAC1C,IAAImH,EAAM,KAAK,MAAMnH,CAAC,EACtB,GAAImH,EAAK,CACP,GAAI,KAAK,UAAUA,CAAG,EAAG,CACvB,KAAK,KAAKA,CAAG,EACb,SAEFkB,EAAQ,KAAKlB,CAAG,GAGpB,KAAK,MAAQkB,EACT,CAAC,KAAK,MAAM,QAAU,CAAC,KAAK,OAAO,MACrC,KAAK,KAAM,GAIvB,EAAO,CACD,IAAK,YACL,MAAO,SAAmBX,EAAS,CAIjC,KAAOA,EAAQ,YAAc,QAC3BA,EAAUA,EAAQ,WAGpB,QADIY,EAAMZ,EAAQ,UACXA,EAAQ,cACbA,EAAUA,EAAQ,aAClBY,GAAOZ,EAAQ,UAEjB,OAAOY,EAEf,EAAO,CACD,IAAK,YACL,MAAO,SAAmBnB,EAAK,CAC7B,IAAIoB,EAASpB,EAAI,aAAa,iBAAiB,GAAK,KAAK,OAAO,OAC5DqB,EAAU,KAAK,OAAO,iBAAmB,KAAK,OAAO,gBAAgB,WAAa,OAAO,YACzFC,EAAaD,EAAU,KAAK,IAAI,KAAK,QAAQ,aAAcjC,EAAgB,CAAA,EAAIgC,EAC/ED,EAAM,KAAK,UAAUnB,CAAG,EACxBuB,EAASJ,EAAMnB,EAAI,aAEvB,OAAOmB,GAAOG,GAAcC,GAAUF,EAE9C,EAAO,CACD,IAAK,WACL,MAAO,UAAoB,CACzB,MAAO,CAAC,KAAK,OAAO,QAAU9C,EAAS,UAAU,SAAS,GAE7D,CAAC,EAEKuB,CACX,EAAK,EAEH5C,EAAQ,QAAU4C,EAClB5I,EAAO,QAAUgG,EAAQ,OAC3B,CAAC,qDCphBD;AAAA;AAAA;AAAA;AAAA,GAKA,IAAIsE,GAAa,CAEf,KAAM,SACN,MAAO,GACP,eAAgB,qBAChB,OAAQ,KACR,WAAY,gBACZ,QAAS,QACT,YAAa,UACb,UAAW,YACX,QAAS,GACT,kBAAmB,KACnB,OAAQ,KACR,gBAAiB,GAEjB,SAAU,KACV,OAAQ,KACR,UAAW,KACX,aAAc,KAEd,WAAY,iBACZ,SAAU,KACV,eAAgB,EAChB,aAAc,EACd,YAAa,EACb,UAAW,GACX,qBAAsB,GACtB,iBAAkB,GAClB,aAAc,GAEd,cAAe,KACf,kBAAmB,IACrB,EAIA,IAAIC,EACA,OAAO,OAAW,IACpBA,EAAQ,OACC,OAAO,OAAW,IAC3BA,EAAQ,OACC,OAAO,KAAS,IACzBA,EAAQ,KAERA,EAAQ,CAAE,EAEZ,IAAIC,EAAWD,EAUf,SAASE,GAAItK,EAAIuK,EAAQ,CACvB,OAAI,OAAOA,GAAW,SACbF,EAAS,iBAAiBrK,CAAE,EAAE,iBAAiBuK,CAAM,GAE9D,OAAO,KAAKA,CAAM,EAAE,QAAQpJ,GAAO,CACjCnB,EAAG,MAAMmB,CAAG,EAAIoJ,EAAOpJ,CAAG,CAC9B,CAAG,EACMnB,EACT,CAUA,SAASwK,GAASC,KAAQC,EAAM,CAC9B,OAAAD,EAAMA,GAAO,CAAE,EACf,OAAO,KAAKC,CAAI,EAAE,QAAQlJ,GAAK,CACxBkJ,EAAKlJ,CAAC,GAGX,OAAO,KAAKkJ,EAAKlJ,CAAC,CAAC,EAAE,QAAQL,GAAO,CAClCsJ,EAAItJ,CAAG,EAAIuJ,EAAKlJ,CAAC,EAAEL,CAAG,CAC5B,CAAK,CACL,CAAG,EACMsJ,CACT,CASA,SAASE,GAAWhD,EAAM,CACxB,MAAMiD,EAAU,CAAE,EAClB,KAAOjD,EAAK,gBAAkB,MAC5BA,EAAOA,EAAK,cACRA,EAAK,WAAa,GACpBiD,EAAQ,KAAKjD,CAAI,EAGrB,OAAOiD,CACT,CAMA,SAASC,GAAMnK,EAAU,CACnB,SAAS,aAAe,YAAc,SAAS,aAAe,cAEhEA,EAAU,EAEV,SAAS,iBAAiB,mBAAoBA,EAAU,CACtD,QAAS,GACT,KAAM,GACN,QAAS,EACf,CAAK,CAEL,CAEA,KAAM,CACJ,UAAWoK,EACb,EAAIT,EACEU,GAA4B,iEAAiE,KAAKD,GAAY,SAAS,EAC7H,SAAS5D,IAAW,CAClB,OAAO6D,EACT,CAEA,IAAIC,GACAC,GACAC,EAMJ,SAASC,IAAkB,CACzB,MAAI,CAACD,GAAiB,SAAS,OAC7BA,EAAgB,SAAS,cAAc,KAAK,EAC5CA,EAAc,MAAM,QAAU,mEAC9B,SAAS,KAAK,YAAYA,CAAa,IAEjCA,EAAgBA,EAAc,aAAe,IAAMb,EAAS,aAAe,SAAS,gBAAgB,YAC9G,CACA,SAASe,GAAqB,CAC5BJ,GAAOX,EAAS,YAAc,SAAS,gBAAgB,YACnDnD,GAAQ,EACV+D,GAAOE,GAAiB,EAExBF,GAAOZ,EAAS,aAAe,SAAS,gBAAgB,YAE5D,CACAe,EAAoB,EACpBf,EAAS,iBAAiB,SAAUe,CAAkB,EACtDf,EAAS,iBAAiB,oBAAqBe,CAAkB,EACjEf,EAAS,iBAAiB,OAAQe,CAAkB,EACpDP,GAAM,IAAM,CACVO,EAAoB,CACtB,CAAC,EACD,SAASC,GAAgB,CACvB,MAAO,CACL,MAAOL,GACP,OAAQC,EACT,CACH,CAIA,MAAMK,EAAe,CAAE,EACvB,SAASC,IAAiB,CACxB,GAAI,CAACD,EAAa,OAChB,OAEF,KAAM,CACJ,MAAON,EACP,OAAQC,CACT,EAAGI,EAAe,EACnBC,EAAa,QAAQ,CAACE,EAAMxC,IAAM,CAChC,KAAM,CACJ,SAAA9C,EACA,QAAAuF,CACN,EAAQD,EACJ,GAAI,CAACtF,EAAS,YACZ,OAEF,MAAMwF,EAAaxF,EAAS,MAAM,sBAAuB,EACnDyF,EAAU,CACd,MAAOD,EAAW,MAClB,OAAQA,EAAW,OACnB,IAAKA,EAAW,IAChB,OAAQA,EAAW,OACnB,KAAAV,EACA,KAAAC,CACD,EACKW,EAAY,CAACH,GAAWA,EAAQ,OAASE,EAAQ,MAAQF,EAAQ,OAASE,EAAQ,MAAQF,EAAQ,QAAUE,EAAQ,OAASF,EAAQ,SAAWE,EAAQ,OACxJE,EAAaD,GAAa,CAACH,GAAWA,EAAQ,MAAQE,EAAQ,KAAOF,EAAQ,SAAWE,EAAQ,OACtGL,EAAatC,CAAC,EAAE,QAAU2C,EACtBC,GACF1F,EAAS,SAAU,EAEjB2F,GACF3F,EAAS,SAAU,CAEzB,CAAG,EACDmE,EAAS,sBAAsBkB,EAAc,CAC/C,CACA,MAAMO,GAAkC,IAAIzB,EAAS,qBAAqB0B,GAAW,CACnFA,EAAQ,QAAQC,GAAS,CACvBA,EAAM,OAAO,SAAS,oBAAsBA,EAAM,cACtD,CAAG,CACH,EAAG,CAGD,WAAY,MACd,CAAC,EACD,SAASC,GAAY/F,EAAU,CAC7BoF,EAAa,KAAK,CAChB,SAAApF,CACJ,CAAG,EACGoF,EAAa,SAAW,GAC1BjB,EAAS,sBAAsBkB,EAAc,EAE/CO,GAAmB,QAAQ5F,EAAS,QAAQ,mBAAqBA,EAAS,KAAK,CACjF,CACA,SAASgG,GAAehG,EAAU,CAChCoF,EAAa,QAAQ,CAACE,EAAMrK,IAAQ,CAC9BqK,EAAK,SAAS,aAAetF,EAAS,YACxCoF,EAAa,OAAOnK,EAAK,CAAC,CAEhC,CAAG,EACD2K,GAAmB,UAAU5F,EAAS,QAAQ,mBAAqBA,EAAS,KAAK,CACnF,CAGA,KAAM,CACJiG,UAAAA,EACF,EAAI9B,EACJ,IAAI+B,GAAa,EAGjB,MAAMC,EAAS,CACb,YAAYpE,EAAMqE,EAAa,CAC7B,MAAMpM,EAAO,KACbA,EAAK,WAAakM,GAClBA,IAAc,EACdlM,EAAK,MAAQ+H,EACb/H,EAAK,SAAW,CACd,GAAGiK,EACJ,EAGD,MAAMoC,EAAcrM,EAAK,MAAM,SAAW,CAAE,EACtCsM,EAAkB,CAAE,EA0B1B,GAzBA,OAAO,KAAKD,CAAW,EAAE,QAAQpL,GAAO,CACtC,MAAMsL,EAAkBtL,EAAI,OAAO,EAAG,CAAC,EAAE,cAAgBA,EAAI,OAAO,CAAC,EACjEsL,GAAmB,OAAOvM,EAAK,SAASuM,CAAe,EAAM,MAC/DD,EAAgBC,CAAe,EAAIF,EAAYpL,CAAG,EAE1D,CAAK,EACDjB,EAAK,QAAUA,EAAK,OAAO,CAAE,EAAEA,EAAK,SAAUsM,EAAiBF,CAAW,EAC1EpM,EAAK,YAAcA,EAAK,OAAO,CAAE,EAAEA,EAAK,OAAO,EAG/C,OAAO,KAAKA,EAAK,OAAO,EAAE,QAAQiB,GAAO,CACnCjB,EAAK,QAAQiB,CAAG,IAAM,OACxBjB,EAAK,QAAQiB,CAAG,EAAI,GACXjB,EAAK,QAAQiB,CAAG,IAAM,UAC/BjB,EAAK,QAAQiB,CAAG,EAAI,GAE5B,CAAK,EAGDjB,EAAK,QAAQ,MAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,GAAI,WAAWA,EAAK,QAAQ,KAAK,CAAC,CAAC,EAGzE,OAAOA,EAAK,QAAQ,iBAAoB,WAC1CA,EAAK,QAAQ,gBAAkB,IAAI,OAAOA,EAAK,QAAQ,eAAe,GAEpEA,EAAK,QAAQ,2BAA2B,OAAQ,CAClD,MAAMwM,EAAwBxM,EAAK,QAAQ,gBAC3CA,EAAK,QAAQ,gBAAkB,IAAMwM,EAAsB,KAAKP,GAAU,SAAS,CACzF,CACI,GAAI,OAAOjM,EAAK,QAAQ,iBAAoB,WAAY,CAEtD,MAAMyM,EAAyBzM,EAAK,QAAQ,gBAC5CA,EAAK,QAAQ,gBAAkB,IAAMyM,IAA2B,EACtE,CAMI,GAHI,OAAOzM,EAAK,QAAQ,cAAiB,WACvCA,EAAK,QAAQ,aAAe,IAAI,OAAOA,EAAK,QAAQ,YAAY,GAE9DA,EAAK,QAAQ,wBAAwB,OAAQ,CAC/C,MAAM0M,EAAqB1M,EAAK,QAAQ,aACxCA,EAAK,QAAQ,aAAe,IAAM0M,EAAmB,KAAKT,GAAU,SAAS,CACnF,CACI,GAAI,OAAOjM,EAAK,QAAQ,cAAiB,WAAY,CAEnD,MAAM2M,EAAsB3M,EAAK,QAAQ,aACzCA,EAAK,QAAQ,aAAe,IAAM2M,IAAwB,EAChE,CAGI,IAAIC,EAAc5M,EAAK,QAAQ,kBAE3B4M,GAAe,OAAOA,GAAgB,UAAY,OAAOA,EAAY,OAAW,MAClF,CAACA,CAAW,EAAIA,GAGZA,aAAuB,UAC3BA,EAAc,MAEhB5M,EAAK,QAAQ,kBAAoB4M,EACjC5M,EAAK,MAAQ,CACX,IAAKA,EAAK,QAAQ,QAAU,KAC5B,WAAY,KACZ,UAAW,GAIX,SAAU,OACX,EACGA,EAAK,QAAO,GAAMA,EAAK,gBAAe,GACxCA,EAAK,KAAM,CAEjB,CACE,IAAIF,EAAIuK,EAAQ,CACd,OAAOD,GAAItK,EAAIuK,CAAM,CACzB,CACE,OAAOE,KAAQC,EAAM,CACnB,OAAOF,GAASC,EAAK,GAAGC,CAAI,CAChC,CAGE,eAAgB,CACd,KAAM,CACJ,MAAAqC,EACA,OAAAC,CACD,EAAG3B,EAAe,EACnB,MAAO,CACL,MAAA0B,EACA,OAAAC,EACA,EAAG,SAAS,gBAAgB,SAC7B,CACL,CAGE,SAAU,CACR,MAAM9M,EAAO,KAGb,IAAI+M,EAAc/M,EAAK,QAAQ,WAyB/B,OAxBI+M,GAAe,OAAOA,GAAgB,WACxCA,EAAc/M,EAAK,MAAM,cAAc+M,CAAW,GAI9CA,aAAuB,UACvB/M,EAAK,QAAQ,QACf+M,EAAc,IAAI,MAClBA,EAAY,IAAM/M,EAAK,QAAQ,QAE/B+M,EAAc,MAGdA,IACE/M,EAAK,QAAQ,QACfA,EAAK,MAAM,MAAQ+M,EAAY,UAAU,EAAI,GAE7C/M,EAAK,MAAM,MAAQ+M,EACnB/M,EAAK,MAAM,YAAc+M,EAAY,YAEvC/M,EAAK,MAAM,UAAY,IAIrBA,EAAK,MAAM,MACN,IAILA,EAAK,MAAM,MAAQ,OACrBA,EAAK,MAAM,IAAM,iFACjBA,EAAK,MAAM,QAAUA,EAAK,IAAIA,EAAK,MAAO,kBAAkB,GAEvD,EAAE,CAACA,EAAK,MAAM,SAAWA,EAAK,MAAM,UAAY,QAC3D,CACE,iBAAkB,CAChB,MAAO,CAAC,KAAK,QAAQ,gBAAiB,CAC1C,CACE,MAAO,CACL,MAAMA,EAAO,KACPgN,EAAkB,CACtB,SAAU,WACV,IAAK,EACL,KAAM,EACN,MAAO,OACP,OAAQ,OACR,SAAU,QACX,EACD,IAAIC,EAAc,CAChB,cAAe,OACf,eAAgB,cAChB,mBAAoB,QACrB,EACD,GAAI,CAACjN,EAAK,QAAQ,QAAS,CAEzB,MAAMkN,EAAWlN,EAAK,MAAM,aAAa,OAAO,EAIhD,GAHIkN,GACFlN,EAAK,MAAM,aAAa,gCAAiCkN,CAAQ,EAE/DlN,EAAK,MAAM,UAAW,CACxB,MAAMmN,EAAcnN,EAAK,MAAM,MAAM,aAAa,OAAO,EACrDmN,GACFnN,EAAK,MAAM,MAAM,aAAa,gCAAiCmN,CAAW,CAEpF,CACA,CAmEI,GAhEInN,EAAK,IAAIA,EAAK,MAAO,UAAU,IAAM,UACvCA,EAAK,IAAIA,EAAK,MAAO,CACnB,SAAU,UAClB,CAAO,EAECA,EAAK,IAAIA,EAAK,MAAO,SAAS,IAAM,QACtCA,EAAK,IAAIA,EAAK,MAAO,CACnB,OAAQ,CAChB,CAAO,EAIHA,EAAK,MAAM,WAAa,SAAS,cAAc,KAAK,EACpDA,EAAK,IAAIA,EAAK,MAAM,WAAYgN,CAAe,EAC/ChN,EAAK,IAAIA,EAAK,MAAM,WAAY,CAC9B,UAAWA,EAAK,QAAQ,MAC9B,CAAK,EAKG,KAAK,MAAM,WAAa,SAC1BA,EAAK,IAAIA,EAAK,MAAM,WAAY,CAC9B,oBAAqB,0CACrB,YAAa,yCACrB,CAAO,EAIHA,EAAK,MAAM,WAAW,aAAa,KAAM,sBAAsBA,EAAK,UAAU,EAAE,EAG5EA,EAAK,QAAQ,gBACfA,EAAK,MAAM,WAAW,aAAa,QAASA,EAAK,QAAQ,cAAc,EAEzEA,EAAK,MAAM,YAAYA,EAAK,MAAM,UAAU,EAGxCA,EAAK,MAAM,UACbiN,EAAcjN,EAAK,OAAO,CACxB,aAAcA,EAAK,QAAQ,QAC3B,kBAAmBA,EAAK,QAAQ,YAChC,YAAa,MACrB,EAASgN,EAAiBC,CAAW,GAI/BjN,EAAK,MAAM,MAAQ,SAAS,cAAc,KAAK,EAC3CA,EAAK,MAAM,MACbiN,EAAcjN,EAAK,OAAO,CACxB,sBAAuBA,EAAK,QAAQ,YACpC,kBAAmBA,EAAK,QAAQ,QAChC,oBAAqBA,EAAK,QAAQ,UAClC,mBAAoBA,EAAK,MAAM,SAAW,QAAQA,EAAK,MAAM,GAAG,IAC1E,EAAWgN,EAAiBC,CAAW,KAG/BjN,EAAK,QAAQ,OAAS,WAAaA,EAAK,QAAQ,OAAS,SAAWA,EAAK,QAAQ,OAAS,iBAAmBA,EAAK,QAAQ,QAAU,KACtIA,EAAK,MAAM,SAAW,YAMpBA,EAAK,MAAM,WAAa,QAAS,CACnC,MAAMoN,EAAW3C,GAAWzK,EAAK,KAAK,EAAE,OAAOF,GAAM,CACnD,MAAMuK,EAASF,EAAS,iBAAiBrK,CAAE,EACrCuN,EAAkBhD,EAAO,mBAAmB,GAAKA,EAAO,gBAAgB,GAAKA,EAAO,UAE1F,OAAOgD,GAAmBA,IAAoB,QADxB,gBACgD,KAAKhD,EAAO,SAAWA,EAAO,YAAY,EAAIA,EAAO,YAAY,CAAC,CAChJ,CAAO,EACDrK,EAAK,MAAM,SAAWoN,EAAS,OAAS,WAAa,OAC3D,CAGIH,EAAY,SAAWjN,EAAK,MAAM,SAGlCA,EAAK,IAAIA,EAAK,MAAM,MAAOiN,CAAW,EACtCjN,EAAK,MAAM,WAAW,YAAYA,EAAK,MAAM,KAAK,EAGlDA,EAAK,SAAU,EACfA,EAAK,SAAS,EAAI,EAGdA,EAAK,QAAQ,QACfA,EAAK,QAAQ,OAAO,KAAKA,CAAI,EAI3BA,EAAK,IAAIA,EAAK,MAAO,kBAAkB,IAAM,QAC/CA,EAAK,IAAIA,EAAK,MAAO,CACnB,mBAAoB,MAC5B,CAAO,EAEH+L,GAAY/L,CAAI,CACpB,CACE,SAAU,CACR,MAAMA,EAAO,KACbgM,GAAehM,CAAI,EAGnB,MAAMsN,EAAoBtN,EAAK,MAAM,aAAa,+BAA+B,EAQjF,GAPAA,EAAK,MAAM,gBAAgB,+BAA+B,EAErDsN,EAGHtN,EAAK,MAAM,aAAa,QAASsN,CAAiB,EAFlDtN,EAAK,MAAM,gBAAgB,OAAO,EAIhCA,EAAK,MAAM,UAAW,CAExB,MAAMuN,EAAuBvN,EAAK,MAAM,MAAM,aAAa,+BAA+B,EAC1FA,EAAK,MAAM,MAAM,gBAAgB,+BAA+B,EAE3DuN,EAGHvN,EAAK,MAAM,MAAM,aAAa,QAASsN,CAAiB,EAFxDtN,EAAK,MAAM,MAAM,gBAAgB,OAAO,EAMtCA,EAAK,MAAM,aACbA,EAAK,MAAM,YAAY,YAAYA,EAAK,MAAM,KAAK,CAE3D,CAGQA,EAAK,MAAM,YACbA,EAAK,MAAM,WAAW,WAAW,YAAYA,EAAK,MAAM,UAAU,EAIhEA,EAAK,QAAQ,WACfA,EAAK,QAAQ,UAAU,KAAKA,CAAI,EAIlC,OAAOA,EAAK,MAAM,QACtB,CACE,YAAa,CACX,MAAMA,EAAO,KACP,CACJ,OAAQ+K,CACT,EAAGI,EAAe,EACbqC,EAAOxN,EAAK,MAAM,WAAW,sBAAuB,EACpDyN,EAAQD,EAAK,OACb,CACJ,MAAAzJ,CACD,EAAG/D,EAAK,QACH0N,EAAW1N,EAAK,QAAQ,OAAS,UAAYA,EAAK,QAAQ,OAAS,iBACzE,IAAI2N,EAAa,EACbC,EAAUH,EACVI,EAAW,EAGf,OAAIH,IAEE3J,EAAQ,GACV4J,EAAa5J,EAAQ,KAAK,IAAI0J,EAAO1C,CAAI,EACrCA,EAAO0C,IACTE,GAAc5J,GAAS0J,EAAQ1C,KAGjC4C,EAAa5J,GAAS0J,EAAQ1C,GAI5BhH,EAAQ,EACV6J,EAAU,KAAK,IAAID,EAAa5C,CAAI,EAC3BhH,EAAQ,EACjB6J,EAAUD,EAAa5J,EAAQ,KAAK,IAAI4J,CAAU,EAElDC,IAAY7C,EAAO0C,IAAU,EAAI1J,GAEnC4J,GAAc,GAIhB3N,EAAK,uBAAyB2N,EAG1BD,EACFG,GAAY9C,EAAO6C,GAAW,EAE9BC,GAAYJ,EAAQG,GAAW,EAIjC5N,EAAK,IAAIA,EAAK,MAAM,MAAO,CACzB,OAAQ,GAAG4N,CAAO,KAClB,UAAW,GAAGC,CAAQ,KACtB,KAAM7N,EAAK,MAAM,WAAa,QAAU,GAAGwN,EAAK,IAAI,KAAO,IAC3D,MAAO,GAAGA,EAAK,KAAK,IAC1B,CAAK,EAGGxN,EAAK,QAAQ,cACfA,EAAK,QAAQ,aAAa,KAAKA,CAAI,EAI9B,CACL,MAAO,CACL,OAAQ4N,EACR,UAAWC,CACZ,EACD,UAAWL,CACZ,CACL,CACE,WAAY,CACV,OAAO,KAAK,qBAAuB,EACvC,CACE,SAASM,EAAO,CACd,MAAM9N,EAAO,KAGb,GAAI,CAAC8N,GAAS,CAAC9N,EAAK,UAAS,EAC3B,OAEF,KAAM,CACJ,OAAQ+K,CACT,EAAGI,EAAe,EACbqC,EAAOxN,EAAK,MAAM,sBAAuB,EACzC+N,EAAQP,EAAK,IACbC,EAAQD,EAAK,OACbnD,EAAS,CAAE,EAGX2D,EAAY,KAAK,IAAI,EAAGD,CAAK,EAC7BE,EAAe,KAAK,IAAI,EAAGR,EAAQM,CAAK,EACxCG,EAAW,KAAK,IAAI,EAAG,CAACH,CAAK,EAC7BI,EAAe,KAAK,IAAI,EAAGJ,EAAQN,EAAQ1C,CAAI,EAC/CqD,EAAkB,KAAK,IAAI,EAAGX,GAASM,EAAQN,EAAQ1C,EAAK,EAC5DsD,EAAc,KAAK,IAAI,EAAG,CAACN,EAAQhD,EAAO0C,CAAK,EAC/Ca,EAAqB,EAAI,IAAMvD,EAAOgD,IAAUhD,EAAO0C,IAG7D,IAAIc,EAAiB,EAgBrB,GAfId,EAAQ1C,EACVwD,EAAiB,GAAKL,GAAYC,GAAgBV,EACzCQ,GAAgBlD,EACzBwD,EAAiBN,EAAelD,EACvBqD,GAAmBrD,IAC5BwD,EAAiBH,EAAkBrD,IAIjC/K,EAAK,QAAQ,OAAS,WAAaA,EAAK,QAAQ,OAAS,iBAAmBA,EAAK,QAAQ,OAAS,oBACpGqK,EAAO,UAAY,qBACnBA,EAAO,QAAUkE,GAIfvO,EAAK,QAAQ,OAAS,SAAWA,EAAK,QAAQ,OAAS,gBAAiB,CAC1E,IAAIwO,EAAQ,EACRxO,EAAK,QAAQ,MAAQ,EACvBwO,GAASxO,EAAK,QAAQ,MAAQuO,EAE9BC,GAASxO,EAAK,QAAQ,OAAS,EAAIuO,GAErClE,EAAO,UAAY,SAASmE,CAAK,sBACvC,CAGI,GAAIxO,EAAK,QAAQ,OAAS,UAAYA,EAAK,QAAQ,OAAS,iBAAkB,CAC5E,IAAIuF,EAAYvF,EAAK,uBAAyBsO,EAG1CtO,EAAK,MAAM,WAAa,aAC1BuF,GAAawI,GAEf1D,EAAO,UAAY,iBAAiB9E,CAAS,OACnD,CACIvF,EAAK,IAAIA,EAAK,MAAM,MAAOqK,CAAM,EAG7BrK,EAAK,QAAQ,UACfA,EAAK,QAAQ,SAAS,KAAKA,EAAM,CAC/B,QAASwN,EACT,UAAAQ,EACA,aAAAC,EACA,SAAAC,EACA,aAAAC,EACA,gBAAAC,EACA,YAAAC,EACA,eAAAE,EACA,mBAAAD,CACR,CAAO,CAEP,CACE,UAAW,CACT,KAAK,WAAY,CACrB,CACA,CAGA,MAAMG,GAAa,SAAUC,EAAO3O,KAAYyK,EAAM,EAGhD,OAAO,aAAgB,SAAWkE,aAAiB,YAAcA,GAAS,OAAOA,GAAU,UAAYA,IAAU,MAAQA,EAAM,WAAa,GAAK,OAAOA,EAAM,UAAa,YAC7KA,EAAQ,CAACA,CAAK,GAEhB,MAAMC,EAAMD,EAAM,OAClB,IAAI5F,EAAI,EACJ8F,EACJ,IAAK9F,EAAGA,EAAI6F,EAAK7F,GAAK,EASpB,GARI,OAAO/I,GAAY,UAAY,OAAOA,EAAY,IAC/C2O,EAAM5F,CAAC,EAAE,WACZ4F,EAAM5F,CAAC,EAAE,SAAW,IAAIqD,GAASuC,EAAM5F,CAAC,EAAG/I,CAAO,GAE3C2O,EAAM5F,CAAC,EAAE,WAElB8F,EAAMF,EAAM5F,CAAC,EAAE,SAAS/I,CAAO,EAAE,MAAM2O,EAAM5F,CAAC,EAAE,SAAU0B,CAAI,GAE5D,OAAOoE,EAAQ,IACjB,OAAOA,EAGX,OAAOF,CACT,EACAD,GAAW,YAActC,GAi1CpB,MAAC0C,GAAWJ", "x_google_ignoreList": [0, 1, 2]}