import{o as de,q as fe}from"./vendor-misc-j6k8kvFA.js";import"./vendor-animations-Dl3DQHMd.js";/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const v=new Map,pt={set(s,t,e){v.has(s)||v.set(s,new Map);const n=v.get(s);if(!n.has(t)&&n.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`);return}n.set(t,e)},get(s,t){return v.has(s)&&v.get(s).get(t)||null},remove(s,t){if(!v.has(s))return;const e=v.get(s);e.delete(t),e.size===0&&v.delete(s)}},Ue=1e6,je=1e3,Lt="transitionend",pe=s=>(s&&window.CSS&&window.CSS.escape&&(s=s.replace(/#([^\s"#']+)/g,(t,e)=>`#${CSS.escape(e)}`)),s),Ge=s=>s==null?`${s}`:Object.prototype.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase(),ze=s=>{do s+=Math.floor(Math.random()*Ue);while(document.getElementById(s));return s},qe=s=>{if(!s)return 0;let{transitionDuration:t,transitionDelay:e}=window.getComputedStyle(s);const n=Number.parseFloat(t),i=Number.parseFloat(e);return!n&&!i?0:(t=t.split(",")[0],e=e.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(e))*je)},Ee=s=>{s.dispatchEvent(new Event(Lt))},b=s=>!s||typeof s!="object"?!1:(typeof s.jquery<"u"&&(s=s[0]),typeof s.nodeType<"u"),S=s=>b(s)?s.jquery?s[0]:s:typeof s=="string"&&s.length>0?document.querySelector(pe(s)):null,F=s=>{if(!b(s)||s.getClientRects().length===0)return!1;const t=getComputedStyle(s).getPropertyValue("visibility")==="visible",e=s.closest("details:not([open])");if(!e)return t;if(e!==s){const n=s.closest("summary");if(n&&n.parentNode!==e||n===null)return!1}return t},y=s=>!s||s.nodeType!==Node.ELEMENT_NODE||s.classList.contains("disabled")?!0:typeof s.disabled<"u"?s.disabled:s.hasAttribute("disabled")&&s.getAttribute("disabled")!=="false",me=s=>{if(!document.documentElement.attachShadow)return null;if(typeof s.getRootNode=="function"){const t=s.getRootNode();return t instanceof ShadowRoot?t:null}return s instanceof ShadowRoot?s:s.parentNode?me(s.parentNode):null},at=()=>{},z=s=>{s.offsetHeight},ge=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Et=[],Qe=s=>{document.readyState==="loading"?(Et.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of Et)t()}),Et.push(s)):s()},f=()=>document.documentElement.dir==="rtl",E=s=>{Qe(()=>{const t=ge();if(t){const e=s.NAME,n=t.fn[e];t.fn[e]=s.jQueryInterface,t.fn[e].Constructor=s,t.fn[e].noConflict=()=>(t.fn[e]=n,s.jQueryInterface)}})},_=(s,t=[],e=s)=>typeof s=="function"?s(...t):e,Ae=(s,t,e=!0)=>{if(!e){_(s);return}const i=qe(t)+5;let a=!1;const r=({target:c})=>{c===t&&(a=!0,t.removeEventListener(Lt,r),_(s))};t.addEventListener(Lt,r),setTimeout(()=>{a||Ee(t)},i)},Rt=(s,t,e,n)=>{const i=s.length;let a=s.indexOf(t);return a===-1?!e&&n?s[i-1]:s[0]:(a+=e?1:-1,n&&(a=(a+i)%i),s[Math.max(0,Math.min(a,i-1))])},Xe=/[^.]*(?=\..*)\.|.*/,Ze=/\..*/,Je=/::\d+$/,mt={};let xt=1;const Te={mouseenter:"mouseover",mouseleave:"mouseout"},ts=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function be(s,t){return t&&`${t}::${xt++}`||s.uidEvent||xt++}function Ne(s){const t=be(s);return s.uidEvent=t,mt[t]=mt[t]||{},mt[t]}function es(s,t){return function e(n){return Pt(n,{delegateTarget:s}),e.oneOff&&o.off(s,n.type,t),t.apply(s,[n])}}function ss(s,t,e){return function n(i){const a=s.querySelectorAll(t);for(let{target:r}=i;r&&r!==this;r=r.parentNode)for(const c of a)if(c===r)return Pt(i,{delegateTarget:r}),n.oneOff&&o.off(s,i.type,t,e),e.apply(r,[i])}}function Ce(s,t,e=null){return Object.values(s).find(n=>n.callable===t&&n.delegationSelector===e)}function ve(s,t,e){const n=typeof t=="string",i=n?e:t||e;let a=Se(s);return ts.has(a)||(a=s),[n,i,a]}function Kt(s,t,e,n,i){if(typeof t!="string"||!s)return;let[a,r,c]=ve(t,e,n);t in Te&&(r=(Be=>function(R){if(!R.relatedTarget||R.relatedTarget!==R.delegateTarget&&!R.delegateTarget.contains(R.relatedTarget))return Be.call(this,R)})(r));const u=Ne(s),d=u[c]||(u[c]={}),h=Ce(d,r,a?e:null);if(h){h.oneOff=h.oneOff&&i;return}const A=be(r,t.replace(Xe,"")),m=a?ss(s,e,r):es(s,r);m.delegationSelector=a?e:null,m.callable=r,m.oneOff=i,m.uidEvent=A,d[A]=m,s.addEventListener(c,m,a)}function $t(s,t,e,n,i){const a=Ce(t[e],n,i);a&&(s.removeEventListener(e,a,!!i),delete t[e][a.uidEvent])}function ns(s,t,e,n){const i=t[e]||{};for(const[a,r]of Object.entries(i))a.includes(n)&&$t(s,t,e,r.callable,r.delegationSelector)}function Se(s){return s=s.replace(Ze,""),Te[s]||s}const o={on(s,t,e,n){Kt(s,t,e,n,!1)},one(s,t,e,n){Kt(s,t,e,n,!0)},off(s,t,e,n){if(typeof t!="string"||!s)return;const[i,a,r]=ve(t,e,n),c=r!==t,u=Ne(s),d=u[r]||{},h=t.startsWith(".");if(typeof a<"u"){if(!Object.keys(d).length)return;$t(s,u,r,a,i?e:null);return}if(h)for(const A of Object.keys(u))ns(s,u,A,t.slice(1));for(const[A,m]of Object.entries(d)){const Z=A.replace(Je,"");(!c||t.includes(Z))&&$t(s,u,r,m.callable,m.delegationSelector)}},trigger(s,t,e){if(typeof t!="string"||!s)return null;const n=ge(),i=Se(t),a=t!==i;let r=null,c=!0,u=!0,d=!1;a&&n&&(r=n.Event(t,e),n(s).trigger(r),c=!r.isPropagationStopped(),u=!r.isImmediatePropagationStopped(),d=r.isDefaultPrevented());const h=Pt(new Event(t,{bubbles:c,cancelable:!0}),e);return d&&h.preventDefault(),u&&s.dispatchEvent(h),h.defaultPrevented&&r&&r.preventDefault(),h}};function Pt(s,t={}){for(const[e,n]of Object.entries(t))try{s[e]=n}catch{Object.defineProperty(s,e,{configurable:!0,get(){return n}})}return s}function Wt(s){if(s==="true")return!0;if(s==="false")return!1;if(s===Number(s).toString())return Number(s);if(s===""||s==="null")return null;if(typeof s!="string")return s;try{return JSON.parse(decodeURIComponent(s))}catch{return s}}function gt(s){return s.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const N={setDataAttribute(s,t,e){s.setAttribute(`data-bs-${gt(t)}`,e)},removeDataAttribute(s,t){s.removeAttribute(`data-bs-${gt(t)}`)},getDataAttributes(s){if(!s)return{};const t={},e=Object.keys(s.dataset).filter(n=>n.startsWith("bs")&&!n.startsWith("bsConfig"));for(const n of e){let i=n.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),t[i]=Wt(s.dataset[n])}return t},getDataAttribute(s,t){return Wt(s.getAttribute(`data-bs-${gt(t)}`))}};class q{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const n=b(e)?N.getDataAttribute(e,"config"):{};return{...this.constructor.Default,...typeof n=="object"?n:{},...b(e)?N.getDataAttributes(e):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[n,i]of Object.entries(e)){const a=t[n],r=b(a)?"element":Ge(a);if(!new RegExp(i).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${r}" but expected type "${i}".`)}}}const is="5.3.3";class g extends q{constructor(t,e){super(),t=S(t),t&&(this._element=t,this._config=this._getConfig(e),pt.set(this._element,this.constructor.DATA_KEY,this))}dispose(){pt.remove(this._element,this.constructor.DATA_KEY),o.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,n=!0){Ae(t,e,n)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return pt.get(S(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,typeof e=="object"?e:null)}static get VERSION(){return is}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const At=s=>{let t=s.getAttribute("data-bs-target");if(!t||t==="#"){let e=s.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e=`#${e.split("#")[1]}`),t=e&&e!=="#"?e.trim():null}return t?t.split(",").map(e=>pe(e)).join(","):null},l={find(s,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,s))},findOne(s,t=document.documentElement){return Element.prototype.querySelector.call(t,s)},children(s,t){return[].concat(...s.children).filter(e=>e.matches(t))},parents(s,t){const e=[];let n=s.parentNode.closest(t);for(;n;)e.push(n),n=n.parentNode.closest(t);return e},prev(s,t){let e=s.previousElementSibling;for(;e;){if(e.matches(t))return[e];e=e.previousElementSibling}return[]},next(s,t){let e=s.nextElementSibling;for(;e;){if(e.matches(t))return[e];e=e.nextElementSibling}return[]},focusableChildren(s){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,s).filter(e=>!y(e)&&F(e))},getSelectorFromElement(s){const t=At(s);return t&&l.findOne(t)?t:null},getElementFromSelector(s){const t=At(s);return t?l.findOne(t):null},getMultipleElementsFromSelector(s){const t=At(s);return t?l.find(t):[]}},ht=(s,t="hide")=>{const e=`click.dismiss${s.EVENT_KEY}`,n=s.NAME;o.on(document,e,`[data-bs-dismiss="${n}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),y(this))return;const a=l.getElementFromSelector(this)||this.closest(`.${n}`);s.getOrCreateInstance(a)[t]()})},os="alert",rs="bs.alert",ye=`.${rs}`,as=`close${ye}`,ls=`closed${ye}`,cs="fade",hs="show";class ut extends g{static get NAME(){return os}close(){if(o.trigger(this._element,as).defaultPrevented)return;this._element.classList.remove(hs);const e=this._element.classList.contains(cs);this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),o.trigger(this._element,ls),this.dispose()}static jQueryInterface(t){return this.each(function(){const e=ut.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}ht(ut,"close");E(ut);const us="button",_s="bs.button",ds=`.${_s}`,fs=".data-api",ps="active",Ft='[data-bs-toggle="button"]',Es=`click${ds}${fs}`;class _t extends g{static get NAME(){return us}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(ps))}static jQueryInterface(t){return this.each(function(){const e=_t.getOrCreateInstance(this);t==="toggle"&&e[t]()})}}o.on(document,Es,Ft,s=>{s.preventDefault();const t=s.target.closest(Ft);_t.getOrCreateInstance(t).toggle()});E(_t);const ms="swipe",Y=".bs.swipe",gs=`touchstart${Y}`,As=`touchmove${Y}`,Ts=`touchend${Y}`,bs=`pointerdown${Y}`,Ns=`pointerup${Y}`,Cs="touch",vs="pen",Ss="pointer-event",ys=40,Os={endCallback:null,leftCallback:null,rightCallback:null},Ds={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class lt extends q{constructor(t,e){super(),this._element=t,!(!t||!lt.isSupported())&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return Os}static get DefaultType(){return Ds}static get NAME(){return ms}dispose(){o.off(this._element,Y)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),_(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=ys)return;const e=t/this._deltaX;this._deltaX=0,e&&_(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(o.on(this._element,bs,t=>this._start(t)),o.on(this._element,Ns,t=>this._end(t)),this._element.classList.add(Ss)):(o.on(this._element,gs,t=>this._start(t)),o.on(this._element,As,t=>this._move(t)),o.on(this._element,Ts,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===vs||t.pointerType===Cs)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ws="carousel",Ls="bs.carousel",D=`.${Ls}`,Oe=".data-api",$s="ArrowLeft",Is="ArrowRight",Ms=500,U="next",P="prev",V="left",ot="right",Rs=`slide${D}`,Tt=`slid${D}`,Ps=`keydown${D}`,ks=`mouseenter${D}`,Vs=`mouseleave${D}`,Hs=`dragstart${D}`,xs=`load${D}${Oe}`,Ks=`click${D}${Oe}`,De="carousel",J="active",Ws="slide",Fs="carousel-item-end",Ys="carousel-item-start",Bs="carousel-item-next",Us="carousel-item-prev",we=".active",Le=".carousel-item",js=we+Le,Gs=".carousel-item img",zs=".carousel-indicators",qs="[data-bs-slide], [data-bs-slide-to]",Qs='[data-bs-ride="carousel"]',Xs={[$s]:ot,[Is]:V},Zs={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Js={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Q extends g{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=l.findOne(zs,this._element),this._addEventListeners(),this._config.ride===De&&this.cycle()}static get Default(){return Zs}static get DefaultType(){return Js}static get NAME(){return ws}next(){this._slide(U)}nextWhenVisible(){!document.hidden&&F(this._element)&&this.next()}prev(){this._slide(P)}pause(){this._isSliding&&Ee(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){o.one(this._element,Tt,()=>this.cycle());return}this.cycle()}}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding){o.one(this._element,Tt,()=>this.to(t));return}const n=this._getItemIndex(this._getActive());if(n===t)return;const i=t>n?U:P;this._slide(i,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&o.on(this._element,Ps,t=>this._keydown(t)),this._config.pause==="hover"&&(o.on(this._element,ks,()=>this.pause()),o.on(this._element,Vs,()=>this._maybeEnableCycle())),this._config.touch&&lt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const n of l.find(Gs,this._element))o.on(n,Hs,i=>i.preventDefault());const e={leftCallback:()=>this._slide(this._directionToOrder(V)),rightCallback:()=>this._slide(this._directionToOrder(ot)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),Ms+this._config.interval))}};this._swipeHelper=new lt(this._element,e)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Xs[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=l.findOne(we,this._indicatorsElement);e.classList.remove(J),e.removeAttribute("aria-current");const n=l.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);n&&(n.classList.add(J),n.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const n=this._getActive(),i=t===U,a=e||Rt(this._getItems(),n,i,this._config.wrap);if(a===n)return;const r=this._getItemIndex(a),c=Z=>o.trigger(this._element,Z,{relatedTarget:a,direction:this._orderToDirection(t),from:this._getItemIndex(n),to:r});if(c(Rs).defaultPrevented||!n||!a)return;const d=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=a;const h=i?Ys:Fs,A=i?Bs:Us;a.classList.add(A),z(a),n.classList.add(h),a.classList.add(h);const m=()=>{a.classList.remove(h,A),a.classList.add(J),n.classList.remove(J,A,h),this._isSliding=!1,c(Tt)};this._queueCallback(m,n,this._isAnimated()),d&&this.cycle()}_isAnimated(){return this._element.classList.contains(Ws)}_getActive(){return l.findOne(js,this._element)}_getItems(){return l.find(Le,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return f()?t===V?P:U:t===V?U:P}_orderToDirection(t){return f()?t===P?V:ot:t===P?ot:V}static jQueryInterface(t){return this.each(function(){const e=Q.getOrCreateInstance(this,t);if(typeof t=="number"){e.to(t);return}if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}o.on(document,Ks,qs,function(s){const t=l.getElementFromSelector(this);if(!t||!t.classList.contains(De))return;s.preventDefault();const e=Q.getOrCreateInstance(t),n=this.getAttribute("data-bs-slide-to");if(n){e.to(n),e._maybeEnableCycle();return}if(N.getDataAttribute(this,"slide")==="next"){e.next(),e._maybeEnableCycle();return}e.prev(),e._maybeEnableCycle()});o.on(window,xs,()=>{const s=l.find(Qs);for(const t of s)Q.getOrCreateInstance(t)});E(Q);const tn="collapse",en="bs.collapse",X=`.${en}`,sn=".data-api",nn=`show${X}`,on=`shown${X}`,rn=`hide${X}`,an=`hidden${X}`,ln=`click${X}${sn}`,bt="show",x="collapse",tt="collapsing",cn="collapsed",hn=`:scope .${x} .${x}`,un="collapse-horizontal",_n="width",dn="height",fn=".collapse.show, .collapse.collapsing",It='[data-bs-toggle="collapse"]',pn={parent:null,toggle:!0},En={parent:"(null|element)",toggle:"boolean"};class G extends g{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const n=l.find(It);for(const i of n){const a=l.getSelectorFromElement(i),r=l.find(a).filter(c=>c===this._element);a!==null&&r.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return pn}static get DefaultType(){return En}static get NAME(){return tn}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(fn).filter(c=>c!==this._element).map(c=>G.getOrCreateInstance(c,{toggle:!1}))),t.length&&t[0]._isTransitioning||o.trigger(this._element,nn).defaultPrevented)return;for(const c of t)c.hide();const n=this._getDimension();this._element.classList.remove(x),this._element.classList.add(tt),this._element.style[n]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(tt),this._element.classList.add(x,bt),this._element.style[n]="",o.trigger(this._element,on)},r=`scroll${n[0].toUpperCase()+n.slice(1)}`;this._queueCallback(i,this._element,!0),this._element.style[n]=`${this._element[r]}px`}hide(){if(this._isTransitioning||!this._isShown()||o.trigger(this._element,rn).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,z(this._element),this._element.classList.add(tt),this._element.classList.remove(x,bt);for(const i of this._triggerArray){const a=l.getElementFromSelector(i);a&&!this._isShown(a)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;const n=()=>{this._isTransitioning=!1,this._element.classList.remove(tt),this._element.classList.add(x),o.trigger(this._element,an)};this._element.style[e]="",this._queueCallback(n,this._element,!0)}_isShown(t=this._element){return t.classList.contains(bt)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=S(t.parent),t}_getDimension(){return this._element.classList.contains(un)?_n:dn}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(It);for(const e of t){const n=l.getElementFromSelector(e);n&&this._addAriaAndCollapsedClass([e],this._isShown(n))}}_getFirstLevelChildren(t){const e=l.find(hn,this._config.parent);return l.find(t,this._config.parent).filter(n=>!e.includes(n))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const n of t)n.classList.toggle(cn,!e),n.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return typeof t=="string"&&/show|hide/.test(t)&&(e.toggle=!1),this.each(function(){const n=G.getOrCreateInstance(this,e);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}o.on(document,ln,It,function(s){(s.target.tagName==="A"||s.delegateTarget&&s.delegateTarget.tagName==="A")&&s.preventDefault();for(const t of l.getMultipleElementsFromSelector(this))G.getOrCreateInstance(t,{toggle:!1}).toggle()});E(G);const Yt="dropdown",mn="bs.dropdown",I=`.${mn}`,kt=".data-api",gn="Escape",Bt="Tab",An="ArrowUp",Ut="ArrowDown",Tn=2,bn=`hide${I}`,Nn=`hidden${I}`,Cn=`show${I}`,vn=`shown${I}`,$e=`click${I}${kt}`,Ie=`keydown${I}${kt}`,Sn=`keyup${I}${kt}`,H="show",yn="dropup",On="dropend",Dn="dropstart",wn="dropup-center",Ln="dropdown-center",L='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',$n=`${L}.${H}`,rt=".dropdown-menu",In=".navbar",Mn=".navbar-nav",Rn=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Pn=f()?"top-end":"top-start",kn=f()?"top-start":"top-end",Vn=f()?"bottom-end":"bottom-start",Hn=f()?"bottom-start":"bottom-end",xn=f()?"left-start":"right-start",Kn=f()?"right-start":"left-start",Wn="top",Fn="bottom",Yn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Bn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class T extends g{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=l.next(this._element,rt)[0]||l.prev(this._element,rt)[0]||l.findOne(rt,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Yn}static get DefaultType(){return Bn}static get NAME(){return Yt}toggle(){return this._isShown()?this.hide():this.show()}show(){if(y(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!o.trigger(this._element,Cn,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Mn))for(const n of[].concat(...document.body.children))o.on(n,"mouseover",at);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(H),this._element.classList.add(H),o.trigger(this._element,vn,t)}}hide(){if(y(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!o.trigger(this._element,bn,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const n of[].concat(...document.body.children))o.off(n,"mouseover",at);this._popper&&this._popper.destroy(),this._menu.classList.remove(H),this._element.classList.remove(H),this._element.setAttribute("aria-expanded","false"),N.removeDataAttribute(this._menu,"popper"),o.trigger(this._element,Nn,t)}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!b(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${Yt.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof de>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:b(this._config.reference)?t=S(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=fe(t,this._menu,e)}_isShown(){return this._menu.classList.contains(H)}_getPlacement(){const t=this._parent;if(t.classList.contains(On))return xn;if(t.classList.contains(Dn))return Kn;if(t.classList.contains(wn))return Wn;if(t.classList.contains(Ln))return Fn;const e=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(yn)?e?kn:Pn:e?Hn:Vn}_detectNavbar(){return this._element.closest(In)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(N.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,..._(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const n=l.find(Rn,this._menu).filter(i=>F(i));n.length&&Rt(n,e,t===Ut,!n.includes(e)).focus()}static jQueryInterface(t){return this.each(function(){const e=T.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}static clearMenus(t){if(t.button===Tn||t.type==="keyup"&&t.key!==Bt)return;const e=l.find($n);for(const n of e){const i=T.getInstance(n);if(!i||i._config.autoClose===!1)continue;const a=t.composedPath(),r=a.includes(i._menu);if(a.includes(i._element)||i._config.autoClose==="inside"&&!r||i._config.autoClose==="outside"&&r||i._menu.contains(t.target)&&(t.type==="keyup"&&t.key===Bt||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const c={relatedTarget:i._element};t.type==="click"&&(c.clickEvent=t),i._completeHide(c)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),n=t.key===gn,i=[An,Ut].includes(t.key);if(!i&&!n||e&&!n)return;t.preventDefault();const a=this.matches(L)?this:l.prev(this,L)[0]||l.next(this,L)[0]||l.findOne(L,t.delegateTarget.parentNode),r=T.getOrCreateInstance(a);if(i){t.stopPropagation(),r.show(),r._selectMenuItem(t);return}r._isShown()&&(t.stopPropagation(),r.hide(),a.focus())}}o.on(document,Ie,L,T.dataApiKeydownHandler);o.on(document,Ie,rt,T.dataApiKeydownHandler);o.on(document,$e,T.clearMenus);o.on(document,Sn,T.clearMenus);o.on(document,$e,L,function(s){s.preventDefault(),T.getOrCreateInstance(this).toggle()});E(T);const Me="backdrop",Un="fade",jt="show",Gt=`mousedown.bs.${Me}`,jn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Gn={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Re extends q{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return jn}static get DefaultType(){return Gn}static get NAME(){return Me}show(t){if(!this._config.isVisible){_(t);return}this._append();const e=this._getElement();this._config.isAnimated&&z(e),e.classList.add(jt),this._emulateAnimation(()=>{_(t)})}hide(t){if(!this._config.isVisible){_(t);return}this._getElement().classList.remove(jt),this._emulateAnimation(()=>{this.dispose(),_(t)})}dispose(){this._isAppended&&(o.off(this._element,Gt),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(Un),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=S(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),o.on(t,Gt,()=>{_(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){Ae(t,this._getElement(),this._config.isAnimated)}}const zn="focustrap",qn="bs.focustrap",ct=`.${qn}`,Qn=`focusin${ct}`,Xn=`keydown.tab${ct}`,Zn="Tab",Jn="forward",zt="backward",ti={autofocus:!0,trapElement:null},ei={autofocus:"boolean",trapElement:"element"};class Pe extends q{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ti}static get DefaultType(){return ei}static get NAME(){return zn}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),o.off(document,ct),o.on(document,Qn,t=>this._handleFocusin(t)),o.on(document,Xn,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,o.off(document,ct))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const n=l.focusableChildren(e);n.length===0?e.focus():this._lastTabNavDirection===zt?n[n.length-1].focus():n[0].focus()}_handleKeydown(t){t.key===Zn&&(this._lastTabNavDirection=t.shiftKey?zt:Jn)}}const qt=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Qt=".sticky-top",et="padding-right",Xt="margin-right";class Mt{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,et,e=>e+t),this._setElementAttributes(qt,et,e=>e+t),this._setElementAttributes(Qt,Xt,e=>e-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,et),this._resetElementAttributes(qt,et),this._resetElementAttributes(Qt,Xt)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,n){const i=this.getWidth(),a=r=>{if(r!==this._element&&window.innerWidth>r.clientWidth+i)return;this._saveInitialAttribute(r,e);const c=window.getComputedStyle(r).getPropertyValue(e);r.style.setProperty(e,`${n(Number.parseFloat(c))}px`)};this._applyManipulationCallback(t,a)}_saveInitialAttribute(t,e){const n=t.style.getPropertyValue(e);n&&N.setDataAttribute(t,e,n)}_resetElementAttributes(t,e){const n=i=>{const a=N.getDataAttribute(i,e);if(a===null){i.style.removeProperty(e);return}N.removeDataAttribute(i,e),i.style.setProperty(e,a)};this._applyManipulationCallback(t,n)}_applyManipulationCallback(t,e){if(b(t)){e(t);return}for(const n of l.find(t,this._element))e(n)}}const si="modal",ni="bs.modal",p=`.${ni}`,ii=".data-api",oi="Escape",ri=`hide${p}`,ai=`hidePrevented${p}`,ke=`hidden${p}`,Ve=`show${p}`,li=`shown${p}`,ci=`resize${p}`,hi=`click.dismiss${p}`,ui=`mousedown.dismiss${p}`,_i=`keydown.dismiss${p}`,di=`click${p}${ii}`,Zt="modal-open",fi="fade",Jt="show",Nt="modal-static",pi=".modal.show",Ei=".modal-dialog",mi=".modal-body",gi='[data-bs-toggle="modal"]',Ai={backdrop:!0,focus:!0,keyboard:!0},Ti={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class K extends g{constructor(t,e){super(t,e),this._dialog=l.findOne(Ei,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Mt,this._addEventListeners()}static get Default(){return Ai}static get DefaultType(){return Ti}static get NAME(){return si}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||o.trigger(this._element,Ve,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Zt),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||o.trigger(this._element,ri).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Jt),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){o.off(window,p),o.off(this._dialog,p),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Re({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Pe({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=l.findOne(mi,this._dialog);e&&(e.scrollTop=0),z(this._element),this._element.classList.add(Jt);const n=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,o.trigger(this._element,li,{relatedTarget:t})};this._queueCallback(n,this._dialog,this._isAnimated())}_addEventListeners(){o.on(this._element,_i,t=>{if(t.key===oi){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),o.on(window,ci,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),o.on(this._element,ui,t=>{o.one(this._element,hi,e=>{if(!(this._element!==t.target||this._element!==e.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Zt),this._resetAdjustments(),this._scrollBar.reset(),o.trigger(this._element,ke)})}_isAnimated(){return this._element.classList.contains(fi)}_triggerBackdropTransition(){if(o.trigger(this._element,ai).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,n=this._element.style.overflowY;n==="hidden"||this._element.classList.contains(Nt)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Nt),this._queueCallback(()=>{this._element.classList.remove(Nt),this._queueCallback(()=>{this._element.style.overflowY=n},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),n=e>0;if(n&&!t){const i=f()?"paddingLeft":"paddingRight";this._element.style[i]=`${e}px`}if(!n&&t){const i=f()?"paddingRight":"paddingLeft";this._element.style[i]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each(function(){const n=K.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t](e)}})}}o.on(document,di,gi,function(s){const t=l.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&s.preventDefault(),o.one(t,Ve,i=>{i.defaultPrevented||o.one(t,ke,()=>{F(this)&&this.focus()})});const e=l.findOne(pi);e&&K.getInstance(e).hide(),K.getOrCreateInstance(t).toggle(this)});ht(K);E(K);const bi="offcanvas",Ni="bs.offcanvas",C=`.${Ni}`,He=".data-api",Ci=`load${C}${He}`,vi="Escape",te="show",ee="showing",se="hiding",Si="offcanvas-backdrop",xe=".offcanvas.show",yi=`show${C}`,Oi=`shown${C}`,Di=`hide${C}`,ne=`hidePrevented${C}`,Ke=`hidden${C}`,wi=`resize${C}`,Li=`click${C}${He}`,$i=`keydown.dismiss${C}`,Ii='[data-bs-toggle="offcanvas"]',Mi={backdrop:!0,keyboard:!0,scroll:!1},Ri={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class O extends g{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Mi}static get DefaultType(){return Ri}static get NAME(){return bi}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||o.trigger(this._element,yi,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Mt().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ee);const n=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(te),this._element.classList.remove(ee),o.trigger(this._element,Oi,{relatedTarget:t})};this._queueCallback(n,this._element,!0)}hide(){if(!this._isShown||o.trigger(this._element,Di).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(se),this._backdrop.hide();const e=()=>{this._element.classList.remove(te,se),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Mt().reset(),o.trigger(this._element,Ke)};this._queueCallback(e,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){o.trigger(this._element,ne);return}this.hide()},e=!!this._config.backdrop;return new Re({className:Si,isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?t:null})}_initializeFocusTrap(){return new Pe({trapElement:this._element})}_addEventListeners(){o.on(this._element,$i,t=>{if(t.key===vi){if(this._config.keyboard){this.hide();return}o.trigger(this._element,ne)}})}static jQueryInterface(t){return this.each(function(){const e=O.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}o.on(document,Li,Ii,function(s){const t=l.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),y(this))return;o.one(t,Ke,()=>{F(this)&&this.focus()});const e=l.findOne(xe);e&&e!==t&&O.getInstance(e).hide(),O.getOrCreateInstance(t).toggle(this)});o.on(window,Ci,()=>{for(const s of l.find(xe))O.getOrCreateInstance(s).show()});o.on(window,wi,()=>{for(const s of l.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(s).position!=="fixed"&&O.getOrCreateInstance(s).hide()});ht(O);E(O);const Pi=/^aria-[\w-]*$/i,We={"*":["class","dir","id","lang","role",Pi],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},ki=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Vi=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Hi=(s,t)=>{const e=s.nodeName.toLowerCase();return t.includes(e)?ki.has(e)?!!Vi.test(s.nodeValue):!0:t.filter(n=>n instanceof RegExp).some(n=>n.test(e))};function xi(s,t,e){if(!s.length)return s;if(e&&typeof e=="function")return e(s);const i=new window.DOMParser().parseFromString(s,"text/html"),a=[].concat(...i.body.querySelectorAll("*"));for(const r of a){const c=r.nodeName.toLowerCase();if(!Object.keys(t).includes(c)){r.remove();continue}const u=[].concat(...r.attributes),d=[].concat(t["*"]||[],t[c]||[]);for(const h of u)Hi(h,d)||r.removeAttribute(h.nodeName)}return i.body.innerHTML}const Ki="TemplateFactory",Wi={allowList:We,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Fi={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Yi={entry:"(string|element|function|null)",selector:"(string|element)"};class Bi extends q{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Wi}static get DefaultType(){return Fi}static get NAME(){return Ki}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[i,a]of Object.entries(this._config.content))this._setContent(t,a,i);const e=t.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&e.classList.add(...n.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,n]of Object.entries(t))super._typeCheckConfig({selector:e,entry:n},Yi)}_setContent(t,e,n){const i=l.findOne(n,t);if(i){if(e=this._resolvePossibleFunction(e),!e){i.remove();return}if(b(e)){this._putElementInTemplate(S(e),i);return}if(this._config.html){i.innerHTML=this._maybeSanitize(e);return}i.textContent=e}}_maybeSanitize(t){return this._config.sanitize?xi(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return _(t,[this])}_putElementInTemplate(t,e){if(this._config.html){e.innerHTML="",e.append(t);return}e.textContent=t.textContent}}const Ui="tooltip",ji=new Set(["sanitize","allowList","sanitizeFn"]),Ct="fade",Gi="modal",st="show",zi=".tooltip-inner",ie=`.${Gi}`,oe="hide.bs.modal",j="hover",vt="focus",qi="click",Qi="manual",Xi="hide",Zi="hidden",Ji="show",to="shown",eo="inserted",so="click",no="focusin",io="focusout",oo="mouseenter",ro="mouseleave",ao={AUTO:"auto",TOP:"top",RIGHT:f()?"left":"right",BOTTOM:"bottom",LEFT:f()?"right":"left"},lo={allowList:We,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},co={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class B extends g{constructor(t,e){if(typeof de>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return lo}static get DefaultType(){return co}static get NAME(){return Ui}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),o.off(this._element.closest(ie),oe,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=o.trigger(this._element,this.constructor.eventName(Ji)),n=(me(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!n)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:a}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(a.append(i),o.trigger(this._element,this.constructor.eventName(eo))),this._popper=this._createPopper(i),i.classList.add(st),"ontouchstart"in document.documentElement)for(const c of[].concat(...document.body.children))o.on(c,"mouseover",at);const r=()=>{o.trigger(this._element,this.constructor.eventName(to)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(r,this.tip,this._isAnimated())}hide(){if(!this._isShown()||o.trigger(this._element,this.constructor.eventName(Xi)).defaultPrevented)return;if(this._getTipElement().classList.remove(st),"ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))o.off(i,"mouseover",at);this._activeTrigger[qi]=!1,this._activeTrigger[vt]=!1,this._activeTrigger[j]=!1,this._isHovered=null;const n=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),o.trigger(this._element,this.constructor.eventName(Zi)))};this._queueCallback(n,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Ct,st),e.classList.add(`bs-${this.constructor.NAME}-auto`);const n=ze(this.constructor.NAME).toString();return e.setAttribute("id",n),this._isAnimated()&&e.classList.add(Ct),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Bi({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[zi]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Ct)}_isShown(){return this.tip&&this.tip.classList.contains(st)}_createPopper(t){const e=_(this._config.placement,[this,t,this._element]),n=ao[e.toUpperCase()];return fe(this._element,t,this._getPopperConfig(n))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(e=>Number.parseInt(e,10)):typeof t=="function"?e=>t(e,this._element):t}_resolvePossibleFunction(t){return _(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:n=>{this._getTipElement().setAttribute("data-popper-placement",n.state.placement)}}]};return{...e,..._(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if(e==="click")o.on(this._element,this.constructor.eventName(so),this._config.selector,n=>{this._initializeOnDelegatedTarget(n).toggle()});else if(e!==Qi){const n=e===j?this.constructor.eventName(oo):this.constructor.eventName(no),i=e===j?this.constructor.eventName(ro):this.constructor.eventName(io);o.on(this._element,n,this._config.selector,a=>{const r=this._initializeOnDelegatedTarget(a);r._activeTrigger[a.type==="focusin"?vt:j]=!0,r._enter()}),o.on(this._element,i,this._config.selector,a=>{const r=this._initializeOnDelegatedTarget(a);r._activeTrigger[a.type==="focusout"?vt:j]=r._element.contains(a.relatedTarget),r._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},o.on(this._element.closest(ie),oe,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=N.getDataAttributes(this._element);for(const n of Object.keys(e))ji.has(n)&&delete e[n];return t={...e,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:S(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,n]of Object.entries(this._config))this.constructor.Default[e]!==n&&(t[e]=n);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const e=B.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}}E(B);const ho="popover",uo=".popover-header",_o=".popover-body",fo={...B.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},po={...B.DefaultType,content:"(null|string|element|function)"};class Vt extends B{static get Default(){return fo}static get DefaultType(){return po}static get NAME(){return ho}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[uo]:this._getTitle(),[_o]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const e=Vt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t]()}})}}E(Vt);const Eo="scrollspy",mo="bs.scrollspy",Ht=`.${mo}`,go=".data-api",Ao=`activate${Ht}`,re=`click${Ht}`,To=`load${Ht}${go}`,bo="dropdown-item",k="active",No='[data-bs-spy="scroll"]',St="[href]",Co=".nav, .list-group",ae=".nav-link",vo=".nav-item",So=".list-group-item",yo=`${ae}, ${vo} > ${ae}, ${So}`,Oo=".dropdown",Do=".dropdown-toggle",wo={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Lo={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class dt extends g{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return wo}static get DefaultType(){return Lo}static get NAME(){return Eo}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=S(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(e=>Number.parseFloat(e))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(o.off(this._config.target,re),o.on(this._config.target,re,St,t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const n=this._rootElement||window,i=e.offsetTop-this._element.offsetTop;if(n.scrollTo){n.scrollTo({top:i,behavior:"smooth"});return}n.scrollTop=i}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(e=>this._observerCallback(e),t)}_observerCallback(t){const e=r=>this._targetLinks.get(`#${r.target.id}`),n=r=>{this._previousScrollData.visibleEntryTop=r.target.offsetTop,this._process(e(r))},i=(this._rootElement||document.documentElement).scrollTop,a=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const r of t){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(r));continue}const c=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(a&&c){if(n(r),!i)return;continue}!a&&!c&&n(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=l.find(St,this._config.target);for(const e of t){if(!e.hash||y(e))continue;const n=l.findOne(decodeURI(e.hash),this._element);F(n)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,n))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(k),this._activateParents(t),o.trigger(this._element,Ao,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(bo)){l.findOne(Do,t.closest(Oo)).classList.add(k);return}for(const e of l.parents(t,Co))for(const n of l.prev(e,yo))n.classList.add(k)}_clearActiveClass(t){t.classList.remove(k);const e=l.find(`${St}.${k}`,t);for(const n of e)n.classList.remove(k)}static jQueryInterface(t){return this.each(function(){const e=dt.getOrCreateInstance(this,t);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}o.on(window,To,()=>{for(const s of l.find(No))dt.getOrCreateInstance(s)});E(dt);const $o="tab",Io="bs.tab",M=`.${Io}`,Mo=`hide${M}`,Ro=`hidden${M}`,Po=`show${M}`,ko=`shown${M}`,Vo=`click${M}`,Ho=`keydown${M}`,xo=`load${M}`,Ko="ArrowLeft",le="ArrowRight",Wo="ArrowUp",ce="ArrowDown",yt="Home",he="End",$="active",ue="fade",Ot="show",Fo="dropdown",Fe=".dropdown-toggle",Yo=".dropdown-menu",Dt=`:not(${Fe})`,Bo='.list-group, .nav, [role="tablist"]',Uo=".nav-item, .list-group-item",jo=`.nav-link${Dt}, .list-group-item${Dt}, [role="tab"]${Dt}`,Ye='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',wt=`${jo}, ${Ye}`,Go=`.${$}[data-bs-toggle="tab"], .${$}[data-bs-toggle="pill"], .${$}[data-bs-toggle="list"]`;class W extends g{constructor(t){super(t),this._parent=this._element.closest(Bo),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),o.on(this._element,Ho,e=>this._keydown(e)))}static get NAME(){return $o}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),n=e?o.trigger(e,Mo,{relatedTarget:t}):null;o.trigger(t,Po,{relatedTarget:e}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add($),this._activate(l.getElementFromSelector(t));const n=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(Ot);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),o.trigger(t,ko,{relatedTarget:e})};this._queueCallback(n,t,t.classList.contains(ue))}_deactivate(t,e){if(!t)return;t.classList.remove($),t.blur(),this._deactivate(l.getElementFromSelector(t));const n=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(Ot);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),o.trigger(t,Ro,{relatedTarget:e})};this._queueCallback(n,t,t.classList.contains(ue))}_keydown(t){if(![Ko,le,Wo,ce,yt,he].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter(i=>!y(i));let n;if([yt,he].includes(t.key))n=e[t.key===yt?0:e.length-1];else{const i=[le,ce].includes(t.key);n=Rt(e,t.target,i,!0)}n&&(n.focus({preventScroll:!0}),W.getOrCreateInstance(n).show())}_getChildren(){return l.find(wt,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const n of e)this._setInitialAttributesOnChild(n)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),n=this._getOuterElement(t);t.setAttribute("aria-selected",e),n!==t&&this._setAttributeIfNotExists(n,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=l.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const n=this._getOuterElement(t);if(!n.classList.contains(Fo))return;const i=(a,r)=>{const c=l.findOne(a,n);c&&c.classList.toggle(r,e)};i(Fe,$),i(Yo,Ot),n.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,n){t.hasAttribute(e)||t.setAttribute(e,n)}_elemIsActive(t){return t.classList.contains($)}_getInnerElement(t){return t.matches(wt)?t:l.findOne(wt,t)}_getOuterElement(t){return t.closest(Uo)||t}static jQueryInterface(t){return this.each(function(){const e=W.getOrCreateInstance(this);if(typeof t=="string"){if(e[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);e[t]()}})}}o.on(document,Vo,Ye,function(s){["A","AREA"].includes(this.tagName)&&s.preventDefault(),!y(this)&&W.getOrCreateInstance(this).show()});o.on(window,xo,()=>{for(const s of l.find(Go))W.getOrCreateInstance(s)});E(W);const zo="toast",qo="bs.toast",w=`.${qo}`,Qo=`mouseover${w}`,Xo=`mouseout${w}`,Zo=`focusin${w}`,Jo=`focusout${w}`,tr=`hide${w}`,er=`hidden${w}`,sr=`show${w}`,nr=`shown${w}`,ir="fade",_e="hide",nt="show",it="showing",or={animation:"boolean",autohide:"boolean",delay:"number"},rr={animation:!0,autohide:!0,delay:5e3};class ft extends g{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return rr}static get DefaultType(){return or}static get NAME(){return zo}show(){if(o.trigger(this._element,sr).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(ir);const e=()=>{this._element.classList.remove(it),o.trigger(this._element,nr),this._maybeScheduleHide()};this._element.classList.remove(_e),z(this._element),this._element.classList.add(nt,it),this._queueCallback(e,this._element,this._config.animation)}hide(){if(!this.isShown()||o.trigger(this._element,tr).defaultPrevented)return;const e=()=>{this._element.classList.add(_e),this._element.classList.remove(it,nt),o.trigger(this._element,er)};this._element.classList.add(it),this._queueCallback(e,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(nt),super.dispose()}isShown(){return this._element.classList.contains(nt)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=e;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=e;break}}if(e){this._clearTimeout();return}const n=t.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){o.on(this._element,Qo,t=>this._onInteraction(t,!0)),o.on(this._element,Xo,t=>this._onInteraction(t,!1)),o.on(this._element,Zo,t=>this._onInteraction(t,!0)),o.on(this._element,Jo,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const e=ft.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof e[t]>"u")throw new TypeError(`No method named "${t}"`);e[t](this)}})}}ht(ft);E(ft);export{ut as Alert,_t as Button,Q as Carousel,G as Collapse,T as Dropdown,K as Modal,O as Offcanvas,Vt as Popover,dt as ScrollSpy,W as Tab,ft as Toast,B as Tooltip};
//# sourceMappingURL=vendor-ui-DQIoTyJ0.js.map
