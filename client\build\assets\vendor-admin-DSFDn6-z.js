var td=Object.defineProperty;var ed=(n,t,e)=>t in n?td(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var P=(n,t,e)=>ed(n,typeof t!="symbol"?t+"":t,e);import{O as Zr,k as nd,a as id,R as Oa,C as va}from"./vendor-misc-BUjjPnRU.js";function Da(n,t,e){for(let i=0;;i++){if(i==n.childCount||i==t.childCount)return n.childCount==t.childCount?null:e;let s=n.child(i),r=t.child(i);if(s==r){e+=s.nodeSize;continue}if(!s.sameMarkup(r))return e;if(s.isText&&s.text!=r.text){for(let o=0;s.text[o]==r.text[o];o++)e++;return e}if(s.content.size||r.content.size){let o=Da(s.content,r.content,e+1);if(o!=null)return o}e+=s.nodeSize}}function Aa(n,t,e,i){for(let s=n.childCount,r=t.childCount;;){if(s==0||r==0)return s==r?null:{a:e,b:i};let o=n.child(--s),l=t.child(--r),a=o.nodeSize;if(o==l){e-=a,i-=a;continue}if(!o.sameMarkup(l))return{a:e,b:i};if(o.isText&&o.text!=l.text){let c=0,h=Math.min(o.text.length,l.text.length);for(;c<h&&o.text[o.text.length-c-1]==l.text[l.text.length-c-1];)c++,e--,i--;return{a:e,b:i}}if(o.content.size||l.content.size){let c=Aa(o.content,l.content,e-1,i-1);if(c)return c}e-=a,i-=a}}class S{constructor(t,e){if(this.content=t,this.size=e||0,e==null)for(let i=0;i<t.length;i++)this.size+=t[i].nodeSize}nodesBetween(t,e,i,s=0,r){for(let o=0,l=0;l<e;o++){let a=this.content[o],c=l+a.nodeSize;if(c>t&&i(a,s+l,r||null,o)!==!1&&a.content.size){let h=l+1;a.nodesBetween(Math.max(0,t-h),Math.min(a.content.size,e-h),i,s+h)}l=c}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,i,s){let r="",o=!0;return this.nodesBetween(t,e,(l,a)=>{let c=l.isText?l.text.slice(Math.max(t,a)-a,e-a):l.isLeaf?s?typeof s=="function"?s(l):s:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&i&&(o?o=!1:r+=i),r+=c},0),r}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,i=t.firstChild,s=this.content.slice(),r=0;for(e.isText&&e.sameMarkup(i)&&(s[s.length-1]=e.withText(e.text+i.text),r=1);r<t.content.length;r++)s.push(t.content[r]);return new S(s,this.size+t.size)}cut(t,e=this.size){if(t==0&&e==this.size)return this;let i=[],s=0;if(e>t)for(let r=0,o=0;o<e;r++){let l=this.content[r],a=o+l.nodeSize;a>t&&((o<t||a>e)&&(l.isText?l=l.cut(Math.max(0,t-o),Math.min(l.text.length,e-o)):l=l.cut(Math.max(0,t-o-1),Math.min(l.content.size,e-o-1))),i.push(l),s+=l.nodeSize),o=a}return new S(i,s)}cutByIndex(t,e){return t==e?S.empty:t==0&&e==this.content.length?this:new S(this.content.slice(t,e))}replaceChild(t,e){let i=this.content[t];if(i==e)return this;let s=this.content.slice(),r=this.size+e.nodeSize-i.nodeSize;return s[t]=e,new S(s,r)}addToStart(t){return new S([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new S(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw new RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,i=0;e<this.content.length;e++){let s=this.content[e];t(s,i,e),i+=s.nodeSize}}findDiffStart(t,e=0){return Da(this,t,e)}findDiffEnd(t,e=this.size,i=t.size){return Aa(this,t,e,i)}findIndex(t,e=-1){if(t==0)return ni(0,t);if(t==this.size)return ni(this.content.length,t);if(t>this.size||t<0)throw new RangeError(`Position ${t} outside of fragment (${this})`);for(let i=0,s=0;;i++){let r=this.child(i),o=s+r.nodeSize;if(o>=t)return o==t||e>0?ni(i+1,o):ni(i,s);s=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(t=>t.toJSON()):null}static fromJSON(t,e){if(!e)return S.empty;if(!Array.isArray(e))throw new RangeError("Invalid input for Fragment.fromJSON");return new S(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return S.empty;let e,i=0;for(let s=0;s<t.length;s++){let r=t[s];i+=r.nodeSize,s&&r.isText&&t[s-1].sameMarkup(r)?(e||(e=t.slice(0,s)),e[e.length-1]=r.withText(e[e.length-1].text+r.text)):e&&e.push(r)}return new S(e||t,i)}static from(t){if(!t)return S.empty;if(t instanceof S)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new S([t],t.nodeSize);throw new RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}S.empty=new S([],0);const us={index:0,offset:0};function ni(n,t){return us.index=n,us.offset=t,us}function Oi(n,t){if(n===t)return!0;if(!(n&&typeof n=="object")||!(t&&typeof t=="object"))return!1;let e=Array.isArray(n);if(Array.isArray(t)!=e)return!1;if(e){if(n.length!=t.length)return!1;for(let i=0;i<n.length;i++)if(!Oi(n[i],t[i]))return!1}else{for(let i in n)if(!(i in t)||!Oi(n[i],t[i]))return!1;for(let i in t)if(!(i in n))return!1}return!0}let V=class Ws{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,i=!1;for(let s=0;s<t.length;s++){let r=t[s];if(this.eq(r))return t;if(this.type.excludes(r.type))e||(e=t.slice(0,s));else{if(r.type.excludes(this.type))return t;!i&&r.type.rank>this.type.rank&&(e||(e=t.slice(0,s)),e.push(this),i=!0),e&&e.push(r)}}return e||(e=t.slice()),i||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&Oi(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Mark.fromJSON");let i=t.marks[e.type];if(!i)throw new RangeError(`There is no mark type ${e.type} in this schema`);let s=i.create(e.attrs);return i.checkAttrs(s.attrs),s}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].eq(e[i]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&t.length==0)return Ws.none;if(t instanceof Ws)return[t];let e=t.slice();return e.sort((i,s)=>i.type.rank-s.type.rank),e}};V.none=[];class vi extends Error{}class T{constructor(t,e,i){this.content=t,this.openStart=e,this.openEnd=i}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let i=Na(this.content,t+this.openStart,e);return i&&new T(i,this.openStart,this.openEnd)}removeBetween(t,e){return new T(Ea(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return T.empty;let i=e.openStart||0,s=e.openEnd||0;if(typeof i!="number"||typeof s!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new T(S.fromJSON(t,e.content),i,s)}static maxOpen(t,e=!0){let i=0,s=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)i++;for(let r=t.lastChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.lastChild)s++;return new T(t,i,s)}}T.empty=new T(S.empty,0,0);function Ea(n,t,e){let{index:i,offset:s}=n.findIndex(t),r=n.maybeChild(i),{index:o,offset:l}=n.findIndex(e);if(s==t||r.isText){if(l!=e&&!n.child(o).isText)throw new RangeError("Removing non-flat range");return n.cut(0,t).append(n.cut(e))}if(i!=o)throw new RangeError("Removing non-flat range");return n.replaceChild(i,r.copy(Ea(r.content,t-s-1,e-s-1)))}function Na(n,t,e,i){let{index:s,offset:r}=n.findIndex(t),o=n.maybeChild(s);if(r==t||o.isText)return n.cut(0,t).append(e).append(n.cut(t));let l=Na(o.content,t-r-1,e);return l&&n.replaceChild(s,o.copy(l))}function sd(n,t,e){if(e.openStart>n.depth)throw new vi("Inserted content deeper than insertion position");if(n.depth-e.openStart!=t.depth-e.openEnd)throw new vi("Inconsistent open depths");return Pa(n,t,e,0)}function Pa(n,t,e,i){let s=n.index(i),r=n.node(i);if(s==t.index(i)&&i<n.depth-e.openStart){let o=Pa(n,t,e,i+1);return r.copy(r.content.replaceChild(s,o))}else if(e.content.size)if(!e.openStart&&!e.openEnd&&n.depth==i&&t.depth==i){let o=n.parent,l=o.content;return Ee(o,l.cut(0,n.parentOffset).append(e.content).append(l.cut(t.parentOffset)))}else{let{start:o,end:l}=rd(e,n);return Ee(r,Ra(n,o,l,t,i))}else return Ee(r,Di(n,t,i))}function Ia(n,t){if(!t.type.compatibleContent(n.type))throw new vi("Cannot join "+t.type.name+" onto "+n.type.name)}function js(n,t,e){let i=n.node(e);return Ia(i,t.node(e)),i}function Ae(n,t){let e=t.length-1;e>=0&&n.isText&&n.sameMarkup(t[e])?t[e]=n.withText(t[e].text+n.text):t.push(n)}function Tn(n,t,e,i){let s=(t||n).node(e),r=0,o=t?t.index(e):s.childCount;n&&(r=n.index(e),n.depth>e?r++:n.textOffset&&(Ae(n.nodeAfter,i),r++));for(let l=r;l<o;l++)Ae(s.child(l),i);t&&t.depth==e&&t.textOffset&&Ae(t.nodeBefore,i)}function Ee(n,t){return n.type.checkContent(t),n.copy(t)}function Ra(n,t,e,i,s){let r=n.depth>s&&js(n,t,s+1),o=i.depth>s&&js(e,i,s+1),l=[];return Tn(null,n,s,l),r&&o&&t.index(s)==e.index(s)?(Ia(r,o),Ae(Ee(r,Ra(n,t,e,i,s+1)),l)):(r&&Ae(Ee(r,Di(n,t,s+1)),l),Tn(t,e,s,l),o&&Ae(Ee(o,Di(e,i,s+1)),l)),Tn(i,null,s,l),new S(l)}function Di(n,t,e){let i=[];if(Tn(null,n,e,i),n.depth>e){let s=js(n,t,e+1);Ae(Ee(s,Di(n,t,e+1)),i)}return Tn(t,null,e,i),new S(i)}function rd(n,t){let e=t.depth-n.openStart,s=t.node(e).copy(n.content);for(let r=e-1;r>=0;r--)s=t.node(r).copy(S.from(s));return{start:s.resolveNoCache(n.openStart+e),end:s.resolveNoCache(s.content.size-n.openEnd-e)}}class Rn{constructor(t,e,i){this.pos=t,this.path=e,this.parentOffset=i,this.depth=e.length/3-1}resolveDepth(t){return t==null?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[this.resolveDepth(t)*3]}index(t){return this.path[this.resolveDepth(t)*3+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t==this.depth&&!this.textOffset?0:1)}start(t){return t=this.resolveDepth(t),t==0?0:this.path[t*3-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(t=this.resolveDepth(t),!t)throw new RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[t*3-1]}after(t){if(t=this.resolveDepth(t),!t)throw new RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[t*3-1]+this.path[t*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let i=this.pos-this.path[this.path.length-1],s=t.child(e);return i?t.child(e).cut(i):s}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):t==0?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let i=this.path[e*3],s=e==0?0:this.path[e*3-1]+1;for(let r=0;r<t;r++)s+=i.child(r).nodeSize;return s}marks(){let t=this.parent,e=this.index();if(t.content.size==0)return V.none;if(this.textOffset)return t.child(e).marks;let i=t.maybeChild(e-1),s=t.maybeChild(e);if(!i){let l=i;i=s,s=l}let r=i.marks;for(var o=0;o<r.length;o++)r[o].type.spec.inclusive===!1&&(!s||!r[o].isInSet(s.marks))&&(r=r[o--].removeFromSet(r));return r}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let i=e.marks,s=t.parent.maybeChild(t.index());for(var r=0;r<i.length;r++)i[r].type.spec.inclusive===!1&&(!s||!i[r].isInSet(s.marks))&&(i=i[r--].removeFromSet(i));return i}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let i=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);i>=0;i--)if(t.pos<=this.end(i)&&(!e||e(this.node(i))))return new Ai(this,t,i);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw new RangeError("Position "+e+" out of range");let i=[],s=0,r=e;for(let o=t;;){let{index:l,offset:a}=o.content.findIndex(r),c=r-a;if(i.push(o,l,s+a),!c||(o=o.child(l),o.isText))break;r=c-1,s+=a+1}return new Rn(e,i,r)}static resolveCached(t,e){let i=to.get(t);if(i)for(let r=0;r<i.elts.length;r++){let o=i.elts[r];if(o.pos==e)return o}else to.set(t,i=new od);let s=i.elts[i.i]=Rn.resolve(t,e);return i.i=(i.i+1)%ld,s}}class od{constructor(){this.elts=[],this.i=0}}const ld=12,to=new WeakMap;class Ai{constructor(t,e,i){this.$from=t,this.$to=e,this.depth=i}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const ad=Object.create(null);let se=class Ks{constructor(t,e,i,s=V.none){this.type=t,this.attrs=e,this.marks=s,this.content=i||S.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,i,s=0){this.content.nodesBetween(t,e,i,s,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,i,s){return this.content.textBetween(t,e,i,s)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,i){return this.type==t&&Oi(this.attrs,e||t.defaultAttrs||ad)&&V.sameSet(this.marks,i||V.none)}copy(t=null){return t==this.content?this:new Ks(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new Ks(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return t==0&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,i=!1){if(t==e)return T.empty;let s=this.resolve(t),r=this.resolve(e),o=i?0:s.sharedDepth(e),l=s.start(o),c=s.node(o).content.cut(s.pos-l,r.pos-l);return new T(c,s.depth-o,r.depth-o)}replace(t,e,i){return sd(this.resolve(t),this.resolve(e),i)}nodeAt(t){for(let e=this;;){let{index:i,offset:s}=e.content.findIndex(t);if(e=e.maybeChild(i),!e)return null;if(s==t||e.isText)return e;t-=s+1}}childAfter(t){let{index:e,offset:i}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:i}}childBefore(t){if(t==0)return{node:null,index:0,offset:0};let{index:e,offset:i}=this.content.findIndex(t);if(i<t)return{node:this.content.child(e),index:e,offset:i};let s=this.content.child(e-1);return{node:s,index:e-1,offset:i-s.nodeSize}}resolve(t){return Rn.resolveCached(this,t)}resolveNoCache(t){return Rn.resolve(this,t)}rangeHasMark(t,e,i){let s=!1;return e>t&&this.nodesBetween(t,e,r=>(i.isInSet(r.marks)&&(s=!0),!s)),s}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),La(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw new Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,i=S.empty,s=0,r=i.childCount){let o=this.contentMatchAt(t).matchFragment(i,s,r),l=o&&o.matchFragment(this.content,e);if(!l||!l.validEnd)return!1;for(let a=s;a<r;a++)if(!this.type.allowsMarks(i.child(a).marks))return!1;return!0}canReplaceWith(t,e,i,s){if(s&&!this.type.allowsMarks(s))return!1;let r=this.contentMatchAt(t).matchType(i),o=r&&r.matchFragment(this.content,e);return o?o.validEnd:!1}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let t=V.none;for(let e=0;e<this.marks.length;e++){let i=this.marks[e];i.type.checkAttrs(i.attrs),t=i.addToSet(t)}if(!V.sameSet(t,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map(e=>e.toJSON())),t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Node.fromJSON");let i;if(e.marks){if(!Array.isArray(e.marks))throw new RangeError("Invalid mark data for Node.fromJSON");i=e.marks.map(t.markFromJSON)}if(e.type=="text"){if(typeof e.text!="string")throw new RangeError("Invalid text node in JSON");return t.text(e.text,i)}let s=S.fromJSON(t,e.content),r=t.nodeType(e.type).create(e.attrs,s,i);return r.type.checkAttrs(r.attrs),r}};se.prototype.text=void 0;class Ei extends se{constructor(t,e,i,s){if(super(t,e,null,s),!i)throw new RangeError("Empty text nodes are not allowed");this.text=i}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):La(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new Ei(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new Ei(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return t==0&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function La(n,t){for(let e=n.length-1;e>=0;e--)t=n[e].type.name+"("+t+")";return t}class Le{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){let i=new cd(t,e);if(i.next==null)return Le.empty;let s=Ba(i);i.next&&i.err("Unexpected trailing text");let r=gd(md(s));return yd(r,i),r}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,i=t.childCount){let s=this;for(let r=e;s&&r<i;r++)s=s.matchType(t.child(r).type);return s}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!(e.isText||e.hasRequiredAttrs()))return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let i=0;i<t.next.length;i++)if(this.next[e].type==t.next[i].type)return!0;return!1}fillBefore(t,e=!1,i=0){let s=[this];function r(o,l){let a=o.matchFragment(t,i);if(a&&(!e||a.validEnd))return S.from(l.map(c=>c.createAndFill()));for(let c=0;c<o.next.length;c++){let{type:h,next:d}=o.next[c];if(!(h.isText||h.hasRequiredAttrs())&&s.indexOf(d)==-1){s.push(d);let u=r(d,l.concat(h));if(u)return u}}return null}return r(this,[])}findWrapping(t){for(let i=0;i<this.wrapCache.length;i+=2)if(this.wrapCache[i]==t)return this.wrapCache[i+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),i=[{match:this,type:null,via:null}];for(;i.length;){let s=i.shift(),r=s.match;if(r.matchType(t)){let o=[];for(let l=s;l.type;l=l.via)o.push(l.type);return o.reverse()}for(let o=0;o<r.next.length;o++){let{type:l,next:a}=r.next[o];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in e)&&(!s.type||a.validEnd)&&(i.push({match:l.contentMatch,type:l,via:s}),e[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw new RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];function e(i){t.push(i);for(let s=0;s<i.next.length;s++)t.indexOf(i.next[s].next)==-1&&e(i.next[s].next)}return e(this),t.map((i,s)=>{let r=s+(i.validEnd?"*":" ")+" ";for(let o=0;o<i.next.length;o++)r+=(o?", ":"")+i.next[o].type.name+"->"+t.indexOf(i.next[o].next);return r}).join(`
`)}}Le.empty=new Le(!0);class cd{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw new SyntaxError(t+" (in content expression '"+this.string+"')")}}function Ba(n){let t=[];do t.push(hd(n));while(n.eat("|"));return t.length==1?t[0]:{type:"choice",exprs:t}}function hd(n){let t=[];do t.push(dd(n));while(n.next&&n.next!=")"&&n.next!="|");return t.length==1?t[0]:{type:"seq",exprs:t}}function dd(n){let t=pd(n);for(;;)if(n.eat("+"))t={type:"plus",expr:t};else if(n.eat("*"))t={type:"star",expr:t};else if(n.eat("?"))t={type:"opt",expr:t};else if(n.eat("{"))t=ud(n,t);else break;return t}function eo(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let t=Number(n.next);return n.pos++,t}function ud(n,t){let e=eo(n),i=e;return n.eat(",")&&(n.next!="}"?i=eo(n):i=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:e,max:i,expr:t}}function fd(n,t){let e=n.nodeTypes,i=e[t];if(i)return[i];let s=[];for(let r in e){let o=e[r];o.isInGroup(t)&&s.push(o)}return s.length==0&&n.err("No node type or group '"+t+"' found"),s}function pd(n){if(n.eat("(")){let t=Ba(n);return n.eat(")")||n.err("Missing closing paren"),t}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let t=fd(n,n.next).map(e=>(n.inline==null?n.inline=e.isInline:n.inline!=e.isInline&&n.err("Mixing inline and block content"),{type:"name",value:e}));return n.pos++,t.length==1?t[0]:{type:"choice",exprs:t}}}function md(n){let t=[[]];return s(r(n,0),e()),t;function e(){return t.push([])-1}function i(o,l,a){let c={term:a,to:l};return t[o].push(c),c}function s(o,l){o.forEach(a=>a.to=l)}function r(o,l){if(o.type=="choice")return o.exprs.reduce((a,c)=>a.concat(r(c,l)),[]);if(o.type=="seq")for(let a=0;;a++){let c=r(o.exprs[a],l);if(a==o.exprs.length-1)return c;s(c,l=e())}else if(o.type=="star"){let a=e();return i(l,a),s(r(o.expr,a),a),[i(a)]}else if(o.type=="plus"){let a=e();return s(r(o.expr,l),a),s(r(o.expr,a),a),[i(a)]}else{if(o.type=="opt")return[i(l)].concat(r(o.expr,l));if(o.type=="range"){let a=l;for(let c=0;c<o.min;c++){let h=e();s(r(o.expr,a),h),a=h}if(o.max==-1)s(r(o.expr,a),a);else for(let c=o.min;c<o.max;c++){let h=e();i(a,h),s(r(o.expr,a),h),a=h}return[i(a)]}else{if(o.type=="name")return[i(l,void 0,o.value)];throw new Error("Unknown expr type")}}}}function za(n,t){return t-n}function no(n,t){let e=[];return i(t),e.sort(za);function i(s){let r=n[s];if(r.length==1&&!r[0].term)return i(r[0].to);e.push(s);for(let o=0;o<r.length;o++){let{term:l,to:a}=r[o];!l&&e.indexOf(a)==-1&&i(a)}}}function gd(n){let t=Object.create(null);return e(no(n,0));function e(i){let s=[];i.forEach(o=>{n[o].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let h=0;h<s.length;h++)s[h][0]==l&&(c=s[h][1]);no(n,a).forEach(h=>{c||s.push([l,c=[]]),c.indexOf(h)==-1&&c.push(h)})})});let r=t[i.join(",")]=new Le(i.indexOf(n.length-1)>-1);for(let o=0;o<s.length;o++){let l=s[o][1].sort(za);r.next.push({type:s[o][0],next:t[l.join(",")]||e(l)})}return r}}function yd(n,t){for(let e=0,i=[n];e<i.length;e++){let s=i[e],r=!s.validEnd,o=[];for(let l=0;l<s.next.length;l++){let{type:a,next:c}=s.next[l];o.push(a.name),r&&!(a.isText||a.hasRequiredAttrs())&&(r=!1),i.indexOf(c)==-1&&i.push(c)}r&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function Fa(n){let t=Object.create(null);for(let e in n){let i=n[e];if(!i.hasDefault)return null;t[e]=i.default}return t}function Va(n,t){let e=Object.create(null);for(let i in n){let s=t&&t[i];if(s===void 0){let r=n[i];if(r.hasDefault)s=r.default;else throw new RangeError("No value supplied for attribute "+i)}e[i]=s}return e}function Ha(n,t,e,i){for(let s in t)if(!(s in n))throw new RangeError(`Unsupported attribute ${s} for ${e} of type ${s}`);for(let s in n){let r=n[s];r.validate&&r.validate(t[s])}}function $a(n,t){let e=Object.create(null);if(t)for(let i in t)e[i]=new xd(n,i,t[i]);return e}let io=class Wa{constructor(t,e,i){this.name=t,this.schema=e,this.spec=i,this.markSet=null,this.groups=i.group?i.group.split(" "):[],this.attrs=$a(t,i.attrs),this.defaultAttrs=Fa(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(i.inline||t=="text"),this.isText=t=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Le.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(t){return this.groups.indexOf(t)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:Va(this.attrs,t)}create(t=null,e,i){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new se(this,this.computeAttrs(t),S.from(e),V.setFrom(i))}createChecked(t=null,e,i){return e=S.from(e),this.checkContent(e),new se(this,this.computeAttrs(t),e,V.setFrom(i))}createAndFill(t=null,e,i){if(t=this.computeAttrs(t),e=S.from(e),e.size){let o=this.contentMatch.fillBefore(e);if(!o)return null;e=o.append(e)}let s=this.contentMatch.matchFragment(e),r=s&&s.fillBefore(S.empty,!0);return r?new se(this,t,e.append(r),V.setFrom(i)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let i=0;i<t.childCount;i++)if(!this.allowsMarks(t.child(i).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw new RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}checkAttrs(t){Ha(this.attrs,t,"node",this.name)}allowsMarkType(t){return this.markSet==null||this.markSet.indexOf(t)>-1}allowsMarks(t){if(this.markSet==null)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){if(this.markSet==null)return t;let e;for(let i=0;i<t.length;i++)this.allowsMarkType(t[i].type)?e&&e.push(t[i]):e||(e=t.slice(0,i));return e?e.length?e:V.none:t}static compile(t,e){let i=Object.create(null);t.forEach((r,o)=>i[r]=new Wa(r,e,o));let s=e.spec.topNode||"doc";if(!i[s])throw new RangeError("Schema is missing its top node type ('"+s+"')");if(!i.text)throw new RangeError("Every schema needs a 'text' type");for(let r in i.text.attrs)throw new RangeError("The text node type should not have attributes");return i}};function bd(n,t,e){let i=e.split("|");return s=>{let r=s===null?"null":typeof s;if(i.indexOf(r)<0)throw new RangeError(`Expected value of type ${i} for attribute ${t} on type ${n}, got ${r}`)}}class xd{constructor(t,e,i){this.hasDefault=Object.prototype.hasOwnProperty.call(i,"default"),this.default=i.default,this.validate=typeof i.validate=="string"?bd(t,e,i.validate):i.validate}get isRequired(){return!this.hasDefault}}class qi{constructor(t,e,i,s){this.name=t,this.rank=e,this.schema=i,this.spec=s,this.attrs=$a(t,s.attrs),this.excluded=null;let r=Fa(this.attrs);this.instance=r?new V(this,r):null}create(t=null){return!t&&this.instance?this.instance:new V(this,Va(this.attrs,t))}static compile(t,e){let i=Object.create(null),s=0;return t.forEach((r,o)=>i[r]=new qi(r,s++,e,o)),i}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}checkAttrs(t){Ha(this.attrs,t,"mark",this.name)}excludes(t){return this.excluded.indexOf(t)>-1}}class ja{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let e=this.spec={};for(let s in t)e[s]=t[s];e.nodes=Zr.from(t.nodes),e.marks=Zr.from(t.marks||{}),this.nodes=io.compile(this.spec.nodes,this),this.marks=qi.compile(this.spec.marks,this);let i=Object.create(null);for(let s in this.nodes){if(s in this.marks)throw new RangeError(s+" can not be both a node and a mark");let r=this.nodes[s],o=r.spec.content||"",l=r.spec.marks;if(r.contentMatch=i[o]||(i[o]=Le.parse(o,this.nodes)),r.inlineContent=r.contentMatch.inlineContent,r.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!r.isInline||!r.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=r}r.markSet=l=="_"?null:l?so(this,l.split(" ")):l==""||!r.inlineContent?[]:null}for(let s in this.marks){let r=this.marks[s],o=r.spec.excludes;r.excluded=o==null?[r]:o==""?[]:so(this,o.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,i,s){if(typeof t=="string")t=this.nodeType(t);else if(t instanceof io){if(t.schema!=this)throw new RangeError("Node type from different schema used ("+t.name+")")}else throw new RangeError("Invalid node type: "+t);return t.createChecked(e,i,s)}text(t,e){let i=this.nodes.text;return new Ei(i,i.defaultAttrs,t,V.setFrom(e))}mark(t,e){return typeof t=="string"&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return se.fromJSON(this,t)}markFromJSON(t){return V.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw new RangeError("Unknown node type: "+t);return e}}function so(n,t){let e=[];for(let i=0;i<t.length;i++){let s=t[i],r=n.marks[s],o=r;if(r)e.push(r);else for(let l in n.marks){let a=n.marks[l];(s=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(s)>-1)&&e.push(o=a)}if(!o)throw new SyntaxError("Unknown mark type: '"+t[i]+"'")}return e}function kd(n){return n.tag!=null}function Sd(n){return n.style!=null}class re{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[];let i=this.matchedStyles=[];e.forEach(s=>{if(kd(s))this.tags.push(s);else if(Sd(s)){let r=/[^=]*/.exec(s.style)[0];i.indexOf(r)<0&&i.push(r),this.styles.push(s)}}),this.normalizeLists=!this.tags.some(s=>{if(!/^(ul|ol)\b/.test(s.tag)||!s.node)return!1;let r=t.nodes[s.node];return r.contentMatch.matchType(r)})}parse(t,e={}){let i=new oo(this,e,!1);return i.addAll(t,V.none,e.from,e.to),i.finish()}parseSlice(t,e={}){let i=new oo(this,e,!0);return i.addAll(t,V.none,e.from,e.to),T.maxOpen(i.finish())}matchTag(t,e,i){for(let s=i?this.tags.indexOf(i)+1:0;s<this.tags.length;s++){let r=this.tags[s];if(Cd(t,r.tag)&&(r.namespace===void 0||t.namespaceURI==r.namespace)&&(!r.context||e.matchesContext(r.context))){if(r.getAttrs){let o=r.getAttrs(t);if(o===!1)continue;r.attrs=o||void 0}return r}}}matchStyle(t,e,i,s){for(let r=s?this.styles.indexOf(s)+1:0;r<this.styles.length;r++){let o=this.styles[r],l=o.style;if(!(l.indexOf(t)!=0||o.context&&!i.matchesContext(o.context)||l.length>t.length&&(l.charCodeAt(t.length)!=61||l.slice(t.length+1)!=e))){if(o.getAttrs){let a=o.getAttrs(e);if(a===!1)continue;o.attrs=a||void 0}return o}}}static schemaRules(t){let e=[];function i(s){let r=s.priority==null?50:s.priority,o=0;for(;o<e.length;o++){let l=e[o];if((l.priority==null?50:l.priority)<r)break}e.splice(o,0,s)}for(let s in t.marks){let r=t.marks[s].spec.parseDOM;r&&r.forEach(o=>{i(o=lo(o)),o.mark||o.ignore||o.clearMark||(o.mark=s)})}for(let s in t.nodes){let r=t.nodes[s].spec.parseDOM;r&&r.forEach(o=>{i(o=lo(o)),o.node||o.ignore||o.mark||(o.node=s)})}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new re(t,re.schemaRules(t)))}}const Ka={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Md={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},qa={ol:!0,ul:!0},Ln=1,qs=2,ki=4;function ro(n,t,e){return t!=null?(t?Ln:0)|(t==="full"?qs:0):n&&n.whitespace=="pre"?Ln|qs:e&-5}class ii{constructor(t,e,i,s,r,o){this.type=t,this.attrs=e,this.marks=i,this.solid=s,this.options=o,this.content=[],this.activeMarks=V.none,this.match=r||(o&ki?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(S.from(t));if(e)this.match=this.type.contentMatch.matchFragment(e);else{let i=this.type.contentMatch,s;return(s=i.findWrapping(t.type))?(this.match=i,s):null}}return this.match.findWrapping(t.type)}finish(t){if(!(this.options&Ln)){let i=this.content[this.content.length-1],s;if(i&&i.isText&&(s=/[ \t\r\n\u000c]+$/.exec(i.text))){let r=i;i.text.length==s[0].length?this.content.pop():this.content[this.content.length-1]=r.withText(r.text.slice(0,r.text.length-s[0].length))}}let e=S.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(S.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!Ka.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class oo{constructor(t,e,i){this.parser=t,this.options=e,this.isOpen=i,this.open=0,this.localPreserveWS=!1;let s=e.topNode,r,o=ro(null,e.preserveWhitespace,0)|(i?ki:0);s?r=new ii(s.type,s.attrs,V.none,!0,e.topMatch||s.type.contentMatch,o):i?r=new ii(null,null,V.none,!0,null,o):r=new ii(t.schema.topNodeType,null,V.none,!0,null,o),this.nodes=[r],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t,e){t.nodeType==3?this.addTextNode(t,e):t.nodeType==1&&this.addElement(t,e)}addTextNode(t,e){let i=t.nodeValue,s=this.top,r=s.options&qs?"full":this.localPreserveWS||(s.options&Ln)>0;if(r==="full"||s.inlineContext(t)||/[^ \t\r\n\u000c]/.test(i)){if(r)r!=="full"?i=i.replace(/\r?\n|\r/g," "):i=i.replace(/\r\n?/g,`
`);else if(i=i.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(i)&&this.open==this.nodes.length-1){let o=s.content[s.content.length-1],l=t.previousSibling;(!o||l&&l.nodeName=="BR"||o.isText&&/[ \t\r\n\u000c]$/.test(o.text))&&(i=i.slice(1))}i&&this.insertNode(this.parser.schema.text(i),e,!/\S/.test(i)),this.findInText(t)}else this.findInside(t)}addElement(t,e,i){let s=this.localPreserveWS,r=this.top;(t.tagName=="PRE"||/pre/.test(t.style&&t.style.whiteSpace))&&(this.localPreserveWS=!0);let o=t.nodeName.toLowerCase(),l;qa.hasOwnProperty(o)&&this.parser.normalizeLists&&wd(t);let a=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(l=this.parser.matchTag(t,this,i));t:if(a?a.ignore:Md.hasOwnProperty(o))this.findInside(t),this.ignoreFallback(t,e);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(t=a.skip);let c,h=this.needsBlock;if(Ka.hasOwnProperty(o))r.content.length&&r.content[0].isInline&&this.open&&(this.open--,r=this.top),c=!0,r.type||(this.needsBlock=!0);else if(!t.firstChild){this.leafFallback(t,e);break t}let d=a&&a.skip?e:this.readStyles(t,e);d&&this.addAll(t,d),c&&this.sync(r),this.needsBlock=h}else{let c=this.readStyles(t,e);c&&this.addElementByRule(t,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=s}leafFallback(t,e){t.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode(`
`),e)}ignoreFallback(t,e){t.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),e,!0)}readStyles(t,e){let i=t.style;if(i&&i.length)for(let s=0;s<this.parser.matchedStyles.length;s++){let r=this.parser.matchedStyles[s],o=i.getPropertyValue(r);if(o)for(let l=void 0;;){let a=this.parser.matchStyle(r,o,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?e=e.filter(c=>!a.clearMark(c)):e=e.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return e}addElementByRule(t,e,i,s){let r,o;if(e.node)if(o=this.parser.schema.nodes[e.node],o.isLeaf)this.insertNode(o.create(e.attrs),i,t.nodeName=="BR")||this.leafFallback(t,i);else{let a=this.enter(o,e.attrs||null,i,e.preserveWhitespace);a&&(r=!0,i=a)}else{let a=this.parser.schema.marks[e.mark];i=i.concat(a.create(e.attrs))}let l=this.top;if(o&&o.isLeaf)this.findInside(t);else if(s)this.addElement(t,i,s);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach(a=>this.insertNode(a,i,!1));else{let a=t;typeof e.contentElement=="string"?a=t.querySelector(e.contentElement):typeof e.contentElement=="function"?a=e.contentElement(t):e.contentElement&&(a=e.contentElement),this.findAround(t,a,!0),this.addAll(a,i),this.findAround(t,a,!1)}r&&this.sync(l)&&this.open--}addAll(t,e,i,s){let r=i||0;for(let o=i?t.childNodes[i]:t.firstChild,l=s==null?null:t.childNodes[s];o!=l;o=o.nextSibling,++r)this.findAtPoint(t,r),this.addDOM(o,e);this.findAtPoint(t,r)}findPlace(t,e,i){let s,r;for(let o=this.open,l=0;o>=0;o--){let a=this.nodes[o],c=a.findWrapping(t);if(c&&(!s||s.length>c.length+l)&&(s=c,r=a,!c.length))break;if(a.solid){if(i)break;l+=2}}if(!s)return null;this.sync(r);for(let o=0;o<s.length;o++)e=this.enterInner(s[o],null,e,!1);return e}insertNode(t,e,i){if(t.isInline&&this.needsBlock&&!this.top.type){let r=this.textblockFromContext();r&&(e=this.enterInner(r,null,e))}let s=this.findPlace(t,e,i);if(s){this.closeExtra();let r=this.top;r.match&&(r.match=r.match.matchType(t.type));let o=V.none;for(let l of s.concat(t.marks))(r.type?r.type.allowsMarkType(l.type):ao(l.type,t.type))&&(o=l.addToSet(o));return r.content.push(t.mark(o)),!0}return!1}enter(t,e,i,s){let r=this.findPlace(t.create(e),i,!1);return r&&(r=this.enterInner(t,e,i,!0,s)),r}enterInner(t,e,i,s=!1,r){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(t);let l=ro(t,r,o.options);o.options&ki&&o.content.length==0&&(l|=ki);let a=V.none;return i=i.filter(c=>(o.type?o.type.allowsMarkType(c.type):ao(c.type,t))?(a=c.addToSet(a),!1):!0),this.nodes.push(new ii(t,e,a,s,null,l)),this.open++,i}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(t){for(let e=this.open;e>=0;e--){if(this.nodes[e]==t)return this.open=e,!0;this.localPreserveWS&&(this.nodes[e].options|=Ln)}return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let i=this.nodes[e].content;for(let s=i.length-1;s>=0;s--)t+=i[s].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let i=0;i<this.find.length;i++)this.find[i].node==t&&this.find[i].offset==e&&(this.find[i].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].pos==null&&t.nodeType==1&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,i){if(t!=e&&this.find)for(let s=0;s<this.find.length;s++)this.find[s].pos==null&&t.nodeType==1&&t.contains(this.find[s].node)&&e.compareDocumentPosition(this.find[s].node)&(i?2:4)&&(this.find[s].pos=this.currentPos)}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),i=this.options.context,s=!this.isOpen&&(!i||i.parent.type==this.nodes[0].type),r=-(i?i.depth+1:0)+(s?0:1),o=(l,a)=>{for(;l>=0;l--){let c=e[l];if(c==""){if(l==e.length-1||l==0)continue;for(;a>=r;a--)if(o(l-1,a))return!0;return!1}else{let h=a>0||a==0&&s?this.nodes[a].type:i&&a>=r?i.node(a-r).type:null;if(!h||h.name!=c&&!h.isInGroup(c))return!1;a--}}return!0};return o(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let i=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(i&&i.isTextblock&&i.defaultAttrs)return i}for(let e in this.parser.schema.nodes){let i=this.parser.schema.nodes[e];if(i.isTextblock&&i.defaultAttrs)return i}}}function wd(n){for(let t=n.firstChild,e=null;t;t=t.nextSibling){let i=t.nodeType==1?t.nodeName.toLowerCase():null;i&&qa.hasOwnProperty(i)&&e?(e.appendChild(t),t=e):i=="li"?e=t:i&&(e=null)}}function Cd(n,t){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,t)}function lo(n){let t={};for(let e in n)t[e]=n[e];return t}function ao(n,t){let e=t.schema.nodes;for(let i in e){let s=e[i];if(!s.allowsMarkType(n))continue;let r=[],o=l=>{r.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:h}=l.edge(a);if(c==t||r.indexOf(h)<0&&o(h))return!0}};if(o(s.contentMatch))return!0}}class He{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},i){i||(i=fs(e).createDocumentFragment());let s=i,r=[];return t.forEach(o=>{if(r.length||o.marks.length){let l=0,a=0;for(;l<r.length&&a<o.marks.length;){let c=o.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(r[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<r.length;)s=r.pop()[1];for(;a<o.marks.length;){let c=o.marks[a++],h=this.serializeMark(c,o.isInline,e);h&&(r.push([c,s]),s.appendChild(h.dom),s=h.contentDOM||h.dom)}}s.appendChild(this.serializeNodeInner(o,e))}),i}serializeNodeInner(t,e){let{dom:i,contentDOM:s}=Si(fs(e),this.nodes[t.type.name](t),null,t.attrs);if(s){if(t.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,s)}return i}serializeNode(t,e={}){let i=this.serializeNodeInner(t,e);for(let s=t.marks.length-1;s>=0;s--){let r=this.serializeMark(t.marks[s],t.isInline,e);r&&((r.contentDOM||r.dom).appendChild(i),i=r.dom)}return i}serializeMark(t,e,i={}){let s=this.marks[t.type.name];return s&&Si(fs(i),s(t,e),null,t.attrs)}static renderSpec(t,e,i=null,s){return Si(t,e,i,s)}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new He(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=co(t.nodes);return e.text||(e.text=i=>i.text),e}static marksFromSchema(t){return co(t.marks)}}function co(n){let t={};for(let e in n){let i=n[e].spec.toDOM;i&&(t[e]=i)}return t}function fs(n){return n.document||window.document}const ho=new WeakMap;function _d(n){let t=ho.get(n);return t===void 0&&ho.set(n,t=Td(n)),t}function Td(n){let t=null;function e(i){if(i&&typeof i=="object")if(Array.isArray(i))if(typeof i[0]=="string")t||(t=[]),t.push(i);else for(let s=0;s<i.length;s++)e(i[s]);else for(let s in i)e(i[s])}return e(n),t}function Si(n,t,e,i){if(typeof t=="string")return{dom:n.createTextNode(t)};if(t.nodeType!=null)return{dom:t};if(t.dom&&t.dom.nodeType!=null)return t;let s=t[0],r;if(typeof s!="string")throw new RangeError("Invalid array passed to renderSpec");if(i&&(r=_d(i))&&r.indexOf(t)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let o=s.indexOf(" ");o>0&&(e=s.slice(0,o),s=s.slice(o+1));let l,a=e?n.createElementNS(e,s):n.createElement(s),c=t[1],h=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){h=2;for(let d in c)if(c[d]!=null){let u=d.indexOf(" ");u>0?a.setAttributeNS(d.slice(0,u),d.slice(u+1),c[d]):a.setAttribute(d,c[d])}}for(let d=h;d<t.length;d++){let u=t[d];if(u===0){if(d<t.length-1||d>h)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:f,contentDOM:p}=Si(n,u,e,i);if(a.appendChild(f),p){if(l)throw new RangeError("Multiple content holes");l=p}}}return{dom:a,contentDOM:l}}const Ja=65535,Ua=Math.pow(2,16);function Od(n,t){return n+t*Ua}function uo(n){return n&Ja}function vd(n){return(n-(n&Ja))/Ua}const Ga=1,Ya=2,Mi=4,Xa=8;class Js{constructor(t,e,i){this.pos=t,this.delInfo=e,this.recover=i}get deleted(){return(this.delInfo&Xa)>0}get deletedBefore(){return(this.delInfo&(Ga|Mi))>0}get deletedAfter(){return(this.delInfo&(Ya|Mi))>0}get deletedAcross(){return(this.delInfo&Mi)>0}}class bt{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&bt.empty)return bt.empty}recover(t){let e=0,i=uo(t);if(!this.inverted)for(let s=0;s<i;s++)e+=this.ranges[s*3+2]-this.ranges[s*3+1];return this.ranges[i*3]+e+vd(t)}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,i){let s=0,r=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?s:0);if(a>t)break;let c=this.ranges[l+r],h=this.ranges[l+o],d=a+c;if(t<=d){let u=c?t==a?-1:t==d?1:e:e,f=a+s+(u<0?0:h);if(i)return f;let p=t==(e<0?a:d)?null:Od(l/3,t-a),m=t==a?Ya:t==d?Ga:Mi;return(e<0?t!=a:t!=d)&&(m|=Xa),new Js(f,m,p)}s+=h-c}return i?t+s:new Js(t+s,0,null)}touches(t,e){let i=0,s=uo(e),r=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>t)break;let c=this.ranges[l+r],h=a+c;if(t<=h&&l==s*3)return!0;i+=this.ranges[l+o]-c}return!1}forEach(t){let e=this.inverted?2:1,i=this.inverted?1:2;for(let s=0,r=0;s<this.ranges.length;s+=3){let o=this.ranges[s],l=o-(this.inverted?r:0),a=o+(this.inverted?0:r),c=this.ranges[s+e],h=this.ranges[s+i];t(l,l+c,a,a+h),r+=h-c}}invert(){return new bt(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return t==0?bt.empty:new bt(t<0?[0,-t,0]:[0,0,t])}}bt.empty=new bt([]);class Bn{constructor(t,e,i=0,s=t?t.length:0){this.mirror=e,this.from=i,this.to=s,this._maps=t||[],this.ownData=!(t||e)}get maps(){return this._maps}slice(t=0,e=this.maps.length){return new Bn(this._maps,this.mirror,t,e)}appendMap(t,e){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(t),e!=null&&this.setMirror(this._maps.length-1,e)}appendMapping(t){for(let e=0,i=this._maps.length;e<t._maps.length;e++){let s=t.getMirror(e);this.appendMap(t._maps[e],s!=null&&s<e?i+s:void 0)}}getMirror(t){if(this.mirror){for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,i=this._maps.length+t._maps.length;e>=0;e--){let s=t.getMirror(e);this.appendMap(t._maps[e].invert(),s!=null&&s>e?i-s-1:void 0)}}invert(){let t=new Bn;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let i=this.from;i<this.to;i++)t=this._maps[i].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,i){let s=0;for(let r=this.from;r<this.to;r++){let o=this._maps[r],l=o.mapResult(t,e);if(l.recover!=null){let a=this.getMirror(r);if(a!=null&&a>r&&a<this.to){r=a,t=this._maps[a].recover(l.recover);continue}}s|=l.delInfo,t=l.pos}return i?t:new Js(t,s,null)}}const ps=Object.create(null);class ot{getMap(){return bt.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw new RangeError("Invalid input for Step.fromJSON");let i=ps[e.stepType];if(!i)throw new RangeError(`No step type ${e.stepType} defined`);return i.fromJSON(t,e)}static jsonID(t,e){if(t in ps)throw new RangeError("Duplicate use of step JSON ID "+t);return ps[t]=e,e.prototype.jsonID=t,e}}class q{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new q(t,null)}static fail(t){return new q(null,t)}static fromReplace(t,e,i,s){try{return q.ok(t.replace(e,i,s))}catch(r){if(r instanceof vi)return q.fail(r.message);throw r}}}function br(n,t,e){let i=[];for(let s=0;s<n.childCount;s++){let r=n.child(s);r.content.size&&(r=r.copy(br(r.content,t,r))),r.isInline&&(r=t(r,e,s)),i.push(r)}return S.fromArray(i)}class ee extends ot{constructor(t,e,i){super(),this.from=t,this.to=e,this.mark=i}apply(t){let e=t.slice(this.from,this.to),i=t.resolve(this.from),s=i.node(i.sharedDepth(this.to)),r=new T(br(e.content,(o,l)=>!o.isAtom||!l.type.allowsMarkType(this.mark.type)?o:o.mark(this.mark.addToSet(o.marks)),s),e.openStart,e.openEnd);return q.fromReplace(t,this.from,this.to,r)}invert(){return new zt(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),i=t.mapResult(this.to,-1);return e.deleted&&i.deleted||e.pos>=i.pos?null:new ee(e.pos,i.pos,this.mark)}merge(t){return t instanceof ee&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new ee(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if(typeof e.from!="number"||typeof e.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new ee(e.from,e.to,t.markFromJSON(e.mark))}}ot.jsonID("addMark",ee);class zt extends ot{constructor(t,e,i){super(),this.from=t,this.to=e,this.mark=i}apply(t){let e=t.slice(this.from,this.to),i=new T(br(e.content,s=>s.mark(this.mark.removeFromSet(s.marks)),t),e.openStart,e.openEnd);return q.fromReplace(t,this.from,this.to,i)}invert(){return new ee(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),i=t.mapResult(this.to,-1);return e.deleted&&i.deleted||e.pos>=i.pos?null:new zt(e.pos,i.pos,this.mark)}merge(t){return t instanceof zt&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new zt(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if(typeof e.from!="number"||typeof e.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new zt(e.from,e.to,t.markFromJSON(e.mark))}}ot.jsonID("removeMark",zt);class ne extends ot{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return q.fail("No node at mark step's position");let i=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return q.fromReplace(t,this.pos,this.pos+1,new T(S.from(i),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);if(e){let i=this.mark.addToSet(e.marks);if(i.length==e.marks.length){for(let s=0;s<e.marks.length;s++)if(!e.marks[s].isInSet(i))return new ne(this.pos,e.marks[s]);return new ne(this.pos,this.mark)}}return new Be(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new ne(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if(typeof e.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new ne(e.pos,t.markFromJSON(e.mark))}}ot.jsonID("addNodeMark",ne);class Be extends ot{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return q.fail("No node at mark step's position");let i=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return q.fromReplace(t,this.pos,this.pos+1,new T(S.from(i),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);return!e||!this.mark.isInSet(e.marks)?this:new ne(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new Be(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if(typeof e.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new Be(e.pos,t.markFromJSON(e.mark))}}ot.jsonID("removeNodeMark",Be);class G extends ot{constructor(t,e,i,s=!1){super(),this.from=t,this.to=e,this.slice=i,this.structure=s}apply(t){return this.structure&&Us(t,this.from,this.to)?q.fail("Structure replace would overwrite content"):q.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new bt([this.from,this.to-this.from,this.slice.size])}invert(t){return new G(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),i=t.mapResult(this.to,-1);return e.deletedAcross&&i.deletedAcross?null:new G(e.pos,Math.max(e.pos,i.pos),this.slice,this.structure)}merge(t){if(!(t instanceof G)||t.structure||this.structure)return null;if(this.from+this.slice.size==t.from&&!this.slice.openEnd&&!t.slice.openStart){let e=this.slice.size+t.slice.size==0?T.empty:new T(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new G(this.from,this.to+(t.to-t.from),e,this.structure)}else if(t.to==this.from&&!this.slice.openStart&&!t.slice.openEnd){let e=this.slice.size+t.slice.size==0?T.empty:new T(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new G(t.from,this.to,e,this.structure)}else return null}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if(typeof e.from!="number"||typeof e.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new G(e.from,e.to,T.fromJSON(t,e.slice),!!e.structure)}}ot.jsonID("replace",G);class Y extends ot{constructor(t,e,i,s,r,o,l=!1){super(),this.from=t,this.to=e,this.gapFrom=i,this.gapTo=s,this.slice=r,this.insert=o,this.structure=l}apply(t){if(this.structure&&(Us(t,this.from,this.gapFrom)||Us(t,this.gapTo,this.to)))return q.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return q.fail("Gap is not a flat range");let i=this.slice.insertAt(this.insert,e.content);return i?q.fromReplace(t,this.from,this.to,i):q.fail("Content does not fit in gap")}getMap(){return new bt([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new Y(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),i=t.mapResult(this.to,-1),s=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),r=this.to==this.gapTo?i.pos:t.map(this.gapTo,1);return e.deletedAcross&&i.deletedAcross||s<e.pos||r>i.pos?null:new Y(e.pos,i.pos,s,r,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if(typeof e.from!="number"||typeof e.to!="number"||typeof e.gapFrom!="number"||typeof e.gapTo!="number"||typeof e.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new Y(e.from,e.to,e.gapFrom,e.gapTo,T.fromJSON(t,e.slice),e.insert,!!e.structure)}}ot.jsonID("replaceAround",Y);function Us(n,t,e){let i=n.resolve(t),s=e-t,r=i.depth;for(;s>0&&r>0&&i.indexAfter(r)==i.node(r).childCount;)r--,s--;if(s>0){let o=i.node(r).maybeChild(i.indexAfter(r));for(;s>0;){if(!o||o.isLeaf)return!0;o=o.firstChild,s--}}return!1}function Dd(n,t,e,i){let s=[],r=[],o,l;n.doc.nodesBetween(t,e,(a,c,h)=>{if(!a.isInline)return;let d=a.marks;if(!i.isInSet(d)&&h.type.allowsMarkType(i.type)){let u=Math.max(c,t),f=Math.min(c+a.nodeSize,e),p=i.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(o&&o.to==u&&o.mark.eq(d[m])?o.to=f:s.push(o=new zt(u,f,d[m])));l&&l.to==u?l.to=f:r.push(l=new ee(u,f,i))}}),s.forEach(a=>n.step(a)),r.forEach(a=>n.step(a))}function Ad(n,t,e,i){let s=[],r=0;n.doc.nodesBetween(t,e,(o,l)=>{if(!o.isInline)return;r++;let a=null;if(i instanceof qi){let c=o.marks,h;for(;h=i.isInSet(c);)(a||(a=[])).push(h),c=h.removeFromSet(c)}else i?i.isInSet(o.marks)&&(a=[i]):a=o.marks;if(a&&a.length){let c=Math.min(l+o.nodeSize,e);for(let h=0;h<a.length;h++){let d=a[h],u;for(let f=0;f<s.length;f++){let p=s[f];p.step==r-1&&d.eq(s[f].style)&&(u=p)}u?(u.to=c,u.step=r):s.push({style:d,from:Math.max(l,t),to:c,step:r})}}}),s.forEach(o=>n.step(new zt(o.from,o.to,o.style)))}function xr(n,t,e,i=e.contentMatch,s=!0){let r=n.doc.nodeAt(t),o=[],l=t+1;for(let a=0;a<r.childCount;a++){let c=r.child(a),h=l+c.nodeSize,d=i.matchType(c.type);if(!d)o.push(new G(l,h,T.empty));else{i=d;for(let u=0;u<c.marks.length;u++)e.allowsMarkType(c.marks[u].type)||n.step(new zt(l,h,c.marks[u]));if(s&&c.isText&&e.whitespace!="pre"){let u,f=/\r?\n|\r/g,p;for(;u=f.exec(c.text);)p||(p=new T(S.from(e.schema.text(" ",e.allowedMarks(c.marks))),0,0)),o.push(new G(l+u.index,l+u.index+u[0].length,p))}}l=h}if(!i.validEnd){let a=i.fillBefore(S.empty,!0);n.replace(l,l,new T(a,0,0))}for(let a=o.length-1;a>=0;a--)n.step(o[a])}function Ed(n,t,e){return(t==0||n.canReplace(t,n.childCount))&&(e==n.childCount||n.canReplace(0,e))}function dn(n){let e=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let i=n.depth;;--i){let s=n.$from.node(i),r=n.$from.index(i),o=n.$to.indexAfter(i);if(i<n.depth&&s.canReplace(r,o,e))return i;if(i==0||s.type.spec.isolating||!Ed(s,r,o))break}return null}function Nd(n,t,e){let{$from:i,$to:s,depth:r}=t,o=i.before(r+1),l=s.after(r+1),a=o,c=l,h=S.empty,d=0;for(let p=r,m=!1;p>e;p--)m||i.index(p)>0?(m=!0,h=S.from(i.node(p).copy(h)),d++):a--;let u=S.empty,f=0;for(let p=r,m=!1;p>e;p--)m||s.after(p+1)<s.end(p)?(m=!0,u=S.from(s.node(p).copy(u)),f++):c++;n.step(new Y(a,c,o,l,new T(h.append(u),d,f),h.size-d,!0))}function kr(n,t,e=null,i=n){let s=Pd(n,t),r=s&&Id(i,t);return r?s.map(fo).concat({type:t,attrs:e}).concat(r.map(fo)):null}function fo(n){return{type:n,attrs:null}}function Pd(n,t){let{parent:e,startIndex:i,endIndex:s}=n,r=e.contentMatchAt(i).findWrapping(t);if(!r)return null;let o=r.length?r[0]:t;return e.canReplaceWith(i,s,o)?r:null}function Id(n,t){let{parent:e,startIndex:i,endIndex:s}=n,r=e.child(i),o=t.contentMatch.findWrapping(r.type);if(!o)return null;let a=(o.length?o[o.length-1]:t).contentMatch;for(let c=i;a&&c<s;c++)a=a.matchType(e.child(c).type);return!a||!a.validEnd?null:o}function Rd(n,t,e){let i=S.empty;for(let o=e.length-1;o>=0;o--){if(i.size){let l=e[o].type.contentMatch.matchFragment(i);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=S.from(e[o].type.create(e[o].attrs,i))}let s=t.start,r=t.end;n.step(new Y(s,r,s,r,new T(i,0,0),e.length,!0))}function Ld(n,t,e,i,s){if(!i.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let r=n.steps.length;n.doc.nodesBetween(t,e,(o,l)=>{let a=typeof s=="function"?s(o):s;if(o.isTextblock&&!o.hasMarkup(i,a)&&Bd(n.doc,n.mapping.slice(r).map(l),i)){let c=null;if(i.schema.linebreakReplacement){let f=i.whitespace=="pre",p=!!i.contentMatch.matchType(i.schema.linebreakReplacement);f&&!p?c=!1:!f&&p&&(c=!0)}c===!1&&Za(n,o,l,r),xr(n,n.mapping.slice(r).map(l,1),i,void 0,c===null);let h=n.mapping.slice(r),d=h.map(l,1),u=h.map(l+o.nodeSize,1);return n.step(new Y(d,u,d+1,u-1,new T(S.from(i.create(a,null,o.marks)),0,0),1,!0)),c===!0&&Qa(n,o,l,r),!1}})}function Qa(n,t,e,i){t.forEach((s,r)=>{if(s.isText){let o,l=/\r?\n|\r/g;for(;o=l.exec(s.text);){let a=n.mapping.slice(i).map(e+1+r+o.index);n.replaceWith(a,a+1,t.type.schema.linebreakReplacement.create())}}})}function Za(n,t,e,i){t.forEach((s,r)=>{if(s.type==s.type.schema.linebreakReplacement){let o=n.mapping.slice(i).map(e+1+r);n.replaceWith(o,o+1,t.type.schema.text(`
`))}})}function Bd(n,t,e){let i=n.resolve(t),s=i.index();return i.parent.canReplaceWith(s,s+1,e)}function zd(n,t,e,i,s){let r=n.doc.nodeAt(t);if(!r)throw new RangeError("No node at given position");e||(e=r.type);let o=e.create(i,null,s||r.marks);if(r.isLeaf)return n.replaceWith(t,t+r.nodeSize,o);if(!e.validContent(r.content))throw new RangeError("Invalid content for node type "+e.name);n.step(new Y(t,t+r.nodeSize,t+1,t+r.nodeSize-1,new T(S.from(o),0,0),1,!0))}function Ut(n,t,e=1,i){let s=n.resolve(t),r=s.depth-e,o=i&&i[i.length-1]||s.parent;if(r<0||s.parent.type.spec.isolating||!s.parent.canReplace(s.index(),s.parent.childCount)||!o.type.validContent(s.parent.content.cutByIndex(s.index(),s.parent.childCount)))return!1;for(let c=s.depth-1,h=e-2;c>r;c--,h--){let d=s.node(c),u=s.index(c);if(d.type.spec.isolating)return!1;let f=d.content.cutByIndex(u,d.childCount),p=i&&i[h+1];p&&(f=f.replaceChild(0,p.type.create(p.attrs)));let m=i&&i[h]||d;if(!d.canReplace(u+1,d.childCount)||!m.type.validContent(f))return!1}let l=s.indexAfter(r),a=i&&i[0];return s.node(r).canReplaceWith(l,l,a?a.type:s.node(r+1).type)}function Fd(n,t,e=1,i){let s=n.doc.resolve(t),r=S.empty,o=S.empty;for(let l=s.depth,a=s.depth-e,c=e-1;l>a;l--,c--){r=S.from(s.node(l).copy(r));let h=i&&i[c];o=S.from(h?h.type.create(h.attrs,o):s.node(l).copy(o))}n.step(new G(t,t,new T(r.append(o),e,e),!0))}function de(n,t){let e=n.resolve(t),i=e.index();return tc(e.nodeBefore,e.nodeAfter)&&e.parent.canReplace(i,i+1)}function Vd(n,t){t.content.size||n.type.compatibleContent(t.type);let e=n.contentMatchAt(n.childCount),{linebreakReplacement:i}=n.type.schema;for(let s=0;s<t.childCount;s++){let r=t.child(s),o=r.type==i?n.type.schema.nodes.text:r.type;if(e=e.matchType(o),!e||!n.type.allowsMarks(r.marks))return!1}return e.validEnd}function tc(n,t){return!!(n&&t&&!n.isLeaf&&Vd(n,t))}function Ji(n,t,e=-1){let i=n.resolve(t);for(let s=i.depth;;s--){let r,o,l=i.index(s);if(s==i.depth?(r=i.nodeBefore,o=i.nodeAfter):e>0?(r=i.node(s+1),l++,o=i.node(s).maybeChild(l)):(r=i.node(s).maybeChild(l-1),o=i.node(s+1)),r&&!r.isTextblock&&tc(r,o)&&i.node(s).canReplace(l,l+1))return t;if(s==0)break;t=e<0?i.before(s):i.after(s)}}function Hd(n,t,e){let i=null,{linebreakReplacement:s}=n.doc.type.schema,r=n.doc.resolve(t-e),o=r.node().type;if(s&&o.inlineContent){let h=o.whitespace=="pre",d=!!o.contentMatch.matchType(s);h&&!d?i=!1:!h&&d&&(i=!0)}let l=n.steps.length;if(i===!1){let h=n.doc.resolve(t+e);Za(n,h.node(),h.before(),l)}o.inlineContent&&xr(n,t+e-1,o,r.node().contentMatchAt(r.index()),i==null);let a=n.mapping.slice(l),c=a.map(t-e);if(n.step(new G(c,a.map(t+e,-1),T.empty,!0)),i===!0){let h=n.doc.resolve(c);Qa(n,h.node(),h.before(),n.steps.length)}return n}function $d(n,t,e){let i=n.resolve(t);if(i.parent.canReplaceWith(i.index(),i.index(),e))return t;if(i.parentOffset==0)for(let s=i.depth-1;s>=0;s--){let r=i.index(s);if(i.node(s).canReplaceWith(r,r,e))return i.before(s+1);if(r>0)return null}if(i.parentOffset==i.parent.content.size)for(let s=i.depth-1;s>=0;s--){let r=i.indexAfter(s);if(i.node(s).canReplaceWith(r,r,e))return i.after(s+1);if(r<i.node(s).childCount)return null}return null}function ec(n,t,e){let i=n.resolve(t);if(!e.content.size)return t;let s=e.content;for(let r=0;r<e.openStart;r++)s=s.firstChild.content;for(let r=1;r<=(e.openStart==0&&e.size?2:1);r++)for(let o=i.depth;o>=0;o--){let l=o==i.depth?0:i.pos<=(i.start(o+1)+i.end(o+1))/2?-1:1,a=i.index(o)+(l>0?1:0),c=i.node(o),h=!1;if(r==1)h=c.canReplace(a,a,s);else{let d=c.contentMatchAt(a).findWrapping(s.firstChild.type);h=d&&c.canReplaceWith(a,a,d[0])}if(h)return l==0?i.pos:l<0?i.before(o+1):i.after(o+1)}return null}function Ui(n,t,e=t,i=T.empty){if(t==e&&!i.size)return null;let s=n.resolve(t),r=n.resolve(e);return nc(s,r,i)?new G(t,e,i):new Wd(s,r,i).fit()}function nc(n,t,e){return!e.openStart&&!e.openEnd&&n.start()==t.start()&&n.parent.canReplace(n.index(),t.index(),e.content)}class Wd{constructor(t,e,i){this.$from=t,this.$to=e,this.unplaced=i,this.frontier=[],this.placed=S.empty;for(let s=0;s<=t.depth;s++){let r=t.node(s);this.frontier.push({type:r.type,match:r.contentMatchAt(t.indexAfter(s))})}for(let s=t.depth;s>0;s--)this.placed=S.from(t.node(s).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,i=this.$from,s=this.close(t<0?this.$to:i.doc.resolve(t));if(!s)return null;let r=this.placed,o=i.depth,l=s.depth;for(;o&&l&&r.childCount==1;)r=r.firstChild.content,o--,l--;let a=new T(r,o,l);return t>-1?new Y(i.pos,t,this.$to.pos,this.$to.end(),a,e):a.size||i.pos!=this.$to.pos?new G(i.pos,s.pos,a):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,i=0,s=this.unplaced.openEnd;i<t;i++){let r=e.firstChild;if(e.childCount>1&&(s=0),r.type.spec.isolating&&s<=i){t=i;break}e=r.content}for(let e=1;e<=2;e++)for(let i=e==1?t:this.unplaced.openStart;i>=0;i--){let s,r=null;i?(r=ms(this.unplaced.content,i-1).firstChild,s=r.content):s=this.unplaced.content;let o=s.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],h,d=null;if(e==1&&(o?c.matchType(o.type)||(d=c.fillBefore(S.from(o),!1)):r&&a.compatibleContent(r.type)))return{sliceDepth:i,frontierDepth:l,parent:r,inject:d};if(e==2&&o&&(h=c.findWrapping(o.type)))return{sliceDepth:i,frontierDepth:l,parent:r,wrap:h};if(r&&c.matchType(r.type))break}}}openMore(){let{content:t,openStart:e,openEnd:i}=this.unplaced,s=ms(t,e);return!s.childCount||s.firstChild.isLeaf?!1:(this.unplaced=new T(t,e+1,Math.max(i,s.size+e>=t.size-i?e+1:0)),!0)}dropNode(){let{content:t,openStart:e,openEnd:i}=this.unplaced,s=ms(t,e);if(s.childCount<=1&&e>0){let r=t.size-e<=e+s.size;this.unplaced=new T(Sn(t,e-1,1),e-1,r?e-1:i)}else this.unplaced=new T(Sn(t,e,1),e,i)}placeNodes({sliceDepth:t,frontierDepth:e,parent:i,inject:s,wrap:r}){for(;this.depth>e;)this.closeFrontierNode();if(r)for(let m=0;m<r.length;m++)this.openFrontierNode(r[m]);let o=this.unplaced,l=i?i.content:o.content,a=o.openStart-t,c=0,h=[],{match:d,type:u}=this.frontier[e];if(s){for(let m=0;m<s.childCount;m++)h.push(s.child(m));d=d.matchFragment(s)}let f=l.size+t-(o.content.size-o.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,h.push(ic(m.mark(u.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?f:-1)))}let p=c==l.childCount;p||(f=-1),this.placed=Mn(this.placed,e,S.from(h)),this.frontier[e].match=d,p&&f<0&&i&&i.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<f;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?t==0?T.empty:new T(Sn(o.content,t-1,1),t-1,f<0?o.openEnd:t-1):new T(Sn(o.content,t,c),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let t=this.frontier[this.depth],e;if(!t.type.isTextblock||!gs(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return-1;let{depth:i}=this.$to,s=this.$to.after(i);for(;i>1&&s==this.$to.end(--i);)++s;return s}findCloseLevel(t){t:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:i,type:s}=this.frontier[e],r=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),o=gs(t,e,s,i,r);if(o){for(let l=e-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],h=gs(t,l,c,a,!0);if(!h||h.childCount)continue t}return{depth:e,fit:o,move:r?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=Mn(this.placed,e.depth,e.fit)),t=e.move;for(let i=e.depth+1;i<=t.depth;i++){let s=t.node(i),r=s.type.contentMatch.fillBefore(s.content,!0,t.index(i));this.openFrontierNode(s.type,s.attrs,r)}return t}openFrontierNode(t,e=null,i){let s=this.frontier[this.depth];s.match=s.match.matchType(t),this.placed=Mn(this.placed,this.depth,S.from(t.create(e,i))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(S.empty,!0);e.childCount&&(this.placed=Mn(this.placed,this.frontier.length,e))}}function Sn(n,t,e){return t==0?n.cutByIndex(e,n.childCount):n.replaceChild(0,n.firstChild.copy(Sn(n.firstChild.content,t-1,e)))}function Mn(n,t,e){return t==0?n.append(e):n.replaceChild(n.childCount-1,n.lastChild.copy(Mn(n.lastChild.content,t-1,e)))}function ms(n,t){for(let e=0;e<t;e++)n=n.firstChild.content;return n}function ic(n,t,e){if(t<=0)return n;let i=n.content;return t>1&&(i=i.replaceChild(0,ic(i.firstChild,t-1,i.childCount==1?e-1:0))),t>0&&(i=n.type.contentMatch.fillBefore(i).append(i),e<=0&&(i=i.append(n.type.contentMatch.matchFragment(i).fillBefore(S.empty,!0)))),n.copy(i)}function gs(n,t,e,i,s){let r=n.node(t),o=s?n.indexAfter(t):n.index(t);if(o==r.childCount&&!e.compatibleContent(r.type))return null;let l=i.fillBefore(r.content,!0,o);return l&&!jd(e,r.content,o)?l:null}function jd(n,t,e){for(let i=e;i<t.childCount;i++)if(!n.allowsMarks(t.child(i).marks))return!0;return!1}function Kd(n){return n.spec.defining||n.spec.definingForContent}function qd(n,t,e,i){if(!i.size)return n.deleteRange(t,e);let s=n.doc.resolve(t),r=n.doc.resolve(e);if(nc(s,r,i))return n.step(new G(t,e,i));let o=rc(s,n.doc.resolve(e));o[o.length-1]==0&&o.pop();let l=-(s.depth+1);o.unshift(l);for(let u=s.depth,f=s.pos-1;u>0;u--,f--){let p=s.node(u).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;o.indexOf(u)>-1?l=u:s.before(u)==f&&o.splice(1,0,-u)}let a=o.indexOf(l),c=[],h=i.openStart;for(let u=i.content,f=0;;f++){let p=u.firstChild;if(c.push(p),f==i.openStart)break;u=p.content}for(let u=h-1;u>=0;u--){let f=c[u],p=Kd(f.type);if(p&&!f.sameMarkup(s.node(Math.abs(l)-1)))h=u;else if(p||!f.type.isTextblock)break}for(let u=i.openStart;u>=0;u--){let f=(u+h+1)%(i.openStart+1),p=c[f];if(p)for(let m=0;m<o.length;m++){let g=o[(m+a)%o.length],y=!0;g<0&&(y=!1,g=-g);let b=s.node(g-1),M=s.index(g-1);if(b.canReplaceWith(M,M,p.type,p.marks))return n.replace(s.before(g),y?r.after(g):e,new T(sc(i.content,0,i.openStart,f),f,i.openEnd))}}let d=n.steps.length;for(let u=o.length-1;u>=0&&(n.replace(t,e,i),!(n.steps.length>d));u--){let f=o[u];f<0||(t=s.before(f),e=r.after(f))}}function sc(n,t,e,i,s){if(t<e){let r=n.firstChild;n=n.replaceChild(0,r.copy(sc(r.content,t+1,e,i,r)))}if(t>i){let r=s.contentMatchAt(0),o=r.fillBefore(n).append(n);n=o.append(r.matchFragment(o).fillBefore(S.empty,!0))}return n}function Jd(n,t,e,i){if(!i.isInline&&t==e&&n.doc.resolve(t).parent.content.size){let s=$d(n.doc,t,i.type);s!=null&&(t=e=s)}n.replaceRange(t,e,new T(S.from(i),0,0))}function Ud(n,t,e){let i=n.doc.resolve(t),s=n.doc.resolve(e),r=rc(i,s);for(let o=0;o<r.length;o++){let l=r[o],a=o==r.length-1;if(a&&l==0||i.node(l).type.contentMatch.validEnd)return n.delete(i.start(l),s.end(l));if(l>0&&(a||i.node(l-1).canReplace(i.index(l-1),s.indexAfter(l-1))))return n.delete(i.before(l),s.after(l))}for(let o=1;o<=i.depth&&o<=s.depth;o++)if(t-i.start(o)==i.depth-o&&e>i.end(o)&&s.end(o)-e!=s.depth-o&&i.start(o-1)==s.start(o-1)&&i.node(o-1).canReplace(i.index(o-1),s.index(o-1)))return n.delete(i.before(o),e);n.delete(t,e)}function rc(n,t){let e=[],i=Math.min(n.depth,t.depth);for(let s=i;s>=0;s--){let r=n.start(s);if(r<n.pos-(n.depth-s)||t.end(s)>t.pos+(t.depth-s)||n.node(s).type.spec.isolating||t.node(s).type.spec.isolating)break;(r==t.start(s)||s==n.depth&&s==t.depth&&n.parent.inlineContent&&t.parent.inlineContent&&s&&t.start(s-1)==r-1)&&e.push(s)}return e}class Qe extends ot{constructor(t,e,i){super(),this.pos=t,this.attr=e,this.value=i}apply(t){let e=t.nodeAt(this.pos);if(!e)return q.fail("No node at attribute step's position");let i=Object.create(null);for(let r in e.attrs)i[r]=e.attrs[r];i[this.attr]=this.value;let s=e.type.create(i,null,e.marks);return q.fromReplace(t,this.pos,this.pos+1,new T(S.from(s),0,e.isLeaf?0:1))}getMap(){return bt.empty}invert(t){return new Qe(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new Qe(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if(typeof e.pos!="number"||typeof e.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new Qe(e.pos,e.attr,e.value)}}ot.jsonID("attr",Qe);class zn extends ot{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let s in t.attrs)e[s]=t.attrs[s];e[this.attr]=this.value;let i=t.type.create(e,t.content,t.marks);return q.ok(i)}getMap(){return bt.empty}invert(t){return new zn(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if(typeof e.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new zn(e.attr,e.value)}}ot.jsonID("docAttr",zn);let en=class extends Error{};en=function n(t){let e=Error.call(this,t);return e.__proto__=n.prototype,e};en.prototype=Object.create(Error.prototype);en.prototype.constructor=en;en.prototype.name="TransformError";class Gd{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new Bn}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new en(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,i=T.empty){let s=Ui(this.doc,t,e,i);return s&&this.step(s),this}replaceWith(t,e,i){return this.replace(t,e,new T(S.from(i),0,0))}delete(t,e){return this.replace(t,e,T.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,i){return qd(this,t,e,i),this}replaceRangeWith(t,e,i){return Jd(this,t,e,i),this}deleteRange(t,e){return Ud(this,t,e),this}lift(t,e){return Nd(this,t,e),this}join(t,e=1){return Hd(this,t,e),this}wrap(t,e){return Rd(this,t,e),this}setBlockType(t,e=t,i,s=null){return Ld(this,t,e,i,s),this}setNodeMarkup(t,e,i=null,s){return zd(this,t,e,i,s),this}setNodeAttribute(t,e,i){return this.step(new Qe(t,e,i)),this}setDocAttribute(t,e){return this.step(new zn(t,e)),this}addNodeMark(t,e){return this.step(new ne(t,e)),this}removeNodeMark(t,e){let i=this.doc.nodeAt(t);if(!i)throw new RangeError("No node at position "+t);if(e instanceof V)e.isInSet(i.marks)&&this.step(new Be(t,e));else{let s=i.marks,r,o=[];for(;r=e.isInSet(s);)o.push(new Be(t,r)),s=r.removeFromSet(s);for(let l=o.length-1;l>=0;l--)this.step(o[l])}return this}split(t,e=1,i){return Fd(this,t,e,i),this}addMark(t,e,i){return Dd(this,t,e,i),this}removeMark(t,e,i){return Ad(this,t,e,i),this}clearIncompatible(t,e,i){return xr(this,t,e,i),this}}const ys=Object.create(null);class N{constructor(t,e,i){this.$anchor=t,this.$head=e,this.ranges=i||[new Yd(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=T.empty){let i=e.content.lastChild,s=null;for(let l=0;l<e.openEnd;l++)s=i,i=i.lastChild;let r=t.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:c}=o[l],h=t.mapping.slice(r);t.replaceRange(h.map(a.pos),h.map(c.pos),l?T.empty:e),l==0&&go(t,r,(i?i.isInline:s&&s.isTextblock)?-1:1)}}replaceWith(t,e){let i=t.steps.length,s=this.ranges;for(let r=0;r<s.length;r++){let{$from:o,$to:l}=s[r],a=t.mapping.slice(i),c=a.map(o.pos),h=a.map(l.pos);r?t.deleteRange(c,h):(t.replaceRangeWith(c,h,e),go(t,i,e.isInline?-1:1))}}static findFrom(t,e,i=!1){let s=t.parent.inlineContent?new E(t):Je(t.node(0),t.parent,t.pos,t.index(),e,i);if(s)return s;for(let r=t.depth-1;r>=0;r--){let o=e<0?Je(t.node(0),t.node(r),t.before(r+1),t.index(r),e,i):Je(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,i);if(o)return o}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new xt(t.node(0))}static atStart(t){return Je(t,t,0,0,1)||new xt(t)}static atEnd(t){return Je(t,t,t.content.size,t.childCount,-1)||new xt(t)}static fromJSON(t,e){if(!e||!e.type)throw new RangeError("Invalid input for Selection.fromJSON");let i=ys[e.type];if(!i)throw new RangeError(`No selection type ${e.type} defined`);return i.fromJSON(t,e)}static jsonID(t,e){if(t in ys)throw new RangeError("Duplicate use of selection JSON ID "+t);return ys[t]=e,e.prototype.jsonID=t,e}getBookmark(){return E.between(this.$anchor,this.$head).getBookmark()}}N.prototype.visible=!0;class Yd{constructor(t,e){this.$from=t,this.$to=e}}let po=!1;function mo(n){!po&&!n.parent.inlineContent&&(po=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+n.parent.type.name+")"))}class E extends N{constructor(t,e=t){mo(t),mo(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let i=t.resolve(e.map(this.head));if(!i.parent.inlineContent)return N.near(i);let s=t.resolve(e.map(this.anchor));return new E(s.parent.inlineContent?s:i,i)}replace(t,e=T.empty){if(super.replace(t,e),e==T.empty){let i=this.$from.marksAcross(this.$to);i&&t.ensureMarks(i)}}eq(t){return t instanceof E&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new Gi(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if(typeof e.anchor!="number"||typeof e.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new E(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,i=e){let s=t.resolve(e);return new this(s,i==e?s:t.resolve(i))}static between(t,e,i){let s=t.pos-e.pos;if((!i||s)&&(i=s>=0?1:-1),!e.parent.inlineContent){let r=N.findFrom(e,i,!0)||N.findFrom(e,-i,!0);if(r)e=r.$head;else return N.near(e,i)}return t.parent.inlineContent||(s==0?t=e:(t=(N.findFrom(t,-i,!0)||N.findFrom(t,i,!0)).$anchor,t.pos<e.pos!=s<0&&(t=e))),new E(t,e)}}N.jsonID("text",E);class Gi{constructor(t,e){this.anchor=t,this.head=e}map(t){return new Gi(t.map(this.anchor),t.map(this.head))}resolve(t){return E.between(t.resolve(this.anchor),t.resolve(this.head))}}class D extends N{constructor(t){let e=t.nodeAfter,i=t.node(0).resolve(t.pos+e.nodeSize);super(t,i),this.node=e}map(t,e){let{deleted:i,pos:s}=e.mapResult(this.anchor),r=t.resolve(s);return i?N.near(r):new D(r)}content(){return new T(S.from(this.node),0,0)}eq(t){return t instanceof D&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new Sr(this.anchor)}static fromJSON(t,e){if(typeof e.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new D(t.resolve(e.anchor))}static create(t,e){return new D(t.resolve(e))}static isSelectable(t){return!t.isText&&t.type.spec.selectable!==!1}}D.prototype.visible=!1;N.jsonID("node",D);class Sr{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:i}=t.mapResult(this.anchor);return e?new Gi(i,i):new Sr(i)}resolve(t){let e=t.resolve(this.anchor),i=e.nodeAfter;return i&&D.isSelectable(i)?new D(e):N.near(e)}}class xt extends N{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=T.empty){if(e==T.empty){t.delete(0,t.doc.content.size);let i=N.atStart(t.doc);i.eq(t.selection)||t.setSelection(i)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new xt(t)}map(t){return new xt(t)}eq(t){return t instanceof xt}getBookmark(){return Xd}}N.jsonID("all",xt);const Xd={map(){return this},resolve(n){return new xt(n)}};function Je(n,t,e,i,s,r=!1){if(t.inlineContent)return E.create(n,e);for(let o=i-(s>0?0:1);s>0?o<t.childCount:o>=0;o+=s){let l=t.child(o);if(l.isAtom){if(!r&&D.isSelectable(l))return D.create(n,e-(s<0?l.nodeSize:0))}else{let a=Je(n,l,e+s,s<0?l.childCount:0,s,r);if(a)return a}e+=l.nodeSize*s}return null}function go(n,t,e){let i=n.steps.length-1;if(i<t)return;let s=n.steps[i];if(!(s instanceof G||s instanceof Y))return;let r=n.mapping.maps[i],o;r.forEach((l,a,c,h)=>{o==null&&(o=h)}),n.setSelection(N.near(n.doc.resolve(o),e))}const yo=1,bo=2,xo=4;class Qd extends Gd{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=(this.updated|yo)&-3,this.storedMarks=null,this}get selectionSet(){return(this.updated&yo)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=bo,this}ensureMarks(t){return V.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&bo)>0}addStep(t,e){super.addStep(t,e),this.updated=this.updated&-3,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let i=this.selection;return e&&(t=t.mark(this.storedMarks||(i.empty?i.$from.marks():i.$from.marksAcross(i.$to)||V.none))),i.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,i){let s=this.doc.type.schema;if(e==null)return t?this.replaceSelectionWith(s.text(t),!0):this.deleteSelection();{if(i==null&&(i=e),i=i??e,!t)return this.deleteRange(e,i);let r=this.storedMarks;if(!r){let o=this.doc.resolve(e);r=i==e?o.marks():o.marksAcross(this.doc.resolve(i))}return this.replaceRangeWith(e,i,s.text(t,r)),this.selection.empty||this.setSelection(N.near(this.selection.$to)),this}}setMeta(t,e){return this.meta[typeof t=="string"?t:t.key]=e,this}getMeta(t){return this.meta[typeof t=="string"?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=xo,this}get scrolledIntoView(){return(this.updated&xo)>0}}function ko(n,t){return!t||!n?n:n.bind(t)}class wn{constructor(t,e,i){this.name=t,this.init=ko(e.init,i),this.apply=ko(e.apply,i)}}const Zd=[new wn("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new wn("selection",{init(n,t){return n.selection||N.atStart(t.doc)},apply(n){return n.selection}}),new wn("storedMarks",{init(n){return n.storedMarks||null},apply(n,t,e,i){return i.selection.$cursor?n.storedMarks:null}}),new wn("scrollToSelection",{init(){return 0},apply(n,t){return n.scrolledIntoView?t+1:t}})];class bs{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Zd.slice(),e&&e.forEach(i=>{if(this.pluginsByKey[i.key])throw new RangeError("Adding different instances of a keyed plugin ("+i.key+")");this.plugins.push(i),this.pluginsByKey[i.key]=i,i.spec.state&&this.fields.push(new wn(i.key,i.spec.state,i))})}}class Ge{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let i=0;i<this.config.plugins.length;i++)if(i!=e){let s=this.config.plugins[i];if(s.spec.filterTransaction&&!s.spec.filterTransaction.call(s,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],i=this.applyInner(t),s=null;for(;;){let r=!1;for(let o=0;o<this.config.plugins.length;o++){let l=this.config.plugins[o];if(l.spec.appendTransaction){let a=s?s[o].n:0,c=s?s[o].state:this,h=a<e.length&&l.spec.appendTransaction.call(l,a?e.slice(a):e,c,i);if(h&&i.filterTransaction(h,o)){if(h.setMeta("appendedTransaction",t),!s){s=[];for(let d=0;d<this.config.plugins.length;d++)s.push(d<o?{state:i,n:e.length}:{state:this,n:0})}e.push(h),i=i.applyInner(h),r=!0}s&&(s[o]={state:i,n:e.length})}}if(!r)return{state:i,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let e=new Ge(this.config),i=this.config.fields;for(let s=0;s<i.length;s++){let r=i[s];e[r.name]=r.apply(t,this[r.name],this,e)}return e}get tr(){return new Qd(this)}static create(t){let e=new bs(t.doc?t.doc.type.schema:t.schema,t.plugins),i=new Ge(e);for(let s=0;s<e.fields.length;s++)i[e.fields[s].name]=e.fields[s].init(t,i);return i}reconfigure(t){let e=new bs(this.schema,t.plugins),i=e.fields,s=new Ge(e);for(let r=0;r<i.length;r++){let o=i[r].name;s[o]=this.hasOwnProperty(o)?this[o]:i[r].init(t,s)}return s}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map(i=>i.toJSON())),t&&typeof t=="object")for(let i in t){if(i=="doc"||i=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let s=t[i],r=s.spec.state;r&&r.toJSON&&(e[i]=r.toJSON.call(s,this[s.key]))}return e}static fromJSON(t,e,i){if(!e)throw new RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw new RangeError("Required config field 'schema' missing");let s=new bs(t.schema,t.plugins),r=new Ge(s);return s.fields.forEach(o=>{if(o.name=="doc")r.doc=se.fromJSON(t.schema,e.doc);else if(o.name=="selection")r.selection=N.fromJSON(r.doc,e.selection);else if(o.name=="storedMarks")e.storedMarks&&(r.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(i)for(let l in i){let a=i[l],c=a.spec.state;if(a.key==o.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(e,l)){r[o.name]=c.fromJSON.call(a,t,e[l],r);return}}r[o.name]=o.init(t,r)}}),r}}function oc(n,t,e){for(let i in n){let s=n[i];s instanceof Function?s=s.bind(t):i=="handleDOMEvents"&&(s=oc(s,t,{})),e[i]=s}return e}class yt{constructor(t){this.spec=t,this.props={},t.props&&oc(t.props,this,this.props),this.key=t.key?t.key.key:lc("plugin")}getState(t){return t[this.key]}}const xs=Object.create(null);function lc(n){return n in xs?n+"$"+ ++xs[n]:(xs[n]=0,n+"$")}class Ht{constructor(t="key"){this.key=lc(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}const tt=function(n){for(var t=0;;t++)if(n=n.previousSibling,!n)return t},nn=function(n){let t=n.assignedSlot||n.parentNode;return t&&t.nodeType==11?t.host:t};let Gs=null;const qt=function(n,t,e){let i=Gs||(Gs=document.createRange());return i.setEnd(n,e??n.nodeValue.length),i.setStart(n,t||0),i},tu=function(){Gs=null},ze=function(n,t,e,i){return e&&(So(n,t,e,i,-1)||So(n,t,e,i,1))},eu=/^(img|br|input|textarea|hr)$/i;function So(n,t,e,i,s){for(var r;;){if(n==e&&t==i)return!0;if(t==(s<0?0:Mt(n))){let o=n.parentNode;if(!o||o.nodeType!=1||Yn(n)||eu.test(n.nodeName)||n.contentEditable=="false")return!1;t=tt(n)+(s<0?0:1),n=o}else if(n.nodeType==1){let o=n.childNodes[t+(s<0?-1:0)];if(o.nodeType==1&&o.contentEditable=="false")if(!((r=o.pmViewDesc)===null||r===void 0)&&r.ignoreForSelection)t+=s;else return!1;else n=o,t=s<0?Mt(n):0}else return!1}}function Mt(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function nu(n,t){for(;;){if(n.nodeType==3&&t)return n;if(n.nodeType==1&&t>0){if(n.contentEditable=="false")return null;n=n.childNodes[t-1],t=Mt(n)}else if(n.parentNode&&!Yn(n))t=tt(n),n=n.parentNode;else return null}}function iu(n,t){for(;;){if(n.nodeType==3&&t<n.nodeValue.length)return n;if(n.nodeType==1&&t<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[t],t=0}else if(n.parentNode&&!Yn(n))t=tt(n)+1,n=n.parentNode;else return null}}function su(n,t,e){for(let i=t==0,s=t==Mt(n);i||s;){if(n==e)return!0;let r=tt(n);if(n=n.parentNode,!n)return!1;i=i&&r==0,s=s&&r==Mt(n)}}function Yn(n){let t;for(let e=n;e&&!(t=e.pmViewDesc);e=e.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==n||t.contentDOM==n)}const Yi=function(n){return n.focusNode&&ze(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function Me(n,t){let e=document.createEvent("Event");return e.initEvent("keydown",!0,!0),e.keyCode=n,e.key=e.code=t,e}function ru(n){let t=n.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}function ou(n,t,e){if(n.caretPositionFromPoint)try{let i=n.caretPositionFromPoint(t,e);if(i)return{node:i.offsetNode,offset:Math.min(Mt(i.offsetNode),i.offset)}}catch{}if(n.caretRangeFromPoint){let i=n.caretRangeFromPoint(t,e);if(i)return{node:i.startContainer,offset:Math.min(Mt(i.startContainer),i.startOffset)}}}const Ft=typeof navigator<"u"?navigator:null,Mo=typeof document<"u"?document:null,ue=Ft&&Ft.userAgent||"",Ys=/Edge\/(\d+)/.exec(ue),ac=/MSIE \d/.exec(ue),Xs=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ue),mt=!!(ac||Xs||Ys),oe=ac?document.documentMode:Xs?+Xs[1]:Ys?+Ys[1]:0,Et=!mt&&/gecko\/(\d+)/i.test(ue);Et&&+(/Firefox\/(\d+)/.exec(ue)||[0,0])[1];const Qs=!mt&&/Chrome\/(\d+)/.exec(ue),it=!!Qs,cc=Qs?+Qs[1]:0,lt=!mt&&!!Ft&&/Apple Computer/.test(Ft.vendor),sn=lt&&(/Mobile\/\w+/.test(ue)||!!Ft&&Ft.maxTouchPoints>2),St=sn||(Ft?/Mac/.test(Ft.platform):!1),lu=Ft?/Win/.test(Ft.platform):!1,Jt=/Android \d/.test(ue),Xn=!!Mo&&"webkitFontSmoothing"in Mo.documentElement.style,au=Xn?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function cu(n){let t=n.defaultView&&n.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function $t(n,t){return typeof n=="number"?n:n[t]}function hu(n){let t=n.getBoundingClientRect(),e=t.width/n.offsetWidth||1,i=t.height/n.offsetHeight||1;return{left:t.left,right:t.left+n.clientWidth*e,top:t.top,bottom:t.top+n.clientHeight*i}}function wo(n,t,e){let i=n.someProp("scrollThreshold")||0,s=n.someProp("scrollMargin")||5,r=n.dom.ownerDocument;for(let o=e||n.dom;o;){if(o.nodeType!=1){o=nn(o);continue}let l=o,a=l==r.body,c=a?cu(r):hu(l),h=0,d=0;if(t.top<c.top+$t(i,"top")?d=-(c.top-t.top+$t(s,"top")):t.bottom>c.bottom-$t(i,"bottom")&&(d=t.bottom-t.top>c.bottom-c.top?t.top+$t(s,"top")-c.top:t.bottom-c.bottom+$t(s,"bottom")),t.left<c.left+$t(i,"left")?h=-(c.left-t.left+$t(s,"left")):t.right>c.right-$t(i,"right")&&(h=t.right-c.right+$t(s,"right")),h||d)if(a)r.defaultView.scrollBy(h,d);else{let f=l.scrollLeft,p=l.scrollTop;d&&(l.scrollTop+=d),h&&(l.scrollLeft+=h);let m=l.scrollLeft-f,g=l.scrollTop-p;t={left:t.left-m,top:t.top-g,right:t.right-m,bottom:t.bottom-g}}let u=a?"fixed":getComputedStyle(o).position;if(/^(fixed|sticky)$/.test(u))break;o=u=="absolute"?o.offsetParent:nn(o)}}function du(n){let t=n.dom.getBoundingClientRect(),e=Math.max(0,t.top),i,s;for(let r=(t.left+t.right)/2,o=e+1;o<Math.min(innerHeight,t.bottom);o+=5){let l=n.root.elementFromPoint(r,o);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=e-20){i=l,s=a.top;break}}return{refDOM:i,refTop:s,stack:hc(n.dom)}}function hc(n){let t=[],e=n.ownerDocument;for(let i=n;i&&(t.push({dom:i,top:i.scrollTop,left:i.scrollLeft}),n!=e);i=nn(i));return t}function uu({refDOM:n,refTop:t,stack:e}){let i=n?n.getBoundingClientRect().top:0;dc(e,i==0?0:i-t)}function dc(n,t){for(let e=0;e<n.length;e++){let{dom:i,top:s,left:r}=n[e];i.scrollTop!=s+t&&(i.scrollTop=s+t),i.scrollLeft!=r&&(i.scrollLeft=r)}}let Ke=null;function fu(n){if(n.setActive)return n.setActive();if(Ke)return n.focus(Ke);let t=hc(n);n.focus(Ke==null?{get preventScroll(){return Ke={preventScroll:!0},!0}}:void 0),Ke||(Ke=!1,dc(t,0))}function uc(n,t){let e,i=2e8,s,r=0,o=t.top,l=t.top,a,c;for(let h=n.firstChild,d=0;h;h=h.nextSibling,d++){let u;if(h.nodeType==1)u=h.getClientRects();else if(h.nodeType==3)u=qt(h).getClientRects();else continue;for(let f=0;f<u.length;f++){let p=u[f];if(p.top<=o&&p.bottom>=l){o=Math.max(p.bottom,o),l=Math.min(p.top,l);let m=p.left>t.left?p.left-t.left:p.right<t.left?t.left-p.right:0;if(m<i){e=h,i=m,s=m&&e.nodeType==3?{left:p.right<t.left?p.right:p.left,top:t.top}:t,h.nodeType==1&&m&&(r=d+(t.left>=(p.left+p.right)/2?1:0));continue}}else p.top>t.top&&!a&&p.left<=t.left&&p.right>=t.left&&(a=h,c={left:Math.max(p.left,Math.min(p.right,t.left)),top:p.top});!e&&(t.left>=p.right&&t.top>=p.top||t.left>=p.left&&t.top>=p.bottom)&&(r=d+1)}}return!e&&a&&(e=a,s=c,i=0),e&&e.nodeType==3?pu(e,s):!e||i&&e.nodeType==1?{node:n,offset:r}:uc(e,s)}function pu(n,t){let e=n.nodeValue.length,i=document.createRange();for(let s=0;s<e;s++){i.setEnd(n,s+1),i.setStart(n,s);let r=Xt(i,1);if(r.top!=r.bottom&&Mr(t,r))return{node:n,offset:s+(t.left>=(r.left+r.right)/2?1:0)}}return{node:n,offset:0}}function Mr(n,t){return n.left>=t.left-1&&n.left<=t.right+1&&n.top>=t.top-1&&n.top<=t.bottom+1}function mu(n,t){let e=n.parentNode;return e&&/^li$/i.test(e.nodeName)&&t.left<n.getBoundingClientRect().left?e:n}function gu(n,t,e){let{node:i,offset:s}=uc(t,e),r=-1;if(i.nodeType==1&&!i.firstChild){let o=i.getBoundingClientRect();r=o.left!=o.right&&e.left>(o.left+o.right)/2?1:-1}return n.docView.posFromDOM(i,s,r)}function yu(n,t,e,i){let s=-1;for(let r=t,o=!1;r!=n.dom;){let l=n.docView.nearestDesc(r,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!o&&a.left>i.left||a.top>i.top?s=l.posBefore:(!o&&a.right<i.left||a.bottom<i.top)&&(s=l.posAfter),o=!0),!l.contentDOM&&s<0&&!l.node.isText))return(l.node.isBlock?i.top<(a.top+a.bottom)/2:i.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;r=l.dom.parentNode}return s>-1?s:n.docView.posFromDOM(t,e,-1)}function fc(n,t,e){let i=n.childNodes.length;if(i&&e.top<e.bottom)for(let s=Math.max(0,Math.min(i-1,Math.floor(i*(t.top-e.top)/(e.bottom-e.top))-2)),r=s;;){let o=n.childNodes[r];if(o.nodeType==1){let l=o.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(Mr(t,c))return fc(o,t,c)}}if((r=(r+1)%i)==s)break}return n}function bu(n,t){let e=n.dom.ownerDocument,i,s=0,r=ou(e,t.left,t.top);r&&({node:i,offset:s}=r);let o=(n.root.elementFromPoint?n.root:e).elementFromPoint(t.left,t.top),l;if(!o||!n.dom.contains(o.nodeType!=1?o.parentNode:o)){let c=n.dom.getBoundingClientRect();if(!Mr(t,c)||(o=fc(n.dom,t,c),!o))return null}if(lt)for(let c=o;i&&c;c=nn(c))c.draggable&&(i=void 0);if(o=mu(o,t),i){if(Et&&i.nodeType==1&&(s=Math.min(s,i.childNodes.length),s<i.childNodes.length)){let h=i.childNodes[s],d;h.nodeName=="IMG"&&(d=h.getBoundingClientRect()).right<=t.left&&d.bottom>t.top&&s++}let c;Xn&&s&&i.nodeType==1&&(c=i.childNodes[s-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=t.top&&s--,i==n.dom&&s==i.childNodes.length-1&&i.lastChild.nodeType==1&&t.top>i.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(s==0||i.nodeType!=1||i.childNodes[s-1].nodeName!="BR")&&(l=yu(n,i,s,t))}l==null&&(l=gu(n,o,t));let a=n.docView.nearestDesc(o,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function Co(n){return n.top<n.bottom||n.left<n.right}function Xt(n,t){let e=n.getClientRects();if(e.length){let i=e[t<0?0:e.length-1];if(Co(i))return i}return Array.prototype.find.call(e,Co)||n.getBoundingClientRect()}const xu=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function pc(n,t,e){let{node:i,offset:s,atom:r}=n.docView.domFromPos(t,e<0?-1:1),o=Xn||Et;if(i.nodeType==3)if(o&&(xu.test(i.nodeValue)||(e<0?!s:s==i.nodeValue.length))){let a=Xt(qt(i,s,s),e);if(Et&&s&&/\s/.test(i.nodeValue[s-1])&&s<i.nodeValue.length){let c=Xt(qt(i,s-1,s-1),-1);if(c.top==a.top){let h=Xt(qt(i,s,s+1),-1);if(h.top!=a.top)return mn(h,h.left<c.left)}}return a}else{let a=s,c=s,h=e<0?1:-1;return e<0&&!s?(c++,h=-1):e>=0&&s==i.nodeValue.length?(a--,h=1):e<0?a--:c++,mn(Xt(qt(i,a,c),h),h<0)}if(!n.state.doc.resolve(t-(r||0)).parent.inlineContent){if(r==null&&s&&(e<0||s==Mt(i))){let a=i.childNodes[s-1];if(a.nodeType==1)return ks(a.getBoundingClientRect(),!1)}if(r==null&&s<Mt(i)){let a=i.childNodes[s];if(a.nodeType==1)return ks(a.getBoundingClientRect(),!0)}return ks(i.getBoundingClientRect(),e>=0)}if(r==null&&s&&(e<0||s==Mt(i))){let a=i.childNodes[s-1],c=a.nodeType==3?qt(a,Mt(a)-(o?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return mn(Xt(c,1),!1)}if(r==null&&s<Mt(i)){let a=i.childNodes[s];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?qt(a,0,o?0:1):a.nodeType==1?a:null:null;if(c)return mn(Xt(c,-1),!0)}return mn(Xt(i.nodeType==3?qt(i):i,-e),e>=0)}function mn(n,t){if(n.width==0)return n;let e=t?n.left:n.right;return{top:n.top,bottom:n.bottom,left:e,right:e}}function ks(n,t){if(n.height==0)return n;let e=t?n.top:n.bottom;return{top:e,bottom:e,left:n.left,right:n.right}}function mc(n,t,e){let i=n.state,s=n.root.activeElement;i!=t&&n.updateState(t),s!=n.dom&&n.focus();try{return e()}finally{i!=t&&n.updateState(i),s!=n.dom&&s&&s.focus()}}function ku(n,t,e){let i=t.selection,s=e=="up"?i.$from:i.$to;return mc(n,t,()=>{let{node:r}=n.docView.domFromPos(s.pos,e=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(r,!0);if(!l)break;if(l.node.isBlock){r=l.contentDOM||l.dom;break}r=l.dom.parentNode}let o=pc(n,s.pos,1);for(let l=r.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=qt(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let h=a[c];if(h.bottom>h.top+1&&(e=="up"?o.top-h.top>(h.bottom-o.top)*2:h.bottom-o.bottom>(o.bottom-h.top)*2))return!1}}return!0})}const Su=/[\u0590-\u08ac]/;function Mu(n,t,e){let{$head:i}=t.selection;if(!i.parent.isTextblock)return!1;let s=i.parentOffset,r=!s,o=s==i.parent.content.size,l=n.domSelection();return l?!Su.test(i.parent.textContent)||!l.modify?e=="left"||e=="backward"?r:o:mc(n,t,()=>{let{focusNode:a,focusOffset:c,anchorNode:h,anchorOffset:d}=n.domSelectionRange(),u=l.caretBidiLevel;l.modify("move",e,"character");let f=i.depth?n.docView.domAfterPos(i.before()):n.dom,{focusNode:p,focusOffset:m}=n.domSelectionRange(),g=p&&!f.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(h,d),a&&(a!=h||c!=d)&&l.extend&&l.extend(a,c)}catch{}return u!=null&&(l.caretBidiLevel=u),g}):i.pos==i.start()||i.pos==i.end()}let _o=null,To=null,Oo=!1;function wu(n,t,e){return _o==t&&To==e?Oo:(_o=t,To=e,Oo=e=="up"||e=="down"?ku(n,t,e):Mu(n,t,e))}const Tt=0,vo=1,Ce=2,Vt=3;class Qn{constructor(t,e,i,s){this.parent=t,this.children=e,this.dom=i,this.contentDOM=s,this.dirty=Tt,i.pmViewDesc=this}matchesWidget(t){return!1}matchesMark(t){return!1}matchesNode(t,e,i){return!1}matchesHack(t){return!1}parseRule(){return null}stopEvent(t){return!1}get size(){let t=0;for(let e=0;e<this.children.length;e++)t+=this.children[e].size;return t}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let t=0;t<this.children.length;t++)this.children[t].destroy()}posBeforeChild(t){for(let e=0,i=this.posAtStart;;e++){let s=this.children[e];if(s==t)return i;i+=s.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(t,e,i){if(this.contentDOM&&this.contentDOM.contains(t.nodeType==1?t:t.parentNode))if(i<0){let r,o;if(t==this.contentDOM)r=t.childNodes[e-1];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;r=t.previousSibling}for(;r&&!((o=r.pmViewDesc)&&o.parent==this);)r=r.previousSibling;return r?this.posBeforeChild(o)+o.size:this.posAtStart}else{let r,o;if(t==this.contentDOM)r=t.childNodes[e];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;r=t.nextSibling}for(;r&&!((o=r.pmViewDesc)&&o.parent==this);)r=r.nextSibling;return r?this.posBeforeChild(o):this.posAtEnd}let s;if(t==this.dom&&this.contentDOM)s=e>tt(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))s=t.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(e==0)for(let r=t;;r=r.parentNode){if(r==this.dom){s=!1;break}if(r.previousSibling)break}if(s==null&&e==t.childNodes.length)for(let r=t;;r=r.parentNode){if(r==this.dom){s=!0;break}if(r.nextSibling)break}}return s??i>0?this.posAtEnd:this.posAtStart}nearestDesc(t,e=!1){for(let i=!0,s=t;s;s=s.parentNode){let r=this.getDesc(s),o;if(r&&(!e||r.node))if(i&&(o=r.nodeDOM)&&!(o.nodeType==1?o.contains(t.nodeType==1?t:t.parentNode):o==t))i=!1;else return r}}getDesc(t){let e=t.pmViewDesc;for(let i=e;i;i=i.parent)if(i==this)return e}posFromDOM(t,e,i){for(let s=t;s;s=s.parentNode){let r=this.getDesc(s);if(r)return r.localPosFromDOM(t,e,i)}return-1}descAt(t){for(let e=0,i=0;e<this.children.length;e++){let s=this.children[e],r=i+s.size;if(i==t&&r!=i){for(;!s.border&&s.children.length;)for(let o=0;o<s.children.length;o++){let l=s.children[o];if(l.size){s=l;break}}return s}if(t<r)return s.descAt(t-i-s.border);i=r}}domFromPos(t,e){if(!this.contentDOM)return{node:this.dom,offset:0,atom:t+1};let i=0,s=0;for(let r=0;i<this.children.length;i++){let o=this.children[i],l=r+o.size;if(l>t||o instanceof yc){s=t-r;break}r=l}if(s)return this.children[i].domFromPos(s-this.children[i].border,e);for(let r;i&&!(r=this.children[i-1]).size&&r instanceof gc&&r.side>=0;i--);if(e<=0){let r,o=!0;for(;r=i?this.children[i-1]:null,!(!r||r.dom.parentNode==this.contentDOM);i--,o=!1);return r&&e&&o&&!r.border&&!r.domAtom?r.domFromPos(r.size,e):{node:this.contentDOM,offset:r?tt(r.dom)+1:0}}else{let r,o=!0;for(;r=i<this.children.length?this.children[i]:null,!(!r||r.dom.parentNode==this.contentDOM);i++,o=!1);return r&&o&&!r.border&&!r.domAtom?r.domFromPos(0,e):{node:this.contentDOM,offset:r?tt(r.dom):this.contentDOM.childNodes.length}}}parseRange(t,e,i=0){if(this.children.length==0)return{node:this.contentDOM,from:t,to:e,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let s=-1,r=-1;for(let o=i,l=0;;l++){let a=this.children[l],c=o+a.size;if(s==-1&&t<=c){let h=o+a.border;if(t>=h&&e<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(t,e,h);t=o;for(let d=l;d>0;d--){let u=this.children[d-1];if(u.size&&u.dom.parentNode==this.contentDOM&&!u.emptyChildAt(1)){s=tt(u.dom)+1;break}t-=u.size}s==-1&&(s=0)}if(s>-1&&(c>e||l==this.children.length-1)){e=c;for(let h=l+1;h<this.children.length;h++){let d=this.children[h];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){r=tt(d.dom);break}e+=d.size}r==-1&&(r=this.contentDOM.childNodes.length);break}o=c}return{node:this.contentDOM,from:t,to:e,fromOffset:s,toOffset:r}}emptyChildAt(t){if(this.border||!this.contentDOM||!this.children.length)return!1;let e=this.children[t<0?0:this.children.length-1];return e.size==0||e.emptyChildAt(t)}domAfterPos(t){let{node:e,offset:i}=this.domFromPos(t,0);if(e.nodeType!=1||i==e.childNodes.length)throw new RangeError("No node after pos "+t);return e.childNodes[i]}setSelection(t,e,i,s=!1){let r=Math.min(t,e),o=Math.max(t,e);for(let f=0,p=0;f<this.children.length;f++){let m=this.children[f],g=p+m.size;if(r>p&&o<g)return m.setSelection(t-p-m.border,e-p-m.border,i,s);p=g}let l=this.domFromPos(t,t?-1:1),a=e==t?l:this.domFromPos(e,e?-1:1),c=i.root.getSelection(),h=i.domSelectionRange(),d=!1;if((Et||lt)&&t==e){let{node:f,offset:p}=l;if(f.nodeType==3){if(d=!!(p&&f.nodeValue[p-1]==`
`),d&&p==f.nodeValue.length)for(let m=f,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:tt(g)+1});break}let y=m.pmViewDesc;if(y&&y.node&&y.node.isBlock)break}}else{let m=f.childNodes[p-1];d=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(Et&&h.focusNode&&h.focusNode!=a.node&&h.focusNode.nodeType==1){let f=h.focusNode.childNodes[h.focusOffset];f&&f.contentEditable=="false"&&(s=!0)}if(!(s||d&&lt)&&ze(l.node,l.offset,h.anchorNode,h.anchorOffset)&&ze(a.node,a.offset,h.focusNode,h.focusOffset))return;let u=!1;if((c.extend||t==e)&&!d){c.collapse(l.node,l.offset);try{t!=e&&c.extend(a.node,a.offset),u=!0}catch{}}if(!u){if(t>e){let p=l;l=a,a=p}let f=document.createRange();f.setEnd(a.node,a.offset),f.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(f)}}ignoreMutation(t){return!this.contentDOM&&t.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(t,e){for(let i=0,s=0;s<this.children.length;s++){let r=this.children[s],o=i+r.size;if(i==o?t<=o&&e>=i:t<o&&e>i){let l=i+r.border,a=o-r.border;if(t>=l&&e<=a){this.dirty=t==i||e==o?Ce:vo,t==l&&e==a&&(r.contentLost||r.dom.parentNode!=this.contentDOM)?r.dirty=Vt:r.markDirty(t-l,e-l);return}else r.dirty=r.dom==r.contentDOM&&r.dom.parentNode==this.contentDOM&&!r.children.length?Ce:Vt}i=o}this.dirty=Ce}markParentsDirty(){let t=1;for(let e=this.parent;e;e=e.parent,t++){let i=t==1?Ce:vo;e.dirty<i&&(e.dirty=i)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(t){return!1}}class gc extends Qn{constructor(t,e,i,s){let r,o=e.type.toDOM;if(typeof o=="function"&&(o=o(i,()=>{if(!r)return s;if(r.parent)return r.parent.posBeforeChild(r)})),!e.type.spec.raw){if(o.nodeType!=1){let l=document.createElement("span");l.appendChild(o),o=l}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(t,[],o,null),this.widget=e,this.widget=e,r=this}matchesWidget(t){return this.dirty==Tt&&t.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(t){let e=this.widget.spec.stopEvent;return e?e(t):!1}ignoreMutation(t){return t.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class Cu extends Qn{constructor(t,e,i,s){super(t,[],e,null),this.textDOM=i,this.text=s}get size(){return this.text.length}localPosFromDOM(t,e){return t!=this.textDOM?this.posAtStart+(e?this.size:0):this.posAtStart+e}domFromPos(t){return{node:this.textDOM,offset:t}}ignoreMutation(t){return t.type==="characterData"&&t.target.nodeValue==t.oldValue}}class Fe extends Qn{constructor(t,e,i,s,r){super(t,[],i,s),this.mark=e,this.spec=r}static create(t,e,i,s){let r=s.nodeViews[e.type.name],o=r&&r(e,s,i);return(!o||!o.dom)&&(o=He.renderSpec(document,e.type.spec.toDOM(e,i),null,e.attrs)),new Fe(t,e,o.dom,o.contentDOM||o.dom,o)}parseRule(){return this.dirty&Vt||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(t){return this.dirty!=Vt&&this.mark.eq(t)}markDirty(t,e){if(super.markDirty(t,e),this.dirty!=Tt){let i=this.parent;for(;!i.node;)i=i.parent;i.dirty<this.dirty&&(i.dirty=this.dirty),this.dirty=Tt}}slice(t,e,i){let s=Fe.create(this.parent,this.mark,!0,i),r=this.children,o=this.size;e<o&&(r=tr(r,e,o,i)),t>0&&(r=tr(r,0,t,i));for(let l=0;l<r.length;l++)r[l].parent=s;return s.children=r,s}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class le extends Qn{constructor(t,e,i,s,r,o,l,a,c){super(t,[],r,o),this.node=e,this.outerDeco=i,this.innerDeco=s,this.nodeDOM=l}static create(t,e,i,s,r,o){let l=r.nodeViews[e.type.name],a,c=l&&l(e,r,()=>{if(!a)return o;if(a.parent)return a.parent.posBeforeChild(a)},i,s),h=c&&c.dom,d=c&&c.contentDOM;if(e.isText){if(!h)h=document.createTextNode(e.text);else if(h.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else h||({dom:h,contentDOM:d}=He.renderSpec(document,e.type.spec.toDOM(e),null,e.attrs));!d&&!e.isText&&h.nodeName!="BR"&&(h.hasAttribute("contenteditable")||(h.contentEditable="false"),e.type.spec.draggable&&(h.draggable=!0));let u=h;return h=kc(h,i,e),c?a=new _u(t,e,i,s,h,d||null,u,c,r,o+1):e.isText?new Xi(t,e,i,s,h,u,r):new le(t,e,i,s,h,d||null,u,r,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let t={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(t.preserveWhitespace="full"),!this.contentDOM)t.getContent=()=>this.node.content;else if(!this.contentLost)t.contentElement=this.contentDOM;else{for(let e=this.children.length-1;e>=0;e--){let i=this.children[e];if(this.dom.contains(i.dom.parentNode)){t.contentElement=i.dom.parentNode;break}}t.contentElement||(t.getContent=()=>S.empty)}return t}matchesNode(t,e,i){return this.dirty==Tt&&t.eq(this.node)&&Ni(e,this.outerDeco)&&i.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(t,e){let i=this.node.inlineContent,s=e,r=t.composing?this.localCompositionInfo(t,e):null,o=r&&r.pos>-1?r:null,l=r&&r.pos<0,a=new Ou(this,o&&o.node,t);Au(this.node,this.innerDeco,(c,h,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,i,t):c.type.side>=0&&!d&&a.syncToMarks(h==this.node.childCount?V.none:this.node.child(h).marks,i,t),a.placeWidget(c,t,s)},(c,h,d,u)=>{a.syncToMarks(c.marks,i,t);let f;a.findNodeMatch(c,h,d,u)||l&&t.state.selection.from>s&&t.state.selection.to<s+c.nodeSize&&(f=a.findIndexWithChild(r.node))>-1&&a.updateNodeAt(c,h,d,f,t)||a.updateNextNode(c,h,d,t,u,s)||a.addNode(c,h,d,t,s),s+=c.nodeSize}),a.syncToMarks([],i,t),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==Ce)&&(o&&this.protectLocalComposition(t,o),bc(this.contentDOM,this.children,t),sn&&Eu(this.dom))}localCompositionInfo(t,e){let{from:i,to:s}=t.state.selection;if(!(t.state.selection instanceof E)||i<e||s>e+this.node.content.size)return null;let r=t.input.compositionNode;if(!r||!this.dom.contains(r.parentNode))return null;if(this.node.inlineContent){let o=r.nodeValue,l=Nu(this.node.content,o,i-e,s-e);return l<0?null:{node:r,pos:l,text:o}}else return{node:r,pos:-1,text:""}}protectLocalComposition(t,{node:e,pos:i,text:s}){if(this.getDesc(e))return;let r=e;for(;r.parentNode!=this.contentDOM;r=r.parentNode){for(;r.previousSibling;)r.parentNode.removeChild(r.previousSibling);for(;r.nextSibling;)r.parentNode.removeChild(r.nextSibling);r.pmViewDesc&&(r.pmViewDesc=void 0)}let o=new Cu(this,r,e,s);t.input.compositionNodes.push(o),this.children=tr(this.children,i,i+s.length,t,o)}update(t,e,i,s){return this.dirty==Vt||!t.sameMarkup(this.node)?!1:(this.updateInner(t,e,i,s),!0)}updateInner(t,e,i,s){this.updateOuterDeco(e),this.node=t,this.innerDeco=i,this.contentDOM&&this.updateChildren(s,this.posAtStart),this.dirty=Tt}updateOuterDeco(t){if(Ni(t,this.outerDeco))return;let e=this.nodeDOM.nodeType!=1,i=this.dom;this.dom=xc(this.dom,this.nodeDOM,Zs(this.outerDeco,this.node,e),Zs(t,this.node,e)),this.dom!=i&&(i.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=t}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function Do(n,t,e,i,s){kc(i,t,n);let r=new le(void 0,n,t,e,i,i,i,s,0);return r.contentDOM&&r.updateChildren(s,0),r}class Xi extends le{constructor(t,e,i,s,r,o,l){super(t,e,i,s,r,null,o,l,0)}parseRule(){let t=this.nodeDOM.parentNode;for(;t&&t!=this.dom&&!t.pmIsDeco;)t=t.parentNode;return{skip:t||!0}}update(t,e,i,s){return this.dirty==Vt||this.dirty!=Tt&&!this.inParent()||!t.sameMarkup(this.node)?!1:(this.updateOuterDeco(e),(this.dirty!=Tt||t.text!=this.node.text)&&t.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=t.text,s.trackWrites==this.nodeDOM&&(s.trackWrites=null)),this.node=t,this.dirty=Tt,!0)}inParent(){let t=this.parent.contentDOM;for(let e=this.nodeDOM;e;e=e.parentNode)if(e==t)return!0;return!1}domFromPos(t){return{node:this.nodeDOM,offset:t}}localPosFromDOM(t,e,i){return t==this.nodeDOM?this.posAtStart+Math.min(e,this.node.text.length):super.localPosFromDOM(t,e,i)}ignoreMutation(t){return t.type!="characterData"&&t.type!="selection"}slice(t,e,i){let s=this.node.cut(t,e),r=document.createTextNode(s.text);return new Xi(this.parent,s,this.outerDeco,this.innerDeco,r,r,i)}markDirty(t,e){super.markDirty(t,e),this.dom!=this.nodeDOM&&(t==0||e==this.nodeDOM.nodeValue.length)&&(this.dirty=Vt)}get domAtom(){return!1}isText(t){return this.node.text==t}}class yc extends Qn{parseRule(){return{ignore:!0}}matchesHack(t){return this.dirty==Tt&&this.dom.nodeName==t}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class _u extends le{constructor(t,e,i,s,r,o,l,a,c,h){super(t,e,i,s,r,o,l,c,h),this.spec=a}update(t,e,i,s){if(this.dirty==Vt)return!1;if(this.spec.update&&(this.node.type==t.type||this.spec.multiType)){let r=this.spec.update(t,e,i);return r&&this.updateInner(t,e,i,s),r}else return!this.contentDOM&&!t.isLeaf?!1:super.update(t,e,i,s)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(t,e,i,s){this.spec.setSelection?this.spec.setSelection(t,e,i.root):super.setSelection(t,e,i,s)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(t){return this.spec.stopEvent?this.spec.stopEvent(t):!1}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}}function bc(n,t,e){let i=n.firstChild,s=!1;for(let r=0;r<t.length;r++){let o=t[r],l=o.dom;if(l.parentNode==n){for(;l!=i;)i=Ao(i),s=!0;i=i.nextSibling}else s=!0,n.insertBefore(l,i);if(o instanceof Fe){let a=i?i.previousSibling:n.lastChild;bc(o.contentDOM,o.children,e),i=a?a.nextSibling:n.firstChild}}for(;i;)i=Ao(i),s=!0;s&&e.trackWrites==n&&(e.trackWrites=null)}const On=function(n){n&&(this.nodeName=n)};On.prototype=Object.create(null);const _e=[new On];function Zs(n,t,e){if(n.length==0)return _e;let i=e?_e[0]:new On,s=[i];for(let r=0;r<n.length;r++){let o=n[r].type.attrs;if(o){o.nodeName&&s.push(i=new On(o.nodeName));for(let l in o){let a=o[l];a!=null&&(e&&s.length==1&&s.push(i=new On(t.isInline?"span":"div")),l=="class"?i.class=(i.class?i.class+" ":"")+a:l=="style"?i.style=(i.style?i.style+";":"")+a:l!="nodeName"&&(i[l]=a))}}}return s}function xc(n,t,e,i){if(e==_e&&i==_e)return t;let s=t;for(let r=0;r<i.length;r++){let o=i[r],l=e[r];if(r){let a;l&&l.nodeName==o.nodeName&&s!=n&&(a=s.parentNode)&&a.nodeName.toLowerCase()==o.nodeName||(a=document.createElement(o.nodeName),a.pmIsDeco=!0,a.appendChild(s),l=_e[0]),s=a}Tu(s,l||_e[0],o)}return s}function Tu(n,t,e){for(let i in t)i!="class"&&i!="style"&&i!="nodeName"&&!(i in e)&&n.removeAttribute(i);for(let i in e)i!="class"&&i!="style"&&i!="nodeName"&&e[i]!=t[i]&&n.setAttribute(i,e[i]);if(t.class!=e.class){let i=t.class?t.class.split(" ").filter(Boolean):[],s=e.class?e.class.split(" ").filter(Boolean):[];for(let r=0;r<i.length;r++)s.indexOf(i[r])==-1&&n.classList.remove(i[r]);for(let r=0;r<s.length;r++)i.indexOf(s[r])==-1&&n.classList.add(s[r]);n.classList.length==0&&n.removeAttribute("class")}if(t.style!=e.style){if(t.style){let i=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,s;for(;s=i.exec(t.style);)n.style.removeProperty(s[1])}e.style&&(n.style.cssText+=e.style)}}function kc(n,t,e){return xc(n,n,_e,Zs(t,e,n.nodeType!=1))}function Ni(n,t){if(n.length!=t.length)return!1;for(let e=0;e<n.length;e++)if(!n[e].type.eq(t[e].type))return!1;return!0}function Ao(n){let t=n.nextSibling;return n.parentNode.removeChild(n),t}class Ou{constructor(t,e,i){this.lock=e,this.view=i,this.index=0,this.stack=[],this.changed=!1,this.top=t,this.preMatch=vu(t.node.content,t)}destroyBetween(t,e){if(t!=e){for(let i=t;i<e;i++)this.top.children[i].destroy();this.top.children.splice(t,e-t),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(t,e,i){let s=0,r=this.stack.length>>1,o=Math.min(r,t.length);for(;s<o&&(s==r-1?this.top:this.stack[s+1<<1]).matchesMark(t[s])&&t[s].type.spec.spanning!==!1;)s++;for(;s<r;)this.destroyRest(),this.top.dirty=Tt,this.index=this.stack.pop(),this.top=this.stack.pop(),r--;for(;r<t.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(t[r])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=Fe.create(this.top,t[r],e,i);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,r++}}findNodeMatch(t,e,i,s){let r=-1,o;if(s>=this.preMatch.index&&(o=this.preMatch.matches[s-this.preMatch.index]).parent==this.top&&o.matchesNode(t,e,i))r=this.top.children.indexOf(o,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(t,e,i)&&!this.preMatch.matched.has(c)){r=l;break}}return r<0?!1:(this.destroyBetween(this.index,r),this.index++,!0)}updateNodeAt(t,e,i,s,r){let o=this.top.children[s];return o.dirty==Vt&&o.dom==o.contentDOM&&(o.dirty=Ce),o.update(t,e,i,r)?(this.destroyBetween(this.index,s),this.index++,!0):!1}findIndexWithChild(t){for(;;){let e=t.parentNode;if(!e)return-1;if(e==this.top.contentDOM){let i=t.pmViewDesc;if(i){for(let s=this.index;s<this.top.children.length;s++)if(this.top.children[s]==i)return s}return-1}t=e}}updateNextNode(t,e,i,s,r,o){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof le){let c=this.preMatch.matched.get(a);if(c!=null&&c!=r)return!1;let h=a.dom,d,u=this.isLocked(h)&&!(t.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==t.text&&a.dirty!=Vt&&Ni(e,a.outerDeco));if(!u&&a.update(t,e,i,s))return this.destroyBetween(this.index,l),a.dom!=h&&(this.changed=!0),this.index++,!0;if(!u&&(d=this.recreateWrapper(a,t,e,i,s,o)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=Ce,d.updateChildren(s,o+1),d.dirty=Tt),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(t,e,i,s,r,o){if(t.dirty||e.isAtom||!t.children.length||!t.node.content.eq(e.content)||!Ni(i,t.outerDeco)||!s.eq(t.innerDeco))return null;let l=le.create(this.top,e,i,s,r,o);if(l.contentDOM){l.children=t.children,t.children=[];for(let a of l.children)a.parent=l}return t.destroy(),l}addNode(t,e,i,s,r){let o=le.create(this.top,t,e,i,s,r);o.contentDOM&&o.updateChildren(s,r+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(t,e,i){let s=this.index<this.top.children.length?this.top.children[this.index]:null;if(s&&s.matchesWidget(t)&&(t==s.widget||!s.widget.type.toDOM.parentNode))this.index++;else{let r=new gc(this.top,t,e,i);this.top.children.splice(this.index++,0,r),this.changed=!0}}addTextblockHacks(){let t=this.top.children[this.index-1],e=this.top;for(;t instanceof Fe;)e=t,t=e.children[e.children.length-1];(!t||!(t instanceof Xi)||/\n$/.test(t.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(t.node.text))&&((lt||it)&&t&&t.dom.contentEditable=="false"&&this.addHackNode("IMG",e),this.addHackNode("BR",this.top))}addHackNode(t,e){if(e==this.top&&this.index<e.children.length&&e.children[this.index].matchesHack(t))this.index++;else{let i=document.createElement(t);t=="IMG"&&(i.className="ProseMirror-separator",i.alt=""),t=="BR"&&(i.className="ProseMirror-trailingBreak");let s=new yc(this.top,[],i,null);e!=this.top?e.children.push(s):e.children.splice(this.index++,0,s),this.changed=!0}}isLocked(t){return this.lock&&(t==this.lock||t.nodeType==1&&t.contains(this.lock.parentNode))}}function vu(n,t){let e=t,i=e.children.length,s=n.childCount,r=new Map,o=[];t:for(;s>0;){let l;for(;;)if(i){let c=e.children[i-1];if(c instanceof Fe)e=c,i=c.children.length;else{l=c,i--;break}}else{if(e==t)break t;i=e.parent.children.indexOf(e),e=e.parent}let a=l.node;if(a){if(a!=n.child(s-1))break;--s,r.set(l,s),o.push(l)}}return{index:s,matched:r,matches:o.reverse()}}function Du(n,t){return n.type.side-t.type.side}function Au(n,t,e,i){let s=t.locals(n),r=0;if(s.length==0){for(let c=0;c<n.childCount;c++){let h=n.child(c);i(h,s,t.forChild(r,h),c),r+=h.nodeSize}return}let o=0,l=[],a=null;for(let c=0;;){let h,d;for(;o<s.length&&s[o].to==r;){let g=s[o++];g.widget&&(h?(d||(d=[h])).push(g):h=g)}if(h)if(d){d.sort(Du);for(let g=0;g<d.length;g++)e(d[g],c,!!a)}else e(h,c,!!a);let u,f;if(a)f=-1,u=a,a=null;else if(c<n.childCount)f=c,u=n.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=r&&l.splice(g--,1);for(;o<s.length&&s[o].from<=r&&s[o].to>r;)l.push(s[o++]);let p=r+u.nodeSize;if(u.isText){let g=p;o<s.length&&s[o].from<g&&(g=s[o].from);for(let y=0;y<l.length;y++)l[y].to<g&&(g=l[y].to);g<p&&(a=u.cut(g-r),u=u.cut(0,g-r),p=g,f=-1)}else for(;o<s.length&&s[o].to<p;)o++;let m=u.isInline&&!u.isLeaf?l.filter(g=>!g.inline):l.slice();i(u,m,t.forChild(r,u),f),r=p}}function Eu(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let t=n.style.cssText;n.style.cssText=t+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=t}}function Nu(n,t,e,i){for(let s=0,r=0;s<n.childCount&&r<=i;){let o=n.child(s++),l=r;if(r+=o.nodeSize,!o.isText)continue;let a=o.text;for(;s<n.childCount;){let c=n.child(s++);if(r+=c.nodeSize,!c.isText)break;a+=c.text}if(r>=e){if(r>=i&&a.slice(i-t.length-l,i-l)==t)return i-t.length;let c=l<i?a.lastIndexOf(t,i-l-1):-1;if(c>=0&&c+t.length+l>=e)return l+c;if(e==i&&a.length>=i+t.length-l&&a.slice(i-l,i-l+t.length)==t)return i}}return-1}function tr(n,t,e,i,s){let r=[];for(let o=0,l=0;o<n.length;o++){let a=n[o],c=l,h=l+=a.size;c>=e||h<=t?r.push(a):(c<t&&r.push(a.slice(0,t-c,i)),s&&(r.push(s),s=void 0),h>e&&r.push(a.slice(e-c,a.size,i)))}return r}function wr(n,t=null){let e=n.domSelectionRange(),i=n.state.doc;if(!e.focusNode)return null;let s=n.docView.nearestDesc(e.focusNode),r=s&&s.size==0,o=n.docView.posFromDOM(e.focusNode,e.focusOffset,1);if(o<0)return null;let l=i.resolve(o),a,c;if(Yi(e)){for(a=o;s&&!s.node;)s=s.parent;let d=s.node;if(s&&d.isAtom&&D.isSelectable(d)&&s.parent&&!(d.isInline&&su(e.focusNode,e.focusOffset,s.dom))){let u=s.posBefore;c=new D(o==u?l:i.resolve(u))}}else{if(e instanceof n.dom.ownerDocument.defaultView.Selection&&e.rangeCount>1){let d=o,u=o;for(let f=0;f<e.rangeCount;f++){let p=e.getRangeAt(f);d=Math.min(d,n.docView.posFromDOM(p.startContainer,p.startOffset,1)),u=Math.max(u,n.docView.posFromDOM(p.endContainer,p.endOffset,-1))}if(d<0)return null;[a,o]=u==n.state.selection.anchor?[u,d]:[d,u],l=i.resolve(o)}else a=n.docView.posFromDOM(e.anchorNode,e.anchorOffset,1);if(a<0)return null}let h=i.resolve(a);if(!c){let d=t=="pointer"||n.state.selection.head<l.pos&&!r?1:-1;c=Cr(n,h,l,d)}return c}function Sc(n){return n.editable?n.hasFocus():wc(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function Gt(n,t=!1){let e=n.state.selection;if(Mc(n,e),!!Sc(n)){if(!t&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&it){let i=n.domSelectionRange(),s=n.domObserver.currentSelection;if(i.anchorNode&&s.anchorNode&&ze(i.anchorNode,i.anchorOffset,s.anchorNode,s.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)Iu(n);else{let{anchor:i,head:s}=e,r,o;Eo&&!(e instanceof E)&&(e.$from.parent.inlineContent||(r=No(n,e.from)),!e.empty&&!e.$from.parent.inlineContent&&(o=No(n,e.to))),n.docView.setSelection(i,s,n,t),Eo&&(r&&Po(r),o&&Po(o)),e.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&Pu(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}const Eo=lt||it&&cc<63;function No(n,t){let{node:e,offset:i}=n.docView.domFromPos(t,0),s=i<e.childNodes.length?e.childNodes[i]:null,r=i?e.childNodes[i-1]:null;if(lt&&s&&s.contentEditable=="false")return Ss(s);if((!s||s.contentEditable=="false")&&(!r||r.contentEditable=="false")){if(s)return Ss(s);if(r)return Ss(r)}}function Ss(n){return n.contentEditable="true",lt&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function Po(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function Pu(n){let t=n.dom.ownerDocument;t.removeEventListener("selectionchange",n.input.hideSelectionGuard);let e=n.domSelectionRange(),i=e.anchorNode,s=e.anchorOffset;t.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(e.anchorNode!=i||e.anchorOffset!=s)&&(t.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Sc(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function Iu(n){let t=n.domSelection(),e=document.createRange();if(!t)return;let i=n.cursorWrapper.dom,s=i.nodeName=="IMG";s?e.setStart(i.parentNode,tt(i)+1):e.setStart(i,0),e.collapse(!0),t.removeAllRanges(),t.addRange(e),!s&&!n.state.selection.visible&&mt&&oe<=11&&(i.disabled=!0,i.disabled=!1)}function Mc(n,t){if(t instanceof D){let e=n.docView.descAt(t.from);e!=n.lastSelectedViewDesc&&(Io(n),e&&e.selectNode(),n.lastSelectedViewDesc=e)}else Io(n)}function Io(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function Cr(n,t,e,i){return n.someProp("createSelectionBetween",s=>s(n,t,e))||E.between(t,e,i)}function Ro(n){return n.editable&&!n.hasFocus()?!1:wc(n)}function wc(n){let t=n.domSelectionRange();if(!t.anchorNode)return!1;try{return n.dom.contains(t.anchorNode.nodeType==3?t.anchorNode.parentNode:t.anchorNode)&&(n.editable||n.dom.contains(t.focusNode.nodeType==3?t.focusNode.parentNode:t.focusNode))}catch{return!1}}function Ru(n){let t=n.docView.domFromPos(n.state.selection.anchor,0),e=n.domSelectionRange();return ze(t.node,t.offset,e.anchorNode,e.anchorOffset)}function er(n,t){let{$anchor:e,$head:i}=n.selection,s=t>0?e.max(i):e.min(i),r=s.parent.inlineContent?s.depth?n.doc.resolve(t>0?s.after():s.before()):null:s;return r&&N.findFrom(r,t)}function Qt(n,t){return n.dispatch(n.state.tr.setSelection(t).scrollIntoView()),!0}function Lo(n,t,e){let i=n.state.selection;if(i instanceof E)if(e.indexOf("s")>-1){let{$head:s}=i,r=s.textOffset?null:t<0?s.nodeBefore:s.nodeAfter;if(!r||r.isText||!r.isLeaf)return!1;let o=n.state.doc.resolve(s.pos+r.nodeSize*(t<0?-1:1));return Qt(n,new E(i.$anchor,o))}else if(i.empty){if(n.endOfTextblock(t>0?"forward":"backward")){let s=er(n.state,t);return s&&s instanceof D?Qt(n,s):!1}else if(!(St&&e.indexOf("m")>-1)){let s=i.$head,r=s.textOffset?null:t<0?s.nodeBefore:s.nodeAfter,o;if(!r||r.isText)return!1;let l=t<0?s.pos-r.nodeSize:s.pos;return r.isAtom||(o=n.docView.descAt(l))&&!o.contentDOM?D.isSelectable(r)?Qt(n,new D(t<0?n.state.doc.resolve(s.pos-r.nodeSize):s)):Xn?Qt(n,new E(n.state.doc.resolve(t<0?l:l+r.nodeSize))):!1:!1}}else return!1;else{if(i instanceof D&&i.node.isInline)return Qt(n,new E(t>0?i.$to:i.$from));{let s=er(n.state,t);return s?Qt(n,s):!1}}}function Pi(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function vn(n,t){let e=n.pmViewDesc;return e&&e.size==0&&(t<0||n.nextSibling||n.nodeName!="BR")}function qe(n,t){return t<0?Lu(n):Bu(n)}function Lu(n){let t=n.domSelectionRange(),e=t.focusNode,i=t.focusOffset;if(!e)return;let s,r,o=!1;for(Et&&e.nodeType==1&&i<Pi(e)&&vn(e.childNodes[i],-1)&&(o=!0);;)if(i>0){if(e.nodeType!=1)break;{let l=e.childNodes[i-1];if(vn(l,-1))s=e,r=--i;else if(l.nodeType==3)e=l,i=e.nodeValue.length;else break}}else{if(Cc(e))break;{let l=e.previousSibling;for(;l&&vn(l,-1);)s=e.parentNode,r=tt(l),l=l.previousSibling;if(l)e=l,i=Pi(e);else{if(e=e.parentNode,e==n.dom)break;i=0}}}o?nr(n,e,i):s&&nr(n,s,r)}function Bu(n){let t=n.domSelectionRange(),e=t.focusNode,i=t.focusOffset;if(!e)return;let s=Pi(e),r,o;for(;;)if(i<s){if(e.nodeType!=1)break;let l=e.childNodes[i];if(vn(l,1))r=e,o=++i;else break}else{if(Cc(e))break;{let l=e.nextSibling;for(;l&&vn(l,1);)r=l.parentNode,o=tt(l)+1,l=l.nextSibling;if(l)e=l,i=0,s=Pi(e);else{if(e=e.parentNode,e==n.dom)break;i=s=0}}}r&&nr(n,r,o)}function Cc(n){let t=n.pmViewDesc;return t&&t.node&&t.node.isBlock}function zu(n,t){for(;n&&t==n.childNodes.length&&!Yn(n);)t=tt(n)+1,n=n.parentNode;for(;n&&t<n.childNodes.length;){let e=n.childNodes[t];if(e.nodeType==3)return e;if(e.nodeType==1&&e.contentEditable=="false")break;n=e,t=0}}function Fu(n,t){for(;n&&!t&&!Yn(n);)t=tt(n),n=n.parentNode;for(;n&&t;){let e=n.childNodes[t-1];if(e.nodeType==3)return e;if(e.nodeType==1&&e.contentEditable=="false")break;n=e,t=n.childNodes.length}}function nr(n,t,e){if(t.nodeType!=3){let r,o;(o=zu(t,e))?(t=o,e=0):(r=Fu(t,e))&&(t=r,e=r.nodeValue.length)}let i=n.domSelection();if(!i)return;if(Yi(i)){let r=document.createRange();r.setEnd(t,e),r.setStart(t,e),i.removeAllRanges(),i.addRange(r)}else i.extend&&i.extend(t,e);n.domObserver.setCurSelection();let{state:s}=n;setTimeout(()=>{n.state==s&&Gt(n)},50)}function Bo(n,t){let e=n.state.doc.resolve(t);if(!(it||lu)&&e.parent.inlineContent){let s=n.coordsAtPos(t);if(t>e.start()){let r=n.coordsAtPos(t-1),o=(r.top+r.bottom)/2;if(o>s.top&&o<s.bottom&&Math.abs(r.left-s.left)>1)return r.left<s.left?"ltr":"rtl"}if(t<e.end()){let r=n.coordsAtPos(t+1),o=(r.top+r.bottom)/2;if(o>s.top&&o<s.bottom&&Math.abs(r.left-s.left)>1)return r.left>s.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function zo(n,t,e){let i=n.state.selection;if(i instanceof E&&!i.empty||e.indexOf("s")>-1||St&&e.indexOf("m")>-1)return!1;let{$from:s,$to:r}=i;if(!s.parent.inlineContent||n.endOfTextblock(t<0?"up":"down")){let o=er(n.state,t);if(o&&o instanceof D)return Qt(n,o)}if(!s.parent.inlineContent){let o=t<0?s:r,l=i instanceof xt?N.near(o,t):N.findFrom(o,t);return l?Qt(n,l):!1}return!1}function Fo(n,t){if(!(n.state.selection instanceof E))return!0;let{$head:e,$anchor:i,empty:s}=n.state.selection;if(!e.sameParent(i))return!0;if(!s)return!1;if(n.endOfTextblock(t>0?"forward":"backward"))return!0;let r=!e.textOffset&&(t<0?e.nodeBefore:e.nodeAfter);if(r&&!r.isText){let o=n.state.tr;return t<0?o.delete(e.pos-r.nodeSize,e.pos):o.delete(e.pos,e.pos+r.nodeSize),n.dispatch(o),!0}return!1}function Vo(n,t,e){n.domObserver.stop(),t.contentEditable=e,n.domObserver.start()}function Vu(n){if(!lt||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:e}=n.domSelectionRange();if(t&&t.nodeType==1&&e==0&&t.firstChild&&t.firstChild.contentEditable=="false"){let i=t.firstChild;Vo(n,i,"true"),setTimeout(()=>Vo(n,i,"false"),20)}return!1}function Hu(n){let t="";return n.ctrlKey&&(t+="c"),n.metaKey&&(t+="m"),n.altKey&&(t+="a"),n.shiftKey&&(t+="s"),t}function $u(n,t){let e=t.keyCode,i=Hu(t);if(e==8||St&&e==72&&i=="c")return Fo(n,-1)||qe(n,-1);if(e==46&&!t.shiftKey||St&&e==68&&i=="c")return Fo(n,1)||qe(n,1);if(e==13||e==27)return!0;if(e==37||St&&e==66&&i=="c"){let s=e==37?Bo(n,n.state.selection.from)=="ltr"?-1:1:-1;return Lo(n,s,i)||qe(n,s)}else if(e==39||St&&e==70&&i=="c"){let s=e==39?Bo(n,n.state.selection.from)=="ltr"?1:-1:1;return Lo(n,s,i)||qe(n,s)}else{if(e==38||St&&e==80&&i=="c")return zo(n,-1,i)||qe(n,-1);if(e==40||St&&e==78&&i=="c")return Vu(n)||zo(n,1,i)||qe(n,1);if(i==(St?"m":"c")&&(e==66||e==73||e==89||e==90))return!0}return!1}function _r(n,t){n.someProp("transformCopied",f=>{t=f(t,n)});let e=[],{content:i,openStart:s,openEnd:r}=t;for(;s>1&&r>1&&i.childCount==1&&i.firstChild.childCount==1;){s--,r--;let f=i.firstChild;e.push(f.type.name,f.attrs!=f.type.defaultAttrs?f.attrs:null),i=f.content}let o=n.someProp("clipboardSerializer")||He.fromSchema(n.state.schema),l=Ac(),a=l.createElement("div");a.appendChild(o.serializeFragment(i,{document:l}));let c=a.firstChild,h,d=0;for(;c&&c.nodeType==1&&(h=Dc[c.nodeName.toLowerCase()]);){for(let f=h.length-1;f>=0;f--){let p=l.createElement(h[f]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${s} ${r}${d?` -${d}`:""} ${JSON.stringify(e)}`);let u=n.someProp("clipboardTextSerializer",f=>f(t,n))||t.content.textBetween(0,t.content.size,`

`);return{dom:a,text:u,slice:t}}function _c(n,t,e,i,s){let r=s.parent.type.spec.code,o,l;if(!e&&!t)return null;let a=t&&(i||r||!e);if(a){if(n.someProp("transformPastedText",u=>{t=u(t,r||i,n)}),r)return t?new T(S.from(n.state.schema.text(t.replace(/\r\n?/g,`
`))),0,0):T.empty;let d=n.someProp("clipboardTextParser",u=>u(t,s,i,n));if(d)l=d;else{let u=s.marks(),{schema:f}=n.state,p=He.fromSchema(f);o=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=o.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(f.text(m,u)))})}}else n.someProp("transformPastedHTML",d=>{e=d(e,n)}),o=qu(e),Xn&&Ju(o);let c=o&&o.querySelector("[data-pm-slice]"),h=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(h&&h[3])for(let d=+h[3];d>0;d--){let u=o.firstChild;for(;u&&u.nodeType!=1;)u=u.nextSibling;if(!u)break;o=u}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||re.fromSchema(n.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||h),context:s,ruleFromNode(u){return u.nodeName=="BR"&&!u.nextSibling&&u.parentNode&&!Wu.test(u.parentNode.nodeName)?{ignore:!0}:null}})),h)l=Uu(Ho(l,+h[1],+h[2]),h[4]);else if(l=T.maxOpen(ju(l.content,s),!0),l.openStart||l.openEnd){let d=0,u=0;for(let f=l.content.firstChild;d<l.openStart&&!f.type.spec.isolating;d++,f=f.firstChild);for(let f=l.content.lastChild;u<l.openEnd&&!f.type.spec.isolating;u++,f=f.lastChild);l=Ho(l,d,u)}return n.someProp("transformPasted",d=>{l=d(l,n)}),l}const Wu=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function ju(n,t){if(n.childCount<2)return n;for(let e=t.depth;e>=0;e--){let s=t.node(e).contentMatchAt(t.index(e)),r,o=[];if(n.forEach(l=>{if(!o)return;let a=s.findWrapping(l.type),c;if(!a)return o=null;if(c=o.length&&r.length&&Oc(a,r,l,o[o.length-1],0))o[o.length-1]=c;else{o.length&&(o[o.length-1]=vc(o[o.length-1],r.length));let h=Tc(l,a);o.push(h),s=s.matchType(h.type),r=a}}),o)return S.from(o)}return n}function Tc(n,t,e=0){for(let i=t.length-1;i>=e;i--)n=t[i].create(null,S.from(n));return n}function Oc(n,t,e,i,s){if(s<n.length&&s<t.length&&n[s]==t[s]){let r=Oc(n,t,e,i.lastChild,s+1);if(r)return i.copy(i.content.replaceChild(i.childCount-1,r));if(i.contentMatchAt(i.childCount).matchType(s==n.length-1?e.type:n[s+1]))return i.copy(i.content.append(S.from(Tc(e,n,s+1))))}}function vc(n,t){if(t==0)return n;let e=n.content.replaceChild(n.childCount-1,vc(n.lastChild,t-1)),i=n.contentMatchAt(n.childCount).fillBefore(S.empty,!0);return n.copy(e.append(i))}function ir(n,t,e,i,s,r){let o=t<0?n.firstChild:n.lastChild,l=o.content;return n.childCount>1&&(r=0),s<i-1&&(l=ir(l,t,e,i,s+1,r)),s>=e&&(l=t<0?o.contentMatchAt(0).fillBefore(l,r<=s).append(l):l.append(o.contentMatchAt(o.childCount).fillBefore(S.empty,!0))),n.replaceChild(t<0?0:n.childCount-1,o.copy(l))}function Ho(n,t,e){return t<n.openStart&&(n=new T(ir(n.content,-1,t,n.openStart,0,n.openEnd),t,n.openEnd)),e<n.openEnd&&(n=new T(ir(n.content,1,e,n.openEnd,0,0),n.openStart,e)),n}const Dc={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let $o=null;function Ac(){return $o||($o=document.implementation.createHTMLDocument("title"))}let Ms=null;function Ku(n){let t=window.trustedTypes;return t?(Ms||(Ms=t.defaultPolicy||t.createPolicy("ProseMirrorClipboard",{createHTML:e=>e})),Ms.createHTML(n)):n}function qu(n){let t=/^(\s*<meta [^>]*>)*/.exec(n);t&&(n=n.slice(t[0].length));let e=Ac().createElement("div"),i=/<([a-z][^>\s]+)/i.exec(n),s;if((s=i&&Dc[i[1].toLowerCase()])&&(n=s.map(r=>"<"+r+">").join("")+n+s.map(r=>"</"+r+">").reverse().join("")),e.innerHTML=Ku(n),s)for(let r=0;r<s.length;r++)e=e.querySelector(s[r])||e;return e}function Ju(n){let t=n.querySelectorAll(it?"span:not([class]):not([style])":"span.Apple-converted-space");for(let e=0;e<t.length;e++){let i=t[e];i.childNodes.length==1&&i.textContent==" "&&i.parentNode&&i.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),i)}}function Uu(n,t){if(!n.size)return n;let e=n.content.firstChild.type.schema,i;try{i=JSON.parse(t)}catch{return n}let{content:s,openStart:r,openEnd:o}=n;for(let l=i.length-2;l>=0;l-=2){let a=e.nodes[i[l]];if(!a||a.hasRequiredAttrs())break;s=S.from(a.create(i[l+1],s)),r++,o++}return new T(s,r,o)}const at={},ct={},Gu={touchstart:!0,touchmove:!0};class Yu{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function Xu(n){for(let t in at){let e=at[t];n.dom.addEventListener(t,n.input.eventHandlers[t]=i=>{Zu(n,i)&&!Tr(n,i)&&(n.editable||!(i.type in ct))&&e(n,i)},Gu[t]?{passive:!0}:void 0)}lt&&n.dom.addEventListener("input",()=>null),sr(n)}function ie(n,t){n.input.lastSelectionOrigin=t,n.input.lastSelectionTime=Date.now()}function Qu(n){n.domObserver.stop();for(let t in n.input.eventHandlers)n.dom.removeEventListener(t,n.input.eventHandlers[t]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function sr(n){n.someProp("handleDOMEvents",t=>{for(let e in t)n.input.eventHandlers[e]||n.dom.addEventListener(e,n.input.eventHandlers[e]=i=>Tr(n,i))})}function Tr(n,t){return n.someProp("handleDOMEvents",e=>{let i=e[t.type];return i?i(n,t)||t.defaultPrevented:!1})}function Zu(n,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let e=t.target;e!=n.dom;e=e.parentNode)if(!e||e.nodeType==11||e.pmViewDesc&&e.pmViewDesc.stopEvent(t))return!1;return!0}function tf(n,t){!Tr(n,t)&&at[t.type]&&(n.editable||!(t.type in ct))&&at[t.type](n,t)}ct.keydown=(n,t)=>{let e=t;if(n.input.shiftKey=e.keyCode==16||e.shiftKey,!Nc(n,e)&&(n.input.lastKeyCode=e.keyCode,n.input.lastKeyCodeTime=Date.now(),!(Jt&&it&&e.keyCode==13)))if(e.keyCode!=229&&n.domObserver.forceFlush(),sn&&e.keyCode==13&&!e.ctrlKey&&!e.altKey&&!e.metaKey){let i=Date.now();n.input.lastIOSEnter=i,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==i&&(n.someProp("handleKeyDown",s=>s(n,Me(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",i=>i(n,e))||$u(n,e)?e.preventDefault():ie(n,"key")};ct.keyup=(n,t)=>{t.keyCode==16&&(n.input.shiftKey=!1)};ct.keypress=(n,t)=>{let e=t;if(Nc(n,e)||!e.charCode||e.ctrlKey&&!e.altKey||St&&e.metaKey)return;if(n.someProp("handleKeyPress",s=>s(n,e))){e.preventDefault();return}let i=n.state.selection;if(!(i instanceof E)||!i.$from.sameParent(i.$to)){let s=String.fromCharCode(e.charCode),r=()=>n.state.tr.insertText(s).scrollIntoView();!/[\r\n]/.test(s)&&!n.someProp("handleTextInput",o=>o(n,i.$from.pos,i.$to.pos,s,r))&&n.dispatch(r()),e.preventDefault()}};function Qi(n){return{left:n.clientX,top:n.clientY}}function ef(n,t){let e=t.x-n.clientX,i=t.y-n.clientY;return e*e+i*i<100}function Or(n,t,e,i,s){if(i==-1)return!1;let r=n.state.doc.resolve(i);for(let o=r.depth+1;o>0;o--)if(n.someProp(t,l=>o>r.depth?l(n,e,r.nodeAfter,r.before(o),s,!0):l(n,e,r.node(o),r.before(o),s,!1)))return!0;return!1}function Ze(n,t,e){if(n.focused||n.focus(),n.state.selection.eq(t))return;let i=n.state.tr.setSelection(t);i.setMeta("pointer",!0),n.dispatch(i)}function nf(n,t){if(t==-1)return!1;let e=n.state.doc.resolve(t),i=e.nodeAfter;return i&&i.isAtom&&D.isSelectable(i)?(Ze(n,new D(e)),!0):!1}function sf(n,t){if(t==-1)return!1;let e=n.state.selection,i,s;e instanceof D&&(i=e.node);let r=n.state.doc.resolve(t);for(let o=r.depth+1;o>0;o--){let l=o>r.depth?r.nodeAfter:r.node(o);if(D.isSelectable(l)){i&&e.$from.depth>0&&o>=e.$from.depth&&r.before(e.$from.depth+1)==e.$from.pos?s=r.before(e.$from.depth):s=r.before(o);break}}return s!=null?(Ze(n,D.create(n.state.doc,s)),!0):!1}function rf(n,t,e,i,s){return Or(n,"handleClickOn",t,e,i)||n.someProp("handleClick",r=>r(n,t,i))||(s?sf(n,e):nf(n,e))}function of(n,t,e,i){return Or(n,"handleDoubleClickOn",t,e,i)||n.someProp("handleDoubleClick",s=>s(n,t,i))}function lf(n,t,e,i){return Or(n,"handleTripleClickOn",t,e,i)||n.someProp("handleTripleClick",s=>s(n,t,i))||af(n,e,i)}function af(n,t,e){if(e.button!=0)return!1;let i=n.state.doc;if(t==-1)return i.inlineContent?(Ze(n,E.create(i,0,i.content.size)),!0):!1;let s=i.resolve(t);for(let r=s.depth+1;r>0;r--){let o=r>s.depth?s.nodeAfter:s.node(r),l=s.before(r);if(o.inlineContent)Ze(n,E.create(i,l+1,l+1+o.content.size));else if(D.isSelectable(o))Ze(n,D.create(i,l));else continue;return!0}}function vr(n){return Ii(n)}const Ec=St?"metaKey":"ctrlKey";at.mousedown=(n,t)=>{let e=t;n.input.shiftKey=e.shiftKey;let i=vr(n),s=Date.now(),r="singleClick";s-n.input.lastClick.time<500&&ef(e,n.input.lastClick)&&!e[Ec]&&n.input.lastClick.button==e.button&&(n.input.lastClick.type=="singleClick"?r="doubleClick":n.input.lastClick.type=="doubleClick"&&(r="tripleClick")),n.input.lastClick={time:s,x:e.clientX,y:e.clientY,type:r,button:e.button};let o=n.posAtCoords(Qi(e));o&&(r=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new cf(n,o,e,!!i)):(r=="doubleClick"?of:lf)(n,o.pos,o.inside,e)?e.preventDefault():ie(n,"pointer"))};class cf{constructor(t,e,i,s){this.view=t,this.pos=e,this.event=i,this.flushed=s,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=t.state.doc,this.selectNode=!!i[Ec],this.allowDefault=i.shiftKey;let r,o;if(e.inside>-1)r=t.state.doc.nodeAt(e.inside),o=e.inside;else{let h=t.state.doc.resolve(e.pos);r=h.parent,o=h.depth?h.before():0}const l=s?null:i.target,a=l?t.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=t.state;(i.button==0&&r.type.spec.draggable&&r.type.spec.selectable!==!1||c instanceof D&&c.from<=o&&c.to>o)&&(this.mightDrag={node:r,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&Et&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),t.root.addEventListener("mouseup",this.up=this.up.bind(this)),t.root.addEventListener("mousemove",this.move=this.move.bind(this)),ie(t,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Gt(this.view)),this.view.input.mouseDown=null}up(t){if(this.done(),!this.view.dom.contains(t.target))return;let e=this.pos;this.view.state.doc!=this.startDoc&&(e=this.view.posAtCoords(Qi(t))),this.updateAllowDefault(t),this.allowDefault||!e?ie(this.view,"pointer"):rf(this.view,e.pos,e.inside,t,this.selectNode)?t.preventDefault():t.button==0&&(this.flushed||lt&&this.mightDrag&&!this.mightDrag.node.isAtom||it&&!this.view.state.selection.visible&&Math.min(Math.abs(e.pos-this.view.state.selection.from),Math.abs(e.pos-this.view.state.selection.to))<=2)?(Ze(this.view,N.near(this.view.state.doc.resolve(e.pos))),t.preventDefault()):ie(this.view,"pointer")}move(t){this.updateAllowDefault(t),ie(this.view,"pointer"),t.buttons==0&&this.done()}updateAllowDefault(t){!this.allowDefault&&(Math.abs(this.event.x-t.clientX)>4||Math.abs(this.event.y-t.clientY)>4)&&(this.allowDefault=!0)}}at.touchstart=n=>{n.input.lastTouch=Date.now(),vr(n),ie(n,"pointer")};at.touchmove=n=>{n.input.lastTouch=Date.now(),ie(n,"pointer")};at.contextmenu=n=>vr(n);function Nc(n,t){return n.composing?!0:lt&&Math.abs(t.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}const hf=Jt?5e3:-1;ct.compositionstart=ct.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:t}=n,e=t.selection.$to;if(t.selection instanceof E&&(t.storedMarks||!e.textOffset&&e.parentOffset&&e.nodeBefore.marks.some(i=>i.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||e.marks(),Ii(n,!0),n.markCursor=null;else if(Ii(n,!t.selection.empty),Et&&t.selection.empty&&e.parentOffset&&!e.textOffset&&e.nodeBefore.marks.length){let i=n.domSelectionRange();for(let s=i.focusNode,r=i.focusOffset;s&&s.nodeType==1&&r!=0;){let o=r<0?s.lastChild:s.childNodes[r-1];if(!o)break;if(o.nodeType==3){let l=n.domSelection();l&&l.collapse(o,o.nodeValue.length);break}else s=o,r=-1}}n.input.composing=!0}Pc(n,hf)};ct.compositionend=(n,t)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=t.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,Pc(n,20))};function Pc(n,t){clearTimeout(n.input.composingTimeout),t>-1&&(n.input.composingTimeout=setTimeout(()=>Ii(n),t))}function Ic(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=uf());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function df(n){let t=n.domSelectionRange();if(!t.focusNode)return null;let e=nu(t.focusNode,t.focusOffset),i=iu(t.focusNode,t.focusOffset);if(e&&i&&e!=i){let s=i.pmViewDesc,r=n.domObserver.lastChangedTextNode;if(e==r||i==r)return r;if(!s||!s.isText(i.nodeValue))return i;if(n.input.compositionNode==i){let o=e.pmViewDesc;if(!(!o||!o.isText(e.nodeValue)))return i}}return e||i}function uf(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function Ii(n,t=!1){if(!(Jt&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),Ic(n),t||n.docView&&n.docView.dirty){let e=wr(n),i=n.state.selection;return e&&!e.eq(i)?n.dispatch(n.state.tr.setSelection(e)):(n.markCursor||t)&&!i.$from.node(i.$from.sharedDepth(i.to)).inlineContent?n.dispatch(n.state.tr.deleteSelection()):n.updateState(n.state),!0}return!1}}function ff(n,t){if(!n.dom.parentNode)return;let e=n.dom.parentNode.appendChild(document.createElement("div"));e.appendChild(t),e.style.cssText="position: fixed; left: -10000px; top: 10px";let i=getSelection(),s=document.createRange();s.selectNodeContents(t),n.dom.blur(),i.removeAllRanges(),i.addRange(s),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e),n.focus()},50)}const Fn=mt&&oe<15||sn&&au<604;at.copy=ct.cut=(n,t)=>{let e=t,i=n.state.selection,s=e.type=="cut";if(i.empty)return;let r=Fn?null:e.clipboardData,o=i.content(),{dom:l,text:a}=_r(n,o);r?(e.preventDefault(),r.clearData(),r.setData("text/html",l.innerHTML),r.setData("text/plain",a)):ff(n,l),s&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function pf(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function mf(n,t){if(!n.dom.parentNode)return;let e=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,i=n.dom.parentNode.appendChild(document.createElement(e?"textarea":"div"));e||(i.contentEditable="true"),i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus();let s=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),i.parentNode&&i.parentNode.removeChild(i),e?Vn(n,i.value,null,s,t):Vn(n,i.textContent,i.innerHTML,s,t)},50)}function Vn(n,t,e,i,s){let r=_c(n,t,e,i,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,s,r||T.empty)))return!0;if(!r)return!1;let o=pf(r),l=o?n.state.tr.replaceSelectionWith(o,i):n.state.tr.replaceSelection(r);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Rc(n){let t=n.getData("text/plain")||n.getData("Text");if(t)return t;let e=n.getData("text/uri-list");return e?e.replace(/\r?\n/g," "):""}ct.paste=(n,t)=>{let e=t;if(n.composing&&!Jt)return;let i=Fn?null:e.clipboardData,s=n.input.shiftKey&&n.input.lastKeyCode!=45;i&&Vn(n,Rc(i),i.getData("text/html"),s,e)?e.preventDefault():mf(n,e)};class Lc{constructor(t,e,i){this.slice=t,this.move=e,this.node=i}}const gf=St?"altKey":"ctrlKey";function Bc(n,t){let e=n.someProp("dragCopies",i=>!i(t));return e??!t[gf]}at.dragstart=(n,t)=>{let e=t,i=n.input.mouseDown;if(i&&i.done(),!e.dataTransfer)return;let s=n.state.selection,r=s.empty?null:n.posAtCoords(Qi(e)),o;if(!(r&&r.pos>=s.from&&r.pos<=(s instanceof D?s.to-1:s.to))){if(i&&i.mightDrag)o=D.create(n.state.doc,i.mightDrag.pos);else if(e.target&&e.target.nodeType==1){let d=n.docView.nearestDesc(e.target,!0);d&&d.node.type.spec.draggable&&d!=n.docView&&(o=D.create(n.state.doc,d.posBefore))}}let l=(o||n.state.selection).content(),{dom:a,text:c,slice:h}=_r(n,l);(!e.dataTransfer.files.length||!it||cc>120)&&e.dataTransfer.clearData(),e.dataTransfer.setData(Fn?"Text":"text/html",a.innerHTML),e.dataTransfer.effectAllowed="copyMove",Fn||e.dataTransfer.setData("text/plain",c),n.dragging=new Lc(h,Bc(n,e),o)};at.dragend=n=>{let t=n.dragging;window.setTimeout(()=>{n.dragging==t&&(n.dragging=null)},50)};ct.dragover=ct.dragenter=(n,t)=>t.preventDefault();ct.drop=(n,t)=>{let e=t,i=n.dragging;if(n.dragging=null,!e.dataTransfer)return;let s=n.posAtCoords(Qi(e));if(!s)return;let r=n.state.doc.resolve(s.pos),o=i&&i.slice;o?n.someProp("transformPasted",p=>{o=p(o,n)}):o=_c(n,Rc(e.dataTransfer),Fn?null:e.dataTransfer.getData("text/html"),!1,r);let l=!!(i&&Bc(n,e));if(n.someProp("handleDrop",p=>p(n,e,o||T.empty,l))){e.preventDefault();return}if(!o)return;e.preventDefault();let a=o?ec(n.state.doc,r.pos,o):r.pos;a==null&&(a=r.pos);let c=n.state.tr;if(l){let{node:p}=i;p?p.replace(c):c.deleteSelection()}let h=c.mapping.map(a),d=o.openStart==0&&o.openEnd==0&&o.content.childCount==1,u=c.doc;if(d?c.replaceRangeWith(h,h,o.content.firstChild):c.replaceRange(h,h,o),c.doc.eq(u))return;let f=c.doc.resolve(h);if(d&&D.isSelectable(o.content.firstChild)&&f.nodeAfter&&f.nodeAfter.sameMarkup(o.content.firstChild))c.setSelection(new D(f));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,b)=>p=b),c.setSelection(Cr(n,f,c.doc.resolve(p)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};at.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&Gt(n)},20))};at.blur=(n,t)=>{let e=t;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),e.relatedTarget&&n.dom.contains(e.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};at.beforeinput=(n,t)=>{if(it&&Jt&&t.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:i}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=i||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",r=>r(n,Me(8,"Backspace")))))return;let{$cursor:s}=n.state.selection;s&&s.pos>0&&n.dispatch(n.state.tr.delete(s.pos-1,s.pos).scrollIntoView())},50)}};for(let n in ct)at[n]=ct[n];function Hn(n,t){if(n==t)return!0;for(let e in n)if(n[e]!==t[e])return!1;for(let e in t)if(!(e in n))return!1;return!0}class Ri{constructor(t,e){this.toDOM=t,this.spec=e||Ne,this.side=this.spec.side||0}map(t,e,i,s){let{pos:r,deleted:o}=t.mapResult(e.from+s,this.side<0?-1:1);return o?null:new wt(r-i,r-i,this)}valid(){return!0}eq(t){return this==t||t instanceof Ri&&(this.spec.key&&this.spec.key==t.spec.key||this.toDOM==t.toDOM&&Hn(this.spec,t.spec))}destroy(t){this.spec.destroy&&this.spec.destroy(t)}}class ae{constructor(t,e){this.attrs=t,this.spec=e||Ne}map(t,e,i,s){let r=t.map(e.from+s,this.spec.inclusiveStart?-1:1)-i,o=t.map(e.to+s,this.spec.inclusiveEnd?1:-1)-i;return r>=o?null:new wt(r,o,this)}valid(t,e){return e.from<e.to}eq(t){return this==t||t instanceof ae&&Hn(this.attrs,t.attrs)&&Hn(this.spec,t.spec)}static is(t){return t.type instanceof ae}destroy(){}}class Dr{constructor(t,e){this.attrs=t,this.spec=e||Ne}map(t,e,i,s){let r=t.mapResult(e.from+s,1);if(r.deleted)return null;let o=t.mapResult(e.to+s,-1);return o.deleted||o.pos<=r.pos?null:new wt(r.pos-i,o.pos-i,this)}valid(t,e){let{index:i,offset:s}=t.content.findIndex(e.from),r;return s==e.from&&!(r=t.child(i)).isText&&s+r.nodeSize==e.to}eq(t){return this==t||t instanceof Dr&&Hn(this.attrs,t.attrs)&&Hn(this.spec,t.spec)}destroy(){}}class wt{constructor(t,e,i){this.from=t,this.to=e,this.type=i}copy(t,e){return new wt(t,e,this.type)}eq(t,e=0){return this.type.eq(t.type)&&this.from+e==t.from&&this.to+e==t.to}map(t,e,i){return this.type.map(t,this,e,i)}static widget(t,e,i){return new wt(t,t,new Ri(e,i))}static inline(t,e,i,s){return new wt(t,e,new ae(i,s))}static node(t,e,i,s){return new wt(t,e,new Dr(i,s))}get spec(){return this.type.spec}get inline(){return this.type instanceof ae}get widget(){return this.type instanceof Ri}}const Ue=[],Ne={};class J{constructor(t,e){this.local=t.length?t:Ue,this.children=e.length?e:Ue}static create(t,e){return e.length?Li(e,t,0,Ne):nt}find(t,e,i){let s=[];return this.findInner(t??0,e??1e9,s,0,i),s}findInner(t,e,i,s,r){for(let o=0;o<this.local.length;o++){let l=this.local[o];l.from<=e&&l.to>=t&&(!r||r(l.spec))&&i.push(l.copy(l.from+s,l.to+s))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<e&&this.children[o+1]>t){let l=this.children[o]+1;this.children[o+2].findInner(t-l,e-l,i,s+l,r)}}map(t,e,i){return this==nt||t.maps.length==0?this:this.mapInner(t,e,0,0,i||Ne)}mapInner(t,e,i,s,r){let o;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(t,i,s);a&&a.type.valid(e,a)?(o||(o=[])).push(a):r.onRemove&&r.onRemove(this.local[l].spec)}return this.children.length?yf(this.children,o||[],t,e,i,s,r):o?new J(o.sort(Pe),Ue):nt}add(t,e){return e.length?this==nt?J.create(t,e):this.addInner(t,e,0):this}addInner(t,e,i){let s,r=0;t.forEach((l,a)=>{let c=a+i,h;if(h=Fc(e,l,c)){for(s||(s=this.children.slice());r<s.length&&s[r]<a;)r+=3;s[r]==a?s[r+2]=s[r+2].addInner(l,h,c+1):s.splice(r,0,a,a+l.nodeSize,Li(h,l,c+1,Ne)),r+=3}});let o=zc(r?Vc(e):e,-i);for(let l=0;l<o.length;l++)o[l].type.valid(t,o[l])||o.splice(l--,1);return new J(o.length?this.local.concat(o).sort(Pe):this.local,s||this.children)}remove(t){return t.length==0||this==nt?this:this.removeInner(t,0)}removeInner(t,e){let i=this.children,s=this.local;for(let r=0;r<i.length;r+=3){let o,l=i[r]+e,a=i[r+1]+e;for(let h=0,d;h<t.length;h++)(d=t[h])&&d.from>l&&d.to<a&&(t[h]=null,(o||(o=[])).push(d));if(!o)continue;i==this.children&&(i=this.children.slice());let c=i[r+2].removeInner(o,l+1);c!=nt?i[r+2]=c:(i.splice(r,3),r-=3)}if(s.length){for(let r=0,o;r<t.length;r++)if(o=t[r])for(let l=0;l<s.length;l++)s[l].eq(o,e)&&(s==this.local&&(s=this.local.slice()),s.splice(l--,1))}return i==this.children&&s==this.local?this:s.length||i.length?new J(s,i):nt}forChild(t,e){if(this==nt)return this;if(e.isLeaf)return J.empty;let i,s;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=t){this.children[l]==t&&(i=this.children[l+2]);break}let r=t+1,o=r+e.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<o&&a.to>r&&a.type instanceof ae){let c=Math.max(r,a.from)-r,h=Math.min(o,a.to)-r;c<h&&(s||(s=[])).push(a.copy(c,h))}}if(s){let l=new J(s.sort(Pe),Ue);return i?new te([l,i]):l}return i||nt}eq(t){if(this==t)return!0;if(!(t instanceof J)||this.local.length!=t.local.length||this.children.length!=t.children.length)return!1;for(let e=0;e<this.local.length;e++)if(!this.local[e].eq(t.local[e]))return!1;for(let e=0;e<this.children.length;e+=3)if(this.children[e]!=t.children[e]||this.children[e+1]!=t.children[e+1]||!this.children[e+2].eq(t.children[e+2]))return!1;return!0}locals(t){return Ar(this.localsInner(t))}localsInner(t){if(this==nt)return Ue;if(t.inlineContent||!this.local.some(ae.is))return this.local;let e=[];for(let i=0;i<this.local.length;i++)this.local[i].type instanceof ae||e.push(this.local[i]);return e}forEachSet(t){t(this)}}J.empty=new J([],[]);J.removeOverlap=Ar;const nt=J.empty;class te{constructor(t){this.members=t}map(t,e){const i=this.members.map(s=>s.map(t,e,Ne));return te.from(i)}forChild(t,e){if(e.isLeaf)return J.empty;let i=[];for(let s=0;s<this.members.length;s++){let r=this.members[s].forChild(t,e);r!=nt&&(r instanceof te?i=i.concat(r.members):i.push(r))}return te.from(i)}eq(t){if(!(t instanceof te)||t.members.length!=this.members.length)return!1;for(let e=0;e<this.members.length;e++)if(!this.members[e].eq(t.members[e]))return!1;return!0}locals(t){let e,i=!0;for(let s=0;s<this.members.length;s++){let r=this.members[s].localsInner(t);if(r.length)if(!e)e=r;else{i&&(e=e.slice(),i=!1);for(let o=0;o<r.length;o++)e.push(r[o])}}return e?Ar(i?e:e.sort(Pe)):Ue}static from(t){switch(t.length){case 0:return nt;case 1:return t[0];default:return new te(t.every(e=>e instanceof J)?t:t.reduce((e,i)=>e.concat(i instanceof J?i:i.members),[]))}}forEachSet(t){for(let e=0;e<this.members.length;e++)this.members[e].forEachSet(t)}}function yf(n,t,e,i,s,r,o){let l=n.slice();for(let c=0,h=r;c<e.maps.length;c++){let d=0;e.maps[c].forEach((u,f,p,m)=>{let g=m-p-(f-u);for(let y=0;y<l.length;y+=3){let b=l[y+1];if(b<0||u>b+h-d)continue;let M=l[y]+h-d;f>=M?l[y+1]=u<=M?-2:-1:u>=h&&g&&(l[y]+=g,l[y+1]+=g)}d+=g}),h=e.maps[c].map(h,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let h=e.map(n[c]+r),d=h-s;if(d<0||d>=i.content.size){a=!0;continue}let u=e.map(n[c+1]+r,-1),f=u-s,{index:p,offset:m}=i.content.findIndex(d),g=i.maybeChild(p);if(g&&m==d&&m+g.nodeSize==f){let y=l[c+2].mapInner(e,g,h+1,n[c]+r+1,o);y!=nt?(l[c]=d,l[c+1]=f,l[c+2]=y):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=bf(l,n,t,e,s,r,o),h=Li(c,i,0,o);t=h.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,u=0;d<h.children.length;d+=3){let f=h.children[d];for(;u<l.length&&l[u]<f;)u+=3;l.splice(u,0,h.children[d],h.children[d+1],h.children[d+2])}}return new J(t.sort(Pe),l)}function zc(n,t){if(!t||!n.length)return n;let e=[];for(let i=0;i<n.length;i++){let s=n[i];e.push(new wt(s.from+t,s.to+t,s.type))}return e}function bf(n,t,e,i,s,r,o){function l(a,c){for(let h=0;h<a.local.length;h++){let d=a.local[h].map(i,s,c);d?e.push(d):o.onRemove&&o.onRemove(a.local[h].spec)}for(let h=0;h<a.children.length;h+=3)l(a.children[h+2],a.children[h]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],t[a]+r+1);return e}function Fc(n,t,e){if(t.isLeaf)return null;let i=e+t.nodeSize,s=null;for(let r=0,o;r<n.length;r++)(o=n[r])&&o.from>e&&o.to<i&&((s||(s=[])).push(o),n[r]=null);return s}function Vc(n){let t=[];for(let e=0;e<n.length;e++)n[e]!=null&&t.push(n[e]);return t}function Li(n,t,e,i){let s=[],r=!1;t.forEach((l,a)=>{let c=Fc(n,l,a+e);if(c){r=!0;let h=Li(c,l,e+a+1,i);h!=nt&&s.push(a,a+l.nodeSize,h)}});let o=zc(r?Vc(n):n,-e).sort(Pe);for(let l=0;l<o.length;l++)o[l].type.valid(t,o[l])||(i.onRemove&&i.onRemove(o[l].spec),o.splice(l--,1));return o.length||s.length?new J(o,s):nt}function Pe(n,t){return n.from-t.from||n.to-t.to}function Ar(n){let t=n;for(let e=0;e<t.length-1;e++){let i=t[e];if(i.from!=i.to)for(let s=e+1;s<t.length;s++){let r=t[s];if(r.from==i.from){r.to!=i.to&&(t==n&&(t=n.slice()),t[s]=r.copy(r.from,i.to),Wo(t,s+1,r.copy(i.to,r.to)));continue}else{r.from<i.to&&(t==n&&(t=n.slice()),t[e]=i.copy(i.from,r.from),Wo(t,s,i.copy(r.from,i.to)));break}}}return t}function Wo(n,t,e){for(;t<n.length&&Pe(e,n[t])>0;)t++;n.splice(t,0,e)}function ws(n){let t=[];return n.someProp("decorations",e=>{let i=e(n.state);i&&i!=nt&&t.push(i)}),n.cursorWrapper&&t.push(J.create(n.state.doc,[n.cursorWrapper.deco])),te.from(t)}const xf={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},kf=mt&&oe<=11;class Sf{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(t){this.anchorNode=t.anchorNode,this.anchorOffset=t.anchorOffset,this.focusNode=t.focusNode,this.focusOffset=t.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(t){return t.anchorNode==this.anchorNode&&t.anchorOffset==this.anchorOffset&&t.focusNode==this.focusNode&&t.focusOffset==this.focusOffset}}class Mf{constructor(t,e){this.view=t,this.handleDOMChange=e,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Sf,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(i=>{for(let s=0;s<i.length;s++)this.queue.push(i[s]);mt&&oe<=11&&i.some(s=>s.type=="childList"&&s.removedNodes.length||s.type=="characterData"&&s.oldValue.length>s.target.nodeValue.length)?this.flushSoon():this.flush()}),kf&&(this.onCharData=i=>{this.queue.push({target:i.target,type:"characterData",oldValue:i.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,xf)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let t=this.observer.takeRecords();if(t.length){for(let e=0;e<t.length;e++)this.queue.push(t[e]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(Ro(this.view)){if(this.suppressingSelectionUpdates)return Gt(this.view);if(mt&&oe<=11&&!this.view.state.selection.empty){let t=this.view.domSelectionRange();if(t.focusNode&&ze(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(t){if(!t.focusNode)return!0;let e=new Set,i;for(let r=t.focusNode;r;r=nn(r))e.add(r);for(let r=t.anchorNode;r;r=nn(r))if(e.has(r)){i=r;break}let s=i&&this.view.docView.nearestDesc(i);if(s&&s.ignoreMutation({type:"selection",target:i.nodeType==3?i.parentNode:i}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}flush(){let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let e=this.pendingRecords();e.length&&(this.queue=[]);let i=t.domSelectionRange(),s=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(i)&&Ro(t)&&!this.ignoreSelectionChange(i),r=-1,o=-1,l=!1,a=[];if(t.editable)for(let h=0;h<e.length;h++){let d=this.registerMutation(e[h],a);d&&(r=r<0?d.from:Math.min(d.from,r),o=o<0?d.to:Math.max(d.to,o),d.typeOver&&(l=!0))}if(Et&&a.length){let h=a.filter(d=>d.nodeName=="BR");if(h.length==2){let[d,u]=h;d.parentNode&&d.parentNode.parentNode==u.parentNode?u.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let u of h){let f=u.parentNode;f&&f.nodeName=="LI"&&(!d||_f(t,d)!=f)&&u.remove()}}}let c=null;r<0&&s&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&Yi(i)&&(c=wr(t))&&c.eq(N.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,Gt(t),this.currentSelection.set(i),t.scrollToSelection()):(r>-1||s)&&(r>-1&&(t.docView.markDirty(r,o),wf(t)),this.handleDOMChange(r,o,l,a),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(i)||Gt(t),this.currentSelection.set(i))}registerMutation(t,e){if(e.indexOf(t.target)>-1)return null;let i=this.view.docView.nearestDesc(t.target);if(t.type=="attributes"&&(i==this.view.docView||t.attributeName=="contenteditable"||t.attributeName=="style"&&!t.oldValue&&!t.target.getAttribute("style"))||!i||i.ignoreMutation(t))return null;if(t.type=="childList"){for(let h=0;h<t.addedNodes.length;h++){let d=t.addedNodes[h];e.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(i.contentDOM&&i.contentDOM!=i.dom&&!i.contentDOM.contains(t.target))return{from:i.posBefore,to:i.posAfter};let s=t.previousSibling,r=t.nextSibling;if(mt&&oe<=11&&t.addedNodes.length)for(let h=0;h<t.addedNodes.length;h++){let{previousSibling:d,nextSibling:u}=t.addedNodes[h];(!d||Array.prototype.indexOf.call(t.addedNodes,d)<0)&&(s=d),(!u||Array.prototype.indexOf.call(t.addedNodes,u)<0)&&(r=u)}let o=s&&s.parentNode==t.target?tt(s)+1:0,l=i.localPosFromDOM(t.target,o,-1),a=r&&r.parentNode==t.target?tt(r):t.target.childNodes.length,c=i.localPosFromDOM(t.target,a,1);return{from:l,to:c}}else return t.type=="attributes"?{from:i.posAtStart-i.border,to:i.posAtEnd+i.border}:(this.lastChangedTextNode=t.target,{from:i.posAtStart,to:i.posAtEnd,typeOver:t.target.nodeValue==t.oldValue})}}let jo=new WeakMap,Ko=!1;function wf(n){if(!jo.has(n)&&(jo.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=Et,Ko)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ko=!0}}function qo(n,t){let e=t.startContainer,i=t.startOffset,s=t.endContainer,r=t.endOffset,o=n.domAtPos(n.state.selection.anchor);return ze(o.node,o.offset,s,r)&&([e,i,s,r]=[s,r,e,i]),{anchorNode:e,anchorOffset:i,focusNode:s,focusOffset:r}}function Cf(n,t){if(t.getComposedRanges){let s=t.getComposedRanges(n.root)[0];if(s)return qo(n,s)}let e;function i(s){s.preventDefault(),s.stopImmediatePropagation(),e=s.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",i,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",i,!0),e?qo(n,e):null}function _f(n,t){for(let e=t.parentNode;e&&e!=n.dom;e=e.parentNode){let i=n.docView.nearestDesc(e,!0);if(i&&i.node.isBlock)return e}return null}function Tf(n,t,e){let{node:i,fromOffset:s,toOffset:r,from:o,to:l}=n.docView.parseRange(t,e),a=n.domSelectionRange(),c,h=a.anchorNode;if(h&&n.dom.contains(h.nodeType==1?h:h.parentNode)&&(c=[{node:h,offset:a.anchorOffset}],Yi(a)||c.push({node:a.focusNode,offset:a.focusOffset})),it&&n.input.lastKeyCode===8)for(let g=r;g>s;g--){let y=i.childNodes[g-1],b=y.pmViewDesc;if(y.nodeName=="BR"&&!b){r=g;break}if(!b||b.size)break}let d=n.state.doc,u=n.someProp("domParser")||re.fromSchema(n.state.schema),f=d.resolve(o),p=null,m=u.parse(i,{topNode:f.parent,topMatch:f.parent.contentMatchAt(f.index()),topOpen:!0,from:s,to:r,preserveWhitespace:f.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:Of,context:f});if(c&&c[0].pos!=null){let g=c[0].pos,y=c[1]&&c[1].pos;y==null&&(y=g),p={anchor:g+o,head:y+o}}return{doc:m,sel:p,from:o,to:l}}function Of(n){let t=n.pmViewDesc;if(t)return t.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(lt&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}else if(n.parentNode.lastChild==n||lt&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}const vf=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function Df(n,t,e,i,s){let r=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,t<0){let k=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,v=wr(n,k);if(v&&!n.state.selection.eq(v)){if(it&&Jt&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",R=>R(n,Me(13,"Enter"))))return;let A=n.state.tr.setSelection(v);k=="pointer"?A.setMeta("pointer",!0):k=="key"&&A.scrollIntoView(),r&&A.setMeta("composition",r),n.dispatch(A)}return}let o=n.state.doc.resolve(t),l=o.sharedDepth(e);t=o.before(l+1),e=n.state.doc.resolve(e).after(l+1);let a=n.state.selection,c=Tf(n,t,e),h=n.state.doc,d=h.slice(c.from,c.to),u,f;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(u=n.state.selection.to,f="end"):(u=n.state.selection.from,f="start"),n.input.lastKeyCode=null;let p=Nf(d.content,c.doc.content,c.from,u,f);if(p&&n.input.domChangeCount++,(sn&&n.input.lastIOSEnter>Date.now()-225||Jt)&&s.some(k=>k.nodeType==1&&!vf.test(k.nodeName))&&(!p||p.endA>=p.endB)&&n.someProp("handleKeyDown",k=>k(n,Me(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!p)if(i&&a instanceof E&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let k=Jo(n,n.state.doc,c.sel);if(k&&!k.eq(n.state.selection)){let v=n.state.tr.setSelection(k);r&&v.setMeta("composition",r),n.dispatch(v)}}return}n.state.selection.from<n.state.selection.to&&p.start==p.endB&&n.state.selection instanceof E&&(p.start>n.state.selection.from&&p.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?p.start=n.state.selection.from:p.endA<n.state.selection.to&&p.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(p.endB+=n.state.selection.to-p.endA,p.endA=n.state.selection.to)),mt&&oe<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)=="  "&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),y=h.resolve(p.start),b=m.sameParent(g)&&m.parent.inlineContent&&y.end()>=p.endA,M;if((sn&&n.input.lastIOSEnter>Date.now()-225&&(!b||s.some(k=>k.nodeName=="DIV"||k.nodeName=="P"))||!b&&m.pos<c.doc.content.size&&(!m.sameParent(g)||!m.parent.inlineContent)&&!/\S/.test(c.doc.textBetween(m.pos,g.pos,"",""))&&(M=N.findFrom(c.doc.resolve(m.pos+1),1,!0))&&M.head>m.pos)&&n.someProp("handleKeyDown",k=>k(n,Me(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>p.start&&Ef(h,p.start,p.endA,m,g)&&n.someProp("handleKeyDown",k=>k(n,Me(8,"Backspace")))){Jt&&it&&n.domObserver.suppressSelectionUpdates();return}it&&p.endB==p.start&&(n.input.lastChromeDelete=Date.now()),Jt&&!b&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(k){return k(n,Me(13,"Enter"))})},20));let w=p.start,x=p.endA,C=k=>{let v=k||n.state.tr.replace(w,x,c.doc.slice(p.start-c.from,p.endB-c.from));if(c.sel){let A=Jo(n,v.doc,c.sel);A&&!(it&&n.composing&&A.empty&&(p.start!=p.endB||n.input.lastChromeDelete<Date.now()-100)&&(A.head==w||A.head==v.mapping.map(x)-1)||mt&&A.empty&&A.head==w)&&v.setSelection(A)}return r&&v.setMeta("composition",r),v.scrollIntoView()},_;if(b){if(m.pos==g.pos){mt&&oe<=11&&m.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>Gt(n),20));let k=C(n.state.tr.delete(w,x)),v=h.resolve(p.start).marksAcross(h.resolve(p.endA));v&&k.ensureMarks(v),n.dispatch(k)}else if(p.endA==p.endB&&(_=Af(m.parent.content.cut(m.parentOffset,g.parentOffset),y.parent.content.cut(y.parentOffset,p.endA-y.start())))){let k=C(n.state.tr);_.type=="add"?k.addMark(w,x,_.mark):k.removeMark(w,x,_.mark),n.dispatch(k)}else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let k=m.parent.textBetween(m.parentOffset,g.parentOffset),v=()=>C(n.state.tr.insertText(k,w,x));n.someProp("handleTextInput",A=>A(n,w,x,k,v))||n.dispatch(v())}}else n.dispatch(C())}function Jo(n,t,e){return Math.max(e.anchor,e.head)>t.content.size?null:Cr(n,t.resolve(e.anchor),t.resolve(e.head))}function Af(n,t){let e=n.firstChild.marks,i=t.firstChild.marks,s=e,r=i,o,l,a;for(let h=0;h<i.length;h++)s=i[h].removeFromSet(s);for(let h=0;h<e.length;h++)r=e[h].removeFromSet(r);if(s.length==1&&r.length==0)l=s[0],o="add",a=h=>h.mark(l.addToSet(h.marks));else if(s.length==0&&r.length==1)l=r[0],o="remove",a=h=>h.mark(l.removeFromSet(h.marks));else return null;let c=[];for(let h=0;h<t.childCount;h++)c.push(a(t.child(h)));if(S.from(c).eq(n))return{mark:l,type:o}}function Ef(n,t,e,i,s){if(e-t<=s.pos-i.pos||Cs(i,!0,!1)<s.pos)return!1;let r=n.resolve(t);if(!i.parent.isTextblock){let l=r.nodeAfter;return l!=null&&e==t+l.nodeSize}if(r.parentOffset<r.parent.content.size||!r.parent.isTextblock)return!1;let o=n.resolve(Cs(r,!0,!0));return!o.parent.isTextblock||o.pos>e||Cs(o,!0,!1)<e?!1:i.parent.content.cut(i.parentOffset).eq(o.parent.content)}function Cs(n,t,e){let i=n.depth,s=t?n.end():n.pos;for(;i>0&&(t||n.indexAfter(i)==n.node(i).childCount);)i--,s++,t=!1;if(e){let r=n.node(i).maybeChild(n.indexAfter(i));for(;r&&!r.isLeaf;)r=r.firstChild,s++}return s}function Nf(n,t,e,i,s){let r=n.findDiffStart(t,e);if(r==null)return null;let{a:o,b:l}=n.findDiffEnd(t,e+n.size,e+t.size);if(s=="end"){let a=Math.max(0,r-Math.min(o,l));i-=o+a-r}if(o<r&&n.size<t.size){let a=i<=r&&i>=o?r-i:0;r-=a,r&&r<t.size&&Uo(t.textBetween(r-1,r+1))&&(r+=a?1:-1),l=r+(l-o),o=r}else if(l<r){let a=i<=r&&i>=l?r-i:0;r-=a,r&&r<n.size&&Uo(n.textBetween(r-1,r+1))&&(r+=a?1:-1),o=r+(o-l),l=r}return{start:r,endA:o,endB:l}}function Uo(n){if(n.length!=2)return!1;let t=n.charCodeAt(0),e=n.charCodeAt(1);return t>=56320&&t<=57343&&e>=55296&&e<=56319}class Hc{constructor(t,e){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Yu,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=e,this.state=e.state,this.directPlugins=e.plugins||[],this.directPlugins.forEach(Zo),this.dispatch=this.dispatch.bind(this),this.dom=t&&t.mount||document.createElement("div"),t&&(t.appendChild?t.appendChild(this.dom):typeof t=="function"?t(this.dom):t.mount&&(this.mounted=!0)),this.editable=Xo(this),Yo(this),this.nodeViews=Qo(this),this.docView=Do(this.state.doc,Go(this),ws(this),this.dom,this),this.domObserver=new Mf(this,(i,s,r,o)=>Df(this,i,s,r,o)),this.domObserver.start(),Xu(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let t=this._props;this._props={};for(let e in t)this._props[e]=t[e];this._props.state=this.state}return this._props}update(t){t.handleDOMEvents!=this._props.handleDOMEvents&&sr(this);let e=this._props;this._props=t,t.plugins&&(t.plugins.forEach(Zo),this.directPlugins=t.plugins),this.updateStateInner(t.state,e)}setProps(t){let e={};for(let i in this._props)e[i]=this._props[i];e.state=this.state;for(let i in t)e[i]=t[i];this.update(e)}updateState(t){this.updateStateInner(t,this._props)}updateStateInner(t,e){var i;let s=this.state,r=!1,o=!1;t.storedMarks&&this.composing&&(Ic(this),o=!0),this.state=t;let l=s.plugins!=t.plugins||this._props.plugins!=e.plugins;if(l||this._props.plugins!=e.plugins||this._props.nodeViews!=e.nodeViews){let f=Qo(this);If(f,this.nodeViews)&&(this.nodeViews=f,r=!0)}(l||e.handleDOMEvents!=this._props.handleDOMEvents)&&sr(this),this.editable=Xo(this),Yo(this);let a=ws(this),c=Go(this),h=s.plugins!=t.plugins&&!s.doc.eq(t.doc)?"reset":t.scrollToSelection>s.scrollToSelection?"to selection":"preserve",d=r||!this.docView.matchesNode(t.doc,c,a);(d||!t.selection.eq(s.selection))&&(o=!0);let u=h=="preserve"&&o&&this.dom.style.overflowAnchor==null&&du(this);if(o){this.domObserver.stop();let f=d&&(mt||it)&&!this.composing&&!s.selection.empty&&!t.selection.empty&&Pf(s.selection,t.selection);if(d){let p=it?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=df(this)),(r||!this.docView.update(t.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=Do(t.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(f=!0)}f||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&Ru(this))?Gt(this,f):(Mc(this,t.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(s),!((i=this.dragging)===null||i===void 0)&&i.node&&!s.doc.eq(t.doc)&&this.updateDraggedNode(this.dragging,s),h=="reset"?this.dom.scrollTop=0:h=="to selection"?this.scrollToSelection():u&&uu(u)}scrollToSelection(){let t=this.domSelectionRange().focusNode;if(!(!t||!this.dom.contains(t.nodeType==1?t:t.parentNode))){if(!this.someProp("handleScrollToSelection",e=>e(this)))if(this.state.selection instanceof D){let e=this.docView.domAfterPos(this.state.selection.from);e.nodeType==1&&wo(this,e.getBoundingClientRect(),t)}else wo(this,this.coordsAtPos(this.state.selection.head,1),t)}}destroyPluginViews(){let t;for(;t=this.pluginViews.pop();)t.destroy&&t.destroy()}updatePluginViews(t){if(!t||t.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let i=this.directPlugins[e];i.spec.view&&this.pluginViews.push(i.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let i=this.state.plugins[e];i.spec.view&&this.pluginViews.push(i.spec.view(this))}}else for(let e=0;e<this.pluginViews.length;e++){let i=this.pluginViews[e];i.update&&i.update(this,t)}}updateDraggedNode(t,e){let i=t.node,s=-1;if(this.state.doc.nodeAt(i.from)==i.node)s=i.from;else{let r=i.from+(this.state.doc.content.size-e.doc.content.size);(r>0&&this.state.doc.nodeAt(r))==i.node&&(s=r)}this.dragging=new Lc(t.slice,t.move,s<0?void 0:D.create(this.state.doc,s))}someProp(t,e){let i=this._props&&this._props[t],s;if(i!=null&&(s=e?e(i):i))return s;for(let o=0;o<this.directPlugins.length;o++){let l=this.directPlugins[o].props[t];if(l!=null&&(s=e?e(l):l))return s}let r=this.state.plugins;if(r)for(let o=0;o<r.length;o++){let l=r[o].props[t];if(l!=null&&(s=e?e(l):l))return s}}hasFocus(){if(mt){let t=this.root.activeElement;if(t==this.dom)return!0;if(!t||!this.dom.contains(t))return!1;for(;t&&this.dom!=t&&this.dom.contains(t);){if(t.contentEditable=="false")return!1;t=t.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&fu(this.dom),Gt(this),this.domObserver.start()}get root(){let t=this._root;if(t==null){for(let e=this.dom.parentNode;e;e=e.parentNode)if(e.nodeType==9||e.nodeType==11&&e.host)return e.getSelection||(Object.getPrototypeOf(e).getSelection=()=>e.ownerDocument.getSelection()),this._root=e}return t||document}updateRoot(){this._root=null}posAtCoords(t){return bu(this,t)}coordsAtPos(t,e=1){return pc(this,t,e)}domAtPos(t,e=0){return this.docView.domFromPos(t,e)}nodeDOM(t){let e=this.docView.descAt(t);return e?e.nodeDOM:null}posAtDOM(t,e,i=-1){let s=this.docView.posFromDOM(t,e,i);if(s==null)throw new RangeError("DOM position not inside the editor");return s}endOfTextblock(t,e){return wu(this,e||this.state,t)}pasteHTML(t,e){return Vn(this,"",t,!1,e||new ClipboardEvent("paste"))}pasteText(t,e){return Vn(this,t,null,!0,e||new ClipboardEvent("paste"))}serializeForClipboard(t){return _r(this,t)}destroy(){this.docView&&(Qu(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],ws(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,tu())}get isDestroyed(){return this.docView==null}dispatchEvent(t){return tf(this,t)}domSelectionRange(){let t=this.domSelection();return t?lt&&this.root.nodeType===11&&ru(this.dom.ownerDocument)==this.dom&&Cf(this,t)||t:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}Hc.prototype.dispatch=function(n){let t=this._props.dispatchTransaction;t?t.call(this,n):this.updateState(this.state.apply(n))};function Go(n){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(n.editable),n.someProp("attributes",e=>{if(typeof e=="function"&&(e=e(n.state)),e)for(let i in e)i=="class"?t.class+=" "+e[i]:i=="style"?t.style=(t.style?t.style+";":"")+e[i]:!t[i]&&i!="contenteditable"&&i!="nodeName"&&(t[i]=String(e[i]))}),t.translate||(t.translate="no"),[wt.node(0,n.state.doc.content.size,t)]}function Yo(n){if(n.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),n.cursorWrapper={dom:t,deco:wt.widget(n.state.selection.from,t,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function Xo(n){return!n.someProp("editable",t=>t(n.state)===!1)}function Pf(n,t){let e=Math.min(n.$anchor.sharedDepth(n.head),t.$anchor.sharedDepth(t.head));return n.$anchor.start(e)!=t.$anchor.start(e)}function Qo(n){let t=Object.create(null);function e(i){for(let s in i)Object.prototype.hasOwnProperty.call(t,s)||(t[s]=i[s])}return n.someProp("nodeViews",e),n.someProp("markViews",e),t}function If(n,t){let e=0,i=0;for(let s in n){if(n[s]!=t[s])return!0;e++}for(let s in t)i++;return e!=i}function Zo(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}const Rf=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),Lf=typeof navigator<"u"&&/Win/.test(navigator.platform);function Bf(n){let t=n.split(/-(?!$)/),e=t[t.length-1];e=="Space"&&(e=" ");let i,s,r,o;for(let l=0;l<t.length-1;l++){let a=t[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))i=!0;else if(/^(c|ctrl|control)$/i.test(a))s=!0;else if(/^s(hift)?$/i.test(a))r=!0;else if(/^mod$/i.test(a))Rf?o=!0:s=!0;else throw new Error("Unrecognized modifier name: "+a)}return i&&(e="Alt-"+e),s&&(e="Ctrl-"+e),o&&(e="Meta-"+e),r&&(e="Shift-"+e),e}function zf(n){let t=Object.create(null);for(let e in n)t[Bf(e)]=n[e];return t}function _s(n,t,e=!0){return t.altKey&&(n="Alt-"+n),t.ctrlKey&&(n="Ctrl-"+n),t.metaKey&&(n="Meta-"+n),e&&t.shiftKey&&(n="Shift-"+n),n}function Ff(n){return new yt({props:{handleKeyDown:$c(n)}})}function $c(n){let t=zf(n);return function(e,i){let s=nd(i),r,o=t[_s(s,i)];if(o&&o(e.state,e.dispatch,e))return!0;if(s.length==1&&s!=" "){if(i.shiftKey){let l=t[_s(s,i,!1)];if(l&&l(e.state,e.dispatch,e))return!0}if((i.altKey||i.metaKey||i.ctrlKey)&&!(Lf&&i.ctrlKey&&i.altKey)&&(r=id[i.keyCode])&&r!=s){let l=t[_s(r,i)];if(l&&l(e.state,e.dispatch,e))return!0}}return!1}}const Er=(n,t)=>n.selection.empty?!1:(t&&t(n.tr.deleteSelection().scrollIntoView()),!0);function Wc(n,t){let{$cursor:e}=n.selection;return!e||(t?!t.endOfTextblock("backward",n):e.parentOffset>0)?null:e}const jc=(n,t,e)=>{let i=Wc(n,e);if(!i)return!1;let s=Nr(i);if(!s){let o=i.blockRange(),l=o&&dn(o);return l==null?!1:(t&&t(n.tr.lift(o,l).scrollIntoView()),!0)}let r=s.nodeBefore;if(Zc(n,s,t,-1))return!0;if(i.parent.content.size==0&&(rn(r,"end")||D.isSelectable(r)))for(let o=i.depth;;o--){let l=Ui(n.doc,i.before(o),i.after(o),T.empty);if(l&&l.slice.size<l.to-l.from){if(t){let a=n.tr.step(l);a.setSelection(rn(r,"end")?N.findFrom(a.doc.resolve(a.mapping.map(s.pos,-1)),-1):D.create(a.doc,s.pos-r.nodeSize)),t(a.scrollIntoView())}return!0}if(o==1||i.node(o-1).childCount>1)break}return r.isAtom&&s.depth==i.depth-1?(t&&t(n.tr.delete(s.pos-r.nodeSize,s.pos).scrollIntoView()),!0):!1},Vf=(n,t,e)=>{let i=Wc(n,e);if(!i)return!1;let s=Nr(i);return s?Kc(n,s,t):!1},Hf=(n,t,e)=>{let i=Jc(n,e);if(!i)return!1;let s=Pr(i);return s?Kc(n,s,t):!1};function Kc(n,t,e){let i=t.nodeBefore,s=i,r=t.pos-1;for(;!s.isTextblock;r--){if(s.type.spec.isolating)return!1;let h=s.lastChild;if(!h)return!1;s=h}let o=t.nodeAfter,l=o,a=t.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let h=l.firstChild;if(!h)return!1;l=h}let c=Ui(n.doc,r,a,T.empty);if(!c||c.from!=r||c instanceof G&&c.slice.size>=a-r)return!1;if(e){let h=n.tr.step(c);h.setSelection(E.create(h.doc,r)),e(h.scrollIntoView())}return!0}function rn(n,t,e=!1){for(let i=n;i;i=t=="start"?i.firstChild:i.lastChild){if(i.isTextblock)return!0;if(e&&i.childCount!=1)return!1}return!1}const qc=(n,t,e)=>{let{$head:i,empty:s}=n.selection,r=i;if(!s)return!1;if(i.parent.isTextblock){if(e?!e.endOfTextblock("backward",n):i.parentOffset>0)return!1;r=Nr(i)}let o=r&&r.nodeBefore;return!o||!D.isSelectable(o)?!1:(t&&t(n.tr.setSelection(D.create(n.doc,r.pos-o.nodeSize)).scrollIntoView()),!0)};function Nr(n){if(!n.parent.type.spec.isolating)for(let t=n.depth-1;t>=0;t--){if(n.index(t)>0)return n.doc.resolve(n.before(t+1));if(n.node(t).type.spec.isolating)break}return null}function Jc(n,t){let{$cursor:e}=n.selection;return!e||(t?!t.endOfTextblock("forward",n):e.parentOffset<e.parent.content.size)?null:e}const Uc=(n,t,e)=>{let i=Jc(n,e);if(!i)return!1;let s=Pr(i);if(!s)return!1;let r=s.nodeAfter;if(Zc(n,s,t,1))return!0;if(i.parent.content.size==0&&(rn(r,"start")||D.isSelectable(r))){let o=Ui(n.doc,i.before(),i.after(),T.empty);if(o&&o.slice.size<o.to-o.from){if(t){let l=n.tr.step(o);l.setSelection(rn(r,"start")?N.findFrom(l.doc.resolve(l.mapping.map(s.pos)),1):D.create(l.doc,l.mapping.map(s.pos))),t(l.scrollIntoView())}return!0}}return r.isAtom&&s.depth==i.depth-1?(t&&t(n.tr.delete(s.pos,s.pos+r.nodeSize).scrollIntoView()),!0):!1},Gc=(n,t,e)=>{let{$head:i,empty:s}=n.selection,r=i;if(!s)return!1;if(i.parent.isTextblock){if(e?!e.endOfTextblock("forward",n):i.parentOffset<i.parent.content.size)return!1;r=Pr(i)}let o=r&&r.nodeAfter;return!o||!D.isSelectable(o)?!1:(t&&t(n.tr.setSelection(D.create(n.doc,r.pos)).scrollIntoView()),!0)};function Pr(n){if(!n.parent.type.spec.isolating)for(let t=n.depth-1;t>=0;t--){let e=n.node(t);if(n.index(t)+1<e.childCount)return n.doc.resolve(n.after(t+1));if(e.type.spec.isolating)break}return null}const $f=(n,t)=>{let e=n.selection,i=e instanceof D,s;if(i){if(e.node.isTextblock||!de(n.doc,e.from))return!1;s=e.from}else if(s=Ji(n.doc,e.from,-1),s==null)return!1;if(t){let r=n.tr.join(s);i&&r.setSelection(D.create(r.doc,s-n.doc.resolve(s).nodeBefore.nodeSize)),t(r.scrollIntoView())}return!0},Wf=(n,t)=>{let e=n.selection,i;if(e instanceof D){if(e.node.isTextblock||!de(n.doc,e.to))return!1;i=e.to}else if(i=Ji(n.doc,e.to,1),i==null)return!1;return t&&t(n.tr.join(i).scrollIntoView()),!0},jf=(n,t)=>{let{$from:e,$to:i}=n.selection,s=e.blockRange(i),r=s&&dn(s);return r==null?!1:(t&&t(n.tr.lift(s,r).scrollIntoView()),!0)},Yc=(n,t)=>{let{$head:e,$anchor:i}=n.selection;return!e.parent.type.spec.code||!e.sameParent(i)?!1:(t&&t(n.tr.insertText(`
`).scrollIntoView()),!0)};function Ir(n){for(let t=0;t<n.edgeCount;t++){let{type:e}=n.edge(t);if(e.isTextblock&&!e.hasRequiredAttrs())return e}return null}const Kf=(n,t)=>{let{$head:e,$anchor:i}=n.selection;if(!e.parent.type.spec.code||!e.sameParent(i))return!1;let s=e.node(-1),r=e.indexAfter(-1),o=Ir(s.contentMatchAt(r));if(!o||!s.canReplaceWith(r,r,o))return!1;if(t){let l=e.after(),a=n.tr.replaceWith(l,l,o.createAndFill());a.setSelection(N.near(a.doc.resolve(l),1)),t(a.scrollIntoView())}return!0},Xc=(n,t)=>{let e=n.selection,{$from:i,$to:s}=e;if(e instanceof xt||i.parent.inlineContent||s.parent.inlineContent)return!1;let r=Ir(s.parent.contentMatchAt(s.indexAfter()));if(!r||!r.isTextblock)return!1;if(t){let o=(!i.parentOffset&&s.index()<s.parent.childCount?i:s).pos,l=n.tr.insert(o,r.createAndFill());l.setSelection(E.create(l.doc,o+1)),t(l.scrollIntoView())}return!0},Qc=(n,t)=>{let{$cursor:e}=n.selection;if(!e||e.parent.content.size)return!1;if(e.depth>1&&e.after()!=e.end(-1)){let r=e.before();if(Ut(n.doc,r))return t&&t(n.tr.split(r).scrollIntoView()),!0}let i=e.blockRange(),s=i&&dn(i);return s==null?!1:(t&&t(n.tr.lift(i,s).scrollIntoView()),!0)};function qf(n){return(t,e)=>{let{$from:i,$to:s}=t.selection;if(t.selection instanceof D&&t.selection.node.isBlock)return!i.parentOffset||!Ut(t.doc,i.pos)?!1:(e&&e(t.tr.split(i.pos).scrollIntoView()),!0);if(!i.depth)return!1;let r=[],o,l,a=!1,c=!1;for(let f=i.depth;;f--)if(i.node(f).isBlock){a=i.end(f)==i.pos+(i.depth-f),c=i.start(f)==i.pos-(i.depth-f),l=Ir(i.node(f-1).contentMatchAt(i.indexAfter(f-1))),r.unshift(a&&l?{type:l}:null),o=f;break}else{if(f==1)return!1;r.unshift(null)}let h=t.tr;(t.selection instanceof E||t.selection instanceof xt)&&h.deleteSelection();let d=h.mapping.map(i.pos),u=Ut(h.doc,d,r.length,r);if(u||(r[0]=l?{type:l}:null,u=Ut(h.doc,d,r.length,r)),!u)return!1;if(h.split(d,r.length,r),!a&&c&&i.node(o).type!=l){let f=h.mapping.map(i.before(o)),p=h.doc.resolve(f);l&&i.node(o-1).canReplaceWith(p.index(),p.index()+1,l)&&h.setNodeMarkup(h.mapping.map(i.before(o)),l)}return e&&e(h.scrollIntoView()),!0}}const Jf=qf(),Uf=(n,t)=>{let{$from:e,to:i}=n.selection,s,r=e.sharedDepth(i);return r==0?!1:(s=e.before(r),t&&t(n.tr.setSelection(D.create(n.doc,s))),!0)};function Gf(n,t,e){let i=t.nodeBefore,s=t.nodeAfter,r=t.index();return!i||!s||!i.type.compatibleContent(s.type)?!1:!i.content.size&&t.parent.canReplace(r-1,r)?(e&&e(n.tr.delete(t.pos-i.nodeSize,t.pos).scrollIntoView()),!0):!t.parent.canReplace(r,r+1)||!(s.isTextblock||de(n.doc,t.pos))?!1:(e&&e(n.tr.join(t.pos).scrollIntoView()),!0)}function Zc(n,t,e,i){let s=t.nodeBefore,r=t.nodeAfter,o,l,a=s.type.spec.isolating||r.type.spec.isolating;if(!a&&Gf(n,t,e))return!0;let c=!a&&t.parent.canReplace(t.index(),t.index()+1);if(c&&(o=(l=s.contentMatchAt(s.childCount)).findWrapping(r.type))&&l.matchType(o[0]||r.type).validEnd){if(e){let f=t.pos+r.nodeSize,p=S.empty;for(let y=o.length-1;y>=0;y--)p=S.from(o[y].create(null,p));p=S.from(s.copy(p));let m=n.tr.step(new Y(t.pos-1,f,t.pos,f,new T(p,1,0),o.length,!0)),g=m.doc.resolve(f+2*o.length);g.nodeAfter&&g.nodeAfter.type==s.type&&de(m.doc,g.pos)&&m.join(g.pos),e(m.scrollIntoView())}return!0}let h=r.type.spec.isolating||i>0&&a?null:N.findFrom(t,1),d=h&&h.$from.blockRange(h.$to),u=d&&dn(d);if(u!=null&&u>=t.depth)return e&&e(n.tr.lift(d,u).scrollIntoView()),!0;if(c&&rn(r,"start",!0)&&rn(s,"end")){let f=s,p=[];for(;p.push(f),!f.isTextblock;)f=f.lastChild;let m=r,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(f.canReplace(f.childCount,f.childCount,m.content)){if(e){let y=S.empty;for(let M=p.length-1;M>=0;M--)y=S.from(p[M].copy(y));let b=n.tr.step(new Y(t.pos-p.length,t.pos+r.nodeSize,t.pos+g,t.pos+r.nodeSize-g,new T(y,p.length,0),0,!0));e(b.scrollIntoView())}return!0}}return!1}function th(n){return function(t,e){let i=t.selection,s=n<0?i.$from:i.$to,r=s.depth;for(;s.node(r).isInline;){if(!r)return!1;r--}return s.node(r).isTextblock?(e&&e(t.tr.setSelection(E.create(t.doc,n<0?s.start(r):s.end(r)))),!0):!1}}const Yf=th(-1),Xf=th(1);function Qf(n,t=null){return function(e,i){let{$from:s,$to:r}=e.selection,o=s.blockRange(r),l=o&&kr(o,n,t);return l?(i&&i(e.tr.wrap(o,l).scrollIntoView()),!0):!1}}function tl(n,t=null){return function(e,i){let s=!1;for(let r=0;r<e.selection.ranges.length&&!s;r++){let{$from:{pos:o},$to:{pos:l}}=e.selection.ranges[r];e.doc.nodesBetween(o,l,(a,c)=>{if(s)return!1;if(!(!a.isTextblock||a.hasMarkup(n,t)))if(a.type==n)s=!0;else{let h=e.doc.resolve(c),d=h.index();s=h.parent.canReplaceWith(d,d+1,n)}})}if(!s)return!1;if(i){let r=e.tr;for(let o=0;o<e.selection.ranges.length;o++){let{$from:{pos:l},$to:{pos:a}}=e.selection.ranges[o];r.setBlockType(l,a,n,t)}i(r.scrollIntoView())}return!0}}function Rr(...n){return function(t,e,i){for(let s=0;s<n.length;s++)if(n[s](t,e,i))return!0;return!1}}Rr(Er,jc,qc);Rr(Er,Uc,Gc);Rr(Yc,Xc,Qc,Jf);typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform()=="darwin";function Zf(n,t=null){return function(e,i){let{$from:s,$to:r}=e.selection,o=s.blockRange(r);if(!o)return!1;let l=i?e.tr:null;return tp(l,o,n,t)?(i&&i(l.scrollIntoView()),!0):!1}}function tp(n,t,e,i=null){let s=!1,r=t,o=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(e)&&t.startIndex==0){if(t.$from.index(t.depth-1)==0)return!1;let a=o.resolve(t.start-2);r=new Ai(a,a,t.depth),t.endIndex<t.parent.childCount&&(t=new Ai(t.$from,o.resolve(t.$to.end(t.depth)),t.depth)),s=!0}let l=kr(r,e,i,t);return l?(n&&ep(n,t,l,s,e),!0):!1}function ep(n,t,e,i,s){let r=S.empty;for(let h=e.length-1;h>=0;h--)r=S.from(e[h].type.create(e[h].attrs,r));n.step(new Y(t.start-(i?2:0),t.end,t.start,t.end,new T(r,0,0),e.length,!0));let o=0;for(let h=0;h<e.length;h++)e[h].type==s&&(o=h+1);let l=e.length-o,a=t.start+e.length-(i?2:0),c=t.parent;for(let h=t.startIndex,d=t.endIndex,u=!0;h<d;h++,u=!1)!u&&Ut(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(h).nodeSize;return n}function np(n){return function(t,e){let{$from:i,$to:s}=t.selection,r=i.blockRange(s,o=>o.childCount>0&&o.firstChild.type==n);return r?e?i.node(r.depth-1).type==n?ip(t,e,n,r):sp(t,e,r):!0:!1}}function ip(n,t,e,i){let s=n.tr,r=i.end,o=i.$to.end(i.depth);r<o&&(s.step(new Y(r-1,o,r,o,new T(S.from(e.create(null,i.parent.copy())),1,0),1,!0)),i=new Ai(s.doc.resolve(i.$from.pos),s.doc.resolve(o),i.depth));const l=dn(i);if(l==null)return!1;s.lift(i,l);let a=s.doc.resolve(s.mapping.map(r,-1)-1);return de(s.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&s.join(a.pos),t(s.scrollIntoView()),!0}function sp(n,t,e){let i=n.tr,s=e.parent;for(let f=e.end,p=e.endIndex-1,m=e.startIndex;p>m;p--)f-=s.child(p).nodeSize,i.delete(f-1,f+1);let r=i.doc.resolve(e.start),o=r.nodeAfter;if(i.mapping.map(e.end)!=e.start+r.nodeAfter.nodeSize)return!1;let l=e.startIndex==0,a=e.endIndex==s.childCount,c=r.node(-1),h=r.index(-1);if(!c.canReplace(h+(l?0:1),h+1,o.content.append(a?S.empty:S.from(s))))return!1;let d=r.pos,u=d+o.nodeSize;return i.step(new Y(d-(l?1:0),u+(a?1:0),d+1,u-1,new T((l?S.empty:S.from(s.copy(S.empty))).append(a?S.empty:S.from(s.copy(S.empty))),l?0:1,a?0:1),l?0:1)),t(i.scrollIntoView()),!0}function rp(n){return function(t,e){let{$from:i,$to:s}=t.selection,r=i.blockRange(s,c=>c.childCount>0&&c.firstChild.type==n);if(!r)return!1;let o=r.startIndex;if(o==0)return!1;let l=r.parent,a=l.child(o-1);if(a.type!=n)return!1;if(e){let c=a.lastChild&&a.lastChild.type==l.type,h=S.from(c?n.create():null),d=new T(S.from(n.create(null,S.from(l.type.create(null,h)))),c?3:1,0),u=r.start,f=r.end;e(t.tr.step(new Y(u-(c?3:1),f,u,f,d,1,!0)).scrollIntoView())}return!0}}function Zi(n){const{state:t,transaction:e}=n;let{selection:i}=e,{doc:s}=e,{storedMarks:r}=e;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return r},get selection(){return i},get doc(){return s},get tr(){return i=e.selection,s=e.doc,r=e.storedMarks,e}}}class ts{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:t,editor:e,state:i}=this,{view:s}=e,{tr:r}=i,o=this.buildProps(r);return Object.fromEntries(Object.entries(t).map(([l,a])=>[l,(...h)=>{const d=a(...h)(o);return!r.getMeta("preventDispatch")&&!this.hasCustomState&&s.dispatch(r),d}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){const{rawCommands:i,editor:s,state:r}=this,{view:o}=s,l=[],a=!!t,c=t||r.tr,h=()=>(!a&&e&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(c),l.every(u=>u===!0)),d={...Object.fromEntries(Object.entries(i).map(([u,f])=>[u,(...m)=>{const g=this.buildProps(c,e),y=f(...m)(g);return l.push(y),d}])),run:h};return d}createCan(t){const{rawCommands:e,state:i}=this,s=!1,r=t||i.tr,o=this.buildProps(r,s);return{...Object.fromEntries(Object.entries(e).map(([a,c])=>[a,(...h)=>c(...h)({...o,dispatch:void 0})])),chain:()=>this.createChain(r,s)}}buildProps(t,e=!0){const{rawCommands:i,editor:s,state:r}=this,{view:o}=s,l={tr:t,editor:s,view:o,state:Zi({state:r,transaction:t}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(t,e),can:()=>this.createCan(t),get commands(){return Object.fromEntries(Object.entries(i).map(([a,c])=>[a,(...h)=>c(...h)(l)]))}};return l}}class op{constructor(){this.callbacks={}}on(t,e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(e),this}emit(t,...e){const i=this.callbacks[t];return i&&i.forEach(s=>s.apply(this,e)),this}off(t,e){const i=this.callbacks[t];return i&&(e?this.callbacks[t]=i.filter(s=>s!==e):delete this.callbacks[t]),this}once(t,e){const i=(...s)=>{this.off(t,i),e.apply(this,s)};return this.on(t,i)}removeAllListeners(){this.callbacks={}}}function O(n,t,e){return n.config[t]===void 0&&n.parent?O(n.parent,t,e):typeof n.config[t]=="function"?n.config[t].bind({...e,parent:n.parent?O(n.parent,t,e):null}):n.config[t]}function es(n){const t=n.filter(s=>s.type==="extension"),e=n.filter(s=>s.type==="node"),i=n.filter(s=>s.type==="mark");return{baseExtensions:t,nodeExtensions:e,markExtensions:i}}function eh(n){const t=[],{nodeExtensions:e,markExtensions:i}=es(n),s=[...e,...i],r={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage,extensions:s},a=O(o,"addGlobalAttributes",l);if(!a)return;a().forEach(h=>{h.types.forEach(d=>{Object.entries(h.attributes).forEach(([u,f])=>{t.push({type:d,name:u,attribute:{...r,...f}})})})})}),s.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=O(o,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([h,d])=>{const u={...r,...d};typeof(u==null?void 0:u.default)=="function"&&(u.default=u.default()),u!=null&&u.isRequired&&(u==null?void 0:u.default)===void 0&&delete u.default,t.push({type:o.name,name:h,attribute:u})})}),t}function Q(n,t){if(typeof n=="string"){if(!t.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return t.nodes[n]}return n}function ht(...n){return n.filter(t=>!!t).reduce((t,e)=>{const i={...t};return Object.entries(e).forEach(([s,r])=>{if(!i[s]){i[s]=r;return}if(s==="class"){const l=r?String(r).split(" "):[],a=i[s]?i[s].split(" "):[],c=l.filter(h=>!a.includes(h));i[s]=[...a,...c].join(" ")}else if(s==="style"){const l=r?r.split(";").map(h=>h.trim()).filter(Boolean):[],a=i[s]?i[s].split(";").map(h=>h.trim()).filter(Boolean):[],c=new Map;a.forEach(h=>{const[d,u]=h.split(":").map(f=>f.trim());c.set(d,u)}),l.forEach(h=>{const[d,u]=h.split(":").map(f=>f.trim());c.set(d,u)}),i[s]=Array.from(c.entries()).map(([h,d])=>`${h}: ${d}`).join("; ")}else i[s]=r}),i},{})}function rr(n,t){return t.filter(e=>e.type===n.type.name).filter(e=>e.attribute.rendered).map(e=>e.attribute.renderHTML?e.attribute.renderHTML(n.attrs)||{}:{[e.name]:n.attrs[e.name]}).reduce((e,i)=>ht(e,i),{})}function nh(n){return typeof n=="function"}function I(n,t=void 0,...e){return nh(n)?t?n.bind(t)(...e):n(...e):n}function lp(n={}){return Object.keys(n).length===0&&n.constructor===Object}function ap(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function el(n,t){return"style"in n?n:{...n,getAttrs:e=>{const i=n.getAttrs?n.getAttrs(e):n.attrs;if(i===!1)return!1;const s=t.reduce((r,o)=>{const l=o.attribute.parseHTML?o.attribute.parseHTML(e):ap(e.getAttribute(o.name));return l==null?r:{...r,[o.name]:l}},{});return{...i,...s}}}}function nl(n){return Object.fromEntries(Object.entries(n).filter(([t,e])=>t==="attrs"&&lp(e)?!1:e!=null))}function cp(n,t){var e;const i=eh(n),{nodeExtensions:s,markExtensions:r}=es(n),o=(e=s.find(c=>O(c,"topNode")))===null||e===void 0?void 0:e.name,l=Object.fromEntries(s.map(c=>{const h=i.filter(y=>y.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:t},u=n.reduce((y,b)=>{const M=O(b,"extendNodeSchema",d);return{...y,...M?M(c):{}}},{}),f=nl({...u,content:I(O(c,"content",d)),marks:I(O(c,"marks",d)),group:I(O(c,"group",d)),inline:I(O(c,"inline",d)),atom:I(O(c,"atom",d)),selectable:I(O(c,"selectable",d)),draggable:I(O(c,"draggable",d)),code:I(O(c,"code",d)),whitespace:I(O(c,"whitespace",d)),linebreakReplacement:I(O(c,"linebreakReplacement",d)),defining:I(O(c,"defining",d)),isolating:I(O(c,"isolating",d)),attrs:Object.fromEntries(h.map(y=>{var b;return[y.name,{default:(b=y==null?void 0:y.attribute)===null||b===void 0?void 0:b.default}]}))}),p=I(O(c,"parseHTML",d));p&&(f.parseDOM=p.map(y=>el(y,h)));const m=O(c,"renderHTML",d);m&&(f.toDOM=y=>m({node:y,HTMLAttributes:rr(y,h)}));const g=O(c,"renderText",d);return g&&(f.toText=g),[c.name,f]})),a=Object.fromEntries(r.map(c=>{const h=i.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:t},u=n.reduce((g,y)=>{const b=O(y,"extendMarkSchema",d);return{...g,...b?b(c):{}}},{}),f=nl({...u,inclusive:I(O(c,"inclusive",d)),excludes:I(O(c,"excludes",d)),group:I(O(c,"group",d)),spanning:I(O(c,"spanning",d)),code:I(O(c,"code",d)),attrs:Object.fromEntries(h.map(g=>{var y;return[g.name,{default:(y=g==null?void 0:g.attribute)===null||y===void 0?void 0:y.default}]}))}),p=I(O(c,"parseHTML",d));p&&(f.parseDOM=p.map(g=>el(g,h)));const m=O(c,"renderHTML",d);return m&&(f.toDOM=g=>m({mark:g,HTMLAttributes:rr(g,h)})),[c.name,f]}));return new ja({topNode:o,nodes:l,marks:a})}function Ts(n,t){return t.nodes[n]||t.marks[n]||null}function il(n,t){return Array.isArray(t)?t.some(e=>(typeof e=="string"?e:e.name)===n.name):t}function Lr(n,t){const e=He.fromSchema(t).serializeFragment(n),s=document.implementation.createHTMLDocument().createElement("div");return s.appendChild(e),s.innerHTML}const hp=(n,t=500)=>{let e="";const i=n.parentOffset;return n.parent.nodesBetween(Math.max(0,i-t),i,(s,r,o,l)=>{var a,c;const h=((c=(a=s.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:s,pos:r,parent:o,index:l}))||s.textContent||"%leaf%";e+=s.isAtom&&!s.isText?h:h.slice(0,Math.max(0,i-r))}),e};function Br(n){return Object.prototype.toString.call(n)==="[object RegExp]"}class ns{constructor(t){this.find=t.find,this.handler=t.handler}}const dp=(n,t)=>{if(Br(t))return t.exec(n);const e=t(n);if(!e)return null;const i=[e.text];return i.index=e.index,i.input=n,i.data=e.data,e.replaceWith&&(e.text.includes(e.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),i.push(e.replaceWith)),i};function si(n){var t;const{editor:e,from:i,to:s,text:r,rules:o,plugin:l}=n,{view:a}=e;if(a.composing)return!1;const c=a.state.doc.resolve(i);if(c.parent.type.spec.code||!((t=c.nodeBefore||c.nodeAfter)===null||t===void 0)&&t.marks.find(u=>u.type.spec.code))return!1;let h=!1;const d=hp(c)+r;return o.forEach(u=>{if(h)return;const f=dp(d,u.find);if(!f)return;const p=a.state.tr,m=Zi({state:a.state,transaction:p}),g={from:i-(f[0].length-r.length),to:s},{commands:y,chain:b,can:M}=new ts({editor:e,state:m});u.handler({state:m,range:g,match:f,commands:y,chain:b,can:M})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:i,to:s,text:r}),a.dispatch(p),h=!0)}),h}function up(n){const{editor:t,rules:e}=n,i=new yt({state:{init(){return null},apply(s,r,o){const l=s.getMeta(i);if(l)return l;const a=s.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:h}=a;typeof h=="string"?h=h:h=Lr(S.from(h),o.schema);const{from:d}=a,u=d+h.length;si({editor:t,from:d,to:u,text:h,rules:e,plugin:i})}),s.selectionSet||s.docChanged?null:r}},props:{handleTextInput(s,r,o,l){return si({editor:t,from:r,to:o,text:l,rules:e,plugin:i})},handleDOMEvents:{compositionend:s=>(setTimeout(()=>{const{$cursor:r}=s.state.selection;r&&si({editor:t,from:r.pos,to:r.pos,text:"",rules:e,plugin:i})}),!1)},handleKeyDown(s,r){if(r.key!=="Enter")return!1;const{$cursor:o}=s.state.selection;return o?si({editor:t,from:o.pos,to:o.pos,text:`
`,rules:e,plugin:i}):!1}},isInputRules:!0});return i}function fp(n){return Object.prototype.toString.call(n).slice(8,-1)}function ri(n){return fp(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function is(n,t){const e={...n};return ri(n)&&ri(t)&&Object.keys(t).forEach(i=>{ri(t[i])&&ri(n[i])?e[i]=is(n[i],t[i]):e[i]=t[i]}),e}class ce{constructor(t={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=I(O(this,"addOptions",{name:this.name}))),this.storage=I(O(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new ce(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>is(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new ce(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=I(O(e,"addOptions",{name:e.name})),e.storage=I(O(e,"addStorage",{name:e.name,options:e.options})),e}static handleExit({editor:t,mark:e}){const{tr:i}=t.state,s=t.state.selection.$from;if(s.pos===s.end()){const o=s.marks();if(!!!o.find(c=>(c==null?void 0:c.type.name)===e.name))return!1;const a=o.find(c=>(c==null?void 0:c.type.name)===e.name);return a&&i.removeStoredMark(a),i.insertText(" ",s.pos),t.view.dispatch(i),!0}return!1}}function pp(n){return typeof n=="number"}class mp{constructor(t){this.find=t.find,this.handler=t.handler}}const gp=(n,t,e)=>{if(Br(t))return[...n.matchAll(t)];const i=t(n,e);return i?i.map(s=>{const r=[s.text];return r.index=s.index,r.input=n,r.data=s.data,s.replaceWith&&(s.text.includes(s.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),r.push(s.replaceWith)),r}):[]};function yp(n){const{editor:t,state:e,from:i,to:s,rule:r,pasteEvent:o,dropEvent:l}=n,{commands:a,chain:c,can:h}=new ts({editor:t,state:e}),d=[];return e.doc.nodesBetween(i,s,(f,p)=>{if(!f.isTextblock||f.type.spec.code)return;const m=Math.max(i,p),g=Math.min(s,p+f.content.size),y=f.textBetween(m-p,g-p,void 0,"￼");gp(y,r.find,o).forEach(M=>{if(M.index===void 0)return;const w=m+M.index+1,x=w+M[0].length,C={from:e.tr.mapping.map(w),to:e.tr.mapping.map(x)},_=r.handler({state:e,range:C,match:M,commands:a,chain:c,can:h,pasteEvent:o,dropEvent:l});d.push(_)})}),d.every(f=>f!==null)}let oi=null;const bp=n=>{var t;const e=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(t=e.clipboardData)===null||t===void 0||t.setData("text/html",n),e};function xp(n){const{editor:t,rules:e}=n;let i=null,s=!1,r=!1,o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}const a=({state:h,from:d,to:u,rule:f,pasteEvt:p})=>{const m=h.tr,g=Zi({state:h,transaction:m});if(!(!yp({editor:t,state:g,from:Math.max(d-1,0),to:u.b-1,rule:f,pasteEvent:p,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m}};return e.map(h=>new yt({view(d){const u=p=>{var m;i=!((m=d.dom.parentElement)===null||m===void 0)&&m.contains(p.target)?d.dom.parentElement:null,i&&(oi=t)},f=()=>{oi&&(oi=null)};return window.addEventListener("dragstart",u),window.addEventListener("dragend",f),{destroy(){window.removeEventListener("dragstart",u),window.removeEventListener("dragend",f)}}},props:{handleDOMEvents:{drop:(d,u)=>{if(r=i===d.dom.parentElement,l=u,!r){const f=oi;f&&setTimeout(()=>{const p=f.state.selection;p&&f.commands.deleteRange({from:p.from,to:p.to})},10)}return!1},paste:(d,u)=>{var f;const p=(f=u.clipboardData)===null||f===void 0?void 0:f.getData("text/html");return o=u,s=!!(p!=null&&p.includes("data-pm-slice")),!1}}},appendTransaction:(d,u,f)=>{const p=d[0],m=p.getMeta("uiEvent")==="paste"&&!s,g=p.getMeta("uiEvent")==="drop"&&!r,y=p.getMeta("applyPasteRules"),b=!!y;if(!m&&!g&&!b)return;if(b){let{text:x}=y;typeof x=="string"?x=x:x=Lr(S.from(x),f.schema);const{from:C}=y,_=C+x.length,k=bp(x);return a({rule:h,state:f,from:C,to:{b:_},pasteEvt:k})}const M=u.doc.content.findDiffStart(f.doc.content),w=u.doc.content.findDiffEnd(f.doc.content);if(!(!pp(M)||!w||M===w.b))return a({rule:h,state:f,from:M,to:w,pasteEvt:o})}}))}function kp(n){const t=n.filter((e,i)=>n.indexOf(e)!==i);return Array.from(new Set(t))}class Ye{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=Ye.resolve(t),this.schema=cp(this.extensions,e),this.setupExtensions()}static resolve(t){const e=Ye.sort(Ye.flatten(t)),i=kp(e.map(s=>s.name));return i.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${i.map(s=>`'${s}'`).join(", ")}]. This can lead to issues.`),e}static flatten(t){return t.map(e=>{const i={name:e.name,options:e.options,storage:e.storage},s=O(e,"addExtensions",i);return s?[e,...this.flatten(s())]:e}).flat(10)}static sort(t){return t.sort((i,s)=>{const r=O(i,"priority")||100,o=O(s,"priority")||100;return r>o?-1:r<o?1:0})}get commands(){return this.extensions.reduce((t,e)=>{const i={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:Ts(e.name,this.schema)},s=O(e,"addCommands",i);return s?{...t,...s()}:t},{})}get plugins(){const{editor:t}=this,e=Ye.sort([...this.extensions].reverse()),i=[],s=[],r=e.map(o=>{const l={name:o.name,options:o.options,storage:o.storage,editor:t,type:Ts(o.name,this.schema)},a=[],c=O(o,"addKeyboardShortcuts",l);let h={};if(o.type==="mark"&&O(o,"exitable",l)&&(h.ArrowRight=()=>ce.handleExit({editor:t,mark:o})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,y])=>[g,()=>y({editor:t})]));h={...h,...m}}const d=Ff(h);a.push(d);const u=O(o,"addInputRules",l);il(o,t.options.enableInputRules)&&u&&i.push(...u());const f=O(o,"addPasteRules",l);il(o,t.options.enablePasteRules)&&f&&s.push(...f());const p=O(o,"addProseMirrorPlugins",l);if(p){const m=p();a.push(...m)}return a}).flat();return[up({editor:t,rules:i}),...xp({editor:t,rules:s}),...r]}get attributes(){return eh(this.extensions)}get nodeViews(){const{editor:t}=this,{nodeExtensions:e}=es(this.extensions);return Object.fromEntries(e.filter(i=>!!O(i,"addNodeView")).map(i=>{const s=this.attributes.filter(a=>a.type===i.name),r={name:i.name,options:i.options,storage:i.storage,editor:t,type:Q(i.name,this.schema)},o=O(i,"addNodeView",r);if(!o)return[];const l=(a,c,h,d,u)=>{const f=rr(a,s);return o()({node:a,view:c,getPos:h,decorations:d,innerDecorations:u,editor:t,extension:i,HTMLAttributes:f})};return[i.name,l]}))}setupExtensions(){this.extensions.forEach(t=>{var e;this.editor.extensionStorage[t.name]=t.storage;const i={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:Ts(t.name,this.schema)};t.type==="mark"&&(!((e=I(O(t,"keepOnSplit",i)))!==null&&e!==void 0)||e)&&this.splittableMarks.push(t.name);const s=O(t,"onBeforeCreate",i),r=O(t,"onCreate",i),o=O(t,"onUpdate",i),l=O(t,"onSelectionUpdate",i),a=O(t,"onTransaction",i),c=O(t,"onFocus",i),h=O(t,"onBlur",i),d=O(t,"onDestroy",i);s&&this.editor.on("beforeCreate",s),r&&this.editor.on("create",r),o&&this.editor.on("update",o),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),h&&this.editor.on("blur",h),d&&this.editor.on("destroy",d)})}}class dt{constructor(t={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=I(O(this,"addOptions",{name:this.name}))),this.storage=I(O(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new dt(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>is(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new dt({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=I(O(e,"addOptions",{name:e.name})),e.storage=I(O(e,"addStorage",{name:e.name,options:e.options})),e}}function ih(n,t,e){const{from:i,to:s}=t,{blockSeparator:r=`

`,textSerializers:o={}}=e||{};let l="";return n.nodesBetween(i,s,(a,c,h,d)=>{var u;a.isBlock&&c>i&&(l+=r);const f=o==null?void 0:o[a.type.name];if(f)return h&&(l+=f({node:a,pos:c,parent:h,index:d,range:t})),!1;a.isText&&(l+=(u=a==null?void 0:a.text)===null||u===void 0?void 0:u.slice(Math.max(i,c)-c,s-c))}),l}function sh(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,t])=>t.spec.toText).map(([t,e])=>[t,e.spec.toText]))}const Sp=dt.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new yt({key:new Ht("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:n}=this,{state:t,schema:e}=n,{doc:i,selection:s}=t,{ranges:r}=s,o=Math.min(...r.map(h=>h.$from.pos)),l=Math.max(...r.map(h=>h.$to.pos)),a=sh(e);return ih(i,{from:o,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),Mp=()=>({editor:n,view:t})=>(requestAnimationFrame(()=>{var e;n.isDestroyed||(t.dom.blur(),(e=window==null?void 0:window.getSelection())===null||e===void 0||e.removeAllRanges())}),!0),wp=(n=!1)=>({commands:t})=>t.setContent("",n),Cp=()=>({state:n,tr:t,dispatch:e})=>{const{selection:i}=t,{ranges:s}=i;return e&&s.forEach(({$from:r,$to:o})=>{n.doc.nodesBetween(r.pos,o.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:h}=t,d=c.resolve(h.map(a)),u=c.resolve(h.map(a+l.nodeSize)),f=d.blockRange(u);if(!f)return;const p=dn(f);if(l.type.isTextblock){const{defaultType:m}=d.parent.contentMatchAt(d.index());t.setNodeMarkup(f.start,m)}(p||p===0)&&t.lift(f,p)})}),!0},_p=n=>t=>n(t),Tp=()=>({state:n,dispatch:t})=>Xc(n,t),Op=(n,t)=>({editor:e,tr:i})=>{const{state:s}=e,r=s.doc.slice(n.from,n.to);i.deleteRange(n.from,n.to);const o=i.mapping.map(t);return i.insert(o,r.content),i.setSelection(new E(i.doc.resolve(o-1))),!0},vp=()=>({tr:n,dispatch:t})=>{const{selection:e}=n,i=e.$anchor.node();if(i.content.size>0)return!1;const s=n.selection.$anchor;for(let r=s.depth;r>0;r-=1)if(s.node(r).type===i.type){if(t){const l=s.before(r),a=s.after(r);n.delete(l,a).scrollIntoView()}return!0}return!1},Dp=n=>({tr:t,state:e,dispatch:i})=>{const s=Q(n,e.schema),r=t.selection.$anchor;for(let o=r.depth;o>0;o-=1)if(r.node(o).type===s){if(i){const a=r.before(o),c=r.after(o);t.delete(a,c).scrollIntoView()}return!0}return!1},Ap=n=>({tr:t,dispatch:e})=>{const{from:i,to:s}=n;return e&&t.delete(i,s),!0},Ep=()=>({state:n,dispatch:t})=>Er(n,t),Np=()=>({commands:n})=>n.keyboardShortcut("Enter"),Pp=()=>({state:n,dispatch:t})=>Kf(n,t);function Bi(n,t,e={strict:!0}){const i=Object.keys(t);return i.length?i.every(s=>e.strict?t[s]===n[s]:Br(t[s])?t[s].test(n[s]):t[s]===n[s]):!0}function rh(n,t,e={}){return n.find(i=>i.type===t&&Bi(Object.fromEntries(Object.keys(e).map(s=>[s,i.attrs[s]])),e))}function sl(n,t,e={}){return!!rh(n,t,e)}function zr(n,t,e){var i;if(!n||!t)return;let s=n.parent.childAfter(n.parentOffset);if((!s.node||!s.node.marks.some(h=>h.type===t))&&(s=n.parent.childBefore(n.parentOffset)),!s.node||!s.node.marks.some(h=>h.type===t)||(e=e||((i=s.node.marks[0])===null||i===void 0?void 0:i.attrs),!rh([...s.node.marks],t,e)))return;let o=s.index,l=n.start()+s.offset,a=o+1,c=l+s.node.nodeSize;for(;o>0&&sl([...n.parent.child(o-1).marks],t,e);)o-=1,l-=n.parent.child(o).nodeSize;for(;a<n.parent.childCount&&sl([...n.parent.child(a).marks],t,e);)c+=n.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function fe(n,t){if(typeof n=="string"){if(!t.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return t.marks[n]}return n}const Ip=(n,t={})=>({tr:e,state:i,dispatch:s})=>{const r=fe(n,i.schema),{doc:o,selection:l}=e,{$from:a,from:c,to:h}=l;if(s){const d=zr(a,r,t);if(d&&d.from<=c&&d.to>=h){const u=E.create(o,d.from,d.to);e.setSelection(u)}}return!0},Rp=n=>t=>{const e=typeof n=="function"?n(t):n;for(let i=0;i<e.length;i+=1)if(e[i](t))return!0;return!1};function oh(n){return n instanceof E}function Te(n=0,t=0,e=0){return Math.min(Math.max(n,t),e)}function lh(n,t=null){if(!t)return null;const e=N.atStart(n),i=N.atEnd(n);if(t==="start"||t===!0)return e;if(t==="end")return i;const s=e.from,r=i.to;return t==="all"?E.create(n,Te(0,s,r),Te(n.content.size,s,r)):E.create(n,Te(t,s,r),Te(t,s,r))}function Lp(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function Fr(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Bp=(n=null,t={})=>({editor:e,view:i,tr:s,dispatch:r})=>{t={scrollIntoView:!0,...t};const o=()=>{(Fr()||Lp())&&i.dom.focus(),requestAnimationFrame(()=>{e.isDestroyed||(i.focus(),t!=null&&t.scrollIntoView&&e.commands.scrollIntoView())})};if(i.hasFocus()&&n===null||n===!1)return!0;if(r&&n===null&&!oh(e.state.selection))return o(),!0;const l=lh(s.doc,n)||e.state.selection,a=e.state.selection.eq(l);return r&&(a||s.setSelection(l),a&&s.storedMarks&&s.setStoredMarks(s.storedMarks),o()),!0},zp=(n,t)=>e=>n.every((i,s)=>t(i,{...e,index:s})),Fp=(n,t)=>({tr:e,commands:i})=>i.insertContentAt({from:e.selection.from,to:e.selection.to},n,t),ah=n=>{const t=n.childNodes;for(let e=t.length-1;e>=0;e-=1){const i=t[e];i.nodeType===3&&i.nodeValue&&/^(\n\s\s|\n)$/.test(i.nodeValue)?n.removeChild(i):i.nodeType===1&&ah(i)}return n};function li(n){const t=`<body>${n}</body>`,e=new window.DOMParser().parseFromString(t,"text/html").body;return ah(e)}function $n(n,t,e){if(n instanceof se||n instanceof S)return n;e={slice:!0,parseOptions:{},...e};const i=typeof n=="object"&&n!==null,s=typeof n=="string";if(i)try{if(Array.isArray(n)&&n.length>0)return S.fromArray(n.map(l=>t.nodeFromJSON(l)));const o=t.nodeFromJSON(n);return e.errorOnInvalidContent&&o.check(),o}catch(r){if(e.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",n,"Error:",r),$n("",t,e)}if(s){if(e.errorOnInvalidContent){let o=!1,l="";const a=new ja({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(o=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(e.slice?re.fromSchema(a).parseSlice(li(n),e.parseOptions):re.fromSchema(a).parse(li(n),e.parseOptions),e.errorOnInvalidContent&&o)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}const r=re.fromSchema(t);return e.slice?r.parseSlice(li(n),e.parseOptions).content:r.parse(li(n),e.parseOptions)}return $n("",t,e)}function Vp(n,t,e){const i=n.steps.length-1;if(i<t)return;const s=n.steps[i];if(!(s instanceof G||s instanceof Y))return;const r=n.mapping.maps[i];let o=0;r.forEach((l,a,c,h)=>{o===0&&(o=h)}),n.setSelection(N.near(n.doc.resolve(o),e))}const Hp=n=>!("type"in n),$p=(n,t,e)=>({tr:i,dispatch:s,editor:r})=>{var o;if(s){e={parseOptions:r.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...e};let l;const a=g=>{r.emit("contentError",{editor:r,error:g,disableCollaboration:()=>{r.storage.collaboration&&(r.storage.collaboration.isDisabled=!0)}})},c={preserveWhitespace:"full",...e.parseOptions};if(!e.errorOnInvalidContent&&!r.options.enableContentCheck&&r.options.emitContentError)try{$n(t,r.schema,{parseOptions:c,errorOnInvalidContent:!0})}catch(g){a(g)}try{l=$n(t,r.schema,{parseOptions:c,errorOnInvalidContent:(o=e.errorOnInvalidContent)!==null&&o!==void 0?o:r.options.enableContentCheck})}catch(g){return a(g),!1}let{from:h,to:d}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},u=!0,f=!0;if((Hp(l)?l:[l]).forEach(g=>{g.check(),u=u?g.isText&&g.marks.length===0:!1,f=f?g.isBlock:!1}),h===d&&f){const{parent:g}=i.doc.resolve(h);g.isTextblock&&!g.type.spec.code&&!g.childCount&&(h-=1,d+=1)}let m;if(u){if(Array.isArray(t))m=t.map(g=>g.text||"").join("");else if(t instanceof S){let g="";t.forEach(y=>{y.text&&(g+=y.text)}),m=g}else typeof t=="object"&&t&&t.text?m=t.text:m=t;i.insertText(m,h,d)}else m=l,i.replaceWith(h,d,m);e.updateSelection&&Vp(i,i.steps.length-1,-1),e.applyInputRules&&i.setMeta("applyInputRules",{from:h,text:m}),e.applyPasteRules&&i.setMeta("applyPasteRules",{from:h,text:m})}return!0},Wp=()=>({state:n,dispatch:t})=>$f(n,t),jp=()=>({state:n,dispatch:t})=>Wf(n,t),Kp=()=>({state:n,dispatch:t})=>jc(n,t),qp=()=>({state:n,dispatch:t})=>Uc(n,t),Jp=()=>({state:n,dispatch:t,tr:e})=>{try{const i=Ji(n.doc,n.selection.$from.pos,-1);return i==null?!1:(e.join(i,2),t&&t(e),!0)}catch{return!1}},Up=()=>({state:n,dispatch:t,tr:e})=>{try{const i=Ji(n.doc,n.selection.$from.pos,1);return i==null?!1:(e.join(i,2),t&&t(e),!0)}catch{return!1}},Gp=()=>({state:n,dispatch:t})=>Vf(n,t),Yp=()=>({state:n,dispatch:t})=>Hf(n,t);function ch(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function Xp(n){const t=n.split(/-(?!$)/);let e=t[t.length-1];e==="Space"&&(e=" ");let i,s,r,o;for(let l=0;l<t.length-1;l+=1){const a=t[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))i=!0;else if(/^(c|ctrl|control)$/i.test(a))s=!0;else if(/^s(hift)?$/i.test(a))r=!0;else if(/^mod$/i.test(a))Fr()||ch()?o=!0:s=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return i&&(e=`Alt-${e}`),s&&(e=`Ctrl-${e}`),o&&(e=`Meta-${e}`),r&&(e=`Shift-${e}`),e}const Qp=n=>({editor:t,view:e,tr:i,dispatch:s})=>{const r=Xp(n).split(/-(?!$)/),o=r.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:o==="Space"?" ":o,altKey:r.includes("Alt"),ctrlKey:r.includes("Ctrl"),metaKey:r.includes("Meta"),shiftKey:r.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction(()=>{e.someProp("handleKeyDown",c=>c(e,l))});return a==null||a.steps.forEach(c=>{const h=c.map(i.mapping);h&&s&&i.maybeStep(h)}),!0};function Wn(n,t,e={}){const{from:i,to:s,empty:r}=n.selection,o=t?Q(t,n.schema):null,l=[];n.doc.nodesBetween(i,s,(d,u)=>{if(d.isText)return;const f=Math.max(i,u),p=Math.min(s,u+d.nodeSize);l.push({node:d,from:f,to:p})});const a=s-i,c=l.filter(d=>o?o.name===d.node.type.name:!0).filter(d=>Bi(d.node.attrs,e,{strict:!1}));return r?!!c.length:c.reduce((d,u)=>d+u.to-u.from,0)>=a}const Zp=(n,t={})=>({state:e,dispatch:i})=>{const s=Q(n,e.schema);return Wn(e,s,t)?jf(e,i):!1},tm=()=>({state:n,dispatch:t})=>Qc(n,t),em=n=>({state:t,dispatch:e})=>{const i=Q(n,t.schema);return np(i)(t,e)},nm=()=>({state:n,dispatch:t})=>Yc(n,t);function ss(n,t){return t.nodes[n]?"node":t.marks[n]?"mark":null}function rl(n,t){const e=typeof t=="string"?[t]:t;return Object.keys(n).reduce((i,s)=>(e.includes(s)||(i[s]=n[s]),i),{})}const im=(n,t)=>({tr:e,state:i,dispatch:s})=>{let r=null,o=null;const l=ss(typeof n=="string"?n:n.name,i.schema);return l?(l==="node"&&(r=Q(n,i.schema)),l==="mark"&&(o=fe(n,i.schema)),s&&e.selection.ranges.forEach(a=>{i.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,h)=>{r&&r===c.type&&e.setNodeMarkup(h,void 0,rl(c.attrs,t)),o&&c.marks.length&&c.marks.forEach(d=>{o===d.type&&e.addMark(h,h+c.nodeSize,o.create(rl(d.attrs,t)))})})}),!0):!1},sm=()=>({tr:n,dispatch:t})=>(t&&n.scrollIntoView(),!0),rm=()=>({tr:n,dispatch:t})=>{if(t){const e=new xt(n.doc);n.setSelection(e)}return!0},om=()=>({state:n,dispatch:t})=>qc(n,t),lm=()=>({state:n,dispatch:t})=>Gc(n,t),am=()=>({state:n,dispatch:t})=>Uf(n,t),cm=()=>({state:n,dispatch:t})=>Xf(n,t),hm=()=>({state:n,dispatch:t})=>Yf(n,t);function or(n,t,e={},i={}){return $n(n,t,{slice:!1,parseOptions:e,errorOnInvalidContent:i.errorOnInvalidContent})}const dm=(n,t=!1,e={},i={})=>({editor:s,tr:r,dispatch:o,commands:l})=>{var a,c;const{doc:h}=r;if(e.preserveWhitespace!=="full"){const d=or(n,s.schema,e,{errorOnInvalidContent:(a=i.errorOnInvalidContent)!==null&&a!==void 0?a:s.options.enableContentCheck});return o&&r.replaceWith(0,h.content.size,d).setMeta("preventUpdate",!t),!0}return o&&r.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:h.content.size},n,{parseOptions:e,errorOnInvalidContent:(c=i.errorOnInvalidContent)!==null&&c!==void 0?c:s.options.enableContentCheck})};function hh(n,t){const e=fe(t,n.schema),{from:i,to:s,empty:r}=n.selection,o=[];r?(n.storedMarks&&o.push(...n.storedMarks),o.push(...n.selection.$head.marks())):n.doc.nodesBetween(i,s,a=>{o.push(...a.marks)});const l=o.find(a=>a.type.name===e.name);return l?{...l.attrs}:{}}function um(n){for(let t=0;t<n.edgeCount;t+=1){const{type:e}=n.edge(t);if(e.isTextblock&&!e.hasRequiredAttrs())return e}return null}function fm(n,t){for(let e=n.depth;e>0;e-=1){const i=n.node(e);if(t(i))return{pos:e>0?n.before(e):0,start:n.start(e),depth:e,node:i}}}function Vr(n){return t=>fm(t.$from,n)}function pm(n,t){const e={from:0,to:n.content.size};return ih(n,e,t)}function mm(n,t){const e=Q(t,n.schema),{from:i,to:s}=n.selection,r=[];n.doc.nodesBetween(i,s,l=>{r.push(l)});const o=r.reverse().find(l=>l.type.name===e.name);return o?{...o.attrs}:{}}function gm(n,t){const e=ss(typeof t=="string"?t:t.name,n.schema);return e==="node"?mm(n,t):e==="mark"?hh(n,t):{}}function dh(n,t,e){const i=[];return n===t?e.resolve(n).marks().forEach(s=>{const r=e.resolve(n),o=zr(r,s.type);o&&i.push({mark:s,...o})}):e.nodesBetween(n,t,(s,r)=>{!s||(s==null?void 0:s.nodeSize)===void 0||i.push(...s.marks.map(o=>({from:r,to:r+s.nodeSize,mark:o})))}),i}function wi(n,t,e){return Object.fromEntries(Object.entries(e).filter(([i])=>{const s=n.find(r=>r.type===t&&r.name===i);return s?s.attribute.keepOnSplit:!1}))}function lr(n,t,e={}){const{empty:i,ranges:s}=n.selection,r=t?fe(t,n.schema):null;if(i)return!!(n.storedMarks||n.selection.$from.marks()).filter(d=>r?r.name===d.type.name:!0).find(d=>Bi(d.attrs,e,{strict:!1}));let o=0;const l=[];if(s.forEach(({$from:d,$to:u})=>{const f=d.pos,p=u.pos;n.doc.nodesBetween(f,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;const y=Math.max(f,g),b=Math.min(p,g+m.nodeSize),M=b-y;o+=M,l.push(...m.marks.map(w=>({mark:w,from:y,to:b})))})}),o===0)return!1;const a=l.filter(d=>r?r.name===d.mark.type.name:!0).filter(d=>Bi(d.mark.attrs,e,{strict:!1})).reduce((d,u)=>d+u.to-u.from,0),c=l.filter(d=>r?d.mark.type!==r&&d.mark.type.excludes(r):!0).reduce((d,u)=>d+u.to-u.from,0);return(a>0?a+c:a)>=o}function ym(n,t,e={}){if(!t)return Wn(n,null,e)||lr(n,null,e);const i=ss(t,n.schema);return i==="node"?Wn(n,t,e):i==="mark"?lr(n,t,e):!1}function ol(n,t){const{nodeExtensions:e}=es(t),i=e.find(o=>o.name===n);if(!i)return!1;const s={name:i.name,options:i.options,storage:i.storage},r=I(O(i,"group",s));return typeof r!="string"?!1:r.split(" ").includes("list")}function Hr(n,{checkChildren:t=!0,ignoreWhitespace:e=!1}={}){var i;if(e){if(n.type.name==="hardBreak")return!0;if(n.isText)return/^\s*$/m.test((i=n.text)!==null&&i!==void 0?i:"")}if(n.isText)return!n.text;if(n.isAtom||n.isLeaf)return!1;if(n.content.childCount===0)return!0;if(t){let s=!0;return n.content.forEach(r=>{s!==!1&&(Hr(r,{ignoreWhitespace:e,checkChildren:t})||(s=!1))}),s}return!1}function bm(n){return n instanceof D}function xm(n,t,e){var i;const{selection:s}=t;let r=null;if(oh(s)&&(r=s.$cursor),r){const l=(i=n.storedMarks)!==null&&i!==void 0?i:r.marks();return!!e.isInSet(l)||!l.some(a=>a.type.excludes(e))}const{ranges:o}=s;return o.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(e):!1;return n.doc.nodesBetween(l.pos,a.pos,(h,d,u)=>{if(c)return!1;if(h.isInline){const f=!u||u.type.allowsMarkType(e),p=!!e.isInSet(h.marks)||!h.marks.some(m=>m.type.excludes(e));c=f&&p}return!c}),c})}const km=(n,t={})=>({tr:e,state:i,dispatch:s})=>{const{selection:r}=e,{empty:o,ranges:l}=r,a=fe(n,i.schema);if(s)if(o){const c=hh(i,a);e.addStoredMark(a.create({...c,...t}))}else l.forEach(c=>{const h=c.$from.pos,d=c.$to.pos;i.doc.nodesBetween(h,d,(u,f)=>{const p=Math.max(f,h),m=Math.min(f+u.nodeSize,d);u.marks.find(y=>y.type===a)?u.marks.forEach(y=>{a===y.type&&e.addMark(p,m,a.create({...y.attrs,...t}))}):e.addMark(p,m,a.create(t))})});return xm(i,e,a)},Sm=(n,t)=>({tr:e})=>(e.setMeta(n,t),!0),Mm=(n,t={})=>({state:e,dispatch:i,chain:s})=>{const r=Q(n,e.schema);let o;return e.selection.$anchor.sameParent(e.selection.$head)&&(o=e.selection.$anchor.parent.attrs),r.isTextblock?s().command(({commands:l})=>tl(r,{...o,...t})(e)?!0:l.clearNodes()).command(({state:l})=>tl(r,{...o,...t})(l,i)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},wm=n=>({tr:t,dispatch:e})=>{if(e){const{doc:i}=t,s=Te(n,0,i.content.size),r=D.create(i,s);t.setSelection(r)}return!0},Cm=n=>({tr:t,dispatch:e})=>{if(e){const{doc:i}=t,{from:s,to:r}=typeof n=="number"?{from:n,to:n}:n,o=E.atStart(i).from,l=E.atEnd(i).to,a=Te(s,o,l),c=Te(r,o,l),h=E.create(i,a,c);t.setSelection(h)}return!0},_m=n=>({state:t,dispatch:e})=>{const i=Q(n,t.schema);return rp(i)(t,e)};function ll(n,t){const e=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(e){const i=e.filter(s=>t==null?void 0:t.includes(s.type.name));n.tr.ensureMarks(i)}}const Tm=({keepMarks:n=!0}={})=>({tr:t,state:e,dispatch:i,editor:s})=>{const{selection:r,doc:o}=t,{$from:l,$to:a}=r,c=s.extensionManager.attributes,h=wi(c,l.node().type.name,l.node().attrs);if(r instanceof D&&r.node.isBlock)return!l.parentOffset||!Ut(o,l.pos)?!1:(i&&(n&&ll(e,s.extensionManager.splittableMarks),t.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const d=a.parentOffset===a.parent.content.size,u=l.depth===0?void 0:um(l.node(-1).contentMatchAt(l.indexAfter(-1)));let f=d&&u?[{type:u,attrs:h}]:void 0,p=Ut(t.doc,t.mapping.map(l.pos),1,f);if(!f&&!p&&Ut(t.doc,t.mapping.map(l.pos),1,u?[{type:u}]:void 0)&&(p=!0,f=u?[{type:u,attrs:h}]:void 0),i){if(p&&(r instanceof E&&t.deleteSelection(),t.split(t.mapping.map(l.pos),1,f),u&&!d&&!l.parentOffset&&l.parent.type!==u)){const m=t.mapping.map(l.before()),g=t.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,u)&&t.setNodeMarkup(t.mapping.map(l.before()),u)}n&&ll(e,s.extensionManager.splittableMarks),t.scrollIntoView()}return p},Om=(n,t={})=>({tr:e,state:i,dispatch:s,editor:r})=>{var o;const l=Q(n,i.schema),{$from:a,$to:c}=i.selection,h=i.selection.node;if(h&&h.isBlock||a.depth<2||!a.sameParent(c))return!1;const d=a.node(-1);if(d.type!==l)return!1;const u=r.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(s){let y=S.empty;const b=a.index(-1)?1:a.index(-2)?2:3;for(let k=a.depth-b;k>=a.depth-3;k-=1)y=S.from(a.node(k).copy(y));const M=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,w={...wi(u,a.node().type.name,a.node().attrs),...t},x=((o=l.contentMatch.defaultType)===null||o===void 0?void 0:o.createAndFill(w))||void 0;y=y.append(S.from(l.createAndFill(null,x)||void 0));const C=a.before(a.depth-(b-1));e.replace(C,a.after(-M),new T(y,4-b,0));let _=-1;e.doc.nodesBetween(C,e.doc.content.size,(k,v)=>{if(_>-1)return!1;k.isTextblock&&k.content.size===0&&(_=v+1)}),_>-1&&e.setSelection(E.near(e.doc.resolve(_))),e.scrollIntoView()}return!0}const f=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p={...wi(u,d.type.name,d.attrs),...t},m={...wi(u,a.node().type.name,a.node().attrs),...t};e.delete(a.pos,c.pos);const g=f?[{type:l,attrs:p},{type:f,attrs:m}]:[{type:l,attrs:p}];if(!Ut(e.doc,a.pos,2))return!1;if(s){const{selection:y,storedMarks:b}=i,{splittableMarks:M}=r.extensionManager,w=b||y.$to.parentOffset&&y.$from.marks();if(e.split(a.pos,2,g).scrollIntoView(),!w||!s)return!0;const x=w.filter(C=>M.includes(C.type.name));e.ensureMarks(x)}return!0},Os=(n,t)=>{const e=Vr(o=>o.type===t)(n.selection);if(!e)return!0;const i=n.doc.resolve(Math.max(0,e.pos-1)).before(e.depth);if(i===void 0)return!0;const s=n.doc.nodeAt(i);return e.node.type===(s==null?void 0:s.type)&&de(n.doc,e.pos)&&n.join(e.pos),!0},vs=(n,t)=>{const e=Vr(o=>o.type===t)(n.selection);if(!e)return!0;const i=n.doc.resolve(e.start).after(e.depth);if(i===void 0)return!0;const s=n.doc.nodeAt(i);return e.node.type===(s==null?void 0:s.type)&&de(n.doc,i)&&n.join(i),!0},vm=(n,t,e,i={})=>({editor:s,tr:r,state:o,dispatch:l,chain:a,commands:c,can:h})=>{const{extensions:d,splittableMarks:u}=s.extensionManager,f=Q(n,o.schema),p=Q(t,o.schema),{selection:m,storedMarks:g}=o,{$from:y,$to:b}=m,M=y.blockRange(b),w=g||m.$to.parentOffset&&m.$from.marks();if(!M)return!1;const x=Vr(C=>ol(C.type.name,d))(m);if(M.depth>=1&&x&&M.depth-x.depth<=1){if(x.node.type===f)return c.liftListItem(p);if(ol(x.node.type.name,d)&&f.validContent(x.node.content)&&l)return a().command(()=>(r.setNodeMarkup(x.pos,f),!0)).command(()=>Os(r,f)).command(()=>vs(r,f)).run()}return!e||!w||!l?a().command(()=>h().wrapInList(f,i)?!0:c.clearNodes()).wrapInList(f,i).command(()=>Os(r,f)).command(()=>vs(r,f)).run():a().command(()=>{const C=h().wrapInList(f,i),_=w.filter(k=>u.includes(k.type.name));return r.ensureMarks(_),C?!0:c.clearNodes()}).wrapInList(f,i).command(()=>Os(r,f)).command(()=>vs(r,f)).run()},Dm=(n,t={},e={})=>({state:i,commands:s})=>{const{extendEmptyMarkRange:r=!1}=e,o=fe(n,i.schema);return lr(i,o,t)?s.unsetMark(o,{extendEmptyMarkRange:r}):s.setMark(o,t)},Am=(n,t,e={})=>({state:i,commands:s})=>{const r=Q(n,i.schema),o=Q(t,i.schema),l=Wn(i,r,e);let a;return i.selection.$anchor.sameParent(i.selection.$head)&&(a=i.selection.$anchor.parent.attrs),l?s.setNode(o,a):s.setNode(r,{...a,...e})},Em=(n,t={})=>({state:e,commands:i})=>{const s=Q(n,e.schema);return Wn(e,s,t)?i.lift(s):i.wrapIn(s,t)},Nm=()=>({state:n,dispatch:t})=>{const e=n.plugins;for(let i=0;i<e.length;i+=1){const s=e[i];let r;if(s.spec.isInputRules&&(r=s.getState(n))){if(t){const o=n.tr,l=r.transform;for(let a=l.steps.length-1;a>=0;a-=1)o.step(l.steps[a].invert(l.docs[a]));if(r.text){const a=o.doc.resolve(r.from).marks();o.replaceWith(r.from,r.to,n.schema.text(r.text,a))}else o.delete(r.from,r.to)}return!0}}return!1},Pm=()=>({tr:n,dispatch:t})=>{const{selection:e}=n,{empty:i,ranges:s}=e;return i||t&&s.forEach(r=>{n.removeMark(r.$from.pos,r.$to.pos)}),!0},Im=(n,t={})=>({tr:e,state:i,dispatch:s})=>{var r;const{extendEmptyMarkRange:o=!1}=t,{selection:l}=e,a=fe(n,i.schema),{$from:c,empty:h,ranges:d}=l;if(!s)return!0;if(h&&o){let{from:u,to:f}=l;const p=(r=c.marks().find(g=>g.type===a))===null||r===void 0?void 0:r.attrs,m=zr(c,a,p);m&&(u=m.from,f=m.to),e.removeMark(u,f,a)}else d.forEach(u=>{e.removeMark(u.$from.pos,u.$to.pos,a)});return e.removeStoredMark(a),!0},Rm=(n,t={})=>({tr:e,state:i,dispatch:s})=>{let r=null,o=null;const l=ss(typeof n=="string"?n:n.name,i.schema);return l?(l==="node"&&(r=Q(n,i.schema)),l==="mark"&&(o=fe(n,i.schema)),s&&e.selection.ranges.forEach(a=>{const c=a.$from.pos,h=a.$to.pos;let d,u,f,p;e.selection.empty?i.doc.nodesBetween(c,h,(m,g)=>{r&&r===m.type&&(f=Math.max(g,c),p=Math.min(g+m.nodeSize,h),d=g,u=m)}):i.doc.nodesBetween(c,h,(m,g)=>{g<c&&r&&r===m.type&&(f=Math.max(g,c),p=Math.min(g+m.nodeSize,h),d=g,u=m),g>=c&&g<=h&&(r&&r===m.type&&e.setNodeMarkup(g,void 0,{...m.attrs,...t}),o&&m.marks.length&&m.marks.forEach(y=>{if(o===y.type){const b=Math.max(g,c),M=Math.min(g+m.nodeSize,h);e.addMark(b,M,o.create({...y.attrs,...t}))}}))}),u&&(d!==void 0&&e.setNodeMarkup(d,void 0,{...u.attrs,...t}),o&&u.marks.length&&u.marks.forEach(m=>{o===m.type&&e.addMark(f,p,o.create({...m.attrs,...t}))}))}),!0):!1},Lm=(n,t={})=>({state:e,dispatch:i})=>{const s=Q(n,e.schema);return Qf(s,t)(e,i)},Bm=(n,t={})=>({state:e,dispatch:i})=>{const s=Q(n,e.schema);return Zf(s,t)(e,i)};var zm=Object.freeze({__proto__:null,blur:Mp,clearContent:wp,clearNodes:Cp,command:_p,createParagraphNear:Tp,cut:Op,deleteCurrentNode:vp,deleteNode:Dp,deleteRange:Ap,deleteSelection:Ep,enter:Np,exitCode:Pp,extendMarkRange:Ip,first:Rp,focus:Bp,forEach:zp,insertContent:Fp,insertContentAt:$p,joinBackward:Kp,joinDown:jp,joinForward:qp,joinItemBackward:Jp,joinItemForward:Up,joinTextblockBackward:Gp,joinTextblockForward:Yp,joinUp:Wp,keyboardShortcut:Qp,lift:Zp,liftEmptyBlock:tm,liftListItem:em,newlineInCode:nm,resetAttributes:im,scrollIntoView:sm,selectAll:rm,selectNodeBackward:om,selectNodeForward:lm,selectParentNode:am,selectTextblockEnd:cm,selectTextblockStart:hm,setContent:dm,setMark:km,setMeta:Sm,setNode:Mm,setNodeSelection:wm,setTextSelection:Cm,sinkListItem:_m,splitBlock:Tm,splitListItem:Om,toggleList:vm,toggleMark:Dm,toggleNode:Am,toggleWrap:Em,undoInputRule:Nm,unsetAllMarks:Pm,unsetMark:Im,updateAttributes:Rm,wrapIn:Lm,wrapInList:Bm});const Fm=dt.create({name:"commands",addCommands(){return{...zm}}}),Vm=dt.create({name:"drop",addProseMirrorPlugins(){return[new yt({key:new Ht("tiptapDrop"),props:{handleDrop:(n,t,e,i)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:e,moved:i})}}})]}}),Hm=dt.create({name:"editable",addProseMirrorPlugins(){return[new yt({key:new Ht("editable"),props:{editable:()=>this.editor.options.editable}})]}}),$m=new Ht("focusEvents"),Wm=dt.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:n}=this;return[new yt({key:$m,props:{handleDOMEvents:{focus:(t,e)=>{n.isFocused=!0;const i=n.state.tr.setMeta("focus",{event:e}).setMeta("addToHistory",!1);return t.dispatch(i),!1},blur:(t,e)=>{n.isFocused=!1;const i=n.state.tr.setMeta("blur",{event:e}).setMeta("addToHistory",!1);return t.dispatch(i),!1}}}})]}}),jm=dt.create({name:"keymap",addKeyboardShortcuts(){const n=()=>this.editor.commands.first(({commands:o})=>[()=>o.undoInputRule(),()=>o.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:h,$anchor:d}=a,{pos:u,parent:f}=d,p=d.parent.isTextblock&&u>0?l.doc.resolve(u-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,y=m&&p.parent.childCount===1?g===d.pos:N.atStart(c).from===u;return!h||!f.type.isTextblock||f.textContent.length||!y||y&&d.parent.type.name==="paragraph"?!1:o.clearNodes()}),()=>o.deleteSelection(),()=>o.joinBackward(),()=>o.selectNodeBackward()]),t=()=>this.editor.commands.first(({commands:o})=>[()=>o.deleteSelection(),()=>o.deleteCurrentNode(),()=>o.joinForward(),()=>o.selectNodeForward()]),i={Enter:()=>this.editor.commands.first(({commands:o})=>[()=>o.newlineInCode(),()=>o.createParagraphNear(),()=>o.liftEmptyBlock(),()=>o.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},s={...i},r={...i,"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Fr()||ch()?r:s},addProseMirrorPlugins(){return[new yt({key:new Ht("clearDocument"),appendTransaction:(n,t,e)=>{if(n.some(m=>m.getMeta("composition")))return;const i=n.some(m=>m.docChanged)&&!t.doc.eq(e.doc),s=n.some(m=>m.getMeta("preventClearDocument"));if(!i||s)return;const{empty:r,from:o,to:l}=t.selection,a=N.atStart(t.doc).from,c=N.atEnd(t.doc).to;if(r||!(o===a&&l===c)||!Hr(e.doc))return;const u=e.tr,f=Zi({state:e,transaction:u}),{commands:p}=new ts({editor:this.editor,state:f});if(p.clearNodes(),!!u.steps.length)return u}})]}}),Km=dt.create({name:"paste",addProseMirrorPlugins(){return[new yt({key:new Ht("tiptapPaste"),props:{handlePaste:(n,t,e)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:e})}}})]}}),qm=dt.create({name:"tabindex",addProseMirrorPlugins(){return[new yt({key:new Ht("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class we{get name(){return this.node.type.name}constructor(t,e,i=!1,s=null){this.currentNode=null,this.actualDepth=null,this.isBlock=i,this.resolvedPos=t,this.editor=e,this.currentNode=s}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var t;return(t=this.actualDepth)!==null&&t!==void 0?t:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(t){let e=this.from,i=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}e=this.from+1,i=this.to-1}this.editor.commands.insertContentAt({from:e,to:i},t)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const t=this.resolvedPos.start(this.resolvedPos.depth-1),e=this.resolvedPos.doc.resolve(t);return new we(e,this.editor)}get before(){let t=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.from-3)),new we(t,this.editor)}get after(){let t=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.to+3)),new we(t,this.editor)}get children(){const t=[];return this.node.content.forEach((e,i)=>{const s=e.isBlock&&!e.isTextblock,r=e.isAtom&&!e.isText,o=this.pos+i+(r?0:1);if(o<0||o>this.resolvedPos.doc.nodeSize-2)return;const l=this.resolvedPos.doc.resolve(o);if(!s&&l.depth<=this.depth)return;const a=new we(l,this.editor,s,s?e:null);s&&(a.actualDepth=this.depth+1),t.push(new we(l,this.editor,s,s?e:null))}),t}get firstChild(){return this.children[0]||null}get lastChild(){const t=this.children;return t[t.length-1]||null}closest(t,e={}){let i=null,s=this.parent;for(;s&&!i;){if(s.node.type.name===t)if(Object.keys(e).length>0){const r=s.node.attrs,o=Object.keys(e);for(let l=0;l<o.length;l+=1){const a=o[l];if(r[a]!==e[a])break}}else i=s;s=s.parent}return i}querySelector(t,e={}){return this.querySelectorAll(t,e,!0)[0]||null}querySelectorAll(t,e={},i=!1){let s=[];if(!this.children||this.children.length===0)return s;const r=Object.keys(e);return this.children.forEach(o=>{i&&s.length>0||(o.node.type.name===t&&r.every(a=>e[a]===o.node.attrs[a])&&s.push(o),!(i&&s.length>0)&&(s=s.concat(o.querySelectorAll(t,e,i))))}),s}setAttribute(t){const{tr:e}=this.editor.state;e.setNodeMarkup(this.from,void 0,{...this.node.attrs,...t}),this.editor.view.dispatch(e)}}const Jm=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function Um(n,t,e){const i=document.querySelector("style[data-tiptap-style]");if(i!==null)return i;const s=document.createElement("style");return t&&s.setAttribute("nonce",t),s.setAttribute("data-tiptap-style",""),s.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(s),s}class H0 extends op{constructor(t={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(t),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:e,slice:i,moved:s})=>this.options.onDrop(e,i,s)),this.on("paste",({event:e,slice:i})=>this.options.onPaste(e,i)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=Um(Jm,this.options.injectNonce))}setOptions(t={}){this.options={...this.options,...t},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(t,e=!0){this.setOptions({editable:t}),e&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(t,e){const i=nh(e)?e(t,[...this.state.plugins]):[...this.state.plugins,t],s=this.state.reconfigure({plugins:i});return this.view.updateState(s),s}unregisterPlugin(t){if(this.isDestroyed)return;const e=this.state.plugins;let i=e;if([].concat(t).forEach(r=>{const o=typeof r=="string"?`${r}$`:r.key;i=i.filter(l=>!l.key.startsWith(o))}),e.length===i.length)return;const s=this.state.reconfigure({plugins:i});return this.view.updateState(s),s}createExtensionManager(){var t,e;const s=[...this.options.enableCoreExtensions?[Hm,Sp.configure({blockSeparator:(e=(t=this.options.coreExtensionOptions)===null||t===void 0?void 0:t.clipboardTextSerializer)===null||e===void 0?void 0:e.blockSeparator}),Fm,Wm,jm,qm,Vm,Km].filter(r=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[r.name]!==!1:!0):[],...this.options.extensions].filter(r=>["extension","node","mark"].includes(r==null?void 0:r.type));this.extensionManager=new Ye(s,this)}createCommandManager(){this.commandManager=new ts({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var t;let e;try{e=or(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(o){if(!(o instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(o.message))throw o;this.emit("contentError",{editor:this,error:o,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(l=>l.name!=="collaboration"),this.createExtensionManager()}}),e=or(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const i=lh(e,this.options.autofocus);this.view=new Hc(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...(t=this.options.editorProps)===null||t===void 0?void 0:t.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:Ge.create({doc:e,selection:i||void 0})});const s=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(s),this.createNodeViews(),this.prependClass();const r=this.view.dom;r.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(t){this.isCapturingTransaction=!0,t(),this.isCapturingTransaction=!1;const e=this.capturedTransaction;return this.capturedTransaction=null,e}dispatchTransaction(t){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=t;return}t.steps.forEach(o=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(o)});return}const e=this.state.apply(t),i=!this.state.selection.eq(e.selection);this.emit("beforeTransaction",{editor:this,transaction:t,nextState:e}),this.view.updateState(e),this.emit("transaction",{editor:this,transaction:t}),i&&this.emit("selectionUpdate",{editor:this,transaction:t});const s=t.getMeta("focus"),r=t.getMeta("blur");s&&this.emit("focus",{editor:this,event:s.event,transaction:t}),r&&this.emit("blur",{editor:this,event:r.event,transaction:t}),!(!t.docChanged||t.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:t})}getAttributes(t){return gm(this.state,t)}isActive(t,e){const i=typeof t=="string"?t:null,s=typeof t=="string"?e:t;return ym(this.state,i,s)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Lr(this.state.doc.content,this.schema)}getText(t){const{blockSeparator:e=`

`,textSerializers:i={}}=t||{};return pm(this.state.doc,{blockSeparator:e,textSerializers:{...sh(this.schema),...i}})}get isEmpty(){return Hr(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const t=this.view.dom;t&&t.editor&&delete t.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var t;return!(!((t=this.view)===null||t===void 0)&&t.docView)}$node(t,e){var i;return((i=this.$doc)===null||i===void 0?void 0:i.querySelector(t,e))||null}$nodes(t,e){var i;return((i=this.$doc)===null||i===void 0?void 0:i.querySelectorAll(t,e))||null}$pos(t){const e=this.state.doc.resolve(t);return new we(e,this)}get $doc(){return this.$pos(0)}}function on(n){return new ns({find:n.find,handler:({state:t,range:e,match:i})=>{const s=I(n.getAttributes,void 0,i);if(s===!1||s===null)return null;const{tr:r}=t,o=i[i.length-1],l=i[0];if(o){const a=l.search(/\S/),c=e.from+l.indexOf(o),h=c+o.length;if(dh(e.from,e.to,t.doc).filter(f=>f.mark.type.excluded.find(m=>m===n.type&&m!==f.mark.type)).filter(f=>f.to>c).length)return null;h<e.to&&r.delete(h,e.to),c>e.from&&r.delete(e.from+a,c);const u=e.from+a+o.length;r.addMark(e.from+a,u,n.type.create(s||{})),r.removeStoredMark(n.type)}}})}function Gm(n){return new ns({find:n.find,handler:({state:t,range:e,match:i})=>{const s=I(n.getAttributes,void 0,i)||{},{tr:r}=t,o=e.from;let l=e.to;const a=n.type.create(s);if(i[1]){const c=i[0].lastIndexOf(i[1]);let h=o+c;h>l?h=l:l=h+i[1].length;const d=i[0][i[0].length-1];r.insertText(d,o+i[0].length-1),r.replaceWith(h,l,a)}else if(i[0]){const c=n.type.isInline?o:o-1;r.insert(c,n.type.create(s)).delete(r.mapping.map(o),r.mapping.map(l))}r.scrollIntoView()}})}function ar(n){return new ns({find:n.find,handler:({state:t,range:e,match:i})=>{const s=t.doc.resolve(e.from),r=I(n.getAttributes,void 0,i)||{};if(!s.node(-1).canReplaceWith(s.index(-1),s.indexAfter(-1),n.type))return null;t.tr.delete(e.from,e.to).setBlockType(e.from,e.from,n.type,r)}})}function jn(n){return new ns({find:n.find,handler:({state:t,range:e,match:i,chain:s})=>{const r=I(n.getAttributes,void 0,i)||{},o=t.tr.delete(e.from,e.to),a=o.doc.resolve(e.from).blockRange(),c=a&&kr(a,n.type,r);if(!c)return null;if(o.wrap(a,c),n.keepMarks&&n.editor){const{selection:d,storedMarks:u}=t,{splittableMarks:f}=n.editor.extensionManager,p=u||d.$to.parentOffset&&d.$from.marks();if(p){const m=p.filter(g=>f.includes(g.type.name));o.ensureMarks(m)}}if(n.keepAttributes){const d=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";s().updateAttributes(d,r).run()}const h=o.doc.resolve(e.from-1).nodeBefore;h&&h.type===n.type&&de(o.doc,e.from-1)&&(!n.joinPredicate||n.joinPredicate(i,h))&&o.join(e.from-1)}})}class gt{constructor(t={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=I(O(this,"addOptions",{name:this.name}))),this.storage=I(O(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new gt(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>is(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new gt(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=I(O(e,"addOptions",{name:e.name})),e.storage=I(O(e,"addStorage",{name:e.name,options:e.options})),e}}function ln(n){return new mp({find:n.find,handler:({state:t,range:e,match:i,pasteEvent:s})=>{const r=I(n.getAttributes,void 0,i,s);if(r===!1||r===null)return null;const{tr:o}=t,l=i[i.length-1],a=i[0];let c=e.to;if(l){const h=a.search(/\S/),d=e.from+a.indexOf(l),u=d+l.length;if(dh(e.from,e.to,t.doc).filter(p=>p.mark.type.excluded.find(g=>g===n.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;u<e.to&&o.delete(u,e.to),d>e.from&&o.delete(e.from+h,d),c=e.from+h+l.length,o.addMark(e.from+h,c,n.type.create(r||{})),o.removeStoredMark(n.type)}}})}function Ym(n,t){const{selection:e}=n,{$from:i}=e;if(e instanceof D){const r=i.index();return i.parent.canReplaceWith(r,r+1,t)}let s=i.depth;for(;s>=0;){const r=i.index(s);if(i.node(s).contentMatchAt(r).matchType(t))return!0;s-=1}return!1}const Xm=/^\s*>\s$/,Qm=gt.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return["blockquote",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[jn({find:Xm,type:this.type})]}}),Zm=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,tg=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,eg=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,ng=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,ig=ce.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:n=>n.type.name===this.name},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return["strong",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[on({find:Zm,type:this.type}),on({find:eg,type:this.type})]},addPasteRules(){return[ln({find:tg,type:this.type}),ln({find:ng,type:this.type})]}}),sg="listItem",al="textStyle",cl=/^\s*([-+*])\s$/,rg=gt.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(sg,this.editor.getAttributes(al)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=jn({find:cl,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=jn({find:cl,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(al),editor:this.editor})),[n]}}),og=/(^|[^`])`([^`]+)`(?!`)/,lg=/(^|[^`])`([^`]+)`(?!`)/g,ag=ce.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[on({find:og,type:this.type})]},addPasteRules(){return[ln({find:lg,type:this.type})]}}),cg=/^```([a-z]+)?[\s\n]$/,hg=/^~~~([a-z]+)?[\s\n]$/,dg=gt.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:n=>{var t;const{languageClassPrefix:e}=this.options,r=[...((t=n.firstElementChild)===null||t===void 0?void 0:t.classList)||[]].filter(o=>o.startsWith(e)).map(o=>o.replace(e,""))[0];return r||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:t}){return["pre",ht(this.options.HTMLAttributes,t),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:t})=>t.setNode(this.name,n),toggleCodeBlock:n=>({commands:t})=>t.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:n,$anchor:t}=this.editor.state.selection,e=t.pos===1;return!n||t.parent.type.name!==this.name?!1:e||!t.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:t}=n,{selection:e}=t,{$from:i,empty:s}=e;if(!s||i.parent.type!==this.type)return!1;const r=i.parentOffset===i.parent.nodeSize-2,o=i.parent.textContent.endsWith(`

`);return!r||!o?!1:n.chain().command(({tr:l})=>(l.delete(i.pos-2,i.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;const{state:t}=n,{selection:e,doc:i}=t,{$from:s,empty:r}=e;if(!r||s.parent.type!==this.type||!(s.parentOffset===s.parent.nodeSize-2))return!1;const l=s.after();return l===void 0?!1:i.nodeAt(l)?n.commands.command(({tr:c})=>(c.setSelection(N.near(i.resolve(l))),!0)):n.commands.exitCode()}}},addInputRules(){return[ar({find:cg,type:this.type,getAttributes:n=>({language:n[1]})}),ar({find:hg,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new yt({key:new Ht("codeBlockVSCodeHandler"),props:{handlePaste:(n,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;const e=t.clipboardData.getData("text/plain"),i=t.clipboardData.getData("vscode-editor-data"),s=i?JSON.parse(i):void 0,r=s==null?void 0:s.mode;if(!e||!r)return!1;const{tr:o,schema:l}=n.state,a=l.text(e.replace(/\r\n?/g,`
`));return o.replaceSelectionWith(this.type.create({language:r},a)),o.selection.$from.parent.type!==this.type&&o.setSelection(E.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.setMeta("paste",!0),n.dispatch(o),!0}}})]}}),ug=gt.create({name:"doc",topNode:!0,content:"block+"});function fg(n={}){return new yt({view(t){return new pg(t,n)}})}class pg{constructor(t,e){var i;this.editorView=t,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(i=e.width)!==null&&i!==void 0?i:1,this.color=e.color===!1?void 0:e.color||"black",this.class=e.class,this.handlers=["dragover","dragend","drop","dragleave"].map(s=>{let r=o=>{this[s](o)};return t.dom.addEventListener(s,r),{name:s,handler:r}})}destroy(){this.handlers.forEach(({name:t,handler:e})=>this.editorView.dom.removeEventListener(t,e))}update(t,e){this.cursorPos!=null&&e.doc!=t.state.doc&&(this.cursorPos>t.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(t){t!=this.cursorPos&&(this.cursorPos=t,t==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let t=this.editorView.state.doc.resolve(this.cursorPos),e=!t.parent.inlineContent,i,s=this.editorView.dom,r=s.getBoundingClientRect(),o=r.width/s.offsetWidth,l=r.height/s.offsetHeight;if(e){let d=t.nodeBefore,u=t.nodeAfter;if(d||u){let f=this.editorView.nodeDOM(this.cursorPos-(d?d.nodeSize:0));if(f){let p=f.getBoundingClientRect(),m=d?p.bottom:p.top;d&&u&&(m=(m+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let g=this.width/2*l;i={left:p.left,right:p.right,top:m-g,bottom:m+g}}}}if(!i){let d=this.editorView.coordsAtPos(this.cursorPos),u=this.width/2*o;i={left:d.left-u,right:d.left+u,top:d.top,bottom:d.bottom}}let a=this.editorView.dom.offsetParent;this.element||(this.element=a.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",e),this.element.classList.toggle("prosemirror-dropcursor-inline",!e);let c,h;if(!a||a==document.body&&getComputedStyle(a).position=="static")c=-pageXOffset,h=-pageYOffset;else{let d=a.getBoundingClientRect(),u=d.width/a.offsetWidth,f=d.height/a.offsetHeight;c=d.left-a.scrollLeft*u,h=d.top-a.scrollTop*f}this.element.style.left=(i.left-c)/o+"px",this.element.style.top=(i.top-h)/l+"px",this.element.style.width=(i.right-i.left)/o+"px",this.element.style.height=(i.bottom-i.top)/l+"px"}scheduleRemoval(t){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),t)}dragover(t){if(!this.editorView.editable)return;let e=this.editorView.posAtCoords({left:t.clientX,top:t.clientY}),i=e&&e.inside>=0&&this.editorView.state.doc.nodeAt(e.inside),s=i&&i.type.spec.disableDropCursor,r=typeof s=="function"?s(this.editorView,e,t):s;if(e&&!r){let o=e.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=ec(this.editorView.state.doc,o,this.editorView.dragging.slice);l!=null&&(o=l)}this.setCursor(o),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(t){this.editorView.dom.contains(t.relatedTarget)||this.setCursor(null)}}const mg=dt.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[fg(this.options)]}});class W extends N{constructor(t){super(t,t)}map(t,e){let i=t.resolve(e.map(this.head));return W.valid(i)?new W(i):N.near(i)}content(){return T.empty}eq(t){return t instanceof W&&t.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(t,e){if(typeof e.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new W(t.resolve(e.pos))}getBookmark(){return new $r(this.anchor)}static valid(t){let e=t.parent;if(e.isTextblock||!gg(t)||!yg(t))return!1;let i=e.type.spec.allowGapCursor;if(i!=null)return i;let s=e.contentMatchAt(t.index()).defaultType;return s&&s.isTextblock}static findGapCursorFrom(t,e,i=!1){t:for(;;){if(!i&&W.valid(t))return t;let s=t.pos,r=null;for(let o=t.depth;;o--){let l=t.node(o);if(e>0?t.indexAfter(o)<l.childCount:t.index(o)>0){r=l.child(e>0?t.indexAfter(o):t.index(o)-1);break}else if(o==0)return null;s+=e;let a=t.doc.resolve(s);if(W.valid(a))return a}for(;;){let o=e>0?r.firstChild:r.lastChild;if(!o){if(r.isAtom&&!r.isText&&!D.isSelectable(r)){t=t.doc.resolve(s+r.nodeSize*e),i=!1;continue t}break}r=o,s+=e;let l=t.doc.resolve(s);if(W.valid(l))return l}return null}}}W.prototype.visible=!1;W.findFrom=W.findGapCursorFrom;N.jsonID("gapcursor",W);class $r{constructor(t){this.pos=t}map(t){return new $r(t.map(this.pos))}resolve(t){let e=t.resolve(this.pos);return W.valid(e)?new W(e):N.near(e)}}function gg(n){for(let t=n.depth;t>=0;t--){let e=n.index(t),i=n.node(t);if(e==0){if(i.type.spec.isolating)return!0;continue}for(let s=i.child(e-1);;s=s.lastChild){if(s.childCount==0&&!s.inlineContent||s.isAtom||s.type.spec.isolating)return!0;if(s.inlineContent)return!1}}return!0}function yg(n){for(let t=n.depth;t>=0;t--){let e=n.indexAfter(t),i=n.node(t);if(e==i.childCount){if(i.type.spec.isolating)return!0;continue}for(let s=i.child(e);;s=s.firstChild){if(s.childCount==0&&!s.inlineContent||s.isAtom||s.type.spec.isolating)return!0;if(s.inlineContent)return!1}}return!0}function bg(){return new yt({props:{decorations:Mg,createSelectionBetween(n,t,e){return t.pos==e.pos&&W.valid(e)?new W(e):null},handleClick:kg,handleKeyDown:xg,handleDOMEvents:{beforeinput:Sg}}})}const xg=$c({ArrowLeft:ai("horiz",-1),ArrowRight:ai("horiz",1),ArrowUp:ai("vert",-1),ArrowDown:ai("vert",1)});function ai(n,t){const e=n=="vert"?t>0?"down":"up":t>0?"right":"left";return function(i,s,r){let o=i.selection,l=t>0?o.$to:o.$from,a=o.empty;if(o instanceof E){if(!r.endOfTextblock(e)||l.depth==0)return!1;a=!1,l=i.doc.resolve(t>0?l.after():l.before())}let c=W.findGapCursorFrom(l,t,a);return c?(s&&s(i.tr.setSelection(new W(c))),!0):!1}}function kg(n,t,e){if(!n||!n.editable)return!1;let i=n.state.doc.resolve(t);if(!W.valid(i))return!1;let s=n.posAtCoords({left:e.clientX,top:e.clientY});return s&&s.inside>-1&&D.isSelectable(n.state.doc.nodeAt(s.inside))?!1:(n.dispatch(n.state.tr.setSelection(new W(i))),!0)}function Sg(n,t){if(t.inputType!="insertCompositionText"||!(n.state.selection instanceof W))return!1;let{$from:e}=n.state.selection,i=e.parent.contentMatchAt(e.index()).findWrapping(n.state.schema.nodes.text);if(!i)return!1;let s=S.empty;for(let o=i.length-1;o>=0;o--)s=S.from(i[o].createAndFill(null,s));let r=n.state.tr.replace(e.pos,e.pos,new T(s,0,0));return r.setSelection(E.near(r.doc.resolve(e.pos+1))),n.dispatch(r),!1}function Mg(n){if(!(n.selection instanceof W))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",J.create(n.doc,[wt.widget(n.selection.head,t,{key:"gapcursor"})])}const wg=dt.create({name:"gapCursor",addProseMirrorPlugins(){return[bg()]},extendNodeSchema(n){var t;const e={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(t=I(O(n,"allowGapCursor",e)))!==null&&t!==void 0?t:null}}}),Cg=gt.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",ht(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:t,state:e,editor:i})=>n.first([()=>n.exitCode(),()=>n.command(()=>{const{selection:s,storedMarks:r}=e;if(s.$from.parent.type.spec.isolating)return!1;const{keepMarks:o}=this.options,{splittableMarks:l}=i.extensionManager,a=r||s.$to.parentOffset&&s.$from.marks();return t().insertContent({type:this.name}).command(({tr:c,dispatch:h})=>{if(h&&a&&o){const d=a.filter(u=>l.includes(u.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),_g=gt.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:t}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,ht(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:n=>({commands:t})=>this.options.levels.includes(n.level)?t.setNode(this.name,n):!1,toggleHeading:n=>({commands:t})=>this.options.levels.includes(n.level)?t.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,t)=>({...n,[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}),{})},addInputRules(){return this.options.levels.map(n=>ar({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}}),Tg=500;class vt{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){if(this.eventCount==0)return null;let i=this.items.length;for(;;i--)if(this.items.get(i-1).selection){--i;break}let s,r;e&&(s=this.remapping(i,this.items.length),r=s.maps.length);let o=t.tr,l,a,c=[],h=[];return this.items.forEach((d,u)=>{if(!d.step){s||(s=this.remapping(i,u+1),r=s.maps.length),r--,h.push(d);return}if(s){h.push(new Rt(d.map));let f=d.step.map(s.slice(r)),p;f&&o.maybeStep(f).doc&&(p=o.mapping.maps[o.mapping.maps.length-1],c.push(new Rt(p,void 0,void 0,c.length+h.length))),r--,p&&s.appendMap(p,r)}else o.maybeStep(d.step);if(d.selection)return l=s?d.selection.map(s.slice(r)):d.selection,a=new vt(this.items.slice(0,i).append(h.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:o,selection:l}}addTransform(t,e,i,s){let r=[],o=this.eventCount,l=this.items,a=!s&&l.length?l.get(l.length-1):null;for(let h=0;h<t.steps.length;h++){let d=t.steps[h].invert(t.docs[h]),u=new Rt(t.mapping.maps[h],d,e),f;(f=a&&a.merge(u))&&(u=f,h?r.pop():l=l.slice(0,l.length-1)),r.push(u),e&&(o++,e=void 0),s||(a=u)}let c=o-i.depth;return c>vg&&(l=Og(l,c),o-=c),new vt(l.append(r),o)}remapping(t,e){let i=new Bn;return this.items.forEach((s,r)=>{let o=s.mirrorOffset!=null&&r-s.mirrorOffset>=t?i.maps.length-s.mirrorOffset:void 0;i.appendMap(s.map,o)},t,e),i}addMaps(t){return this.eventCount==0?this:new vt(this.items.append(t.map(e=>new Rt(e))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let i=[],s=Math.max(0,this.items.length-e),r=t.mapping,o=t.steps.length,l=this.eventCount;this.items.forEach(u=>{u.selection&&l--},s);let a=e;this.items.forEach(u=>{let f=r.getMirror(--a);if(f==null)return;o=Math.min(o,f);let p=r.maps[f];if(u.step){let m=t.steps[f].invert(t.docs[f]),g=u.selection&&u.selection.map(r.slice(a+1,f));g&&l++,i.push(new Rt(p,m,g))}else i.push(new Rt(p))},s);let c=[];for(let u=e;u<o;u++)c.push(new Rt(r.maps[u]));let h=this.items.slice(0,s).append(c).append(i),d=new vt(h,l);return d.emptyItemCount()>Tg&&(d=d.compress(this.items.length-i.length)),d}emptyItemCount(){let t=0;return this.items.forEach(e=>{e.step||t++}),t}compress(t=this.items.length){let e=this.remapping(0,t),i=e.maps.length,s=[],r=0;return this.items.forEach((o,l)=>{if(l>=t)s.push(o),o.selection&&r++;else if(o.step){let a=o.step.map(e.slice(i)),c=a&&a.getMap();if(i--,c&&e.appendMap(c,i),a){let h=o.selection&&o.selection.map(e.slice(i));h&&r++;let d=new Rt(c.invert(),a,h),u,f=s.length-1;(u=s.length&&s[f].merge(d))?s[f]=u:s.push(d)}}else o.map&&i--},this.items.length,0),new vt(Oa.from(s.reverse()),r)}}vt.empty=new vt(Oa.empty,0);function Og(n,t){let e;return n.forEach((i,s)=>{if(i.selection&&t--==0)return e=s,!1}),n.slice(e)}class Rt{constructor(t,e,i,s){this.map=t,this.step=e,this.selection=i,this.mirrorOffset=s}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new Rt(e.getMap().invert(),e,this.selection)}}}class Zt{constructor(t,e,i,s,r){this.done=t,this.undone=e,this.prevRanges=i,this.prevTime=s,this.prevComposition=r}}const vg=20;function Dg(n,t,e,i){let s=e.getMeta(Ie),r;if(s)return s.historyState;e.getMeta(Ng)&&(n=new Zt(n.done,n.undone,null,0,-1));let o=e.getMeta("appendedTransaction");if(e.steps.length==0)return n;if(o&&o.getMeta(Ie))return o.getMeta(Ie).redo?new Zt(n.done.addTransform(e,void 0,i,Ci(t)),n.undone,hl(e.mapping.maps),n.prevTime,n.prevComposition):new Zt(n.done,n.undone.addTransform(e,void 0,i,Ci(t)),null,n.prevTime,n.prevComposition);if(e.getMeta("addToHistory")!==!1&&!(o&&o.getMeta("addToHistory")===!1)){let l=e.getMeta("composition"),a=n.prevTime==0||!o&&n.prevComposition!=l&&(n.prevTime<(e.time||0)-i.newGroupDelay||!Ag(e,n.prevRanges)),c=o?Ds(n.prevRanges,e.mapping):hl(e.mapping.maps);return new Zt(n.done.addTransform(e,a?t.selection.getBookmark():void 0,i,Ci(t)),vt.empty,c,e.time,l??n.prevComposition)}else return(r=e.getMeta("rebased"))?new Zt(n.done.rebased(e,r),n.undone.rebased(e,r),Ds(n.prevRanges,e.mapping),n.prevTime,n.prevComposition):new Zt(n.done.addMaps(e.mapping.maps),n.undone.addMaps(e.mapping.maps),Ds(n.prevRanges,e.mapping),n.prevTime,n.prevComposition)}function Ag(n,t){if(!t)return!1;if(!n.docChanged)return!0;let e=!1;return n.mapping.maps[0].forEach((i,s)=>{for(let r=0;r<t.length;r+=2)i<=t[r+1]&&s>=t[r]&&(e=!0)}),e}function hl(n){let t=[];for(let e=n.length-1;e>=0&&t.length==0;e--)n[e].forEach((i,s,r,o)=>t.push(r,o));return t}function Ds(n,t){if(!n)return null;let e=[];for(let i=0;i<n.length;i+=2){let s=t.map(n[i],1),r=t.map(n[i+1],-1);s<=r&&e.push(s,r)}return e}function Eg(n,t,e){let i=Ci(t),s=Ie.get(t).spec.config,r=(e?n.undone:n.done).popEvent(t,i);if(!r)return null;let o=r.selection.resolve(r.transform.doc),l=(e?n.done:n.undone).addTransform(r.transform,t.selection.getBookmark(),s,i),a=new Zt(e?l:r.remaining,e?r.remaining:l,null,0,-1);return r.transform.setSelection(o).setMeta(Ie,{redo:e,historyState:a})}let As=!1,dl=null;function Ci(n){let t=n.plugins;if(dl!=t){As=!1,dl=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){As=!0;break}}return As}const Ie=new Ht("history"),Ng=new Ht("closeHistory");function Pg(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new yt({key:Ie,state:{init(){return new Zt(vt.empty,vt.empty,null,0,-1)},apply(t,e,i){return Dg(e,i,t,n)}},config:n,props:{handleDOMEvents:{beforeinput(t,e){let i=e.inputType,s=i=="historyUndo"?fh:i=="historyRedo"?ph:null;return s?(e.preventDefault(),s(t.state,t.dispatch)):!1}}}})}function uh(n,t){return(e,i)=>{let s=Ie.getState(e);if(!s||(n?s.undone:s.done).eventCount==0)return!1;if(i){let r=Eg(s,e,n);r&&i(t?r.scrollIntoView():r)}return!0}}const fh=uh(!1,!0),ph=uh(!0,!0),Ig=dt.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:t})=>fh(n,t),redo:()=>({state:n,dispatch:t})=>ph(n,t)}},addProseMirrorPlugins(){return[Pg(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),Rg=gt.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",ht(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:t})=>{if(!Ym(t,t.schema.nodes[this.name]))return!1;const{selection:e}=t,{$from:i,$to:s}=e,r=n();return i.parentOffset===0?r.insertContentAt({from:Math.max(i.pos-1,0),to:s.pos},{type:this.name}):bm(e)?r.insertContentAt(s.pos,{type:this.name}):r.insertContent({type:this.name}),r.command(({tr:o,dispatch:l})=>{var a;if(l){const{$to:c}=o.selection,h=c.end();if(c.nodeAfter)c.nodeAfter.isTextblock?o.setSelection(E.create(o.doc,c.pos+1)):c.nodeAfter.isBlock?o.setSelection(D.create(o.doc,c.pos)):o.setSelection(E.create(o.doc,c.pos));else{const d=(a=c.parent.type.contentMatch.defaultType)===null||a===void 0?void 0:a.create();d&&(o.insert(h,d),o.setSelection(E.create(o.doc,h+1)))}o.scrollIntoView()}return!0}).run()}}},addInputRules(){return[Gm({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Lg=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,Bg=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,zg=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,Fg=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,Vg=ce.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:n=>n.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[on({find:Lg,type:this.type}),on({find:zg,type:this.type})]},addPasteRules(){return[ln({find:Bg,type:this.type}),ln({find:Fg,type:this.type})]}}),Hg=gt.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",ht(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),$g="listItem",ul="textStyle",fl=/^(\d+)\.\s$/,Wg=gt.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1},type:{default:null,parseHTML:n=>n.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){const{start:t,...e}=n;return t===1?["ol",ht(this.options.HTMLAttributes,e),0]:["ol",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes($g,this.editor.getAttributes(ul)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=jn({find:fl,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=jn({find:fl,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(ul)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[n]}}),jg=gt.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Kg=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,qg=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,Jg=ce.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",ht(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[on({find:Kg,type:this.type})]},addPasteRules(){return[ln({find:qg,type:this.type})]}}),Ug=gt.create({name:"text",group:"inline"}),$0=dt.create({name:"starterKit",addExtensions(){const n=[];return this.options.bold!==!1&&n.push(ig.configure(this.options.bold)),this.options.blockquote!==!1&&n.push(Qm.configure(this.options.blockquote)),this.options.bulletList!==!1&&n.push(rg.configure(this.options.bulletList)),this.options.code!==!1&&n.push(ag.configure(this.options.code)),this.options.codeBlock!==!1&&n.push(dg.configure(this.options.codeBlock)),this.options.document!==!1&&n.push(ug.configure(this.options.document)),this.options.dropcursor!==!1&&n.push(mg.configure(this.options.dropcursor)),this.options.gapcursor!==!1&&n.push(wg.configure(this.options.gapcursor)),this.options.hardBreak!==!1&&n.push(Cg.configure(this.options.hardBreak)),this.options.heading!==!1&&n.push(_g.configure(this.options.heading)),this.options.history!==!1&&n.push(Ig.configure(this.options.history)),this.options.horizontalRule!==!1&&n.push(Rg.configure(this.options.horizontalRule)),this.options.italic!==!1&&n.push(Vg.configure(this.options.italic)),this.options.listItem!==!1&&n.push(Hg.configure(this.options.listItem)),this.options.orderedList!==!1&&n.push(Wg.configure(this.options.orderedList)),this.options.paragraph!==!1&&n.push(jg.configure(this.options.paragraph)),this.options.strike!==!1&&n.push(Jg.configure(this.options.strike)),this.options.text!==!1&&n.push(Ug.configure(this.options.text)),n}});/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Wt(){}const Gg=(()=>{let n=0;return()=>n++})();function H(n){return n==null}function U(n){if(Array.isArray&&Array.isArray(n))return!0;const t=Object.prototype.toString.call(n);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function B(n){return n!==null&&Object.prototype.toString.call(n)==="[object Object]"}function rt(n){return(typeof n=="number"||n instanceof Number)&&isFinite(+n)}function Pt(n,t){return rt(n)?n:t}function L(n,t){return typeof n>"u"?t:n}const Yg=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100*t:+n;function $(n,t,e){if(n&&typeof n.call=="function")return n.apply(e,t)}function F(n,t,e,i){let s,r,o;if(U(n))for(r=n.length,s=0;s<r;s++)t.call(e,n[s],s);else if(B(n))for(o=Object.keys(n),r=o.length,s=0;s<r;s++)t.call(e,n[o[s]],o[s])}function zi(n,t){let e,i,s,r;if(!n||!t||n.length!==t.length)return!1;for(e=0,i=n.length;e<i;++e)if(s=n[e],r=t[e],s.datasetIndex!==r.datasetIndex||s.index!==r.index)return!1;return!0}function Fi(n){if(U(n))return n.map(Fi);if(B(n)){const t=Object.create(null),e=Object.keys(n),i=e.length;let s=0;for(;s<i;++s)t[e[s]]=Fi(n[e[s]]);return t}return n}function mh(n){return["__proto__","prototype","constructor"].indexOf(n)===-1}function Xg(n,t,e,i){if(!mh(n))return;const s=t[n],r=e[n];B(s)&&B(r)?Kn(s,r,i):t[n]=Fi(r)}function Kn(n,t,e){const i=U(t)?t:[t],s=i.length;if(!B(n))return n;e=e||{};const r=e.merger||Xg;let o;for(let l=0;l<s;++l){if(o=i[l],!B(o))continue;const a=Object.keys(o);for(let c=0,h=a.length;c<h;++c)r(a[c],n,o,e)}return n}function Dn(n,t){return Kn(n,t,{merger:Qg})}function Qg(n,t,e){if(!mh(n))return;const i=t[n],s=e[n];B(i)&&B(s)?Dn(i,s):Object.prototype.hasOwnProperty.call(t,n)||(t[n]=Fi(s))}const pl={"":n=>n,x:n=>n.x,y:n=>n.y};function Zg(n){const t=n.split("."),e=[];let i="";for(const s of t)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(e.push(i),i="");return e}function ty(n){const t=Zg(n);return e=>{for(const i of t){if(i==="")break;e=e&&e[i]}return e}}function Vi(n,t){return(pl[t]||(pl[t]=ty(t)))(n)}function Wr(n){return n.charAt(0).toUpperCase()+n.slice(1)}const Hi=n=>typeof n<"u",he=n=>typeof n=="function",ml=(n,t)=>{if(n.size!==t.size)return!1;for(const e of n)if(!t.has(e))return!1;return!0};function ey(n){return n.type==="mouseup"||n.type==="click"||n.type==="contextmenu"}const X=Math.PI,At=2*X,ny=At+X,$i=Number.POSITIVE_INFINITY,iy=X/180,Dt=X/2,ge=X/4,gl=X*2/3,gh=Math.log10,an=Math.sign;function An(n,t,e){return Math.abs(n-t)<e}function yl(n){const t=Math.round(n);n=An(n,t,n/1e3)?t:n;const e=Math.pow(10,Math.floor(gh(n))),i=n/e;return(i<=1?1:i<=2?2:i<=5?5:10)*e}function sy(n){const t=[],e=Math.sqrt(n);let i;for(i=1;i<e;i++)n%i===0&&(t.push(i),t.push(n/i));return e===(e|0)&&t.push(e),t.sort((s,r)=>s-r).pop(),t}function ry(n){return typeof n=="symbol"||typeof n=="object"&&n!==null&&!(Symbol.toPrimitive in n||"toString"in n||"valueOf"in n)}function qn(n){return!ry(n)&&!isNaN(parseFloat(n))&&isFinite(n)}function oy(n,t){const e=Math.round(n);return e-t<=n&&e+t>=n}function ly(n,t,e){let i,s,r;for(i=0,s=n.length;i<s;i++)r=n[i][e],isNaN(r)||(t.min=Math.min(t.min,r),t.max=Math.max(t.max,r))}function Oe(n){return n*(X/180)}function ay(n){return n*(180/X)}function bl(n){if(!rt(n))return;let t=1,e=0;for(;Math.round(n*t)/t!==n;)t*=10,e++;return e}function cy(n,t){const e=t.x-n.x,i=t.y-n.y,s=Math.sqrt(e*e+i*i);let r=Math.atan2(i,e);return r<-.5*X&&(r+=At),{angle:r,distance:s}}function cr(n,t){return Math.sqrt(Math.pow(t.x-n.x,2)+Math.pow(t.y-n.y,2))}function hy(n,t){return(n-t+ny)%At-X}function Bt(n){return(n%At+At)%At}function yh(n,t,e,i){const s=Bt(n),r=Bt(t),o=Bt(e),l=Bt(r-s),a=Bt(o-s),c=Bt(s-r),h=Bt(s-o);return s===r||s===o||i&&r===o||l>a&&c<h}function Ct(n,t,e){return Math.max(t,Math.min(e,n))}function dy(n){return Ct(n,-32768,32767)}function Xe(n,t,e,i=1e-6){return n>=Math.min(t,e)-i&&n<=Math.max(t,e)+i}function jr(n,t,e){e=e||(o=>n[o]<t);let i=n.length-1,s=0,r;for(;i-s>1;)r=s+i>>1,e(r)?s=r:i=r;return{lo:s,hi:i}}const ve=(n,t,e,i)=>jr(n,e,i?s=>{const r=n[s][t];return r<e||r===e&&n[s+1][t]===e}:s=>n[s][t]<e),uy=(n,t,e)=>jr(n,e,i=>n[i][t]>=e);function fy(n,t,e){let i=0,s=n.length;for(;i<s&&n[i]<t;)i++;for(;s>i&&n[s-1]>e;)s--;return i>0||s<n.length?n.slice(i,s):n}const bh=["push","pop","shift","splice","unshift"];function py(n,t){if(n._chartjs){n._chartjs.listeners.push(t);return}Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),bh.forEach(e=>{const i="_onData"+Wr(e),s=n[e];Object.defineProperty(n,e,{configurable:!0,enumerable:!1,value(...r){const o=s.apply(this,r);return n._chartjs.listeners.forEach(l=>{typeof l[i]=="function"&&l[i](...r)}),o}})})}function xl(n,t){const e=n._chartjs;if(!e)return;const i=e.listeners,s=i.indexOf(t);s!==-1&&i.splice(s,1),!(i.length>0)&&(bh.forEach(r=>{delete n[r]}),delete n._chartjs)}function my(n){const t=new Set(n);return t.size===n.length?n:Array.from(t)}const xh=function(){return typeof window>"u"?function(n){return n()}:window.requestAnimationFrame}();function kh(n,t){let e=[],i=!1;return function(...s){e=s,i||(i=!0,xh.call(window,()=>{i=!1,n.apply(t,e)}))}}function gy(n,t){let e;return function(...i){return t?(clearTimeout(e),e=setTimeout(n,t,i)):n.apply(this,i),t}}const Kr=n=>n==="start"?"left":n==="end"?"right":"center",et=(n,t,e)=>n==="start"?t:n==="end"?e:(t+e)/2,yy=(n,t,e,i)=>n===(i?"left":"right")?e:n==="center"?(t+e)/2:t;function by(n,t,e){const i=t.length;let s=0,r=i;if(n._sorted){const{iScale:o,vScale:l,_parsed:a}=n,c=n.dataset&&n.dataset.options?n.dataset.options.spanGaps:null,h=o.axis,{min:d,max:u,minDefined:f,maxDefined:p}=o.getUserBounds();if(f){if(s=Math.min(ve(a,h,d).lo,e?i:ve(t,h,o.getPixelForValue(d)).lo),c){const m=a.slice(0,s+1).reverse().findIndex(g=>!H(g[l.axis]));s-=Math.max(0,m)}s=Ct(s,0,i-1)}if(p){let m=Math.max(ve(a,o.axis,u,!0).hi+1,e?0:ve(t,h,o.getPixelForValue(u),!0).hi+1);if(c){const g=a.slice(m-1).findIndex(y=>!H(y[l.axis]));m+=Math.max(0,g)}r=Ct(m,s,i)-s}else r=i-s}return{start:s,count:r}}function xy(n){const{xScale:t,yScale:e,_scaleRanges:i}=n,s={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!i)return n._scaleRanges=s,!0;const r=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==e.min||i.ymax!==e.max;return Object.assign(i,s),r}const ci=n=>n===0||n===1,kl=(n,t,e)=>-(Math.pow(2,10*(n-=1))*Math.sin((n-t)*At/e)),Sl=(n,t,e)=>Math.pow(2,-10*n)*Math.sin((n-t)*At/e)+1,En={linear:n=>n,easeInQuad:n=>n*n,easeOutQuad:n=>-n*(n-2),easeInOutQuad:n=>(n/=.5)<1?.5*n*n:-.5*(--n*(n-2)-1),easeInCubic:n=>n*n*n,easeOutCubic:n=>(n-=1)*n*n+1,easeInOutCubic:n=>(n/=.5)<1?.5*n*n*n:.5*((n-=2)*n*n+2),easeInQuart:n=>n*n*n*n,easeOutQuart:n=>-((n-=1)*n*n*n-1),easeInOutQuart:n=>(n/=.5)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2),easeInQuint:n=>n*n*n*n*n,easeOutQuint:n=>(n-=1)*n*n*n*n+1,easeInOutQuint:n=>(n/=.5)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2),easeInSine:n=>-Math.cos(n*Dt)+1,easeOutSine:n=>Math.sin(n*Dt),easeInOutSine:n=>-.5*(Math.cos(X*n)-1),easeInExpo:n=>n===0?0:Math.pow(2,10*(n-1)),easeOutExpo:n=>n===1?1:-Math.pow(2,-10*n)+1,easeInOutExpo:n=>ci(n)?n:n<.5?.5*Math.pow(2,10*(n*2-1)):.5*(-Math.pow(2,-10*(n*2-1))+2),easeInCirc:n=>n>=1?n:-(Math.sqrt(1-n*n)-1),easeOutCirc:n=>Math.sqrt(1-(n-=1)*n),easeInOutCirc:n=>(n/=.5)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1),easeInElastic:n=>ci(n)?n:kl(n,.075,.3),easeOutElastic:n=>ci(n)?n:Sl(n,.075,.3),easeInOutElastic(n){return ci(n)?n:n<.5?.5*kl(n*2,.1125,.45):.5+.5*Sl(n*2-1,.1125,.45)},easeInBack(n){return n*n*((1.70158+1)*n-1.70158)},easeOutBack(n){return(n-=1)*n*((1.70158+1)*n********)+1},easeInOutBack(n){let t=1.70158;return(n/=.5)<1?.5*(n*n*(((t*=1.525)+1)*n-t)):.5*((n-=2)*n*(((t*=1.525)+1)*n+t)+2)},easeInBounce:n=>1-En.easeOutBounce(1-n),easeOutBounce(n){return n<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375},easeInOutBounce:n=>n<.5?En.easeInBounce(n*2)*.5:En.easeOutBounce(n*2-1)*.5+.5};function qr(n){if(n&&typeof n=="object"){const t=n.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Ml(n){return qr(n)?n:new va(n)}function Es(n){return qr(n)?n:new va(n).saturate(.5).darken(.1).hexString()}const ky=["x","y","borderWidth","radius","tension"],Sy=["color","borderColor","backgroundColor"];function My(n){n.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),n.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),n.set("animations",{colors:{type:"color",properties:Sy},numbers:{type:"number",properties:ky}}),n.describe("animations",{_fallback:"animation"}),n.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function wy(n){n.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const wl=new Map;function Cy(n,t){t=t||{};const e=n+JSON.stringify(t);let i=wl.get(e);return i||(i=new Intl.NumberFormat(n,t),wl.set(e,i)),i}function Sh(n,t,e){return Cy(t,e).format(n)}const _y={values(n){return U(n)?n:""+n},numeric(n,t,e){if(n===0)return"0";const i=this.chart.options.locale;let s,r=n;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),r=Ty(n,e)}const o=gh(Math.abs(r)),l=isNaN(o)?1:Math.max(Math.min(-1*Math.floor(o),20),0),a={notation:s,minimumFractionDigits:l,maximumFractionDigits:l};return Object.assign(a,this.options.ticks.format),Sh(n,i,a)}};function Ty(n,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),e}var Mh={formatters:_y};function Oy(n){n.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Mh.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),n.route("scale.ticks","color","","color"),n.route("scale.grid","color","","borderColor"),n.route("scale.border","color","","borderColor"),n.route("scale.title","color","","color"),n.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),n.describe("scales",{_fallback:"scale"}),n.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Ve=Object.create(null),hr=Object.create(null);function Nn(n,t){if(!t)return n;const e=t.split(".");for(let i=0,s=e.length;i<s;++i){const r=e[i];n=n[r]||(n[r]=Object.create(null))}return n}function Ns(n,t,e){return typeof t=="string"?Kn(Nn(n,t),e):Kn(Nn(n,""),t)}class vy{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>Es(s.backgroundColor),this.hoverBorderColor=(i,s)=>Es(s.borderColor),this.hoverColor=(i,s)=>Es(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ns(this,t,e)}get(t){return Nn(this,t)}describe(t,e){return Ns(hr,t,e)}override(t,e){return Ns(Ve,t,e)}route(t,e,i,s){const r=Nn(this,t),o=Nn(this,i),l="_"+e;Object.defineProperties(r,{[l]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){const a=this[l],c=o[s];return B(a)?Object.assign({},c,a):L(a,c)},set(a){this[l]=a}}})}apply(t){t.forEach(e=>e(this))}}var K=new vy({_scriptable:n=>!n.startsWith("on"),_indexable:n=>n!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[My,wy,Oy]);function Dy(n){return!n||H(n.size)||H(n.family)?null:(n.style?n.style+" ":"")+(n.weight?n.weight+" ":"")+n.size+"px "+n.family}function Cl(n,t,e,i,s){let r=t[s];return r||(r=t[s]=n.measureText(s).width,e.push(s)),r>i&&(i=r),i}function ye(n,t,e){const i=n.currentDevicePixelRatio,s=e!==0?Math.max(e/2,.5):0;return Math.round((t-s)*i)/i+s}function _l(n,t){!t&&!n||(t=t||n.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,n.width,n.height),t.restore())}function dr(n,t,e,i){wh(n,t,e,i,null)}function wh(n,t,e,i,s){let r,o,l,a,c,h,d,u;const f=t.pointStyle,p=t.rotation,m=t.radius;let g=(p||0)*iy;if(f&&typeof f=="object"&&(r=f.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){n.save(),n.translate(e,i),n.rotate(g),n.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),n.restore();return}if(!(isNaN(m)||m<=0)){switch(n.beginPath(),f){default:s?n.ellipse(e,i,s/2,m,0,0,At):n.arc(e,i,m,0,At),n.closePath();break;case"triangle":h=s?s/2:m,n.moveTo(e+Math.sin(g)*h,i-Math.cos(g)*m),g+=gl,n.lineTo(e+Math.sin(g)*h,i-Math.cos(g)*m),g+=gl,n.lineTo(e+Math.sin(g)*h,i-Math.cos(g)*m),n.closePath();break;case"rectRounded":c=m*.516,a=m-c,o=Math.cos(g+ge)*a,d=Math.cos(g+ge)*(s?s/2-c:a),l=Math.sin(g+ge)*a,u=Math.sin(g+ge)*(s?s/2-c:a),n.arc(e-d,i-l,c,g-X,g-Dt),n.arc(e+u,i-o,c,g-Dt,g),n.arc(e+d,i+l,c,g,g+Dt),n.arc(e-u,i+o,c,g+Dt,g+X),n.closePath();break;case"rect":if(!p){a=Math.SQRT1_2*m,h=s?s/2:a,n.rect(e-h,i-a,2*h,2*a);break}g+=ge;case"rectRot":d=Math.cos(g)*(s?s/2:m),o=Math.cos(g)*m,l=Math.sin(g)*m,u=Math.sin(g)*(s?s/2:m),n.moveTo(e-d,i-l),n.lineTo(e+u,i-o),n.lineTo(e+d,i+l),n.lineTo(e-u,i+o),n.closePath();break;case"crossRot":g+=ge;case"cross":d=Math.cos(g)*(s?s/2:m),o=Math.cos(g)*m,l=Math.sin(g)*m,u=Math.sin(g)*(s?s/2:m),n.moveTo(e-d,i-l),n.lineTo(e+d,i+l),n.moveTo(e+u,i-o),n.lineTo(e-u,i+o);break;case"star":d=Math.cos(g)*(s?s/2:m),o=Math.cos(g)*m,l=Math.sin(g)*m,u=Math.sin(g)*(s?s/2:m),n.moveTo(e-d,i-l),n.lineTo(e+d,i+l),n.moveTo(e+u,i-o),n.lineTo(e-u,i+o),g+=ge,d=Math.cos(g)*(s?s/2:m),o=Math.cos(g)*m,l=Math.sin(g)*m,u=Math.sin(g)*(s?s/2:m),n.moveTo(e-d,i-l),n.lineTo(e+d,i+l),n.moveTo(e+u,i-o),n.lineTo(e-u,i+o);break;case"line":o=s?s/2:Math.cos(g)*m,l=Math.sin(g)*m,n.moveTo(e-o,i-l),n.lineTo(e+o,i+l);break;case"dash":n.moveTo(e,i),n.lineTo(e+Math.cos(g)*(s?s/2:m),i+Math.sin(g)*m);break;case!1:n.closePath();break}n.fill(),t.borderWidth>0&&n.stroke()}}function Jn(n,t,e){return e=e||.5,!t||n&&n.x>t.left-e&&n.x<t.right+e&&n.y>t.top-e&&n.y<t.bottom+e}function rs(n,t){n.save(),n.beginPath(),n.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),n.clip()}function ls(n){n.restore()}function Ay(n,t,e,i,s){if(!t)return n.lineTo(e.x,e.y);if(s==="middle"){const r=(t.x+e.x)/2;n.lineTo(r,t.y),n.lineTo(r,e.y)}else s==="after"!=!!i?n.lineTo(t.x,e.y):n.lineTo(e.x,t.y);n.lineTo(e.x,e.y)}function Ey(n,t,e,i){if(!t)return n.lineTo(e.x,e.y);n.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?e.cp2x:e.cp1x,i?e.cp2y:e.cp1y,e.x,e.y)}function Ny(n,t){t.translation&&n.translate(t.translation[0],t.translation[1]),H(t.rotation)||n.rotate(t.rotation),t.color&&(n.fillStyle=t.color),t.textAlign&&(n.textAlign=t.textAlign),t.textBaseline&&(n.textBaseline=t.textBaseline)}function Py(n,t,e,i,s){if(s.strikethrough||s.underline){const r=n.measureText(i),o=t-r.actualBoundingBoxLeft,l=t+r.actualBoundingBoxRight,a=e-r.actualBoundingBoxAscent,c=e+r.actualBoundingBoxDescent,h=s.strikethrough?(a+c)/2:c;n.strokeStyle=n.fillStyle,n.beginPath(),n.lineWidth=s.decorationWidth||2,n.moveTo(o,h),n.lineTo(l,h),n.stroke()}}function Iy(n,t){const e=n.fillStyle;n.fillStyle=t.color,n.fillRect(t.left,t.top,t.width,t.height),n.fillStyle=e}function Un(n,t,e,i,s,r={}){const o=U(t)?t:[t],l=r.strokeWidth>0&&r.strokeColor!=="";let a,c;for(n.save(),n.font=s.string,Ny(n,r),a=0;a<o.length;++a)c=o[a],r.backdrop&&Iy(n,r.backdrop),l&&(r.strokeColor&&(n.strokeStyle=r.strokeColor),H(r.strokeWidth)||(n.lineWidth=r.strokeWidth),n.strokeText(c,e,i,r.maxWidth)),n.fillText(c,e,i,r.maxWidth),Py(n,e,i,c,r),i+=Number(s.lineHeight);n.restore()}function ur(n,t){const{x:e,y:i,w:s,h:r,radius:o}=t;n.arc(e+o.topLeft,i+o.topLeft,o.topLeft,1.5*X,X,!0),n.lineTo(e,i+r-o.bottomLeft),n.arc(e+o.bottomLeft,i+r-o.bottomLeft,o.bottomLeft,X,Dt,!0),n.lineTo(e+s-o.bottomRight,i+r),n.arc(e+s-o.bottomRight,i+r-o.bottomRight,o.bottomRight,Dt,0,!0),n.lineTo(e+s,i+o.topRight),n.arc(e+s-o.topRight,i+o.topRight,o.topRight,0,-Dt,!0),n.lineTo(e+o.topLeft,i)}const Ry=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Ly=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function By(n,t){const e=(""+n).match(Ry);if(!e||e[1]==="normal")return t*1.2;switch(n=+e[2],e[3]){case"px":return n;case"%":n/=100;break}return t*n}const zy=n=>+n||0;function Ch(n,t){const e={},i=B(t),s=i?Object.keys(t):t,r=B(n)?i?o=>L(n[o],n[t[o]]):o=>n[o]:()=>n;for(const o of s)e[o]=zy(r(o));return e}function Fy(n){return Ch(n,{top:"y",right:"x",bottom:"y",left:"x"})}function Pn(n){return Ch(n,["topLeft","topRight","bottomLeft","bottomRight"])}function Ot(n){const t=Fy(n);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function st(n,t){n=n||{},t=t||K.font;let e=L(n.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let i=L(n.style,t.style);i&&!(""+i).match(Ly)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:L(n.family,t.family),lineHeight:By(L(n.lineHeight,t.lineHeight),e),size:e,style:i,weight:L(n.weight,t.weight),string:""};return s.string=Dy(s),s}function hi(n,t,e,i){let s,r,o;for(s=0,r=n.length;s<r;++s)if(o=n[s],o!==void 0&&o!==void 0)return o}function Vy(n,t,e){const{min:i,max:s}=n,r=Yg(t,(s-i)/2),o=(l,a)=>e&&l===0?0:l+a;return{min:o(i,-Math.abs(r)),max:o(s,r)}}function $e(n,t){return Object.assign(Object.create(n),t)}function Jr(n,t=[""],e,i,s=()=>n[0]){const r=e||n;typeof i>"u"&&(i=vh("_fallback",n));const o={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:n,_rootScopes:r,_fallback:i,_getTarget:s,override:l=>Jr([l,...n],t,r,i)};return new Proxy(o,{deleteProperty(l,a){return delete l[a],delete l._keys,delete n[0][a],!0},get(l,a){return Th(l,a,()=>Uy(a,t,n,l))},getOwnPropertyDescriptor(l,a){return Reflect.getOwnPropertyDescriptor(l._scopes[0],a)},getPrototypeOf(){return Reflect.getPrototypeOf(n[0])},has(l,a){return Ol(l).includes(a)},ownKeys(l){return Ol(l)},set(l,a,c){const h=l._storage||(l._storage=s());return l[a]=h[a]=c,delete l._keys,!0}})}function cn(n,t,e,i){const s={_cacheable:!1,_proxy:n,_context:t,_subProxy:e,_stack:new Set,_descriptors:_h(n,i),setContext:r=>cn(n,r,e,i),override:r=>cn(n.override(r),t,e,i)};return new Proxy(s,{deleteProperty(r,o){return delete r[o],delete n[o],!0},get(r,o,l){return Th(r,o,()=>$y(r,o,l))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(n,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(n,o)},getPrototypeOf(){return Reflect.getPrototypeOf(n)},has(r,o){return Reflect.has(n,o)},ownKeys(){return Reflect.ownKeys(n)},set(r,o,l){return n[o]=l,delete r[o],!0}})}function _h(n,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:i=t.indexable,_allKeys:s=t.allKeys}=n;return{allKeys:s,scriptable:e,indexable:i,isScriptable:he(e)?e:()=>e,isIndexable:he(i)?i:()=>i}}const Hy=(n,t)=>n?n+Wr(t):t,Ur=(n,t)=>B(t)&&n!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Th(n,t,e){if(Object.prototype.hasOwnProperty.call(n,t)||t==="constructor")return n[t];const i=e();return n[t]=i,i}function $y(n,t,e){const{_proxy:i,_context:s,_subProxy:r,_descriptors:o}=n;let l=i[t];return he(l)&&o.isScriptable(t)&&(l=Wy(t,l,n,e)),U(l)&&l.length&&(l=jy(t,l,n,o.isIndexable)),Ur(t,l)&&(l=cn(l,s,r&&r[t],o)),l}function Wy(n,t,e,i){const{_proxy:s,_context:r,_subProxy:o,_stack:l}=e;if(l.has(n))throw new Error("Recursion detected: "+Array.from(l).join("->")+"->"+n);l.add(n);let a=t(r,o||i);return l.delete(n),Ur(n,a)&&(a=Gr(s._scopes,s,n,a)),a}function jy(n,t,e,i){const{_proxy:s,_context:r,_subProxy:o,_descriptors:l}=e;if(typeof r.index<"u"&&i(n))return t[r.index%t.length];if(B(t[0])){const a=t,c=s._scopes.filter(h=>h!==a);t=[];for(const h of a){const d=Gr(c,s,n,h);t.push(cn(d,r,o&&o[n],l))}}return t}function Oh(n,t,e){return he(n)?n(t,e):n}const Ky=(n,t)=>n===!0?t:typeof n=="string"?Vi(t,n):void 0;function qy(n,t,e,i,s){for(const r of t){const o=Ky(e,r);if(o){n.add(o);const l=Oh(o._fallback,e,s);if(typeof l<"u"&&l!==e&&l!==i)return l}else if(o===!1&&typeof i<"u"&&e!==i)return null}return!1}function Gr(n,t,e,i){const s=t._rootScopes,r=Oh(t._fallback,e,i),o=[...n,...s],l=new Set;l.add(i);let a=Tl(l,o,e,r||e,i);return a===null||typeof r<"u"&&r!==e&&(a=Tl(l,o,r,a,i),a===null)?!1:Jr(Array.from(l),[""],s,r,()=>Jy(t,e,i))}function Tl(n,t,e,i,s){for(;e;)e=qy(n,t,e,i,s);return e}function Jy(n,t,e){const i=n._getTarget();t in i||(i[t]={});const s=i[t];return U(s)&&B(e)?e:s||{}}function Uy(n,t,e,i){let s;for(const r of t)if(s=vh(Hy(r,n),e),typeof s<"u")return Ur(n,s)?Gr(e,i,n,s):s}function vh(n,t){for(const e of t){if(!e)continue;const i=e[n];if(typeof i<"u")return i}}function Ol(n){let t=n._keys;return t||(t=n._keys=Gy(n._scopes)),t}function Gy(n){const t=new Set;for(const e of n)for(const i of Object.keys(e).filter(s=>!s.startsWith("_")))t.add(i);return Array.from(t)}const Yy=Number.EPSILON||1e-14,hn=(n,t)=>t<n.length&&!n[t].skip&&n[t],Dh=n=>n==="x"?"y":"x";function Xy(n,t,e,i){const s=n.skip?t:n,r=t,o=e.skip?t:e,l=cr(r,s),a=cr(o,r);let c=l/(l+a),h=a/(l+a);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=i*c,u=i*h;return{previous:{x:r.x-d*(o.x-s.x),y:r.y-d*(o.y-s.y)},next:{x:r.x+u*(o.x-s.x),y:r.y+u*(o.y-s.y)}}}function Qy(n,t,e){const i=n.length;let s,r,o,l,a,c=hn(n,0);for(let h=0;h<i-1;++h)if(a=c,c=hn(n,h+1),!(!a||!c)){if(An(t[h],0,Yy)){e[h]=e[h+1]=0;continue}s=e[h]/t[h],r=e[h+1]/t[h],l=Math.pow(s,2)+Math.pow(r,2),!(l<=9)&&(o=3/Math.sqrt(l),e[h]=s*o*t[h],e[h+1]=r*o*t[h])}}function Zy(n,t,e="x"){const i=Dh(e),s=n.length;let r,o,l,a=hn(n,0);for(let c=0;c<s;++c){if(o=l,l=a,a=hn(n,c+1),!l)continue;const h=l[e],d=l[i];o&&(r=(h-o[e])/3,l[`cp1${e}`]=h-r,l[`cp1${i}`]=d-r*t[c]),a&&(r=(a[e]-h)/3,l[`cp2${e}`]=h+r,l[`cp2${i}`]=d+r*t[c])}}function tb(n,t="x"){const e=Dh(t),i=n.length,s=Array(i).fill(0),r=Array(i);let o,l,a,c=hn(n,0);for(o=0;o<i;++o)if(l=a,a=c,c=hn(n,o+1),!!a){if(c){const h=c[t]-a[t];s[o]=h!==0?(c[e]-a[e])/h:0}r[o]=l?c?an(s[o-1])!==an(s[o])?0:(s[o-1]+s[o])/2:s[o-1]:s[o]}Qy(n,s,r),Zy(n,r,t)}function di(n,t,e){return Math.max(Math.min(n,e),t)}function eb(n,t){let e,i,s,r,o,l=Jn(n[0],t);for(e=0,i=n.length;e<i;++e)o=r,r=l,l=e<i-1&&Jn(n[e+1],t),r&&(s=n[e],o&&(s.cp1x=di(s.cp1x,t.left,t.right),s.cp1y=di(s.cp1y,t.top,t.bottom)),l&&(s.cp2x=di(s.cp2x,t.left,t.right),s.cp2y=di(s.cp2y,t.top,t.bottom)))}function nb(n,t,e,i,s){let r,o,l,a;if(t.spanGaps&&(n=n.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")tb(n,s);else{let c=i?n[n.length-1]:n[0];for(r=0,o=n.length;r<o;++r)l=n[r],a=Xy(c,l,n[Math.min(r+1,o-(i?0:1))%o],t.tension),l.cp1x=a.previous.x,l.cp1y=a.previous.y,l.cp2x=a.next.x,l.cp2y=a.next.y,c=l}t.capBezierPoints&&eb(n,e)}function Yr(){return typeof window<"u"&&typeof document<"u"}function Xr(n){let t=n.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Wi(n,t,e){let i;return typeof n=="string"?(i=parseInt(n,10),n.indexOf("%")!==-1&&(i=i/100*t.parentNode[e])):i=n,i}const as=n=>n.ownerDocument.defaultView.getComputedStyle(n,null);function ib(n,t){return as(n).getPropertyValue(t)}const sb=["top","right","bottom","left"];function Re(n,t,e){const i={};e=e?"-"+e:"";for(let s=0;s<4;s++){const r=sb[s];i[r]=parseFloat(n[t+"-"+r+e])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const rb=(n,t,e)=>(n>0||t>0)&&(!e||!e.shadowRoot);function ob(n,t){const e=n.touches,i=e&&e.length?e[0]:n,{offsetX:s,offsetY:r}=i;let o=!1,l,a;if(rb(s,r,n.target))l=s,a=r;else{const c=t.getBoundingClientRect();l=i.clientX-c.left,a=i.clientY-c.top,o=!0}return{x:l,y:a,box:o}}function xe(n,t){if("native"in n)return n;const{canvas:e,currentDevicePixelRatio:i}=t,s=as(e),r=s.boxSizing==="border-box",o=Re(s,"padding"),l=Re(s,"border","width"),{x:a,y:c,box:h}=ob(n,e),d=o.left+(h&&l.left),u=o.top+(h&&l.top);let{width:f,height:p}=t;return r&&(f-=o.width+l.width,p-=o.height+l.height),{x:Math.round((a-d)/f*e.width/i),y:Math.round((c-u)/p*e.height/i)}}function lb(n,t,e){let i,s;if(t===void 0||e===void 0){const r=n&&Xr(n);if(!r)t=n.clientWidth,e=n.clientHeight;else{const o=r.getBoundingClientRect(),l=as(r),a=Re(l,"border","width"),c=Re(l,"padding");t=o.width-c.width-a.width,e=o.height-c.height-a.height,i=Wi(l.maxWidth,r,"clientWidth"),s=Wi(l.maxHeight,r,"clientHeight")}}return{width:t,height:e,maxWidth:i||$i,maxHeight:s||$i}}const ui=n=>Math.round(n*10)/10;function ab(n,t,e,i){const s=as(n),r=Re(s,"margin"),o=Wi(s.maxWidth,n,"clientWidth")||$i,l=Wi(s.maxHeight,n,"clientHeight")||$i,a=lb(n,t,e);let{width:c,height:h}=a;if(s.boxSizing==="content-box"){const u=Re(s,"border","width"),f=Re(s,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-r.width),h=Math.max(0,i?c/i:h-r.height),c=ui(Math.min(c,o,a.maxWidth)),h=ui(Math.min(h,l,a.maxHeight)),c&&!h&&(h=ui(c/2)),(t!==void 0||e!==void 0)&&i&&a.height&&h>a.height&&(h=a.height,c=ui(Math.floor(h*i))),{width:c,height:h}}function vl(n,t,e){const i=t||1,s=Math.floor(n.height*i),r=Math.floor(n.width*i);n.height=Math.floor(n.height),n.width=Math.floor(n.width);const o=n.canvas;return o.style&&(e||!o.style.height&&!o.style.width)&&(o.style.height=`${n.height}px`,o.style.width=`${n.width}px`),n.currentDevicePixelRatio!==i||o.height!==s||o.width!==r?(n.currentDevicePixelRatio=i,o.height=s,o.width=r,n.ctx.setTransform(i,0,0,i,0,0),!0):!1}const cb=function(){let n=!1;try{const t={get passive(){return n=!0,!1}};Yr()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return n}();function Dl(n,t){const e=ib(n,t),i=e&&e.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function ke(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}}function hb(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:i==="middle"?e<.5?n.y:t.y:i==="after"?e<1?n.y:t.y:e>0?t.y:n.y}}function db(n,t,e,i){const s={x:n.cp2x,y:n.cp2y},r={x:t.cp1x,y:t.cp1y},o=ke(n,s,e),l=ke(s,r,e),a=ke(r,t,e),c=ke(o,l,e),h=ke(l,a,e);return ke(c,h,e)}const ub=function(n,t){return{x(e){return n+n+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,i){return e-i},leftForLtr(e,i){return e-i}}},fb=function(){return{x(n){return n},setWidth(n){},textAlign(n){return n},xPlus(n,t){return n+t},leftForLtr(n,t){return n}}};function tn(n,t,e){return n?ub(t,e):fb()}function Ah(n,t){let e,i;(t==="ltr"||t==="rtl")&&(e=n.canvas.style,i=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),n.prevTextDirection=i)}function Eh(n,t){t!==void 0&&(delete n.prevTextDirection,n.canvas.style.setProperty("direction",t[0],t[1]))}function Nh(n){return n==="angle"?{between:yh,compare:hy,normalize:Bt}:{between:Xe,compare:(t,e)=>t-e,normalize:t=>t}}function Al({start:n,end:t,count:e,loop:i,style:s}){return{start:n%e,end:t%e,loop:i&&(t-n+1)%e===0,style:s}}function pb(n,t,e){const{property:i,start:s,end:r}=e,{between:o,normalize:l}=Nh(i),a=t.length;let{start:c,end:h,loop:d}=n,u,f;if(d){for(c+=a,h+=a,u=0,f=a;u<f&&o(l(t[c%a][i]),s,r);++u)c--,h--;c%=a,h%=a}return h<c&&(h+=a),{start:c,end:h,loop:d,style:n.style}}function Ph(n,t,e){if(!e)return[n];const{property:i,start:s,end:r}=e,o=t.length,{compare:l,between:a,normalize:c}=Nh(i),{start:h,end:d,loop:u,style:f}=pb(n,t,e),p=[];let m=!1,g=null,y,b,M;const w=()=>a(s,M,y)&&l(s,M)!==0,x=()=>l(r,y)===0||a(r,M,y),C=()=>m||w(),_=()=>!m||x();for(let k=h,v=h;k<=d;++k)b=t[k%o],!b.skip&&(y=c(b[i]),y!==M&&(m=a(y,s,r),g===null&&C()&&(g=l(y,s)===0?k:v),g!==null&&_()&&(p.push(Al({start:g,end:k,loop:u,count:o,style:f})),g=null),v=k,M=y));return g!==null&&p.push(Al({start:g,end:d,loop:u,count:o,style:f})),p}function Ih(n,t){const e=[],i=n.segments;for(let s=0;s<i.length;s++){const r=Ph(i[s],n.points,t);r.length&&e.push(...r)}return e}function mb(n,t,e,i){let s=0,r=t-1;if(e&&!i)for(;s<t&&!n[s].skip;)s++;for(;s<t&&n[s].skip;)s++;for(s%=t,e&&(r+=s);r>s&&n[r%t].skip;)r--;return r%=t,{start:s,end:r}}function gb(n,t,e,i){const s=n.length,r=[];let o=t,l=n[t],a;for(a=t+1;a<=e;++a){const c=n[a%s];c.skip||c.stop?l.skip||(i=!1,r.push({start:t%s,end:(a-1)%s,loop:i}),t=o=c.stop?a:null):(o=a,l.skip&&(t=a)),l=c}return o!==null&&r.push({start:t%s,end:o%s,loop:i}),r}function yb(n,t){const e=n.points,i=n.options.spanGaps,s=e.length;if(!s)return[];const r=!!n._loop,{start:o,end:l}=mb(e,s,r,i);if(i===!0)return El(n,[{start:o,end:l,loop:r}],e,t);const a=l<o?l+s:l,c=!!n._fullLoop&&o===0&&l===s-1;return El(n,gb(e,o,a,c),e,t)}function El(n,t,e,i){return!i||!i.setContext||!e?t:bb(n,t,e,i)}function bb(n,t,e,i){const s=n._chart.getContext(),r=Nl(n.options),{_datasetIndex:o,options:{spanGaps:l}}=n,a=e.length,c=[];let h=r,d=t[0].start,u=d;function f(p,m,g,y){const b=l?-1:1;if(p!==m){for(p+=a;e[p%a].skip;)p-=b;for(;e[m%a].skip;)m+=b;p%a!==m%a&&(c.push({start:p%a,end:m%a,loop:g,style:y}),h=y,d=m%a)}}for(const p of t){d=l?d:p.start;let m=e[d%a],g;for(u=d+1;u<=p.end;u++){const y=e[u%a];g=Nl(i.setContext($e(s,{type:"segment",p0:m,p1:y,p0DataIndex:(u-1)%a,p1DataIndex:u%a,datasetIndex:o}))),xb(g,h)&&f(d,u-1,p.loop,h),m=y,h=g}d<u-1&&f(d,u-1,p.loop,h)}return c}function Nl(n){return{backgroundColor:n.backgroundColor,borderCapStyle:n.borderCapStyle,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderJoinStyle:n.borderJoinStyle,borderWidth:n.borderWidth,borderColor:n.borderColor}}function xb(n,t){if(!t)return!1;const e=[],i=function(s,r){return qr(r)?(e.includes(r)||e.push(r),e.indexOf(r)):r};return JSON.stringify(n,i)!==JSON.stringify(t,i)}function fi(n,t,e){return n.options.clip?n[e]:t[e]}function kb(n,t){const{xScale:e,yScale:i}=n;return e&&i?{left:fi(e,t,"left"),right:fi(e,t,"right"),top:fi(i,t,"top"),bottom:fi(i,t,"bottom")}:t}function Rh(n,t){const e=t._clip;if(e.disabled)return!1;const i=kb(t,n.chartArea);return{left:e.left===!1?0:i.left-(e.left===!0?0:e.left),right:e.right===!1?n.width:i.right+(e.right===!0?0:e.right),top:e.top===!1?0:i.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?n.height:i.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Sb{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){const r=e.listeners[s],o=e.duration;r.forEach(l=>l({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=xh.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const r=i.items;let o=r.length-1,l=!1,a;for(;o>=0;--o)a=r[o],a._active?(a._total>i.duration&&(i.duration=a._total),a.tick(t),l=!0):(r[o]=r[r.length-1],r.pop());l&&(s.draw(),this._notify(s,i,t,"progress")),r.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=r.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var jt=new Sb;const Pl="transparent",Mb={boolean(n,t,e){return e>.5?t:n},color(n,t,e){const i=Ml(n||Pl),s=i.valid&&Ml(t||Pl);return s&&s.valid?s.mix(i,e).hexString():t},number(n,t,e){return n+(t-n)*e}};class wb{constructor(t,e,i,s){const r=e[i];s=hi([t.to,s,r,t.from]);const o=hi([t.from,r,s]);this._active=!0,this._fn=t.fn||Mb[t.type||typeof o],this._easing=En[t.easing]||En.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const s=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=hi([t.to,e,s,t.from]),this._from=hi([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,s=this._prop,r=this._from,o=this._loop,l=this._to;let a;if(this._active=r!==l&&(o||e<i),!this._active){this._target[s]=l,this._notify(!0);return}if(e<0){this._target[s]=r;return}a=e/i%2,a=o&&a>1?2-a:a,a=this._easing(Math.min(1,Math.max(0,a))),this._target[s]=this._fn(r,l,a)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][e]()}}class Lh{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!B(t))return;const e=Object.keys(K.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{const r=t[s];if(!B(r))return;const o={};for(const l of e)o[l]=r[l];(U(r.properties)&&r.properties||[s]).forEach(l=>{(l===s||!i.has(l))&&i.set(l,o)})})}_animateOptions(t,e){const i=e.options,s=_b(t,i);if(!s)return[];const r=this._createAnimations(s,i);return i.$shared&&Cb(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){const i=this._properties,s=[],r=t.$animations||(t.$animations={}),o=Object.keys(e),l=Date.now();let a;for(a=o.length-1;a>=0;--a){const c=o[a];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,e));continue}const h=e[c];let d=r[c];const u=i.get(c);if(d)if(u&&d.active()){d.update(u,h,l);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}r[c]=d=new wb(u,t,c,h),s.push(d)}return s}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const i=this._createAnimations(t,e);if(i.length)return jt.add(this._chart,i),!0}}function Cb(n,t){const e=[],i=Object.keys(t);for(let s=0;s<i.length;s++){const r=n[i[s]];r&&r.active()&&e.push(r.wait())}return Promise.all(e)}function _b(n,t){if(!t)return;let e=n.options;if(!e){n.options=t;return}return e.$shared&&(n.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Il(n,t){const e=n&&n.options||{},i=e.reverse,s=e.min===void 0?t:0,r=e.max===void 0?t:0;return{start:i?r:s,end:i?s:r}}function Tb(n,t,e){if(e===!1)return!1;const i=Il(n,e),s=Il(t,e);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function Ob(n){let t,e,i,s;return B(n)?(t=n.top,e=n.right,i=n.bottom,s=n.left):t=e=i=s=n,{top:t,right:e,bottom:i,left:s,disabled:n===!1}}function Bh(n,t){const e=[],i=n._getSortedDatasetMetas(t);let s,r;for(s=0,r=i.length;s<r;++s)e.push(i[s].index);return e}function Rl(n,t,e,i={}){const s=n.keys,r=i.mode==="single";let o,l,a,c;if(t===null)return;let h=!1;for(o=0,l=s.length;o<l;++o){if(a=+s[o],a===e){if(h=!0,i.all)continue;break}c=n.values[a],rt(c)&&(r||t===0||an(t)===an(c))&&(t+=c)}return!h&&!i.all?0:t}function vb(n,t){const{iScale:e,vScale:i}=t,s=e.axis==="x"?"x":"y",r=i.axis==="x"?"x":"y",o=Object.keys(n),l=new Array(o.length);let a,c,h;for(a=0,c=o.length;a<c;++a)h=o[a],l[a]={[s]:h,[r]:n[h]};return l}function Ps(n,t){const e=n&&n.options.stacked;return e||e===void 0&&t.stack!==void 0}function Db(n,t,e){return`${n.id}.${t.id}.${e.stack||e.type}`}function Ab(n){const{min:t,max:e,minDefined:i,maxDefined:s}=n.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:s?e:Number.POSITIVE_INFINITY}}function Eb(n,t,e){const i=n[t]||(n[t]={});return i[e]||(i[e]={})}function Ll(n,t,e,i){for(const s of t.getMatchingVisibleMetas(i).reverse()){const r=n[s.index];if(e&&r>0||!e&&r<0)return s.index}return null}function Bl(n,t){const{chart:e,_cachedMeta:i}=n,s=e._stacks||(e._stacks={}),{iScale:r,vScale:o,index:l}=i,a=r.axis,c=o.axis,h=Db(r,o,i),d=t.length;let u;for(let f=0;f<d;++f){const p=t[f],{[a]:m,[c]:g}=p,y=p._stacks||(p._stacks={});u=y[c]=Eb(s,h,m),u[l]=g,u._top=Ll(u,o,!0,i.type),u._bottom=Ll(u,o,!1,i.type);const b=u._visualValues||(u._visualValues={});b[l]=g}}function Is(n,t){const e=n.scales;return Object.keys(e).filter(i=>e[i].axis===t).shift()}function Nb(n,t){return $e(n,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Pb(n,t,e){return $e(n,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function gn(n,t){const e=n.controller.index,i=n.vScale&&n.vScale.axis;if(i){t=t||n._parsed;for(const s of t){const r=s._stacks;if(!r||r[i]===void 0||r[i][e]===void 0)return;delete r[i][e],r[i]._visualValues!==void 0&&r[i]._visualValues[e]!==void 0&&delete r[i]._visualValues[e]}}}const Rs=n=>n==="reset"||n==="none",zl=(n,t)=>t?n:Object.assign({},n),Ib=(n,t,e)=>n&&!t.hidden&&t._stacked&&{keys:Bh(e,!0),values:null};class In{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ps(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&gn(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(d,u,f,p)=>d==="x"?u:d==="r"?p:f,r=e.xAxisID=L(i.xAxisID,Is(t,"x")),o=e.yAxisID=L(i.yAxisID,Is(t,"y")),l=e.rAxisID=L(i.rAxisID,Is(t,"r")),a=e.indexAxis,c=e.iAxisID=s(a,r,o,l),h=e.vAxisID=s(a,o,r,l);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(l),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&xl(this._data,this),t._stacked&&gn(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(B(e)){const s=this._cachedMeta;this._data=vb(e,s)}else if(i!==e){if(i){xl(i,this);const s=this._cachedMeta;gn(s),s._parsed=[]}e&&Object.isExtensible(e)&&py(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const r=e._stacked;e._stacked=Ps(e.vScale,e),e.stack!==i.stack&&(s=!0,gn(e),e.stack=i.stack),this._resyncElements(t),(s||r!==e._stacked)&&(Bl(this,e._parsed),e._stacked=Ps(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:s}=this,{iScale:r,_stacked:o}=i,l=r.axis;let a=t===0&&e===s.length?!0:i._sorted,c=t>0&&i._parsed[t-1],h,d,u;if(this._parsing===!1)i._parsed=s,i._sorted=!0,u=s;else{U(s[t])?u=this.parseArrayData(i,s,t,e):B(s[t])?u=this.parseObjectData(i,s,t,e):u=this.parsePrimitiveData(i,s,t,e);const f=()=>d[l]===null||c&&d[l]<c[l];for(h=0;h<e;++h)i._parsed[h+t]=d=u[h],a&&(f()&&(a=!1),c=d);i._sorted=a}o&&Bl(this,u)}parsePrimitiveData(t,e,i,s){const{iScale:r,vScale:o}=t,l=r.axis,a=o.axis,c=r.getLabels(),h=r===o,d=new Array(s);let u,f,p;for(u=0,f=s;u<f;++u)p=u+i,d[u]={[l]:h||r.parse(c[p],p),[a]:o.parse(e[p],p)};return d}parseArrayData(t,e,i,s){const{xScale:r,yScale:o}=t,l=new Array(s);let a,c,h,d;for(a=0,c=s;a<c;++a)h=a+i,d=e[h],l[a]={x:r.parse(d[0],h),y:o.parse(d[1],h)};return l}parseObjectData(t,e,i,s){const{xScale:r,yScale:o}=t,{xAxisKey:l="x",yAxisKey:a="y"}=this._parsing,c=new Array(s);let h,d,u,f;for(h=0,d=s;h<d;++h)u=h+i,f=e[u],c[h]={x:r.parse(Vi(f,l),u),y:o.parse(Vi(f,a),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const s=this.chart,r=this._cachedMeta,o=e[t.axis],l={keys:Bh(s,!0),values:e._stacks[t.axis]._visualValues};return Rl(l,o,r.index,{mode:i})}updateRangeFromParsed(t,e,i,s){const r=i[e.axis];let o=r===null?NaN:r;const l=s&&i._stacks[e.axis];s&&l&&(s.values=l,o=Rl(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const i=this._cachedMeta,s=i._parsed,r=i._sorted&&t===i.iScale,o=s.length,l=this._getOtherScale(t),a=Ib(e,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Ab(l);let u,f;function p(){f=s[u];const m=f[l.axis];return!rt(f[t.axis])||h>m||d<m}for(u=0;u<o&&!(!p()&&(this.updateRangeFromParsed(c,t,f,a),r));++u);if(r){for(u=o-1;u>=0;--u)if(!p()){this.updateRangeFromParsed(c,t,f,a);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let s,r,o;for(s=0,r=e.length;s<r;++s)o=e[s][t.axis],rt(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Ob(L(this.options.clip,Tb(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],r=e.chartArea,o=[],l=this._drawStart||0,a=this._drawCount||s.length-l,c=this.options.drawActiveElementsOnTop;let h;for(i.dataset&&i.dataset.draw(t,r,l,a),h=l;h<l+a;++h){const d=s[h];d.hidden||(d.active&&c?o.push(d):d.draw(t,r))}for(h=0;h<o.length;++h)o[h].draw(t,r)}getStyle(t,e){const i=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const s=this.getDataset();let r;if(t>=0&&t<this._cachedMeta.data.length){const o=this._cachedMeta.data[t];r=o.$context||(o.$context=Pb(this.getContext(),t,o)),r.parsed=this.getParsed(t),r.raw=s.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=Nb(this.chart.getContext(),this.index)),r.dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const s=e==="active",r=this._cachedDataOpts,o=t+"-"+e,l=r[o],a=this.enableOptionSharing&&Hi(i);if(l)return zl(l,a);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=s?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(K.elements[t]),p=()=>this.getContext(i,s,e),m=c.resolveNamedOptions(u,f,p,d);return m.$shared&&(m.$shared=a,r[o]=Object.freeze(zl(m,a))),m}_resolveAnimations(t,e,i){const s=this.chart,r=this._cachedDataOpts,o=`animation-${e}`,l=r[o];if(l)return l;let a;if(s.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);a=h.createResolver(u,this.getContext(t,i,e))}const c=new Lh(s,a&&a.animations);return a&&a._cacheable&&(r[o]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Rs(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:o}}updateElement(t,e,i,s){Rs(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!Rs(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;const r=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[l,a,c]of this._syncList)this[l](a,c);this._syncList=[];const s=i.length,r=e.length,o=Math.min(r,s);o&&this.parse(0,o),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,i=!0){const s=this._cachedMeta,r=s.data,o=t+e;let l;const a=c=>{for(c.length+=e,l=c.length-1;l>=o;l--)c[l]=c[l-e]};for(a(r),l=t;l<o;++l)r[l]=new this.dataElementType;this._parsing&&a(s._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,e);i._stacked&&gn(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}P(In,"defaults",{}),P(In,"datasetElementType",null),P(In,"dataElementType",null);class Ls extends In{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=e,o=this.chart._animationsDisabled;let{start:l,count:a}=by(e,s,o);this._drawStart=l,this._drawCount=a,xy(e)&&(l=0,a=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:c},t),this.updateElements(s,l,a,t)}updateElements(t,e,i,s){const r=s==="reset",{iScale:o,vScale:l,_stacked:a,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,s),u=o.axis,f=l.axis,{spanGaps:p,segment:m}=this.options,g=qn(p)?p:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||r||s==="none",b=e+i,M=t.length;let w=e>0&&this.getParsed(e-1);for(let x=0;x<M;++x){const C=t[x],_=y?C:{};if(x<e||x>=b){_.skip=!0;continue}const k=this.getParsed(x),v=H(k[f]),A=_[u]=o.getPixelForValue(k[u],x),R=_[f]=r||v?l.getBasePixel():l.getPixelForValue(a?this.applyStack(l,k,a):k[f],x);_.skip=isNaN(A)||isNaN(R)||v,_.stop=x>0&&Math.abs(k[u]-w[u])>g,m&&(_.parsed=k,_.raw=c.data[x]),d&&(_.options=h||this.resolveDataElementOptions(x,C.active?"active":s)),y||this.updateElement(C,x,_,s),w=k}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;const r=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,r,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}P(Ls,"id","line"),P(Ls,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),P(Ls,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});function be(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Qr{constructor(t){P(this,"options");this.options=t||{}}static override(t){Object.assign(Qr.prototype,t)}init(){}formats(){return be()}parse(){return be()}format(){return be()}add(){return be()}diff(){return be()}startOf(){return be()}endOf(){return be()}}var Rb={_date:Qr};function Lb(n,t,e,i){const{controller:s,data:r,_sorted:o}=n,l=s._cachedMeta.iScale,a=n.dataset&&n.dataset.options?n.dataset.options.spanGaps:null;if(l&&t===l.axis&&t!=="r"&&o&&r.length){const c=l._reversePixels?uy:ve;if(i){if(s._sharedOptions){const h=r[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const u=c(r,t,e-d),f=c(r,t,e+d);return{lo:u.lo,hi:f.hi}}}}else{const h=c(r,t,e);if(a){const{vScale:d}=s._cachedMeta,{_parsed:u}=n,f=u.slice(0,h.lo+1).reverse().findIndex(m=>!H(m[d.axis]));h.lo-=Math.max(0,f);const p=u.slice(h.hi).findIndex(m=>!H(m[d.axis]));h.hi+=Math.max(0,p)}return h}}return{lo:0,hi:r.length-1}}function cs(n,t,e,i,s){const r=n.getSortedVisibleDatasetMetas(),o=e[t];for(let l=0,a=r.length;l<a;++l){const{index:c,data:h}=r[l],{lo:d,hi:u}=Lb(r[l],t,o,s);for(let f=d;f<=u;++f){const p=h[f];p.skip||i(p,c,f)}}}function Bb(n){const t=n.indexOf("x")!==-1,e=n.indexOf("y")!==-1;return function(i,s){const r=t?Math.abs(i.x-s.x):0,o=e?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function Bs(n,t,e,i,s){const r=[];return!s&&!n.isPointInArea(t)||cs(n,e,t,function(l,a,c){!s&&!Jn(l,n.chartArea,0)||l.inRange(t.x,t.y,i)&&r.push({element:l,datasetIndex:a,index:c})},!0),r}function zb(n,t,e,i){let s=[];function r(o,l,a){const{startAngle:c,endAngle:h}=o.getProps(["startAngle","endAngle"],i),{angle:d}=cy(o,{x:t.x,y:t.y});yh(d,c,h)&&s.push({element:o,datasetIndex:l,index:a})}return cs(n,e,t,r),s}function Fb(n,t,e,i,s,r){let o=[];const l=Bb(e);let a=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,s);if(i&&!f)return;const p=h.getCenterPoint(s);if(!(!!r||n.isPointInArea(p))&&!f)return;const g=l(t,p);g<a?(o=[{element:h,datasetIndex:d,index:u}],a=g):g===a&&o.push({element:h,datasetIndex:d,index:u})}return cs(n,e,t,c),o}function zs(n,t,e,i,s,r){return!r&&!n.isPointInArea(t)?[]:e==="r"&&!i?zb(n,t,e,s):Fb(n,t,e,i,s,r)}function Fl(n,t,e,i,s){const r=[],o=e==="x"?"inXRange":"inYRange";let l=!1;return cs(n,e,t,(a,c,h)=>{a[o]&&a[o](t[e],s)&&(r.push({element:a,datasetIndex:c,index:h}),l=l||a.inRange(t.x,t.y,s))}),i&&!l?[]:r}var Vb={modes:{index(n,t,e,i){const s=xe(t,n),r=e.axis||"x",o=e.includeInvisible||!1,l=e.intersect?Bs(n,s,r,i,o):zs(n,s,r,!1,i,o),a=[];return l.length?(n.getSortedVisibleDatasetMetas().forEach(c=>{const h=l[0].index,d=c.data[h];d&&!d.skip&&a.push({element:d,datasetIndex:c.index,index:h})}),a):[]},dataset(n,t,e,i){const s=xe(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;let l=e.intersect?Bs(n,s,r,i,o):zs(n,s,r,!1,i,o);if(l.length>0){const a=l[0].datasetIndex,c=n.getDatasetMeta(a).data;l=[];for(let h=0;h<c.length;++h)l.push({element:c[h],datasetIndex:a,index:h})}return l},point(n,t,e,i){const s=xe(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;return Bs(n,s,r,i,o)},nearest(n,t,e,i){const s=xe(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;return zs(n,s,r,e.intersect,i,o)},x(n,t,e,i){const s=xe(t,n);return Fl(n,s,"x",e.intersect,i)},y(n,t,e,i){const s=xe(t,n);return Fl(n,s,"y",e.intersect,i)}}};const zh=["left","top","right","bottom"];function yn(n,t){return n.filter(e=>e.pos===t)}function Vl(n,t){return n.filter(e=>zh.indexOf(e.pos)===-1&&e.box.axis===t)}function bn(n,t){return n.sort((e,i)=>{const s=t?i:e,r=t?e:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function Hb(n){const t=[];let e,i,s,r,o,l;for(e=0,i=(n||[]).length;e<i;++e)s=n[e],{position:r,options:{stack:o,stackWeight:l=1}}=s,t.push({index:e,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:o&&r+o,stackWeight:l});return t}function $b(n){const t={};for(const e of n){const{stack:i,pos:s,stackWeight:r}=e;if(!i||!zh.includes(s))continue;const o=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return t}function Wb(n,t){const e=$b(n),{vBoxMaxWidth:i,hBoxMaxHeight:s}=t;let r,o,l;for(r=0,o=n.length;r<o;++r){l=n[r];const{fullSize:a}=l.box,c=e[l.stack],h=c&&l.stackWeight/c.weight;l.horizontal?(l.width=h?h*i:a&&t.availableWidth,l.height=s):(l.width=i,l.height=h?h*s:a&&t.availableHeight)}return e}function jb(n){const t=Hb(n),e=bn(t.filter(c=>c.box.fullSize),!0),i=bn(yn(t,"left"),!0),s=bn(yn(t,"right")),r=bn(yn(t,"top"),!0),o=bn(yn(t,"bottom")),l=Vl(t,"x"),a=Vl(t,"y");return{fullSize:e,leftAndTop:i.concat(r),rightAndBottom:s.concat(a).concat(o).concat(l),chartArea:yn(t,"chartArea"),vertical:i.concat(s).concat(a),horizontal:r.concat(o).concat(l)}}function Hl(n,t,e,i){return Math.max(n[e],t[e])+Math.max(n[i],t[i])}function Fh(n,t){n.top=Math.max(n.top,t.top),n.left=Math.max(n.left,t.left),n.bottom=Math.max(n.bottom,t.bottom),n.right=Math.max(n.right,t.right)}function Kb(n,t,e,i){const{pos:s,box:r}=e,o=n.maxPadding;if(!B(s)){e.size&&(n[s]-=e.size);const d=i[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?r.height:r.width),e.size=d.size/d.count,n[s]+=e.size}r.getPadding&&Fh(o,r.getPadding());const l=Math.max(0,t.outerWidth-Hl(o,n,"left","right")),a=Math.max(0,t.outerHeight-Hl(o,n,"top","bottom")),c=l!==n.w,h=a!==n.h;return n.w=l,n.h=a,e.horizontal?{same:c,other:h}:{same:h,other:c}}function qb(n){const t=n.maxPadding;function e(i){const s=Math.max(t[i]-n[i],0);return n[i]+=s,s}n.y+=e("top"),n.x+=e("left"),e("right"),e("bottom")}function Jb(n,t){const e=t.maxPadding;function i(s){const r={left:0,top:0,right:0,bottom:0};return s.forEach(o=>{r[o]=Math.max(t[o],e[o])}),r}return i(n?["left","right"]:["top","bottom"])}function Cn(n,t,e,i){const s=[];let r,o,l,a,c,h;for(r=0,o=n.length,c=0;r<o;++r){l=n[r],a=l.box,a.update(l.width||t.w,l.height||t.h,Jb(l.horizontal,t));const{same:d,other:u}=Kb(t,e,l,i);c|=d&&s.length,h=h||u,a.fullSize||s.push(l)}return c&&Cn(s,t,e,i)||h}function pi(n,t,e,i,s){n.top=e,n.left=t,n.right=t+i,n.bottom=e+s,n.width=i,n.height=s}function $l(n,t,e,i){const s=e.padding;let{x:r,y:o}=t;for(const l of n){const a=l.box,c=i[l.stack]||{placed:0,weight:1},h=l.stackWeight/c.weight||1;if(l.horizontal){const d=t.w*h,u=c.size||a.height;Hi(c.start)&&(o=c.start),a.fullSize?pi(a,s.left,o,e.outerWidth-s.right-s.left,u):pi(a,t.left+c.placed,o,d,u),c.start=o,c.placed+=d,o=a.bottom}else{const d=t.h*h,u=c.size||a.width;Hi(c.start)&&(r=c.start),a.fullSize?pi(a,r,s.top,u,e.outerHeight-s.bottom-s.top):pi(a,r,t.top+c.placed,u,d),c.start=r,c.placed+=d,r=a.right}}t.x=r,t.y=o}var _t={addBox(n,t){n.boxes||(n.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},n.boxes.push(t)},removeBox(n,t){const e=n.boxes?n.boxes.indexOf(t):-1;e!==-1&&n.boxes.splice(e,1)},configure(n,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(n,t,e,i){if(!n)return;const s=Ot(n.options.layout.padding),r=Math.max(t-s.width,0),o=Math.max(e-s.height,0),l=jb(n.boxes),a=l.vertical,c=l.horizontal;F(n.boxes,m=>{typeof m.beforeLayout=="function"&&m.beforeLayout()});const h=a.reduce((m,g)=>g.box.options&&g.box.options.display===!1?m:m+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:s,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/h,hBoxMaxHeight:o/2}),u=Object.assign({},s);Fh(u,Ot(i));const f=Object.assign({maxPadding:u,w:r,h:o,x:s.left,y:s.top},s),p=Wb(a.concat(c),d);Cn(l.fullSize,f,d,p),Cn(a,f,d,p),Cn(c,f,d,p)&&Cn(a,f,d,p),qb(f),$l(l.leftAndTop,f,d,p),f.x+=f.w,f.y+=f.h,$l(l.rightAndBottom,f,d,p),n.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},F(l.chartArea,m=>{const g=m.box;Object.assign(g,n.chartArea),g.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Vh{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class Ub extends Vh{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const _i="$chartjs",Gb={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Wl=n=>n===null||n==="";function Yb(n,t){const e=n.style,i=n.getAttribute("height"),s=n.getAttribute("width");if(n[_i]={initial:{height:i,width:s,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Wl(s)){const r=Dl(n,"width");r!==void 0&&(n.width=r)}if(Wl(i))if(n.style.height==="")n.height=n.width/(t||2);else{const r=Dl(n,"height");r!==void 0&&(n.height=r)}return n}const Hh=cb?{passive:!0}:!1;function Xb(n,t,e){n&&n.addEventListener(t,e,Hh)}function Qb(n,t,e){n&&n.canvas&&n.canvas.removeEventListener(t,e,Hh)}function Zb(n,t){const e=Gb[n.type]||n.type,{x:i,y:s}=xe(n,t);return{type:e,chart:t,native:n,x:i!==void 0?i:null,y:s!==void 0?s:null}}function ji(n,t){for(const e of n)if(e===t||e.contains(t))return!0}function tx(n,t,e){const i=n.canvas,s=new MutationObserver(r=>{let o=!1;for(const l of r)o=o||ji(l.addedNodes,i),o=o&&!ji(l.removedNodes,i);o&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}function ex(n,t,e){const i=n.canvas,s=new MutationObserver(r=>{let o=!1;for(const l of r)o=o||ji(l.removedNodes,i),o=o&&!ji(l.addedNodes,i);o&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}const Gn=new Map;let jl=0;function $h(){const n=window.devicePixelRatio;n!==jl&&(jl=n,Gn.forEach((t,e)=>{e.currentDevicePixelRatio!==n&&t()}))}function nx(n,t){Gn.size||window.addEventListener("resize",$h),Gn.set(n,t)}function ix(n){Gn.delete(n),Gn.size||window.removeEventListener("resize",$h)}function sx(n,t,e){const i=n.canvas,s=i&&Xr(i);if(!s)return;const r=kh((l,a)=>{const c=s.clientWidth;e(l,a),c<s.clientWidth&&e()},window),o=new ResizeObserver(l=>{const a=l[0],c=a.contentRect.width,h=a.contentRect.height;c===0&&h===0||r(c,h)});return o.observe(s),nx(n,r),o}function Fs(n,t,e){e&&e.disconnect(),t==="resize"&&ix(n)}function rx(n,t,e){const i=n.canvas,s=kh(r=>{n.ctx!==null&&e(Zb(r,n))},n);return Xb(i,t,s),s}class ox extends Vh{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(Yb(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[_i])return!1;const i=e[_i].initial;["height","width"].forEach(r=>{const o=i[r];H(o)?e.removeAttribute(r):e.setAttribute(r,o)});const s=i.style||{};return Object.keys(s).forEach(r=>{e.style[r]=s[r]}),e.width=e.width,delete e[_i],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const s=t.$proxies||(t.$proxies={}),o={attach:tx,detach:ex,resize:sx}[e]||rx;s[e]=o(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:Fs,detach:Fs,resize:Fs}[e]||Qb)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return ab(t,e,i,s)}isAttached(t){const e=t&&Xr(t);return!!(e&&e.isConnected)}}function lx(n){return!Yr()||typeof OffscreenCanvas<"u"&&n instanceof OffscreenCanvas?Ub:ox}class Yt{constructor(){P(this,"x");P(this,"y");P(this,"active",!1);P(this,"options");P(this,"$animations")}tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return qn(this.x)&&qn(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const s={};return t.forEach(r=>{s[r]=i[r]&&i[r].active()?i[r]._to:this[r]}),s}}P(Yt,"defaults",{}),P(Yt,"defaultRoutes");function ax(n,t){const e=n.options.ticks,i=cx(n),s=Math.min(e.maxTicksLimit||i,i),r=e.major.enabled?dx(t):[],o=r.length,l=r[0],a=r[o-1],c=[];if(o>s)return ux(t,c,r,o/s),c;const h=hx(r,t,s);if(o>0){let d,u;const f=o>1?Math.round((a-l)/(o-1)):null;for(mi(t,c,h,H(f)?0:l-f,l),d=0,u=o-1;d<u;d++)mi(t,c,h,r[d],r[d+1]);return mi(t,c,h,a,H(f)?t.length:a+f),c}return mi(t,c,h),c}function cx(n){const t=n.options.offset,e=n._tickSize(),i=n._length/e+(t?0:1),s=n._maxLength/e;return Math.floor(Math.min(i,s))}function hx(n,t,e){const i=fx(n),s=t.length/e;if(!i)return Math.max(s,1);const r=sy(i);for(let o=0,l=r.length-1;o<l;o++){const a=r[o];if(a>s)return a}return Math.max(s,1)}function dx(n){const t=[];let e,i;for(e=0,i=n.length;e<i;e++)n[e].major&&t.push(e);return t}function ux(n,t,e,i){let s=0,r=e[0],o;for(i=Math.ceil(i),o=0;o<n.length;o++)o===r&&(t.push(n[o]),s++,r=e[s*i])}function mi(n,t,e,i,s){const r=L(i,0),o=Math.min(L(s,n.length),n.length);let l=0,a,c,h;for(e=Math.ceil(e),s&&(a=s-i,e=a/Math.floor(a/e)),h=r;h<0;)l++,h=Math.round(r+l*e);for(c=Math.max(r,0);c<o;c++)c===h&&(t.push(n[c]),l++,h=Math.round(r+l*e))}function fx(n){const t=n.length;let e,i;if(t<2)return!1;for(i=n[0],e=1;e<t;++e)if(n[e]-n[e-1]!==i)return!1;return i}const px=n=>n==="left"?"right":n==="right"?"left":n,Kl=(n,t,e)=>t==="top"||t==="left"?n[t]+e:n[t]-e,ql=(n,t)=>Math.min(t||n,n);function Jl(n,t){const e=[],i=n.length/t,s=n.length;let r=0;for(;r<s;r+=i)e.push(n[Math.floor(r)]);return e}function mx(n,t,e){const i=n.ticks.length,s=Math.min(t,i-1),r=n._startPixel,o=n._endPixel,l=1e-6;let a=n.getPixelForTick(s),c;if(!(e&&(i===1?c=Math.max(a-r,o-a):t===0?c=(n.getPixelForTick(1)-a)/2:c=(a-n.getPixelForTick(s-1))/2,a+=s<t?c:-c,a<r-l||a>o+l)))return a}function gx(n,t){F(n,e=>{const i=e.gc,s=i.length/2;let r;if(s>t){for(r=0;r<s;++r)delete e.data[i[r]];i.splice(0,s)}})}function xn(n){return n.drawTicks?n.tickLength:0}function Ul(n,t){if(!n.display)return 0;const e=st(n.font,t),i=Ot(n.padding);return(U(n.text)?n.text.length:1)*e.lineHeight+i.height}function yx(n,t){return $e(n,{scale:t,type:"scale"})}function bx(n,t,e){return $e(n,{tick:e,index:t,type:"tick"})}function xx(n,t,e){let i=Kr(n);return(e&&t!=="right"||!e&&t==="right")&&(i=px(i)),i}function kx(n,t,e,i){const{top:s,left:r,bottom:o,right:l,chart:a}=n,{chartArea:c,scales:h}=a;let d=0,u,f,p;const m=o-s,g=l-r;if(n.isHorizontal()){if(f=et(i,r,l),B(e)){const y=Object.keys(e)[0],b=e[y];p=h[y].getPixelForValue(b)+m-t}else e==="center"?p=(c.bottom+c.top)/2+m-t:p=Kl(n,e,t);u=l-r}else{if(B(e)){const y=Object.keys(e)[0],b=e[y];f=h[y].getPixelForValue(b)-g+t}else e==="center"?f=(c.left+c.right)/2-g+t:f=Kl(n,e,t);p=et(i,o,s),d=e==="left"?-Dt:Dt}return{titleX:f,titleY:p,maxWidth:u,rotation:d}}class un extends Yt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=Pt(t,Number.POSITIVE_INFINITY),e=Pt(e,Number.NEGATIVE_INFINITY),i=Pt(i,Number.POSITIVE_INFINITY),s=Pt(s,Number.NEGATIVE_INFINITY),{min:Pt(t,i),max:Pt(e,s),minDefined:rt(t),maxDefined:rt(e)}}getMinMax(t){let{min:e,max:i,minDefined:s,maxDefined:r}=this.getUserBounds(),o;if(s&&r)return{min:e,max:i};const l=this.getMatchingVisibleMetas();for(let a=0,c=l.length;a<c;++a)o=l[a].controller.getMinMax(this,t),s||(e=Math.min(e,o.min)),r||(i=Math.max(i,o.max));return e=r&&e>i?i:e,i=s&&e>i?e:i,{min:Pt(e,Pt(i,e)),max:Pt(i,Pt(e,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){$(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:s,grace:r,ticks:o}=this.options,l=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Vy(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const a=l<this.ticks.length;this._convertTicksToLabels(a?Jl(this.ticks,l):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=ax(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),a&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,i;this.isHorizontal()?(e=this.left,i=this.right):(e=this.top,i=this.bottom,t=!t),this._startPixel=e,this._endPixel=i,this._reversePixels=t,this._length=i-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){$(this.options.afterUpdate,[this])}beforeSetDimensions(){$(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){$(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),$(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){$(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,s,r;for(i=0,s=t.length;i<s;i++)r=t[i],r.label=$(e.callback,[r.value,i,t],this)}afterTickToLabelConversion(){$(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){$(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=ql(this.ticks.length,t.ticks.maxTicksLimit),s=e.minRotation||0,r=e.maxRotation;let o=s,l,a,c;if(!this._isVisible()||!e.display||s>=r||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=Ct(this.chart.width-d,0,this.maxWidth);l=t.offset?this.maxWidth/i:f/(i-1),d+6>l&&(l=f/(i-(t.offset?.5:1)),a=this.maxHeight-xn(t.grid)-e.padding-Ul(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),o=ay(Math.min(Math.asin(Ct((h.highest.height+6)/l,-1,1)),Math.asin(Ct(a/c,-1,1))-Math.asin(Ct(u/c,-1,1)))),o=Math.max(s,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){$(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){$(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:r}}=this,o=this._isVisible(),l=this.isHorizontal();if(o){const a=Ul(s,e.options.font);if(l?(t.width=this.maxWidth,t.height=xn(r)+a):(t.height=this.maxHeight,t.width=xn(r)+a),i.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=i.padding*2,p=Oe(this.labelRotation),m=Math.cos(p),g=Math.sin(p);if(l){const y=i.mirror?0:g*d.width+m*u.height;t.height=Math.min(this.maxHeight,t.height+y+f)}else{const y=i.mirror?0:m*d.width+g*u.height;t.width=Math.min(this.maxWidth,t.width+y+f)}this._calculatePadding(c,h,g,m)}}this._handleMargins(),l?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){const{ticks:{align:r,padding:o},position:l}=this.options,a=this.labelRotation!==0,c=l!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;a?c?(u=s*t.width,f=i*e.height):(u=i*t.height,f=s*e.width):r==="start"?f=e.width:r==="end"?u=t.width:r!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+o)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+o)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;r==="start"?(h=0,d=t.height):r==="end"&&(h=e.height,d=0),this.paddingTop=h+o,this.paddingBottom=d+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){$(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,i;for(e=0,i=t.length;e<i;e++)H(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=Jl(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:s,_longestTextCache:r}=this,o=[],l=[],a=Math.floor(e/ql(e,i));let c=0,h=0,d,u,f,p,m,g,y,b,M,w,x;for(d=0;d<e;d+=a){if(p=t[d].label,m=this._resolveTickFontOptions(d),s.font=g=m.string,y=r[g]=r[g]||{data:{},gc:[]},b=m.lineHeight,M=w=0,!H(p)&&!U(p))M=Cl(s,y.data,y.gc,M,p),w=b;else if(U(p))for(u=0,f=p.length;u<f;++u)x=p[u],!H(x)&&!U(x)&&(M=Cl(s,y.data,y.gc,M,x),w+=b);o.push(M),l.push(w),c=Math.max(M,c),h=Math.max(w,h)}gx(r,e);const C=o.indexOf(c),_=l.indexOf(h),k=v=>({width:o[v]||0,height:l[v]||0});return{first:k(0),last:k(e-1),widest:k(C),highest:k(_),widths:o,heights:l}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return dy(this._alignToPixels?ye(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=bx(this.getContext(),t,i))}return this.$context||(this.$context=yx(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=Oe(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),r=this._getLabelSizes(),o=t.autoSkipPadding||0,l=r?r.widest.width+o:0,a=r?r.highest.height+o:0;return this.isHorizontal()?a*i>l*s?l/i:a/s:a*s<l*i?a/i:l/s}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,s=this.options,{grid:r,position:o,border:l}=s,a=r.offset,c=this.isHorizontal(),d=this.ticks.length+(a?1:0),u=xn(r),f=[],p=l.setContext(this.getContext()),m=p.display?p.width:0,g=m/2,y=function(Z){return ye(i,Z,m)};let b,M,w,x,C,_,k,v,A,R,z,ut;if(o==="top")b=y(this.bottom),_=this.bottom-u,v=b-g,R=y(t.top)+g,ut=t.bottom;else if(o==="bottom")b=y(this.top),R=t.top,ut=y(t.bottom)-g,_=b+g,v=this.top+u;else if(o==="left")b=y(this.right),C=this.right-u,k=b-g,A=y(t.left)+g,z=t.right;else if(o==="right")b=y(this.left),A=t.left,z=y(t.right)-g,C=b+g,k=this.left+u;else if(e==="x"){if(o==="center")b=y((t.top+t.bottom)/2+.5);else if(B(o)){const Z=Object.keys(o)[0],kt=o[Z];b=y(this.chart.scales[Z].getPixelForValue(kt))}R=t.top,ut=t.bottom,_=b+g,v=_+u}else if(e==="y"){if(o==="center")b=y((t.left+t.right)/2);else if(B(o)){const Z=Object.keys(o)[0],kt=o[Z];b=y(this.chart.scales[Z].getPixelForValue(kt))}C=b-g,k=C-u,A=t.left,z=t.right}const Nt=L(s.ticks.maxTicksLimit,d),j=Math.max(1,Math.ceil(d/Nt));for(M=0;M<d;M+=j){const Z=this.getContext(M),kt=r.setContext(Z),Zn=l.setContext(Z),ti=kt.lineWidth,We=kt.color,ei=Zn.dash||[],je=Zn.dashOffset,fn=kt.tickWidth,pe=kt.tickColor,pn=kt.tickBorderDash||[],me=kt.tickBorderDashOffset;w=mx(this,M,a),w!==void 0&&(x=ye(i,w,ti),c?C=k=A=z=x:_=v=R=ut=x,f.push({tx1:C,ty1:_,tx2:k,ty2:v,x1:A,y1:R,x2:z,y2:ut,width:ti,color:We,borderDash:ei,borderDashOffset:je,tickWidth:fn,tickColor:pe,tickBorderDash:pn,tickBorderDashOffset:me}))}return this._ticksLength=d,this._borderValue=b,f}_computeLabelItems(t){const e=this.axis,i=this.options,{position:s,ticks:r}=i,o=this.isHorizontal(),l=this.ticks,{align:a,crossAlign:c,padding:h,mirror:d}=r,u=xn(i.grid),f=u+h,p=d?-h:f,m=-Oe(this.labelRotation),g=[];let y,b,M,w,x,C,_,k,v,A,R,z,ut="middle";if(s==="top")C=this.bottom-p,_=this._getXAxisLabelAlignment();else if(s==="bottom")C=this.top+p,_=this._getXAxisLabelAlignment();else if(s==="left"){const j=this._getYAxisLabelAlignment(u);_=j.textAlign,x=j.x}else if(s==="right"){const j=this._getYAxisLabelAlignment(u);_=j.textAlign,x=j.x}else if(e==="x"){if(s==="center")C=(t.top+t.bottom)/2+f;else if(B(s)){const j=Object.keys(s)[0],Z=s[j];C=this.chart.scales[j].getPixelForValue(Z)+f}_=this._getXAxisLabelAlignment()}else if(e==="y"){if(s==="center")x=(t.left+t.right)/2-f;else if(B(s)){const j=Object.keys(s)[0],Z=s[j];x=this.chart.scales[j].getPixelForValue(Z)}_=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(a==="start"?ut="top":a==="end"&&(ut="bottom"));const Nt=this._getLabelSizes();for(y=0,b=l.length;y<b;++y){M=l[y],w=M.label;const j=r.setContext(this.getContext(y));k=this.getPixelForTick(y)+r.labelOffset,v=this._resolveTickFontOptions(y),A=v.lineHeight,R=U(w)?w.length:1;const Z=R/2,kt=j.color,Zn=j.textStrokeColor,ti=j.textStrokeWidth;let We=_;o?(x=k,_==="inner"&&(y===b-1?We=this.options.reverse?"left":"right":y===0?We=this.options.reverse?"right":"left":We="center"),s==="top"?c==="near"||m!==0?z=-R*A+A/2:c==="center"?z=-Nt.highest.height/2-Z*A+A:z=-Nt.highest.height+A/2:c==="near"||m!==0?z=A/2:c==="center"?z=Nt.highest.height/2-Z*A:z=Nt.highest.height-R*A,d&&(z*=-1),m!==0&&!j.showLabelBackdrop&&(x+=A/2*Math.sin(m))):(C=k,z=(1-R)*A/2);let ei;if(j.showLabelBackdrop){const je=Ot(j.backdropPadding),fn=Nt.heights[y],pe=Nt.widths[y];let pn=z-je.top,me=0-je.left;switch(ut){case"middle":pn-=fn/2;break;case"bottom":pn-=fn;break}switch(_){case"center":me-=pe/2;break;case"right":me-=pe;break;case"inner":y===b-1?me-=pe:y>0&&(me-=pe/2);break}ei={left:me,top:pn,width:pe+je.width,height:fn+je.height,color:j.backdropColor}}g.push({label:w,font:v,textOffset:z,options:{rotation:m,color:kt,strokeColor:Zn,strokeWidth:ti,textAlign:We,textBaseline:ut,translation:[x,C],backdrop:ei}})}return g}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-Oe(this.labelRotation))return t==="top"?"left":"right";let s="center";return e.align==="start"?s="left":e.align==="end"?s="right":e.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:s,padding:r}}=this.options,o=this._getLabelSizes(),l=t+r,a=o.widest.width;let c,h;return e==="left"?s?(h=this.right+r,i==="near"?c="left":i==="center"?(c="center",h+=a/2):(c="right",h+=a)):(h=this.right-l,i==="near"?c="right":i==="center"?(c="center",h-=a/2):(c="left",h=this.left)):e==="right"?s?(h=this.left+r,i==="near"?c="right":i==="center"?(c="center",h-=a/2):(c="left",h-=a)):(h=this.left+l,i==="near"?c="left":i==="center"?(c="center",h+=a/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:s,width:r,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,r,o),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const s=this.ticks.findIndex(r=>r.value===t);return s>=0?e.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){const e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let r,o;const l=(a,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(a.x,a.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(e.display)for(r=0,o=s.length;r<o;++r){const a=s[r];e.drawOnChartArea&&l({x:a.x1,y:a.y1},{x:a.x2,y:a.y2},a),e.drawTicks&&l({x:a.tx1,y:a.ty1},{x:a.tx2,y:a.ty2},{color:a.tickColor,width:a.tickWidth,borderDash:a.tickBorderDash,borderDashOffset:a.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:s}}=this,r=i.setContext(this.getContext()),o=i.display?r.width:0;if(!o)return;const l=s.setContext(this.getContext(0)).lineWidth,a=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=ye(t,this.left,o)-o/2,h=ye(t,this.right,l)+l/2,d=u=a):(d=ye(t,this.top,o)-o/2,u=ye(t,this.bottom,l)+l/2,c=h=a),e.save(),e.lineWidth=r.width,e.strokeStyle=r.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&rs(i,s);const r=this.getLabelItems(t);for(const o of r){const l=o.options,a=o.font,c=o.label,h=o.textOffset;Un(i,c,0,h,a,l)}s&&ls(i)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;const r=st(i.font),o=Ot(i.padding),l=i.align;let a=r.lineHeight/2;e==="bottom"||e==="center"||B(e)?(a+=o.bottom,U(i.text)&&(a+=r.lineHeight*(i.text.length-1))):a+=o.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=kx(this,a,e,l);Un(t,i.text,0,0,r,{color:i.color,maxWidth:d,rotation:u,textAlign:xx(l,e,s),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=L(t.grid&&t.grid.z,-1),s=L(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==un.prototype.draw?[{z:e,draw:r=>{this.draw(r)}}]:[{z:i,draw:r=>{this.drawBackground(),this.drawGrid(r),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:r=>{this.drawLabels(r)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let r,o;for(r=0,o=e.length;r<o;++r){const l=e[r];l[i]===this.id&&(!t||l.type===t)&&s.push(l)}return s}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return st(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class gi{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;wx(e)&&(i=this.register(e));const s=this.items,r=t.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+t);return r in s||(s[r]=t,Sx(t,o,i),this.override&&K.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in K[s]&&(delete K[s][i],this.override&&delete Ve[i])}}function Sx(n,t,e){const i=Kn(Object.create(null),[e?K.get(e):{},K.get(t),n.defaults]);K.set(t,i),n.defaultRoutes&&Mx(t,n.defaultRoutes),n.descriptors&&K.describe(t,n.descriptors)}function Mx(n,t){Object.keys(t).forEach(e=>{const i=e.split("."),s=i.pop(),r=[n].concat(i).join("."),o=t[e].split("."),l=o.pop(),a=o.join(".");K.route(r,s,a,l)})}function wx(n){return"id"in n&&"defaults"in n}class Cx{constructor(){this.controllers=new gi(In,"datasets",!0),this.elements=new gi(Yt,"elements"),this.plugins=new gi(Object,"plugins"),this.scales=new gi(un,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(s=>{const r=i||this._getRegistryForType(s);i||r.isForType(s)||r===this.plugins&&s.id?this._exec(t,r,s):F(s,o=>{const l=i||this._getRegistryForType(o);this._exec(t,l,o)})})}_exec(t,e,i){const s=Wr(t);$(i["before"+s],[],i),e[t](i),$(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const s=e.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var Lt=new Cx;class _x{constructor(){this._init=[]}notify(t,e,i,s){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const r=s?this._descriptors(t).filter(s):this._descriptors(t),o=this._notify(r,t,e,i);return e==="afterDestroy"&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,s){s=s||{};for(const r of t){const o=r.plugin,l=o[i],a=[e,s,r.options];if($(l,a,o)===!1&&s.cancelable)return!1}return!0}invalidate(){H(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,s=L(i.options&&i.options.plugins,{}),r=Tx(i);return s===!1&&!e?[]:vx(t,r,s,e)}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,s=(r,o)=>r.filter(l=>!o.some(a=>l.plugin.id===a.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function Tx(n){const t={},e=[],i=Object.keys(Lt.plugins.items);for(let r=0;r<i.length;r++)e.push(Lt.getPlugin(i[r]));const s=n.plugins||[];for(let r=0;r<s.length;r++){const o=s[r];e.indexOf(o)===-1&&(e.push(o),t[o.id]=!0)}return{plugins:e,localIds:t}}function Ox(n,t){return!t&&n===!1?null:n===!0?{}:n}function vx(n,{plugins:t,localIds:e},i,s){const r=[],o=n.getContext();for(const l of t){const a=l.id,c=Ox(i[a],s);c!==null&&r.push({plugin:l,options:Dx(n.config,{plugin:l,local:e[a]},c,o)})}return r}function Dx(n,{plugin:t,local:e},i,s){const r=n.pluginScopeKeys(t),o=n.getOptionScopes(i,r);return e&&t.defaults&&o.push(t.defaults),n.createResolver(o,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function fr(n,t){const e=K.datasets[n]||{};return((t.datasets||{})[n]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Ax(n,t){let e=n;return n==="_index_"?e=t:n==="_value_"&&(e=t==="x"?"y":"x"),e}function Ex(n,t){return n===t?"_index_":"_value_"}function Gl(n){if(n==="x"||n==="y"||n==="r")return n}function Nx(n){if(n==="top"||n==="bottom")return"x";if(n==="left"||n==="right")return"y"}function pr(n,...t){if(Gl(n))return n;for(const e of t){const i=e.axis||Nx(e.position)||n.length>1&&Gl(n[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${n}' axis. Please provide 'axis' or 'position' option.`)}function Yl(n,t,e){if(e[t+"AxisID"]===n)return{axis:t}}function Px(n,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(i=>i.xAxisID===n||i.yAxisID===n);if(e.length)return Yl(n,"x",e[0])||Yl(n,"y",e[0])}return{}}function Ix(n,t){const e=Ve[n.type]||{scales:{}},i=t.scales||{},s=fr(n.type,t),r=Object.create(null);return Object.keys(i).forEach(o=>{const l=i[o];if(!B(l))return console.error(`Invalid scale configuration for scale: ${o}`);if(l._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${o}`);const a=pr(o,l,Px(o,n),K.scales[l.type]),c=Ex(a,s),h=e.scales||{};r[o]=Dn(Object.create(null),[{axis:a},l,h[a],h[c]])}),n.data.datasets.forEach(o=>{const l=o.type||n.type,a=o.indexAxis||fr(l,t),h=(Ve[l]||{}).scales||{};Object.keys(h).forEach(d=>{const u=Ax(d,a),f=o[u+"AxisID"]||u;r[f]=r[f]||Object.create(null),Dn(r[f],[{axis:u},i[f],h[d]])})}),Object.keys(r).forEach(o=>{const l=r[o];Dn(l,[K.scales[l.type],K.scale])}),r}function Wh(n){const t=n.options||(n.options={});t.plugins=L(t.plugins,{}),t.scales=Ix(n,t)}function jh(n){return n=n||{},n.datasets=n.datasets||[],n.labels=n.labels||[],n}function Rx(n){return n=n||{},n.data=jh(n.data),Wh(n),n}const Xl=new Map,Kh=new Set;function yi(n,t){let e=Xl.get(n);return e||(e=t(),Xl.set(n,e),Kh.add(e)),e}const kn=(n,t,e)=>{const i=Vi(t,e);i!==void 0&&n.add(i)};class Lx{constructor(t){this._config=Rx(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=jh(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Wh(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return yi(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return yi(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return yi(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,i=this.type;return yi(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const i=this._scopeCache;let s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){const{options:s,type:r}=this,o=this._cachedScopes(t,i),l=o.get(e);if(l)return l;const a=new Set;e.forEach(h=>{t&&(a.add(t),h.forEach(d=>kn(a,t,d))),h.forEach(d=>kn(a,s,d)),h.forEach(d=>kn(a,Ve[r]||{},d)),h.forEach(d=>kn(a,K,d)),h.forEach(d=>kn(a,hr,d))});const c=Array.from(a);return c.length===0&&c.push(Object.create(null)),Kh.has(e)&&o.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,Ve[e]||{},K.datasets[e]||{},{type:e},K,hr]}resolveNamedOptions(t,e,i,s=[""]){const r={$shared:!0},{resolver:o,subPrefixes:l}=Ql(this._resolverCache,t,s);let a=o;if(zx(o,e)){r.$shared=!1,i=he(i)?i():i;const c=this.createResolver(t,i,l);a=cn(o,i,c)}for(const c of e)r[c]=a[c];return r}createResolver(t,e,i=[""],s){const{resolver:r}=Ql(this._resolverCache,t,i);return B(e)?cn(r,e,void 0,s):r}}function Ql(n,t,e){let i=n.get(t);i||(i=new Map,n.set(t,i));const s=e.join();let r=i.get(s);return r||(r={resolver:Jr(t,e),subPrefixes:e.filter(l=>!l.toLowerCase().includes("hover"))},i.set(s,r)),r}const Bx=n=>B(n)&&Object.getOwnPropertyNames(n).some(t=>he(n[t]));function zx(n,t){const{isScriptable:e,isIndexable:i}=_h(n);for(const s of t){const r=e(s),o=i(s),l=(o||r)&&n[s];if(r&&(he(l)||Bx(l))||o&&U(l))return!0}return!1}var Fx="4.5.0";const Vx=["top","bottom","left","right","chartArea"];function Zl(n,t){return n==="top"||n==="bottom"||Vx.indexOf(n)===-1&&t==="x"}function ta(n,t){return function(e,i){return e[n]===i[n]?e[t]-i[t]:e[n]-i[n]}}function ea(n){const t=n.chart,e=t.options.animation;t.notifyPlugins("afterRender"),$(e&&e.onComplete,[n],t)}function Hx(n){const t=n.chart,e=t.options.animation;$(e&&e.onProgress,[n],t)}function qh(n){return Yr()&&typeof n=="string"?n=document.getElementById(n):n&&n.length&&(n=n[0]),n&&n.canvas&&(n=n.canvas),n}const Ti={},na=n=>{const t=qh(n);return Object.values(Ti).filter(e=>e.canvas===t).pop()};function $x(n,t,e){const i=Object.keys(n);for(const s of i){const r=+s;if(r>=t){const o=n[s];delete n[s],(e>0||r>t)&&(n[r+e]=o)}}}function Wx(n,t,e,i){return!e||n.type==="mouseout"?null:i?t:n}class Se{static register(...t){Lt.add(...t),ia()}static unregister(...t){Lt.remove(...t),ia()}constructor(t,e){const i=this.config=new Lx(e),s=qh(t),r=na(s);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||lx(s)),this.platform.updateConfig(i);const l=this.platform.acquireContext(s,o.aspectRatio),a=l&&l.canvas,c=a&&a.height,h=a&&a.width;if(this.id=Gg(),this.ctx=l,this.canvas=a,this.width=h,this.height=c,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new _x,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=gy(d=>this.update(d),o.resizeDelay||0),this._dataChanges=[],Ti[this.id]=this,!l||!a){console.error("Failed to create chart: can't acquire context from the given item");return}jt.listen(this,"complete",ea),jt.listen(this,"progress",Hx),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:r}=this;return H(t)?e&&r?r:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Lt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():vl(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return _l(this.canvas,this.ctx),this}stop(){return jt.stop(this),this}resize(t,e){jt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,t,e,r),l=i.devicePixelRatio||this.platform.getDevicePixelRatio(),a=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,vl(this,l,!0)&&(this.notifyPlugins("resize",{size:o}),$(i.onResize,[this,o],this),this.attached&&this._doResize(a)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};F(e,(i,s)=>{i.id=s})}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((o,l)=>(o[l]=!1,o),{});let r=[];e&&(r=r.concat(Object.keys(e).map(o=>{const l=e[o],a=pr(o,l),c=a==="r",h=a==="x";return{options:l,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),F(r,o=>{const l=o.options,a=l.id,c=pr(a,l),h=L(l.type,o.dtype);(l.position===void 0||Zl(l.position,c)!==Zl(o.dposition))&&(l.position=o.dposition),s[a]=!0;let d=null;if(a in i&&i[a].type===h)d=i[a];else{const u=Lt.getScale(h);d=new u({id:a,type:h,ctx:this.ctx,chart:this}),i[d.id]=d}d.init(l,t)}),F(s,(o,l)=>{o||delete i[l]}),F(i,o=>{_t.configure(this,o,o.options),_t.addBox(this,o)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((s,r)=>s.index-r.index),i>e){for(let s=e;s<i;++s)this._destroyDatasetMeta(s);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(ta("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((i,s)=>{e.filter(r=>r===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){const r=e[i];let o=this.getDatasetMeta(i);const l=r.type||this.config.type;if(o.type&&o.type!==l&&(this._destroyDatasetMeta(i),o=this.getDatasetMeta(i)),o.type=l,o.indexAxis=r.indexAxis||fr(l,this.options),o.order=r.order||0,o.index=i,o.label=""+r.label,o.visible=this.isDatasetVisible(i),o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{const a=Lt.getController(l),{datasetElementType:c,dataElementType:h}=K.datasets[l];Object.assign(a,{dataElementType:Lt.getElement(h),datasetElementType:c&&Lt.getElement(c)}),o.controller=new a(this,i),t.push(o.controller)}}return this._updateMetasets(),t}_resetElements(){F(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!s&&r.indexOf(d)===-1;d.buildOrUpdateElements(u),o=Math.max(+d.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||F(r,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(ta("z","_idx"));const{_active:l,_lastEvent:a}=this;a?this._eventHandler(a,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){F(this.scales,t=>{_t.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!ml(e,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:r}of e){const o=i==="_removeElements"?-r:r;$x(t,s,o)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=r=>new Set(t.filter(o=>o[0]===r).map((o,l)=>l+","+o.splice(1).join(","))),s=i(0);for(let r=1;r<e;r++)if(!ml(s,i(r)))return;return Array.from(s).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;_t.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],F(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,r)=>{s._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,i=this.data.datasets.length;e<i;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,he(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(jt.has(this)?this.attached&&!jt.running(this)&&jt.start(this):(this.draw(),ea({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let s,r;for(s=0,r=e.length;s<r;++s){const o=e[s];(!t||o.visible)&&i.push(o)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=Rh(this,t);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(s&&rs(e,s),t.controller.draw(),s&&ls(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return Jn(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){const r=Vb.modes[e];return typeof r=="function"?r(this,t,i,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let s=i.filter(r=>r&&r._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=$e(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!e.hidden}setDatasetVisibility(t,e){const i=this.getDatasetMeta(t);i.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const s=i?"show":"hide",r=this.getDatasetMeta(t),o=r.controller._resolveAnimations(void 0,s);Hi(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(r,{visible:i}),this.update(l=>l.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),jt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),_l(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Ti[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(r,o)=>{e.addEventListener(this,r,o),t[r]=o},s=(r,o,l)=>{r.offsetX=o,r.offsetY=l,this._eventHandler(r)};F(this.options.events,r=>i(r,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(a,c)=>{e.addEventListener(this,a,c),t[a]=c},s=(a,c)=>{t[a]&&(e.removeEventListener(this,a,c),delete t[a])},r=(a,c)=>{this.canvas&&this.resize(a,c)};let o;const l=()=>{s("attach",l),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,s("resize",r),this._stop(),this._resize(0,0),i("attach",l)},e.isAttached(this.canvas)?l():o()}unbindEvents(){F(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},F(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const s=i?"set":"remove";let r,o,l,a;for(e==="dataset"&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+s+"DatasetHoverStyle"]()),l=0,a=t.length;l<a;++l){o=t[l];const c=o&&this.getDatasetMeta(o.datasetIndex).controller;c&&c[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map(({datasetIndex:r,index:o})=>{const l=this.getDatasetMeta(r);if(!l)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:l.data[o],index:o}});!zi(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,i){const s=this.options.hover,r=(a,c)=>a.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),o=r(e,t),l=i?t:r(t,e);o.length&&this.updateHoverStyle(o,s.mode,!1),l.length&&s.mode&&this.updateHoverStyle(l,s.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=o=>(o.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:s=[],options:r}=this,o=e,l=this._getActiveElements(t,s,i,o),a=ey(t),c=Wx(t,this._lastEvent,i,a);i&&(this._lastEvent=null,$(r.onHover,[t,l,this],this),a&&$(r.onClick,[t,l,this],this));const h=!zi(l,s);return(h||e)&&(this._active=l,this._updateHoverStyles(l,s,e)),this._lastEvent=c,h}_getActiveElements(t,e,i,s){if(t.type==="mouseout")return[];if(!i)return e;const r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}}P(Se,"defaults",K),P(Se,"instances",Ti),P(Se,"overrides",Ve),P(Se,"registry",Lt),P(Se,"version",Fx),P(Se,"getChart",na);function ia(){return F(Se.instances,n=>n._plugins.invalidate())}function Jh(n,t,e=t){n.lineCap=L(e.borderCapStyle,t.borderCapStyle),n.setLineDash(L(e.borderDash,t.borderDash)),n.lineDashOffset=L(e.borderDashOffset,t.borderDashOffset),n.lineJoin=L(e.borderJoinStyle,t.borderJoinStyle),n.lineWidth=L(e.borderWidth,t.borderWidth),n.strokeStyle=L(e.borderColor,t.borderColor)}function jx(n,t,e){n.lineTo(e.x,e.y)}function Kx(n){return n.stepped?Ay:n.tension||n.cubicInterpolationMode==="monotone"?Ey:jx}function Uh(n,t,e={}){const i=n.length,{start:s=0,end:r=i-1}=e,{start:o,end:l}=t,a=Math.max(s,o),c=Math.min(r,l),h=s<o&&r<o||s>l&&r>l;return{count:i,start:a,loop:t.loop,ilen:c<a&&!h?i+c-a:c-a}}function qx(n,t,e,i){const{points:s,options:r}=t,{count:o,start:l,loop:a,ilen:c}=Uh(s,e,i),h=Kx(r);let{move:d=!0,reverse:u}=i||{},f,p,m;for(f=0;f<=c;++f)p=s[(l+(u?c-f:f))%o],!p.skip&&(d?(n.moveTo(p.x,p.y),d=!1):h(n,m,p,u,r.stepped),m=p);return a&&(p=s[(l+(u?c:0))%o],h(n,m,p,u,r.stepped)),!!a}function Jx(n,t,e,i){const s=t.points,{count:r,start:o,ilen:l}=Uh(s,e,i),{move:a=!0,reverse:c}=i||{};let h=0,d=0,u,f,p,m,g,y;const b=w=>(o+(c?l-w:w))%r,M=()=>{m!==g&&(n.lineTo(h,g),n.lineTo(h,m),n.lineTo(h,y))};for(a&&(f=s[b(0)],n.moveTo(f.x,f.y)),u=0;u<=l;++u){if(f=s[b(u)],f.skip)continue;const w=f.x,x=f.y,C=w|0;C===p?(x<m?m=x:x>g&&(g=x),h=(d*h+w)/++d):(M(),n.lineTo(w,x),p=C,d=0,m=g=x),y=x}M()}function mr(n){const t=n.options,e=t.borderDash&&t.borderDash.length;return!n._decimated&&!n._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Jx:qx}function Ux(n){return n.stepped?hb:n.tension||n.cubicInterpolationMode==="monotone"?db:ke}function Gx(n,t,e,i){let s=t._path;s||(s=t._path=new Path2D,t.path(s,e,i)&&s.closePath()),Jh(n,t.options),n.stroke(s)}function Yx(n,t,e,i){const{segments:s,options:r}=t,o=mr(t);for(const l of s)Jh(n,r,l.style),n.beginPath(),o(n,t,l,{start:e,end:e+i-1})&&n.closePath(),n.stroke()}const Xx=typeof Path2D=="function";function Qx(n,t,e,i){Xx&&!t.options.segment?Gx(n,t,e,i):Yx(n,t,e,i)}class De extends Yt{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;nb(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=yb(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,s=t[e],r=this.points,o=Ih(this,{property:e,start:s,end:s});if(!o.length)return;const l=[],a=Ux(i);let c,h;for(c=0,h=o.length;c<h;++c){const{start:d,end:u}=o[c],f=r[d],p=r[u];if(f===p){l.push(f);continue}const m=Math.abs((s-f[e])/(p[e]-f[e])),g=a(f,p,m,i.stepped);g[e]=t[e],l.push(g)}return l.length===1?l[0]:l}pathSegment(t,e,i){return mr(this)(t,this,e,i)}path(t,e,i){const s=this.segments,r=mr(this);let o=this._loop;e=e||0,i=i||this.points.length-e;for(const l of s)o&=r(t,this,l,{start:e,end:e+i-1});return!!o}draw(t,e,i,s){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),Qx(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}P(De,"id","line"),P(De,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),P(De,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),P(De,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function sa(n,t,e,i){const s=n.options,{[e]:r}=n.getProps([e],i);return Math.abs(t-r)<s.radius+s.hitRadius}class Vs extends Yt{constructor(e){super();P(this,"parsed");P(this,"skip");P(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,i,s){const r=this.options,{x:o,y:l}=this.getProps(["x","y"],s);return Math.pow(e-o,2)+Math.pow(i-l,2)<Math.pow(r.hitRadius+r.radius,2)}inXRange(e,i){return sa(this,e,"x",i)}inYRange(e,i){return sa(this,e,"y",i)}getCenterPoint(e){const{x:i,y:s}=this.getProps(["x","y"],e);return{x:i,y:s}}size(e){e=e||this.options||{};let i=e.radius||0;i=Math.max(i,i&&e.hoverRadius||0);const s=i&&e.borderWidth||0;return(i+s)*2}draw(e,i){const s=this.options;this.skip||s.radius<.1||!Jn(this,i,this.size(s)/2)||(e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.fillStyle=s.backgroundColor,dr(e,s,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}P(Vs,"id","point"),P(Vs,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),P(Vs,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Zx(n,t,e){const i=n.segments,s=n.points,r=t.points,o=[];for(const l of i){let{start:a,end:c}=l;c=hs(a,c,s);const h=gr(e,s[a],s[c],l.loop);if(!t.segments){o.push({source:l,target:h,start:s[a],end:s[c]});continue}const d=Ih(t,h);for(const u of d){const f=gr(e,r[u.start],r[u.end],u.loop),p=Ph(l,s,f);for(const m of p)o.push({source:m,target:u,start:{[e]:ra(h,f,"start",Math.max)},end:{[e]:ra(h,f,"end",Math.min)}})}}return o}function gr(n,t,e,i){if(i)return;let s=t[n],r=e[n];return n==="angle"&&(s=Bt(s),r=Bt(r)),{property:n,start:s,end:r}}function t0(n,t){const{x:e=null,y:i=null}=n||{},s=t.points,r=[];return t.segments.forEach(({start:o,end:l})=>{l=hs(o,l,s);const a=s[o],c=s[l];i!==null?(r.push({x:a.x,y:i}),r.push({x:c.x,y:i})):e!==null&&(r.push({x:e,y:a.y}),r.push({x:e,y:c.y}))}),r}function hs(n,t,e){for(;t>n;t--){const i=e[t];if(!isNaN(i.x)&&!isNaN(i.y))break}return t}function ra(n,t,e,i){return n&&t?i(n[e],t[e]):n?n[e]:t?t[e]:0}function Gh(n,t){let e=[],i=!1;return U(n)?(i=!0,e=n):e=t0(n,t),e.length?new De({points:e,options:{tension:0},_loop:i,_fullLoop:i}):null}function oa(n){return n&&n.fill!==!1}function e0(n,t,e){let s=n[t].fill;const r=[t];let o;if(!e)return s;for(;s!==!1&&r.indexOf(s)===-1;){if(!rt(s))return s;if(o=n[s],!o)return!1;if(o.visible)return s;r.push(s),s=o.fill}return!1}function n0(n,t,e){const i=o0(n);if(B(i))return isNaN(i.value)?!1:i;let s=parseFloat(i);return rt(s)&&Math.floor(s)===s?i0(i[0],t,s,e):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function i0(n,t,e,i){return(n==="-"||n==="+")&&(e=t+e),e===t||e<0||e>=i?!1:e}function s0(n,t){let e=null;return n==="start"?e=t.bottom:n==="end"?e=t.top:B(n)?e=t.getPixelForValue(n.value):t.getBasePixel&&(e=t.getBasePixel()),e}function r0(n,t,e){let i;return n==="start"?i=e:n==="end"?i=t.options.reverse?t.min:t.max:B(n)?i=n.value:i=t.getBaseValue(),i}function o0(n){const t=n.options,e=t.fill;let i=L(e&&e.target,e);return i===void 0&&(i=!!t.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function l0(n){const{scale:t,index:e,line:i}=n,s=[],r=i.segments,o=i.points,l=a0(t,e);l.push(Gh({x:null,y:t.bottom},i));for(let a=0;a<r.length;a++){const c=r[a];for(let h=c.start;h<=c.end;h++)c0(s,o[h],l)}return new De({points:s,options:{}})}function a0(n,t){const e=[],i=n.getMatchingVisibleMetas("line");for(let s=0;s<i.length;s++){const r=i[s];if(r.index===t)break;r.hidden||e.unshift(r.dataset)}return e}function c0(n,t,e){const i=[];for(let s=0;s<e.length;s++){const r=e[s],{first:o,last:l,point:a}=h0(r,t,"x");if(!(!a||o&&l)){if(o)i.unshift(a);else if(n.push(a),!l)break}}n.push(...i)}function h0(n,t,e){const i=n.interpolate(t,e);if(!i)return{};const s=i[e],r=n.segments,o=n.points;let l=!1,a=!1;for(let c=0;c<r.length;c++){const h=r[c],d=o[h.start][e],u=o[h.end][e];if(Xe(s,d,u)){l=s===d,a=s===u;break}}return{first:l,last:a,point:i}}class Yh{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:s,y:r,radius:o}=this;return e=e||{start:0,end:At},t.arc(s,r,o,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:s}=this,r=t.angle;return{x:e+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}}function d0(n){const{chart:t,fill:e,line:i}=n;if(rt(e))return u0(t,e);if(e==="stack")return l0(n);if(e==="shape")return!0;const s=f0(n);return s instanceof Yh?s:Gh(s,i)}function u0(n,t){const e=n.getDatasetMeta(t);return e&&n.isDatasetVisible(t)?e.dataset:null}function f0(n){return(n.scale||{}).getPointPositionForValue?m0(n):p0(n)}function p0(n){const{scale:t={},fill:e}=n,i=s0(e,t);if(rt(i)){const s=t.isHorizontal();return{x:s?i:null,y:s?null:i}}return null}function m0(n){const{scale:t,fill:e}=n,i=t.options,s=t.getLabels().length,r=i.reverse?t.max:t.min,o=r0(e,t,r),l=[];if(i.grid.circular){const a=t.getPointPositionForValue(0,r);return new Yh({x:a.x,y:a.y,radius:t.getDistanceFromCenterForValue(o)})}for(let a=0;a<s;++a)l.push(t.getPointPositionForValue(a,o));return l}function Hs(n,t,e){const i=d0(t),{chart:s,index:r,line:o,scale:l,axis:a}=t,c=o.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},p=s.getDatasetMeta(r),m=Rh(s,p);i&&o.points.length&&(rs(n,e),g0(n,{line:o,target:i,above:u,below:f,area:e,scale:l,axis:a,clip:m}),ls(n))}function g0(n,t){const{line:e,target:i,above:s,below:r,area:o,scale:l,clip:a}=t,c=e._loop?"angle":t.axis;n.save();let h=r;r!==s&&(c==="x"?(la(n,i,o.top),$s(n,{line:e,target:i,color:s,scale:l,property:c,clip:a}),n.restore(),n.save(),la(n,i,o.bottom)):c==="y"&&(aa(n,i,o.left),$s(n,{line:e,target:i,color:r,scale:l,property:c,clip:a}),n.restore(),n.save(),aa(n,i,o.right),h=s)),$s(n,{line:e,target:i,color:h,scale:l,property:c,clip:a}),n.restore()}function la(n,t,e){const{segments:i,points:s}=t;let r=!0,o=!1;n.beginPath();for(const l of i){const{start:a,end:c}=l,h=s[a],d=s[hs(a,c,s)];r?(n.moveTo(h.x,h.y),r=!1):(n.lineTo(h.x,e),n.lineTo(h.x,h.y)),o=!!t.pathSegment(n,l,{move:o}),o?n.closePath():n.lineTo(d.x,e)}n.lineTo(t.first().x,e),n.closePath(),n.clip()}function aa(n,t,e){const{segments:i,points:s}=t;let r=!0,o=!1;n.beginPath();for(const l of i){const{start:a,end:c}=l,h=s[a],d=s[hs(a,c,s)];r?(n.moveTo(h.x,h.y),r=!1):(n.lineTo(e,h.y),n.lineTo(h.x,h.y)),o=!!t.pathSegment(n,l,{move:o}),o?n.closePath():n.lineTo(e,d.y)}n.lineTo(e,t.first().y),n.closePath(),n.clip()}function $s(n,t){const{line:e,target:i,property:s,color:r,scale:o,clip:l}=t,a=Zx(e,i,s);for(const{source:c,target:h,start:d,end:u}of a){const{style:{backgroundColor:f=r}={}}=c,p=i!==!0;n.save(),n.fillStyle=f,y0(n,o,l,p&&gr(s,d,u)),n.beginPath();const m=!!e.pathSegment(n,c);let g;if(p){m?n.closePath():ca(n,i,u,s);const y=!!i.pathSegment(n,h,{move:m,reverse:!0});g=m&&y,g||ca(n,i,d,s)}n.closePath(),n.fill(g?"evenodd":"nonzero"),n.restore()}}function y0(n,t,e,i){const s=t.chart.chartArea,{property:r,start:o,end:l}=i||{};if(r==="x"||r==="y"){let a,c,h,d;r==="x"?(a=o,c=s.top,h=l,d=s.bottom):(a=s.left,c=o,h=s.right,d=l),n.beginPath(),e&&(a=Math.max(a,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),n.rect(a,c,h-a,d-c),n.clip()}}function ca(n,t,e,i){const s=t.interpolate(e,i);s&&n.lineTo(s.x,s.y)}var W0={id:"filler",afterDatasetsUpdate(n,t,e){const i=(n.data.datasets||[]).length,s=[];let r,o,l,a;for(o=0;o<i;++o)r=n.getDatasetMeta(o),l=r.dataset,a=null,l&&l.options&&l instanceof De&&(a={visible:n.isDatasetVisible(o),index:o,fill:n0(l,o,i),chart:n,axis:r.controller.options.indexAxis,scale:r.vScale,line:l}),r.$filler=a,s.push(a);for(o=0;o<i;++o)a=s[o],!(!a||a.fill===!1)&&(a.fill=e0(s,o,e.propagate))},beforeDraw(n,t,e){const i=e.drawTime==="beforeDraw",s=n.getSortedVisibleDatasetMetas(),r=n.chartArea;for(let o=s.length-1;o>=0;--o){const l=s[o].$filler;l&&(l.line.updateControlPoints(r,l.axis),i&&l.fill&&Hs(n.ctx,l,r))}},beforeDatasetsDraw(n,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const i=n.getSortedVisibleDatasetMetas();for(let s=i.length-1;s>=0;--s){const r=i[s].$filler;oa(r)&&Hs(n.ctx,r,n.chartArea)}},beforeDatasetDraw(n,t,e){const i=t.meta.$filler;!oa(i)||e.drawTime!=="beforeDatasetDraw"||Hs(n.ctx,i,n.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const ha=(n,t)=>{let{boxHeight:e=t,boxWidth:i=t}=n;return n.usePointStyle&&(e=Math.min(e,t),i=n.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:e,itemHeight:Math.max(t,e)}},b0=(n,t)=>n!==null&&t!==null&&n.datasetIndex===t.datasetIndex&&n.index===t.index;class da extends Yt{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=$(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(i=>t.filter(i,this.chart.data))),t.sort&&(e=e.sort((i,s)=>t.sort(i,s,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const i=t.labels,s=st(i.font),r=s.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:a}=ha(i,r);let c,h;e.font=s.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(o,r,l,a)+10):(h=this.maxHeight,c=this._fitCols(o,s,l,a)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){const{ctx:r,maxWidth:o,options:{labels:{padding:l}}}=this,a=this.legendHitBoxes=[],c=this.lineWidths=[0],h=s+l;let d=t;r.textAlign="left",r.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((p,m)=>{const g=i+e/2+r.measureText(p.text).width;(m===0||c[c.length-1]+g+2*l>o)&&(d+=h,c[c.length-(m>0?0:1)]=0,f+=h,u++),a[m]={left:0,top:f,row:u,width:g,height:s},c[c.length-1]+=g+l}),d}_fitCols(t,e,i,s){const{ctx:r,maxHeight:o,options:{labels:{padding:l}}}=this,a=this.legendHitBoxes=[],c=this.columnSizes=[],h=o-t;let d=l,u=0,f=0,p=0,m=0;return this.legendItems.forEach((g,y)=>{const{itemWidth:b,itemHeight:M}=x0(i,e,r,g,s);y>0&&f+M+2*l>h&&(d+=u+l,c.push({width:u,height:f}),p+=u+l,m++,u=f=0),a[y]={left:p,top:f,col:m,width:b,height:M},u=Math.max(u,b),f+=M+l}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:r}}=this,o=tn(r,this.left,this.width);if(this.isHorizontal()){let l=0,a=et(i,this.left+s,this.right-this.lineWidths[l]);for(const c of e)l!==c.row&&(l=c.row,a=et(i,this.left+s,this.right-this.lineWidths[l])),c.top+=this.top+t+s,c.left=o.leftForLtr(o.x(a),c.width),a+=c.width+s}else{let l=0,a=et(i,this.top+t+s,this.bottom-this.columnSizes[l].height);for(const c of e)c.col!==l&&(l=c.col,a=et(i,this.top+t+s,this.bottom-this.columnSizes[l].height)),c.top=a,c.left+=this.left+s,c.left=o.leftForLtr(o.x(c.left),c.width),a+=c.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;rs(t,this),this._draw(),ls(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:r,labels:o}=t,l=K.color,a=tn(t.rtl,this.left,this.width),c=st(o.font),{padding:h}=o,d=c.size,u=d/2;let f;this.drawTitle(),s.textAlign=a.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=c.string;const{boxWidth:p,boxHeight:m,itemHeight:g}=ha(o,d),y=function(C,_,k){if(isNaN(p)||p<=0||isNaN(m)||m<0)return;s.save();const v=L(k.lineWidth,1);if(s.fillStyle=L(k.fillStyle,l),s.lineCap=L(k.lineCap,"butt"),s.lineDashOffset=L(k.lineDashOffset,0),s.lineJoin=L(k.lineJoin,"miter"),s.lineWidth=v,s.strokeStyle=L(k.strokeStyle,l),s.setLineDash(L(k.lineDash,[])),o.usePointStyle){const A={radius:m*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:v},R=a.xPlus(C,p/2),z=_+u;wh(s,A,R,z,o.pointStyleWidth&&p)}else{const A=_+Math.max((d-m)/2,0),R=a.leftForLtr(C,p),z=Pn(k.borderRadius);s.beginPath(),Object.values(z).some(ut=>ut!==0)?ur(s,{x:R,y:A,w:p,h:m,radius:z}):s.rect(R,A,p,m),s.fill(),v!==0&&s.stroke()}s.restore()},b=function(C,_,k){Un(s,k.text,C,_+g/2,c,{strikethrough:k.hidden,textAlign:a.textAlign(k.textAlign)})},M=this.isHorizontal(),w=this._computeTitleHeight();M?f={x:et(r,this.left+h,this.right-i[0]),y:this.top+h+w,line:0}:f={x:this.left+h,y:et(r,this.top+w+h,this.bottom-e[0].height),line:0},Ah(this.ctx,t.textDirection);const x=g+h;this.legendItems.forEach((C,_)=>{s.strokeStyle=C.fontColor,s.fillStyle=C.fontColor;const k=s.measureText(C.text).width,v=a.textAlign(C.textAlign||(C.textAlign=o.textAlign)),A=p+u+k;let R=f.x,z=f.y;a.setWidth(this.width),M?_>0&&R+A+h>this.right&&(z=f.y+=x,f.line++,R=f.x=et(r,this.left+h,this.right-i[f.line])):_>0&&z+x>this.bottom&&(R=f.x=R+e[f.line].width+h,f.line++,z=f.y=et(r,this.top+w+h,this.bottom-e[f.line].height));const ut=a.x(R);if(y(ut,z,C),R=yy(v,R+p+u,M?R+A:this.right,t.rtl),b(a.x(R),z,C),M)f.x+=A+h;else if(typeof C.text!="string"){const Nt=c.lineHeight;f.y+=Xh(C,Nt)+h}else f.y+=x}),Eh(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=st(e.font),s=Ot(e.padding);if(!e.display)return;const r=tn(t.rtl,this.left,this.width),o=this.ctx,l=e.position,a=i.size/2,c=s.top+a;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=et(t.align,d,this.right-u);else{const p=this.columnSizes.reduce((m,g)=>Math.max(m,g.height),0);h=c+et(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const f=et(l,d,d+u);o.textAlign=r.textAlign(Kr(l)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,Un(o,e.text,f,h,i)}_computeTitleHeight(){const t=this.options.title,e=st(t.font),i=Ot(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,r;if(Xe(t,this.left,this.right)&&Xe(e,this.top,this.bottom)){for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(s=r[i],Xe(t,s.left,s.left+s.width)&&Xe(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){const e=this.options;if(!M0(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const s=this._hoveredItem,r=b0(s,i);s&&!r&&$(e.onLeave,[t,s,this],this),this._hoveredItem=i,i&&!r&&$(e.onHover,[t,i,this],this)}else i&&$(e.onClick,[t,i,this],this)}}function x0(n,t,e,i,s){const r=k0(i,n,t,e),o=S0(s,i,t.lineHeight);return{itemWidth:r,itemHeight:o}}function k0(n,t,e,i){let s=n.text;return s&&typeof s!="string"&&(s=s.reduce((r,o)=>r.length>o.length?r:o)),t+e.size/2+i.measureText(s).width}function S0(n,t,e){let i=n;return typeof t.text!="string"&&(i=Xh(t,e)),i}function Xh(n,t){const e=n.text?n.text.length:0;return t*e}function M0(n,t){return!!((n==="mousemove"||n==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(n==="click"||n==="mouseup"))}var j0={id:"legend",_element:da,start(n,t,e){const i=n.legend=new da({ctx:n.ctx,options:e,chart:n});_t.configure(n,i,e),_t.addBox(n,i)},stop(n){_t.removeBox(n,n.legend),delete n.legend},beforeUpdate(n,t,e){const i=n.legend;_t.configure(n,i,e),i.options=e},afterUpdate(n){const t=n.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(n,t){t.replay||n.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(n,t,e){const i=t.datasetIndex,s=e.chart;s.isDatasetVisible(i)?(s.hide(i),t.hidden=!0):(s.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:n=>n.chart.options.color,boxWidth:40,padding:10,generateLabels(n){const t=n.data.datasets,{labels:{usePointStyle:e,pointStyle:i,textAlign:s,color:r,useBorderRadius:o,borderRadius:l}}=n.legend.options;return n._getSortedDatasetMetas().map(a=>{const c=a.controller.getStyle(e?0:void 0),h=Ot(c.borderWidth);return{text:t[a.index].label,fillStyle:c.backgroundColor,fontColor:r,hidden:!a.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:i||c.pointStyle,rotation:c.rotation,textAlign:s||c.textAlign,borderRadius:o&&(l||c.borderRadius),datasetIndex:a.index}},this)}},title:{color:n=>n.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:n=>!n.startsWith("on"),labels:{_scriptable:n=>!["generateLabels","filter","sort"].includes(n)}}};class Qh extends Yt{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const s=U(i.text)?i.text.length:1;this._padding=Ot(i.padding);const r=s*st(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:i,bottom:s,right:r,options:o}=this,l=o.align;let a=0,c,h,d;return this.isHorizontal()?(h=et(l,i,r),d=e+t,c=r-i):(o.position==="left"?(h=i+t,d=et(l,s,e),a=X*-.5):(h=r-t,d=et(l,e,s),a=X*.5),c=s-e),{titleX:h,titleY:d,maxWidth:c,rotation:a}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=st(e.font),r=i.lineHeight/2+this._padding.top,{titleX:o,titleY:l,maxWidth:a,rotation:c}=this._drawArgs(r);Un(t,e.text,0,0,i,{color:e.color,maxWidth:a,rotation:c,textAlign:Kr(e.align),textBaseline:"middle",translation:[o,l]})}}function w0(n,t){const e=new Qh({ctx:n.ctx,options:t,chart:n});_t.configure(n,e,t),_t.addBox(n,e),n.titleBlock=e}var K0={id:"title",_element:Qh,start(n,t,e){w0(n,e)},stop(n){const t=n.titleBlock;_t.removeBox(n,t),delete n.titleBlock},beforeUpdate(n,t,e){const i=n.titleBlock;_t.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const _n={average(n){if(!n.length)return!1;let t,e,i=new Set,s=0,r=0;for(t=0,e=n.length;t<e;++t){const l=n[t].element;if(l&&l.hasValue()){const a=l.tooltipPosition();i.add(a.x),s+=a.y,++r}}return r===0||i.size===0?!1:{x:[...i].reduce((l,a)=>l+a)/i.size,y:s/r}},nearest(n,t){if(!n.length)return!1;let e=t.x,i=t.y,s=Number.POSITIVE_INFINITY,r,o,l;for(r=0,o=n.length;r<o;++r){const a=n[r].element;if(a&&a.hasValue()){const c=a.getCenterPoint(),h=cr(t,c);h<s&&(s=h,l=a)}}if(l){const a=l.tooltipPosition();e=a.x,i=a.y}return{x:e,y:i}}};function It(n,t){return t&&(U(t)?Array.prototype.push.apply(n,t):n.push(t)),n}function Kt(n){return(typeof n=="string"||n instanceof String)&&n.indexOf(`
`)>-1?n.split(`
`):n}function C0(n,t){const{element:e,datasetIndex:i,index:s}=t,r=n.getDatasetMeta(i).controller,{label:o,value:l}=r.getLabelAndValue(s);return{chart:n,label:o,parsed:r.getParsed(s),raw:n.data.datasets[i].data[s],formattedValue:l,dataset:r.getDataset(),dataIndex:s,datasetIndex:i,element:e}}function ua(n,t){const e=n.chart.ctx,{body:i,footer:s,title:r}=n,{boxWidth:o,boxHeight:l}=t,a=st(t.bodyFont),c=st(t.titleFont),h=st(t.footerFont),d=r.length,u=s.length,f=i.length,p=Ot(t.padding);let m=p.height,g=0,y=i.reduce((w,x)=>w+x.before.length+x.lines.length+x.after.length,0);if(y+=n.beforeBody.length+n.afterBody.length,d&&(m+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),y){const w=t.displayColors?Math.max(l,a.lineHeight):a.lineHeight;m+=f*w+(y-f)*a.lineHeight+(y-1)*t.bodySpacing}u&&(m+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let b=0;const M=function(w){g=Math.max(g,e.measureText(w).width+b)};return e.save(),e.font=c.string,F(n.title,M),e.font=a.string,F(n.beforeBody.concat(n.afterBody),M),b=t.displayColors?o+2+t.boxPadding:0,F(i,w=>{F(w.before,M),F(w.lines,M),F(w.after,M)}),b=0,e.font=h.string,F(n.footer,M),e.restore(),g+=p.width,{width:g,height:m}}function _0(n,t){const{y:e,height:i}=t;return e<i/2?"top":e>n.height-i/2?"bottom":"center"}function T0(n,t,e,i){const{x:s,width:r}=i,o=e.caretSize+e.caretPadding;if(n==="left"&&s+r+o>t.width||n==="right"&&s-r-o<0)return!0}function O0(n,t,e,i){const{x:s,width:r}=e,{width:o,chartArea:{left:l,right:a}}=n;let c="center";return i==="center"?c=s<=(l+a)/2?"left":"right":s<=r/2?c="left":s>=o-r/2&&(c="right"),T0(c,n,t,e)&&(c="center"),c}function fa(n,t,e){const i=e.yAlign||t.yAlign||_0(n,e);return{xAlign:e.xAlign||t.xAlign||O0(n,t,e,i),yAlign:i}}function v0(n,t){let{x:e,width:i}=n;return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function D0(n,t,e){let{y:i,height:s}=n;return t==="top"?i+=e:t==="bottom"?i-=s+e:i-=s/2,i}function pa(n,t,e,i){const{caretSize:s,caretPadding:r,cornerRadius:o}=n,{xAlign:l,yAlign:a}=e,c=s+r,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Pn(o);let p=v0(t,l);const m=D0(t,a,c);return a==="center"?l==="left"?p+=c:l==="right"&&(p-=c):l==="left"?p-=Math.max(h,u)+s:l==="right"&&(p+=Math.max(d,f)+s),{x:Ct(p,0,i.width-t.width),y:Ct(m,0,i.height-t.height)}}function bi(n,t,e){const i=Ot(e.padding);return t==="center"?n.x+n.width/2:t==="right"?n.x+n.width-i.right:n.x+i.left}function ma(n){return It([],Kt(n))}function A0(n,t,e){return $e(n,{tooltip:t,tooltipItems:e,type:"tooltip"})}function ga(n,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?n.override(e):n}const Zh={beforeTitle:Wt,title(n){if(n.length>0){const t=n[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:Wt,beforeBody:Wt,beforeLabel:Wt,label(n){if(this&&this.options&&this.options.mode==="dataset")return n.label+": "+n.formattedValue||n.formattedValue;let t=n.dataset.label||"";t&&(t+=": ");const e=n.formattedValue;return H(e)||(t+=e),t},labelColor(n){const e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(n){const e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Wt,afterBody:Wt,beforeFooter:Wt,footer:Wt,afterFooter:Wt};function ft(n,t,e,i){const s=n[t].call(e,i);return typeof s>"u"?Zh[t].call(e,i):s}class yr extends Yt{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,r=new Lh(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=A0(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:i}=e,s=ft(i,"beforeTitle",this,t),r=ft(i,"title",this,t),o=ft(i,"afterTitle",this,t);let l=[];return l=It(l,Kt(s)),l=It(l,Kt(r)),l=It(l,Kt(o)),l}getBeforeBody(t,e){return ma(ft(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,s=[];return F(t,r=>{const o={before:[],lines:[],after:[]},l=ga(i,r);It(o.before,Kt(ft(l,"beforeLabel",this,r))),It(o.lines,ft(l,"label",this,r)),It(o.after,Kt(ft(l,"afterLabel",this,r))),s.push(o)}),s}getAfterBody(t,e){return ma(ft(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,s=ft(i,"beforeFooter",this,t),r=ft(i,"footer",this,t),o=ft(i,"afterFooter",this,t);let l=[];return l=It(l,Kt(s)),l=It(l,Kt(r)),l=It(l,Kt(o)),l}_createItems(t){const e=this._active,i=this.chart.data,s=[],r=[],o=[];let l=[],a,c;for(a=0,c=e.length;a<c;++a)l.push(C0(this.chart,e[a]));return t.filter&&(l=l.filter((h,d,u)=>t.filter(h,d,u,i))),t.itemSort&&(l=l.sort((h,d)=>t.itemSort(h,d,i))),F(l,h=>{const d=ga(t.callbacks,h);s.push(ft(d,"labelColor",this,h)),r.push(ft(d,"labelPointStyle",this,h)),o.push(ft(d,"labelTextColor",this,h))}),this.labelColors=s,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){const i=this.options.setContext(this.getContext()),s=this._active;let r,o=[];if(!s.length)this.opacity!==0&&(r={opacity:0});else{const l=_n[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const a=this._size=ua(this,i),c=Object.assign({},l,a),h=fa(this.chart,i,c),d=pa(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,r={opacity:1,x:d.x,y:d.y,width:a.width,height:a.height,caretX:l.x,caretY:l.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){const r=this.getCaretPosition(t,i,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){const{xAlign:s,yAlign:r}=this,{caretSize:o,cornerRadius:l}=i,{topLeft:a,topRight:c,bottomLeft:h,bottomRight:d}=Pn(l),{x:u,y:f}=t,{width:p,height:m}=e;let g,y,b,M,w,x;return r==="center"?(w=f+m/2,s==="left"?(g=u,y=g-o,M=w+o,x=w-o):(g=u+p,y=g+o,M=w-o,x=w+o),b=g):(s==="left"?y=u+Math.max(a,h)+o:s==="right"?y=u+p-Math.max(c,d)-o:y=this.caretX,r==="top"?(M=f,w=M-o,g=y-o,b=y+o):(M=f+m,w=M+o,g=y+o,b=y-o),x=M),{x1:g,x2:y,x3:b,y1:M,y2:w,y3:x}}drawTitle(t,e,i){const s=this.title,r=s.length;let o,l,a;if(r){const c=tn(i.rtl,this.x,this.width);for(t.x=bi(this,i.titleAlign,i),e.textAlign=c.textAlign(i.titleAlign),e.textBaseline="middle",o=st(i.titleFont),l=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,a=0;a<r;++a)e.fillText(s[a],c.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+l,a+1===r&&(t.y+=i.titleMarginBottom-l)}}_drawColorBox(t,e,i,s,r){const o=this.labelColors[i],l=this.labelPointStyles[i],{boxHeight:a,boxWidth:c}=r,h=st(r.bodyFont),d=bi(this,"left",r),u=s.x(d),f=a<h.lineHeight?(h.lineHeight-a)/2:0,p=e.y+f;if(r.usePointStyle){const m={radius:Math.min(c,a)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},g=s.leftForLtr(u,c)+c/2,y=p+a/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,dr(t,m,g,y),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,dr(t,m,g,y)}else{t.lineWidth=B(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const m=s.leftForLtr(u,c),g=s.leftForLtr(s.xPlus(u,1),c-2),y=Pn(o.borderRadius);Object.values(y).some(b=>b!==0)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,ur(t,{x:m,y:p,w:c,h:a,radius:y}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),ur(t,{x:g,y:p+1,w:c-2,h:a-2,radius:y}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(m,p,c,a),t.strokeRect(m,p,c,a),t.fillStyle=o.backgroundColor,t.fillRect(g,p+1,c-2,a-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:s}=this,{bodySpacing:r,bodyAlign:o,displayColors:l,boxHeight:a,boxWidth:c,boxPadding:h}=i,d=st(i.bodyFont);let u=d.lineHeight,f=0;const p=tn(i.rtl,this.x,this.width),m=function(k){e.fillText(k,p.x(t.x+f),t.y+u/2),t.y+=u+r},g=p.textAlign(o);let y,b,M,w,x,C,_;for(e.textAlign=o,e.textBaseline="middle",e.font=d.string,t.x=bi(this,g,i),e.fillStyle=i.bodyColor,F(this.beforeBody,m),f=l&&g!=="right"?o==="center"?c/2+h:c+2+h:0,w=0,C=s.length;w<C;++w){for(y=s[w],b=this.labelTextColors[w],e.fillStyle=b,F(y.before,m),M=y.lines,l&&M.length&&(this._drawColorBox(e,t,w,p,i),u=Math.max(d.lineHeight,a)),x=0,_=M.length;x<_;++x)m(M[x]),u=d.lineHeight;F(y.after,m)}f=0,u=d.lineHeight,F(this.afterBody,m),t.y-=r}drawFooter(t,e,i){const s=this.footer,r=s.length;let o,l;if(r){const a=tn(i.rtl,this.x,this.width);for(t.x=bi(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=a.textAlign(i.footerAlign),e.textBaseline="middle",o=st(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,l=0;l<r;++l)e.fillText(s[l],a.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){const{xAlign:r,yAlign:o}=this,{x:l,y:a}=t,{width:c,height:h}=i,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:p}=Pn(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(l+d,a),o==="top"&&this.drawCaret(t,e,i,s),e.lineTo(l+c-u,a),e.quadraticCurveTo(l+c,a,l+c,a+u),o==="center"&&r==="right"&&this.drawCaret(t,e,i,s),e.lineTo(l+c,a+h-p),e.quadraticCurveTo(l+c,a+h,l+c-p,a+h),o==="bottom"&&this.drawCaret(t,e,i,s),e.lineTo(l+f,a+h),e.quadraticCurveTo(l,a+h,l,a+h-f),o==="center"&&r==="left"&&this.drawCaret(t,e,i,s),e.lineTo(l,a+d),e.quadraticCurveTo(l,a,l+d,a),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){const o=_n[t.position].call(this,this._active,this._eventPosition);if(!o)return;const l=this._size=ua(this,t),a=Object.assign({},o,this._size),c=fa(e,t,a),h=pa(t,a,c,e);(s._to!==h.x||r._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=l.width,this.height=l.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=Ot(e.padding),l=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&l&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,s,e),Ah(t,e.textDirection),r.y+=o.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),Eh(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,s=t.map(({datasetIndex:l,index:a})=>{const c=this.chart.getDatasetMeta(l);if(!c)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:c.data[a],index:a}}),r=!zi(i,s),o=this._positionChanged(s,e);(r||o)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,r=this._active||[],o=this._getActiveElements(t,r,e,i),l=this._positionChanged(o,t),a=e||!zi(o,r)||l;return a&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),a}_getActiveElements(t,e,i,s){const r=this.options;if(t.type==="mouseout")return[];if(!s)return e.filter(l=>this.chart.data.datasets[l.datasetIndex]&&this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index)!==void 0);const o=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:i,caretY:s,options:r}=this,o=_n[r.position].call(this,t,e);return o!==!1&&(i!==o.x||s!==o.y)}}P(yr,"positioners",_n);var q0={id:"tooltip",_element:yr,positioners:_n,afterInit(n,t,e){e&&(n.tooltip=new yr({chart:n,options:e}))},beforeUpdate(n,t,e){n.tooltip&&n.tooltip.initialize(e)},reset(n,t,e){n.tooltip&&n.tooltip.initialize(e)},afterDraw(n){const t=n.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(n.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(n.ctx),n.notifyPlugins("afterTooltipDraw",e)}},afterEvent(n,t){if(n.tooltip){const e=t.replay;n.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(n,t)=>t.bodyFont.size,boxWidth:(n,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Zh},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:n=>n!=="filter"&&n!=="itemSort"&&n!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const E0=(n,t,e,i)=>(typeof t=="string"?(e=n.push(t)-1,i.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function N0(n,t,e,i){const s=n.indexOf(t);if(s===-1)return E0(n,t,e,i);const r=n.lastIndexOf(t);return s!==r?e:s}const P0=(n,t)=>n===null?null:Ct(Math.round(n),0,t);function ya(n){const t=this.getLabels();return n>=0&&n<t.length?t[n]:n}class ba extends un{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const i=this.getLabels();for(const{index:s,label:r}of e)i[s]===r&&i.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(H(t))return null;const i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:N0(i,t,L(e,t),this._addedLabels),P0(e,i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,s=[];let r=this.getLabels();r=t===0&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=t;o<=e;o++)s.push({value:o});return s}getLabelForValue(t){return ya.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}P(ba,"id","category"),P(ba,"defaults",{ticks:{callback:ya}});function I0(n,t){const e=[],{bounds:s,step:r,min:o,max:l,precision:a,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=n,f=r||1,p=h-1,{min:m,max:g}=t,y=!H(o),b=!H(l),M=!H(c),w=(g-m)/(d+1);let x=yl((g-m)/p/f)*f,C,_,k,v;if(x<1e-14&&!y&&!b)return[{value:m},{value:g}];v=Math.ceil(g/x)-Math.floor(m/x),v>p&&(x=yl(v*x/p/f)*f),H(a)||(C=Math.pow(10,a),x=Math.ceil(x*C)/C),s==="ticks"?(_=Math.floor(m/x)*x,k=Math.ceil(g/x)*x):(_=m,k=g),y&&b&&r&&oy((l-o)/r,x/1e3)?(v=Math.round(Math.min((l-o)/x,h)),x=(l-o)/v,_=o,k=l):M?(_=y?o:_,k=b?l:k,v=c-1,x=(k-_)/v):(v=(k-_)/x,An(v,Math.round(v),x/1e3)?v=Math.round(v):v=Math.ceil(v));const A=Math.max(bl(x),bl(_));C=Math.pow(10,H(a)?A:a),_=Math.round(_*C)/C,k=Math.round(k*C)/C;let R=0;for(y&&(u&&_!==o?(e.push({value:o}),_<o&&R++,An(Math.round((_+R*x)*C)/C,o,xa(o,w,n))&&R++):_<o&&R++);R<v;++R){const z=Math.round((_+R*x)*C)/C;if(b&&z>l)break;e.push({value:z})}return b&&u&&k!==l?e.length&&An(e[e.length-1].value,l,xa(l,w,n))?e[e.length-1].value=l:e.push({value:l}):(!b||k===l)&&e.push({value:k}),e}function xa(n,t,{horizontal:e,minRotation:i}){const s=Oe(i),r=(e?Math.sin(s):Math.cos(s))||.001,o=.75*t*(""+n).length;return Math.min(t/r,o)}class R0 extends un{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return H(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:s,max:r}=this;const o=a=>s=e?s:a,l=a=>r=i?r:a;if(t){const a=an(s),c=an(r);a<0&&c<0?l(0):a>0&&c>0&&o(0)}if(s===r){let a=r===0?1:Math.abs(r*.05);l(r+a),t||o(s-a)}this.min=s,this.max=r}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:i}=t,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),e=e||11),e&&(s=Math.min(e,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},r=this._range||this,o=I0(s,r);return t.bounds==="ticks"&&ly(o,this,"value"),t.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return Sh(t,this.chart.options.locale,this.options.ticks.format)}}class ka extends R0{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=rt(t)?t:0,this.max=rt(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=Oe(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,r.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}P(ka,"id","linear"),P(ka,"defaults",{ticks:{callback:Mh.formatters.numeric}});const ds={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},pt=Object.keys(ds);function Sa(n,t){return n-t}function Ma(n,t){if(H(t))return null;const e=n._adapter,{parser:i,round:s,isoWeekday:r}=n._parseOpts;let o=t;return typeof i=="function"&&(o=i(o)),rt(o)||(o=typeof i=="string"?e.parse(o,i):e.parse(o)),o===null?null:(s&&(o=s==="week"&&(qn(r)||r===!0)?e.startOf(o,"isoWeek",r):e.startOf(o,s)),+o)}function wa(n,t,e,i){const s=pt.length;for(let r=pt.indexOf(n);r<s-1;++r){const o=ds[pt[r]],l=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((e-t)/(l*o.size))<=i)return pt[r]}return pt[s-1]}function L0(n,t,e,i,s){for(let r=pt.length-1;r>=pt.indexOf(e);r--){const o=pt[r];if(ds[o].common&&n._adapter.diff(s,i,o)>=t-1)return o}return pt[e?pt.indexOf(e):0]}function B0(n){for(let t=pt.indexOf(n)+1,e=pt.length;t<e;++t)if(ds[pt[t]].common)return pt[t]}function Ca(n,t,e){if(!e)n[t]=!0;else if(e.length){const{lo:i,hi:s}=jr(e,t),r=e[i]>=t?e[i]:e[s];n[r]=!0}}function z0(n,t,e,i){const s=n._adapter,r=+s.startOf(t[0].value,i),o=t[t.length-1].value;let l,a;for(l=r;l<=o;l=+s.add(l,1,i))a=e[l],a>=0&&(t[a].major=!0);return t}function _a(n,t,e){const i=[],s={},r=t.length;let o,l;for(o=0;o<r;++o)l=t[o],s[l]=o,i.push({value:l,major:!1});return r===0||!e?i:z0(n,i,s,e)}class Ki extends un{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const i=t.time||(t.time={}),s=this._adapter=new Rb._date(t.adapters.date);s.init(e),Dn(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Ma(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:s,max:r,minDefined:o,maxDefined:l}=this.getUserBounds();function a(c){!o&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!l&&!isNaN(c.max)&&(r=Math.max(r,c.max))}(!o||!l)&&(a(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&a(this.getMinMax(!1))),s=rt(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),r=rt(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const r=this.min,o=this.max,l=fy(s,r,o);return this._unit=e.unit||(i.autoSkip?wa(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):L0(this,l.length,e.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:B0(this._unit),this.initOffsets(s),t.reverse&&l.reverse(),_a(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,i=0,s,r;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?e=1-s:e=(this.getDecimalForValue(t[1])-s)/2,r=this.getDecimalForValue(t[t.length-1]),t.length===1?i=r:i=(r-this.getDecimalForValue(t[t.length-2]))/2);const o=t.length<3?.5:.25;e=Ct(e,0,o),i=Ct(i,0,o),this._offsets={start:e,end:i,factor:1/(e+1+i)}}_generate(){const t=this._adapter,e=this.min,i=this.max,s=this.options,r=s.time,o=r.unit||wa(r.minUnit,e,i,this._getLabelCapacity(e)),l=L(s.ticks.stepSize,1),a=o==="week"?r.isoWeekday:!1,c=qn(a)||a===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",a)),d=+t.startOf(d,c?"day":o),t.diff(i,e,o)>1e5*l)throw new Error(e+" and "+i+" are too far apart with stepSize of "+l+" "+o);const p=s.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<i;u=+t.add(u,l,o),f++)Ca(h,u,p);return(u===i||s.bounds==="ticks"||f===1)&&Ca(h,u,p),Object.keys(h).sort(Sa).map(m=>+m)}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const s=this.options.time.displayFormats,r=this._unit,o=e||s[r];return this._adapter.format(t,o)}_tickFormatFunction(t,e,i,s){const r=this.options,o=r.ticks.callback;if(o)return $(o,[t,e,i],this);const l=r.time.displayFormats,a=this._unit,c=this._majorUnit,h=a&&l[a],d=c&&l[c],u=i[e],f=c&&d&&u&&u.major;return this._adapter.format(t,s||(f?d:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,s=Oe(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),o=Math.sin(s),l=this._resolveTickFontOptions(0).size;return{w:i*r+l*o,h:i*o+l*r}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,_a(this,[t],this._majorUnit),s),o=this._getLabelSize(r),l=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return l>0?l:1}getDataTimestamps(){let t=this._cache.data||[],e,i;if(t.length)return t;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(e=0,i=s.length;e<i;++e)t=t.concat(s[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(Ma(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return my(t.sort(Sa))}}P(Ki,"id","time"),P(Ki,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function xi(n,t,e){let i=0,s=n.length-1,r,o,l,a;e?(t>=n[i].pos&&t<=n[s].pos&&({lo:i,hi:s}=ve(n,"pos",t)),{pos:r,time:l}=n[i],{pos:o,time:a}=n[s]):(t>=n[i].time&&t<=n[s].time&&({lo:i,hi:s}=ve(n,"time",t)),{time:r,pos:l}=n[i],{time:o,pos:a}=n[s]);const c=o-r;return c?l+(a-l)*(t-r)/c:l}class Ta extends Ki{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=xi(e,this.min),this._tableRange=xi(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,s=[],r=[];let o,l,a,c,h;for(o=0,l=t.length;o<l;++o)c=t[o],c>=e&&c<=i&&s.push(c);if(s.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,l=s.length;o<l;++o)h=s[o+1],a=s[o-1],c=s[o],Math.round((h+a)/2)!==c&&r.push({time:c,pos:o/(l-1)});return r}_generate(){const t=this.min,e=this.max;let i=super.getDataTimestamps();return(!i.includes(t)||!i.length)&&i.splice(0,0,t),(!i.includes(e)||i.length===1)&&i.push(e),i.sort((s,r)=>s-r)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return e.length&&i.length?t=this.normalize(e.concat(i)):t=e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(xi(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return xi(this._table,i*this._tableRange+this._minPos,!0)}}P(Ta,"id","timeseries"),P(Ta,"defaults",Ki.defaults);export{ag as C,H0 as E,ka as L,Vs as P,$0 as S,dg as a,Se as b,ba as c,De as d,q0 as e,j0 as f,Ls as g,W0 as i,K0 as p};
//# sourceMappingURL=vendor-admin-DSFDn6-z.js.map
