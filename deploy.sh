#!/bin/bash
# deploy.sh: <PERSON>ript to deploy the application

# Paths
DEPLOYMENT_CONF="/etc/nginx/conf.d/deployment.conf"

# Function to get the current deployment
get_current_deployment() {
  grep -oP 'default "\K[^"]+' $DEPLOYMENT_CONF
}

CURRENT_DEPLOYMENT=$(get_current_deployment)
echo "Current deployment is: $CURRENT_DEPLOYMENT"

if [ "$CURRENT_DEPLOYMENT" = "blue" ]; then
  NEW_DEPLOYMENT="green"
  OLD_DEPLOYMENT="blue"
else
  NEW_DEPLOYMENT="blue"
  OLD_DEPLOYMENT="green"
fi

echo "Deploying to: $NEW_DEPLOYMENT"

# Remove old containers in the new deployment environment
echo "Stopping and removing old containers..."
docker-compose stop frontend-$NEW_DEPLOYMENT
docker-compose rm -f frontend-$NEW_DEPLOYMENT

# Build and deploy backend first
echo "🔧 Building and deploying backend..."
docker-compose up -d --build backend

# Build new frontend without cache
echo "🎨 Building frontend ($NEW_DEPLOYMENT)..."
docker-compose build --no-cache frontend-$NEW_DEPLOYMENT

# Wait for backend to be ready
echo "⏳ Waiting for backend to be ready..."
sleep 10  # Give backend time to start

# Check if backend is responding (non-blocking)
echo "🔍 Checking backend health..."
if command -v curl >/dev/null 2>&1; then
  if curl -f http://localhost:4005/health 2>/dev/null; then
    echo "✅ Backend is healthy!"
  else
    echo "⚠️ Backend health check failed, but continuing deployment..."
  fi
else
  echo "ℹ️ curl not available, skipping health check"
fi

# Show backend status
echo "📋 Backend container status:"
docker-compose ps backend

# Show backend logs (last few lines)
echo "📋 Backend logs (last 5 lines):"
docker-compose logs --tail=5 backend

# Deploy new frontend
echo "🚀 Deploying frontend ($NEW_DEPLOYMENT)..."
docker-compose up -d frontend-$NEW_DEPLOYMENT

# Brief wait for startup
echo "⏳ Waiting for containers to start..."
sleep 5

# Update nginx configuration
echo "🔄 Updating nginx configuration..."
echo 'map "" $deployment {
    default "'$NEW_DEPLOYMENT'";
}' | sudo tee $DEPLOYMENT_CONF >/dev/null

# Reload nginx
echo "🔄 Reloading nginx..."
sudo nginx -t && sudo nginx -s reload

echo "✅ Deployment complete! Switched to $NEW_DEPLOYMENT"
echo "🔗 Frontend: https://devskills.ee"
echo "🔗 Backend API: https://devskills.ee/api/v1/communication/public/contact-form"
echo "📋 If issues occur, run './rollback.sh' to revert."