import React from "react";
import Footer from "@/components/footers/Footer";
import Header from "@/components/headers/Header";
import { menuItems } from "@/data/menu";
import Contact from "@/components/home/<USER>";
import MarqueeDark from "@/components/home/<USER>";
import Map from "@/components/common/Map";
import { useTranslation } from "react-i18next";

import UnifiedSEO from "@/components/common/UnifiedSEO";
import { getPageSEOData } from "@/utils/seoHelpers";

const dark = true;
export default function ElegantContactPageDark() {
  const { t, currentLanguage } = useTranslation();

  const seoData = getPageSEOData("contact");

  const title =
    currentLanguage === "en"
      ? seoData.title
      : "Kontakt | DevSkills - Võta Meie Meeskonnaga Ühendust";

  const description =
    currentLanguage === "en"
      ? seoData.description
      : "Võta ühendust DevSkills-iga äri transformatsiooni, tarkvara arenduse ja tehisintellekti rakendamise teenuste osas.";

  return (
    <>
      <UnifiedSEO
        title={title}
        description={description}
        slug="contact"
        type="website"
        image="https://devskills.ee/contact.jpg"
        schema={seoData.schema}
        keywords={seoData.keywords}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/3.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("contact.page.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("contact.page.subtitle")}
                      </p>
                    </div>
                  </div>
                </div>
              </section>
              <section
                className={`page-section  scrollSpysection mb-0 pb-0  ${
                  dark ? "bg-dark-1 light-content" : ""
                } `}
                id="contact"
              >
                <Contact />
              </section>
              <div className="page-section overflow-hidden">
                <MarqueeDark />
              </div>
              <div className="google-map light-content">
                <Map />
              </div>

              {/* Legal Links Section */}
              <section className="page-section bg-dark-1 light-content pt-40 pb-40">
                <div className="container">
                  <div className="row">
                    <div className="col-lg-8 offset-lg-2 text-center">
                      <h3 className="section-title-small mb-30">
                        {t("legal.title")}
                      </h3>
                      <div className="row">
                        <div className="col-md-6 mb-30">
                          <div className="card bg-dark-2 border-0 h-100">
                            <div className="card-body text-center p-40">
                              <div className="mb-20">
                                <i
                                  className="mi-shield text-primary"
                                  style={{ fontSize: "2.5rem" }}
                                ></i>
                              </div>
                              <h4 className="card-title text-white mb-20">
                                {t("legal.privacy.title")}
                              </h4>
                              <p className="text-gray mb-30">
                                {t("legal.privacy.description")}
                              </p>
                              <a
                                href={`/${currentLanguage}/privacy-policy`}
                                className="btn btn-mod btn-border btn-circle btn-medium"
                              >
                                {t("legal.privacy.button")}
                              </a>
                            </div>
                          </div>
                        </div>
                        <div className="col-md-6 mb-30">
                          <div className="card bg-dark-2 border-0 h-100">
                            <div className="card-body text-center p-40">
                              <div className="mb-20">
                                <i
                                  className="mi-document text-primary"
                                  style={{ fontSize: "2.5rem" }}
                                ></i>
                              </div>
                              <h4 className="card-title text-white mb-20">
                                {t("legal.terms.title")}
                              </h4>
                              <p className="text-gray mb-30">
                                {t("legal.terms.description")}
                              </p>
                              <a
                                href={`/${currentLanguage}/terms-conditions`}
                                className="btn btn-mod btn-border btn-circle btn-medium"
                              >
                                {t("legal.terms.button")}
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
