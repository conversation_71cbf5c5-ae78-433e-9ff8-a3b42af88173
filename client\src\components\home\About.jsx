import React from "react";
import { useTranslation } from "react-i18next";

export default function About() {
  const { t } = useTranslation();
  return (
    <div className="col-lg-6 offset-lg-1">
      <div className="row">
        <div
          className="col-sm-6 pt-60 pt-xs-0 mb-xs-40"
          data-rellax-y=""
          data-rellax-speed="-0.5"
          data-rellax-percentage="0.5"
        >
          <div className="spot-box clearfix mb-30">
            <div className="spot-box-icon float-end ms-3" />
            <div className="spot-box-text text-end">
              {/* <span className="text-gray">{t("hero.brands")}</span> */}
            </div>
          </div>
          <img
            src="/assets/img/2.jpg"
            width={400}
            height={489}
            className="w-100 round grayscale"
            alt="Image Description"
          />
        </div>
        <div
          className="col-sm-6"
          data-rellax-y=""
          data-rellax-speed="0.5"
          data-rellax-percentage="0.5"
        >
          <img
            src="/assets/img/3.jpg"
            width={400}
            height={489}
            className="w-100 round grayscale"
            alt="Image Description"
          />
          <div className="spot-box clearfix mt-30">
            <div className="spot-box-icon float-start me-3" />
            <div className="spot-box-text">
              {/* <span className="text-gray">{t("hero.interfaces")}</span> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
