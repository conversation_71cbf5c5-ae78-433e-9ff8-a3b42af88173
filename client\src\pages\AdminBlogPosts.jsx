// client/src/pages/AdminBlogPosts.jsx

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import { adminAPI, blogAPI, API_BASE_URL } from "../utils/api";

const AdminBlogPosts = () => {
  const navigate = useNavigate();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    status: "all",
    search: "",
  });
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    loadPosts();
  }, [filters]);

  const loadPosts = async () => {
    try {
      setLoading(true);

      const params = {};
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== "all") {
          params[key] = value;
        }
      });

      const { response, data } = await adminAPI.getPosts(params);

      if (data.success) {
        setPosts(data.data.posts);
        setPagination(data.data.pagination);
      } else {
        setError(data.message || "Failed to load posts");
      }
    } catch (error) {
      console.error("Load posts error:", error);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (postId) => {
    if (
      !confirm(
        "Are you sure you want to delete this blog post? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const { response, data } = await blogAPI.deletePost(postId);

      if (data.success) {
        loadPosts(); // Reload the list
      } else {
        setError(data.message || "Failed to delete post");
      }
    } catch (error) {
      console.error("Delete error:", error);
      setError("Network error. Please try again.");
    }
  };

  const handleToggleVisibility = async (postId) => {
    try {
      const { response, data } = await blogAPI.toggleVisibility(postId);

      if (data.success) {
        loadPosts(); // Reload the list
      } else {
        setError(data.message || "Failed to toggle visibility");
      }
    } catch (error) {
      console.error("Toggle visibility error:", error);
      setError("Network error. Please try again.");
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getImageUrl = (filename) => {
    if (!filename) return null;

    // If it's already a full URL, return as is
    if (filename.startsWith("http")) {
      return filename;
    }

    // Construct the full URL for uploaded images
    const baseUrl = API_BASE_URL.replace("/api", "");
    return `${baseUrl}/uploads/blog-images/${filename}`;
  };

  const getStatusBadge = (post) => {
    if (!post.published) {
      return (
        <span className="badge bg-secondary">
          <iconify-icon
            icon="solar:document-text-bold"
            className="me-1"
          ></iconify-icon>
          Draft
        </span>
      );
    }

    if (post.scheduledAt && new Date(post.scheduledAt) > new Date()) {
      return (
        <span className="badge bg-warning">
          <iconify-icon
            icon="solar:clock-circle-bold"
            className="me-1"
          ></iconify-icon>
          Scheduled
        </span>
      );
    }

    return (
      <span className="badge bg-success">
        <iconify-icon
          icon="solar:check-circle-bold"
          className="me-1"
        ></iconify-icon>
        Published
      </span>
    );
  };

  return (
    <>
      <SEO
        title="Manage Blog Posts - Admin"
        description="Manage blog posts in the admin panel"
        noIndex={true}
      />

      <AdminLayout title="Blog Posts">
        {/* Action Bar */}
        <div className="mb-30">
          <div className="row align-items-center">
            <div className="col-12 col-lg-6 mb-3 mb-lg-0">
              <p className="section-descr mb-0">
                Manage your blog posts, create new content, and organize your
                articles.
              </p>
            </div>
            <div className="col-12 col-lg-6 text-lg-end">
              <button
                onClick={() => navigate("/admin/blog/new")}
                className="btn btn-mod btn-color btn-round w-100 w-lg-auto"
              >
                <iconify-icon
                  icon="solar:add-circle-bold"
                  className="me-2"
                ></iconify-icon>
                New Post
              </button>
            </div>
          </div>
        </div>
        {/* Filters */}
        <div className="admin-table mb-30" style={{ padding: "15px 20px" }}>
          <div className="row g-3">
            <div className="col-12 col-md-6 col-lg-4">
              <label className="form-label">Search Posts</label>
              <input
                type="text"
                value={filters.search}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    search: e.target.value,
                    page: 1,
                  }))
                }
                className="form-control"
                placeholder="Search by title..."
              />
            </div>

            <div className="col-6 col-md-3 col-lg-3">
              <label className="form-label">Status</label>
              <select
                value={filters.status}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    status: e.target.value,
                    page: 1,
                  }))
                }
                className="form-control"
              >
                <option value="all">All Posts</option>
                <option value="published">Published</option>
                <option value="draft">Drafts</option>
              </select>
            </div>

            <div className="col-6 col-md-3 col-lg-2">
              <label className="form-label">Per Page</label>
              <select
                value={filters.limit}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    limit: parseInt(e.target.value),
                    page: 1,
                  }))
                }
                className="form-control"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="alert alert-danger mb-30" role="alert">
            <iconify-icon
              icon="solar:danger-triangle-bold"
              className="me-2"
            ></iconify-icon>
            {error}
          </div>
        )}

        {/* Posts Table */}
        <div className="admin-table">
          {loading ? (
            <div className="text-center py-60" style={{ padding: "40px 20px" }}>
              <iconify-icon
                icon="solar:refresh-bold"
                className="fa-2x color-primary-1 mb-20"
                style={{ animation: "spin 1s linear infinite" }}
              ></iconify-icon>
              <div className="hs-line-4 font-alt black">Loading posts...</div>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-60" style={{ padding: "40px 20px" }}>
              <iconify-icon
                icon="solar:document-text-bold"
                className="fa-3x color-gray-light-1 mb-20"
              ></iconify-icon>
              <div className="hs-line-4 font-alt black mb-10">
                No blog posts found
              </div>
              <p className="section-descr mb-30">
                {filters.search || filters.status !== "all"
                  ? "Try adjusting your search filters or create your first blog post."
                  : "Get started by creating your first blog post."}
              </p>
              <button
                onClick={() => navigate("/admin/blog/new")}
                className="btn btn-mod btn-color btn-round"
              >
                <iconify-icon
                  icon="solar:add-circle-bold"
                  className="me-2"
                ></iconify-icon>
                Create First Post
              </button>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="d-none d-lg-block">
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Title</th>
                        <th>Status</th>
                        <th>Author</th>
                        <th>Created</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {posts.map((post) => {
                        const englishTranslation =
                          post.translations.find((t) => t.language === "en") ||
                          post.translations[0];

                        return (
                          <tr key={post.id}>
                            <td>
                              <div className="d-flex align-items-center">
                                {post.featuredImage && (
                                  <img
                                    className="rounded me-3"
                                    src={getImageUrl(post.featuredImage)}
                                    alt=""
                                    style={{
                                      width: "50px",
                                      height: "50px",
                                      objectFit: "cover",
                                    }}
                                    onError={(e) => {
                                      e.target.style.display = "none";
                                    }}
                                  />
                                )}
                                <div>
                                  <div className="fw-bold">
                                    {englishTranslation?.title || "Untitled"}
                                  </div>
                                  <small className="text-muted">
                                    /{post.slug}
                                  </small>
                                </div>
                              </div>
                            </td>
                            <td>
                              {getStatusBadge(post)}
                              {post.featured && (
                                <span className="badge bg-primary ms-2">
                                  <iconify-icon
                                    icon="solar:star-bold"
                                    className="me-1"
                                  ></iconify-icon>
                                  Featured
                                </span>
                              )}
                            </td>
                            <td>{post.author.name || post.author.email}</td>
                            <td>{formatDate(post.createdAt)}</td>
                            <td>
                              <div className="btn-group" role="group">
                                <button
                                  onClick={() =>
                                    navigate(`/admin/blog/edit/${post.id}`)
                                  }
                                  className="btn btn-sm btn-outline-primary"
                                  title="Edit"
                                >
                                  <iconify-icon icon="solar:pen-bold"></iconify-icon>
                                </button>

                                <button
                                  onClick={() =>
                                    handleToggleVisibility(post.id)
                                  }
                                  className={`btn btn-sm ${
                                    post.published
                                      ? "btn-outline-warning"
                                      : "btn-outline-success"
                                  }`}
                                  title={
                                    post.published ? "Unpublish" : "Publish"
                                  }
                                >
                                  <iconify-icon
                                    icon={
                                      post.published
                                        ? "solar:eye-closed-bold"
                                        : "solar:eye-bold"
                                    }
                                  ></iconify-icon>
                                </button>

                                <button
                                  onClick={() => handleDelete(post.id)}
                                  className="btn btn-sm btn-outline-danger"
                                  title="Delete"
                                >
                                  <iconify-icon icon="solar:trash-bin-trash-bold"></iconify-icon>
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Mobile Card View */}
              <div className="d-lg-none">
                <div className="row g-3">
                  {posts.map((post) => {
                    const englishTranslation =
                      post.translations.find((t) => t.language === "en") ||
                      post.translations[0];

                    return (
                      <div key={post.id} className="col-12">
                        <div className="card border-0 shadow-sm">
                          <div className="card-body p-3">
                            <div className="row align-items-center">
                              <div className="col-12 mb-2">
                                <div className="d-flex align-items-center">
                                  {post.featuredImage && (
                                    <img
                                      className="rounded me-3"
                                      src={getImageUrl(post.featuredImage)}
                                      alt=""
                                      style={{
                                        width: "40px",
                                        height: "40px",
                                        objectFit: "cover",
                                      }}
                                      onError={(e) => {
                                        e.target.style.display = "none";
                                      }}
                                    />
                                  )}
                                  <div className="flex-grow-1">
                                    <h6 className="mb-1 fw-bold">
                                      {englishTranslation?.title || "Untitled"}
                                    </h6>
                                    <small className="text-muted">
                                      /{post.slug}
                                    </small>
                                  </div>
                                </div>
                              </div>

                              <div className="col-6 col-sm-4 mb-2">
                                <small className="text-muted d-block">
                                  Status
                                </small>
                                <div>
                                  {getStatusBadge(post)}
                                  {post.featured && (
                                    <span className="badge bg-primary ms-1">
                                      <iconify-icon
                                        icon="solar:star-bold"
                                        className="me-1"
                                      ></iconify-icon>
                                      Featured
                                    </span>
                                  )}
                                </div>
                              </div>

                              <div className="col-6 col-sm-4 mb-2">
                                <small className="text-muted d-block">
                                  Author
                                </small>
                                <small>
                                  {post.author.name || post.author.email}
                                </small>
                              </div>

                              <div className="col-12 col-sm-4 mb-2">
                                <small className="text-muted d-block">
                                  Created
                                </small>
                                <small>{formatDate(post.createdAt)}</small>
                              </div>

                              <div className="col-12">
                                <div className="d-flex gap-2 flex-wrap">
                                  <button
                                    onClick={() =>
                                      navigate(`/admin/blog/edit/${post.id}`)
                                    }
                                    className="btn btn-sm btn-outline-primary flex-fill"
                                    title="Edit"
                                  >
                                    <iconify-icon
                                      icon="solar:pen-bold"
                                      className="me-1"
                                    ></iconify-icon>
                                    Edit
                                  </button>

                                  <button
                                    onClick={() =>
                                      handleToggleVisibility(post.id)
                                    }
                                    className={`btn btn-sm flex-fill ${
                                      post.published
                                        ? "btn-outline-warning"
                                        : "btn-outline-success"
                                    }`}
                                    title={
                                      post.published ? "Unpublish" : "Publish"
                                    }
                                  >
                                    <iconify-icon
                                      icon={
                                        post.published
                                          ? "solar:eye-closed-bold"
                                          : "solar:eye-bold"
                                      }
                                      className="me-1"
                                    ></iconify-icon>
                                    {post.published ? "Hide" : "Show"}
                                  </button>

                                  <button
                                    onClick={() => handleDelete(post.id)}
                                    className="btn btn-sm btn-outline-danger flex-fill"
                                    title="Delete"
                                  >
                                    <iconify-icon
                                      icon="solar:trash-bin-trash-bold"
                                      className="me-1"
                                    ></iconify-icon>
                                    Delete
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="row mt-30 align-items-center">
            <div className="col-12 col-md-6 mb-3 mb-md-0">
              <p className="small text-muted mb-0 text-center text-md-start">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                of {pagination.total} results
              </p>
            </div>
            <div className="col-12 col-md-6">
              <nav aria-label="Blog posts pagination">
                <ul className="pagination pagination-sm justify-content-center justify-content-md-end mb-0">
                  <li
                    className={`page-item ${
                      pagination.page <= 1 ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page - 1 }))
                      }
                      disabled={pagination.page <= 1}
                    >
                      Previous
                    </button>
                  </li>

                  <li className="page-item active">
                    <span className="page-link">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                  </li>

                  <li
                    className={`page-item ${
                      pagination.page >= pagination.pages ? "disabled" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, page: prev.page + 1 }))
                      }
                      disabled={pagination.page >= pagination.pages}
                    >
                      Next
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default AdminBlogPosts;
