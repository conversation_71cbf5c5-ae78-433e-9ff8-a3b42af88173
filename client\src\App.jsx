import React, { useEffect, Suspense, lazy } from "react";
import "./styles/styles.css";
import "./styles/module-buttons.css";
import "./styles/grayscale-effect.css";
import "./styles/languageSelector.css";
import "./styles/gdpr.css";
import "./styles/analytics.css";
import "./i18n"; // Initialize i18n

// Eager load GDPR component for better LCP performance
import GDPRConsent from "./components/common/GDPRConsent";

import { parallaxMouseMovement, parallaxScroll } from "@/utils/parallax";

import { init_wow } from "@/utils/initWowjs";
import { headerChangeOnScroll } from "@/utils/changeHeaderOnScroll";
import { Route, Routes, useLocation } from "react-router-dom";
import { trackPageView } from "@/utils/analytics";

import ScrollTopBehaviour from "./components/common/ScrollTopBehaviour";
import LanguageRedirect from "./components/routing/LanguageRedirect";

// Eager load the home page for better initial performance
import Home5MainDemoMultiPageDark from "@/pages/home/<USER>";

// Lazy load all other pages to reduce initial bundle size

const ElegantAboutPageDark = lazy(() => import("./pages/about/page"));
const ElegantServicesPageDark = lazy(() => import("./pages/services/page"));
const ElegantWebstorePageDark = lazy(() => import("./pages/webstore/page"));
const ElegantPortfolioPageDark = lazy(() => import("./pages/portfolio/page"));
const ElegantBlogPageDark = lazy(() => import("./pages/blogs/page"));
const ElegantPortfolioSinglePageDark = lazy(() =>
  import("./pages/portfolio-single/page")
);
const ElegantWebstoreSinglePageDark = lazy(() =>
  import("./pages/webstore-single/page")
);
const ElegantBlogSinglePageDark = lazy(() =>
  import("./pages/blog-single/page")
);
const ElegantContactPageDark = lazy(() => import("./pages/contact/page"));
const PrivacyPolicyPage = lazy(() => import("@/pages/privacy-policy/page"));
const TermsConditionsPage = lazy(() => import("@/pages/terms-conditions/page"));
const MainPageNotFound = lazy(() => import("./pages/otherPages/page"));

// Admin routes - split into separate bundle for performance optimization
// This removes ~593KB from the main bundle, improving load times for public users
const AdminRoutes = lazy(() => import("@/routes/AdminRoutes"));

// Loading component for lazy-loaded routes
const PageLoader = () => (
  <div
    className="page-loader"
    style={{
      position: "fixed",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "#1a1a1a",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 9999,
    }}
  >
    <div
      className="loader"
      style={{
        width: "40px",
        height: "40px",
        border: "4px solid #333",
        borderTop: "4px solid #fff",
        borderRadius: "50%",
        animation: "spin 1s linear infinite",
      }}
    ></div>
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

function App() {
  const { pathname } = useLocation();
  useEffect(() => {
    init_wow();
    parallaxMouseMovement();

    // Initialize navbar with better handling for page refreshes
    const initializeNavbar = () => {
      var mainNav = document.querySelector(".main-nav");
      if (!mainNav) {
        // If navbar doesn't exist yet, try again after a short delay
        setTimeout(initializeNavbar, 100);
        return;
      }

      // Ensure navbar is visible
      mainNav.style.display = "";

      if (mainNav?.classList.contains("transparent")) {
        mainNav.classList.add("js-transparent");
      } else if (!mainNav?.classList?.contains("dark")) {
        mainNav?.classList.add("js-no-transparent-white");
      }

      // Force initial scroll check to set correct navbar state
      headerChangeOnScroll();
    };

    // Initialize immediately and also after DOM is fully ready
    initializeNavbar();
    setTimeout(initializeNavbar, 50);

    window.addEventListener("scroll", headerChangeOnScroll);
    parallaxScroll();

    // Track page view on route change (deferred to avoid blocking render)
    setTimeout(() => {
      const pageTitle = document.title || "DevSkills";
      const pageLocation = window.location.href;
      trackPageView(pageTitle, pageLocation);
    }, 100);

    return () => {
      window.removeEventListener("scroll", headerChangeOnScroll);
    };
  }, [pathname]);

  // Additional effect to handle navbar on route changes
  useEffect(() => {
    const handleNavbarOnRouteChange = () => {
      const mainNav = document.querySelector(".main-nav");
      if (mainNav) {
        // Ensure navbar is visible
        mainNav.style.display = "";
        mainNav.style.visibility = "visible";
        mainNav.style.opacity = "1";

        // Force scroll check to set correct state
        setTimeout(() => {
          headerChangeOnScroll();
        }, 100);
      }
    };

    handleNavbarOnRouteChange();
  }, [pathname]);
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Import the script only on the client side
      import("bootstrap/dist/js/bootstrap.esm").then(() => {
        // Bootstrap is now loaded and available
        console.log("Bootstrap loaded");
      });
    }
  }, []);
  return (
    <>
      <Suspense fallback={<PageLoader />}>
        <Routes>
          {/* Language-prefixed routes - ALL languages must have prefixes */}
          <Route path="/:lang">
            <Route index element={<Home5MainDemoMultiPageDark />} />
            <Route path="about" element={<ElegantAboutPageDark />} />

            <Route path="services" element={<ElegantServicesPageDark />} />
            <Route path="portfolio" element={<ElegantPortfolioPageDark />} />
            <Route
              path="portfolio-single/:id"
              element={<ElegantPortfolioSinglePageDark />}
            />
            <Route path="webstore" element={<ElegantWebstorePageDark />} />
            <Route
              path="webstore-single/:id"
              element={<ElegantWebstoreSinglePageDark />}
            />
            <Route path="blog" element={<ElegantBlogPageDark />} />
            <Route
              path="blog-single/:id"
              element={<ElegantBlogSinglePageDark />}
            />
            <Route path="contact" element={<ElegantContactPageDark />} />
            <Route path="privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="terms-conditions" element={<TermsConditionsPage />} />
          </Route>

          {/* Admin routes (no language prefix) - Code split for performance */}
          <Route path="/admin/*" element={<AdminRoutes />} />

          {/* Root redirect - ALWAYS redirect to language-prefixed URL */}
          <Route path="/" element={<LanguageRedirect />} />

          {/* Catch any non-prefixed routes and redirect them */}
          <Route path="/about" element={<LanguageRedirect />} />
          <Route path="/services" element={<LanguageRedirect />} />
          <Route path="/portfolio" element={<LanguageRedirect />} />
          <Route path="/portfolio-single/*" element={<LanguageRedirect />} />
          <Route path="/blog" element={<LanguageRedirect />} />
          <Route path="/blog-single/*" element={<LanguageRedirect />} />
          <Route path="/contact" element={<LanguageRedirect />} />
          <Route path="/privacy-policy" element={<LanguageRedirect />} />
          <Route path="/terms-conditions" element={<LanguageRedirect />} />

          {/* 404 for everything else */}
          <Route path="*" element={<MainPageNotFound />} />
        </Routes>
      </Suspense>
      <ScrollTopBehaviour />
      <GDPRConsent />
      {/* Analytics Debug Panel - Only show in development */}
      {/* {import.meta.env.DEV && <AnalyticsDebug />} */}
    </>
  );
}

export default App;
