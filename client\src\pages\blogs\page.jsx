// client/src/pages/blogs/page.jsx

import React from "react";
import Footer from "@/components/footers/Footer";

import Header from "@/components/headers/Header";

import { Link, useSearchParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { menuItems } from "@/data/menu";
import Pagination from "@/components/common/Pagination";
import { blogAPI, categoriesAPI, tagsAPI, archiveAPI } from "@/utils/api";
import { useTranslation } from "react-i18next";
import UnifiedSEO from "@/components/common/UnifiedSEO";
import { getPageSEOData } from "@/utils/seoHelpers";

export default function ElegantBlogPageDark() {
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";
  const [searchParams, setSearchParams] = useSearchParams();
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [archiveData, setArchiveData] = useState([]);

  // Get current filters from URL
  const currentCategory = searchParams.get("category");
  const currentTag = searchParams.get("tag");
  const currentSearch = searchParams.get("search");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch blog posts with filters
        const params = {
          language: currentLanguage,
          page: currentPage,
          limit: 9,
        };

        if (currentCategory) params.category = currentCategory;
        if (currentTag) params.tag = currentTag;
        if (currentSearch) params.search = currentSearch;

        const blogResult = await blogAPI.getBlogPosts(params);

        if (blogResult.response.ok && blogResult.data) {
          // Extract the posts array from the nested response structure
          const posts =
            blogResult.data.data?.data || blogResult.data.data || [];
          const pagination =
            blogResult.data.data?.pagination || blogResult.data.pagination;
          console.log("Blog listing API response:", blogResult.data);
          console.log("Posts array:", posts);
          console.log("Pagination:", pagination);
          setBlogPosts(Array.isArray(posts) ? posts : []);
          setTotalPages(pagination?.totalPages || 1);
        } else {
          console.error(
            "Failed to fetch blog posts:",
            blogResult.response.status
          );
          setBlogPosts([]);
        }

        // Fetch categories
        try {
          const categoriesResult = await categoriesAPI.getCategories();
          if (categoriesResult.response.ok && categoriesResult.data) {
            setCategories(categoriesResult.data.data || []);
          }
        } catch (error) {
          console.error("Error fetching categories:", error);
        }

        // Fetch tags
        try {
          const tagsResult = await tagsAPI.getTags();
          if (tagsResult.response.ok && tagsResult.data) {
            setTags(tagsResult.data.data || []);
          }
        } catch (error) {
          console.error("Error fetching tags:", error);
        }

        // Fetch archive data
        try {
          const archiveResult = await archiveAPI.getArchive();
          if (archiveResult.response.ok && archiveResult.data) {
            setArchiveData(archiveResult.data.archive || []);
          }
        } catch (error) {
          console.error("Error fetching archive:", error);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setBlogPosts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [
    currentLanguage,
    currentPage,
    currentCategory,
    currentTag,
    currentSearch,
  ]);

  // Helper function to get translation for current language
  const getTranslation = (post, field) => {
    const translation = post.translations?.find(
      (t) => t.language === currentLanguage
    );
    return (
      translation?.[field] ||
      post.translations?.find((t) => t.language === "en")?.[field] ||
      ""
    );
  };

  // Filter handlers
  const handleCategoryFilter = (categorySlug) => {
    const newParams = new URLSearchParams(searchParams);
    if (categorySlug) {
      newParams.set("category", categorySlug);
    } else {
      newParams.delete("category");
    }
    newParams.delete("page"); // Reset to first page when filtering
    setSearchParams(newParams);
    setCurrentPage(1);
  };

  const handleTagFilter = (tagSlug) => {
    const newParams = new URLSearchParams(searchParams);
    if (tagSlug) {
      newParams.set("tag", tagSlug);
    } else {
      newParams.delete("tag");
    }
    newParams.delete("page"); // Reset to first page when filtering
    setSearchParams(newParams);
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchParams({});
    setCurrentPage(1);
  };

  const seoData = getPageSEOData("blog");

  return (
    <>
      <UnifiedSEO
        title={t("blog.page.title") || seoData.title}
        description={t("blog.page.description") || seoData.description}
        slug="blog"
        type="website"
        image="https://devskills.ee/blog.jpg"
        schema={seoData.schema}
        keywords={
          t("blog.page.keywords", { returnObjects: true }) || seoData.keywords
        }
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className={`page-section bg-dark-alpha-50 light-content ${
                  currentCategory || currentTag || currentSearch
                    ? "blog-hero-minimal"
                    : ""
                }`}
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("blog.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("blog.subtitle")}
                      </p>
                    </div>
                  </div>

                  {/* Filter Status */}
                  {(currentCategory || currentTag || currentSearch) && (
                    <div className="filter-status-minimal">
                      <div className="d-flex flex-wrap justify-content-center align-items-center gap-3">
                        <span className="text-white-opacity">Filtered by:</span>
                        {currentCategory && (
                          <span className="badge bg-primary">
                            Category:{" "}
                            {
                              categories.find((c) => c.slug === currentCategory)
                                ?.name
                            }
                          </span>
                        )}
                        {currentTag && (
                          <span className="badge bg-secondary">
                            Tag: {tags.find((t) => t.slug === currentTag)?.name}
                          </span>
                        )}
                        {currentSearch && (
                          <span className="badge bg-info">
                            Search: "{currentSearch}"
                          </span>
                        )}
                        <button
                          onClick={clearFilters}
                          className="link-hover-anim link-circle-1 align-middle"
                          data-link-animate="y"
                        >
                          <span className="link-strong link-strong-unhovered">
                            Clear Filters
                          </span>
                          <span
                            className="link-strong link-strong-hovered"
                            aria-hidden="true"
                          >
                            Clear Filters
                          </span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </section>
              <>
                <section
                  className="page-section bg-dark-1 light-content"
                  id="blog"
                >
                  <div className="container">
                    {/* Blog Posts Grid */}
                    <div
                      className="row mt-n50 mb-50 wow fadeInUp"
                      data-wow-offset={0}
                    >
                      {/* Loading State */}
                      {loading && (
                        <div className="col-12 text-center">
                          <div className="text-gray">{t("blog.loading")}</div>
                        </div>
                      )}

                      {/* Empty State */}
                      {!loading && blogPosts.length === 0 && (
                        <div className="col-12 text-center">
                          <div className="text-gray">{t("blog.empty")}</div>
                        </div>
                      )}

                      {/* Post Items */}
                      {!loading &&
                        Array.isArray(blogPosts) &&
                        blogPosts.map((post) => (
                          <div
                            key={post.id}
                            className="post-prev col-md-6 col-lg-4 mt-50"
                          >
                            <div className="post-prev-container">
                              <div className="post-prev-img">
                                <Link to={`/blog-single/${post.slug}`}>
                                  <img
                                    src={
                                      post.featuredImage ||
                                      "/assets/images/demo-elegant/blog/1.jpg"
                                    }
                                    width={607}
                                    height={358}
                                    alt={getTranslation(post, "title")}
                                  />
                                </Link>
                              </div>
                              <h3 className="post-prev-title">
                                <Link to={`/blog-single/${post.slug}`}>
                                  {getTranslation(post, "title")}
                                </Link>
                              </h3>
                              <div className="post-prev-text">
                                {getTranslation(post, "excerpt")}
                              </div>
                              <div className="post-prev-info clearfix">
                                <div className="float-start">
                                  <a href="#" className="icon-author">
                                    <i className="mi-user size-14 align-middle" />
                                  </a>
                                  <a href="#">
                                    {post.author?.name || "DevSkills Team"}
                                  </a>
                                </div>
                                <div className="float-end">
                                  <i className="mi-calendar size-14 align-middle" />
                                  <a href="#">
                                    {new Date(
                                      post.publishedAt || post.createdAt
                                    ).toLocaleDateString()}
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      {/* End Post Item */}

                      {/* End Post Item */}
                    </div>
                    {/* End Blog Posts Grid */}
                    {/* Pagination */}
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={setCurrentPage}
                    />
                    {/* End Pagination */}
                  </div>
                </section>
                {/* End Blog Section */}
                {/* Divider */}
                <hr className="mt-0 mb-0 white" />
                {/* End Divider */}
                {/* Section */}
                <section className="page-section bg-dark-1 light-content">
                  <div className="container relative">
                    <div className="row mt-n60">
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">
                            {t("blog.categories")}
                          </h3>
                          <div className="widget-body">
                            <ul className="clearlist widget-menu">
                              {categories.map((category) => (
                                <li key={category.id}>
                                  <a
                                    href="#"
                                    title=""
                                    onClick={(e) => {
                                      e.preventDefault();
                                      handleCategoryFilter(category.slug);
                                    }}
                                    className={
                                      currentCategory === category.slug
                                        ? "active"
                                        : ""
                                    }
                                  >
                                    {category.name}
                                  </a>
                                  <small>
                                    {" "}
                                    - {category._count?.blogPosts || 0}{" "}
                                  </small>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">{t("blog.tags")}</h3>
                          <div className="widget-body">
                            <div className="tags">
                              {tags.map((tag) => (
                                <a
                                  href="#"
                                  key={tag.id}
                                  onClick={(e) => {
                                    e.preventDefault();
                                    handleTagFilter(tag.slug);
                                  }}
                                  className={
                                    currentTag === tag.slug ? "active" : ""
                                  }
                                >
                                  {tag.name}
                                </a>
                              ))}
                            </div>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">{t("blog.archive")}</h3>
                          <div className="widget-body">
                            <ul className="clearlist widget-menu">
                              {archiveData.map((archive, index) => (
                                <li key={index}>
                                  <a href="#" title="">
                                    {archive.monthName} {archive.year}
                                  </a>
                                  <small> - {archive.count} </small>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                      <div className="col-sm-6 col-lg-3 mt-60">
                        {/* Widget */}
                        <div className="widget mb-0">
                          <h3 className="widget-title">
                            {t("blog.about_widget.title")}
                          </h3>
                          <div className="widget-body">
                            <div className="widget-text clearfix">
                              <img
                                src="/assets/img/power-128.png"
                                alt="DevSkills Logo"
                                height={40}
                                width={40}
                                className="left img-left"
                                style={{ borderRadius: "8px" }}
                              />
                              {t("blog.about_widget.text")}
                            </div>
                          </div>
                        </div>
                        {/* End Widget */}
                      </div>
                    </div>
                  </div>
                </section>
              </>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
